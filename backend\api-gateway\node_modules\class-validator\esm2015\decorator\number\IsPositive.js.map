{"version": 3, "file": "IsPositive.js", "sourceRoot": "", "sources": ["../../../../src/decorator/number/IsPositive.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,WAAW,GAAG,YAAY,CAAC;AAExC;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,KAAc;IACvC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC;AAChD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,iBAAqC;IAC9D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;YACrD,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,qCAAqC,EAChE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_POSITIVE = 'isPositive';\n\n/**\n * Checks if the value is a positive number greater than zero.\n */\nexport function isPositive(value: unknown): boolean {\n  return typeof value === 'number' && value > 0;\n}\n\n/**\n * Checks if the value is a positive number greater than zero.\n */\nexport function IsPositive(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_POSITIVE,\n      validator: {\n        validate: (value, args): boolean => isPositive(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a positive number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}