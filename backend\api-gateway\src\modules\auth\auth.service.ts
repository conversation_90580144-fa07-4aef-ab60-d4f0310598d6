import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';

// DTOs
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { Enable2FaDto } from './dto/enable-2fa.dto';

// Services
import { TwoFactorService } from './services/two-factor.service';
import { OtpService } from './services/otp.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly twoFactorService: TwoFactorService,
    private readonly otpService: OtpService,
  ) {}

  /**
   * تسجيل مستخدم جديد
   */
  async register(registerDto: RegisterDto) {
    const { email, phone, password, firstName, lastName, nationalId } = registerDto;

    // التحقق من وجود المستخدم
    const existingUser = await this.findUserByEmailOrPhone(email, phone);
    if (existingUser) {
      throw new ConflictException('المستخدم موجود مسبقاً');
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 12);

    // إنشاء المستخدم
    const user = await this.createUser({
      email,
      phone,
      password: hashedPassword,
      firstName,
      lastName,
      nationalId,
      isVerified: false,
      is2FAEnabled: false,
    });

    // إرسال رمز التحقق
    await this.otpService.sendVerificationOtp(user.id, phone);

    return {
      message: 'تم التسجيل بنجاح. يرجى التحقق من رقم الهاتف',
      userId: user.id,
      requiresVerification: true,
    };
  }

  /**
   * تسجيل الدخول
   */
  async login(user: any) {
    // التحقق من حالة المستخدم
    if (!user.isVerified) {
      throw new UnauthorizedException('يرجى التحقق من رقم الهاتف أولاً');
    }

    if (!user.isActive) {
      throw new UnauthorizedException('الحساب معطل');
    }

    // التحقق من المصادقة الثنائية
    if (user.is2FAEnabled) {
      // إرسال رمز المصادقة الثنائية
      await this.twoFactorService.send2FACode(user.id);
      
      return {
        message: 'يرجى إدخال رمز المصادقة الثنائية',
        requires2FA: true,
        tempToken: this.generateTempToken(user.id),
      };
    }

    // إنشاء رمز الوصول
    const tokens = await this.generateTokens(user);

    // تسجيل عملية الدخول
    await this.logUserActivity(user.id, 'login', {
      ip: 'unknown', // سيتم تمريره من الكونترولر
      userAgent: 'unknown',
    });

    return {
      message: 'تم تسجيل الدخول بنجاح',
      user: this.sanitizeUser(user),
      ...tokens,
    };
  }

  /**
   * التحقق من رمز OTP
   */
  async verifyOtp(verifyOtpDto: VerifyOtpDto) {
    const { userId, otp, type } = verifyOtpDto;

    const isValid = await this.otpService.verifyOtp(userId, otp, type);
    if (!isValid) {
      throw new BadRequestException('رمز التحقق غير صحيح أو منتهي الصلاحية');
    }

    // تحديث حالة المستخدم حسب نوع التحقق
    if (type === 'verification') {
      await this.updateUserVerificationStatus(userId, true);
    }

    const user = await this.findUserById(userId);
    const tokens = await this.generateTokens(user);

    return {
      message: 'تم التحقق بنجاح',
      user: this.sanitizeUser(user),
      ...tokens,
    };
  }

  /**
   * طلب إعادة تعيين كلمة المرور
   */
  async forgotPassword(email: string) {
    const user = await this.findUserByEmail(email);
    if (!user) {
      // لا نكشف عن عدم وجود المستخدم لأسباب أمنية
      return { message: 'إذا كان البريد الإلكتروني موجود، سيتم إرسال رمز الإعادة' };
    }

    await this.otpService.sendPasswordResetOtp(user.id, user.phone);

    return { message: 'تم إرسال رمز إعادة التعيين إلى رقم الهاتف' };
  }

  /**
   * إعادة تعيين كلمة المرور
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { email, otp, newPassword } = resetPasswordDto;

    const user = await this.findUserByEmail(email);
    if (!user) {
      throw new NotFoundException('المستخدم غير موجود');
    }

    const isValidOtp = await this.otpService.verifyOtp(user.id, otp, 'password_reset');
    if (!isValidOtp) {
      throw new BadRequestException('رمز التحقق غير صحيح');
    }

    // تشفير كلمة المرور الجديدة
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    await this.updateUserPassword(user.id, hashedPassword);

    // إلغاء جميع الجلسات النشطة
    await this.invalidateAllUserSessions(user.id);

    return { message: 'تم تغيير كلمة المرور بنجاح' };
  }

  /**
   * تغيير كلمة المرور
   */
  async changePassword(userId: string, changePasswordDto: ChangePasswordDto) {
    const { currentPassword, newPassword } = changePasswordDto;

    const user = await this.findUserById(userId);
    if (!user) {
      throw new NotFoundException('المستخدم غير موجود');
    }

    // التحقق من كلمة المرور الحالية
    const isValidPassword = await bcrypt.compare(currentPassword, user.password);
    if (!isValidPassword) {
      throw new BadRequestException('كلمة المرور الحالية غير صحيحة');
    }

    // تشفير كلمة المرور الجديدة
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    await this.updateUserPassword(userId, hashedPassword);

    return { message: 'تم تغيير كلمة المرور بنجاح' };
  }

  /**
   * تفعيل المصادقة الثنائية
   */
  async enable2FA(userId: string, enable2FaDto: Enable2FaDto) {
    const { method, phone } = enable2FaDto;

    await this.twoFactorService.enable2FA(userId, method, phone);

    return { message: 'تم تفعيل المصادقة الثنائية بنجاح' };
  }

  /**
   * إلغاء المصادقة الثنائية
   */
  async disable2FA(userId: string) {
    await this.twoFactorService.disable2FA(userId);
    return { message: 'تم إلغاء المصادقة الثنائية' };
  }

  /**
   * الحصول على بيانات المستخدم
   */
  async getProfile(userId: string) {
    const user = await this.findUserById(userId);
    if (!user) {
      throw new NotFoundException('المستخدم غير موجود');
    }

    return this.sanitizeUser(user);
  }

  /**
   * تسجيل الخروج
   */
  async logout(userId: string) {
    await this.invalidateUserSession(userId);
    return { message: 'تم تسجيل الخروج بنجاح' };
  }

  /**
   * تجديد رمز الوصول
   */
  async refreshToken(userId: string) {
    const user = await this.findUserById(userId);
    if (!user || !user.isActive) {
      throw new UnauthorizedException('المستخدم غير مخول');
    }

    const tokens = await this.generateTokens(user);
    return tokens;
  }

  // Helper Methods (سيتم تنفيذها لاحقاً)
  private async findUserByEmailOrPhone(email: string, phone: string) {
    // TODO: تنفيذ البحث في قاعدة البيانات
    return null;
  }

  private async findUserByEmail(email: string) {
    // TODO: تنفيذ البحث في قاعدة البيانات
    return null;
  }

  private async findUserById(id: string) {
    // TODO: تنفيذ البحث في قاعدة البيانات
    return null;
  }

  private async createUser(userData: any) {
    // TODO: تنفيذ إنشاء المستخدم في قاعدة البيانات
    return { id: 'temp-id', ...userData };
  }

  private async updateUserVerificationStatus(userId: string, isVerified: boolean) {
    // TODO: تحديث حالة التحقق في قاعدة البيانات
  }

  private async updateUserPassword(userId: string, hashedPassword: string) {
    // TODO: تحديث كلمة المرور في قاعدة البيانات
  }

  private async generateTokens(user: any) {
    const payload = { sub: user.id, email: user.email };
    
    return {
      accessToken: this.jwtService.sign(payload),
      refreshToken: this.jwtService.sign(payload, { expiresIn: '7d' }),
    };
  }

  private generateTempToken(userId: string) {
    return this.jwtService.sign({ sub: userId, temp: true }, { expiresIn: '10m' });
  }

  private sanitizeUser(user: any) {
    const { password, ...sanitized } = user;
    return sanitized;
  }

  private async logUserActivity(userId: string, action: string, metadata: any) {
    // TODO: تسجيل نشاط المستخدم
  }

  private async invalidateUserSession(userId: string) {
    // TODO: إلغاء جلسة المستخدم
  }

  private async invalidateAllUserSessions(userId: string) {
    // TODO: إلغاء جميع جلسات المستخدم
  }
}
