{"name": "@nestjs/cli", "version": "10.4.9", "description": "Nest - modern, fast, powerful node.js web framework (@cli)", "publishConfig": {"access": "public"}, "engines": {"node": ">= 16.14"}, "bin": {"nest": "bin/nest.js"}, "scripts": {"build": "tsc", "clean": "gulp clean:bundle", "format": "prettier --write \"**/*.ts\"", "lint": "eslint '{lib,commands,actions}/**/*.ts' --fix", "start": "node bin/nest.js", "prepack": "npm run build", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "test": "jest --config test/jest-config.json", "test:dev": "npm run clean && jest --config test/jest-config.json --watchAll", "prerelease": "npm run build", "release": "release-it", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/nestjs/nest-cli.git"}, "license": "MIT", "bugs": {"url": "https://github.com/nestjs/nest-cli/issues"}, "homepage": "https://github.com/nestjs/nest-cli#readme", "dependencies": {"@angular-devkit/core": "17.3.11", "@angular-devkit/schematics": "17.3.11", "@angular-devkit/schematics-cli": "17.3.11", "@nestjs/schematics": "^10.0.1", "chalk": "4.1.2", "chokidar": "3.6.0", "cli-table3": "0.6.5", "commander": "4.1.1", "fork-ts-checker-webpack-plugin": "9.0.2", "glob": "10.4.5", "inquirer": "8.2.6", "node-emoji": "1.11.0", "ora": "5.4.1", "tree-kill": "1.2.2", "tsconfig-paths": "4.2.0", "tsconfig-paths-webpack-plugin": "4.2.0", "typescript": "5.7.2", "webpack": "5.97.1", "webpack-node-externals": "3.0.0"}, "devDependencies": {"@commitlint/cli": "19.6.0", "@commitlint/config-angular": "19.6.0", "@swc/cli": "0.5.2", "@swc/core": "1.10.1", "@types/inquirer": "9.0.7", "@types/jest": "29.5.14", "@types/node": "22.10.1", "@types/node-emoji": "1.8.2", "@types/webpack-node-externals": "3.0.4", "@typescript-eslint/eslint-plugin": "8.17.0", "@typescript-eslint/parser": "8.17.0", "delete-empty": "3.0.0", "eslint": "9.16.0", "eslint-config-prettier": "9.1.0", "gulp": "5.0.0", "gulp-clean": "0.4.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.10", "prettier": "3.4.2", "release-it": "17.10.0", "ts-jest": "29.2.5", "ts-loader": "9.5.1", "ts-node": "10.9.2"}, "lint-staged": {"**/*.{ts,json}": []}, "peerDependencies": {"@swc/cli": "^0.1.62 || ^0.3.0 || ^0.4.0 || ^0.5.0", "@swc/core": "^1.3.62"}, "peerDependenciesMeta": {"@swc/cli": {"optional": true}, "@swc/core": {"optional": true}}}