import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { ProfileService } from '../services/profile.service';
import { CreateProfileDto } from '../dto/create-profile.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { UserProfile } from '../entities/user-profile.entity';
import { JwtAuthGuard } from '../../../common/guards/auth.guard';
import { RolesGuard } from '../../../common/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { GetUser } from '../../../common/decorators/get-user.decorator';
import { UserRole } from '../../users/entities/user.entity';

@ApiTags('User Profiles')
@Controller('profiles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'إنشاء ملف شخصي',
    description: 'إنشاء ملف شخصي جديد للمستخدم',
  })
  @ApiResponse({
    status: 201,
    description: 'تم إنشاء الملف الشخصي بنجاح',
    type: UserProfile,
  })
  @ApiResponse({
    status: 400,
    description: 'بيانات غير صالحة أو الملف الشخصي موجود بالفعل',
  })
  async create(
    @GetUser() user: any,
    @Body() createProfileDto: CreateProfileDto,
  ): Promise<UserProfile> {
    return this.profileService.create(user.sub, createProfileDto);
  }

  @Get('me')
  @ApiOperation({
    summary: 'الحصول على الملف الشخصي الحالي',
    description: 'استرجاع الملف الشخصي للمستخدم المسجل حالياً',
  })
  @ApiResponse({
    status: 200,
    description: 'الملف الشخصي',
    type: UserProfile,
  })
  @ApiResponse({
    status: 404,
    description: 'الملف الشخصي غير موجود',
  })
  async getMyProfile(@GetUser() user: any): Promise<UserProfile> {
    return this.profileService.findByUserId(user.sub);
  }

  @Get('me/completion')
  @ApiOperation({
    summary: 'نسبة اكتمال الملف الشخصي',
    description: 'الحصول على نسبة اكتمال الملف الشخصي الحالي',
  })
  @ApiResponse({
    status: 200,
    description: 'نسبة الاكتمال',
    schema: {
      type: 'object',
      properties: {
        completionPercentage: { type: 'number', example: 75 },
        isComplete: { type: 'boolean', example: false },
      },
    },
  })
  async getMyProfileCompletion(@GetUser() user: any) {
    const completionPercentage = await this.profileService.getProfileCompletionPercentage(user.sub);
    return {
      completionPercentage,
      isComplete: completionPercentage === 100,
    };
  }

  @Patch('me')
  @ApiOperation({
    summary: 'تحديث الملف الشخصي الحالي',
    description: 'تحديث الملف الشخصي للمستخدم المسجل حالياً',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تحديث الملف الشخصي بنجاح',
    type: UserProfile,
  })
  @ApiResponse({
    status: 404,
    description: 'الملف الشخصي غير موجود',
  })
  async updateMyProfile(
    @GetUser() user: any,
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<UserProfile> {
    return this.profileService.update(user.sub, updateProfileDto);
  }

  @Get('stats')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'إحصائيات الملفات الشخصية',
    description: 'الحصول على إحصائيات الملفات الشخصية (للمديرين فقط)',
  })
  @ApiResponse({
    status: 200,
    description: 'إحصائيات الملفات الشخصية',
    schema: {
      type: 'object',
      properties: {
        totalProfiles: { type: 'number' },
        completeProfiles: { type: 'number' },
        verifiedProfiles: { type: 'number' },
        profilesWithIncome: { type: 'number' },
        completionRate: { type: 'number' },
        verificationRate: { type: 'number' },
      },
    },
  })
  async getStats() {
    return this.profileService.getProfileStats();
  }

  @Get('incomplete')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.AGENT)
  @ApiOperation({
    summary: 'الملفات الشخصية غير المكتملة',
    description: 'الحصول على قائمة الملفات الشخصية غير المكتملة',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'عدد النتائج',
    example: 50,
  })
  @ApiResponse({
    status: 200,
    description: 'قائمة الملفات الشخصية غير المكتملة',
    type: [UserProfile],
  })
  async getIncompleteProfiles(@Query('limit') limit: number = 50) {
    return this.profileService.getIncompleteProfiles(limit);
  }

  @Get('unverified')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.AGENT)
  @ApiOperation({
    summary: 'الملفات الشخصية غير المتحقق منها',
    description: 'الحصول على قائمة الملفات الشخصية غير المتحقق منها',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'عدد النتائج',
    example: 50,
  })
  @ApiResponse({
    status: 200,
    description: 'قائمة الملفات الشخصية غير المتحقق منها',
    type: [UserProfile],
  })
  async getUnverifiedProfiles(@Query('limit') limit: number = 50) {
    return this.profileService.getUnverifiedProfiles(limit);
  }

  @Get('search')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.AGENT)
  @ApiOperation({
    summary: 'البحث في الملفات الشخصية',
    description: 'البحث في الملفات الشخصية بالاسم أو المهنة أو جهة العمل',
  })
  @ApiQuery({
    name: 'q',
    required: true,
    description: 'مصطلح البحث',
    example: 'مهندس',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'عدد النتائج',
    example: 20,
  })
  @ApiResponse({
    status: 200,
    description: 'نتائج البحث',
    type: [UserProfile],
  })
  async searchProfiles(
    @Query('q') searchTerm: string,
    @Query('limit') limit: number = 20,
  ) {
    return this.profileService.searchProfiles(searchTerm, limit);
  }

  @Get(':userId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.AGENT)
  @ApiOperation({
    summary: 'الحصول على ملف شخصي محدد',
    description: 'استرجاع ملف شخصي لمستخدم محدد (للمديرين والوكلاء فقط)',
  })
  @ApiParam({
    name: 'userId',
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'الملف الشخصي',
    type: UserProfile,
  })
  @ApiResponse({
    status: 404,
    description: 'الملف الشخصي غير موجود',
  })
  async findOne(@Param('userId', ParseUUIDPipe) userId: string): Promise<UserProfile> {
    return this.profileService.findByUserId(userId);
  }

  @Patch(':userId/verify')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'التحقق من الملف الشخصي',
    description: 'تحديث حالة التحقق من الملف الشخصي (للمديرين فقط)',
  })
  @ApiParam({
    name: 'userId',
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تحديث حالة التحقق بنجاح',
    type: UserProfile,
  })
  async verifyProfile(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body('isVerified') isVerified: boolean = true,
  ): Promise<UserProfile> {
    return this.profileService.verifyProfile(userId, isVerified);
  }

  @Delete(':userId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(RolesGuard)
  @Roles(UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'حذف ملف شخصي',
    description: 'حذف ملف شخصي لمستخدم محدد (للمدير العام فقط)',
  })
  @ApiParam({
    name: 'userId',
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 204,
    description: 'تم حذف الملف الشخصي بنجاح',
  })
  async remove(@Param('userId', ParseUUIDPipe) userId: string): Promise<void> {
    return this.profileService.remove(userId);
  }
}
