-- Migration: Create Admin Panel Tables
-- Description: إنشاء جداول لوحة الإدارة
-- Version: 005
-- Created: 2024-01-15

-- Create system settings table
CREATE TABLE IF NOT EXISTS system_settings (
    id VARCHAR(50) PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    setting_type VARCHAR(20) NOT NULL CHECK (setting_type IN ('string', 'integer', 'decimal', 'boolean', 'json', 'list')),
    category VARCHAR(50) NOT NULL CHECK (category IN ('general', 'financial', 'security', 'notifications', 'limits', 'fees', 'compliance', 'integrations')),
    description TEXT,
    is_public BOOLEAN NOT NULL DEFAULT false,
    is_encrypted BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    validation_rules JSONB DEFAULT '{}',
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Soft delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by VARCHAR(50)
);

-- Create system settings history table
CREATE TABLE IF NOT EXISTS system_settings_history (
    id VARCHAR(50) PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    updated_by VARCHAR(50) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    
    -- Foreign key
    CONSTRAINT fk_settings_history_key FOREIGN KEY (setting_key) REFERENCES system_settings(key)
);

-- Create user activity log table
CREATE TABLE IF NOT EXISTS user_activity_log (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    performed_by VARCHAR(50),
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_activity_log_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_activity_log_performed_by FOREIGN KEY (performed_by) REFERENCES users(id)
);

-- Create user sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(50) PRIMARY KEY,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    location_info JSONB DEFAULT '{}',
    
    -- Session details
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Security
    login_method VARCHAR(20) DEFAULT 'password' CHECK (login_method IN ('password', 'otp', 'biometric', 'social')),
    two_factor_verified BOOLEAN NOT NULL DEFAULT false,
    
    -- Foreign key
    CONSTRAINT fk_sessions_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create admin notifications table
CREATE TABLE IF NOT EXISTS admin_notifications (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN ('info', 'warning', 'error', 'success', 'alert')),
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    
    -- Target
    target_user_id VARCHAR(50),
    target_role VARCHAR(20),
    is_global BOOLEAN NOT NULL DEFAULT false,
    
    -- Status
    is_read BOOLEAN NOT NULL DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    read_by VARCHAR(50),
    
    -- Actions
    action_url VARCHAR(500),
    action_label VARCHAR(100),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign keys
    CONSTRAINT fk_notifications_target_user FOREIGN KEY (target_user_id) REFERENCES users(id),
    CONSTRAINT fk_notifications_read_by FOREIGN KEY (read_by) REFERENCES users(id)
);

-- Create system alerts table
CREATE TABLE IF NOT EXISTS system_alerts (
    id VARCHAR(50) PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    
    -- Alert details
    source_system VARCHAR(50),
    source_component VARCHAR(100),
    error_code VARCHAR(50),
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved', 'suppressed')),
    acknowledged_by VARCHAR(50),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved_by VARCHAR(50),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    
    -- Occurrence tracking
    first_occurrence TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_occurrence TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    occurrence_count INTEGER NOT NULL DEFAULT 1,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Foreign keys
    CONSTRAINT fk_alerts_acknowledged_by FOREIGN KEY (acknowledged_by) REFERENCES users(id),
    CONSTRAINT fk_alerts_resolved_by FOREIGN KEY (resolved_by) REFERENCES users(id)
);

-- Create audit trail table
CREATE TABLE IF NOT EXISTS audit_trail (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Action details
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(50),
    
    -- User details
    user_id VARCHAR(50),
    user_role VARCHAR(20),
    user_ip INET,
    
    -- Changes
    old_values JSONB,
    new_values JSONB,
    changes_summary TEXT,
    
    -- Context
    request_id VARCHAR(50),
    session_id VARCHAR(50),
    api_endpoint VARCHAR(200),
    http_method VARCHAR(10),
    
    -- Result
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Foreign keys
    CONSTRAINT fk_audit_trail_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create system health metrics table
CREATE TABLE IF NOT EXISTS system_health_metrics (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Metric details
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(20),
    
    -- System details
    component VARCHAR(50) NOT NULL,
    instance_id VARCHAR(50),
    
    -- Thresholds
    warning_threshold DECIMAL(15,4),
    critical_threshold DECIMAL(15,4),
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'normal' CHECK (status IN ('normal', 'warning', 'critical')),
    
    -- Timestamps
    recorded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for better performance

-- System settings indexes
CREATE INDEX idx_system_settings_key ON system_settings(key) WHERE deleted_at IS NULL;
CREATE INDEX idx_system_settings_category ON system_settings(category) WHERE deleted_at IS NULL;
CREATE INDEX idx_system_settings_is_public ON system_settings(is_public) WHERE deleted_at IS NULL;

-- System settings history indexes
CREATE INDEX idx_settings_history_key ON system_settings_history(setting_key);
CREATE INDEX idx_settings_history_updated_at ON system_settings_history(updated_at);

-- User activity log indexes
CREATE INDEX idx_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX idx_activity_log_activity_type ON user_activity_log(activity_type);
CREATE INDEX idx_activity_log_created_at ON user_activity_log(created_at);
CREATE INDEX idx_activity_log_performed_by ON user_activity_log(performed_by);

-- User sessions indexes
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_session_id ON user_sessions(session_id);
CREATE INDEX idx_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_sessions_is_active ON user_sessions(is_active);
CREATE INDEX idx_sessions_last_activity ON user_sessions(last_activity);

-- Admin notifications indexes
CREATE INDEX idx_notifications_target_user ON admin_notifications(target_user_id);
CREATE INDEX idx_notifications_target_role ON admin_notifications(target_role);
CREATE INDEX idx_notifications_is_read ON admin_notifications(is_read);
CREATE INDEX idx_notifications_created_at ON admin_notifications(created_at);
CREATE INDEX idx_notifications_priority ON admin_notifications(priority);

-- System alerts indexes
CREATE INDEX idx_alerts_alert_type ON system_alerts(alert_type);
CREATE INDEX idx_alerts_severity ON system_alerts(severity);
CREATE INDEX idx_alerts_status ON system_alerts(status);
CREATE INDEX idx_alerts_first_occurrence ON system_alerts(first_occurrence);
CREATE INDEX idx_alerts_last_occurrence ON system_alerts(last_occurrence);

-- Audit trail indexes
CREATE INDEX idx_audit_trail_user_id ON audit_trail(user_id);
CREATE INDEX idx_audit_trail_action ON audit_trail(action);
CREATE INDEX idx_audit_trail_resource_type ON audit_trail(resource_type);
CREATE INDEX idx_audit_trail_resource_id ON audit_trail(resource_id);
CREATE INDEX idx_audit_trail_created_at ON audit_trail(created_at);
CREATE INDEX idx_audit_trail_success ON audit_trail(success);

-- System health metrics indexes
CREATE INDEX idx_health_metrics_component ON system_health_metrics(component);
CREATE INDEX idx_health_metrics_metric_name ON system_health_metrics(metric_name);
CREATE INDEX idx_health_metrics_recorded_at ON system_health_metrics(recorded_at);
CREATE INDEX idx_health_metrics_status ON system_health_metrics(status);

-- Create GIN indexes for JSONB fields
CREATE INDEX idx_system_settings_validation_rules ON system_settings USING GIN(validation_rules);
CREATE INDEX idx_activity_log_metadata ON user_activity_log USING GIN(metadata);
CREATE INDEX idx_sessions_device_info ON user_sessions USING GIN(device_info);
CREATE INDEX idx_sessions_location_info ON user_sessions USING GIN(location_info);
CREATE INDEX idx_notifications_metadata ON admin_notifications USING GIN(metadata);
CREATE INDEX idx_alerts_metadata ON system_alerts USING GIN(metadata);
CREATE INDEX idx_audit_trail_old_values ON audit_trail USING GIN(old_values);
CREATE INDEX idx_audit_trail_new_values ON audit_trail USING GIN(new_values);
CREATE INDEX idx_audit_trail_metadata ON audit_trail USING GIN(metadata);
CREATE INDEX idx_health_metrics_metadata ON system_health_metrics USING GIN(metadata);

-- Create functions for ID generation
CREATE OR REPLACE FUNCTION generate_system_setting_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'setting_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_activity_log_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'activity_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_session_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'session_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_notification_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'notif_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_alert_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'alert_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_audit_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'audit_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_health_metric_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'metric_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for ID generation
CREATE TRIGGER generate_system_setting_id_trigger
    BEFORE INSERT ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION generate_system_setting_id();

CREATE TRIGGER generate_activity_log_id_trigger
    BEFORE INSERT ON user_activity_log
    FOR EACH ROW
    EXECUTE FUNCTION generate_activity_log_id();

CREATE TRIGGER generate_session_id_trigger
    BEFORE INSERT ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION generate_session_id();

CREATE TRIGGER generate_notification_id_trigger
    BEFORE INSERT ON admin_notifications
    FOR EACH ROW
    EXECUTE FUNCTION generate_notification_id();

CREATE TRIGGER generate_alert_id_trigger
    BEFORE INSERT ON system_alerts
    FOR EACH ROW
    EXECUTE FUNCTION generate_alert_id();

CREATE TRIGGER generate_audit_id_trigger
    BEFORE INSERT ON audit_trail
    FOR EACH ROW
    EXECUTE FUNCTION generate_audit_id();

CREATE TRIGGER generate_health_metric_id_trigger
    BEFORE INSERT ON system_health_metrics
    FOR EACH ROW
    EXECUTE FUNCTION generate_health_metric_id();

-- Create triggers for updated_at
CREATE TRIGGER update_system_settings_updated_at
    BEFORE UPDATE ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_last_activity
    BEFORE UPDATE ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create settings history
CREATE OR REPLACE FUNCTION create_settings_history()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' AND OLD.value != NEW.value THEN
        INSERT INTO system_settings_history (
            setting_key, old_value, new_value, updated_by
        ) VALUES (
            NEW.key, OLD.value, NEW.value, NEW.updated_by
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for settings history
CREATE TRIGGER create_settings_history_trigger
    AFTER UPDATE ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION create_settings_history();

-- Add comments for documentation
COMMENT ON TABLE system_settings IS 'جدول إعدادات النظام - يحتوي على جميع إعدادات التطبيق';
COMMENT ON COLUMN system_settings.key IS 'مفتاح الإعداد الفريد';
COMMENT ON COLUMN system_settings.value IS 'قيمة الإعداد';
COMMENT ON COLUMN system_settings.is_encrypted IS 'هل القيمة مشفرة';

COMMENT ON TABLE user_activity_log IS 'جدول سجل نشاط المستخدمين - تتبع جميع أنشطة المستخدمين';
COMMENT ON TABLE user_sessions IS 'جدول جلسات المستخدمين - إدارة جلسات تسجيل الدخول';
COMMENT ON TABLE admin_notifications IS 'جدول إشعارات الإدارة - إشعارات للمديرين';
COMMENT ON TABLE system_alerts IS 'جدول تنبيهات النظام - تنبيهات تقنية';
COMMENT ON TABLE audit_trail IS 'جدول مسار التدقيق - تتبع جميع التغييرات المهمة';
COMMENT ON TABLE system_health_metrics IS 'جدول مقاييس صحة النظام - مراقبة الأداء';

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON system_settings TO ws_app_user;
GRANT SELECT, INSERT ON system_settings_history TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON user_activity_log TO ws_app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_sessions TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON admin_notifications TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON system_alerts TO ws_app_user;
GRANT SELECT, INSERT ON audit_trail TO ws_app_user;
GRANT SELECT, INSERT ON system_health_metrics TO ws_app_user;
