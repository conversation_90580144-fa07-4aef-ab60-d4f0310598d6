"""
Admin Panel Tests
================
اختبارات لوحة الإدارة الشاملة
"""

import pytest
import asyncio
from datetime import datetime, date, timedelta
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from admin_panel.admin_dashboard import AdminDashboard, DashboardPeriod
from admin_panel.user_management import UserManagement, UserStatus, KYCStatus
from admin_panel.financial_reports import FinancialReports, ReportType, ReportFormat
from admin_panel.system_settings import SystemSettings, SettingType, SettingCategory


class TestAdminDashboard:
    """اختبارات لوحة الإدارة الرئيسية"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def admin_dashboard(self, mock_db_connection):
        """لوحة الإدارة للاختبار"""
        db_connection, _ = mock_db_connection
        return AdminDashboard(db_connection)
    
    @pytest.mark.asyncio
    async def test_get_system_overview_today(self, admin_dashboard, mock_db_connection):
        """اختبار الحصول على نظرة عامة للنظام - اليوم"""
        db_connection, conn = mock_db_connection
        
        # Setup mocks for helper methods
        admin_dashboard._get_system_metrics = AsyncMock(return_value={
            'total_users': 1000,
            'active_users': 850,
            'new_users_today': 25,
            'total_agents': 150,
            'active_agents': 120,
            'pending_agents': 15
        })
        
        admin_dashboard._get_financial_summary = AsyncMock(return_value={
            'total_revenue': 125000.0,
            'transaction_fees': 100000.0,
            'commission_expenses': 25000.0,
            'net_profit': 75000.0
        })
        
        admin_dashboard._get_user_analytics = AsyncMock(return_value={
            'user_growth_rate': 15.5,
            'user_retention_rate': 85.2,
            'average_session_duration': 1800
        })
        
        admin_dashboard._get_transaction_analytics = AsyncMock(return_value={
            'total_transactions': 5000,
            'successful_transactions': 4850,
            'failed_transactions': 150,
            'success_rate': 97.0
        })
        
        admin_dashboard._get_agent_analytics = AsyncMock(return_value={
            'total_agents': 150,
            'active_agents': 120,
            'top_performing_agents': []
        })
        
        admin_dashboard._get_risk_metrics = AsyncMock(return_value={
            'high_risk_transactions': 25,
            'fraud_alerts': 5,
            'compliance_issues': 2
        })
        
        admin_dashboard._get_system_health = AsyncMock(return_value={
            'uptime': 99.9,
            'response_time': 150,
            'error_rate': 0.1
        })
        
        # Get system overview
        result = await admin_dashboard.get_system_overview(DashboardPeriod.TODAY)
        
        # Assertions
        assert 'period' in result
        assert result['period'] == 'today'
        assert 'system_metrics' in result
        assert 'financial_summary' in result
        assert 'user_analytics' in result
        assert 'transaction_analytics' in result
        assert 'agent_analytics' in result
        assert 'risk_metrics' in result
        assert 'system_health' in result
        assert 'last_updated' in result
        
        # Verify system metrics
        assert result['system_metrics']['total_users'] == 1000
        assert result['system_metrics']['active_users'] == 850
        assert result['system_metrics']['new_users_today'] == 25
        
        # Verify financial summary
        assert result['financial_summary']['total_revenue'] == 125000.0
        assert result['financial_summary']['net_profit'] == 75000.0
    
    @pytest.mark.asyncio
    async def test_get_real_time_metrics(self, admin_dashboard, mock_db_connection):
        """اختبار الحصول على المقاييس في الوقت الفعلي"""
        db_connection, conn = mock_db_connection
        
        # Mock database responses
        conn.fetchval.side_effect = [
            50,  # active_sessions
        ]
        
        conn.fetchrow.side_effect = [
            {
                'count': 25,
                'volume': Decimal('125000.00'),
                'successful': 23,
                'failed': 2
            }  # hourly_stats
        ]
        
        # Mock helper methods
        admin_dashboard._get_system_load_metrics = AsyncMock(return_value={
            'cpu_usage': 45.2,
            'memory_usage': 68.5,
            'disk_usage': 35.8,
            'network_io': 1250
        })
        
        admin_dashboard._get_active_alerts = AsyncMock(return_value={
            'critical': 0,
            'warning': 3,
            'info': 8,
            'total': 11
        })
        
        # Get real-time metrics
        result = await admin_dashboard.get_real_time_metrics()
        
        # Assertions
        assert result['active_sessions'] == 50
        assert result['hourly_transactions']['count'] == 25
        assert result['hourly_transactions']['volume'] == 125000.0
        assert result['hourly_transactions']['successful'] == 23
        assert result['hourly_transactions']['failed'] == 2
        assert result['hourly_transactions']['success_rate'] == 92.0  # 23/25 * 100
        assert 'system_load' in result
        assert 'alerts' in result
        assert 'timestamp' in result
    
    @pytest.mark.asyncio
    async def test_get_financial_dashboard(self, admin_dashboard, mock_db_connection):
        """اختبار الحصول على لوحة التحكم المالية"""
        db_connection, conn = mock_db_connection
        
        # Mock revenue data
        revenue_data = [
            {
                'date': date(2024, 1, 15),
                'transaction_count': 100,
                'total_volume': Decimal('50000.00'),
                'total_fees': Decimal('500.00'),
                'avg_transaction_amount': Decimal('500.00')
            },
            {
                'date': date(2024, 1, 16),
                'transaction_count': 120,
                'total_volume': Decimal('60000.00'),
                'total_fees': Decimal('600.00'),
                'avg_transaction_amount': Decimal('500.00')
            }
        ]
        
        # Mock commission data
        commission_data = [
            {
                'date': date(2024, 1, 15),
                'total_commissions': Decimal('50.00'),
                'commission_count': 10,
                'avg_commission': Decimal('5.00')
            },
            {
                'date': date(2024, 1, 16),
                'total_commissions': Decimal('60.00'),
                'commission_count': 12,
                'avg_commission': Decimal('5.00')
            }
        ]
        
        # Mock top sources
        top_sources = [
            {
                'transaction_type': 'transfer',
                'count': 180,
                'total_volume': Decimal('90000.00'),
                'total_fees': Decimal('900.00')
            },
            {
                'transaction_type': 'payment',
                'count': 40,
                'total_volume': Decimal('20000.00'),
                'total_fees': Decimal('200.00')
            }
        ]
        
        conn.fetch.side_effect = [revenue_data, commission_data, top_sources]
        conn.fetchval.return_value = Decimal('800.00')  # Previous period revenue
        
        # Get financial dashboard
        result = await admin_dashboard.get_financial_dashboard(DashboardPeriod.THIS_MONTH)
        
        # Assertions
        assert result['period'] == 'this_month'
        assert 'summary' in result
        assert 'daily_revenue' in result
        assert 'daily_commissions' in result
        assert 'top_revenue_sources' in result
        
        # Verify summary
        assert result['summary']['total_revenue'] == 1100.0  # 500 + 600
        assert result['summary']['previous_revenue'] == 800.0
        assert result['summary']['total_transactions'] == 220  # 100 + 120
        assert result['summary']['total_commissions'] == 110.0  # 50 + 60
        
        # Verify daily breakdown
        assert len(result['daily_revenue']) == 2
        assert result['daily_revenue'][0]['revenue'] == 500.0
        assert result['daily_revenue'][1]['revenue'] == 600.0
        
        # Verify top sources
        assert len(result['top_revenue_sources']) == 2
        assert result['top_revenue_sources'][0]['type'] == 'transfer'
        assert result['top_revenue_sources'][0]['fees'] == 900.0


class TestUserManagement:
    """اختبارات إدارة المستخدمين"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def user_management(self, mock_db_connection):
        """خدمة إدارة المستخدمين للاختبار"""
        db_connection, _ = mock_db_connection
        return UserManagement(db_connection)
    
    @pytest.mark.asyncio
    async def test_get_users_list_with_pagination(self, user_management, mock_db_connection):
        """اختبار الحصول على قائمة المستخدمين مع التصفح"""
        db_connection, conn = mock_db_connection
        
        # Mock total count
        conn.fetchval.return_value = 150
        
        # Mock users data
        users_data = [
            {
                'id': 'user_001',
                'email': '<EMAIL>',
                'phone': '+966501234567',
                'first_name': 'أحمد',
                'last_name': 'محمد',
                'date_of_birth': date(1990, 1, 1),
                'nationality': 'SA',
                'gender': 'male',
                'role': 'user',
                'is_active': True,
                'is_verified': True,
                'kyc_level': 2,
                'kyc_status': 'approved',
                'country': 'SA',
                'preferred_currency': 'SAR',
                'language': 'ar',
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'last_login': datetime.now() - timedelta(hours=2),
                'login_count': 25,
                'failed_login_attempts': 0
            },
            {
                'id': 'user_002',
                'email': '<EMAIL>',
                'phone': '+966501234568',
                'first_name': 'فاطمة',
                'last_name': 'علي',
                'date_of_birth': date(1985, 5, 15),
                'nationality': 'SA',
                'gender': 'female',
                'role': 'agent',
                'is_active': True,
                'is_verified': True,
                'kyc_level': 3,
                'kyc_status': 'approved',
                'country': 'SA',
                'preferred_currency': 'SAR',
                'language': 'ar',
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'last_login': datetime.now() - timedelta(hours=1),
                'login_count': 50,
                'failed_login_attempts': 0
            }
        ]
        
        conn.fetch.return_value = users_data
        
        # Get users list
        result = await user_management.get_users_list(
            page=1,
            limit=50,
            search=None,
            role=None,
            status=None,
            kyc_status=None
        )
        
        # Assertions
        assert 'users' in result
        assert 'pagination' in result
        assert 'filters' in result
        
        # Verify users data
        assert len(result['users']) == 2
        assert result['users'][0]['id'] == 'user_001'
        assert result['users'][0]['full_name'] == 'أحمد محمد'
        assert result['users'][0]['role'] == 'user'
        assert result['users'][1]['id'] == 'user_002'
        assert result['users'][1]['role'] == 'agent'
        
        # Verify pagination
        assert result['pagination']['current_page'] == 1
        assert result['pagination']['total_count'] == 150
        assert result['pagination']['total_pages'] == 3  # 150 / 50
        assert result['pagination']['has_next'] == True
        assert result['pagination']['has_prev'] == False
    
    @pytest.mark.asyncio
    async def test_update_user_status_success(self, user_management, mock_db_connection):
        """اختبار تحديث حالة المستخدم بنجاح"""
        db_connection, conn = mock_db_connection
        
        # Mock successful update
        conn.fetchval.return_value = '<EMAIL>'
        
        # Mock activity logging
        user_management._log_user_activity = AsyncMock()
        
        # Update user status
        result = await user_management.update_user_status(
            user_id='user_001',
            is_active=False,
            updated_by='admin_001',
            reason='Account suspended due to suspicious activity'
        )
        
        # Assertions
        assert result == True
        
        # Verify database call
        conn.fetchval.assert_called_once()
        
        # Verify activity logging
        user_management._log_user_activity.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_kyc_status_success(self, user_management, mock_db_connection):
        """اختبار تحديث حالة KYC بنجاح"""
        db_connection, conn = mock_db_connection
        
        # Mock successful update
        conn.fetchval.return_value = '<EMAIL>'
        
        # Mock activity logging
        user_management._log_user_activity = AsyncMock()
        
        # Update KYC status
        result = await user_management.update_kyc_status(
            user_id='user_001',
            kyc_status='approved',
            kyc_level=3,
            updated_by='admin_001',
            notes='All documents verified successfully'
        )
        
        # Assertions
        assert result == True
        
        # Verify database call
        conn.fetchval.assert_called_once()
        
        # Verify activity logging
        user_management._log_user_activity.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_user_statistics(self, user_management, mock_db_connection):
        """اختبار الحصول على إحصائيات المستخدمين"""
        db_connection, conn = mock_db_connection
        
        # Mock statistics data
        stats_data = {
            'total_users': 1000,
            'active_users': 850,
            'verified_users': 750,
            'kyc_approved_users': 600,
            'new_users_30d': 150,
            'active_users_7d': 400
        }
        
        role_distribution = [
            {'role': 'user', 'count': 800},
            {'role': 'agent', 'count': 150},
            {'role': 'admin', 'count': 50}
        ]
        
        geo_distribution = [
            {'country': 'SA', 'count': 900},
            {'country': 'AE', 'count': 50},
            {'country': 'KW', 'count': 30}
        ]
        
        conn.fetchrow.return_value = stats_data
        conn.fetch.side_effect = [role_distribution, geo_distribution]
        
        # Get statistics
        result = await user_management.get_user_statistics()
        
        # Assertions
        assert result['total_users'] == 1000
        assert result['active_users'] == 850
        assert result['verified_users'] == 750
        assert result['kyc_approved_users'] == 600
        assert result['activity_rate'] == 40.0  # 400/1000 * 100
        assert result['verification_rate'] == 75.0  # 750/1000 * 100
        assert result['kyc_approval_rate'] == 60.0  # 600/1000 * 100
        
        # Verify role distribution
        assert len(result['role_distribution']) == 3
        assert result['role_distribution'][0]['role'] == 'user'
        assert result['role_distribution'][0]['count'] == 800
        
        # Verify geographic distribution
        assert len(result['geographic_distribution']) == 3
        assert result['geographic_distribution'][0]['country'] == 'SA'
        assert result['geographic_distribution'][0]['count'] == 900


class TestFinancialReports:
    """اختبارات التقارير المالية"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def financial_reports(self, mock_db_connection):
        """خدمة التقارير المالية للاختبار"""
        db_connection, _ = mock_db_connection
        return FinancialReports(db_connection)
    
    @pytest.mark.asyncio
    async def test_generate_revenue_report(self, financial_reports, mock_db_connection):
        """اختبار إنشاء تقرير الإيرادات"""
        db_connection, conn = mock_db_connection
        
        # Mock revenue data
        revenue_data = [
            {
                'date': date(2024, 1, 15),
                'transaction_count': 100,
                'total_volume': Decimal('50000.00'),
                'total_fees': Decimal('500.00'),
                'avg_amount': Decimal('500.00'),
                'successful_count': 95,
                'failed_count': 5
            }
        ]
        
        # Mock commission data
        commission_data = [
            {
                'date': date(2024, 1, 15),
                'total_commissions': Decimal('50.00'),
                'commission_count': 10
            }
        ]
        
        conn.fetch.side_effect = [revenue_data, commission_data]
        conn.fetchval.return_value = Decimal('400.00')  # Previous period revenue
        
        # Mock projections calculation
        financial_reports._calculate_revenue_projections = AsyncMock(return_value={
            'next_30_days': {
                'projected_revenue': 15000.0,
                'confidence': 'medium',
                'based_on_days': 7
            }
        })
        
        # Generate revenue report
        result = await financial_reports.generate_revenue_report(
            start_date=date(2024, 1, 15),
            end_date=date(2024, 1, 15),
            include_projections=True
        )
        
        # Assertions
        assert result['report_type'] == 'revenue'
        assert 'period' in result
        assert 'summary' in result
        assert 'daily_breakdown' in result
        assert 'projections' in result
        
        # Verify summary
        assert result['summary']['total_revenue'] == 500.0
        assert result['summary']['total_commissions'] == 50.0
        assert result['summary']['net_profit'] == 450.0
        assert result['summary']['total_transactions'] == 100
        assert result['summary']['growth_rate'] == 25.0  # (500-400)/400 * 100
        
        # Verify daily breakdown
        assert len(result['daily_breakdown']) == 1
        assert result['daily_breakdown'][0]['total_fees'] == 500.0
        assert result['daily_breakdown'][0]['total_commissions'] == 50.0
        assert result['daily_breakdown'][0]['net_profit'] == 450.0
        
        # Verify projections
        assert result['projections']['next_30_days']['projected_revenue'] == 15000.0
    
    @pytest.mark.asyncio
    async def test_generate_transaction_report(self, financial_reports, mock_db_connection):
        """اختبار إنشاء تقرير المعاملات"""
        db_connection, conn = mock_db_connection
        
        # Mock transaction statistics
        stats_data = {
            'total_transactions': 1000,
            'successful_transactions': 950,
            'failed_transactions': 40,
            'pending_transactions': 10,
            'total_volume': Decimal('500000.00'),
            'avg_amount': Decimal('500.00'),
            'min_amount': Decimal('10.00'),
            'max_amount': Decimal('50000.00')
        }
        
        # Mock transaction types
        types_data = [
            {
                'type': 'transfer',
                'count': 800,
                'volume': Decimal('400000.00'),
                'successful_count': 760
            },
            {
                'type': 'payment',
                'count': 200,
                'volume': Decimal('100000.00'),
                'successful_count': 190
            }
        ]
        
        # Mock hourly distribution
        hourly_data = [
            {'hour': 9, 'transaction_count': 50, 'volume': Decimal('25000.00')},
            {'hour': 10, 'transaction_count': 75, 'volume': Decimal('37500.00')},
            {'hour': 11, 'transaction_count': 100, 'volume': Decimal('50000.00')}
        ]
        
        # Mock daily trend
        daily_data = [
            {
                'date': date(2024, 1, 15),
                'transaction_count': 500,
                'successful_count': 475,
                'volume': Decimal('250000.00')
            },
            {
                'date': date(2024, 1, 16),
                'transaction_count': 500,
                'successful_count': 475,
                'volume': Decimal('250000.00')
            }
        ]
        
        conn.fetchrow.return_value = stats_data
        conn.fetch.side_effect = [types_data, hourly_data, daily_data]
        
        # Generate transaction report
        result = await financial_reports.generate_transaction_report(
            start_date=date(2024, 1, 15),
            end_date=date(2024, 1, 16)
        )
        
        # Assertions
        assert result['report_type'] == 'transactions'
        assert 'summary' in result
        assert 'transaction_types' in result
        assert 'hourly_distribution' in result
        assert 'daily_trend' in result
        
        # Verify summary
        assert result['summary']['total_transactions'] == 1000
        assert result['summary']['successful_transactions'] == 950
        assert result['summary']['success_rate'] == 95.0
        assert result['summary']['total_volume'] == 500000.0
        
        # Verify transaction types
        assert len(result['transaction_types']) == 2
        assert result['transaction_types'][0]['type'] == 'transfer'
        assert result['transaction_types'][0]['success_rate'] == 95.0  # 760/800 * 100
        
        # Verify hourly distribution
        assert len(result['hourly_distribution']) == 3
        assert result['hourly_distribution'][0]['hour'] == 9
        assert result['hourly_distribution'][2]['transaction_count'] == 100
        
        # Verify daily trend
        assert len(result['daily_trend']) == 2
        assert result['daily_trend'][0]['success_rate'] == 95.0  # 475/500 * 100


class TestSystemSettings:
    """اختبارات إعدادات النظام"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def system_settings(self, mock_db_connection):
        """خدمة إعدادات النظام للاختبار"""
        db_connection, _ = mock_db_connection
        return SystemSettings(db_connection)
    
    @pytest.mark.asyncio
    async def test_get_setting_from_database(self, system_settings, mock_db_connection):
        """اختبار الحصول على إعداد من قاعدة البيانات"""
        db_connection, conn = mock_db_connection
        
        # Mock database response
        conn.fetchrow.return_value = {
            'value': '100000.00',
            'setting_type': 'decimal',
            'is_encrypted': False
        }
        
        # Get setting
        result = await system_settings.get_setting('financial.max_transaction_amount')
        
        # Assertions
        assert result == Decimal('100000.00')
        conn.fetchrow.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_setting_default_value(self, system_settings):
        """اختبار الحصول على القيمة الافتراضية للإعداد"""
        # Mock empty database response
        system_settings._is_cached = Mock(return_value=False)
        
        async def mock_get_connection():
            conn = AsyncMock()
            conn.fetchrow.return_value = None
            return conn
        
        system_settings.db_connection.get_connection.return_value.__aenter__ = mock_get_connection
        
        # Get setting (should return default)
        result = await system_settings.get_setting('financial.min_transaction_amount')
        
        # Assertions
        assert result == Decimal('10.00')  # Default value
    
    @pytest.mark.asyncio
    async def test_set_setting_success(self, system_settings, mock_db_connection):
        """اختبار تعيين إعداد بنجاح"""
        db_connection, conn = mock_db_connection
        
        # Mock successful upsert
        conn.fetchval.return_value = 'financial.max_transaction_amount'
        
        # Mock helper methods
        system_settings._validate_setting_value = Mock(return_value=Decimal('150000.00'))
        system_settings._serialize_value = Mock(return_value='150000.00')
        system_settings._cache_setting = Mock()
        system_settings._log_setting_change = AsyncMock()
        
        # Set setting
        result = await system_settings.set_setting(
            key='financial.max_transaction_amount',
            value=150000.00,
            updated_by='admin_001'
        )
        
        # Assertions
        assert result == True
        conn.fetchval.assert_called_once()
        system_settings._cache_setting.assert_called_once()
        system_settings._log_setting_change.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_bulk_update_settings(self, system_settings):
        """اختبار التحديث المتعدد للإعدادات"""
        # Mock set_setting method
        system_settings.set_setting = AsyncMock(side_effect=[True, True, False])
        
        settings_to_update = {
            'financial.max_transaction_amount': 150000.00,
            'security.session_timeout_minutes': 45,
            'invalid.setting.key': 'value'
        }
        
        # Bulk update settings
        result = await system_settings.bulk_update_settings(
            settings=settings_to_update,
            updated_by='admin_001'
        )
        
        # Assertions
        assert len(result) == 3
        assert result['financial.max_transaction_amount'] == True
        assert result['security.session_timeout_minutes'] == True
        assert result['invalid.setting.key'] == False
        
        # Verify set_setting was called for each setting
        assert system_settings.set_setting.call_count == 3
    
    def test_validate_setting_value_integer(self, system_settings):
        """اختبار التحقق من صحة قيمة الإعداد - عدد صحيح"""
        setting_info = {
            'type': SettingType.INTEGER,
            'validation_rules': {'min_value': 1, 'max_value': 100}
        }
        
        # Valid integer
        result = system_settings._validate_setting_value('test.setting', 50, setting_info)
        assert result == 50
        
        # String that can be converted to integer
        result = system_settings._validate_setting_value('test.setting', '75', setting_info)
        assert result == 75
        
        # Value below minimum
        with pytest.raises(ValueError, match="must be at least"):
            system_settings._validate_setting_value('test.setting', 0, setting_info)
        
        # Value above maximum
        with pytest.raises(ValueError, match="must be at most"):
            system_settings._validate_setting_value('test.setting', 150, setting_info)
    
    def test_validate_setting_value_boolean(self, system_settings):
        """اختبار التحقق من صحة قيمة الإعداد - منطقي"""
        setting_info = {'type': SettingType.BOOLEAN}
        
        # Valid boolean
        result = system_settings._validate_setting_value('test.setting', True, setting_info)
        assert result == True
        
        # String representations
        result = system_settings._validate_setting_value('test.setting', 'true', setting_info)
        assert result == True
        
        result = system_settings._validate_setting_value('test.setting', 'false', setting_info)
        assert result == False
        
        result = system_settings._validate_setting_value('test.setting', '1', setting_info)
        assert result == True
        
        result = system_settings._validate_setting_value('test.setting', '0', setting_info)
        assert result == False


# Integration Tests
class TestAdminPanelIntegration:
    """اختبارات التكامل للوحة الإدارة"""
    
    @pytest.mark.asyncio
    async def test_dashboard_with_real_data_flow(self):
        """اختبار تدفق البيانات الحقيقي للوحة التحكم"""
        # This would test the complete flow:
        # 1. Get system overview
        # 2. Fetch real-time metrics
        # 3. Generate financial reports
        # 4. Update system settings
        pass


# Performance Tests
class TestAdminPanelPerformance:
    """اختبارات الأداء للوحة الإدارة"""
    
    def test_dashboard_cache_performance(self):
        """اختبار أداء التخزين المؤقت للوحة التحكم"""
        # Test that dashboard caching improves performance
        pass
    
    def test_report_generation_performance(self):
        """اختبار أداء إنشاء التقارير"""
        # Test that report generation completes within acceptable time
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
