"""
Payment API
===========
واجهة برمجة التطبيقات للدفع والتسوية
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.security import HTTPBearer
from pydantic import BaseModel, Field, validator
from typing import Dict, List, Optional, Any
from datetime import datetime, date
from decimal import Decimal
import logging

from .payment_gateway_service import (
    PaymentGatewayService, PaymentRequest, PaymentResponse, RefundRequest, RefundResponse,
    PaymentMethod, PaymentProvider, PaymentStatus, NotificationRecipient
)
from .settlement_service import (
    SettlementService, SettlementRequest, SettlementResponse, BalanceInfo,
    SettlementType, SettlementStatus
)
from .risk_management_service import (
    RiskManagementService, RiskAssessmentRequest, RiskAssessmentResult,
    RiskLevel, RiskAction
)
from .financial_reports_service import (
    FinancialReportsService, ReportRequest, ReportResult,
    ReportType, ReportFormat, ReportPeriod
)
from ..shared.auth.auth_service import get_current_user, require_permissions
from ..shared.database.connection import get_db_connection

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/payment", tags=["Payment"])
security = HTTPBearer()


# Pydantic Models
class PaymentRequestModel(BaseModel):
    """نموذج طلب الدفع"""
    amount: Decimal = Field(..., gt=0, description="مبلغ الدفع")
    currency: str = Field(..., min_length=3, max_length=3, description="العملة")
    payment_method: PaymentMethod = Field(..., description="طريقة الدفع")
    provider: PaymentProvider = Field(..., description="مقدم الخدمة")
    customer_id: str = Field(..., description="معرف العميل")
    description: str = Field(..., max_length=500, description="وصف الدفع")
    metadata: Optional[Dict[str, Any]] = Field(None, description="بيانات إضافية")
    return_url: Optional[str] = Field(None, description="رابط العودة")
    webhook_url: Optional[str] = Field(None, description="رابط الإشعار")
    expires_at: Optional[datetime] = Field(None, description="تاريخ انتهاء الصلاحية")
    
    @validator('currency')
    def validate_currency(cls, v):
        if v.upper() not in ['SAR', 'USD', 'EUR', 'GBP']:
            raise ValueError('Unsupported currency')
        return v.upper()


class RefundRequestModel(BaseModel):
    """نموذج طلب الاسترداد"""
    payment_id: str = Field(..., description="معرف الدفع")
    amount: Optional[Decimal] = Field(None, gt=0, description="مبلغ الاسترداد")
    reason: Optional[str] = Field(None, max_length=500, description="سبب الاسترداد")
    metadata: Optional[Dict[str, Any]] = Field(None, description="بيانات إضافية")


class SettlementRequestModel(BaseModel):
    """نموذج طلب التسوية"""
    account_id: str = Field(..., description="معرف الحساب")
    amount: Decimal = Field(..., gt=0, description="مبلغ التسوية")
    currency: str = Field(..., min_length=3, max_length=3, description="العملة")
    settlement_type: SettlementType = Field(..., description="نوع التسوية")
    description: str = Field(..., max_length=500, description="وصف التسوية")
    reference_transactions: Optional[List[str]] = Field(None, description="المعاملات المرجعية")
    metadata: Optional[Dict[str, Any]] = Field(None, description="بيانات إضافية")
    scheduled_at: Optional[datetime] = Field(None, description="وقت الجدولة")


class RiskAssessmentRequestModel(BaseModel):
    """نموذج طلب تقييم المخاطر"""
    user_id: str = Field(..., description="معرف المستخدم")
    transaction_amount: Decimal = Field(..., gt=0, description="مبلغ المعاملة")
    currency: str = Field(..., min_length=3, max_length=3, description="العملة")
    transaction_type: str = Field(..., description="نوع المعاملة")
    payment_method: str = Field(..., description="طريقة الدفع")
    ip_address: Optional[str] = Field(None, description="عنوان IP")
    device_fingerprint: Optional[str] = Field(None, description="بصمة الجهاز")
    location: Optional[Dict[str, Any]] = Field(None, description="الموقع")
    metadata: Optional[Dict[str, Any]] = Field(None, description="بيانات إضافية")


class ReportRequestModel(BaseModel):
    """نموذج طلب التقرير"""
    report_type: ReportType = Field(..., description="نوع التقرير")
    period: ReportPeriod = Field(..., description="فترة التقرير")
    start_date: date = Field(..., description="تاريخ البداية")
    end_date: date = Field(..., description="تاريخ النهاية")
    format: ReportFormat = Field(ReportFormat.JSON, description="تنسيق التقرير")
    filters: Optional[Dict[str, Any]] = Field(None, description="المرشحات")
    include_charts: bool = Field(False, description="تضمين الرسوم البيانية")
    include_predictions: bool = Field(False, description="تضمين التنبؤات")
    
    @validator('end_date')
    def validate_dates(cls, v, values):
        if 'start_date' in values and v < values['start_date']:
            raise ValueError('End date must be after start date')
        return v


# Dependency injection
async def get_payment_service() -> PaymentGatewayService:
    """الحصول على خدمة الدفع"""
    db_connection = await get_db_connection()
    return PaymentGatewayService(db_connection)


async def get_settlement_service() -> SettlementService:
    """الحصول على خدمة التسوية"""
    db_connection = await get_db_connection()
    return SettlementService(db_connection)


async def get_risk_service() -> RiskManagementService:
    """الحصول على خدمة إدارة المخاطر"""
    db_connection = await get_db_connection()
    return RiskManagementService(db_connection)


async def get_reports_service() -> FinancialReportsService:
    """الحصول على خدمة التقارير"""
    db_connection = await get_db_connection()
    return FinancialReportsService(db_connection)


# Payment Endpoints
@router.post("/payments", response_model=Dict[str, Any])
async def create_payment(
    request: PaymentRequestModel,
    current_user: Dict = Depends(get_current_user),
    payment_service: PaymentGatewayService = Depends(get_payment_service),
    risk_service: RiskManagementService = Depends(get_risk_service)
):
    """إنشاء دفعة جديدة"""
    try:
        logger.info(f"💳 Creating payment: {request.amount} {request.currency} for {request.customer_id}")
        
        # Risk assessment
        risk_request = RiskAssessmentRequest(
            user_id=request.customer_id,
            transaction_amount=request.amount,
            currency=request.currency,
            transaction_type="payment",
            payment_method=request.payment_method.value,
            metadata=request.metadata
        )
        
        risk_result = await risk_service.assess_risk(risk_request)
        
        # Check if payment should be blocked
        if risk_result.recommended_action == RiskAction.BLOCK:
            raise HTTPException(
                status_code=403,
                detail={
                    "error": "Payment blocked due to high risk",
                    "risk_score": risk_result.risk_score,
                    "risk_level": risk_result.risk_level.value,
                    "assessment_id": risk_result.assessment_id
                }
            )
        
        # Create payment request
        payment_request = PaymentRequest(
            amount=request.amount,
            currency=request.currency,
            payment_method=request.payment_method,
            provider=request.provider,
            customer_id=request.customer_id,
            description=request.description,
            metadata=request.metadata,
            return_url=request.return_url,
            webhook_url=request.webhook_url,
            expires_at=request.expires_at
        )
        
        # Process payment
        result = await payment_service.process_payment(payment_request)
        
        return {
            "success": True,
            "payment": {
                "payment_id": result.payment_id,
                "status": result.status.value,
                "amount": float(result.amount),
                "currency": result.currency,
                "provider_transaction_id": result.provider_transaction_id,
                "payment_url": result.payment_url,
                "created_at": result.created_at.isoformat() if result.created_at else None
            },
            "risk_assessment": {
                "assessment_id": risk_result.assessment_id,
                "risk_score": risk_result.risk_score,
                "risk_level": risk_result.risk_level.value,
                "requires_manual_review": risk_result.requires_manual_review
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Payment creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/payments/{payment_id}", response_model=Dict[str, Any])
async def get_payment(
    payment_id: str,
    current_user: Dict = Depends(get_current_user),
    payment_service: PaymentGatewayService = Depends(get_payment_service)
):
    """الحصول على تفاصيل الدفع"""
    try:
        result = await payment_service.get_payment_status(payment_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="Payment not found")
        
        return {
            "success": True,
            "payment": {
                "payment_id": result.payment_id,
                "status": result.status.value,
                "amount": float(result.amount),
                "currency": result.currency,
                "provider_transaction_id": result.provider_transaction_id,
                "created_at": result.created_at.isoformat() if result.created_at else None,
                "updated_at": result.updated_at.isoformat() if result.updated_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get payment: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/payments/{payment_id}/refund", response_model=Dict[str, Any])
async def refund_payment(
    payment_id: str,
    request: RefundRequestModel,
    current_user: Dict = Depends(get_current_user),
    payment_service: PaymentGatewayService = Depends(get_payment_service)
):
    """استرداد دفعة"""
    try:
        # Verify payment_id matches request
        if request.payment_id != payment_id:
            raise HTTPException(status_code=400, detail="Payment ID mismatch")
        
        refund_request = RefundRequest(
            payment_id=request.payment_id,
            amount=request.amount,
            reason=request.reason,
            metadata=request.metadata
        )
        
        result = await payment_service.process_refund(refund_request)
        
        return {
            "success": True,
            "refund": {
                "refund_id": result.refund_id,
                "payment_id": result.payment_id,
                "status": result.status.value,
                "amount": float(result.amount),
                "currency": result.currency,
                "provider_refund_id": result.provider_refund_id,
                "created_at": result.created_at.isoformat() if result.created_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Refund processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/payments/statistics", response_model=Dict[str, Any])
async def get_payment_statistics(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    provider: Optional[PaymentProvider] = Query(None, description="مقدم الخدمة"),
    current_user: Dict = Depends(get_current_user),
    payment_service: PaymentGatewayService = Depends(get_payment_service)
):
    """الحصول على إحصائيات الدفع"""
    try:
        start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
        end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None
        
        stats = await payment_service.get_payment_statistics(
            start_date=start_datetime,
            end_date=end_datetime,
            provider=provider
        )
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get payment statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Settlement Endpoints
@router.post("/settlements", response_model=Dict[str, Any])
async def create_settlement(
    request: SettlementRequestModel,
    current_user: Dict = Depends(get_current_user),
    settlement_service: SettlementService = Depends(get_settlement_service)
):
    """إنشاء تسوية جديدة"""
    try:
        settlement_request = SettlementRequest(
            account_id=request.account_id,
            amount=request.amount,
            currency=request.currency,
            settlement_type=request.settlement_type,
            description=request.description,
            reference_transactions=request.reference_transactions,
            metadata=request.metadata,
            scheduled_at=request.scheduled_at
        )
        
        result = await settlement_service.process_settlement(settlement_request)
        
        return {
            "success": True,
            "settlement": {
                "settlement_id": result.settlement_id,
                "status": result.status.value,
                "account_id": result.account_id,
                "amount": float(result.amount),
                "currency": result.currency,
                "fees_amount": float(result.fees_amount),
                "net_amount": float(result.net_amount),
                "reference_number": result.reference_number,
                "created_at": result.created_at.isoformat() if result.created_at else None
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Settlement creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/settlements/{settlement_id}", response_model=Dict[str, Any])
async def get_settlement(
    settlement_id: str,
    current_user: Dict = Depends(get_current_user),
    settlement_service: SettlementService = Depends(get_settlement_service)
):
    """الحصول على تفاصيل التسوية"""
    try:
        result = await settlement_service.get_settlement_status(settlement_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="Settlement not found")
        
        return {
            "success": True,
            "settlement": {
                "settlement_id": result.settlement_id,
                "status": result.status.value,
                "account_id": result.account_id,
                "amount": float(result.amount),
                "currency": result.currency,
                "fees_amount": float(result.fees_amount),
                "net_amount": float(result.net_amount),
                "bank_reference": result.bank_reference,
                "created_at": result.created_at.isoformat() if result.created_at else None,
                "completed_at": result.completed_at.isoformat() if result.completed_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get settlement: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/accounts/{account_id}/balance", response_model=Dict[str, Any])
async def get_account_balance(
    account_id: str,
    currency: str = Query(..., description="العملة"),
    current_user: Dict = Depends(get_current_user),
    settlement_service: SettlementService = Depends(get_settlement_service)
):
    """الحصول على رصيد الحساب"""
    try:
        balance = await settlement_service.get_account_balance(account_id, currency)
        
        return {
            "success": True,
            "balance": {
                "account_id": balance.account_id,
                "available_balance": float(balance.available_balance),
                "pending_balance": float(balance.pending_balance),
                "reserved_balance": float(balance.reserved_balance),
                "total_balance": float(balance.total_balance),
                "currency": balance.currency,
                "last_updated": balance.last_updated.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get account balance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/settlements/pending", response_model=Dict[str, Any])
async def get_pending_settlements(
    account_id: Optional[str] = Query(None, description="معرف الحساب"),
    current_user: Dict = Depends(get_current_user),
    settlement_service: SettlementService = Depends(get_settlement_service)
):
    """الحصول على التسويات المعلقة"""
    try:
        settlements = await settlement_service.get_pending_settlements(account_id)
        
        return {
            "success": True,
            "pending_settlements": settlements
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get pending settlements: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Risk Management Endpoints
@router.post("/risk/assess", response_model=Dict[str, Any])
async def assess_risk(
    request: RiskAssessmentRequestModel,
    current_user: Dict = Depends(get_current_user),
    risk_service: RiskManagementService = Depends(get_risk_service)
):
    """تقييم المخاطر"""
    try:
        risk_request = RiskAssessmentRequest(
            user_id=request.user_id,
            transaction_amount=request.transaction_amount,
            currency=request.currency,
            transaction_type=request.transaction_type,
            payment_method=request.payment_method,
            ip_address=request.ip_address,
            device_fingerprint=request.device_fingerprint,
            location=request.location,
            metadata=request.metadata
        )
        
        result = await risk_service.assess_risk(risk_request)
        
        return {
            "success": True,
            "risk_assessment": {
                "assessment_id": result.assessment_id,
                "risk_score": result.risk_score,
                "risk_level": result.risk_level.value,
                "recommended_action": result.recommended_action.value,
                "requires_manual_review": result.requires_manual_review,
                "risk_factors": result.risk_factors,
                "compliance_checks": result.compliance_checks,
                "limits_applied": result.limits_applied,
                "created_at": result.created_at.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Risk assessment failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/risk/users/{user_id}/profile", response_model=Dict[str, Any])
async def get_user_risk_profile(
    user_id: str,
    current_user: Dict = Depends(get_current_user),
    risk_service: RiskManagementService = Depends(get_risk_service)
):
    """الحصول على ملف المخاطر للمستخدم"""
    try:
        profile = await risk_service.get_user_risk_profile(user_id)
        
        return {
            "success": True,
            "risk_profile": profile
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get user risk profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/risk/statistics", response_model=Dict[str, Any])
async def get_risk_statistics(
    start_date: Optional[datetime] = Query(None, description="تاريخ البداية"),
    end_date: Optional[datetime] = Query(None, description="تاريخ النهاية"),
    current_user: Dict = Depends(get_current_user),
    risk_service: RiskManagementService = Depends(get_risk_service)
):
    """الحصول على إحصائيات المخاطر"""
    try:
        stats = await risk_service.get_risk_statistics(
            start_date=start_date,
            end_date=end_date
        )
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get risk statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Reports Endpoints
@router.post("/reports", response_model=Dict[str, Any])
async def generate_report(
    request: ReportRequestModel,
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user),
    reports_service: FinancialReportsService = Depends(get_reports_service)
):
    """إنشاء تقرير مالي"""
    try:
        report_request = ReportRequest(
            report_type=request.report_type,
            period=request.period,
            start_date=request.start_date,
            end_date=request.end_date,
            format=request.format,
            filters=request.filters,
            include_charts=request.include_charts,
            include_predictions=request.include_predictions,
            requested_by=current_user.get("user_id")
        )
        
        # For large reports, process in background
        if (request.end_date - request.start_date).days > 30:
            background_tasks.add_task(reports_service.generate_report, report_request)
            return {
                "success": True,
                "message": "Report generation started in background",
                "status": "processing"
            }
        
        # Generate report immediately for small requests
        result = await reports_service.generate_report(report_request)
        
        return {
            "success": True,
            "report": {
                "report_id": result.report_id,
                "report_type": result.report_type.value,
                "period": result.period.value,
                "start_date": result.start_date.isoformat(),
                "end_date": result.end_date.isoformat(),
                "format": result.format.value,
                "file_path": result.file_path,
                "file_size": result.file_size,
                "generated_at": result.generated_at.isoformat(),
                "expires_at": result.expires_at.isoformat() if result.expires_at else None,
                "data": result.data if result.format == ReportFormat.JSON else None
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Report generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/reports/{report_id}", response_model=Dict[str, Any])
async def get_report(
    report_id: str,
    current_user: Dict = Depends(get_current_user),
    reports_service: FinancialReportsService = Depends(get_reports_service)
):
    """الحصول على تقرير"""
    try:
        status = await reports_service.get_report_status(report_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Report not found")
        
        return {
            "success": True,
            "report": status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/reports", response_model=Dict[str, Any])
async def list_reports(
    report_type: Optional[ReportType] = Query(None, description="نوع التقرير"),
    limit: int = Query(50, ge=1, le=100, description="عدد النتائج"),
    current_user: Dict = Depends(get_current_user),
    reports_service: FinancialReportsService = Depends(get_reports_service)
):
    """قائمة التقارير المتاحة"""
    try:
        reports = await reports_service.get_available_reports(
            user_id=current_user.get("user_id"),
            report_type=report_type,
            limit=limit
        )
        
        return {
            "success": True,
            "reports": reports,
            "total": len(reports)
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to list reports: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Webhook Endpoints
@router.post("/webhooks/{provider}")
async def handle_webhook(
    provider: PaymentProvider,
    payload: Dict[str, Any],
    signature: str = None,
    payment_service: PaymentGatewayService = Depends(get_payment_service)
):
    """معالجة webhook من مقدم الخدمة"""
    try:
        success = await payment_service.handle_webhook(provider, payload, signature)
        
        if success:
            return {"success": True, "message": "Webhook processed successfully"}
        else:
            raise HTTPException(status_code=400, detail="Invalid webhook")
        
    except Exception as e:
        logger.error(f"❌ Webhook processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Service Status Endpoints
@router.get("/status", response_model=Dict[str, Any])
async def get_service_status(
    current_user: Dict = Depends(get_current_user),
    payment_service: PaymentGatewayService = Depends(get_payment_service),
    settlement_service: SettlementService = Depends(get_settlement_service),
    risk_service: RiskManagementService = Depends(get_risk_service),
    reports_service: FinancialReportsService = Depends(get_reports_service)
):
    """الحصول على حالة الخدمات"""
    try:
        return {
            "success": True,
            "services": {
                "payment_gateway": await payment_service.get_service_statistics(),
                "settlement": await settlement_service.get_service_statistics(),
                "risk_management": await risk_service.get_service_statistics(),
                "financial_reports": await reports_service.get_service_statistics()
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get service status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
