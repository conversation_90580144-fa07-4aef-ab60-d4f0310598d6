{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestResolver", "title": "Nest Resolver Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the resolver.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the resolver?"}, "path": {"type": "string", "format": "path", "description": "The path to create the resolver."}, "language": {"type": "string", "description": "Nest resolver language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest resolver source root directory."}, "flat": {"type": "boolean", "default": false, "description": "Flag to indicate if a directory is created."}, "spec": {"type": "boolean", "default": true, "description": "Specifies if a spec file is generated."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}}, "required": ["name"]}