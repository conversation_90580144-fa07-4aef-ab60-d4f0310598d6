{"version": 3, "file": "getExampleNumber.js", "names": ["PhoneNumber", "getExampleNumber", "country", "examples", "metadata"], "sources": ["../source/getExampleNumber.js"], "sourcesContent": ["import PhoneNumber from './PhoneNumber.js'\r\n\r\nexport default function getExampleNumber(country, examples, metadata) {\r\n\tif (examples[country]) {\r\n\t\treturn new PhoneNumber(country, examples[country], metadata)\r\n\t}\r\n}"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,kBAAxB;AAEA,eAAe,SAASC,gBAAT,CAA0BC,OAA1B,EAAmCC,QAAnC,EAA6CC,QAA7C,EAAuD;EACrE,IAAID,QAAQ,CAACD,OAAD,CAAZ,EAAuB;IACtB,OAAO,IAAIF,WAAJ,CAAgBE,OAAhB,EAAyBC,QAAQ,CAACD,OAAD,CAAjC,EAA4CE,QAA5C,CAAP;EACA;AACD"}