import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  Link,
  Container,
  Paper,
} from '@mui/material';
import {
  Email,
  Send,
  ArrowBack,
  CheckCircle,
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Head from 'next/head';

const validationSchema = Yup.object({
  email: Yup.string()
    .email('البريد الإلكتروني غير صالح')
    .required('البريد الإلكتروني مطلوب'),
});

const ForgotPasswordPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const formik = useFormik({
    initialValues: {
      email: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/auth/forgot-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: values.email.toLowerCase(),
          }),
        });

        const data = await response.json();

        if (response.ok) {
          setSuccess(true);
        } else {
          setError(data.message || 'حدث خطأ في إرسال رسالة إعادة تعيين كلمة المرور');
        }
      } catch (err) {
        setError('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    },
  });

  if (success) {
    return (
      <>
        <Head>
          <title>تم إرسال رسالة إعادة تعيين كلمة المرور - WS Transfir</title>
        </Head>

        <Box
          sx={{
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 2,
          }}
        >
          <Container maxWidth="sm">
            <Paper
              elevation={24}
              sx={{
                borderRadius: 4,
                overflow: 'hidden',
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
              }}
            >
              <Box
                sx={{
                  background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                  color: 'white',
                  padding: 3,
                  textAlign: 'center',
                }}
              >
                <CheckCircle sx={{ fontSize: 60, mb: 2 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  تم الإرسال بنجاح
                </Typography>
              </Box>

              <CardContent sx={{ padding: 4, textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  تم إرسال رسالة إعادة تعيين كلمة المرور
                </Typography>
                
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                  إذا كان البريد الإلكتروني <strong>{formik.values.email}</strong> موجود في نظامنا،
                  ستتلقى رسالة تحتوي على رابط لإعادة تعيين كلمة المرور خلال دقائق قليلة.
                </Typography>

                <Alert severity="info" sx={{ mb: 3, textAlign: 'right' }}>
                  <Typography variant="body2">
                    <strong>ملاحظات مهمة:</strong>
                  </Typography>
                  <ul style={{ margin: '8px 0', paddingRight: '20px' }}>
                    <li>تحقق من مجلد الرسائل غير المرغوب فيها (Spam)</li>
                    <li>الرابط صالح لمدة 60 دقيقة فقط</li>
                    <li>يمكنك طلب رسالة جديدة إذا لم تصلك الرسالة</li>
                  </ul>
                </Alert>

                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                  <Button
                    variant="outlined"
                    startIcon={<ArrowBack />}
                    onClick={() => router.push('/login')}
                  >
                    العودة لتسجيل الدخول
                  </Button>
                  
                  <Button
                    variant="contained"
                    startIcon={<Send />}
                    onClick={() => {
                      setSuccess(false);
                      formik.resetForm();
                    }}
                    sx={{
                      background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    }}
                  >
                    إرسال رسالة أخرى
                  </Button>
                </Box>
              </CardContent>
            </Paper>
          </Container>
        </Box>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>نسيت كلمة المرور - WS Transfir</title>
        <meta name="description" content="إعادة تعيين كلمة المرور في نظام WS Transfir" />
      </Head>

      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 2,
        }}
      >
        <Container maxWidth="sm">
          <Paper
            elevation={24}
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
            }}
          >
            {/* Header */}
            <Box
              sx={{
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                color: 'white',
                padding: 3,
                textAlign: 'center',
              }}
            >
              <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                WS Transfir
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                إعادة تعيين كلمة المرور
              </Typography>
            </Box>

            <CardContent sx={{ padding: 4 }}>
              <Typography variant="h5" component="h2" textAlign="center" gutterBottom>
                نسيت كلمة المرور؟
              </Typography>
              
              <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 3 }}>
                لا تقلق! أدخل بريدك الإلكتروني وسنرسل لك رابط لإعادة تعيين كلمة المرور
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <form onSubmit={formik.handleSubmit}>
                <TextField
                  fullWidth
                  id="email"
                  name="email"
                  label="البريد الإلكتروني"
                  type="email"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Email color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 3 }}
                  dir="ltr"
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  startIcon={<Send />}
                  sx={{
                    mb: 3,
                    py: 1.5,
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
                    },
                  }}
                >
                  {loading ? 'جاري الإرسال...' : 'إرسال رابط إعادة التعيين'}
                </Button>

                <Box textAlign="center">
                  <Link
                    href="/login"
                    variant="body2"
                    sx={{ 
                      textDecoration: 'none', 
                      fontWeight: 'bold',
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: 1,
                    }}
                  >
                    <ArrowBack fontSize="small" />
                    العودة لتسجيل الدخول
                  </Link>
                </Box>
              </form>

              {/* Help Section */}
              <Box sx={{ mt: 4, p: 2, backgroundColor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  تحتاج مساعدة؟
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  إذا كنت تواجه مشاكل في الوصول لحسابك، يمكنك التواصل مع فريق الدعم على:
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  📧 <EMAIL>
                </Typography>
                <Typography variant="body2">
                  📞 +966 11 123 4567
                </Typography>
              </Box>
            </CardContent>
          </Paper>

          {/* Footer */}
          <Box textAlign="center" sx={{ mt: 3 }}>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
              © 2024 WS Transfir. جميع الحقوق محفوظة.
            </Typography>
          </Box>
        </Container>
      </Box>
    </>
  );
};

export default ForgotPasswordPage;
