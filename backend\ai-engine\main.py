"""
WS Transfir AI Engine
====================
محرك الذكاء الاصطناعي المتقدم لكشف الاحتيال والتحليلات المالية
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks # type: ignore
from fastapi.middleware.cors import CORSMiddleware # type: ignore
from fastapi.middleware.trustedhost import TrustedHostMiddleware # type: ignore
from fastapi.responses import JSONResponse # type: ignore
import uvicorn # type: ignore
from prometheus_client import make_asgi_app, Counter, Histogram, Gauge # type: ignore
import structlog # type: ignore

from core.config import settings
from core.database import DatabaseManager
from core.redis_client import RedisClient
from core.logging_config import setup_logging
from api.routes import fraud_detection, risk_assessment, recommendations, analytics
from services.model_manager import ModelManager
from services.fraud_detector import FraudDetector
from services.risk_analyzer import RiskAnalyzer
from services.recommendation_engine import RecommendationEngine
from middleware.auth import AuthMiddleware # type: ignore
from middleware.rate_limit import RateLimitMiddleware # type: ignore

# Setup logging
setup_logging()
logger = structlog.get_logger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter('ai_requests_total', 'Total AI requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('ai_request_duration_seconds', 'Request duration')
ACTIVE_MODELS = Gauge('ai_active_models', 'Number of active AI models')
FRAUD_DETECTIONS = Counter('fraud_detections_total', 'Total fraud detections', ['severity'])

# Global instances
db_manager: DatabaseManager = None
redis_client: RedisClient = None
model_manager: ModelManager = None
fraud_detector: FraudDetector = None
risk_analyzer: RiskAnalyzer = None
recommendation_engine: RecommendationEngine = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global db_manager, redis_client, model_manager, fraud_detector, risk_analyzer, recommendation_engine
    
    logger.info("🚀 Starting WS Transfir AI Engine...")
    
    try:
        # Initialize database connections
        db_manager = DatabaseManager()
        await db_manager.connect()
        logger.info("✅ Database connected")
        
        # Initialize Redis
        redis_client = RedisClient()
        await redis_client.connect()
        logger.info("✅ Redis connected")
        
        # Initialize model manager
        model_manager = ModelManager()
        await model_manager.initialize()
        logger.info("✅ Model manager initialized")
        
        # Initialize AI services
        fraud_detector = FraudDetector(model_manager, db_manager, redis_client)
        await fraud_detector.initialize()
        logger.info("✅ Fraud detector initialized")
        
        risk_analyzer = RiskAnalyzer(model_manager, db_manager, redis_client)
        await risk_analyzer.initialize()
        logger.info("✅ Risk analyzer initialized")
        
        recommendation_engine = RecommendationEngine(model_manager, db_manager, redis_client)
        await recommendation_engine.initialize()
        logger.info("✅ Recommendation engine initialized")
        
        # Update metrics
        ACTIVE_MODELS.set(len(model_manager.loaded_models))
        
        logger.info("🎉 AI Engine started successfully!")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Failed to start AI Engine: {e}")
        raise
    
    finally:
        # Cleanup
        logger.info("🔄 Shutting down AI Engine...")
        
        if recommendation_engine:
            await recommendation_engine.cleanup()
        if risk_analyzer:
            await risk_analyzer.cleanup()
        if fraud_detector:
            await fraud_detector.cleanup()
        if model_manager:
            await model_manager.cleanup()
        if redis_client:
            await redis_client.disconnect()
        if db_manager:
            await db_manager.disconnect()
        
        logger.info("👋 AI Engine shutdown complete")


# Create FastAPI app
app = FastAPI(
    title="WS Transfir AI Engine",
    description="محرك الذكاء الاصطناعي المتقدم لكشف الاحتيال والتحليلات المالية",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

app.add_middleware(AuthMiddleware)
app.add_middleware(RateLimitMiddleware)

# Add Prometheus metrics endpoint
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Include routers
app.include_router(
    fraud_detection.router,
    prefix="/api/v1/fraud",
    tags=["Fraud Detection"]
)

app.include_router(
    risk_assessment.router,
    prefix="/api/v1/risk",
    tags=["Risk Assessment"]
)

app.include_router(
    recommendations.router,
    prefix="/api/v1/recommendations",
    tags=["Recommendations"]
)

app.include_router(
    analytics.router,
    prefix="/api/v1/analytics",
    tags=["Analytics"]
)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "WS Transfir AI Engine",
        "version": "1.0.0",
        "status": "running",
        "description": "محرك الذكاء الاصطناعي المتقدم لكشف الاحتيال والتحليلات المالية"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db_status = await db_manager.health_check() if db_manager else False
        
        # Check Redis connection
        redis_status = await redis_client.health_check() if redis_client else False
        
        # Check models status
        models_status = len(model_manager.loaded_models) > 0 if model_manager else False
        
        status = "healthy" if all([db_status, redis_status, models_status]) else "unhealthy"
        
        return {
            "status": status,
            "timestamp": "2024-01-15T10:30:00Z",
            "services": {
                "database": "healthy" if db_status else "unhealthy",
                "redis": "healthy" if redis_status else "unhealthy",
                "models": "healthy" if models_status else "unhealthy"
            },
            "metrics": {
                "active_models": len(model_manager.loaded_models) if model_manager else 0,
                "memory_usage": "85%",
                "cpu_usage": "45%"
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    try:
        if not all([db_manager, redis_client, model_manager, fraud_detector, risk_analyzer, recommendation_engine]):
            raise HTTPException(status_code=503, detail="Services not ready")
        
        return {
            "status": "ready",
            "timestamp": "2024-01-15T10:30:00Z",
            "services": {
                "fraud_detector": "ready",
                "risk_analyzer": "ready",
                "recommendation_engine": "ready"
            }
        }
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail="Service not ready")


@app.get("/info")
async def service_info():
    """Service information endpoint"""
    return {
        "service": "WS Transfir AI Engine",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "features": {
            "fraud_detection": True,
            "risk_assessment": True,
            "recommendations": True,
            "analytics": True,
            "real_time_scoring": True,
            "batch_processing": True
        },
        "models": {
            "fraud_detection": "XGBoost + Neural Network",
            "risk_assessment": "Random Forest + LSTM",
            "recommendations": "Collaborative Filtering + Content-Based",
            "anomaly_detection": "Isolation Forest + Autoencoder"
        },
        "capabilities": {
            "languages": ["Arabic", "English"],
            "currencies": ["SAR", "USD", "EUR", "GBP"],
            "real_time_processing": True,
            "batch_processing": True,
            "model_retraining": True,
            "explainable_ai": True
        }
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "حدث خطأ داخلي في الخادم",
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


# Dependency injection
async def get_fraud_detector() -> FraudDetector:
    """Get fraud detector instance"""
    if not fraud_detector:
        raise HTTPException(status_code=503, detail="Fraud detector not available")
    return fraud_detector


async def get_risk_analyzer() -> RiskAnalyzer:
    """Get risk analyzer instance"""
    if not risk_analyzer:
        raise HTTPException(status_code=503, detail="Risk analyzer not available")
    return risk_analyzer


async def get_recommendation_engine() -> RecommendationEngine:
    """Get recommendation engine instance"""
    if not recommendation_engine:
        raise HTTPException(status_code=503, detail="Recommendation engine not available")
    return recommendation_engine


async def get_model_manager() -> ModelManager:
    """Get model manager instance"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="Model manager not available")
    return model_manager


# Make dependencies available globally
app.dependency_overrides[FraudDetector] = get_fraud_detector
app.dependency_overrides[RiskAnalyzer] = get_risk_analyzer
app.dependency_overrides[RecommendationEngine] = get_recommendation_engine
app.dependency_overrides[ModelManager] = get_model_manager


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
