{"version": 3, "file": "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js", "names": ["extractFormattedPhoneNumberFromPossibleRfc3966NumberUri", "numberToParse", "extractFormattedPhoneNumber", "phoneContext", "extractPhoneContext", "isPhoneContextValid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phoneNumberString", "char<PERSON>t", "PLUS_SIGN", "indexOfRfc3966Prefix", "indexOf", "RFC3966_PREFIX_", "indexOfNationalNumber", "length", "indexOfPhoneContext", "RFC3966_PHONE_CONTEXT_", "substring", "indexOfIsdn", "RFC3966_ISDN_SUBADDRESS_"], "sources": ["../../source/helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js"], "sourcesContent": ["import extractPhoneContext, {\r\n\tisPhoneContextValid,\r\n\tPLUS_SIGN,\r\n\tRFC3966_PREFIX_,\r\n\tRFC3966_PHONE_CONTEXT_,\r\n\tRFC3966_ISDN_SUBADDRESS_\r\n} from './extractPhoneContext.js'\r\n\r\nimport ParseError from '../ParseError.js'\r\n\r\n/**\r\n * @param  {string} numberToParse\r\n * @param  {string} nationalNumber\r\n * @return {}\r\n */\r\nexport default function extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(numberToParse, {\r\n\textractFormattedPhoneNumber\r\n}) {\r\n\tconst phoneContext = extractPhoneContext(numberToParse)\r\n\tif (!isPhoneContextValid(phoneContext)) {\r\n\t\tthrow new ParseError('NOT_A_NUMBER')\r\n\t}\r\n\r\n\tlet phoneNumberString\r\n\r\n\tif (phoneContext === null) {\r\n\t\t// Extract a possible number from the string passed in.\r\n\t\t// (this strips leading characters that could not be the start of a phone number)\r\n\t\tphoneNumberString = extractFormattedPhoneNumber(numberToParse) || ''\r\n\t} else {\r\n\t\tphoneNumberString = ''\r\n\r\n\t\t// If the phone context contains a phone number prefix, we need to capture\r\n\t\t// it, whereas domains will be ignored.\r\n\t\tif (phoneContext.charAt(0) === PLUS_SIGN) {\r\n\t\t\tphoneNumberString += phoneContext\r\n\t\t}\r\n\r\n\t\t// Now append everything between the \"tel:\" prefix and the phone-context.\r\n\t\t// This should include the national number, an optional extension or\r\n\t\t// isdn-subaddress component. Note we also handle the case when \"tel:\" is\r\n\t\t// missing, as we have seen in some of the phone number inputs.\r\n\t\t// In that case, we append everything from the beginning.\r\n\t\tconst indexOfRfc3966Prefix = numberToParse.indexOf(RFC3966_PREFIX_)\r\n\t\tlet indexOfNationalNumber\r\n\t\t// RFC 3966 \"tel:\" prefix is preset at this stage because\r\n\t\t// `isPhoneContextValid()` requires it to be present.\r\n\t\t/* istanbul ignore else */\r\n\t\tif (indexOfRfc3966Prefix >= 0) {\r\n\t\t\tindexOfNationalNumber = indexOfRfc3966Prefix + RFC3966_PREFIX_.length\r\n\t\t} else {\r\n\t\t\tindexOfNationalNumber = 0\r\n\t\t}\r\n\t\tconst indexOfPhoneContext = numberToParse.indexOf(RFC3966_PHONE_CONTEXT_)\r\n\t\tphoneNumberString += numberToParse.substring(indexOfNationalNumber, indexOfPhoneContext)\r\n\t}\r\n\r\n\t// Delete the isdn-subaddress and everything after it if it is present.\r\n\t// Note extension won't appear at the same time with isdn-subaddress\r\n\t// according to paragraph 5.3 of the RFC3966 spec.\r\n\tconst indexOfIsdn = phoneNumberString.indexOf(RFC3966_ISDN_SUBADDRESS_)\r\n\tif (indexOfIsdn > 0) {\r\n\t\tphoneNumberString = phoneNumberString.substring(0, indexOfIsdn)\r\n\t}\r\n\t// If both phone context and isdn-subaddress are absent but other\r\n\t// parameters are present, the parameters are left in nationalNumber.\r\n\t// This is because we are concerned about deleting content from a potential\r\n\t// number string when there is no strong evidence that the number is\r\n\t// actually written in RFC3966.\r\n\r\n\tif (phoneNumberString !== '') {\r\n\t\treturn phoneNumberString\r\n\t}\r\n}"], "mappings": ";;;;;;;;;AAAA;;AAQA;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACe,SAASA,uDAAT,CAAiEC,aAAjE,QAEZ;EAAA,IADFC,2BACE,QADFA,2BACE;EACF,IAAMC,YAAY,GAAG,IAAAC,+BAAA,EAAoBH,aAApB,CAArB;;EACA,IAAI,CAAC,IAAAI,wCAAA,EAAoBF,YAApB,CAAL,EAAwC;IACvC,MAAM,IAAIG,sBAAJ,CAAe,cAAf,CAAN;EACA;;EAED,IAAIC,iBAAJ;;EAEA,IAAIJ,YAAY,KAAK,IAArB,EAA2B;IAC1B;IACA;IACAI,iBAAiB,GAAGL,2BAA2B,CAACD,aAAD,CAA3B,IAA8C,EAAlE;EACA,CAJD,MAIO;IACNM,iBAAiB,GAAG,EAApB,CADM,CAGN;IACA;;IACA,IAAIJ,YAAY,CAACK,MAAb,CAAoB,CAApB,MAA2BC,8BAA/B,EAA0C;MACzCF,iBAAiB,IAAIJ,YAArB;IACA,CAPK,CASN;IACA;IACA;IACA;IACA;;;IACA,IAAMO,oBAAoB,GAAGT,aAAa,CAACU,OAAd,CAAsBC,oCAAtB,CAA7B;IACA,IAAIC,qBAAJ,CAfM,CAgBN;IACA;;IACA;;IACA,IAAIH,oBAAoB,IAAI,CAA5B,EAA+B;MAC9BG,qBAAqB,GAAGH,oBAAoB,GAAGE,oCAAA,CAAgBE,MAA/D;IACA,CAFD,MAEO;MACND,qBAAqB,GAAG,CAAxB;IACA;;IACD,IAAME,mBAAmB,GAAGd,aAAa,CAACU,OAAd,CAAsBK,2CAAtB,CAA5B;IACAT,iBAAiB,IAAIN,aAAa,CAACgB,SAAd,CAAwBJ,qBAAxB,EAA+CE,mBAA/C,CAArB;EACA,CAtCC,CAwCF;EACA;EACA;;;EACA,IAAMG,WAAW,GAAGX,iBAAiB,CAACI,OAAlB,CAA0BQ,6CAA1B,CAApB;;EACA,IAAID,WAAW,GAAG,CAAlB,EAAqB;IACpBX,iBAAiB,GAAGA,iBAAiB,CAACU,SAAlB,CAA4B,CAA5B,EAA+BC,WAA/B,CAApB;EACA,CA9CC,CA+CF;EACA;EACA;EACA;EACA;;;EAEA,IAAIX,iBAAiB,KAAK,EAA1B,EAA8B;IAC7B,OAAOA,iBAAP;EACA;AACD"}