import {
  Controller,
  Get,
  Put,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';

import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

// DTOs
import { UpdateProfileDto } from './dto/update-profile.dto';
import { UploadDocumentDto } from './dto/upload-document.dto';
import { KycSubmissionDto } from './dto/kyc-submission.dto';
import { ChangePhoneDto } from './dto/change-phone.dto';
import { ChangeEmailDto } from './dto/change-email.dto';
import { UserPreferencesDto } from './dto/user-preferences.dto';

@ApiTags('users')
@Controller('users')
@UseGuards(ThrottlerGuard, JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('profile')
  @ApiOperation({ summary: 'الحصول على الملف الشخصي' })
  @ApiResponse({ status: 200, description: 'الملف الشخصي' })
  async getProfile(@Request() req) {
    return this.usersService.getProfile(req.user.id);
  }

  @Put('profile')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'تحديث الملف الشخصي' })
  @ApiResponse({ status: 200, description: 'تم تحديث الملف الشخصي' })
  @ApiResponse({ status: 400, description: 'بيانات غير صحيحة' })
  async updateProfile(@Body() updateProfileDto: UpdateProfileDto, @Request() req) {
    return this.usersService.updateProfile(req.user.id, updateProfileDto);
  }

  @Post('documents/upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'رفع وثيقة' })
  @ApiResponse({ status: 201, description: 'تم رفع الوثيقة بنجاح' })
  @ApiResponse({ status: 400, description: 'ملف غير صحيح' })
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDocumentDto: UploadDocumentDto,
    @Request() req,
  ) {
    return this.usersService.uploadDocument(req.user.id, file, uploadDocumentDto);
  }

  @Get('documents')
  @ApiOperation({ summary: 'قائمة الوثائق المرفوعة' })
  @ApiResponse({ status: 200, description: 'قائمة الوثائق' })
  async getDocuments(@Request() req) {
    return this.usersService.getDocuments(req.user.id);
  }

  @Delete('documents/:documentId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'حذف وثيقة' })
  @ApiResponse({ status: 200, description: 'تم حذف الوثيقة' })
  @ApiResponse({ status: 404, description: 'الوثيقة غير موجودة' })
  async deleteDocument(@Param('documentId') documentId: string, @Request() req) {
    return this.usersService.deleteDocument(req.user.id, documentId);
  }

  @Post('kyc/submit')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'تقديم طلب التحقق من الهوية' })
  @ApiResponse({ status: 201, description: 'تم تقديم الطلب بنجاح' })
  @ApiResponse({ status: 400, description: 'بيانات ناقصة' })
  async submitKyc(@Body() kycSubmissionDto: KycSubmissionDto, @Request() req) {
    return this.usersService.submitKyc(req.user.id, kycSubmissionDto);
  }

  @Get('kyc/status')
  @ApiOperation({ summary: 'حالة التحقق من الهوية' })
  @ApiResponse({ status: 200, description: 'حالة KYC' })
  async getKycStatus(@Request() req) {
    return this.usersService.getKycStatus(req.user.id);
  }

  @Put('phone')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'تغيير رقم الهاتف' })
  @ApiResponse({ status: 200, description: 'تم إرسال رمز التحقق' })
  async changePhone(@Body() changePhoneDto: ChangePhoneDto, @Request() req) {
    return this.usersService.changePhone(req.user.id, changePhoneDto);
  }

  @Put('email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'تغيير البريد الإلكتروني' })
  @ApiResponse({ status: 200, description: 'تم إرسال رمز التحقق' })
  async changeEmail(@Body() changeEmailDto: ChangeEmailDto, @Request() req) {
    return this.usersService.changeEmail(req.user.id, changeEmailDto);
  }

  @Get('preferences')
  @ApiOperation({ summary: 'إعدادات المستخدم' })
  @ApiResponse({ status: 200, description: 'إعدادات المستخدم' })
  async getPreferences(@Request() req) {
    return this.usersService.getPreferences(req.user.id);
  }

  @Put('preferences')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'تحديث إعدادات المستخدم' })
  @ApiResponse({ status: 200, description: 'تم تحديث الإعدادات' })
  async updatePreferences(@Body() preferencesDto: UserPreferencesDto, @Request() req) {
    return this.usersService.updatePreferences(req.user.id, preferencesDto);
  }

  @Get('activity')
  @ApiOperation({ summary: 'سجل نشاط المستخدم' })
  @ApiResponse({ status: 200, description: 'سجل النشاط' })
  async getActivity(@Query('page') page: number = 1, @Query('limit') limit: number = 20, @Request() req) {
    return this.usersService.getActivity(req.user.id, page, limit);
  }

  @Get('statistics')
  @ApiOperation({ summary: 'إحصائيات المستخدم' })
  @ApiResponse({ status: 200, description: 'إحصائيات المستخدم' })
  async getStatistics(@Request() req) {
    return this.usersService.getStatistics(req.user.id);
  }

  @Post('deactivate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'إلغاء تفعيل الحساب' })
  @ApiResponse({ status: 200, description: 'تم إلغاء تفعيل الحساب' })
  async deactivateAccount(@Body('reason') reason: string, @Request() req) {
    return this.usersService.deactivateAccount(req.user.id, reason);
  }

  @Post('reactivate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'إعادة تفعيل الحساب' })
  @ApiResponse({ status: 200, description: 'تم إعادة تفعيل الحساب' })
  async reactivateAccount(@Request() req) {
    return this.usersService.reactivateAccount(req.user.id);
  }

  @Get('beneficiaries')
  @ApiOperation({ summary: 'قائمة المستفيدين المحفوظين' })
  @ApiResponse({ status: 200, description: 'قائمة المستفيدين' })
  async getBeneficiaries(@Request() req) {
    return this.usersService.getBeneficiaries(req.user.id);
  }

  @Post('beneficiaries')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'إضافة مستفيد جديد' })
  @ApiResponse({ status: 201, description: 'تم إضافة المستفيد' })
  async addBeneficiary(@Body() beneficiaryDto: any, @Request() req) {
    return this.usersService.addBeneficiary(req.user.id, beneficiaryDto);
  }

  @Put('beneficiaries/:beneficiaryId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'تحديث بيانات مستفيد' })
  @ApiResponse({ status: 200, description: 'تم تحديث المستفيد' })
  async updateBeneficiary(
    @Param('beneficiaryId') beneficiaryId: string,
    @Body() beneficiaryDto: any,
    @Request() req,
  ) {
    return this.usersService.updateBeneficiary(req.user.id, beneficiaryId, beneficiaryDto);
  }

  @Delete('beneficiaries/:beneficiaryId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'حذف مستفيد' })
  @ApiResponse({ status: 200, description: 'تم حذف المستفيد' })
  async deleteBeneficiary(@Param('beneficiaryId') beneficiaryId: string, @Request() req) {
    return this.usersService.deleteBeneficiary(req.user.id, beneficiaryId);
  }

  @Get('limits')
  @ApiOperation({ summary: 'حدود التحويل للمستخدم' })
  @ApiResponse({ status: 200, description: 'حدود التحويل' })
  async getTransferLimits(@Request() req) {
    return this.usersService.getTransferLimits(req.user.id);
  }

  @Post('upgrade-limits')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'طلب رفع حدود التحويل' })
  @ApiResponse({ status: 200, description: 'تم تقديم الطلب' })
  async requestLimitUpgrade(@Body() upgradeDto: any, @Request() req) {
    return this.usersService.requestLimitUpgrade(req.user.id, upgradeDto);
  }
}
