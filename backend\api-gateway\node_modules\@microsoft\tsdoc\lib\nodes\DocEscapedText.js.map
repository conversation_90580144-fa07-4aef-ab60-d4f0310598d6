{"version": 3, "file": "DocEscapedText.js", "sourceRoot": "", "sources": ["../../src/nodes/DocEscapedText.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAiC,OAAO,EAAE,MAAM,WAAW,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAYvD;;GAEG;AACH,MAAM,CAAN,IAAY,WAKX;AALD,WAAY,WAAW;IACrB;;OAEG;IACH,2EAAmB,CAAA;AACrB,CAAC,EALW,WAAW,KAAX,WAAW,QAKtB;AAED;;;;;;GAMG;AACH;IAAoC,kCAAO;IAQzC;;;OAGG;IACH,wBAAmB,UAA2C;QAC5D,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,KAAI,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;QAE3C,KAAI,CAAC,mBAAmB,GAAG,IAAI,UAAU,CAAC;YACxC,aAAa,EAAE,KAAI,CAAC,aAAa;YACjC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,OAAO,EAAE,UAAU,CAAC,kBAAkB;SACvC,CAAC,CAAC;QAEH,KAAI,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;;IAC7C,CAAC;IAGD,sBAAW,gCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,WAAW,CAAC;QACjC,CAAC;;;OAAA;IAKD,sBAAW,uCAAW;QAHtB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;;;OAAA;IAKD,sBAAW,uCAAW;QAHtB;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAClE,CAAC;YACD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;;;OAAA;IAKD,sBAAW,uCAAW;QAHtB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;;;OAAA;IAED,gBAAgB;IACN,wCAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACpC,CAAC;IACH,qBAAC;AAAD,CAAC,AA3DD,CAAoC,OAAO,GA2D1C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, type IDocNodeParsedParameters, DocNode } from './DocNode';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\n\r\n/**\r\n * Constructor parameters for {@link DocEscapedText}.\r\n */\r\nexport interface IDocEscapedTextParsedParameters extends IDocNodeParsedParameters {\r\n  escapeStyle: EscapeStyle;\r\n  encodedTextExcerpt: TokenSequence;\r\n  decodedText: string;\r\n}\r\n\r\n/**\r\n * The style of escaping to be used with DocEscapedText.\r\n */\r\nexport enum EscapeStyle {\r\n  /**\r\n   * Use a backslash symbol to escape the character.\r\n   */\r\n  CommonMarkBackslash\r\n}\r\n\r\n/**\r\n * Represents a text character that should be escaped as a TSDoc symbol.\r\n * @remarks\r\n * Note that renders will normally apply appropriate escaping when rendering\r\n * DocPlainText in a format such as HTML or TSDoc.  The DocEscapedText node\r\n * forces a specific escaping that may not be the default.\r\n */\r\nexport class DocEscapedText extends DocNode {\r\n  private readonly _escapeStyle: EscapeStyle;\r\n\r\n  private _encodedText: string | undefined;\r\n  private readonly _encodedTextExcerpt: DocExcerpt;\r\n\r\n  private readonly _decodedText: string;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocEscapedTextParsedParameters) {\r\n    super(parameters);\r\n\r\n    this._escapeStyle = parameters.escapeStyle;\r\n\r\n    this._encodedTextExcerpt = new DocExcerpt({\r\n      configuration: this.configuration,\r\n      excerptKind: ExcerptKind.EscapedText,\r\n      content: parameters.encodedTextExcerpt\r\n    });\r\n\r\n    this._decodedText = parameters.decodedText;\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.EscapedText;\r\n  }\r\n\r\n  /**\r\n   * The style of escaping to be performed.\r\n   */\r\n  public get escapeStyle(): EscapeStyle {\r\n    return this._escapeStyle;\r\n  }\r\n\r\n  /**\r\n   * The text sequence including escapes.\r\n   */\r\n  public get encodedText(): string {\r\n    if (this._encodedText === undefined) {\r\n      this._encodedText = this._encodedTextExcerpt.content.toString();\r\n    }\r\n    return this._encodedText;\r\n  }\r\n\r\n  /**\r\n   * The text without escaping.\r\n   */\r\n  public get decodedText(): string {\r\n    return this._decodedText;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._encodedTextExcerpt];\r\n  }\r\n}\r\n"]}