"""
Authentication Middleware
========================
وسطاء المصادقة والتفويض
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
import re

from fastapi import Request, HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from .jwt_manager import JWTManager, TokenPayload, UserRole, Permission
from .auth_service import AuthService

logger = logging.getLogger(__name__)


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """وسطاء المصادقة"""
    
    def __init__(self, app, jwt_manager: JWTManager, auth_service: AuthService):
        super().__init__(app)
        self.jwt_manager = jwt_manager
        self.auth_service = auth_service
        
        # Public endpoints that don't require authentication
        self.public_endpoints = {
            "/",
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/forgot-password",
            "/api/v1/auth/reset-password",
            "/api/v1/auth/verify-email",
            "/metrics"
        }
        
        # Endpoints that require specific roles
        self.role_protected_endpoints = {
            "/api/v1/admin": [UserRole.ADMIN, UserRole.SUPER_ADMIN],
            "/api/v1/agents": [UserRole.AGENT, UserRole.AGENT_MANAGER, UserRole.ADMIN, UserRole.SUPER_ADMIN],
            "/api/v1/system": [UserRole.SUPER_ADMIN]
        }
    
    async def dispatch(self, request: Request, call_next):
        """معالجة الطلب"""
        try:
            # Get request info
            path = request.url.path
            method = request.method
            ip_address = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            
            # Log request
            logger.debug(f"🌐 {method} {path} from {ip_address}")
            
            # Check if endpoint is public
            if self._is_public_endpoint(path):
                return await call_next(request)
            
            # Extract token from request
            token = self._extract_token(request)
            if not token:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={
                        "error": "missing_token",
                        "message": "رمز المصادقة مطلوب",
                        "message_en": "Authentication token required"
                    }
                )
            
            # Verify token
            token_payload = self.jwt_manager.verify_token(token)
            if not token_payload:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={
                        "error": "invalid_token",
                        "message": "رمز المصادقة غير صحيح أو منتهي الصلاحية",
                        "message_en": "Invalid or expired token"
                    }
                )
            
            # Check role-based access
            required_roles = self._get_required_roles(path)
            if required_roles and not self._check_role_access(token_payload, required_roles):
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={
                        "error": "insufficient_permissions",
                        "message": "ليس لديك صلاحية للوصول لهذا المورد",
                        "message_en": "Insufficient permissions"
                    }
                )
            
            # Add user info to request state
            request.state.user = token_payload
            request.state.user_id = token_payload.user_id
            request.state.user_role = token_payload.role
            request.state.user_permissions = token_payload.permissions
            request.state.session_id = token_payload.session_id
            request.state.ip_address = ip_address
            request.state.user_agent = user_agent
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["X-XSS-Protection"] = "1; mode=block"
            response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Authentication middleware error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "authentication_error",
                    "message": "حدث خطأ في نظام المصادقة",
                    "message_en": "Authentication system error"
                }
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """الحصول على عنوان IP للعميل"""
        # Check for forwarded headers (for load balancers/proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _is_public_endpoint(self, path: str) -> bool:
        """فحص إذا كان المسار عام"""
        # Check exact matches
        if path in self.public_endpoints:
            return True
        
        # Check pattern matches
        public_patterns = [
            r"^/static/.*",
            r"^/assets/.*",
            r"^/favicon\.ico$",
            r"^/robots\.txt$"
        ]
        
        for pattern in public_patterns:
            if re.match(pattern, path):
                return True
        
        return False
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """استخراج الرمز من الطلب"""
        # Check Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # Remove "Bearer " prefix
        
        # Check query parameter (for WebSocket connections)
        token = request.query_params.get("token")
        if token:
            return token
        
        # Check cookie
        token = request.cookies.get("access_token")
        if token:
            return token
        
        return None
    
    def _get_required_roles(self, path: str) -> Optional[List[UserRole]]:
        """الحصول على الأدوار المطلوبة للمسار"""
        for endpoint_pattern, roles in self.role_protected_endpoints.items():
            if path.startswith(endpoint_pattern):
                return roles
        
        return None
    
    def _check_role_access(self, token_payload: TokenPayload, required_roles: List[UserRole]) -> bool:
        """فحص صلاحية الدور"""
        return token_payload.role in required_roles


class RateLimitMiddleware(BaseHTTPMiddleware):
    """وسطاء تحديد المعدل"""
    
    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests = {}  # {ip: [(timestamp, count), ...]}
        
    async def dispatch(self, request: Request, call_next):
        """معالجة الطلب مع تحديد المعدل"""
        try:
            ip_address = self._get_client_ip(request)
            current_time = datetime.now()
            
            # Clean old requests
            self._clean_old_requests(current_time)
            
            # Check rate limit
            if self._is_rate_limited(ip_address, current_time):
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={
                        "error": "rate_limit_exceeded",
                        "message": "تم تجاوز حد المعدل المسموح",
                        "message_en": "Rate limit exceeded",
                        "retry_after": 60
                    },
                    headers={"Retry-After": "60"}
                )
            
            # Record request
            self._record_request(ip_address, current_time)
            
            return await call_next(request)
            
        except Exception as e:
            logger.error(f"❌ Rate limit middleware error: {e}")
            return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """الحصول على عنوان IP للعميل"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        return request.client.host if request.client else "unknown"
    
    def _clean_old_requests(self, current_time: datetime):
        """تنظيف الطلبات القديمة"""
        cutoff_time = current_time.timestamp() - 60  # 1 minute ago
        
        for ip in list(self.requests.keys()):
            self.requests[ip] = [
                (timestamp, count) for timestamp, count in self.requests[ip]
                if timestamp > cutoff_time
            ]
            
            if not self.requests[ip]:
                del self.requests[ip]
    
    def _is_rate_limited(self, ip_address: str, current_time: datetime) -> bool:
        """فحص إذا كان المعدل محدود"""
        if ip_address not in self.requests:
            return False
        
        # Count requests in the last minute
        cutoff_time = current_time.timestamp() - 60
        recent_requests = sum(
            count for timestamp, count in self.requests[ip_address]
            if timestamp > cutoff_time
        )
        
        return recent_requests >= self.requests_per_minute
    
    def _record_request(self, ip_address: str, current_time: datetime):
        """تسجيل الطلب"""
        if ip_address not in self.requests:
            self.requests[ip_address] = []
        
        self.requests[ip_address].append((current_time.timestamp(), 1))


# FastAPI Dependencies
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    jwt_manager: JWTManager = Depends()
) -> TokenPayload:
    """الحصول على المستخدم الحالي"""
    try:
        token_payload = jwt_manager.verify_token(credentials.credentials)
        
        if not token_payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="رمز المصادقة غير صحيح أو منتهي الصلاحية",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        return token_payload
        
    except Exception as e:
        logger.error(f"❌ Get current user failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="فشل في التحقق من المصادقة",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def get_current_active_user(
    current_user: TokenPayload = Depends(get_current_user)
) -> TokenPayload:
    """الحصول على المستخدم النشط الحالي"""
    # Additional checks can be added here (e.g., user is active, verified, etc.)
    return current_user


def require_permission(required_permission: Permission):
    """مطلوب صلاحية معينة"""
    def permission_checker(current_user: TokenPayload = Depends(get_current_user)) -> TokenPayload:
        if required_permission not in current_user.permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"مطلوب صلاحية: {required_permission.value}"
            )
        return current_user
    
    return permission_checker


def require_role(required_role: UserRole):
    """مطلوب دور معين"""
    def role_checker(current_user: TokenPayload = Depends(get_current_user)) -> TokenPayload:
        # Define role hierarchy
        role_hierarchy = {
            UserRole.SUPER_ADMIN: 5,
            UserRole.ADMIN: 4,
            UserRole.AGENT_MANAGER: 3,
            UserRole.AGENT: 2,
            UserRole.USER: 1,
            UserRole.GUEST: 0
        }
        
        user_level = role_hierarchy.get(current_user.role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        if user_level < required_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"مطلوب دور: {required_role.value}"
            )
        
        return current_user
    
    return role_checker


def require_2fa():
    """مطلوب التحقق بخطوتين"""
    def two_factor_checker(current_user: TokenPayload = Depends(get_current_user)) -> TokenPayload:
        if not current_user.two_factor_verified:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="مطلوب التحقق بخطوتين"
            )
        return current_user
    
    return two_factor_checker


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """وسطاء رؤوس الأمان"""
    
    async def dispatch(self, request: Request, call_next):
        """إضافة رؤوس الأمان"""
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
        
        # HSTS (HTTP Strict Transport Security)
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # CSP (Content Security Policy)
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )
        response.headers["Content-Security-Policy"] = csp
        
        return response


class CORSMiddleware(BaseHTTPMiddleware):
    """وسطاء CORS"""
    
    def __init__(self, app, allowed_origins: List[str] = None):
        super().__init__(app)
        self.allowed_origins = allowed_origins or ["*"]
    
    async def dispatch(self, request: Request, call_next):
        """معالجة CORS"""
        origin = request.headers.get("origin")
        
        # Handle preflight requests
        if request.method == "OPTIONS":
            response = JSONResponse(content={})
        else:
            response = await call_next(request)
        
        # Add CORS headers
        if origin and (self.allowed_origins == ["*"] or origin in self.allowed_origins):
            response.headers["Access-Control-Allow-Origin"] = origin
        
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Max-Age"] = "86400"
        
        return response
