@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: WS Transfir Development Mode Startup
:: تشغيل نظام WS Transfir في وضع التطوير

echo 🚀 تشغيل نظام WS Transfir - وضع التطوير
echo ==========================================
echo.

:: Check if Node.js is installed
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً من: https://nodejs.org
    echo.
    echo 📥 تحميل Node.js:
    echo    1. اذهب إلى https://nodejs.org
    echo    2. حمل الإصدار LTS
    echo    3. قم بالتثبيت
    echo    4. أعد تشغيل هذا الملف
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js متاح - الإصدار: %NODE_VERSION%
echo.

:: Check if npm is available
echo 🔍 فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm متاح - الإصدار: %NPM_VERSION%
echo.

:: Create development environment file
if not exist ".env.development" (
    echo ⚙️ إنشاء ملف البيئة للتطوير...
    (
        echo # WS Transfir Development Environment
        echo NODE_ENV=development
        echo PORT=3000
        echo.
        echo # Database URLs (using local services or online)
        echo DATABASE_URL=postgresql://localhost:5432/ws_transfir_dev
        echo REDIS_URL=redis://localhost:6379
        echo MONGODB_URL=mongodb://localhost:27017/ws_transfir_dev
        echo.
        echo # JWT Configuration
        echo JWT_SECRET=dev-jwt-secret-key-not-for-production
        echo JWT_REFRESH_SECRET=dev-refresh-secret-key-not-for-production
        echo.
        echo # API URLs for development
        echo NEXT_PUBLIC_API_URL=http://localhost:3000
        echo NEXT_PUBLIC_AUTH_URL=http://localhost:3001
        echo NEXT_PUBLIC_USER_URL=http://localhost:3002
        echo NEXT_PUBLIC_TRANSFER_URL=http://localhost:3003
        echo NEXT_PUBLIC_WALLET_URL=http://localhost:3004
        echo NEXT_PUBLIC_NOTIFICATION_URL=http://localhost:3005
        echo NEXT_PUBLIC_ANALYTICS_URL=http://localhost:3006
        echo.
        echo # Development settings
        echo LOG_LEVEL=debug
        echo ENABLE_CORS=true
        echo ENABLE_SWAGGER=true
    ) > .env.development
    echo ✅ تم إنشاء ملف .env.development
    echo.
)

:: Install dependencies for frontend
echo 📦 تثبيت dependencies للواجهة الأمامية...
if exist "frontend\web-app\package.json" (
    cd frontend\web-app
    echo تثبيت dependencies في frontend/web-app...
    npm install
    if errorlevel 1 (
        echo ❌ فشل في تثبيت dependencies للواجهة الأمامية
        cd ..\..
        pause
        exit /b 1
    )
    cd ..\..
    echo ✅ تم تثبيت dependencies للواجهة الأمامية
) else (
    echo ⚠️ مجلد frontend/web-app غير موجود
)
echo.

:: Create a simple package.json for the root if it doesn't exist
if not exist "package.json" (
    echo 📝 إنشاء package.json للمشروع...
    (
        echo {
        echo   "name": "ws-transfir",
        echo   "version": "1.0.0",
        echo   "description": "WS Transfir Money Transfer System",
        echo   "scripts": {
        echo     "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:mock-api\"",
        echo     "dev:frontend": "cd frontend/web-app && npm run dev",
        echo     "dev:mock-api": "node mock-api-server.js",
        echo     "build": "cd frontend/web-app && npm run build",
        echo     "start": "cd frontend/web-app && npm start"
        echo   },
        echo   "devDependencies": {
        echo     "concurrently": "^7.6.0",
        echo     "express": "^4.18.2",
        echo     "cors": "^2.8.5"
        echo   }
        echo }
    ) > package.json
    echo ✅ تم إنشاء package.json
)

:: Install root dependencies
echo 📦 تثبيت dependencies للمشروع...
npm install
if errorlevel 1 (
    echo ❌ فشل في تثبيت dependencies للمشروع
    pause
    exit /b 1
)
echo ✅ تم تثبيت dependencies للمشروع
echo.

:: Create mock API server
if not exist "mock-api-server.js" (
    echo 🔧 إنشاء خادم API وهمي للتطوير...
    (
        echo const express = require('express'^);
        echo const cors = require('cors'^);
        echo const app = express(^);
        echo.
        echo app.use(cors(^)^);
        echo app.use(express.json(^)^);
        echo.
        echo // Mock API endpoints
        echo app.get('/api/health', (req, res^) =^> {
        echo   res.json({ status: 'OK', message: 'Mock API Server is running' }^);
        echo }^);
        echo.
        echo app.post('/api/auth/login', (req, res^) =^> {
        echo   res.json({
        echo     success: true,
        echo     token: 'mock-jwt-token',
        echo     user: { id: '1', name: 'مستخدم تجريبي', email: '<EMAIL>' }
        echo   }^);
        echo }^);
        echo.
        echo app.post('/api/auth/register', (req, res^) =^> {
        echo   res.json({ success: true, message: 'تم إنشاء الحساب بنجاح' }^);
        echo }^);
        echo.
        echo app.get('/api/transfers', (req, res^) =^> {
        echo   res.json({
        echo     data: [
        echo       { id: '1', amount: '1500', currency: 'SAR', status: 'completed', receiverName: 'أحمد محمد' },
        echo       { id: '2', amount: '750', currency: 'USD', status: 'pending', receiverName: 'فاطمة علي' }
        echo     ],
        echo     total: 2,
        echo     page: 1,
        echo     totalPages: 1
        echo   }^);
        echo }^);
        echo.
        echo app.get('/api/profile/me', (req, res^) =^> {
        echo   res.json({
        echo     id: '1',
        echo     firstName: 'أحمد',
        echo     lastName: 'محمد',
        echo     email: '<EMAIL>',
        echo     phone: '+966501234567',
        echo     isVerified: true
        echo   }^);
        echo }^);
        echo.
        echo const PORT = process.env.PORT ^|^| 3000;
        echo app.listen(PORT, (^) =^> {
        echo   console.log(`🚀 Mock API Server running on http://localhost:${PORT}`^);
        echo   console.log(`📊 Health check: http://localhost:${PORT}/api/health`^);
        echo }^);
    ) > mock-api-server.js
    echo ✅ تم إنشاء خادم API وهمي
    echo.
)

:: Start the development servers
echo 🚀 بدء تشغيل خوادم التطوير...
echo.
echo ✅ سيتم تشغيل:
echo    📱 الواجهة الأمامية على: http://localhost:3100
echo    🔧 خادم API وهمي على: http://localhost:3000
echo.
echo 📝 ملاحظات مهمة:
echo    - هذا وضع تطوير مبسط بدون قواعد بيانات حقيقية
echo    - البيانات وهمية للاختبار فقط
echo    - لتشغيل النظام الكامل، استخدم Docker
echo.

:: Start both servers
echo 🎬 بدء التشغيل...
start "Mock API Server" cmd /k "node mock-api-server.js"

timeout /t 3 /nobreak >nul

if exist "frontend\web-app" (
    cd frontend\web-app
    echo 🌐 بدء تشغيل الواجهة الأمامية...
    start "Frontend Server" cmd /k "npm run dev"
    cd ..\..
) else (
    echo ⚠️ مجلد الواجهة الأمامية غير موجود
)

echo.
echo ✅ تم بدء تشغيل النظام في وضع التطوير!
echo.
echo 🌐 روابط الوصول:
echo    📱 التطبيق: http://localhost:3100
echo    🔧 API: http://localhost:3000
echo    📊 Health Check: http://localhost:3000/api/health
echo.
echo 💡 نصائح:
echo    - اضغط Ctrl+C في نوافذ الأوامر لإيقاف الخوادم
echo    - تحقق من console للأخطاء
echo    - البيانات وهمية للاختبار فقط
echo.

pause
