{"version": 3, "file": "Token.d.ts", "sourceRoot": "", "sources": ["../../src/parser/Token.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAE7C;;GAEG;AACH,oBAAY,SAAS;IACnB;;;OAGG;IACH,UAAU,OAAO;IAEjB;;;;OAIG;IACH,OAAO,OAAO;IAEd;;OAEG;IACH,OAAO,OAAO;IAEd;;OAEG;IACH,SAAS,OAAO;IAEhB;;;OAGG;IACH,gBAAgB,OAAO;IAEvB;;OAEG;IACH,KAAK,OAAO;IAEZ;;;OAGG;IACH,SAAS,OAAO;IAEhB;;;OAGG;IACH,QAAQ,OAAO;IAEf;;;OAGG;IACH,WAAW,OAAO;IAElB;;;OAGG;IACH,MAAM,OAAO;IAEb;;;OAGG;IACH,WAAW,OAAO;IAElB;;;OAGG;IACH,WAAW,OAAO;IAElB;;;OAGG;IACH,KAAK,OAAO;IAEZ;;;OAGG;IACH,MAAM,OAAO;IAEb;;;OAGG;IACH,MAAM,OAAO;IAEb;;;OAGG;IACH,gBAAgB,OAAO;IAEvB;;;OAGG;IACH,iBAAiB,OAAO;IAExB;;;OAGG;IACH,QAAQ,OAAO;IAEf;;;OAGG;IACH,MAAM,OAAO;IAEb;;;OAGG;IACH,KAAK,OAAO;IAEZ;;;OAGG;IACH,KAAK,OAAO;IAEZ;;;OAGG;IACH,iBAAiB,OAAO;IAExB;;;OAGG;IACH,kBAAkB,OAAO;IAEzB;;;OAGG;IACH,IAAI,OAAO;IAEX;;;OAGG;IACH,eAAe,OAAO;IAEtB;;;OAGG;IACH,gBAAgB,OAAO;IAEvB;;;OAGG;IACH,WAAW,OAAO;IAElB;;;OAGG;IACH,IAAI,OAAO;IAEX;;;OAGG;IACH,UAAU,OAAO;CAClB;AAED;;;;GAIG;AACH,qBAAa,KAAK;IAChB;;OAEG;IACH,SAAgB,IAAI,EAAE,SAAS,CAAC;IAChC;;;OAGG;IACH,SAAgB,KAAK,EAAE,SAAS,CAAC;IAEjC;;OAEG;IACH,SAAgB,IAAI,EAAE,SAAS,CAAC;gBAEb,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS;IAM9D,QAAQ,IAAI,MAAM;CAM1B"}