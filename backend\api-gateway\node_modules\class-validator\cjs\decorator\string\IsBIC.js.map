{"version": 3, "file": "IsBIC.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBIC.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,gEAAiD;AAEpC,QAAA,MAAM,GAAG,OAAO,CAAC;AAE9B;;;GAGG;AACH,SAAgB,KAAK,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,eAAc,EAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAFD,sBAEC;AAED;;;GAGG;AACH,SAAgB,KAAK,CAAC,iBAAqC;IACzD,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,cAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAChD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,uCAAuC,EAClE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,sBAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBICValidator from 'validator/lib/isBIC';\n\nexport const IS_BIC = 'isBIC';\n\n/**\n * Check if a string is a BIC (Bank Identification Code) or SWIFT code.\n * If given value is not a string, then it returns false.\n */\nexport function isBIC(value: unknown): boolean {\n  return typeof value === 'string' && isBICValidator(value);\n}\n\n/**\n * Check if a string is a BIC (Bank Identification Code) or SWIFT code.\n * If given value is not a string, then it returns false.\n */\nexport function IsBIC(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BIC,\n      validator: {\n        validate: (value, args): boolean => isBIC(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a BIC or SWIFT code',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}