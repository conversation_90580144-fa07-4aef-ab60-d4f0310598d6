"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;
const LCS_1 = require("./LCS");
var LCS_2 = require("./LCS");
Object.defineProperty(exports, "FIRST_KEY_INDEX", { enumerable: true, get: function () { return LCS_2.FIRST_KEY_INDEX; } });
Object.defineProperty(exports, "IS_READ_ONLY", { enumerable: true, get: function () { return LCS_2.IS_READ_ONLY; } });
function transformArguments(key1, key2) {
    const args = (0, LCS_1.transformArguments)(key1, key2);
    args.push('LEN');
    return args;
}
exports.transformArguments = transformArguments;
