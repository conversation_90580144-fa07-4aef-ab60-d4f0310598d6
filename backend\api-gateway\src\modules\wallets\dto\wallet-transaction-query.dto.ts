import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsDateString,
  <PERSON>N<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum WalletTransactionType {
  TOP_UP = 'top_up',
  WITHDRAWAL = 'withdrawal',
  TRANSFER_SENT = 'transfer_sent',
  TRANSFER_RECEIVED = 'transfer_received',
  BILL_PAYMENT = 'bill_payment',
  CURRENCY_CONVERSION = 'currency_conversion',
  FEE = 'fee',
  REFUND = 'refund',
  CASHBACK = 'cashback',
  INTEREST = 'interest',
}

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export class WalletTransactionQueryDto {
  @ApiProperty({
    description: 'رقم الصفحة',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'رقم الصفحة يجب أن يكون رقم' })
  @Min(1, { message: 'رقم الصفحة يجب أن يكون أكبر من صفر' })
  page?: number = 1;

  @ApiProperty({
    description: 'عدد العناصر في الصفحة',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'عدد العناصر يجب أن يكون رقم' })
  @Min(1, { message: 'عدد العناصر يجب أن يكون أكبر من صفر' })
  @Max(100, { message: 'عدد العناصر يجب أن يكون أقل من 100' })
  limit?: number = 20;

  @ApiProperty({
    description: 'نوع المعاملة',
    enum: WalletTransactionType,
    required: false,
  })
  @IsOptional()
  @IsEnum(WalletTransactionType, { message: 'نوع المعاملة غير صحيح' })
  type?: WalletTransactionType;

  @ApiProperty({
    description: 'حالة المعاملة',
    enum: TransactionStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(TransactionStatus, { message: 'حالة المعاملة غير صحيحة' })
  status?: TransactionStatus;

  @ApiProperty({
    description: 'العملة',
    example: 'SAR',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'العملة يجب أن تكون نص' })
  currency?: string;

  @ApiProperty({
    description: 'تاريخ البداية',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ البداية غير صحيح' })
  startDate?: string;

  @ApiProperty({
    description: 'تاريخ النهاية',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ النهاية غير صحيح' })
  endDate?: string;

  @ApiProperty({
    description: 'الحد الأدنى للمبلغ',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'الحد الأدنى للمبلغ يجب أن يكون رقم' })
  @Min(0, { message: 'الحد الأدنى للمبلغ يجب أن يكون أكبر من أو يساوي صفر' })
  minAmount?: number;

  @ApiProperty({
    description: 'الحد الأقصى للمبلغ',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'الحد الأقصى للمبلغ يجب أن يكون رقم' })
  @Min(0, { message: 'الحد الأقصى للمبلغ يجب أن يكون أكبر من أو يساوي صفر' })
  maxAmount?: number;

  @ApiProperty({
    description: 'البحث في النص (رقم المرجع، الوصف، إلخ)',
    example: 'فاتورة كهرباء',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'نص البحث يجب أن يكون نص' })
  search?: string;

  @ApiProperty({
    description: 'ترتيب النتائج',
    example: 'createdAt:desc',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ترتيب النتائج يجب أن يكون نص' })
  sortBy?: string = 'createdAt:desc';

  @ApiProperty({
    description: 'تضمين المعاملات المخفية',
    example: false,
    required: false,
  })
  @IsOptional()
  includeHidden?: boolean = false;

  @ApiProperty({
    description: 'تضمين الرسوم في النتائج',
    example: true,
    required: false,
  })
  @IsOptional()
  includeFees?: boolean = true;

  @ApiProperty({
    description: 'تجميع النتائج حسب',
    example: 'day',
    enum: ['day', 'week', 'month', 'year'],
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'تجميع النتائج يجب أن يكون نص' })
  groupBy?: 'day' | 'week' | 'month' | 'year';
}
