import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Menu,
  MenuItem,
  Alert,
} from '@mui/material';
import {
  AccountBalance,
  TrendingUp,
  SwapHoriz,
  Notifications,
  MoreVert,
  Add,
  Visibility,
  Send,
  Receipt,
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '../components/Layout';

interface DashboardStats {
  totalBalance: string;
  totalTransfers: number;
  pendingTransfers: number;
  monthlyVolume: string;
}

interface RecentTransfer {
  id: string;
  referenceNumber: string;
  amount: string;
  currency: string;
  receiverName: string;
  status: string;
  createdAt: string;
}

const DashboardPage: React.FC = () => {
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentTransfers, setRecentTransfers] = useState<RecentTransfer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API calls
      const [statsResponse, transfersResponse] = await Promise.all([
        fetch('/api/dashboard/stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }),
        fetch('/api/transfers?limit=5', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }),
      ]);

      if (statsResponse.ok && transfersResponse.ok) {
        const statsData = await statsResponse.json();
        const transfersData = await transfersResponse.json();
        
        setStats(statsData);
        setRecentTransfers(transfersData.data || []);
      } else {
        setError('فشل في تحميل بيانات لوحة التحكم');
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال');
      // Mock data for development
      setStats({
        totalBalance: '15,750.00',
        totalTransfers: 24,
        pendingTransfers: 3,
        monthlyVolume: '45,230.50',
      });
      setRecentTransfers([
        {
          id: '1',
          referenceNumber: 'WS20241225001',
          amount: '1,500.00',
          currency: 'SAR',
          receiverName: 'أحمد محمد',
          status: 'completed',
          createdAt: '2024-12-25T10:30:00Z',
        },
        {
          id: '2',
          referenceNumber: 'WS20241224002',
          amount: '750.00',
          currency: 'USD',
          receiverName: 'فاطمة علي',
          status: 'pending',
          createdAt: '2024-12-24T15:45:00Z',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'معلق';
      case 'processing':
        return 'قيد المعالجة';
      case 'failed':
        return 'فاشل';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Layout>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Typography>جاري التحميل...</Typography>
        </Box>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>لوحة التحكم - WS Transfir</title>
        <meta name="description" content="لوحة التحكم الرئيسية لنظام WS Transfir" />
      </Head>

      <Layout>
        <Box sx={{ flexGrow: 1, p: 3 }}>
          {/* Header */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                مرحباً بك في لوحة التحكم
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                إدارة تحويلاتك المالية بسهولة وأمان
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => router.push('/transfers/new')}
              sx={{
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                px: 3,
              }}
            >
              تحويل جديد
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Stats Cards */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography variant="h4" component="div" fontWeight="bold">
                        {stats?.totalBalance} ر.س
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>
                        الرصيد الإجمالي
                      </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                      <AccountBalance />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography variant="h4" component="div" fontWeight="bold">
                        {stats?.totalTransfers}
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>
                        إجمالي التحويلات
                      </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                      <SwapHoriz />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography variant="h4" component="div" fontWeight="bold">
                        {stats?.pendingTransfers}
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>
                        تحويلات معلقة
                      </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                      <Notifications />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography variant="h4" component="div" fontWeight="bold">
                        {stats?.monthlyVolume} ر.س
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>
                        حجم التحويلات الشهرية
                      </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                      <TrendingUp />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Recent Transfers */}
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" component="h2">
                  التحويلات الأخيرة
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => router.push('/transfers')}
                >
                  عرض الكل
                </Button>
              </Box>

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>الرقم المرجعي</TableCell>
                      <TableCell>المستقبل</TableCell>
                      <TableCell>المبلغ</TableCell>
                      <TableCell>الحالة</TableCell>
                      <TableCell>التاريخ</TableCell>
                      <TableCell align="center">الإجراءات</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentTransfers.map((transfer) => (
                      <TableRow key={transfer.id} hover>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {transfer.referenceNumber}
                          </Typography>
                        </TableCell>
                        <TableCell>{transfer.receiverName}</TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {transfer.amount} {transfer.currency}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getStatusText(transfer.status)}
                            color={getStatusColor(transfer.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {formatDate(transfer.createdAt)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            size="small"
                            onClick={() => router.push(`/transfers/${transfer.id}`)}
                          >
                            <Visibility />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {recentTransfers.length === 0 && (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    لا توجد تحويلات حتى الآن
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<Send />}
                    onClick={() => router.push('/transfers/new')}
                    sx={{ mt: 2 }}
                  >
                    إنشاء أول تحويل
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      </Layout>
    </>
  );
};

export default DashboardPage;
