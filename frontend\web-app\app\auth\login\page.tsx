'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  EyeIcon, 
  EyeSlashIcon,
  ArrowRightIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Link from 'next/link';

// مخطط التحقق
const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'البريد الإلكتروني مطلوب')
    .email('البريد الإلكتروني غير صحيح'),
  password: z
    .string()
    .min(1, 'كلمة المرور مطلوبة')
    .min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, state: authState } = useAuth();
  const { t, isRTL } = useLanguage();

  // إعداد النموذج
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  // إعادة توجيه المستخدمين المسجلين
  useEffect(() => {
    if (authState.isAuthenticated) {
      const redirectTo = searchParams.get('redirect') || '/dashboard';
      router.push(redirectTo);
    }
  }, [authState.isAuthenticated, router, searchParams]);

  // معالجة تسجيل الدخول
  const onSubmit = async (data: LoginFormData) => {
    try {
      await login(data.email, data.password);
    } catch (error: any) {
      if (error.response?.status === 401) {
        setError('root', {
          message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        });
      } else if (error.response?.status === 423) {
        setError('root', {
          message: 'تم تجميد الحساب. يرجى التواصل مع الدعم',
        });
      } else {
        setError('root', {
          message: error.message || 'حدث خطأ أثناء تسجيل الدخول',
        });
      }
    }
  };

  // إذا كان المستخدم يحتاج للمصادقة الثنائية
  if (authState.requires2FA) {
    router.push('/auth/verify-2fa');
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* الشعار */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-700 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold">WS</span>
            </div>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              WS Transfir
            </span>
          </div>
        </motion.div>

        {/* بطاقة تسجيل الدخول */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="p-8">
            {/* العنوان */}
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {t('auth.login')}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                مرحباً بك مرة أخرى! يرجى تسجيل الدخول إلى حسابك
              </p>
            </div>

            {/* رسالة الخطأ العامة */}
            {errors.root && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mb-4 p-4 bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-lg flex items-center space-x-3 rtl:space-x-reverse"
              >
                <ExclamationTriangleIcon className="w-5 h-5 text-error-600 dark:text-error-400 flex-shrink-0" />
                <span className="text-error-700 dark:text-error-300 text-sm">
                  {errors.root.message}
                </span>
              </motion.div>
            )}

            {/* النموذج */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* البريد الإلكتروني */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('auth.email')}
                </label>
                <input
                  {...register('email')}
                  type="email"
                  className={`input ${errors.email ? 'input-error' : ''}`}
                  placeholder="<EMAIL>"
                  dir="ltr"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* كلمة المرور */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('auth.password')}
                </label>
                <div className="relative">
                  <input
                    {...register('password')}
                    type={showPassword ? 'text' : 'password'}
                    className={`input pr-10 ${errors.password ? 'input-error' : ''}`}
                    placeholder="••••••••"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="w-5 h-5" />
                    ) : (
                      <EyeIcon className="w-5 h-5" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* خيارات إضافية */}
              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    {...register('rememberMe')}
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="mr-2 text-sm text-gray-700 dark:text-gray-300">
                    {t('auth.rememberMe')}
                  </span>
                </label>

                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
                >
                  {t('auth.forgotPassword')}
                </Link>
              </div>

              {/* زر تسجيل الدخول */}
              <Button
                type="submit"
                fullWidth
                size="lg"
                isLoading={isSubmitting}
                loadingText="جاري تسجيل الدخول..."
                rightIcon={<ArrowRightIcon className="w-5 h-5" />}
              >
                {t('auth.login')}
              </Button>
            </form>

            {/* رابط التسجيل */}
            <div className="mt-6 text-center">
              <p className="text-gray-600 dark:text-gray-400">
                ليس لديك حساب؟{' '}
                <Link
                  href="/auth/register"
                  className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
                >
                  إنشاء حساب جديد
                </Link>
              </p>
            </div>
          </Card>
        </motion.div>

        {/* معلومات إضافية */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mt-8 text-center"
        >
          <p className="text-sm text-gray-500 dark:text-gray-400">
            محمي بأحدث تقنيات الأمان والتشفير
          </p>
        </motion.div>
      </div>
    </div>
  );
}
