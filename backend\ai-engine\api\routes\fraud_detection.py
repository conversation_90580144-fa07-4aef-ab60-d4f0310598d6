"""
Fraud Detection API Routes
=========================
واجهات برمجة التطبيقات لكشف الاحتيال
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query # type: ignore
from fastapi.responses import JSONResponse # type: ignore
from pydantic import BaseModel, Field, validator # type: ignore
import structlog # type: ignore

from services.fraud_detector import FraudDetector, TransactionData, FraudPrediction
from core.config import settings
from middleware.auth import get_current_user # type: ignore
from utils.rate_limiter import rate_limit # type: ignore
from utils.validators import validate_transaction_data # type: ignore

logger = structlog.get_logger(__name__)

router = APIRouter()


# Request/Response Models
class TransactionRequest(BaseModel):
    """طلب تحليل المعاملة"""
    transaction_id: str = Field(..., description="معرف المعاملة")
    user_id: str = Field(..., description="معرف المستخدم")
    amount: float = Field(..., gt=0, description="مبلغ المعاملة")
    currency: str = Field(..., description="العملة")
    merchant_id: str = Field(..., description="معرف التاجر")
    merchant_category: str = Field(..., description="فئة التاجر")
    location: Dict[str, Any] = Field(default_factory=dict, description="الموقع الجغرافي")
    device_info: Dict[str, Any] = Field(default_factory=dict, description="معلومات الجهاز")
    additional_data: Dict[str, Any] = Field(default_factory=dict, description="بيانات إضافية")
    
    @validator('currency')
    def validate_currency(cls, v):
        if v not in settings.SUPPORTED_CURRENCIES:
            raise ValueError(f"العملة غير مدعومة: {v}")
        return v
    
    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError("المبلغ يجب أن يكون أكبر من صفر")
        if v > 1000000:  # 1M limit
            raise ValueError("المبلغ يتجاوز الحد الأقصى المسموح")
        return v


class FraudResponse(BaseModel):
    """استجابة كشف الاحتيال"""
    transaction_id: str
    fraud_score: float = Field(..., ge=0, le=1, description="درجة الاحتيال (0-1)")
    risk_level: str = Field(..., description="مستوى المخاطر")
    confidence: float = Field(..., ge=0, le=1, description="مستوى الثقة")
    is_fraud: bool = Field(..., description="هل المعاملة احتيالية")
    reasons: List[str] = Field(..., description="أسباب التصنيف")
    features_importance: Dict[str, float] = Field(..., description="أهمية الميزات")
    timestamp: datetime = Field(..., description="وقت التحليل")
    model_version: str = Field(..., description="إصدار النموذج")
    processing_time_ms: float = Field(..., description="وقت المعالجة بالميلي ثانية")


class BatchTransactionRequest(BaseModel):
    """طلب تحليل مجموعة من المعاملات"""
    transactions: List[TransactionRequest] = Field(..., max_items=100, description="قائمة المعاملات")


class BatchFraudResponse(BaseModel):
    """استجابة تحليل مجموعة من المعاملات"""
    results: List[FraudResponse]
    total_processed: int
    total_fraud_detected: int
    processing_time_ms: float
    batch_id: str


class FraudStatistics(BaseModel):
    """إحصائيات كشف الاحتيال"""
    total_predictions: int
    fraud_detected: int
    fraud_rate: float
    false_positives: int
    model_version: str
    fraud_threshold: float
    cache_size: Dict[str, int]
    uptime_seconds: float


@router.post("/analyze", response_model=FraudResponse)
@rate_limit(requests=100, window=60)
async def analyze_transaction(
    request: TransactionRequest,
    background_tasks: BackgroundTasks,
    fraud_detector: FraudDetector = Depends(),
    current_user: Dict = Depends(get_current_user)
):
    """
    تحليل معاملة واحدة لكشف الاحتيال
    
    - **transaction_id**: معرف المعاملة الفريد
    - **user_id**: معرف المستخدم
    - **amount**: مبلغ المعاملة
    - **currency**: العملة المستخدمة
    - **merchant_id**: معرف التاجر
    - **merchant_category**: فئة التاجر
    """
    try:
        start_time = datetime.now()
        
        logger.info(f"🔍 Analyzing transaction: {request.transaction_id}")
        
        # Validate transaction data
        await validate_transaction_data(request.dict())
        
        # Convert request to TransactionData
        transaction_data = TransactionData(
            transaction_id=request.transaction_id,
            user_id=request.user_id,
            amount=request.amount,
            currency=request.currency,
            merchant_id=request.merchant_id,
            merchant_category=request.merchant_category,
            timestamp=datetime.now(),
            location=request.location,
            device_info=request.device_info,
            additional_data=request.additional_data
        )
        
        # Perform fraud detection
        prediction = await fraud_detector.predict_fraud(transaction_data)
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # Log the result
        background_tasks.add_task(
            log_fraud_analysis,
            request.transaction_id,
            prediction.fraud_score,
            prediction.risk_level,
            current_user.get("user_id")
        )
        
        # Prepare response
        response = FraudResponse(
            transaction_id=prediction.transaction_id,
            fraud_score=prediction.fraud_score,
            risk_level=prediction.risk_level,
            confidence=prediction.confidence,
            is_fraud=prediction.fraud_score >= settings.FRAUD_THRESHOLD,
            reasons=prediction.reasons,
            features_importance=prediction.features_importance,
            timestamp=prediction.timestamp,
            model_version=prediction.model_version,
            processing_time_ms=processing_time
        )
        
        logger.info(f"✅ Transaction analysis completed: {request.transaction_id} - Score: {prediction.fraud_score:.3f}")
        
        return response
        
    except ValueError as e:
        logger.error(f"❌ Validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Fraud analysis failed: {e}")
        raise HTTPException(status_code=500, detail="فشل في تحليل المعاملة")


@router.post("/analyze-batch", response_model=BatchFraudResponse)
@rate_limit(requests=10, window=60)
async def analyze_batch_transactions(
    request: BatchTransactionRequest,
    background_tasks: BackgroundTasks,
    fraud_detector: FraudDetector = Depends(),
    current_user: Dict = Depends(get_current_user)
):
    """
    تحليل مجموعة من المعاملات لكشف الاحتيال
    
    - **transactions**: قائمة المعاملات (حد أقصى 100 معاملة)
    """
    try:
        start_time = datetime.now()
        batch_id = f"batch_{int(start_time.timestamp())}"
        
        logger.info(f"🔍 Analyzing batch: {batch_id} - {len(request.transactions)} transactions")
        
        results = []
        fraud_count = 0
        
        # Process each transaction
        for i, transaction_request in enumerate(request.transactions):
            try:
                # Convert to TransactionData
                transaction_data = TransactionData(
                    transaction_id=transaction_request.transaction_id,
                    user_id=transaction_request.user_id,
                    amount=transaction_request.amount,
                    currency=transaction_request.currency,
                    merchant_id=transaction_request.merchant_id,
                    merchant_category=transaction_request.merchant_category,
                    timestamp=datetime.now(),
                    location=transaction_request.location,
                    device_info=transaction_request.device_info,
                    additional_data=transaction_request.additional_data
                )
                
                # Perform fraud detection
                prediction = await fraud_detector.predict_fraud(transaction_data)
                
                # Create response
                fraud_response = FraudResponse(
                    transaction_id=prediction.transaction_id,
                    fraud_score=prediction.fraud_score,
                    risk_level=prediction.risk_level,
                    confidence=prediction.confidence,
                    is_fraud=prediction.fraud_score >= settings.FRAUD_THRESHOLD,
                    reasons=prediction.reasons,
                    features_importance=prediction.features_importance,
                    timestamp=prediction.timestamp,
                    model_version=prediction.model_version,
                    processing_time_ms=0  # Will be calculated for the batch
                )
                
                results.append(fraud_response)
                
                if fraud_response.is_fraud:
                    fraud_count += 1
                    
            except Exception as e:
                logger.error(f"❌ Failed to process transaction {i}: {e}")
                # Continue with other transactions
                continue
        
        # Calculate total processing time
        total_processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # Log batch analysis
        background_tasks.add_task(
            log_batch_analysis,
            batch_id,
            len(request.transactions),
            len(results),
            fraud_count,
            current_user.get("user_id")
        )
        
        response = BatchFraudResponse(
            results=results,
            total_processed=len(results),
            total_fraud_detected=fraud_count,
            processing_time_ms=total_processing_time,
            batch_id=batch_id
        )
        
        logger.info(f"✅ Batch analysis completed: {batch_id} - {len(results)}/{len(request.transactions)} processed")
        
        return response
        
    except Exception as e:
        logger.error(f"❌ Batch fraud analysis failed: {e}")
        raise HTTPException(status_code=500, detail="فشل في تحليل المعاملات")


@router.get("/statistics", response_model=FraudStatistics)
async def get_fraud_statistics(
    fraud_detector: FraudDetector = Depends(),
    current_user: Dict = Depends(get_current_user)
):
    """
    الحصول على إحصائيات كشف الاحتيال
    """
    try:
        stats = await fraud_detector.get_statistics()
        
        return FraudStatistics(
            total_predictions=stats["total_predictions"],
            fraud_detected=stats["fraud_detected"],
            fraud_rate=stats["fraud_rate"],
            false_positives=stats["false_positives"],
            model_version=stats["model_version"],
            fraud_threshold=stats["fraud_threshold"],
            cache_size=stats["cache_size"],
            uptime_seconds=0  # TODO: Calculate actual uptime
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to get fraud statistics: {e}")
        raise HTTPException(status_code=500, detail="فشل في الحصول على الإحصائيات")


@router.get("/transaction/{transaction_id}/history")
async def get_transaction_fraud_history(
    transaction_id: str,
    fraud_detector: FraudDetector = Depends(),
    current_user: Dict = Depends(get_current_user)
):
    """
    الحصول على تاريخ تحليل الاحتيال لمعاملة معينة
    """
    try:
        # Get cached prediction
        cache_key = f"fraud_prediction:{transaction_id}"
        cached_data = await fraud_detector.redis_client.get(cache_key)
        
        if not cached_data:
            raise HTTPException(status_code=404, detail="لم يتم العثور على تحليل للمعاملة")
        
        import json
        prediction_data = json.loads(cached_data)
        
        return {
            "transaction_id": transaction_id,
            "fraud_score": prediction_data["fraud_score"],
            "risk_level": prediction_data["risk_level"],
            "confidence": prediction_data["confidence"],
            "timestamp": prediction_data["timestamp"],
            "model_version": prediction_data["model_version"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get transaction history: {e}")
        raise HTTPException(status_code=500, detail="فشل في الحصول على تاريخ المعاملة")


@router.post("/feedback")
async def submit_fraud_feedback(
    transaction_id: str,
    is_actual_fraud: bool,
    feedback_notes: Optional[str] = None,
    fraud_detector: FraudDetector = Depends(),
    current_user: Dict = Depends(get_current_user)
):
    """
    إرسال تغذية راجعة حول دقة كشف الاحتيال
    """
    try:
        # TODO: Store feedback in database for model improvement
        logger.info(f"📝 Fraud feedback received: {transaction_id} - Actual fraud: {is_actual_fraud}")
        
        # Update false positive counter if needed
        if not is_actual_fraud:
            fraud_detector.false_positives += 1
        
        return {
            "message": "تم استلام التغذية الراجعة بنجاح",
            "transaction_id": transaction_id,
            "feedback_recorded": True
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to submit feedback: {e}")
        raise HTTPException(status_code=500, detail="فشل في إرسال التغذية الراجعة")


# Background tasks
async def log_fraud_analysis(
    transaction_id: str,
    fraud_score: float,
    risk_level: str,
    user_id: str
):
    """تسجيل تحليل الاحتيال"""
    try:
        # TODO: Log to database or external system
        logger.info(f"📊 Fraud analysis logged: {transaction_id} - Score: {fraud_score} - Level: {risk_level}")
    except Exception as e:
        logger.error(f"❌ Failed to log fraud analysis: {e}")


async def log_batch_analysis(
    batch_id: str,
    total_transactions: int,
    processed_transactions: int,
    fraud_detected: int,
    user_id: str
):
    """تسجيل تحليل المجموعة"""
    try:
        # TODO: Log to database or external system
        logger.info(f"📊 Batch analysis logged: {batch_id} - {processed_transactions}/{total_transactions} processed - {fraud_detected} fraud detected")
    except Exception as e:
        logger.error(f"❌ Failed to log batch analysis: {e}")
