import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { TransfersService } from '../services/transfers.service';
import { CreateTransferDto } from '../dto/create-transfer.dto';
import { Transfer, TransferStatus, TransferType } from '../entities/transfer.entity';
import { JwtAuthGuard } from '../../../common/guards/auth.guard';
import { RolesGuard } from '../../../common/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { GetUser } from '../../../common/decorators/get-user.decorator';

@ApiTags('Transfers')
@Controller('transfers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class TransfersController {
  constructor(private readonly transfersService: TransfersService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'إنشاء تحويل جديد',
    description: 'إنشاء تحويل مالي جديد',
  })
  @ApiResponse({
    status: 201,
    description: 'تم إنشاء التحويل بنجاح',
    type: Transfer,
  })
  @ApiResponse({
    status: 400,
    description: 'بيانات غير صالحة',
  })
  @ApiResponse({
    status: 403,
    description: 'غير مصرح بالعملية',
  })
  async create(
    @GetUser() user: any,
    @Body() createTransferDto: CreateTransferDto,
  ): Promise<Transfer> {
    return this.transfersService.create(user.sub, createTransferDto);
  }

  @Get()
  @ApiOperation({
    summary: 'الحصول على قائمة التحويلات',
    description: 'استرجاع قائمة التحويلات مع إمكانية التصفية',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'رقم الصفحة',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'عدد العناصر في الصفحة',
    example: 10,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: TransferStatus,
    description: 'تصفية حسب الحالة',
  })
  @ApiQuery({
    name: 'transferType',
    required: false,
    enum: TransferType,
    description: 'تصفية حسب نوع التحويل',
  })
  @ApiResponse({
    status: 200,
    description: 'قائمة التحويلات',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Transfer' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  })
  async findAll(
    @GetUser() user: any,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status?: TransferStatus,
    @Query('transferType') transferType?: TransferType,
  ) {
    return this.transfersService.getUserTransfers(user.sub, {
      page,
      limit,
      status,
      transferType,
    });
  }

  @Get('quote')
  @ApiOperation({
    summary: 'الحصول على عرض سعر',
    description: 'حساب تكلفة التحويل وسعر الصرف',
  })
  @ApiQuery({
    name: 'sendAmount',
    required: true,
    description: 'مبلغ الإرسال',
    example: '1000',
  })
  @ApiQuery({
    name: 'sendCurrency',
    required: true,
    description: 'عملة الإرسال',
    example: 'SAR',
  })
  @ApiQuery({
    name: 'receiveCurrency',
    required: true,
    description: 'عملة الاستقبال',
    example: 'USD',
  })
  @ApiQuery({
    name: 'transferType',
    required: true,
    enum: TransferType,
    description: 'نوع التحويل',
  })
  @ApiResponse({
    status: 200,
    description: 'عرض السعر',
    schema: {
      type: 'object',
      properties: {
        sendAmount: { type: 'string' },
        sendCurrency: { type: 'string' },
        receiveAmount: { type: 'string' },
        receiveCurrency: { type: 'string' },
        exchangeRate: { type: 'string' },
        transferFee: { type: 'string' },
        exchangeFee: { type: 'string' },
        totalFees: { type: 'string' },
        totalAmount: { type: 'string' },
        estimatedDelivery: { type: 'string', format: 'date-time' },
      },
    },
  })
  async getQuote(
    @Query('sendAmount') sendAmount: string,
    @Query('sendCurrency') sendCurrency: string,
    @Query('receiveCurrency') receiveCurrency: string,
    @Query('transferType') transferType: TransferType,
  ) {
    return this.transfersService.getQuote(
      sendAmount,
      sendCurrency,
      receiveCurrency,
      transferType,
    );
  }

  @Get('stats')
  @ApiOperation({
    summary: 'إحصائيات التحويلات',
    description: 'الحصول على إحصائيات تحويلات المستخدم',
  })
  @ApiResponse({
    status: 200,
    description: 'إحصائيات التحويلات',
    schema: {
      type: 'object',
      properties: {
        totalTransfers: { type: 'number' },
        completedTransfers: { type: 'number' },
        pendingTransfers: { type: 'number' },
        totalVolume: { type: 'string' },
        successRate: { type: 'number' },
      },
    },
  })
  async getStats(@GetUser() user: any) {
    return this.transfersService.getTransferStats(user.sub);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'الحصول على تحويل محدد',
    description: 'استرجاع تفاصيل تحويل محدد',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف التحويل',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تفاصيل التحويل',
    type: Transfer,
  })
  @ApiResponse({
    status: 404,
    description: 'التحويل غير موجود',
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: any,
  ): Promise<Transfer> {
    const transfer = await this.transfersService.findById(id);
    
    // التحقق من الصلاحية
    if (transfer.senderId !== user.sub && transfer.receiverId !== user.sub) {
      // يمكن للمديرين والوكلاء رؤية جميع التحويلات
      if (!['admin', 'super_admin', 'agent'].includes(user.role)) {
        throw new Error('غير مصرح لك بعرض هذا التحويل');
      }
    }
    
    return transfer;
  }

  @Get('reference/:referenceNumber')
  @ApiOperation({
    summary: 'البحث بالرقم المرجعي',
    description: 'البحث عن تحويل بالرقم المرجعي',
  })
  @ApiParam({
    name: 'referenceNumber',
    description: 'الرقم المرجعي للتحويل',
    example: 'WS20241225001',
  })
  @ApiResponse({
    status: 200,
    description: 'تفاصيل التحويل',
    type: Transfer,
  })
  @ApiResponse({
    status: 404,
    description: 'التحويل غير موجود',
  })
  async findByReference(
    @Param('referenceNumber') referenceNumber: string,
    @GetUser() user: any,
  ): Promise<Transfer> {
    const transfer = await this.transfersService.findByReference(referenceNumber);
    
    // التحقق من الصلاحية
    if (transfer.senderId !== user.sub && transfer.receiverId !== user.sub) {
      if (!['admin', 'super_admin', 'agent'].includes(user.role)) {
        throw new Error('غير مصرح لك بعرض هذا التحويل');
      }
    }
    
    return transfer;
  }

  @Patch(':id/cancel')
  @ApiOperation({
    summary: 'إلغاء تحويل',
    description: 'إلغاء تحويل معلق أو قيد المعالجة',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف التحويل',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تم إلغاء التحويل بنجاح',
    type: Transfer,
  })
  @ApiResponse({
    status: 400,
    description: 'لا يمكن إلغاء التحويل',
  })
  async cancel(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: any,
    @Body('reason') reason?: string,
  ): Promise<Transfer> {
    return this.transfersService.cancel(id, user.sub, reason);
  }

  @Patch(':id/status')
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin', 'agent')
  @ApiOperation({
    summary: 'تحديث حالة التحويل',
    description: 'تحديث حالة التحويل (للمديرين والوكلاء فقط)',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف التحويل',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تحديث الحالة بنجاح',
    type: Transfer,
  })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('status') status: TransferStatus,
    @Body('reason') reason?: string,
    @GetUser() user?: any,
  ): Promise<Transfer> {
    return this.transfersService.updateStatus(id, status, reason, user?.sub);
  }
}
