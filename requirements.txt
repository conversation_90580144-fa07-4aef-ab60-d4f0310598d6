# =====================================================
# WalletSystem Requirements
# =====================================================

# Core Framework
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# Database
asyncpg>=0.29.0
psycopg2-binary>=2.9.9
alembic>=1.13.0

# HTTP Client
aiohttp>=3.9.0
httpx>=0.25.0

# Authentication & Security
PyJWT>=2.8.0
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Data Processing
pandas>=2.1.0
numpy>=1.25.0

# Async Support
asyncio-mqtt>=0.16.0
aiofiles>=23.2.1

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0

# Development Tools
black>=23.10.0
flake8>=6.1.0
mypy>=1.7.0
pre-commit>=3.5.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# Monitoring & Logging
structlog>=23.2.0
prometheus-client>=0.19.0

# Date & Time
python-dateutil>=2.8.2
pytz>=2023.3

# Utilities
python-dotenv>=1.0.0
click>=8.1.0
rich>=13.7.0

# Optional Dependencies (for specific features)
# Uncomment as needed:

# Redis (for caching and rate limiting)
# redis>=5.0.0
# aioredis>=2.0.0

# Message Queue
# celery>=5.3.0
# kombu>=5.3.0

# Email
# aiosmtplib>=3.0.0
# jinja2>=3.1.0

# File Storage
# boto3>=1.34.0  # AWS S3
# azure-storage-blob>=12.19.0  # Azure Blob

# Monitoring
# sentry-sdk[fastapi]>=1.38.0
# datadog>=0.48.0

# Machine Learning (for future AI features)
# scikit-learn>=1.3.0
# tensorflow>=2.15.0
# torch>=2.1.0

# Financial Libraries
# yfinance>=0.2.0
# quantlib>=1.32.0

# Blockchain (for future crypto features)
# web3>=6.11.0
# eth-account>=0.10.0

# Development Dependencies
[dev]
jupyter>=1.0.0
ipython>=8.17.0
notebook>=7.0.0

# Production Dependencies
[prod]
gunicorn>=21.2.0
supervisor>=4.2.0
nginx-python-module>=1.25.0
