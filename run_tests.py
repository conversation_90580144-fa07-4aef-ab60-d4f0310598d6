#!/usr/bin/env python3
"""
Test Runner Script
==================
سكريبت تشغيل الاختبارات مع إعدادات متقدمة
"""

import os
import sys
import subprocess
import argparse
import logging
from pathlib import Path
from typing import List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Project root directory
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
TESTS_DIR = PROJECT_ROOT / "tests"
REPORTS_DIR = PROJECT_ROOT / "reports"

def ensure_directories():
    """إنشاء المجلدات المطلوبة"""
    REPORTS_DIR.mkdir(exist_ok=True)
    (PROJECT_ROOT / "htmlcov").mkdir(exist_ok=True)

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    required_packages = [
        "pytest",
        "pytest-asyncio", 
        "pytest-cov",
        "pytest-mock",
        "pytest-html",
        "pytest-json-report"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing packages: {', '.join(missing_packages)}")
        logger.info("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def run_command(cmd: List[str], cwd: Optional[Path] = None) -> int:
    """تشغيل أمر وإرجاع رمز الخروج"""
    logger.info(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd or PROJECT_ROOT,
            capture_output=False,
            text=True
        )
        return result.returncode
    except Exception as e:
        logger.error(f"Command failed: {e}")
        return 1

def run_linting():
    """تشغيل فحص جودة الكود"""
    logger.info("🔍 Running code quality checks...")
    
    commands = [
        # Black formatting check
        ["python", "-m", "black", "--check", "--diff", "backend", "tests"],
        
        # isort import sorting check
        ["python", "-m", "isort", "--check-only", "--diff", "backend", "tests"],
        
        # Flake8 linting
        ["python", "-m", "flake8", "backend", "tests"],
        
        # MyPy type checking
        ["python", "-m", "mypy", "backend"],
    ]
    
    for cmd in commands:
        if run_command(cmd) != 0:
            logger.error(f"Linting failed: {' '.join(cmd)}")
            return False
    
    logger.info("✅ All linting checks passed!")
    return True

def run_security_checks():
    """تشغيل فحص الأمان"""
    logger.info("🔒 Running security checks...")
    
    commands = [
        # Bandit security linting
        ["python", "-m", "bandit", "-r", "backend", "-f", "json", "-o", "reports/bandit-report.json"],
        
        # Safety check for known vulnerabilities
        ["python", "-m", "safety", "check", "--json", "--output", "reports/safety-report.json"],
    ]
    
    for cmd in commands:
        try:
            run_command(cmd)
        except Exception as e:
            logger.warning(f"Security check failed (optional): {e}")
    
    logger.info("✅ Security checks completed!")
    return True

def run_tests(
    test_type: str = "all",
    coverage: bool = True,
    verbose: bool = True,
    parallel: bool = False,
    markers: Optional[str] = None,
    pattern: Optional[str] = None
):
    """تشغيل الاختبارات"""
    logger.info(f"🧪 Running {test_type} tests...")
    
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Test selection
    if test_type == "unit":
        cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "api":
        cmd.extend(["-m", "api"])
    elif test_type == "security":
        cmd.extend(["-m", "security"])
    elif test_type == "bank":
        cmd.extend(["-m", "bank"])
    elif test_type == "webhook":
        cmd.extend(["-m", "webhook"])
    elif test_type == "accounting":
        cmd.extend(["-m", "accounting"])
    elif test_type == "compliance":
        cmd.extend(["-m", "compliance"])
    elif test_type == "fast":
        cmd.extend(["-m", "not slow"])
    
    # Custom markers
    if markers:
        cmd.extend(["-m", markers])
    
    # Test pattern
    if pattern:
        cmd.extend(["-k", pattern])
    
    # Coverage
    if coverage:
        cmd.extend([
            "--cov=backend",
            "--cov-report=html:htmlcov",
            "--cov-report=xml:coverage.xml",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
    
    # Verbose output
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Parallel execution
    if parallel:
        try:
            import pytest_xdist
            cmd.extend(["-n", "auto"])
        except ImportError:
            logger.warning("pytest-xdist not available, running sequentially")
    
    # Reports
    cmd.extend([
        "--html=reports/report.html",
        "--self-contained-html",
        "--json-report",
        "--json-report-file=reports/report.json",
        "--tb=short",
        "--durations=10"
    ])
    
    # Run tests
    return run_command(cmd) == 0

def generate_reports():
    """إنشاء التقارير"""
    logger.info("📊 Generating reports...")
    
    # Coverage report
    if (PROJECT_ROOT / "htmlcov" / "index.html").exists():
        logger.info(f"Coverage report: {PROJECT_ROOT / 'htmlcov' / 'index.html'}")
    
    # Test report
    if (REPORTS_DIR / "report.html").exists():
        logger.info(f"Test report: {REPORTS_DIR / 'report.html'}")
    
    # JSON report
    if (REPORTS_DIR / "report.json").exists():
        logger.info(f"JSON report: {REPORTS_DIR / 'report.json'}")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="WalletSystem Test Runner")
    
    parser.add_argument(
        "--type", "-t",
        choices=["all", "unit", "integration", "api", "security", "bank", "webhook", "accounting", "compliance", "fast"],
        default="all",
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "--no-coverage", "-nc",
        action="store_true",
        help="Disable coverage reporting"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Quiet output"
    )
    
    parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="Run tests in parallel"
    )
    
    parser.add_argument(
        "--lint", "-l",
        action="store_true",
        help="Run linting checks"
    )
    
    parser.add_argument(
        "--security", "-s",
        action="store_true",
        help="Run security checks"
    )
    
    parser.add_argument(
        "--markers", "-m",
        help="Custom pytest markers"
    )
    
    parser.add_argument(
        "--pattern", "-k",
        help="Test name pattern"
    )
    
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install test dependencies"
    )
    
    args = parser.parse_args()
    
    # Install dependencies if requested
    if args.install_deps:
        logger.info("📦 Installing test dependencies...")
        deps = [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
            "pytest-mock>=3.12.0",
            "pytest-html>=4.1.0",
            "pytest-json-report>=1.5.0",
            "pytest-xdist>=3.5.0",
            "bandit>=1.7.5",
            "safety>=2.3.0"
        ]
        
        cmd = ["pip", "install"] + deps
        if run_command(cmd) != 0:
            logger.error("Failed to install dependencies")
            return 1
    
    # Ensure directories exist
    ensure_directories()
    
    # Check dependencies
    if not check_dependencies():
        logger.error("Missing required dependencies. Use --install-deps to install them.")
        return 1
    
    success = True
    
    # Run linting if requested
    if args.lint:
        success = run_linting() and success
    
    # Run security checks if requested
    if args.security:
        success = run_security_checks() and success
    
    # Run tests
    success = run_tests(
        test_type=args.type,
        coverage=not args.no_coverage,
        verbose=not args.quiet,
        parallel=args.parallel,
        markers=args.markers,
        pattern=args.pattern
    ) and success
    
    # Generate reports
    generate_reports()
    
    if success:
        logger.info("🎉 All tests passed!")
        return 0
    else:
        logger.error("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
