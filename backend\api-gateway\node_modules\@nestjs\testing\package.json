{"name": "@nestjs/testing", "version": "10.4.20", "description": "Nest - modern, fast, powerful node.js web framework (@testing)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://nestjs.com", "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "repository": {"type": "git", "url": "https://github.com/nestjs/nest.git", "directory": "packages/testing"}, "publishConfig": {"access": "public"}, "dependencies": {"tslib": "2.8.1"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/platform-express": "^10.0.0"}, "peerDependenciesMeta": {"@nestjs/microservices": {"optional": true}, "@nestjs/platform-express": {"optional": true}}}