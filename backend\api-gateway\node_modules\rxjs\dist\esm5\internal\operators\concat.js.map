{"version": 3, "file": "concat.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/concat.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAC5C,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAY1C,MAAM,UAAU,MAAM;IAAO,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,yBAAc;;IACzC,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,SAAS,EAAE,CAAC,IAAI,gBAAE,MAAM,UAAK,IAAI,IAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;AACL,CAAC"}