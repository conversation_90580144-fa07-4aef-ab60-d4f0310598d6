"""
Settlement Service
=================
خدمة التسوية التلقائية المتقدمة
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from decimal import Decimal
import json
import uuid

import asyncpg
from ..shared.database.connection import DatabaseConnection
from .payment_gateway_service import PaymentStatus, TransactionType

logger = logging.getLogger(__name__)


class SettlementStatus(Enum):
    """حالات التسوية"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIALLY_SETTLED = "partially_settled"


class SettlementType(Enum):
    """أنواع التسوية"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    INSTANT = "instant"
    MANUAL = "manual"


class AccountType(Enum):
    """أنواع الحسابات"""
    MERCHANT = "merchant"
    AGENT = "agent"
    SYSTEM = "system"
    ESCROW = "escrow"
    RESERVE = "reserve"


@dataclass
class SettlementRequest:
    """طلب تسوية"""
    account_id: str
    amount: Decimal
    currency: str
    settlement_type: SettlementType
    description: str
    reference_transactions: List[str] = None
    metadata: Dict[str, Any] = None
    scheduled_at: datetime = None


@dataclass
class SettlementResponse:
    """استجابة التسوية"""
    settlement_id: str
    status: SettlementStatus
    account_id: str
    amount: Decimal
    currency: str
    fees_amount: Decimal
    net_amount: Decimal
    reference_number: str = None
    bank_reference: str = None
    error_message: str = None
    created_at: datetime = None
    completed_at: datetime = None


@dataclass
class BalanceInfo:
    """معلومات الرصيد"""
    account_id: str
    available_balance: Decimal
    pending_balance: Decimal
    reserved_balance: Decimal
    total_balance: Decimal
    currency: str
    last_updated: datetime


class SettlementService:
    """خدمة التسوية التلقائية"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Settlement configurations
        self.settlement_configs = {
            SettlementType.DAILY: {
                "schedule_time": "23:00",
                "min_amount": Decimal('100.00'),
                "max_amount": Decimal('1000000.00'),
                "fees_percentage": Decimal('0.5'),
                "processing_days": 1
            },
            SettlementType.WEEKLY: {
                "schedule_day": "friday",
                "schedule_time": "23:00",
                "min_amount": Decimal('500.00'),
                "max_amount": Decimal('5000000.00'),
                "fees_percentage": Decimal('0.3'),
                "processing_days": 2
            },
            SettlementType.MONTHLY: {
                "schedule_day": "last",
                "schedule_time": "23:00",
                "min_amount": Decimal('1000.00'),
                "max_amount": Decimal('10000000.00'),
                "fees_percentage": Decimal('0.2'),
                "processing_days": 3
            },
            SettlementType.INSTANT: {
                "min_amount": Decimal('10.00'),
                "max_amount": Decimal('50000.00'),
                "fees_percentage": Decimal('1.0'),
                "processing_days": 0
            }
        }
        
        # Risk management settings
        self.risk_limits = {
            "daily_limit": Decimal('100000.00'),
            "weekly_limit": Decimal('500000.00'),
            "monthly_limit": Decimal('2000000.00'),
            "max_single_settlement": Decimal('50000.00')
        }
        
        # Statistics
        self.settlements_processed = 0
        self.settlements_failed = 0
        self.total_settled_amount = Decimal('0.00')
        
        # Bank integration settings
        self.bank_configs = {
            "default_bank": {
                "bank_code": "RIBLSARI",
                "api_endpoint": "https://api.bank.com/v1/",
                "api_key": "your_bank_api_key",
                "supported_currencies": ["SAR", "USD", "EUR"]
            }
        }
    
    async def process_settlement(self, request: SettlementRequest) -> SettlementResponse:
        """معالجة تسوية"""
        try:
            logger.info(f"💰 Processing settlement: {request.amount} {request.currency} for account {request.account_id}")
            
            # Validate settlement request
            await self._validate_settlement_request(request)
            
            # Generate settlement ID
            settlement_id = f"set_{uuid.uuid4().hex[:12]}"
            
            # Check account balance
            balance_info = await self.get_account_balance(request.account_id, request.currency)
            if balance_info.available_balance < request.amount:
                raise ValueError("Insufficient balance for settlement")
            
            # Check risk limits
            await self._check_risk_limits(request)
            
            # Calculate fees
            fees = await self._calculate_settlement_fees(request.amount, request.settlement_type)
            net_amount = request.amount - fees
            
            # Reserve funds
            await self._reserve_funds(request.account_id, request.amount, request.currency)
            
            # Store settlement record
            await self._store_settlement_record(settlement_id, request, fees, net_amount)
            
            # Process settlement based on type
            if request.settlement_type == SettlementType.INSTANT:
                result = await self._process_instant_settlement(settlement_id, request, net_amount)
            else:
                result = await self._schedule_settlement(settlement_id, request, net_amount)
            
            # Update statistics
            if result.status in [SettlementStatus.COMPLETED, SettlementStatus.PROCESSING]:
                self.settlements_processed += 1
                if result.status == SettlementStatus.COMPLETED:
                    self.total_settled_amount += request.amount
            else:
                self.settlements_failed += 1
            
            logger.info(f"✅ Settlement processed: {settlement_id} - Status: {result.status.value}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Settlement processing failed: {e}")
            self.settlements_failed += 1
            
            # Release reserved funds on failure
            if 'settlement_id' in locals():
                await self._release_reserved_funds(request.account_id, request.amount, request.currency)
            
            return SettlementResponse(
                settlement_id=settlement_id if 'settlement_id' in locals() else None,
                status=SettlementStatus.FAILED,
                account_id=request.account_id,
                amount=request.amount,
                currency=request.currency,
                fees_amount=Decimal('0.00'),
                net_amount=request.amount,
                error_message=str(e),
                created_at=datetime.now()
            )
    
    async def get_account_balance(self, account_id: str, currency: str) -> BalanceInfo:
        """الحصول على رصيد الحساب"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        account_id,
                        available_balance,
                        pending_balance,
                        reserved_balance,
                        (available_balance + pending_balance + reserved_balance) as total_balance,
                        currency,
                        updated_at
                    FROM account_balances 
                    WHERE account_id = $1 AND currency = $2
                """
                
                row = await conn.fetchrow(query, account_id, currency)
                
                if row:
                    return BalanceInfo(
                        account_id=row['account_id'],
                        available_balance=row['available_balance'],
                        pending_balance=row['pending_balance'],
                        reserved_balance=row['reserved_balance'],
                        total_balance=row['total_balance'],
                        currency=row['currency'],
                        last_updated=row['updated_at']
                    )
                else:
                    # Create new balance record if not exists
                    await self._create_balance_record(account_id, currency)
                    return BalanceInfo(
                        account_id=account_id,
                        available_balance=Decimal('0.00'),
                        pending_balance=Decimal('0.00'),
                        reserved_balance=Decimal('0.00'),
                        total_balance=Decimal('0.00'),
                        currency=currency,
                        last_updated=datetime.now()
                    )
                
        except Exception as e:
            logger.error(f"❌ Failed to get account balance: {e}")
            raise
    
    async def update_account_balance(
        self, 
        account_id: str, 
        amount: Decimal, 
        currency: str, 
        transaction_type: str,
        reference_id: str = None
    ) -> bool:
        """تحديث رصيد الحساب"""
        try:
            async with self.db_connection.get_connection() as conn:
                async with conn.transaction():
                    # Get current balance
                    current_balance = await self.get_account_balance(account_id, currency)
                    
                    # Calculate new balances based on transaction type
                    new_available = current_balance.available_balance
                    new_pending = current_balance.pending_balance
                    new_reserved = current_balance.reserved_balance
                    
                    if transaction_type == "credit":
                        new_available += amount
                    elif transaction_type == "debit":
                        if current_balance.available_balance < amount:
                            raise ValueError("Insufficient available balance")
                        new_available -= amount
                    elif transaction_type == "reserve":
                        if current_balance.available_balance < amount:
                            raise ValueError("Insufficient available balance")
                        new_available -= amount
                        new_reserved += amount
                    elif transaction_type == "release_reserve":
                        if current_balance.reserved_balance < amount:
                            raise ValueError("Insufficient reserved balance")
                        new_reserved -= amount
                        new_available += amount
                    elif transaction_type == "settle_reserve":
                        if current_balance.reserved_balance < amount:
                            raise ValueError("Insufficient reserved balance")
                        new_reserved -= amount
                    
                    # Update balance
                    update_query = """
                        UPDATE account_balances 
                        SET available_balance = $1,
                            pending_balance = $2,
                            reserved_balance = $3,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE account_id = $4 AND currency = $5
                    """
                    
                    await conn.execute(
                        update_query,
                        new_available,
                        new_pending,
                        new_reserved,
                        account_id,
                        currency
                    )
                    
                    # Log balance change
                    await self._log_balance_change(
                        conn, account_id, currency, amount, transaction_type, 
                        current_balance.available_balance, new_available, reference_id
                    )
                    
                    logger.info(f"💳 Balance updated for {account_id}: {transaction_type} {amount} {currency}")
                    return True
                    
        except Exception as e:
            logger.error(f"❌ Failed to update account balance: {e}")
            return False
    
    async def get_settlement_status(self, settlement_id: str) -> Optional[SettlementResponse]:
        """الحصول على حالة التسوية"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM settlements WHERE id = $1
                """
                
                row = await conn.fetchrow(query, settlement_id)
                
                if row:
                    return SettlementResponse(
                        settlement_id=row['id'],
                        status=SettlementStatus(row['status']),
                        account_id=row['account_id'],
                        amount=row['amount'],
                        currency=row['currency'],
                        fees_amount=row['fees_amount'],
                        net_amount=row['net_amount'],
                        reference_number=row['reference_number'],
                        bank_reference=row['bank_reference'],
                        created_at=row['created_at'],
                        completed_at=row['completed_at']
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get settlement status: {e}")
            return None
    
    async def get_pending_settlements(self, account_id: str = None) -> List[Dict[str, Any]]:
        """الحصول على التسويات المعلقة"""
        try:
            async with self.db_connection.get_connection() as conn:
                if account_id:
                    query = """
                        SELECT * FROM settlements 
                        WHERE account_id = $1 AND status IN ('pending', 'processing')
                        ORDER BY created_at DESC
                    """
                    rows = await conn.fetch(query, account_id)
                else:
                    query = """
                        SELECT * FROM settlements 
                        WHERE status IN ('pending', 'processing')
                        ORDER BY created_at DESC
                    """
                    rows = await conn.fetch(query)
                
                return [
                    {
                        "settlement_id": row['id'],
                        "account_id": row['account_id'],
                        "amount": float(row['amount']),
                        "currency": row['currency'],
                        "status": row['status'],
                        "settlement_type": row['settlement_type'],
                        "created_at": row['created_at'].isoformat(),
                        "scheduled_at": row['scheduled_at'].isoformat() if row['scheduled_at'] else None
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get pending settlements: {e}")
            return []
    
    async def process_scheduled_settlements(self) -> Dict[str, Any]:
        """معالجة التسويات المجدولة"""
        try:
            logger.info("⏰ Processing scheduled settlements")
            
            # Get settlements ready for processing
            ready_settlements = await self._get_ready_settlements()
            
            results = {
                "processed": 0,
                "failed": 0,
                "total_amount": Decimal('0.00'),
                "details": []
            }
            
            for settlement in ready_settlements:
                try:
                    # Process settlement
                    result = await self._execute_settlement(settlement)
                    
                    if result.status == SettlementStatus.COMPLETED:
                        results["processed"] += 1
                        results["total_amount"] += settlement['amount']
                    else:
                        results["failed"] += 1
                    
                    results["details"].append({
                        "settlement_id": settlement['id'],
                        "status": result.status.value,
                        "amount": float(settlement['amount']),
                        "error": result.error_message
                    })
                    
                except Exception as e:
                    logger.error(f"❌ Failed to process settlement {settlement['id']}: {e}")
                    results["failed"] += 1
                    results["details"].append({
                        "settlement_id": settlement['id'],
                        "status": "failed",
                        "error": str(e)
                    })
            
            logger.info(f"✅ Scheduled settlements processed: {results['processed']} successful, {results['failed']} failed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to process scheduled settlements: {e}")
            return {"processed": 0, "failed": 0, "total_amount": 0, "details": []}
    
    async def get_settlement_statistics(
        self,
        start_date: date = None,
        end_date: date = None,
        account_id: str = None
    ) -> Dict[str, Any]:
        """الحصول على إحصائيات التسوية"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = []
                params = []
                param_count = 0
                
                if start_date:
                    param_count += 1
                    where_conditions.append(f"DATE(created_at) >= ${param_count}")
                    params.append(start_date)
                
                if end_date:
                    param_count += 1
                    where_conditions.append(f"DATE(created_at) <= ${param_count}")
                    params.append(end_date)
                
                if account_id:
                    param_count += 1
                    where_conditions.append(f"account_id = ${param_count}")
                    params.append(account_id)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Overall statistics
                stats_query = f"""
                    SELECT 
                        COUNT(*) as total_settlements,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_settlements,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_settlements,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_settled_amount,
                        SUM(CASE WHEN status = 'completed' THEN fees_amount ELSE 0 END) as total_fees,
                        AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END) as avg_settlement_amount
                    FROM settlements 
                    WHERE {where_clause}
                """
                
                stats = await conn.fetchrow(stats_query, *params)
                
                # Settlement type breakdown
                type_query = f"""
                    SELECT 
                        settlement_type,
                        COUNT(*) as count,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount
                    FROM settlements 
                    WHERE {where_clause}
                    GROUP BY settlement_type
                    ORDER BY total_amount DESC
                """
                
                type_stats = await conn.fetch(type_query, *params)
                
                # Daily trend
                daily_query = f"""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as settlements_count,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as daily_amount
                    FROM settlements 
                    WHERE {where_clause}
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                    LIMIT 30
                """
                
                daily_stats = await conn.fetch(daily_query, *params)
                
                # Calculate success rate
                total_settlements = stats['total_settlements']
                success_rate = (stats['completed_settlements'] / max(total_settlements, 1)) * 100
                
                return {
                    "overview": {
                        "total_settlements": total_settlements,
                        "completed_settlements": stats['completed_settlements'],
                        "failed_settlements": stats['failed_settlements'],
                        "success_rate": round(success_rate, 2),
                        "total_settled_amount": float(stats['total_settled_amount'] or 0),
                        "total_fees": float(stats['total_fees'] or 0),
                        "avg_settlement_amount": float(stats['avg_settlement_amount'] or 0)
                    },
                    "by_type": [
                        {
                            "type": row['settlement_type'],
                            "total_settlements": row['count'],
                            "completed_settlements": row['completed'],
                            "success_rate": round((row['completed'] / max(row['count'], 1)) * 100, 2),
                            "total_amount": float(row['total_amount'] or 0)
                        }
                        for row in type_stats
                    ],
                    "daily_trend": [
                        {
                            "date": row['date'].isoformat(),
                            "settlements_count": row['settlements_count'],
                            "completed_count": row['completed_count'],
                            "success_rate": round((row['completed_count'] / max(row['settlements_count'], 1)) * 100, 2),
                            "daily_amount": float(row['daily_amount'] or 0)
                        }
                        for row in daily_stats
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get settlement statistics: {e}")
            return {}
    
    # Helper methods
    async def _validate_settlement_request(self, request: SettlementRequest):
        """التحقق من صحة طلب التسوية"""
        if request.amount <= 0:
            raise ValueError("Settlement amount must be positive")
        
        config = self.settlement_configs.get(request.settlement_type)
        if not config:
            raise ValueError(f"Unsupported settlement type: {request.settlement_type.value}")
        
        if request.amount < config['min_amount']:
            raise ValueError(f"Amount below minimum: {config['min_amount']}")
        
        if request.amount > config['max_amount']:
            raise ValueError(f"Amount exceeds maximum: {config['max_amount']}")
    
    async def _check_risk_limits(self, request: SettlementRequest):
        """فحص حدود المخاطر"""
        # Check daily limit
        today_total = await self._get_daily_settlement_total(request.account_id)
        if today_total + request.amount > self.risk_limits['daily_limit']:
            raise ValueError("Daily settlement limit exceeded")
        
        # Check single settlement limit
        if request.amount > self.risk_limits['max_single_settlement']:
            raise ValueError("Single settlement limit exceeded")
    
    async def _calculate_settlement_fees(self, amount: Decimal, settlement_type: SettlementType) -> Decimal:
        """حساب رسوم التسوية"""
        config = self.settlement_configs.get(settlement_type)
        if not config:
            return Decimal('0.00')
        
        return amount * (config['fees_percentage'] / 100)
    
    async def _reserve_funds(self, account_id: str, amount: Decimal, currency: str):
        """حجز الأموال"""
        success = await self.update_account_balance(account_id, amount, currency, "reserve")
        if not success:
            raise ValueError("Failed to reserve funds")
    
    async def _release_reserved_funds(self, account_id: str, amount: Decimal, currency: str):
        """إلغاء حجز الأموال"""
        await self.update_account_balance(account_id, amount, currency, "release_reserve")
    
    async def _store_settlement_record(
        self, 
        settlement_id: str, 
        request: SettlementRequest, 
        fees: Decimal, 
        net_amount: Decimal
    ):
        """حفظ سجل التسوية"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO settlements (
                        id, account_id, amount, currency, settlement_type, status,
                        fees_amount, net_amount, description, metadata, scheduled_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                """
                
                await conn.execute(
                    query,
                    settlement_id,
                    request.account_id,
                    request.amount,
                    request.currency,
                    request.settlement_type.value,
                    SettlementStatus.PENDING.value,
                    fees,
                    net_amount,
                    request.description,
                    json.dumps(request.metadata or {}),
                    request.scheduled_at
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store settlement record: {e}")
            raise
    
    async def _process_instant_settlement(
        self, 
        settlement_id: str, 
        request: SettlementRequest, 
        net_amount: Decimal
    ) -> SettlementResponse:
        """معالجة التسوية الفورية"""
        try:
            # Simulate bank transfer
            bank_reference = await self._execute_bank_transfer(request.account_id, net_amount, request.currency)
            
            # Update settlement status
            await self._update_settlement_status(settlement_id, SettlementStatus.COMPLETED, bank_reference)
            
            # Settle reserved funds
            await self.update_account_balance(request.account_id, request.amount, request.currency, "settle_reserve")
            
            return SettlementResponse(
                settlement_id=settlement_id,
                status=SettlementStatus.COMPLETED,
                account_id=request.account_id,
                amount=request.amount,
                currency=request.currency,
                fees_amount=request.amount - net_amount,
                net_amount=net_amount,
                bank_reference=bank_reference,
                created_at=datetime.now(),
                completed_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"❌ Instant settlement failed: {e}")
            await self._update_settlement_status(settlement_id, SettlementStatus.FAILED, error_message=str(e))
            
            return SettlementResponse(
                settlement_id=settlement_id,
                status=SettlementStatus.FAILED,
                account_id=request.account_id,
                amount=request.amount,
                currency=request.currency,
                fees_amount=Decimal('0.00'),
                net_amount=net_amount,
                error_message=str(e),
                created_at=datetime.now()
            )
    
    async def _schedule_settlement(
        self, 
        settlement_id: str, 
        request: SettlementRequest, 
        net_amount: Decimal
    ) -> SettlementResponse:
        """جدولة التسوية"""
        # Calculate scheduled time based on settlement type
        config = self.settlement_configs.get(request.settlement_type)
        scheduled_at = request.scheduled_at or self._calculate_next_settlement_time(request.settlement_type)
        
        # Update scheduled time
        await self._update_settlement_schedule(settlement_id, scheduled_at)
        
        return SettlementResponse(
            settlement_id=settlement_id,
            status=SettlementStatus.PENDING,
            account_id=request.account_id,
            amount=request.amount,
            currency=request.currency,
            fees_amount=request.amount - net_amount,
            net_amount=net_amount,
            created_at=datetime.now()
        )
    
    def _calculate_next_settlement_time(self, settlement_type: SettlementType) -> datetime:
        """حساب وقت التسوية التالي"""
        now = datetime.now()
        config = self.settlement_configs.get(settlement_type)
        
        if settlement_type == SettlementType.DAILY:
            # Next day at configured time
            next_day = now + timedelta(days=1)
            return next_day.replace(hour=23, minute=0, second=0, microsecond=0)
        
        elif settlement_type == SettlementType.WEEKLY:
            # Next Friday at configured time
            days_until_friday = (4 - now.weekday()) % 7
            if days_until_friday == 0 and now.hour >= 23:
                days_until_friday = 7
            next_friday = now + timedelta(days=days_until_friday)
            return next_friday.replace(hour=23, minute=0, second=0, microsecond=0)
        
        elif settlement_type == SettlementType.MONTHLY:
            # Last day of current month
            if now.month == 12:
                next_month = now.replace(year=now.year + 1, month=1, day=1)
            else:
                next_month = now.replace(month=now.month + 1, day=1)
            last_day = next_month - timedelta(days=1)
            return last_day.replace(hour=23, minute=0, second=0, microsecond=0)
        
        return now + timedelta(hours=1)  # Default
    
    async def _execute_bank_transfer(self, account_id: str, amount: Decimal, currency: str) -> str:
        """تنفيذ التحويل البنكي"""
        # Simulate bank API integration
        await asyncio.sleep(0.2)  # Simulate API call delay
        
        # Generate bank reference
        bank_reference = f"BNK{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:8].upper()}"
        
        logger.info(f"🏦 Bank transfer executed: {amount} {currency} - Reference: {bank_reference}")
        return bank_reference
    
    async def _get_ready_settlements(self) -> List[Dict[str, Any]]:
        """الحصول على التسويات الجاهزة للمعالجة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM settlements 
                    WHERE status = 'pending' 
                    AND (scheduled_at IS NULL OR scheduled_at <= CURRENT_TIMESTAMP)
                    ORDER BY created_at
                """
                
                rows = await conn.fetch(query)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Failed to get ready settlements: {e}")
            return []
    
    async def _execute_settlement(self, settlement: Dict[str, Any]) -> SettlementResponse:
        """تنفيذ التسوية"""
        try:
            # Execute bank transfer
            bank_reference = await self._execute_bank_transfer(
                settlement['account_id'], 
                settlement['net_amount'], 
                settlement['currency']
            )
            
            # Update settlement status
            await self._update_settlement_status(
                settlement['id'], 
                SettlementStatus.COMPLETED, 
                bank_reference
            )
            
            # Settle reserved funds
            await self.update_account_balance(
                settlement['account_id'], 
                settlement['amount'], 
                settlement['currency'], 
                "settle_reserve"
            )
            
            return SettlementResponse(
                settlement_id=settlement['id'],
                status=SettlementStatus.COMPLETED,
                account_id=settlement['account_id'],
                amount=settlement['amount'],
                currency=settlement['currency'],
                fees_amount=settlement['fees_amount'],
                net_amount=settlement['net_amount'],
                bank_reference=bank_reference,
                completed_at=datetime.now()
            )
            
        except Exception as e:
            await self._update_settlement_status(settlement['id'], SettlementStatus.FAILED, error_message=str(e))
            
            return SettlementResponse(
                settlement_id=settlement['id'],
                status=SettlementStatus.FAILED,
                account_id=settlement['account_id'],
                amount=settlement['amount'],
                currency=settlement['currency'],
                fees_amount=settlement['fees_amount'],
                net_amount=settlement['net_amount'],
                error_message=str(e)
            )
    
    async def _update_settlement_status(
        self, 
        settlement_id: str, 
        status: SettlementStatus, 
        bank_reference: str = None,
        error_message: str = None
    ):
        """تحديث حالة التسوية"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE settlements 
                    SET status = $1, 
                        bank_reference = $2,
                        error_message = $3,
                        completed_at = CASE WHEN $1 = 'completed' THEN CURRENT_TIMESTAMP ELSE completed_at END,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = $4
                """
                
                await conn.execute(query, status.value, bank_reference, error_message, settlement_id)
                
        except Exception as e:
            logger.error(f"❌ Failed to update settlement status: {e}")
    
    async def _update_settlement_schedule(self, settlement_id: str, scheduled_at: datetime):
        """تحديث جدولة التسوية"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE settlements 
                    SET scheduled_at = $1, updated_at = CURRENT_TIMESTAMP
                    WHERE id = $2
                """
                
                await conn.execute(query, scheduled_at, settlement_id)
                
        except Exception as e:
            logger.error(f"❌ Failed to update settlement schedule: {e}")
    
    async def _create_balance_record(self, account_id: str, currency: str):
        """إنشاء سجل رصيد جديد"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO account_balances (
                        account_id, currency, available_balance, pending_balance, reserved_balance
                    ) VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (account_id, currency) DO NOTHING
                """
                
                await conn.execute(
                    query,
                    account_id,
                    currency,
                    Decimal('0.00'),
                    Decimal('0.00'),
                    Decimal('0.00')
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to create balance record: {e}")
    
    async def _log_balance_change(
        self, 
        conn, 
        account_id: str, 
        currency: str, 
        amount: Decimal, 
        transaction_type: str,
        old_balance: Decimal, 
        new_balance: Decimal, 
        reference_id: str = None
    ):
        """تسجيل تغيير الرصيد"""
        try:
            query = """
                INSERT INTO balance_changes (
                    account_id, currency, amount, transaction_type,
                    old_balance, new_balance, reference_id
                ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            """
            
            await conn.execute(
                query,
                account_id,
                currency,
                amount,
                transaction_type,
                old_balance,
                new_balance,
                reference_id
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to log balance change: {e}")
    
    async def _get_daily_settlement_total(self, account_id: str) -> Decimal:
        """الحصول على إجمالي التسويات اليومية"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT COALESCE(SUM(amount), 0) as total
                    FROM settlements 
                    WHERE account_id = $1 
                    AND DATE(created_at) = CURRENT_DATE
                    AND status IN ('completed', 'processing', 'pending')
                """
                
                row = await conn.fetchrow(query, account_id)
                return row['total'] if row else Decimal('0.00')
                
        except Exception as e:
            logger.error(f"❌ Failed to get daily settlement total: {e}")
            return Decimal('0.00')
    
    async def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمة"""
        return {
            "settlements_processed": self.settlements_processed,
            "settlements_failed": self.settlements_failed,
            "total_settled_amount": float(self.total_settled_amount),
            "success_rate": (self.settlements_processed / max(self.settlements_processed + self.settlements_failed, 1)) * 100,
            "supported_types": len(self.settlement_configs),
            "active_risk_limits": len(self.risk_limits)
        }
