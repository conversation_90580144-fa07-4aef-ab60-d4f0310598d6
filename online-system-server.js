/**
 * WS Transfir Online System Server
 * خادم نظام WS Transfir الأونلاين المتكامل
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const fs = require('fs');
const path = require('path');

const app = express();

// Configuration
const config = {
  port: process.env.PORT || 3000,
  frontendPort: process.env.FRONTEND_PORT || 3100,
  environment: process.env.NODE_ENV || 'production',
  host: '0.0.0.0', // للوصول الخارجي
};

console.log('🚀 تشغيل نظام WS Transfir الأونلاين');
console.log('===================================');
console.log(`🌐 البيئة: ${config.environment}`);
console.log(`🔧 منفذ API: ${config.port}`);
console.log(`🎨 منفذ Frontend: ${config.frontendPort}`);
console.log('');

// Enhanced Security Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "https:", "wss:", "ws:"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdn.jsdelivr.net"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// Performance Middleware
app.use(compression({
  level: 6,
  threshold: 1024,
  filter: (req, res) => {
    if (req.headers['x-no-compression']) return false;
    return compression.filter(req, res);
  }
}));

// Logging
app.use(morgan('combined', {
  stream: {
    write: (message) => {
      console.log(`[${new Date().toISOString()}] ${message.trim()}`);
    }
  }
}));

// Enhanced Rate Limiting
const createRateLimit = (windowMs, max, message) => rateLimit({
  windowMs,
  max,
  message: { error: message, retryAfter: Math.ceil(windowMs / 1000) },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      error: 'TOO_MANY_REQUESTS',
      message: message,
      retryAfter: Math.ceil(windowMs / 1000),
      timestamp: new Date().toISOString()
    });
  }
});

// Different rate limits for different endpoints
app.use('/api/auth', createRateLimit(15 * 60 * 1000, 10, 'Too many authentication attempts'));
app.use('/api/transfers', createRateLimit(15 * 60 * 1000, 50, 'Too many transfer requests'));
app.use('/api/', createRateLimit(15 * 60 * 1000, 100, 'Too many API requests'));

// CORS Configuration for online access
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    // Allow all origins in development
    if (config.environment === 'development') {
      return callback(null, true);
    }
    
    // In production, you can specify allowed origins
    const allowedOrigins = [
      'http://localhost:3100',
      'http://localhost:3000',
      'https://wstransfir.com',
      'https://app.wstransfir.com',
      // Add your domain here
    ];
    
    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    // For now, allow all origins (remove in production)
    return callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'X-API-Key',
    'X-Client-Version'
  ],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request tracking middleware
app.use((req, res, next) => {
  req.requestId = require('crypto').randomUUID();
  req.startTime = Date.now();
  
  res.setHeader('X-Request-ID', req.requestId);
  res.setHeader('X-Powered-By', 'WS-Transfir-v1.0');
  
  // Log request
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} - ${req.ip} - ID: ${req.requestId}`);
  
  // Response time tracking
  res.on('finish', () => {
    const duration = Date.now() - req.startTime;
    console.log(`[${new Date().toISOString()}] Response ${req.requestId} - ${res.statusCode} - ${duration}ms`);
  });
  
  next();
});

// Health check endpoint with comprehensive system info
app.get('/api/health', (req, res) => {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  res.json({
    status: 'OK',
    message: 'WS Transfir Online System is running successfully',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: config.environment,
    server: {
      uptime: {
        seconds: Math.floor(uptime),
        human: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
      },
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
        usage: `${Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)}%`,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
      },
    },
    services: {
      api: 'healthy',
      database: 'simulated',
      cache: 'simulated',
      notifications: 'simulated',
      payments: 'simulated',
      analytics: 'simulated',
    },
    endpoints: {
      health: '/api/health',
      auth: '/api/auth/*',
      users: '/api/users/*',
      profile: '/api/profile/*',
      transfers: '/api/transfers/*',
      wallets: '/api/wallets/*',
      notifications: '/api/notifications/*',
      analytics: '/api/analytics/*',
    },
    features: {
      authentication: 'enabled',
      twoFactorAuth: 'enabled',
      encryption: 'enabled',
      rateLimit: 'enabled',
      monitoring: 'enabled',
      analytics: 'enabled',
      notifications: 'enabled',
    },
    requestId: req.requestId,
  });
});

// System status endpoint
app.get('/api/status', (req, res) => {
  res.json({
    online: true,
    status: 'operational',
    services: {
      api: { status: 'up', responseTime: '< 100ms' },
      database: { status: 'simulated', responseTime: '< 50ms' },
      cache: { status: 'simulated', responseTime: '< 10ms' },
      notifications: { status: 'simulated', responseTime: '< 200ms' },
    },
    lastUpdated: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Enhanced Authentication endpoints
const users = new Map([
  ['<EMAIL>', {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123', // In real app, this would be hashed
    firstName: 'مدير',
    lastName: 'النظام',
    role: 'admin',
    isVerified: true,
    avatar: null,
    permissions: ['read:all', 'write:all', 'delete:all', 'admin:panel'],
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: null,
  }],
  ['<EMAIL>', {
    id: '2',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'أحمد',
    lastName: 'محمد',
    role: 'user',
    isVerified: true,
    avatar: null,
    permissions: ['read:own', 'write:own', 'transfer:create'],
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: null,
  }]
]);

// Input validation middleware
const validateLoginInput = (req, res, next) => {
  const { email, password } = req.body;
  const errors = [];
  
  if (!email) {
    errors.push('البريد الإلكتروني مطلوب');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('البريد الإلكتروني غير صحيح');
  }
  
  if (!password) {
    errors.push('كلمة المرور مطلوبة');
  } else if (password.length < 6) {
    errors.push('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
  }
  
  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'بيانات غير صحيحة',
      errors,
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    });
  }
  
  next();
};

// Authentication endpoint
app.post('/api/auth/login', validateLoginInput, (req, res) => {
  const { email, password } = req.body;
  const timestamp = new Date().toISOString();
  
  console.log(`[${timestamp}] Login attempt: ${email} (Request ID: ${req.requestId})`);
  
  // Simulate authentication delay
  setTimeout(() => {
    const user = users.get(email);
    
    if (user && user.password === password) {
      const token = `jwt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const refreshToken = `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Update last login
      user.lastLogin = timestamp;
      
      res.json({
        success: true,
        message: 'تم تسجيل الدخول بنجاح',
        token,
        refreshToken,
        expiresIn: 3600, // 1 hour
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role,
          isVerified: user.isVerified,
          avatar: user.avatar,
          lastLogin: timestamp,
          permissions: user.permissions,
        },
        timestamp,
        requestId: req.requestId,
      });
    } else {
      res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة',
        error: 'INVALID_CREDENTIALS',
        timestamp,
        requestId: req.requestId,
      });
    }
  }, 300); // Reduced delay for online experience
});

// Registration endpoint
app.post('/api/auth/register', (req, res) => {
  const { firstName, lastName, email, password, phone } = req.body;
  const timestamp = new Date().toISOString();
  
  // Check if user already exists
  if (users.has(email)) {
    return res.status(409).json({
      success: false,
      message: 'المستخدم موجود بالفعل',
      error: 'USER_EXISTS',
      timestamp,
      requestId: req.requestId,
    });
  }
  
  // Create new user
  const newUser = {
    id: Date.now().toString(),
    firstName,
    lastName,
    email,
    password, // In real app, hash this
    phone,
    role: 'user',
    isVerified: false,
    avatar: null,
    permissions: ['read:own', 'write:own', 'transfer:create'],
    createdAt: timestamp,
    lastLogin: null,
  };
  
  users.set(email, newUser);
  
  res.status(201).json({
    success: true,
    message: 'تم إنشاء الحساب بنجاح',
    user: {
      id: newUser.id,
      firstName: newUser.firstName,
      lastName: newUser.lastName,
      email: newUser.email,
      phone: newUser.phone,
      isVerified: newUser.isVerified,
    },
    timestamp,
    requestId: req.requestId,
  });
});

// Profile endpoints
app.get('/api/profile/me', (req, res) => {
  // In a real app, you'd verify the JWT token here
  const user = users.get('<EMAIL>'); // Mock user
  
  res.json({
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    phone: '+966501234567',
    dateOfBirth: '1990-01-01',
    nationality: 'SA',
    isVerified: user.isVerified,
    avatar: user.avatar,
    completionPercentage: 85,
    address: {
      addressLine1: 'شارع الملك فهد',
      city: 'الرياض',
      country: 'SA',
      postalCode: '12345'
    },
    emergencyContact: {
      name: 'فاطمة محمد',
      phone: '+966501234568',
      relation: 'زوجة'
    },
    preferences: {
      language: 'ar',
      currency: 'SAR',
      notifications: {
        email: true,
        sms: true,
        push: true
      }
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Transfers endpoints
app.get('/api/transfers', (req, res) => {
  const { page = 1, limit = 10, status, search } = req.query;
  
  const mockTransfers = [
    {
      id: '1',
      referenceNumber: 'WS20241225001',
      amount: '1,500.00',
      currency: 'SAR',
      receiverName: 'أحمد محمد',
      receiverCountry: 'مصر',
      receiverCity: 'القاهرة',
      status: 'completed',
      createdAt: '2024-12-25T10:30:00Z',
      completedAt: '2024-12-25T14:30:00Z',
      estimatedDelivery: '2024-12-25T14:30:00Z',
      fees: '15.00',
      exchangeRate: '1.00',
      paymentMethod: 'bank_transfer',
      purpose: 'family_support'
    },
    {
      id: '2',
      referenceNumber: 'WS20241224002',
      amount: '750.00',
      currency: 'USD',
      receiverName: 'فاطمة علي',
      receiverCountry: 'الأردن',
      receiverCity: 'عمان',
      status: 'pending',
      createdAt: '2024-12-24T15:45:00Z',
      completedAt: null,
      estimatedDelivery: '2024-12-26T15:45:00Z',
      fees: '12.50',
      exchangeRate: '3.75',
      paymentMethod: 'credit_card',
      purpose: 'education'
    },
    {
      id: '3',
      referenceNumber: 'WS20241223003',
      amount: '2,200.00',
      currency: 'SAR',
      receiverName: 'محمد حسن',
      receiverCountry: 'لبنان',
      receiverCity: 'بيروت',
      status: 'processing',
      createdAt: '2024-12-23T09:15:00Z',
      completedAt: null,
      estimatedDelivery: '2024-12-25T09:15:00Z',
      fees: '22.00',
      exchangeRate: '1.00',
      paymentMethod: 'wallet',
      purpose: 'business'
    }
  ];
  
  let filteredTransfers = mockTransfers;
  
  if (status) {
    filteredTransfers = filteredTransfers.filter(t => t.status === status);
  }
  
  if (search) {
    filteredTransfers = filteredTransfers.filter(t => 
      t.referenceNumber.includes(search) || 
      t.receiverName.includes(search)
    );
  }
  
  res.json({
    success: true,
    data: filteredTransfers,
    pagination: {
      total: filteredTransfers.length,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(filteredTransfers.length / parseInt(limit))
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Transfer statistics
app.get('/api/transfers/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalTransfers: 24,
      totalAmount: '45,230.50',
      totalFees: '1,205.75',
      pendingTransfers: 3,
      completedTransfers: 21,
      thisMonth: {
        transfers: 8,
        amount: '12,450.00',
        fees: '324.50'
      },
      topCountries: [
        { country: 'مصر', count: 8, amount: '15,230.00' },
        { country: 'الأردن', count: 6, amount: '12,450.00' },
        { country: 'لبنان', count: 4, amount: '8,750.00' },
        { country: 'المغرب', count: 3, amount: '5,200.00' },
        { country: 'تونس', count: 3, amount: '3,600.00' }
      ]
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  const timestamp = new Date().toISOString();
  const requestId = req.requestId || 'unknown';
  
  console.error(`[${timestamp}] Error ${requestId}:`, err);
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'حدث خطأ في الخادم',
    error: config.environment === 'development' ? err.stack : 'INTERNAL_SERVER_ERROR',
    timestamp,
    requestId,
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود',
    error: 'NOT_FOUND',
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Start server
const server = app.listen(config.port, config.host, () => {
  console.log('');
  console.log('🌐 ═══════════════════════════════════════════════════════════');
  console.log('🌐 WS TRANSFIR ONLINE SYSTEM - FULLY OPERATIONAL');
  console.log('🌐 ═══════════════════════════════════════════════════════════');
  console.log(`🚀 Server running on: http://${config.host}:${config.port}`);
  console.log(`📊 Health check: http://${config.host}:${config.port}/api/health`);
  console.log(`🔐 Admin login: <EMAIL> / admin123`);
  console.log(`👤 User login: <EMAIL> / password123`);
  console.log(`🌍 Environment: ${config.environment}`);
  console.log(`📡 External access: Enabled`);
  console.log('🌐 ═══════════════════════════════════════════════════════════');
  console.log('');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

module.exports = app;
