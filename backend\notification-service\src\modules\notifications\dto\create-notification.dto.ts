import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsUUID,
  IsEnum,
  IsOptional,
  IsObject,
  IsDateString,
  IsEmail,
  IsPhoneNumber,
  IsNumber,
  IsBoolean,
  Length,
  Min,
  Max,
  ValidateIf,
} from 'class-validator';
import { 
  NotificationType, 
  NotificationPriority 
} from '../entities/notification.entity';

export class CreateNotificationDto {
  @ApiProperty({
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @IsUUID(4, { message: 'معرف المستخدم يجب أن يكون UUID صالح' })
  userId: string;

  @ApiProperty({
    description: 'نوع الإشعار',
    enum: NotificationType,
    example: NotificationType.EMAIL,
  })
  @IsEnum(NotificationType, { message: 'نوع الإشعار غير صالح' })
  type: NotificationType;

  @ApiProperty({
    description: 'أولوية الإشعار',
    enum: NotificationPriority,
    example: NotificationPriority.NORMAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(NotificationPriority, { message: 'أولوية الإشعار غير صالحة' })
  priority?: NotificationPriority = NotificationPriority.NORMAL;

  @ApiProperty({
    description: 'عنوان الإشعار',
    example: 'تم إتمام التحويل بنجاح',
    minLength: 1,
    maxLength: 500,
  })
  @IsString({ message: 'العنوان يجب أن يكون نص' })
  @Length(1, 500, { message: 'العنوان يجب أن يكون بين 1 و 500 حرف' })
  title: string;

  @ApiProperty({
    description: 'محتوى الإشعار',
    example: 'تم إتمام تحويلك بمبلغ 1000 ريال سعودي بنجاح',
  })
  @IsString({ message: 'المحتوى يجب أن يكون نص' })
  @Length(1, 5000, { message: 'المحتوى يجب أن يكون بين 1 و 5000 حرف' })
  content: string;

  @ApiProperty({
    description: 'معرف القالب المستخدم',
    example: 'template-uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'معرف القالب يجب أن يكون UUID صالح' })
  templateId?: string;

  @ApiProperty({
    description: 'قناة الإرسال',
    example: 'email_primary',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'قناة الإرسال يجب أن تكون نص' })
  @Length(1, 100, { message: 'قناة الإرسال يجب أن تكون بين 1 و 100 حرف' })
  channel?: string;

  @ApiProperty({
    description: 'المستقبل (email للبريد الإلكتروني، phone للرسائل النصية)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'المستقبل يجب أن يكون نص' })
  @ValidateIf((o) => o.type === NotificationType.EMAIL)
  @IsEmail({}, { message: 'البريد الإلكتروني غير صالح' })
  @ValidateIf((o) => o.type === NotificationType.SMS)
  @IsPhoneNumber(null, { message: 'رقم الهاتف غير صالح' })
  recipient?: string;

  @ApiProperty({
    description: 'بيانات إضافية للإشعار',
    example: { 
      transferId: 'transfer-123', 
      amount: 1000, 
      currency: 'SAR' 
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'البيانات الإضافية يجب أن تكون كائن' })
  data?: Record<string, any>;

  @ApiProperty({
    description: 'بيانات وصفية',
    example: { 
      source: 'transfer_service', 
      category: 'transaction' 
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'البيانات الوصفية يجب أن تكون كائن' })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'تاريخ الإرسال المجدول (ISO 8601)',
    example: '2024-12-25T10:00:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ الإرسال المجدول غير صالح' })
  scheduledAt?: string;

  @ApiProperty({
    description: 'الحد الأقصى لمحاولات الإرسال',
    example: 3,
    minimum: 1,
    maximum: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'الحد الأقصى للمحاولات يجب أن يكون رقم' })
  @Min(1, { message: 'الحد الأقصى للمحاولات يجب أن يكون على الأقل 1' })
  @Max(10, { message: 'الحد الأقصى للمحاولات يجب أن يكون أقل من 10' })
  maxAttempts?: number = 3;

  @ApiProperty({
    description: 'تاريخ انتهاء صلاحية الإشعار (ISO 8601)',
    example: '2024-12-26T10:00:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ انتهاء الصلاحية غير صالح' })
  expiresAt?: string;

  @ApiProperty({
    description: 'إرسال فوري (تجاهل الجدولة)',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'الإرسال الفوري يجب أن يكون قيمة منطقية' })
  sendImmediately?: boolean = false;

  @ApiProperty({
    description: 'تجميع الإشعارات المتشابهة',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تجميع الإشعارات يجب أن يكون قيمة منطقية' })
  allowBatching?: boolean = true;

  @ApiProperty({
    description: 'تتبع القراءة والتفاعل',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تتبع القراءة يجب أن يكون قيمة منطقية' })
  trackEngagement?: boolean = true;
}
