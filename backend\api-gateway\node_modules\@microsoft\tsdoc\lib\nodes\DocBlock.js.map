{"version": 3, "file": "DocBlock.js", "sourceRoot": "", "sources": ["../../src/nodes/DocBlock.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAE,OAAO,EAA0D,MAAM,WAAW,CAAC;AACzG,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAiB1C;;;GAGG;AACH;IAA8B,4BAAO;IAInC;;;OAGG;IACH,kBAAmB,UAA2D;QAC5E,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAClB,KAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;QACrC,KAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAAC,EAAE,aAAa,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;;IACxE,CAAC;IAGD,sBAAW,0BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,KAAK,CAAC;QAC3B,CAAC;;;OAAA;IAKD,sBAAW,8BAAQ;QAHnB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAKD,sBAAW,6BAAO;QAHlB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;;;OAAA;IAED,gBAAgB;IACN,kCAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IACH,eAAC;AAAD,CAAC,AArCD,CAA8B,OAAO,GAqCpC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport { DocSection } from './DocSection';\r\nimport type { DocBlockTag } from './DocBlockTag';\r\n\r\n/**\r\n * Constructor parameters for {@link DocBlock}.\r\n */\r\nexport interface IDocBlockParameters extends IDocNodeParameters {\r\n  blockTag: DocBlockTag;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocBlock}.\r\n */\r\nexport interface IDocBlockParsedParameters extends IDocNodeParsedParameters {\r\n  blockTag: DocBlockTag;\r\n}\r\n\r\n/**\r\n * Represents a section that is introduced by a TSDoc block tag.\r\n * For example, an `@example` block.\r\n */\r\nexport class DocBlock extends DocNode {\r\n  private readonly _blockTag: DocBlockTag;\r\n  private readonly _content: DocSection;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocBlockParameters | IDocBlockParsedParameters) {\r\n    super(parameters);\r\n    this._blockTag = parameters.blockTag;\r\n    this._content = new DocSection({ configuration: this.configuration });\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.Block;\r\n  }\r\n\r\n  /**\r\n   * The TSDoc tag that introduces this section.\r\n   */\r\n  public get blockTag(): DocBlockTag {\r\n    return this._blockTag;\r\n  }\r\n\r\n  /**\r\n   * The TSDoc tag that introduces this section.\r\n   */\r\n  public get content(): DocSection {\r\n    return this._content;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this.blockTag, this._content];\r\n  }\r\n}\r\n"]}