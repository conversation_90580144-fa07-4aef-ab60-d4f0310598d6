'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { toast } from 'react-hot-toast';

// أنواع الإشعارات
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'transfer' | 'security';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  isImportant: boolean;
  actionUrl?: string;
  actionText?: string;
  metadata?: Record<string, any>;
}

// حالة الإشعارات
export interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  settings: NotificationSettings;
}

// إعدادات الإشعارات
export interface NotificationSettings {
  pushEnabled: boolean;
  emailEnabled: boolean;
  smsEnabled: boolean;
  transferNotifications: boolean;
  securityNotifications: boolean;
  marketingNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

// أنواع الإجراءات
type NotificationAction =
  | { type: 'LOAD_START' }
  | { type: 'LOAD_SUCCESS'; payload: Notification[] }
  | { type: 'LOAD_ERROR'; payload: string }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'MARK_AS_READ'; payload: string }
  | { type: 'MARK_ALL_AS_READ' }
  | { type: 'DELETE_NOTIFICATION'; payload: string }
  | { type: 'CLEAR_ALL_NOTIFICATIONS' }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<NotificationSettings> };

// الحالة الأولية
const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  settings: {
    pushEnabled: true,
    emailEnabled: true,
    smsEnabled: true,
    transferNotifications: true,
    securityNotifications: true,
    marketingNotifications: false,
    soundEnabled: true,
    vibrationEnabled: true,
  },
};

// مخفض الحالة
function notificationReducer(state: NotificationState, action: NotificationAction): NotificationState {
  switch (action.type) {
    case 'LOAD_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'LOAD_SUCCESS':
      return {
        ...state,
        notifications: action.payload,
        unreadCount: action.payload.filter(n => !n.isRead).length,
        isLoading: false,
        error: null,
      };

    case 'LOAD_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };

    case 'ADD_NOTIFICATION':
      const newNotifications = [action.payload, ...state.notifications];
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: newNotifications.filter(n => !n.isRead).length,
      };

    case 'MARK_AS_READ':
      const updatedNotifications = state.notifications.map(n =>
        n.id === action.payload ? { ...n, isRead: true } : n
      );
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.isRead).length,
      };

    case 'MARK_ALL_AS_READ':
      const allReadNotifications = state.notifications.map(n => ({ ...n, isRead: true }));
      return {
        ...state,
        notifications: allReadNotifications,
        unreadCount: 0,
      };

    case 'DELETE_NOTIFICATION':
      const filteredNotifications = state.notifications.filter(n => n.id !== action.payload);
      return {
        ...state,
        notifications: filteredNotifications,
        unreadCount: filteredNotifications.filter(n => !n.isRead).length,
      };

    case 'CLEAR_ALL_NOTIFICATIONS':
      return {
        ...state,
        notifications: [],
        unreadCount: 0,
      };

    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    default:
      return state;
  }
}

// سياق الإشعارات
const NotificationContext = createContext<{
  state: NotificationState;
  loadNotifications: () => Promise<void>;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  clearAllNotifications: () => Promise<void>;
  updateSettings: (settings: Partial<NotificationSettings>) => Promise<void>;
  showToast: (type: 'success' | 'error' | 'info' | 'warning', message: string) => void;
  requestPermission: () => Promise<boolean>;
} | null>(null);

// مزود السياق
export function NotificationProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(notificationReducer, initialState);

  // تحميل الإشعارات عند بدء التطبيق
  useEffect(() => {
    loadNotifications();
    loadSettings();
  }, []);

  // طلب إذن الإشعارات
  useEffect(() => {
    if ('Notification' in window && state.settings.pushEnabled) {
      requestPermission();
    }
  }, [state.settings.pushEnabled]);

  // تحميل الإشعارات
  const loadNotifications = async () => {
    try {
      dispatch({ type: 'LOAD_START' });
      
      // محاكاة تحميل الإشعارات من API
      const mockNotifications: Notification[] = [
        {
          id: '1',
          type: 'transfer',
          title: 'تحويل مكتمل',
          message: 'تم إرسال حوالة بقيمة 500 ريال بنجاح',
          timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 دقيقة مضت
          isRead: false,
          isImportant: true,
          actionUrl: '/dashboard/transfers/123',
          actionText: 'عرض التفاصيل',
        },
        {
          id: '2',
          type: 'security',
          title: 'تسجيل دخول جديد',
          message: 'تم تسجيل الدخول من جهاز جديد',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // ساعتان مضتا
          isRead: false,
          isImportant: true,
        },
        {
          id: '3',
          type: 'info',
          title: 'تحديث النظام',
          message: 'تم تحديث النظام بميزات جديدة',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // يوم مضى
          isRead: true,
          isImportant: false,
        },
      ];

      dispatch({ type: 'LOAD_SUCCESS', payload: mockNotifications });
    } catch (error: any) {
      dispatch({ type: 'LOAD_ERROR', payload: error.message });
    }
  };

  // تحميل الإعدادات
  const loadSettings = () => {
    const savedSettings = localStorage.getItem('notificationSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        dispatch({ type: 'UPDATE_SETTINGS', payload: settings });
      } catch (error) {
        console.error('Error loading notification settings:', error);
      }
    }
  };

  // إضافة إشعار جديد
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
    };

    dispatch({ type: 'ADD_NOTIFICATION', payload: newNotification });

    // إظهار إشعار المتصفح إذا كان مفعلاً
    if (state.settings.pushEnabled && 'Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icon-192x192.png',
        badge: '/icon-72x72.png',
        tag: newNotification.id,
        requireInteraction: notification.isImportant,
      });
    }

    // تشغيل الصوت إذا كان مفعلاً
    if (state.settings.soundEnabled) {
      playNotificationSound(notification.type);
    }

    // الاهتزاز إذا كان مفعلاً
    if (state.settings.vibrationEnabled && 'vibrate' in navigator) {
      navigator.vibrate([200, 100, 200]);
    }
  };

  // تمييز كمقروء
  const markAsRead = async (id: string) => {
    try {
      // TODO: استدعاء API لتمييز الإشعار كمقروء
      dispatch({ type: 'MARK_AS_READ', payload: id });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // تمييز الكل كمقروء
  const markAllAsRead = async () => {
    try {
      // TODO: استدعاء API لتمييز جميع الإشعارات كمقروءة
      dispatch({ type: 'MARK_ALL_AS_READ' });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // حذف إشعار
  const deleteNotification = async (id: string) => {
    try {
      // TODO: استدعاء API لحذف الإشعار
      dispatch({ type: 'DELETE_NOTIFICATION', payload: id });
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // مسح جميع الإشعارات
  const clearAllNotifications = async () => {
    try {
      // TODO: استدعاء API لمسح جميع الإشعارات
      dispatch({ type: 'CLEAR_ALL_NOTIFICATIONS' });
    } catch (error) {
      console.error('Error clearing all notifications:', error);
    }
  };

  // تحديث الإعدادات
  const updateSettings = async (settings: Partial<NotificationSettings>) => {
    try {
      const newSettings = { ...state.settings, ...settings };
      
      // حفظ في التخزين المحلي
      localStorage.setItem('notificationSettings', JSON.stringify(newSettings));
      
      // TODO: إرسال إلى API
      dispatch({ type: 'UPDATE_SETTINGS', payload: settings });
    } catch (error) {
      console.error('Error updating notification settings:', error);
    }
  };

  // إظهار toast
  const showToast = (type: 'success' | 'error' | 'info' | 'warning', message: string) => {
    switch (type) {
      case 'success':
        toast.success(message);
        break;
      case 'error':
        toast.error(message);
        break;
      case 'warning':
        toast(message, { icon: '⚠️' });
        break;
      case 'info':
      default:
        toast(message, { icon: 'ℹ️' });
        break;
    }
  };

  // طلب إذن الإشعارات
  const requestPermission = async (): Promise<boolean> => {
    if (!('Notification' in window)) {
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  };

  // تشغيل صوت الإشعار
  const playNotificationSound = (type: Notification['type']) => {
    try {
      const audio = new Audio(`/sounds/notification-${type}.mp3`);
      audio.volume = 0.5;
      audio.play().catch(() => {
        // تجاهل الأخطاء إذا لم يتمكن من تشغيل الصوت
      });
    } catch (error) {
      // تجاهل الأخطاء
    }
  };

  const value = {
    state,
    loadNotifications,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    updateSettings,
    showToast,
    requestPermission,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

// خطاف استخدام السياق
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}
