import React from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  But<PERSON>,
  <PERSON>,
  Avatar,
  LinearProgress
} from '@mui/material';
import {
  AccountBalance,
  Send,
  Receipt,
  Security,
  Speed,
  Public,
  TrendingUp,
  CheckCircle
} from '@mui/icons-material';

const HomePage: NextPage = () => {
  const features = [
    {
      icon: <Send color="primary" />,
      title: 'تحويلات سريعة',
      description: 'أرسل الأموال في ثوانٍ إلى أي مكان في العالم'
    },
    {
      icon: <Security color="primary" />,
      title: 'أمان متقدم',
      description: 'حماية عالية المستوى مع تشفير من الدرجة المصرفية'
    },
    {
      icon: <AccountBalance color="primary" />,
      title: 'دعم متعدد البنوك',
      description: 'اتصال مباشر مع أكثر من 200 بنك حول العالم'
    },
    {
      icon: <Speed color="primary" />,
      title: 'معالجة فورية',
      description: 'معالجة المعاملات في الوقت الفعلي على مدار الساعة'
    }
  ];

  const stats = [
    { label: 'المستخدمون النشطون', value: '1.2M+', color: '#1976d2' },
    { label: 'التحويلات اليومية', value: '50K+', color: '#2e7d32' },
    { label: 'البلدان المدعومة', value: '180+', color: '#ed6c02' },
    { label: 'معدل الرضا', value: '99.8%', color: '#9c27b0' }
  ];

  return (
    <>
      <Head>
        <title>WS Transfir - نظام تحويل الأموال العالمي</title>
        <meta name="description" content="نظام تحويل الأموال العالمي المتقدم مع الذكاء الاصطناعي" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Container maxWidth="lg">
        {/* Hero Section */}
        <Box sx={{ py: 8, textAlign: 'center' }}>
          <Typography variant="h2" component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
            🏦 WS Transfir
          </Typography>
          <Typography variant="h4" component="h2" gutterBottom color="text.secondary" sx={{ mb: 4 }}>
            نظام تحويل الأموال العالمي المتقدم
          </Typography>
          <Typography variant="h6" component="p" sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}>
            أرسل واستقبل الأموال بسرعة وأمان إلى أي مكان في العالم مع أحدث تقنيات الذكاء الاصطناعي
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap', mb: 4 }}>
            <Button 
              variant="contained" 
              size="large" 
              startIcon={<Send />}
              sx={{ px: 4, py: 1.5 }}
            >
              ابدأ التحويل الآن
            </Button>
            <Button 
              variant="outlined" 
              size="large" 
              startIcon={<AccountBalance />}
              sx={{ px: 4, py: 1.5 }}
            >
              إنشاء حساب
            </Button>
          </Box>

          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Chip icon={<CheckCircle />} label="مجاني للتسجيل" color="success" />
            <Chip icon={<Security />} label="آمن 100%" color="primary" />
            <Chip icon={<Speed />} label="تحويل فوري" color="secondary" />
          </Box>
        </Box>

        {/* Stats Section */}
        <Box sx={{ py: 6 }}>
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom sx={{ mb: 4 }}>
            📊 إحصائيات النظام
          </Typography>
          <Grid container spacing={3}>
            {stats.map((stat, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <CardContent>
                    <Typography variant="h3" component="div" sx={{ color: stat.color, fontWeight: 'bold' }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Features Section */}
        <Box sx={{ py: 6 }}>
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom sx={{ mb: 4 }}>
            ✨ المميزات الرئيسية
          </Typography>
          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card sx={{ height: '100%', textAlign: 'center', p: 2 }}>
                  <CardContent>
                    <Avatar sx={{ mx: 'auto', mb: 2, bgcolor: 'primary.light' }}>
                      {feature.icon}
                    </Avatar>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Services Status */}
        <Box sx={{ py: 6 }}>
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom sx={{ mb: 4 }}>
            🔧 حالة الخدمات
          </Typography>
          <Grid container spacing={3}>
            {[
              { name: 'API Gateway', status: 'running', port: 3000 },
              { name: 'خدمة المصادقة', status: 'running', port: 3001 },
              { name: 'خدمة المستخدمين', status: 'running', port: 3002 },
              { name: 'خدمة التحويلات', status: 'running', port: 3003 },
              { name: 'خدمة المحافظ', status: 'running', port: 3004 },
              { name: 'محرك الذكاء الاصطناعي', status: 'running', port: 8000 }
            ].map((service, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Box 
                        sx={{ 
                          width: 12, 
                          height: 12, 
                          borderRadius: '50%', 
                          bgcolor: service.status === 'running' ? 'success.main' : 'error.main',
                          mr: 1 
                        }} 
                      />
                      <Typography variant="h6" component="h3">
                        {service.name}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      المنفذ: {service.port}
                    </Typography>
                    <Chip 
                      label={service.status === 'running' ? 'يعمل' : 'متوقف'} 
                      color={service.status === 'running' ? 'success' : 'error'}
                      size="small"
                    />
                    <LinearProgress 
                      variant="determinate" 
                      value={service.status === 'running' ? 100 : 0} 
                      sx={{ mt: 1 }}
                      color={service.status === 'running' ? 'success' : 'error'}
                    />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* API Links */}
        <Box sx={{ py: 6, textAlign: 'center' }}>
          <Typography variant="h4" component="h2" gutterBottom sx={{ mb: 4 }}>
            🔗 روابط مفيدة
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button 
              variant="outlined" 
              href="http://localhost:3000/docs" 
              target="_blank"
              startIcon={<Receipt />}
            >
              توثيق API
            </Button>
            <Button 
              variant="outlined" 
              href="http://localhost:3000/health" 
              target="_blank"
              startIcon={<TrendingUp />}
            >
              فحص الصحة
            </Button>
            <Button 
              variant="outlined" 
              href="http://localhost:8080" 
              target="_blank"
              startIcon={<Public />}
            >
              الخادم البسيط
            </Button>
          </Box>
        </Box>

        {/* Footer */}
        <Box sx={{ py: 4, textAlign: 'center', borderTop: 1, borderColor: 'divider' }}>
          <Typography variant="body2" color="text.secondary">
            © 2024 WS Transfir - جميع الحقوق محفوظة
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            نظام تحويل الأموال العالمي المتقدم مع الذكاء الاصطناعي
          </Typography>
        </Box>
      </Container>
    </>
  );
};

export default HomePage;
