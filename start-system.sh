#!/bin/bash

# WS Transfir System Startup Script
# نص تشغيل نظام WS Transfir

echo "🚀 بدء تشغيل نظام WS Transfir"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if Docker is installed and running
check_docker() {
    print_header "🔍 فحص Docker..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker غير مثبت. يرجى تثبيت Docker أولاً."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker غير يعمل. يرجى تشغيل Docker أولاً."
        exit 1
    fi
    
    print_status "Docker متاح ويعمل بشكل صحيح"
}

# Check if Docker Compose is installed
check_docker_compose() {
    print_header "🔍 فحص Docker Compose..."
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً."
        exit 1
    fi
    
    print_status "Docker Compose متاح"
}

# Create necessary directories
create_directories() {
    print_header "📁 إنشاء المجلدات المطلوبة..."
    
    directories=(
        "backend/logs"
        "backend/uploads"
        "backend/database/init"
        "backend/database/migrations"
        "backend/database/seeds"
        "backend/config"
        "nginx/logs"
        "nginx/ssl"
        "monitoring/prometheus"
        "monitoring/grafana/provisioning"
        "monitoring/grafana/dashboards"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "تم إنشاء المجلد: $dir"
        fi
    done
}

# Create environment files if they don't exist
create_env_files() {
    print_header "⚙️ إنشاء ملفات البيئة..."
    
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# WS Transfir Environment Configuration
NODE_ENV=development
LOG_LEVEL=debug

# Database Configuration
POSTGRES_USER=ws_user
POSTGRES_PASSWORD=ws_password
POSTGRES_DB=ws_transfir

# Redis Configuration
REDIS_PASSWORD=

# MongoDB Configuration
MONGO_INITDB_ROOT_USERNAME=ws_user
MONGO_INITDB_ROOT_PASSWORD=ws_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# External Services
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********

# Payment Gateways
STRIPE_SECRET_KEY=sk_test_your_stripe_key
PAYPAL_CLIENT_ID=your_paypal_client_id

# Monitoring
GRAFANA_ADMIN_PASSWORD=admin123
EOF
        print_status "تم إنشاء ملف .env"
    fi
}

# Create basic configuration files
create_config_files() {
    print_header "📝 إنشاء ملفات الإعداد..."
    
    # Redis configuration
    if [ ! -f "backend/config/redis.conf" ]; then
        cat > backend/config/redis.conf << EOF
# Redis Configuration for WS Transfir
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./
maxmemory 256mb
maxmemory-policy allkeys-lru
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
EOF
        print_status "تم إنشاء ملف إعداد Redis"
    fi
    
    # Prometheus configuration
    if [ ! -f "monitoring/prometheus/prometheus.yml" ]; then
        cat > monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:3000']
    metrics_path: '/metrics'

  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:3001']
    metrics_path: '/metrics'

  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:3002']
    metrics_path: '/metrics'

  - job_name: 'transfer-service'
    static_configs:
      - targets: ['transfer-service:3003']
    metrics_path: '/metrics'

  - job_name: 'wallet-service'
    static_configs:
      - targets: ['wallet-service:3004']
    metrics_path: '/metrics'

  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service:3005']
    metrics_path: '/metrics'

  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service:3006']
    metrics_path: '/metrics'
EOF
        print_status "تم إنشاء ملف إعداد Prometheus"
    fi
}

# Create basic Nginx configuration
create_nginx_config() {
    print_header "🌐 إنشاء إعداد Nginx..."
    
    if [ ! -f "nginx/nginx.conf" ]; then
        cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream api_backend {
        server api-gateway:3000;
    }
    
    upstream web_frontend {
        server web-app:3000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # Frontend
        location / {
            proxy_pass http://web_frontend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # API
        location /api/ {
            proxy_pass http://api_backend/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF
        print_status "تم إنشاء ملف إعداد Nginx"
    fi
}

# Pull latest images
pull_images() {
    print_header "📥 تحميل أحدث الصور..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose pull
    else
        docker compose pull
    fi
}

# Build and start services
start_services() {
    print_header "🏗️ بناء وتشغيل الخدمات..."
    
    print_status "بناء الصور المخصصة..."
    if command -v docker-compose &> /dev/null; then
        docker-compose build
    else
        docker compose build
    fi
    
    print_status "تشغيل الخدمات..."
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi
}

# Wait for services to be ready
wait_for_services() {
    print_header "⏳ انتظار جاهزية الخدمات..."
    
    services=(
        "postgres:5432"
        "redis:6379"
        "mongodb:27017"
        "elasticsearch:9200"
        "rabbitmq:5672"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        print_status "انتظار $name على المنفذ $port..."
        
        timeout=60
        while ! nc -z localhost "$port" 2>/dev/null; do
            sleep 2
            timeout=$((timeout - 2))
            if [ $timeout -le 0 ]; then
                print_warning "انتهت مهلة انتظار $name"
                break
            fi
        done
        
        if nc -z localhost "$port" 2>/dev/null; then
            print_status "$name جاهز"
        fi
    done
}

# Show service status
show_status() {
    print_header "📊 حالة الخدمات:"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
}

# Show access URLs
show_urls() {
    print_header "🌐 روابط الوصول:"
    
    echo ""
    echo -e "${GREEN}التطبيق الرئيسي:${NC}"
    echo "  🌐 الموقع الإلكتروني: http://localhost"
    echo "  🌐 التطبيق المباشر: http://localhost:3100"
    echo ""
    echo -e "${GREEN}واجهات الإدارة:${NC}"
    echo "  📊 Grafana (المراقبة): http://localhost:3007 (admin/admin123)"
    echo "  🔍 Prometheus: http://localhost:9090"
    echo "  📈 Kibana (البحث): http://localhost:5601"
    echo "  🗄️  Adminer (قاعدة البيانات): http://localhost:8080"
    echo "  🔴 Redis Commander: http://localhost:8081"
    echo "  🐰 RabbitMQ Management: http://localhost:15672 (ws_user/ws_password)"
    echo "  🔍 Jaeger (التتبع): http://localhost:16686"
    echo ""
    echo -e "${GREEN}APIs الخدمات:${NC}"
    echo "  🚪 API Gateway: http://localhost:3000"
    echo "  🔐 Auth Service: http://localhost:3001"
    echo "  👤 User Service: http://localhost:3002"
    echo "  💸 Transfer Service: http://localhost:3003"
    echo "  💰 Wallet Service: http://localhost:3004"
    echo "  🔔 Notification Service: http://localhost:3005"
    echo "  📊 Analytics Service: http://localhost:3006"
    echo "  🤖 AI Engine: http://localhost:8000"
    echo ""
}

# Main execution
main() {
    print_header "🚀 تشغيل نظام WS Transfir"
    echo ""
    
    check_docker
    check_docker_compose
    create_directories
    create_env_files
    create_config_files
    create_nginx_config
    
    print_status "بدء تشغيل النظام..."
    pull_images
    start_services
    wait_for_services
    
    echo ""
    show_status
    echo ""
    show_urls
    
    print_header "✅ تم تشغيل النظام بنجاح!"
    print_status "يمكنك الآن الوصول للتطبيق على: http://localhost"
    print_warning "تأكد من تحديث متغيرات البيئة في ملف .env قبل الاستخدام في الإنتاج"
}

# Run main function
main "$@"
