import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
}

export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  WALLET = 'wallet',
  MADA = 'mada',
  APPLE_PAY = 'apple_pay',
  GOOGLE_PAY = 'google_pay',
  SADAD = 'sadad',
}

export enum PaymentGatewayType {
  STRIPE = 'stripe',
  PAYPAL = 'paypal',
  SADAD = 'sadad',
  MADA = 'mada',
  INTERNAL = 'internal',
}

@Entity('payments')
@Index(['userId', 'status'])
@Index(['gatewayType', 'status'])
@Index(['createdAt'])
@Index(['externalTransactionId'])
export class Payment {
  @ApiProperty({ description: 'معرف الدفعة' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'رقم مرجعي للدفعة' })
  @Column({ unique: true, length: 50 })
  @Index()
  referenceNumber: string;

  @ApiProperty({ description: 'معرف المستخدم' })
  @Column('uuid')
  @Index()
  userId: string;

  @ApiProperty({ description: 'معرف التحويل المرتبط', required: false })
  @Column('uuid', { nullable: true })
  transferId?: string;

  @ApiProperty({ description: 'حالة الدفعة', enum: PaymentStatus })
  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @ApiProperty({ description: 'طريقة الدفع', enum: PaymentMethod })
  @Column({
    type: 'enum',
    enum: PaymentMethod,
  })
  method: PaymentMethod;

  @ApiProperty({ description: 'نوع بوابة الدفع', enum: PaymentGatewayType })
  @Column({
    type: 'enum',
    enum: PaymentGatewayType,
  })
  gatewayType: PaymentGatewayType;

  @ApiProperty({ description: 'مبلغ الدفعة' })
  @Column('decimal', { precision: 15, scale: 2 })
  amount: string;

  @ApiProperty({ description: 'العملة' })
  @Column({ length: 3 })
  currency: string;

  @ApiProperty({ description: 'رسوم الدفعة' })
  @Column('decimal', { precision: 15, scale: 2, default: '0.00' })
  fees: string;

  @ApiProperty({ description: 'المبلغ الصافي بعد الرسوم' })
  @Column('decimal', { precision: 15, scale: 2 })
  netAmount: string;

  @ApiProperty({ description: 'وصف الدفعة' })
  @Column('text', { nullable: true })
  description?: string;

  @ApiProperty({ description: 'معرف المعاملة في البوابة الخارجية' })
  @Column({ length: 255, nullable: true })
  externalTransactionId?: string;

  @ApiProperty({ description: 'معرف الجلسة في البوابة الخارجية' })
  @Column({ length: 255, nullable: true })
  externalSessionId?: string;

  @ApiProperty({ description: 'تفاصيل طريقة الدفع' })
  @Column('jsonb', { nullable: true })
  paymentMethodDetails?: {
    cardLast4?: string;
    cardBrand?: string;
    cardExpiry?: string;
    bankName?: string;
    accountLast4?: string;
    walletProvider?: string;
  };

  @ApiProperty({ description: 'عنوان الفوترة' })
  @Column('jsonb', { nullable: true })
  billingAddress?: {
    name: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };

  @ApiProperty({ description: 'بيانات إضافية من البوابة' })
  @Column('jsonb', { nullable: true })
  gatewayResponse?: Record<string, any>;

  @ApiProperty({ description: 'بيانات وصفية' })
  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'رسالة الخطأ في حالة الفشل' })
  @Column('text', { nullable: true })
  errorMessage?: string;

  @ApiProperty({ description: 'كود الخطأ' })
  @Column({ length: 50, nullable: true })
  errorCode?: string;

  @ApiProperty({ description: 'عدد محاولات المعالجة' })
  @Column('int', { default: 0 })
  attempts: number;

  @ApiProperty({ description: 'تاريخ المعالجة' })
  @Column('timestamp', { nullable: true })
  processedAt?: Date;

  @ApiProperty({ description: 'تاريخ الإكمال' })
  @Column('timestamp', { nullable: true })
  completedAt?: Date;

  @ApiProperty({ description: 'تاريخ الإلغاء' })
  @Column('timestamp', { nullable: true })
  cancelledAt?: Date;

  @ApiProperty({ description: 'تاريخ انتهاء الصلاحية' })
  @Column('timestamp', { nullable: true })
  expiresAt?: Date;

  @ApiProperty({ description: 'عنوان IP للمستخدم' })
  @Column({ length: 45, nullable: true })
  ipAddress?: string;

  @ApiProperty({ description: 'معلومات المتصفح' })
  @Column('text', { nullable: true })
  userAgent?: string;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'تاريخ آخر تحديث' })
  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  markAsProcessing(): void {
    this.status = PaymentStatus.PROCESSING;
    this.processedAt = new Date();
  }

  markAsCompleted(externalTransactionId?: string): void {
    this.status = PaymentStatus.COMPLETED;
    this.completedAt = new Date();
    if (externalTransactionId) {
      this.externalTransactionId = externalTransactionId;
    }
  }

  markAsFailed(errorMessage: string, errorCode?: string): void {
    this.status = PaymentStatus.FAILED;
    this.errorMessage = errorMessage;
    this.errorCode = errorCode;
  }

  markAsCancelled(): void {
    this.status = PaymentStatus.CANCELLED;
    this.cancelledAt = new Date();
  }

  markAsRefunded(): void {
    this.status = PaymentStatus.REFUNDED;
  }

  canCancel(): boolean {
    return [PaymentStatus.PENDING, PaymentStatus.PROCESSING].includes(this.status);
  }

  canRefund(): boolean {
    return this.status === PaymentStatus.COMPLETED;
  }

  isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  incrementAttempts(): void {
    this.attempts++;
  }

  calculateNetAmount(): void {
    const amount = parseFloat(this.amount);
    const fees = parseFloat(this.fees);
    this.netAmount = (amount - fees).toFixed(2);
  }

  setGatewayResponse(response: Record<string, any>): void {
    this.gatewayResponse = {
      ...this.gatewayResponse,
      ...response,
      timestamp: new Date().toISOString(),
    };
  }
}
