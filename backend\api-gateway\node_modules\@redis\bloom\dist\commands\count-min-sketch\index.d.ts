import * as INCRBY from './INCRBY';
import * as INFO from './INFO';
import * as INITBYDIM from './INITBYDIM';
import * as INITBYPROB from './INITBYPROB';
import * as MERGE from './MERGE';
import * as QUERY from './QUERY';
declare const _default: {
    INCRBY: typeof INCRBY;
    incrBy: typeof INCRBY;
    INFO: typeof INFO;
    info: typeof INFO;
    INITBYDIM: typeof INITBYDIM;
    initByDim: typeof INITBYDIM;
    INITBYPROB: typeof INITBYPROB;
    initByProb: typeof INITBYPROB;
    MERGE: typeof MERGE;
    merge: typeof MERGE;
    QUERY: typeof QUERY;
    query: typeof QUERY;
};
export default _default;
