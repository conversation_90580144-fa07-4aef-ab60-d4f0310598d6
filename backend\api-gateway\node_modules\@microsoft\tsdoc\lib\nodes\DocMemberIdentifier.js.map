{"version": 3, "file": "DocMemberIdentifier.js", "sourceRoot": "", "sources": ["../../src/nodes/DocMemberIdentifier.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAE,OAAO,EAA0D,MAAM,WAAW,CAAC;AACzG,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEtD,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAoBvD;;GAEG;AACH;IAAyC,uCAAO;IAQ9C;;;OAGG;IACH,6BAAmB,UAAiF;QAClG,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,IAAI,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBAChC,KAAI,CAAC,iBAAiB,GAAG,IAAI,UAAU,CAAC;oBACtC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,0BAA0B;oBACnD,OAAO,EAAE,UAAU,CAAC,gBAAgB;iBACrC,CAAC,CAAC;YACL,CAAC;YAED,KAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC;gBACvC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,2BAA2B;gBACpD,OAAO,EAAE,UAAU,CAAC,iBAAiB;aACtC,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBACjC,KAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC;oBACvC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,2BAA2B;oBACpD,OAAO,EAAE,UAAU,CAAC,iBAAiB;iBACtC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC;QAC3C,CAAC;;IACH,CAAC;IAED;;;;;;;;OAQG;IACW,qCAAiB,GAA/B,UAAgC,UAAkB;QAChD,OAAO,CAAC,YAAY,CAAC,wCAAwC,CAAC,UAAU,CAAC,CAAC;IAC5E,CAAC;IAGD,sBAAW,qCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,gBAAgB,CAAC;QACtC,CAAC;;;OAAA;IASD,sBAAW,2CAAU;QAPrB;;;;;;WAMG;aACH;YACE,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAmB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACjE,CAAC;YACD,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;;;OAAA;IAOD,sBAAW,0CAAS;QALpB;;;;WAIG;aACH;YACE,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC5B,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;;;OAAA;IAED,gBAAgB;IACN,6CAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACpF,CAAC;IACH,0BAAC;AAAD,CAAC,AA3FD,CAAyC,OAAO,GA2F/C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport { StringChecks } from '../parser/StringChecks';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberIdentifier}.\r\n */\r\nexport interface IDocMemberIdentifierParameters extends IDocNodeParameters {\r\n  identifier: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberIdentifier}.\r\n */\r\nexport interface IDocMemberIdentifierParsedParameters extends IDocNodeParsedParameters {\r\n  leftQuoteExcerpt?: TokenSequence;\r\n\r\n  identifierExcerpt: TokenSequence;\r\n\r\n  rightQuoteExcerpt?: TokenSequence;\r\n}\r\n\r\n/**\r\n * A member identifier is part of a {@link DocMemberReference}.\r\n */\r\nexport class DocMemberIdentifier extends DocNode {\r\n  private readonly _leftQuoteExcerpt: DocExcerpt | undefined;\r\n\r\n  private _identifier: string | undefined;\r\n  private readonly _identifierExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _rightQuoteExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocMemberIdentifierParameters | IDocMemberIdentifierParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      if (parameters.leftQuoteExcerpt) {\r\n        this._leftQuoteExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.MemberIdentifier_LeftQuote,\r\n          content: parameters.leftQuoteExcerpt\r\n        });\r\n      }\r\n\r\n      this._identifierExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.MemberIdentifier_Identifier,\r\n        content: parameters.identifierExcerpt\r\n      });\r\n\r\n      if (parameters.rightQuoteExcerpt) {\r\n        this._rightQuoteExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.MemberIdentifier_RightQuote,\r\n          content: parameters.rightQuoteExcerpt\r\n        });\r\n      }\r\n    } else {\r\n      this._identifier = parameters.identifier;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Tests whether the input string can be used without quotes as a member identifier in a declaration reference.\r\n   * If not, {@link DocMemberIdentifier.hasQuotes} will be required.\r\n   *\r\n   * @remarks\r\n   * In order to be used without quotes, the string must follow the identifier syntax for ECMAScript / TypeScript,\r\n   * and it must not be one of the reserved words used for system selectors (such as `instance`, `static`,\r\n   * `constructor`, etc).\r\n   */\r\n  public static isValidIdentifier(identifier: string): boolean {\r\n    return !StringChecks.explainIfInvalidUnquotedMemberIdentifier(identifier);\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.MemberIdentifier;\r\n  }\r\n\r\n  /**\r\n   * The identifier string without any quote encoding.\r\n   *\r\n   * @remarks\r\n   * If the value is not a valid ECMAScript identifier, it will be quoted as a\r\n   * string literal during rendering.\r\n   */\r\n  public get identifier(): string {\r\n    if (this._identifier === undefined) {\r\n      this._identifier = this._identifierExcerpt!.content.toString();\r\n    }\r\n    return this._identifier;\r\n  }\r\n\r\n  /**\r\n   * Returns true if the identifier will be rendered as a quoted string literal\r\n   * instead of as a programming language identifier.  This is required if the\r\n   * `identifier` property is not a valid ECMAScript identifier.\r\n   */\r\n  public get hasQuotes(): boolean {\r\n    if (this._identifierExcerpt) {\r\n      return !!this._leftQuoteExcerpt;\r\n    } else {\r\n      return !DocMemberIdentifier.isValidIdentifier(this.identifier);\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._leftQuoteExcerpt, this._identifierExcerpt, this._rightQuoteExcerpt];\r\n  }\r\n}\r\n"]}