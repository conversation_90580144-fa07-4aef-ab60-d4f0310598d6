"""
JWT Manager - Advanced Authentication System
===========================================
مدير JWT المتقدم لنظام المصادقة
"""

import jwt
import bcrypt
import secrets
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
import base64
import os

logger = logging.getLogger(__name__)


class TokenType(Enum):
    """أنواع الرموز المميزة"""
    ACCESS = "access"
    REFRESH = "refresh"
    RESET_PASSWORD = "reset_password"
    EMAIL_VERIFICATION = "email_verification"
    TWO_FACTOR = "two_factor"


class UserRole(Enum):
    """أدوار المستخدمين"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    AGENT_MANAGER = "agent_manager"
    AGENT = "agent"
    USER = "user"
    GUEST = "guest"


class Permission(Enum):
    """الصلاحيات"""
    # User Management
    CREATE_USER = "create_user"
    READ_USER = "read_user"
    UPDATE_USER = "update_user"
    DELETE_USER = "delete_user"
    
    # Transaction Management
    CREATE_TRANSACTION = "create_transaction"
    READ_TRANSACTION = "read_transaction"
    UPDATE_TRANSACTION = "update_transaction"
    DELETE_TRANSACTION = "delete_transaction"
    APPROVE_TRANSACTION = "approve_transaction"
    
    # Agent Management
    CREATE_AGENT = "create_agent"
    READ_AGENT = "read_agent"
    UPDATE_AGENT = "update_agent"
    DELETE_AGENT = "delete_agent"
    MANAGE_COMMISSIONS = "manage_commissions"
    
    # System Management
    MANAGE_SYSTEM = "manage_system"
    VIEW_ANALYTICS = "view_analytics"
    MANAGE_SETTINGS = "manage_settings"
    VIEW_LOGS = "view_logs"
    
    # Financial Operations
    TRANSFER_MONEY = "transfer_money"
    WITHDRAW_MONEY = "withdraw_money"
    DEPOSIT_MONEY = "deposit_money"
    VIEW_BALANCE = "view_balance"


@dataclass
class TokenPayload:
    """محتوى الرمز المميز"""
    user_id: str
    email: str
    role: UserRole
    permissions: List[Permission]
    token_type: TokenType
    issued_at: datetime
    expires_at: datetime
    session_id: str
    device_id: Optional[str] = None
    ip_address: Optional[str] = None
    two_factor_verified: bool = False


@dataclass
class AuthResult:
    """نتيجة المصادقة"""
    success: bool
    user_id: Optional[str] = None
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    token_type: str = "Bearer"
    error: Optional[str] = None
    requires_2fa: bool = False
    session_id: Optional[str] = None


class JWTManager:
    """مدير JWT المتقدم"""
    
    def __init__(
        self,
        secret_key: str,
        refresh_secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 15,
        refresh_token_expire_days: int = 30
    ):
        self.secret_key = secret_key
        self.refresh_secret_key = refresh_secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days
        
        # Role-Permission mapping
        self.role_permissions = self._initialize_role_permissions()
        
        # Active sessions tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        # Blacklisted tokens
        self.blacklisted_tokens: set = set()
        
        # Failed login attempts tracking
        self.failed_attempts: Dict[str, Dict[str, Any]] = {}
        
    def _initialize_role_permissions(self) -> Dict[UserRole, List[Permission]]:
        """تهيئة صلاحيات الأدوار"""
        return {
            UserRole.SUPER_ADMIN: list(Permission),  # All permissions
            
            UserRole.ADMIN: [
                Permission.CREATE_USER, Permission.READ_USER, Permission.UPDATE_USER,
                Permission.READ_TRANSACTION, Permission.UPDATE_TRANSACTION, Permission.APPROVE_TRANSACTION,
                Permission.CREATE_AGENT, Permission.READ_AGENT, Permission.UPDATE_AGENT,
                Permission.MANAGE_COMMISSIONS, Permission.VIEW_ANALYTICS, Permission.MANAGE_SETTINGS,
                Permission.VIEW_LOGS
            ],
            
            UserRole.AGENT_MANAGER: [
                Permission.READ_USER, Permission.UPDATE_USER,
                Permission.CREATE_TRANSACTION, Permission.READ_TRANSACTION, Permission.UPDATE_TRANSACTION,
                Permission.CREATE_AGENT, Permission.READ_AGENT, Permission.UPDATE_AGENT,
                Permission.MANAGE_COMMISSIONS, Permission.VIEW_ANALYTICS
            ],
            
            UserRole.AGENT: [
                Permission.READ_USER, Permission.UPDATE_USER,
                Permission.CREATE_TRANSACTION, Permission.READ_TRANSACTION,
                Permission.TRANSFER_MONEY, Permission.VIEW_BALANCE
            ],
            
            UserRole.USER: [
                Permission.READ_USER, Permission.UPDATE_USER,
                Permission.CREATE_TRANSACTION, Permission.READ_TRANSACTION,
                Permission.TRANSFER_MONEY, Permission.WITHDRAW_MONEY,
                Permission.DEPOSIT_MONEY, Permission.VIEW_BALANCE
            ],
            
            UserRole.GUEST: [
                Permission.READ_USER
            ]
        }
    
    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        try:
            # Generate salt and hash password
            salt = bcrypt.gensalt(rounds=12)
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
            return hashed.decode('utf-8')
            
        except Exception as e:
            logger.error(f"❌ Password hashing failed: {e}")
            raise
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            return bcrypt.checkpw(
                password.encode('utf-8'),
                hashed_password.encode('utf-8')
            )
            
        except Exception as e:
            logger.error(f"❌ Password verification failed: {e}")
            return False
    
    def generate_session_id(self) -> str:
        """توليد معرف الجلسة"""
        return secrets.token_urlsafe(32)
    
    def create_access_token(
        self,
        user_id: str,
        email: str,
        role: UserRole,
        session_id: str,
        device_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        two_factor_verified: bool = False
    ) -> str:
        """إنشاء رمز الوصول"""
        try:
            now = datetime.now(timezone.utc)
            expires_at = now + timedelta(minutes=self.access_token_expire_minutes)
            
            # Get permissions for role
            permissions = self.role_permissions.get(role, [])
            
            payload = {
                "user_id": user_id,
                "email": email,
                "role": role.value,
                "permissions": [p.value for p in permissions],
                "token_type": TokenType.ACCESS.value,
                "iat": now.timestamp(),
                "exp": expires_at.timestamp(),
                "session_id": session_id,
                "device_id": device_id,
                "ip_address": ip_address,
                "two_factor_verified": two_factor_verified,
                "jti": secrets.token_urlsafe(16)  # JWT ID for blacklisting
            }
            
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            
            # Track active session
            self.active_sessions[session_id] = {
                "user_id": user_id,
                "email": email,
                "role": role.value,
                "created_at": now,
                "last_activity": now,
                "device_id": device_id,
                "ip_address": ip_address,
                "two_factor_verified": two_factor_verified
            }
            
            logger.info(f"✅ Access token created for user: {user_id}")
            return token
            
        except Exception as e:
            logger.error(f"❌ Access token creation failed: {e}")
            raise
    
    def create_refresh_token(
        self,
        user_id: str,
        session_id: str
    ) -> str:
        """إنشاء رمز التحديث"""
        try:
            now = datetime.now(timezone.utc)
            expires_at = now + timedelta(days=self.refresh_token_expire_days)
            
            payload = {
                "user_id": user_id,
                "token_type": TokenType.REFRESH.value,
                "iat": now.timestamp(),
                "exp": expires_at.timestamp(),
                "session_id": session_id,
                "jti": secrets.token_urlsafe(16)
            }
            
            token = jwt.encode(payload, self.refresh_secret_key, algorithm=self.algorithm)
            
            logger.info(f"✅ Refresh token created for user: {user_id}")
            return token
            
        except Exception as e:
            logger.error(f"❌ Refresh token creation failed: {e}")
            raise
    
    def verify_token(self, token: str, token_type: TokenType = TokenType.ACCESS) -> Optional[TokenPayload]:
        """التحقق من الرمز المميز"""
        try:
            # Choose the right secret key
            secret_key = self.secret_key if token_type == TokenType.ACCESS else self.refresh_secret_key
            
            # Decode token
            payload = jwt.decode(token, secret_key, algorithms=[self.algorithm])
            
            # Check if token is blacklisted
            jti = payload.get("jti")
            if jti and jti in self.blacklisted_tokens:
                logger.warning(f"⚠️ Blacklisted token used: {jti}")
                return None
            
            # Verify token type
            if payload.get("token_type") != token_type.value:
                logger.warning(f"⚠️ Invalid token type: {payload.get('token_type')}")
                return None
            
            # Check session validity
            session_id = payload.get("session_id")
            if session_id and session_id not in self.active_sessions:
                logger.warning(f"⚠️ Invalid session: {session_id}")
                return None
            
            # Create TokenPayload object
            token_payload = TokenPayload(
                user_id=payload["user_id"],
                email=payload.get("email", ""),
                role=UserRole(payload.get("role", UserRole.USER.value)),
                permissions=[Permission(p) for p in payload.get("permissions", [])],
                token_type=TokenType(payload["token_type"]),
                issued_at=datetime.fromtimestamp(payload["iat"], timezone.utc),
                expires_at=datetime.fromtimestamp(payload["exp"], timezone.utc),
                session_id=session_id,
                device_id=payload.get("device_id"),
                ip_address=payload.get("ip_address"),
                two_factor_verified=payload.get("two_factor_verified", False)
            )
            
            # Update last activity
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["last_activity"] = datetime.now(timezone.utc)
            
            return token_payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("⚠️ Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"⚠️ Invalid token: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Token verification failed: {e}")
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """تحديث رمز الوصول"""
        try:
            # Verify refresh token
            token_payload = self.verify_token(refresh_token, TokenType.REFRESH)
            if not token_payload:
                return None
            
            # Get session info
            session_id = token_payload.session_id
            session_info = self.active_sessions.get(session_id)
            if not session_info:
                return None
            
            # Create new access token
            new_access_token = self.create_access_token(
                user_id=token_payload.user_id,
                email=session_info["email"],
                role=UserRole(session_info["role"]),
                session_id=session_id,
                device_id=session_info.get("device_id"),
                ip_address=session_info.get("ip_address"),
                two_factor_verified=session_info.get("two_factor_verified", False)
            )
            
            logger.info(f"✅ Access token refreshed for user: {token_payload.user_id}")
            return new_access_token
            
        except Exception as e:
            logger.error(f"❌ Token refresh failed: {e}")
            return None
    
    def blacklist_token(self, token: str) -> bool:
        """إضافة الرمز إلى القائمة السوداء"""
        try:
            # Decode token to get JTI
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm], options={"verify_exp": False})
            jti = payload.get("jti")
            
            if jti:
                self.blacklisted_tokens.add(jti)
                logger.info(f"✅ Token blacklisted: {jti}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Token blacklisting failed: {e}")
            return False
    
    def logout_session(self, session_id: str) -> bool:
        """تسجيل خروج الجلسة"""
        try:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                logger.info(f"✅ Session logged out: {session_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Session logout failed: {e}")
            return False
    
    def logout_all_sessions(self, user_id: str) -> int:
        """تسجيل خروج جميع جلسات المستخدم"""
        try:
            sessions_to_remove = []
            
            for session_id, session_info in self.active_sessions.items():
                if session_info["user_id"] == user_id:
                    sessions_to_remove.append(session_id)
            
            for session_id in sessions_to_remove:
                del self.active_sessions[session_id]
            
            logger.info(f"✅ All sessions logged out for user: {user_id} ({len(sessions_to_remove)} sessions)")
            return len(sessions_to_remove)
            
        except Exception as e:
            logger.error(f"❌ Logout all sessions failed: {e}")
            return 0
    
    def check_permission(self, token_payload: TokenPayload, required_permission: Permission) -> bool:
        """فحص الصلاحية"""
        return required_permission in token_payload.permissions
    
    def check_role(self, token_payload: TokenPayload, required_role: UserRole) -> bool:
        """فحص الدور"""
        # Define role hierarchy
        role_hierarchy = {
            UserRole.SUPER_ADMIN: 5,
            UserRole.ADMIN: 4,
            UserRole.AGENT_MANAGER: 3,
            UserRole.AGENT: 2,
            UserRole.USER: 1,
            UserRole.GUEST: 0
        }
        
        user_level = role_hierarchy.get(token_payload.role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        return user_level >= required_level
    
    def track_failed_login(self, identifier: str, ip_address: str = None) -> Dict[str, Any]:
        """تتبع محاولات تسجيل الدخول الفاشلة"""
        now = datetime.now(timezone.utc)
        key = f"{identifier}:{ip_address}" if ip_address else identifier
        
        if key not in self.failed_attempts:
            self.failed_attempts[key] = {
                "count": 0,
                "first_attempt": now,
                "last_attempt": now,
                "locked_until": None
            }
        
        attempt_info = self.failed_attempts[key]
        attempt_info["count"] += 1
        attempt_info["last_attempt"] = now
        
        # Lock account after 5 failed attempts
        if attempt_info["count"] >= 5:
            attempt_info["locked_until"] = now + timedelta(minutes=30)
            logger.warning(f"⚠️ Account locked due to failed attempts: {identifier}")
        
        return attempt_info
    
    def is_account_locked(self, identifier: str, ip_address: str = None) -> bool:
        """فحص إذا كان الحساب مقفل"""
        key = f"{identifier}:{ip_address}" if ip_address else identifier
        
        if key not in self.failed_attempts:
            return False
        
        attempt_info = self.failed_attempts[key]
        locked_until = attempt_info.get("locked_until")
        
        if locked_until and datetime.now(timezone.utc) < locked_until:
            return True
        
        # Reset if lock period has passed
        if locked_until and datetime.now(timezone.utc) >= locked_until:
            del self.failed_attempts[key]
        
        return False
    
    def clear_failed_attempts(self, identifier: str, ip_address: str = None):
        """مسح محاولات تسجيل الدخول الفاشلة"""
        key = f"{identifier}:{ip_address}" if ip_address else identifier
        
        if key in self.failed_attempts:
            del self.failed_attempts[key]
    
    def get_active_sessions(self, user_id: str = None) -> List[Dict[str, Any]]:
        """الحصول على الجلسات النشطة"""
        sessions = []
        
        for session_id, session_info in self.active_sessions.items():
            if user_id is None or session_info["user_id"] == user_id:
                sessions.append({
                    "session_id": session_id,
                    **session_info,
                    "created_at": session_info["created_at"].isoformat(),
                    "last_activity": session_info["last_activity"].isoformat()
                })
        
        return sessions
    
    def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        now = datetime.now(timezone.utc)
        expired_sessions = []
        
        for session_id, session_info in self.active_sessions.items():
            # Remove sessions inactive for more than 24 hours
            if (now - session_info["last_activity"]).total_seconds() > 86400:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.active_sessions[session_id]
        
        logger.info(f"🧹 Cleaned up {len(expired_sessions)} expired sessions")
        return len(expired_sessions)
    
    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المصادقة"""
        return {
            "active_sessions": len(self.active_sessions),
            "blacklisted_tokens": len(self.blacklisted_tokens),
            "failed_attempts": len(self.failed_attempts),
            "locked_accounts": sum(
                1 for attempt in self.failed_attempts.values()
                if attempt.get("locked_until") and 
                datetime.now(timezone.utc) < attempt["locked_until"]
            )
        }
