@echo off
chcp 65001 >nul
title WS Transfir Professional System

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🚀 WS TRANSFIR - PROFESSIONAL SYSTEM STARTUP 🚀        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:: Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت. حمل من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متاح
echo.

:: Check project structure
if not exist "frontend\web-app" (
    echo ❌ مجلد frontend/web-app غير موجود
    pause
    exit /b 1
)

echo ✅ هيكل المشروع صحيح
echo.

:: Create simple API server
echo 🔧 إنشاء خادم API...
(
    echo const express = require('express'^);
    echo const cors = require('cors'^);
    echo const app = express(^);
    echo.
    echo app.use(cors(^)^);
    echo app.use(express.json(^)^);
    echo.
    echo // Health check
    echo app.get('/api/health', (req, res^) =^> {
    echo   res.json({ status: 'OK', message: 'WS Transfir API Running', timestamp: new Date(^) }^);
    echo }^);
    echo.
    echo // Auth endpoints
    echo app.post('/api/auth/login', (req, res^) =^> {
    echo   const { email, password } = req.body;
    echo   if (email === '<EMAIL>' ^&^& password === 'admin123'^) {
    echo     res.json({
    echo       success: true,
    echo       token: 'mock-admin-token',
    echo       user: { id: '1', firstName: 'مدير', lastName: 'النظام', email, role: 'admin' }
    echo     }^);
    echo   } else if (email ^&^& password^) {
    echo     res.json({
    echo       success: true,
    echo       token: 'mock-user-token',
    echo       user: { id: '2', firstName: 'أحمد', lastName: 'محمد', email, role: 'user' }
    echo     }^);
    echo   } else {
    echo     res.status(401^).json({ success: false, message: 'بيانات خاطئة' }^);
    echo   }
    echo }^);
    echo.
    echo app.post('/api/auth/register', (req, res^) =^> {
    echo   res.json({ success: true, message: 'تم إنشاء الحساب بنجاح' }^);
    echo }^);
    echo.
    echo // Profile endpoint
    echo app.get('/api/profile/me', (req, res^) =^> {
    echo   res.json({
    echo     id: '1', firstName: 'أحمد', lastName: 'محمد',
    echo     email: '<EMAIL>', phone: '+966501234567',
    echo     isVerified: true, completionPercentage: 85
    echo   }^);
    echo }^);
    echo.
    echo // Transfers endpoints
    echo app.get('/api/transfers', (req, res^) =^> {
    echo   res.json({
    echo     data: [
    echo       { id: '1', referenceNumber: 'WS20241225001', amount: '1,500.00', currency: 'SAR', receiverName: 'أحمد محمد', status: 'completed' },
    echo       { id: '2', referenceNumber: 'WS20241224002', amount: '750.00', currency: 'USD', receiverName: 'فاطمة علي', status: 'pending' }
    echo     ],
    echo     total: 2, page: 1, totalPages: 1
    echo   }^);
    echo }^);
    echo.
    echo app.get('/api/transfers/stats', (req, res^) =^> {
    echo   res.json({ totalTransfers: 24, totalAmount: '45,230.50', pendingTransfers: 3, completedTransfers: 21 }^);
    echo }^);
    echo.
    echo const PORT = 3000;
    echo app.listen(PORT, (^) =^> {
    echo   console.log('🚀 WS Transfir API Server running on http://localhost:' + PORT^);
    echo   console.log('📊 Health: http://localhost:' + PORT + '/api/health'^);
    echo   console.log('🔐 Login: <EMAIL> / admin123'^);
    echo }^);
) > simple-api-server.js

echo ✅ تم إنشاء خادم API
echo.

:: Install dependencies if needed
cd frontend\web-app
if not exist "node_modules" (
    echo 📦 تثبيت dependencies...
    npm install --silent
    if errorlevel 1 (
        echo ❌ فشل في التثبيت
        cd ..\..
        pause
        exit /b 1
    )
)

:: Create next config if needed
if not exist "next.config.js" (
    echo /** @type {import('next').NextConfig} */ > next.config.js
    echo const nextConfig = { reactStrictMode: true }; >> next.config.js
    echo module.exports = nextConfig; >> next.config.js
)

cd ..\..

echo 🚀 تشغيل النظام...
echo.

:: Start API server
start "WS Transfir API" cmd /k "node simple-api-server.js"
timeout /t 3 /nobreak >nul

:: Start frontend
cd frontend\web-app
start "WS Transfir Frontend" cmd /k "npm run dev"
cd ..\..

:: Wait and open browser
timeout /t 8 /nobreak >nul
start "" "http://localhost:3100"
start "" "http://localhost:3000/api/health"

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    ✅ WS TRANSFIR SYSTEM IS NOW RUNNING!                  ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🌐 روابط الوصول:
echo ═══════════════════════════════════════════════════════════════
echo 📱 التطبيق:              http://localhost:3100
echo 🔧 API Server:           http://localhost:3000
echo 📊 Health Check:         http://localhost:3000/api/health
echo.
echo 🔐 بيانات الدخول:
echo ═══════════════════════════════════════════════════════════════
echo 👨‍💼 مدير:                <EMAIL> / admin123
echo 👤 مستخدم:              <EMAIL> / password123
echo.
echo 📋 الصفحات المتاحة:
echo ═══════════════════════════════════════════════════════════════
echo ✅ الرئيسية             http://localhost:3100
echo ✅ تسجيل الدخول         http://localhost:3100/login
echo ✅ إنشاء حساب           http://localhost:3100/register
echo ✅ الملف الشخصي         http://localhost:3100/profile
echo ✅ التحويلات            http://localhost:3100/transfers
echo ✅ استرداد كلمة المرور   http://localhost:3100/forgot-password
echo.
echo 💡 ملاحظات:
echo ═══════════════════════════════════════════════════════════════
echo 🔹 جميع البيانات وهمية للعرض
echo 🔹 استخدم بيانات الدخول أعلاه
echo 🔹 اضغط Ctrl+C في النوافذ للإيقاف
echo 🔹 تحقق من console للأخطاء
echo.

pause
