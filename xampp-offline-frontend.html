<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WS Transfir - XAMPP Offline Professional System</title>
    <meta name="description" content="نظام التحويلات المالية WS Transfir المحلي الاحترافي على XAMPP">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📴</text></svg>">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --offline-color: #8e44ad;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8e44ad 100%);
            min-height: 100vh;
            color: #2c3e50;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            text-align: center;
            border: 3px solid rgba(142, 68, 173, 0.3);
        }
        
        .header h1 {
            font-size: 3em;
            color: var(--offline-color);
            margin-bottom: 15px;
            font-weight: 800;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .header p {
            font-size: 1.3em;
            color: #7f8c8d;
            margin-bottom: 25px;
        }
        
        .offline-badge {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: linear-gradient(135deg, var(--offline-color) 0%, #9b59b6 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1em;
            animation: offlinePulse 3s infinite;
            box-shadow: 0 10px 25px rgba(142, 68, 173, 0.3);
        }
        
        @keyframes offlinePulse {
            0%, 100% { transform: scale(1); box-shadow: 0 10px 25px rgba(142, 68, 173, 0.3); }
            50% { transform: scale(1.05); box-shadow: 0 15px 35px rgba(142, 68, 173, 0.5); }
        }
        
        .main-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 35px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 2px solid rgba(142, 68, 173, 0.2);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--offline-color), #9b59b6, var(--offline-color));
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .card:hover::before {
            transform: translateX(0);
        }
        
        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 35px 70px rgba(0, 0, 0, 0.2);
            border-color: var(--offline-color);
        }
        
        .card h2 {
            color: var(--offline-color);
            margin-bottom: 25px;
            font-size: 1.6em;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 700;
        }
        
        .btn {
            background: linear-gradient(135deg, var(--offline-color) 0%, #9b59b6 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(142, 68, 173, 0.4);
        }
        
        .btn.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%);
        }
        
        .btn.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
        }
        
        .btn.danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
        }
        
        .system-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        
        .info-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid var(--offline-color);
            transition: all 0.3s ease;
        }
        
        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .info-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .info-value {
            font-weight: 700;
            color: var(--dark-color);
            font-size: 1.1em;
        }
        
        .features-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 40px;
            margin: 30px 0;
            border: 2px solid rgba(142, 68, 173, 0.2);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .feature-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 20px;
            text-align: center;
            transition: all 0.4s ease;
            border: 2px solid transparent;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(142, 68, 173, 0.2);
            border-color: var(--offline-color);
        }
        
        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }
        
        .feature-title {
            font-size: 1.2em;
            font-weight: 700;
            color: var(--offline-color);
            margin-bottom: 10px;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 40px;
            text-align: center;
            border: 2px solid rgba(142, 68, 173, 0.2);
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin: 25px 0;
            flex-wrap: wrap;
        }
        
        .footer-link {
            color: var(--offline-color);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 10px 20px;
            border-radius: 10px;
        }
        
        .footer-link:hover {
            background: var(--offline-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success-color);
            animation: statusBlink 2s infinite;
            margin-left: 8px;
        }
        
        @keyframes statusBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2.2em;
            }
            
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .card {
                padding: 25px;
            }
            
            .system-info {
                grid-template-columns: 1fr;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📴 WS Transfir Offline Professional</h1>
            <p>نظام التحويلات المالية المحلي الاحترافي على XAMPP</p>
            <div class="offline-badge">
                <span>📴</span>
                <span>النظام يعمل محلياً بالكامل</span>
                <span class="status-indicator"></span>
            </div>
        </div>

        <div class="main-content">
            <!-- System Status Card -->
            <div class="card">
                <h2><span>📊</span>حالة النظام المحلي</h2>
                <div class="system-info">
                    <div class="info-item">
                        <div class="info-label">حالة الخادم</div>
                        <div class="info-value" id="server-status">🟢 يعمل محلياً</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">وضع التشغيل</div>
                        <div class="info-value">📴 Offline</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">المنفذ</div>
                        <div class="info-value">8080</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">قاعدة البيانات</div>
                        <div class="info-value">💾 محاكاة محلية</div>
                    </div>
                </div>
                <button class="btn success" onclick="checkOfflineHealth()">
                    <span>🏥</span>
                    فحص صحة النظام المحلي
                </button>
            </div>

            <!-- Authentication Card -->
            <div class="card">
                <h2><span>🔐</span>نظام المصادقة المحلي</h2>
                <p>اختبار تسجيل الدخول في النظام المحلي</p>
                <button class="btn" onclick="testOfflineAdminLogin()">
                    <span>👨‍💼</span>
                    تسجيل دخول المدير محلياً
                </button>
                <button class="btn" onclick="testOfflineUserLogin()">
                    <span>👤</span>
                    تسجيل دخول المستخدم محلياً
                </button>
                <button class="btn warning" onclick="showOfflineCredentials()">
                    <span>🔑</span>
                    عرض بيانات الدخول
                </button>
            </div>

            <!-- Offline Features Card -->
            <div class="card">
                <h2><span>📴</span>ميزات النظام المحلي</h2>
                <p>جميع الميزات تعمل بدون إنترنت</p>
                <button class="btn" onclick="testOfflineTransfers()">
                    <span>💸</span>
                    التحويلات المحلية
                </button>
                <button class="btn" onclick="testOfflineProfile()">
                    <span>👤</span>
                    الملف الشخصي المحلي
                </button>
                <button class="btn success" onclick="testOfflineStats()">
                    <span>📈</span>
                    الإحصائيات المحلية
                </button>
            </div>

            <!-- Performance Card -->
            <div class="card">
                <h2><span>⚡</span>الأداء المحلي</h2>
                <p>أداء فائق بدون اتصال إنترنت</p>
                <div class="system-info">
                    <div class="info-item">
                        <div class="info-label">سرعة الاستجابة</div>
                        <div class="info-value">< 5ms</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">استخدام الذاكرة</div>
                        <div class="info-value">محسن</div>
                    </div>
                </div>
                <button class="btn danger" onclick="clearOfflineResults()">
                    <span>🗑️</span>
                    مسح النتائج
                </button>
            </div>
        </div>

        <!-- Offline Features Section -->
        <div class="features-section">
            <h2 style="text-align: center; color: var(--offline-color); margin-bottom: 30px; font-size: 2em;">
                <span>📴</span> ميزات النظام المحلي الاحترافي
            </h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">🚀</div>
                    <div class="feature-title">سرعة فائقة</div>
                    <p>استجابة فورية بدون تأخير الشبكة</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔒</div>
                    <div class="feature-title">خصوصية كاملة</div>
                    <p>جميع البيانات محفوظة محلياً</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💾</div>
                    <div class="feature-title">قاعدة بيانات محلية</div>
                    <p>محاكاة كاملة لقاعدة البيانات</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">تكامل XAMPP</div>
                    <p>متوافق بالكامل مع بيئة XAMPP</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">واجهة متجاوبة</div>
                    <p>تصميم احترافي لجميع الأجهزة</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🛡️</div>
                    <div class="feature-title">أمان محلي</div>
                    <p>حماية متقدمة بدون اتصال خارجي</p>
                </div>
            </div>
        </div>

        <!-- Results Display -->
        <div id="result" style="display: none; background: var(--dark-color); color: #ecf0f1; padding: 25px; border-radius: 15px; margin: 25px 0; font-family: 'Courier New', monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto;"></div>

        <!-- Footer -->
        <div class="footer">
            <h3>📴 WS Transfir Offline Professional System</h3>
            <p>نظام التحويلات المالية المحلي الاحترافي - يعمل بدون إنترنت</p>
            <div class="footer-links">
                <a href="/api/health" class="footer-link">📊 فحص الصحة</a>
                <a href="/api/status" class="footer-link">📈 حالة النظام</a>
                <a href="mailto:<EMAIL>" class="footer-link">📧 الدعم الفني</a>
                <a href="#" class="footer-link">🌐 النظام المحلي</a>
            </div>
            <p style="margin-top: 25px; color: #7f8c8d; font-size: 0.9em;">
                © 2024 WS Transfir. جميع الحقوق محفوظة. | النظام المحلي الاحترافي
            </p>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        
        // Utility functions for offline system
        function showResult(data, title = 'النتيجة المحلية') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `=== ${title} ===\n${JSON.stringify(data, null, 2)}`;
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function showError(error, title = 'خطأ محلي') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `=== ${title} ===\nخطأ: ${error.message || error}`;
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // Offline system functions
        async function checkOfflineHealth() {
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                showResult(data, 'فحص صحة النظام المحلي');
                
                // Update UI
                document.getElementById('server-status').innerHTML = '🟢 يعمل محلياً بنجاح';
            } catch (error) {
                showError(error, 'خطأ في فحص النظام المحلي');
            }
        }
        
        async function testOfflineAdminLogin() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                const data = await response.json();
                showResult(data, 'تسجيل دخول المدير محلياً');
            } catch (error) {
                showError(error, 'خطأ في تسجيل الدخول المحلي');
            }
        }
        
        async function testOfflineUserLogin() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                const data = await response.json();
                showResult(data, 'تسجيل دخول المستخدم محلياً');
            } catch (error) {
                showError(error, 'خطأ في تسجيل الدخول المحلي');
            }
        }
        
        function showOfflineCredentials() {
            const credentials = {
                admin: {
                    email: '<EMAIL>',
                    password: 'admin123',
                    role: 'مدير النظام المحلي'
                },
                user: {
                    email: '<EMAIL>',
                    password: 'password123',
                    role: 'مستخدم عادي محلي'
                }
            };
            showResult(credentials, 'بيانات الدخول المحلية');
        }
        
        function clearOfflineResults() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            resultDiv.innerHTML = '';
        }
        
        // Auto-check system health on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkOfflineHealth();
            }, 1000);
        });
        
        // Update time every second
        setInterval(() => {
            const now = new Date().toLocaleString('ar-SA');
            console.log(`📴 النظام المحلي يعمل: ${now}`);
        }, 30000);
        
        console.log('📴 WS Transfir Offline Professional System loaded successfully!');
        console.log('🏥 Health Check: /api/health');
        console.log('🔐 Authentication: /api/auth/login');
        console.log('📴 Mode: Complete Offline');
    </script>
</body>
</html>
