[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "walletsystem"
version = "1.0.0"
description = "Advanced Digital Wallet System"
readme = "README_NEW.md"
license = {text = "MIT"}
authors = [
    {name = "WalletSystem Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "WalletSystem Development Team", email = "<EMAIL>"}
]
keywords = [
    "wallet", "digital-wallet", "fintech", "payment", "banking",
    "financial-services", "fastapi", "asyncio", "postgresql"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial",
    "Framework :: FastAPI",
    "Framework :: AsyncIO",
]
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "asyncpg>=0.29.0",
    "aiohttp>=3.9.0",
    "PyJWT>=2.8.0",
    "cryptography>=41.0.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "python-dateutil>=2.8.2",
    "python-dotenv>=1.0.0",
]
dynamic = ["version"]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "black>=23.10.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
    "isort>=5.12.0",
]
prod = [
    "gunicorn>=21.2.0",
    "supervisor>=4.2.0",
]
all = [
    "redis>=5.0.0",
    "celery>=5.3.0",
    "boto3>=1.34.0",
    "sentry-sdk[fastapi]>=1.38.0",
]

[project.scripts]
walletsystem = "backend.main:main"
walletsystem-migrate = "database.migrate:main"
walletsystem-seed = "database.seed:main"

[project.urls]
Homepage = "https://github.com/walletsystem/walletsystem"
Documentation = "https://docs.walletsystem.com"
Repository = "https://github.com/walletsystem/walletsystem"
"Bug Tracker" = "https://github.com/walletsystem/walletsystem/issues"

# Black Configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
    # directories
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.pytest_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
  | node_modules
  | frontend/web-app/.next
  | frontend/web-app/build
  | frontend/mobile-app/build
)/
'''
force-exclude = '''
/(
    migrations/.*
  | seeds/.*
  | \.min\..*
  | node_modules/.*
)/
'''

# isort Configuration
[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["backend", "frontend", "tests", "database", "config"]
known_third_party = [
    "fastapi", "uvicorn", "pydantic", "asyncpg", "aiohttp", 
    "pytest", "sqlalchemy", "alembic", "redis", "celery"
]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
skip_glob = [
    "migrations/*",
    "seeds/*",
    "node_modules/*",
    "build/*",
    "dist/*",
    ".venv/*",
    "venv/*"
]

# Coverage Configuration
[tool.coverage.run]
source = ["backend"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/seeds/*",
    "*/venv/*",
    "*/.venv/*",
    "*/node_modules/*",
    "*/build/*",
    "*/dist/*",
    "*/__pycache__/*",
    "*/.*",
]
branch = true
parallel = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
ignore_errors = true
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# MyPy Configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_optional = true
disallow_any_generics = true
disallow_subclassing_any = true
disallow_untyped_calls = false
disallow_untyped_defs = false
disallow_incomplete_defs = true
check_untyped_defs = true
no_implicit_optional = true
show_error_codes = true
show_column_numbers = true

[[tool.mypy.overrides]]
module = [
    "asyncpg.*",
    "aiohttp.*",
    "fastapi.*",
    "uvicorn.*",
    "pydantic.*",
    "pytest.*",
    "redis.*",
    "celery.*",
]
ignore_missing_imports = true

# Pytest Configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=backend",
    "--cov-report=html:htmlcov",
    "--cov-report=xml:coverage.xml",
    "--cov-report=term-missing",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "performance: Performance tests",
    "security: Security tests",
    "slow: Slow running tests",
    "auth: Authentication tests",
    "database: Database tests",
    "api: API tests",
    "bank: Bank integration tests",
    "webhook: Webhook tests",
    "accounting: Accounting integration tests",
    "compliance: Compliance tests",
    "mock: Tests that use mocking",
    "requires_network: Tests that require network access",
    "requires_database: Tests that require database connection",
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
asyncio_mode = "auto"
