{"name": "@nestjs/config", "version": "3.3.0", "description": "Nest - modern, fast, powerful node.js web framework (@config)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/config#readme", "scripts": {"build": "rimraf -rf dist && tsc -p tsconfig.json", "format": "prettier --write \"{lib,test}/**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:integration": "jest --config ./tests/jest-e2e.json --runInBand", "prerelease": "npm run build", "release": "release-it"}, "dependencies": {"dotenv": "16.4.5", "dotenv-expand": "10.0.0", "lodash": "4.17.21"}, "devDependencies": {"@commitlint/cli": "19.5.0", "@commitlint/config-angular": "19.5.0", "@nestjs/common": "10.4.5", "@nestjs/core": "10.4.5", "@nestjs/platform-express": "10.4.5", "@nestjs/testing": "10.4.5", "@types/jest": "29.5.13", "@types/lodash": "4.17.12", "@types/node": "20.16.13", "@typescript-eslint/eslint-plugin": "8.10.0", "@typescript-eslint/parser": "8.10.0", "eslint": "9.13.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "husky": "9.1.6", "jest": "29.7.0", "joi": "17.13.3", "lint-staged": "15.2.10", "prettier": "3.3.3", "reflect-metadata": "0.2.2", "release-it": "17.10.0", "rimraf": "6.0.1", "rxjs": "7.8.1", "ts-jest": "29.2.5", "typescript": "5.6.3"}, "peerDependencies": {"@nestjs/common": "^8.0.0 || ^9.0.0 || ^10.0.0", "rxjs": "^7.1.0"}, "lint-staged": {"*.ts": ["prettier --write"]}, "husky": {"hooks": {"commit-msg": "commitlint -c .commitlintrc.json -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "https://github.com/nestjs/config"}}