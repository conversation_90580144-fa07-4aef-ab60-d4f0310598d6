import { MsPattern } from '../interfaces';
/**
 * Transforms the Pattern to Route.
 * 1. If <PERSON><PERSON> is a `string`, it will be returned as it is.
 * 2. If <PERSON>tern is a `number`, it will be converted to `string`.
 * 3. If <PERSON><PERSON> is a `JSON` object, it will be transformed to Route. For that end,
 * the function will sort properties of `JSON` Object and creates `route` string
 * according to the following template:
 * <key1>:<value1>/<key2>:<value2>/.../<keyN>:<valueN>
 *
 * @param  {MsPattern} pattern - client pattern
 * @returns string
 */
export declare function transformPatternToRoute(pattern: MsPattern): string;
