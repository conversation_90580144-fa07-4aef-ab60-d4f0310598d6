"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sha512_256 = exports.SHA512_256 = exports.sha512_224 = exports.SHA512_224 = exports.sha384 = exports.SHA384 = exports.sha512 = exports.SHA512 = void 0;
/**
 * SHA2-512 a.k.a. sha512 and sha384. It is slower than sha256 in js because u64 operations are slow.
 *
 * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and
 * [the paper on truncated SHA512/256](https://eprint.iacr.org/2010/548.pdf).
 * @module
 * @deprecated
 */
const sha2_ts_1 = require("./sha2.js");
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.SHA512 = sha2_ts_1.SHA512;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.sha512 = sha2_ts_1.sha512;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.SHA384 = sha2_ts_1.SHA384;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.sha384 = sha2_ts_1.sha384;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.SHA512_224 = sha2_ts_1.SHA512_224;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.sha512_224 = sha2_ts_1.sha512_224;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.SHA512_256 = sha2_ts_1.SHA512_256;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.sha512_256 = sha2_ts_1.sha512_256;
//# sourceMappingURL=sha512.js.map