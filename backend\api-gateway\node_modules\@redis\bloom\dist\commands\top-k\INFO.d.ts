export declare const FIRST_KEY_INDEX = 1;
export declare const IS_READ_ONLY = true;
export declare function transformArguments(key: string): Array<string>;
export type InfoRawReply = [
    _: string,
    k: number,
    _: string,
    width: number,
    _: string,
    depth: number,
    _: string,
    decay: string
];
export interface InfoReply {
    k: number;
    width: number;
    depth: number;
    decay: number;
}
export declare function transformReply(reply: InfoRawReply): InfoReply;
