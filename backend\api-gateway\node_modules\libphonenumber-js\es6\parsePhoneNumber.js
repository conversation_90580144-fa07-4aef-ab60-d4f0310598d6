import normalizeArguments from './normalizeArguments.js';
import parsePhoneNumber_ from './parsePhoneNumber_.js';
export default function parsePhoneNumber() {
  var _normalizeArguments = normalizeArguments(arguments),
      text = _normalizeArguments.text,
      options = _normalizeArguments.options,
      metadata = _normalizeArguments.metadata;

  return parsePhoneNumber_(text, options, metadata);
}
//# sourceMappingURL=parsePhoneNumber.js.map