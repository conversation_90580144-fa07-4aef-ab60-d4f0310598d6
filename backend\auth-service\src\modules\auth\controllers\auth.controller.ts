import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Req,
  UseGuards,
  Get,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { Request } from 'express';
import { Throttle } from '@nestjs/throttler';

// Services
import { AuthService } from '../services/auth.service';

// DTOs
import { RegisterDto } from '../dto/register.dto';
import { LoginDto, VerifyEmailDto, VerifyPhoneDto, ResendVerificationDto, RefreshTokenDto } from '../dto/login.dto';

// Guards
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

// Decorators
import { GetUser } from '../decorators/get-user.decorator';
import { User } from '../../users/entities/user.entity';

@ApiTags('Authentication')
@Controller('auth')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @Throttle(5, 60) // 5 requests per minute
  @ApiOperation({
    summary: 'تسجيل مستخدم جديد',
    description: 'إنشاء حساب جديد في النظام',
  })
  @ApiResponse({
    status: 201,
    description: 'تم إنشاء الحساب بنجاح',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'تم إنشاء الحساب بنجاح' },
        userId: { type: 'string', example: 'uuid-string' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'بيانات غير صالحة',
  })
  @ApiResponse({
    status: 409,
    description: 'المستخدم موجود بالفعل',
  })
  @ApiBody({ type: RegisterDto })
  async register(
    @Body() registerDto: RegisterDto,
    @Req() req: Request,
  ) {
    const ip = req.ip || req.connection.remoteAddress;
    return this.authService.register(registerDto, ip);
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @Throttle(10, 60) // 10 requests per minute
  @ApiOperation({
    summary: 'تسجيل الدخول',
    description: 'تسجيل دخول المستخدم والحصول على رمز الوصول',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تسجيل الدخول بنجاح',
    schema: {
      type: 'object',
      properties: {
        user: {
          type: 'object',
          description: 'بيانات المستخدم',
        },
        accessToken: {
          type: 'string',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        refreshToken: {
          type: 'string',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        expiresIn: {
          type: 'number',
          example: 86400,
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'بيانات الدخول غير صحيحة',
  })
  @ApiResponse({
    status: 403,
    description: 'الحساب محظور أو غير مفعل',
  })
  @ApiBody({ type: LoginDto })
  async login(
    @Body() loginDto: LoginDto,
    @Req() req: Request,
  ) {
    const ip = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');
    return this.authService.login(loginDto, ip, userAgent);
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'تسجيل الخروج',
    description: 'تسجيل خروج المستخدم وإلغاء الجلسة',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تسجيل الخروج بنجاح',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'تم تسجيل الخروج بنجاح' },
      },
    },
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        refreshToken: { type: 'string' },
      },
    },
  })
  async logout(
    @GetUser() user: User,
    @Body('refreshToken') refreshToken: string,
  ) {
    return this.authService.logout(user.id, refreshToken);
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @Throttle(20, 60) // 20 requests per minute
  @ApiOperation({
    summary: 'تحديث الرمز المميز',
    description: 'الحصول على رمز وصول جديد باستخدام رمز التحديث',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تحديث الرمز بنجاح',
    schema: {
      type: 'object',
      properties: {
        accessToken: { type: 'string' },
        expiresIn: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'رمز التحديث غير صالح',
  })
  @ApiBody({ type: RefreshTokenDto })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto.refreshToken);
  }

  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  @Throttle(10, 60) // 10 requests per minute
  @ApiOperation({
    summary: 'تفعيل البريد الإلكتروني',
    description: 'تفعيل البريد الإلكتروني باستخدام رمز التحقق',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تفعيل البريد الإلكتروني بنجاح',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'تم تفعيل البريد الإلكتروني بنجاح' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'رمز التحقق غير صالح أو منتهي الصلاحية',
  })
  @ApiBody({ type: VerifyEmailDto })
  async verifyEmail(@Body() verifyEmailDto: VerifyEmailDto) {
    return this.authService.verifyEmail(verifyEmailDto);
  }

  @Post('verify-phone')
  @HttpCode(HttpStatus.OK)
  @Throttle(10, 60) // 10 requests per minute
  @ApiOperation({
    summary: 'تفعيل رقم الهاتف',
    description: 'تفعيل رقم الهاتف باستخدام رمز التحقق المرسل عبر SMS',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تفعيل رقم الهاتف بنجاح',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'تم تفعيل رقم الهاتف بنجاح' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'رمز التحقق غير صالح أو منتهي الصلاحية',
  })
  @ApiBody({ type: VerifyPhoneDto })
  async verifyPhone(@Body() verifyPhoneDto: VerifyPhoneDto) {
    return this.authService.verifyPhone(verifyPhoneDto);
  }

  @Post('resend-verification')
  @HttpCode(HttpStatus.OK)
  @Throttle(3, 60) // 3 requests per minute
  @ApiOperation({
    summary: 'إعادة إرسال رمز التحقق',
    description: 'إعادة إرسال رمز التحقق للبريد الإلكتروني أو رقم الهاتف',
  })
  @ApiResponse({
    status: 200,
    description: 'تم إرسال رمز التحقق بنجاح',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'تم إرسال رمز التحقق بنجاح' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'المستخدم غير موجود أو مفعل بالفعل',
  })
  @ApiBody({ type: ResendVerificationDto })
  async resendVerification(@Body() resendDto: ResendVerificationDto) {
    return this.authService.resendVerification(resendDto);
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'الحصول على بيانات المستخدم الحالي',
    description: 'استرجاع بيانات المستخدم المسجل حالياً',
  })
  @ApiResponse({
    status: 200,
    description: 'بيانات المستخدم',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        phone: { type: 'string' },
        status: { type: 'string' },
        role: { type: 'string' },
        emailVerified: { type: 'boolean' },
        phoneVerified: { type: 'boolean' },
        kycStatus: { type: 'string' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'غير مصرح بالوصول',
  })
  async getProfile(@GetUser() user: User) {
    return user;
  }
}
