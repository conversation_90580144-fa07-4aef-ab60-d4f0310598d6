-- Seed: Admin Panel Data
-- Description: إنشاء بيانات لوحة الإدارة التجريبية
-- Version: 003
-- Created: 2024-01-15

-- Insert default system settings
INSERT INTO system_settings (
    key, value, setting_type, category, description, is_public, is_encrypted, created_by
) VALUES 
-- General Settings
(
    'system.name',
    'WS Transfir',
    'string',
    'general',
    'اسم النظام',
    true,
    false,
    'user_admin_001'
),
(
    'system.version',
    '1.0.0',
    'string',
    'general',
    'إصدار النظام',
    true,
    false,
    'user_admin_001'
),
(
    'system.maintenance_mode',
    'false',
    'boolean',
    'general',
    'وضع الصيانة',
    true,
    false,
    'user_admin_001'
),
(
    'system.timezone',
    'Asia/Riyadh',
    'string',
    'general',
    'المنطقة الزمنية للنظام',
    true,
    false,
    'user_admin_001'
),
(
    'system.language',
    'ar',
    'string',
    'general',
    'اللغة الافتراضية للنظام',
    true,
    false,
    'user_admin_001'
),

-- Financial Settings
(
    'financial.default_currency',
    'SAR',
    'string',
    'financial',
    'العملة الافتراضية',
    true,
    false,
    'user_admin_001'
),
(
    'financial.min_transaction_amount',
    '10.00',
    'decimal',
    'financial',
    'الحد الأدنى لمبلغ المعاملة',
    true,
    false,
    'user_admin_001'
),
(
    'financial.max_transaction_amount',
    '100000.00',
    'decimal',
    'financial',
    'الحد الأقصى لمبلغ المعاملة',
    true,
    false,
    'user_admin_001'
),
(
    'financial.auto_settlement_enabled',
    'true',
    'boolean',
    'financial',
    'تفعيل التسوية التلقائية',
    false,
    false,
    'user_admin_001'
),

-- Security Settings
(
    'security.password_min_length',
    '8',
    'integer',
    'security',
    'الحد الأدنى لطول كلمة المرور',
    true,
    false,
    'user_admin_001'
),
(
    'security.max_login_attempts',
    '5',
    'integer',
    'security',
    'الحد الأقصى لمحاولات تسجيل الدخول',
    false,
    false,
    'user_admin_001'
),
(
    'security.session_timeout_minutes',
    '30',
    'integer',
    'security',
    'مهلة انتهاء الجلسة بالدقائق',
    false,
    false,
    'user_admin_001'
),
(
    'security.two_factor_required',
    'false',
    'boolean',
    'security',
    'إجبارية المصادقة الثنائية',
    false,
    false,
    'user_admin_001'
),
(
    'security.password_complexity_required',
    'true',
    'boolean',
    'security',
    'إجبارية تعقيد كلمة المرور',
    true,
    false,
    'user_admin_001'
),

-- Fee Settings
(
    'fees.transfer_fee_rate',
    '0.0050',
    'decimal',
    'fees',
    'معدل رسوم التحويل',
    true,
    false,
    'user_admin_001'
),
(
    'fees.min_transfer_fee',
    '5.00',
    'decimal',
    'fees',
    'الحد الأدنى لرسوم التحويل',
    true,
    false,
    'user_admin_001'
),
(
    'fees.max_transfer_fee',
    '100.00',
    'decimal',
    'fees',
    'الحد الأقصى لرسوم التحويل',
    true,
    false,
    'user_admin_001'
),
(
    'fees.international_transfer_fee_rate',
    '0.0075',
    'decimal',
    'fees',
    'معدل رسوم التحويل الدولي',
    true,
    false,
    'user_admin_001'
),

-- Limits Settings
(
    'limits.daily_transaction_limit',
    '50000.00',
    'decimal',
    'limits',
    'الحد اليومي للمعاملات',
    true,
    false,
    'user_admin_001'
),
(
    'limits.monthly_transaction_limit',
    '500000.00',
    'decimal',
    'limits',
    'الحد الشهري للمعاملات',
    true,
    false,
    'user_admin_001'
),
(
    'limits.kyc_level_1_limit',
    '10000.00',
    'decimal',
    'limits',
    'حد المستوى الأول للتحقق من الهوية',
    false,
    false,
    'user_admin_001'
),
(
    'limits.kyc_level_2_limit',
    '50000.00',
    'decimal',
    'limits',
    'حد المستوى الثاني للتحقق من الهوية',
    false,
    false,
    'user_admin_001'
),
(
    'limits.kyc_level_3_limit',
    '500000.00',
    'decimal',
    'limits',
    'حد المستوى الثالث للتحقق من الهوية',
    false,
    false,
    'user_admin_001'
),

-- Notification Settings
(
    'notifications.email_enabled',
    'true',
    'boolean',
    'notifications',
    'تفعيل الإشعارات عبر البريد الإلكتروني',
    false,
    false,
    'user_admin_001'
),
(
    'notifications.sms_enabled',
    'true',
    'boolean',
    'notifications',
    'تفعيل الإشعارات عبر الرسائل النصية',
    false,
    false,
    'user_admin_001'
),
(
    'notifications.push_enabled',
    'true',
    'boolean',
    'notifications',
    'تفعيل الإشعارات المباشرة',
    false,
    false,
    'user_admin_001'
),
(
    'notifications.admin_email',
    '<EMAIL>',
    'string',
    'notifications',
    'بريد المدير الإلكتروني',
    false,
    false,
    'user_admin_001'
),

-- Compliance Settings
(
    'compliance.aml_enabled',
    'true',
    'boolean',
    'compliance',
    'تفعيل مكافحة غسيل الأموال',
    false,
    false,
    'user_admin_001'
),
(
    'compliance.kyc_required',
    'true',
    'boolean',
    'compliance',
    'إجبارية التحقق من الهوية',
    false,
    false,
    'user_admin_001'
),
(
    'compliance.suspicious_amount_threshold',
    '50000.00',
    'decimal',
    'compliance',
    'حد المبلغ المشبوه',
    false,
    false,
    'user_admin_001'
),

-- Integration Settings
(
    'integrations.payment_gateway_enabled',
    'true',
    'boolean',
    'integrations',
    'تفعيل بوابة الدفع',
    false,
    false,
    'user_admin_001'
),
(
    'integrations.api_rate_limit',
    '1000',
    'integer',
    'integrations',
    'حد معدل استخدام API',
    false,
    false,
    'user_admin_001'
) ON CONFLICT (key) DO NOTHING;

-- Insert sample user activity logs
INSERT INTO user_activity_log (
    user_id, activity_type, description, ip_address, user_agent, performed_by, metadata
) VALUES 
(
    'user_admin_001',
    'login',
    'تسجيل دخول ناجح',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'user_admin_001',
    '{"login_method": "password", "two_factor_used": true}'
),
(
    'user_agent_manager_001',
    'profile_update',
    'تحديث الملف الشخصي',
    '*************',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'user_agent_manager_001',
    '{"fields_updated": ["phone", "address"]}'
),
(
    'user_agent_001',
    'transaction_create',
    'إنشاء معاملة جديدة',
    '*************',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
    'user_agent_001',
    '{"transaction_id": "txn_001", "amount": 1000.00, "type": "transfer"}'
),
(
    'user_customer_001',
    'kyc_submission',
    'تقديم وثائق التحقق من الهوية',
    '*************',
    'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0',
    'user_customer_001',
    '{"documents_submitted": ["national_id", "bank_statement"]}'
),
(
    'user_admin_001',
    'user_status_change',
    'تغيير حالة المستخدم',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'user_admin_001',
    '{"target_user": "user_customer_002", "old_status": "inactive", "new_status": "active"}'
);

-- Insert sample user sessions
INSERT INTO user_sessions (
    session_id, user_id, ip_address, user_agent, device_info, location_info,
    expires_at, login_method, two_factor_verified
) VALUES 
(
    'sess_admin_001_' || extract(epoch from now()),
    'user_admin_001',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    '{"device_type": "desktop", "os": "Windows 10", "browser": "Chrome"}',
    '{"country": "SA", "city": "Riyadh", "timezone": "Asia/Riyadh"}',
    CURRENT_TIMESTAMP + INTERVAL '8 hours',
    'password',
    true
),
(
    'sess_agent_001_' || extract(epoch from now()),
    'user_agent_001',
    '*************',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
    '{"device_type": "mobile", "os": "iOS 15", "browser": "Safari"}',
    '{"country": "SA", "city": "Riyadh", "timezone": "Asia/Riyadh"}',
    CURRENT_TIMESTAMP + INTERVAL '4 hours',
    'password',
    false
),
(
    'sess_customer_001_' || extract(epoch from now()),
    'user_customer_001',
    '*************',
    'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0',
    '{"device_type": "mobile", "os": "Android 11", "browser": "Firefox"}',
    '{"country": "SA", "city": "Jeddah", "timezone": "Asia/Riyadh"}',
    CURRENT_TIMESTAMP + INTERVAL '2 hours',
    'password',
    false
);

-- Insert sample admin notifications
INSERT INTO admin_notifications (
    title, message, notification_type, priority, target_role, is_global,
    action_url, action_label, metadata
) VALUES 
(
    'مراجعة طلبات KYC المعلقة',
    'يوجد 15 طلب KYC في انتظار المراجعة',
    'warning',
    'high',
    'admin',
    true,
    '/admin/kyc/pending',
    'مراجعة الطلبات',
    '{"pending_count": 15, "oldest_request": "2024-01-10"}'
),
(
    'تحديث النظام متاح',
    'يتوفر تحديث جديد للنظام (الإصدار 1.0.1)',
    'info',
    'medium',
    'super_admin',
    false,
    '/admin/system/updates',
    'عرض التحديث',
    '{"version": "1.0.1", "release_date": "2024-01-15"}'
),
(
    'تنبيه أمني',
    'تم اكتشاف محاولات دخول مشبوهة من عنوان IP: *************',
    'error',
    'critical',
    'admin',
    true,
    '/admin/security/alerts',
    'عرض التفاصيل',
    '{"ip_address": "*************", "attempts": 10, "blocked": true}'
),
(
    'تقرير الأداء اليومي',
    'تم إنشاء تقرير الأداء اليومي بنجاح',
    'success',
    'low',
    'admin',
    true,
    '/admin/reports/daily',
    'عرض التقرير',
    '{"report_date": "2024-01-15", "transactions": 1250, "volume": 2500000}'
);

-- Insert sample system alerts
INSERT INTO system_alerts (
    alert_type, severity, title, description, source_system, source_component,
    error_code, status, metadata
) VALUES 
(
    'performance',
    'warning',
    'استخدام عالي للذاكرة',
    'استخدام الذاكرة وصل إلى 85% على خادم التطبيق الرئيسي',
    'application_server',
    'memory_monitor',
    'MEM_HIGH_001',
    'active',
    '{"memory_usage": 85, "threshold": 80, "server": "app-server-01"}'
),
(
    'security',
    'critical',
    'محاولات دخول مشبوهة',
    'تم اكتشاف أكثر من 50 محاولة دخول فاشلة في آخر 10 دقائق',
    'authentication_service',
    'login_monitor',
    'AUTH_BRUTE_001',
    'active',
    '{"failed_attempts": 52, "time_window": "10_minutes", "source_ips": ["*************", "*********"]}'
),
(
    'database',
    'warning',
    'استعلامات بطيئة',
    'تم اكتشاف استعلامات تستغرق أكثر من 5 ثوانٍ',
    'database_server',
    'query_monitor',
    'DB_SLOW_001',
    'acknowledged',
    '{"slow_queries": 15, "avg_time": 7.2, "max_time": 12.5}'
),
(
    'integration',
    'error',
    'فشل في الاتصال ببوابة الدفع',
    'تعذر الاتصال ببوابة الدفع الخارجية',
    'payment_gateway',
    'connection_monitor',
    'PAY_CONN_001',
    'resolved',
    '{"gateway": "external_payment_api", "error_count": 5, "last_success": "2024-01-15T10:30:00Z"}'
);

-- Update alert with acknowledgment
UPDATE system_alerts 
SET acknowledged_by = 'user_admin_001',
    acknowledged_at = CURRENT_TIMESTAMP - INTERVAL '2 hours'
WHERE alert_type = 'database' AND severity = 'warning';

-- Update alert with resolution
UPDATE system_alerts 
SET resolved_by = 'user_admin_001',
    resolved_at = CURRENT_TIMESTAMP - INTERVAL '1 hour',
    resolution_notes = 'تم إعادة تشغيل خدمة بوابة الدفع وعاد الاتصال للعمل بشكل طبيعي',
    status = 'resolved'
WHERE alert_type = 'integration' AND severity = 'error';

-- Insert sample audit trail entries
INSERT INTO audit_trail (
    action, resource_type, resource_id, user_id, user_role, user_ip,
    old_values, new_values, changes_summary, api_endpoint, http_method, success
) VALUES 
(
    'user_status_update',
    'user',
    'user_customer_002',
    'user_admin_001',
    'admin',
    '*************',
    '{"is_active": false, "status": "inactive"}',
    '{"is_active": true, "status": "active"}',
    'تم تفعيل حساب المستخدم',
    '/api/v1/admin/users/user_customer_002/status',
    'PUT',
    true
),
(
    'setting_update',
    'system_setting',
    'financial.max_transaction_amount',
    'user_admin_001',
    'admin',
    '*************',
    '{"value": "50000.00"}',
    '{"value": "100000.00"}',
    'تم تحديث الحد الأقصى لمبلغ المعاملة',
    '/api/v1/admin/settings',
    'PUT',
    true
),
(
    'agent_approval',
    'agent_profile',
    'agent_test_123',
    'user_agent_manager_001',
    'agent_manager',
    '*************',
    '{"status": "pending"}',
    '{"status": "active", "approved_at": "2024-01-15T12:00:00Z"}',
    'تم الموافقة على طلب الوكيل',
    '/api/v1/agents/agent_test_123/approve',
    'PUT',
    true
),
(
    'transaction_create',
    'transaction',
    'txn_sample_001',
    'user_customer_001',
    'user',
    '*************',
    null,
    '{"amount": 1000.00, "type": "transfer", "status": "pending"}',
    'تم إنشاء معاملة جديدة',
    '/api/v1/transactions',
    'POST',
    true
);

-- Insert sample system health metrics
INSERT INTO system_health_metrics (
    metric_name, metric_value, metric_unit, component, instance_id,
    warning_threshold, critical_threshold, status, metadata
) VALUES 
(
    'cpu_usage',
    45.2,
    'percent',
    'application_server',
    'app-server-01',
    70.0,
    90.0,
    'normal',
    '{"cores": 8, "load_avg": 2.1}'
),
(
    'memory_usage',
    68.5,
    'percent',
    'application_server',
    'app-server-01',
    80.0,
    95.0,
    'normal',
    '{"total_gb": 32, "used_gb": 21.9, "available_gb": 10.1}'
),
(
    'disk_usage',
    35.8,
    'percent',
    'database_server',
    'db-server-01',
    80.0,
    95.0,
    'normal',
    '{"total_gb": 1000, "used_gb": 358, "available_gb": 642}'
),
(
    'response_time',
    125.5,
    'milliseconds',
    'api_gateway',
    'gateway-01',
    500.0,
    1000.0,
    'normal',
    '{"endpoint": "/api/v1/transactions", "method": "POST"}'
),
(
    'active_connections',
    245,
    'count',
    'database_server',
    'db-server-01',
    500,
    800,
    'normal',
    '{"max_connections": 1000, "idle_connections": 155}'
),
(
    'queue_size',
    12,
    'count',
    'message_queue',
    'queue-01',
    100,
    500,
    'normal',
    '{"queue_type": "transaction_processing", "processing_rate": 50}'
);

-- Mark some notifications as read
UPDATE admin_notifications 
SET is_read = true,
    read_at = CURRENT_TIMESTAMP - INTERVAL '1 hour',
    read_by = 'user_admin_001'
WHERE notification_type = 'success';

-- Log the seeding
INSERT INTO schema_migrations (version, name, file_path, checksum, execution_time_ms, success)
VALUES (
    'seed_003',
    'Admin Panel Data Seed',
    'database/seeds/003_admin_panel_data.sql',
    'admin_panel_seed_checksum',
    0,
    true
) ON CONFLICT (version) DO NOTHING;
