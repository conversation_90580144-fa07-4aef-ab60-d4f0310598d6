'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { authAPI } from '@/services/api/auth';
import { tokenStorage } from '@/utils/storage';

// أنواع البيانات
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  isVerified: boolean;
  is2FAEnabled: boolean;
  role: 'user' | 'agent' | 'admin';
  kycStatus: 'pending' | 'verified' | 'rejected';
  avatar?: string;
  createdAt: string;
  lastLoginAt?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  requires2FA: boolean;
  tempToken: string | null;
}

// أنواع الإجراءات
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; accessToken: string; refreshToken: string } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'AUTH_2FA_REQUIRED'; payload: { tempToken: string } }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'CLEAR_ERROR' };

// الحالة الأولية
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  requires2FA: false,
  tempToken: null,
};

// مخفض الحالة
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        requires2FA: false,
        tempToken: null,
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
        requires2FA: false,
        tempToken: null,
      };

    case 'AUTH_LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };

    case 'AUTH_2FA_REQUIRED':
      return {
        ...state,
        isLoading: false,
        requires2FA: true,
        tempToken: action.payload.tempToken,
        error: null,
      };

    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
}

// سياق المصادقة
const AuthContext = createContext<{
  state: AuthState;
  login: (email: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  verify2FA: (code: string) => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  clearError: () => void;
} | null>(null);

// أنواع بيانات التسجيل
export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  nationalId: string;
}

// مزود السياق
export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const router = useRouter();

  // التحقق من الرمز المميز عند تحميل التطبيق
  useEffect(() => {
    const initAuth = async () => {
      try {
        const token = tokenStorage.getAccessToken();
        if (token) {
          const response = await authAPI.getProfile();
          dispatch({
            type: 'AUTH_SUCCESS',
            payload: {
              user: response.data,
              accessToken: token,
              refreshToken: tokenStorage.getRefreshToken() || '',
            },
          });
        } else {
          dispatch({ type: 'AUTH_LOGOUT' });
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        tokenStorage.clearTokens();
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    };

    initAuth();
  }, []);

  // تسجيل الدخول
  const login = async (email: string, password: string) => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authAPI.login({ email, password });

      if (response.requires2FA) {
        dispatch({
          type: 'AUTH_2FA_REQUIRED',
          payload: { tempToken: response.tempToken },
        });
        toast.success('يرجى إدخال رمز المصادقة الثنائية');
        return;
      }

      // حفظ الرموز المميزة
      tokenStorage.setTokens(response.accessToken, response.refreshToken);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
        },
      });

      toast.success(`مرحباً ${response.user.firstName}!`);
      router.push('/dashboard');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'فشل في تسجيل الدخول';
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // التسجيل
  const register = async (data: RegisterData) => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authAPI.register(data);

      toast.success('تم التسجيل بنجاح! يرجى التحقق من رقم الهاتف');
      router.push('/auth/verify-phone');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'فشل في التسجيل';
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // تسجيل الخروج
  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      tokenStorage.clearTokens();
      dispatch({ type: 'AUTH_LOGOUT' });
      toast.success('تم تسجيل الخروج بنجاح');
      router.push('/');
    }
  };

  // التحقق من المصادقة الثنائية
  const verify2FA = async (code: string) => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authAPI.verify2FA({
        tempToken: state.tempToken!,
        code,
      });

      // حفظ الرموز المميزة
      tokenStorage.setTokens(response.accessToken, response.refreshToken);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
        },
      });

      toast.success(`مرحباً ${response.user.firstName}!`);
      router.push('/dashboard');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'رمز التحقق غير صحيح';
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // تجديد الرمز المميز
  const refreshToken = async () => {
    try {
      const refreshTokenValue = tokenStorage.getRefreshToken();
      if (!refreshTokenValue) {
        throw new Error('No refresh token');
      }

      const response = await authAPI.refreshToken();
      tokenStorage.setTokens(response.accessToken, response.refreshToken);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: state.user!,
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
        },
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      tokenStorage.clearTokens();
      dispatch({ type: 'AUTH_LOGOUT' });
      router.push('/auth/login');
    }
  };

  // تحديث الملف الشخصي
  const updateProfile = async (data: Partial<User>) => {
    try {
      const response = await authAPI.updateProfile(data);
      dispatch({ type: 'UPDATE_USER', payload: response.data });
      toast.success('تم تحديث الملف الشخصي بنجاح');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'فشل في تحديث الملف الشخصي';
      toast.error(errorMessage);
      throw error;
    }
  };

  // مسح الخطأ
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value = {
    state,
    login,
    register,
    logout,
    verify2FA,
    refreshToken,
    updateProfile,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// خطاف استخدام السياق
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
