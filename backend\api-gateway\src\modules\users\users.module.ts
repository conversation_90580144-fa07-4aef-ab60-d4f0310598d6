import { Module } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { KycService } from './services/kyc.service';
import { ProfileService } from './services/profile.service';
import { DocumentService } from './services/document.service';

@Module({
  controllers: [UsersController],
  providers: [
    UsersService,
    KycService,
    ProfileService,
    DocumentService,
  ],
  exports: [UsersService],
})
export class UsersModule {}
