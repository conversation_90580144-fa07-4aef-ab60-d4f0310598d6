name: ws_transfir
description: تطبيق WS Transfir للهواتف الذكية - نظام متقدم لتحويل الأموال والمدفوعات الرقمية
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.3
  bloc: ^8.1.2
  equatable: ^2.0.5
  hydrated_bloc: ^9.1.2

  # Navigation
  go_router: ^12.1.3
  auto_route: ^7.9.2

  # Network & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  connectivity_plus: ^5.0.2
  socket_io_client: ^2.0.3+1

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0

  # UI & Design
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_animate: ^4.3.0
  smooth_page_indicator: ^1.1.0
  pull_to_refresh: ^2.0.0

  # Forms & Validation
  reactive_forms: ^16.1.1
  form_builder_validators: ^9.1.0

  # Charts & Analytics
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^23.2.7

  # Camera & QR
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  image_picker: ^1.0.4
  camera: ^0.10.5+5

  # Biometrics & Security
  local_auth: ^2.1.7
  crypto: ^3.0.3
  encrypt: ^5.0.1

  # Notifications
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0
  awesome_notifications: ^0.8.2

  # Location & Maps
  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.0
  location: ^5.0.3

  # Utils
  intl: ^0.19.0
  url_launcher: ^6.2.2
  share_plus: ^7.2.1
  path_provider: ^2.1.1
  permission_handler: ^11.1.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  flutter_native_splash: ^2.3.6
  flutter_launcher_icons: ^0.13.1

  # Firebase
  firebase_core: ^2.24.2
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8
  firebase_performance: ^0.9.3+8

  # Development
  logger: ^2.0.2+1
  pretty_dio_logger: ^1.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  auto_route_generator: ^7.3.2

  # Testing
  bloc_test: ^9.1.5
  mocktail: ^1.0.1
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
    - assets/sounds/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#3B82F6"
    theme_color: "#3B82F6"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48

flutter_native_splash:
  color: "#3B82F6"
  image: assets/images/splash_logo.png
  color_dark: "#1E40AF"
  image_dark: assets/images/splash_logo_dark.png
  android_12:
    image: assets/images/splash_logo.png
    icon_background_color: "#3B82F6"
    image_dark: assets/images/splash_logo_dark.png
    icon_background_color_dark: "#1E40AF"
  web: false
