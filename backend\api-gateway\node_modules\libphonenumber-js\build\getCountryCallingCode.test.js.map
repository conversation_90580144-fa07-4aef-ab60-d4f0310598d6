{"version": 3, "file": "getCountryCallingCode.test.js", "names": ["describe", "it", "getCountryCallingCode", "metadata", "should", "equal", "expect", "to"], "sources": ["../source/getCountryCallingCode.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\nimport getCountryCallingCode from './getCountryCallingCode.js'\r\n\r\ndescribe('getCountryCallingCode', () => {\r\n\tit('should get country calling code', () => {\r\n\t\tgetCountryCallingCode('US', metadata).should.equal('1')\r\n\t})\r\n\r\n\tit('should throw if country is unknown', () => {\r\n\t\texpect(() => getCountryCallingCode('ZZ', metadata)).to.throw('Unknown country: ZZ')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEA;;;;AAEAA,QAAQ,CAAC,uBAAD,EAA0B,YAAM;EACvCC,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C,IAAAC,iCAAA,EAAsB,IAAtB,EAA4BC,uBAA5B,EAAsCC,MAAtC,CAA6CC,KAA7C,CAAmD,GAAnD;EACA,CAFC,CAAF;EAIAJ,EAAE,CAAC,oCAAD,EAAuC,YAAM;IAC9CK,MAAM,CAAC;MAAA,OAAM,IAAAJ,iCAAA,EAAsB,IAAtB,EAA4BC,uBAA5B,CAAN;IAAA,CAAD,CAAN,CAAoDI,EAApD,UAA6D,qBAA7D;EACA,CAFC,CAAF;AAGA,CARO,CAAR"}