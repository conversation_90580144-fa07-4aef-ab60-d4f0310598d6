import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateProfileDto } from './create-profile.dto';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsDateString,
  Length,
} from 'class-validator';

export class UpdateProfileDto extends PartialType(CreateProfileDto) {
  @ApiProperty({
    description: 'تاريخ آخر تحديث للملف الشخصي',
    example: '2024-01-15T10:30:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  lastUpdated?: string;

  @ApiProperty({
    description: 'ملاحظات إضافية',
    example: 'تم تحديث المعلومات المهنية',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  notes?: string;

  @ApiProperty({
    description: 'هل الملف الشخصي مكتمل',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isComplete?: boolean;

  @ApiProperty({
    description: 'هل تم التحقق من الملف الشخصي',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isVerified?: boolean;
}
