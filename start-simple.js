const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

// Create API server
const apiApp = express();
apiApp.use(cors());
apiApp.use(express.json());

// API endpoints
apiApp.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'WS Transfir API Server is running successfully',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    port: 3000
  });
});

apiApp.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  console.log('Login attempt:', email);
  
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      success: true,
      token: 'admin-jwt-token-mock-123',
      user: {
        id: '1',
        firstName: 'مدير',
        lastName: 'النظام',
        email: email,
        role: 'admin',
        isVerified: true
      }
    });
  } else if (email && password) {
    res.json({
      success: true,
      token: 'user-jwt-token-mock-456',
      user: {
        id: '2',
        firstName: 'أحمد',
        lastName: 'محمد',
        email: email,
        role: 'user',
        isVerified: true
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة'
    });
  }
});

apiApp.post('/api/auth/register', (req, res) => {
  const { firstName, lastName, email, password, phone } = req.body;
  console.log('Registration attempt:', { firstName, lastName, email, phone });
  
  res.json({
    success: true,
    message: 'تم إنشاء الحساب بنجاح',
    user: {
      id: Date.now().toString(),
      firstName,
      lastName,
      email,
      phone,
      isVerified: false
    }
  });
});

apiApp.get('/api/profile/me', (req, res) => {
  res.json({
    id: '1',
    firstName: 'أحمد',
    lastName: 'محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    dateOfBirth: '1990-01-01',
    nationality: 'SA',
    isVerified: true,
    completionPercentage: 85,
    address: {
      addressLine1: 'شارع الملك فهد',
      city: 'الرياض',
      country: 'SA',
      postalCode: '12345'
    },
    emergencyContact: {
      name: 'فاطمة محمد',
      phone: '+966501234568',
      relation: 'زوجة'
    }
  });
});

apiApp.get('/api/transfers', (req, res) => {
  const mockTransfers = [
    {
      id: '1',
      referenceNumber: 'WS20241225001',
      amount: '1,500.00',
      currency: 'SAR',
      receiverName: 'أحمد محمد',
      receiverCountry: 'مصر',
      status: 'completed',
      createdAt: '2024-12-25T10:30:00Z',
      estimatedDelivery: '2024-12-25T14:30:00Z',
      fees: '15.00'
    },
    {
      id: '2',
      referenceNumber: 'WS20241224002',
      amount: '750.00',
      currency: 'USD',
      receiverName: 'فاطمة علي',
      receiverCountry: 'الأردن',
      status: 'pending',
      createdAt: '2024-12-24T15:45:00Z',
      estimatedDelivery: '2024-12-26T15:45:00Z',
      fees: '12.50'
    },
    {
      id: '3',
      referenceNumber: 'WS20241223003',
      amount: '2,200.00',
      currency: 'SAR',
      receiverName: 'محمد حسن',
      receiverCountry: 'لبنان',
      status: 'processing',
      createdAt: '2024-12-23T09:15:00Z',
      estimatedDelivery: '2024-12-25T09:15:00Z',
      fees: '22.00'
    }
  ];
  
  res.json({
    data: mockTransfers,
    total: mockTransfers.length,
    page: 1,
    totalPages: 1
  });
});

apiApp.get('/api/transfers/stats', (req, res) => {
  res.json({
    totalTransfers: 24,
    totalAmount: '45,230.50',
    pendingTransfers: 3,
    completedTransfers: 21
  });
});

// Create frontend server
const frontendApp = express();
frontendApp.use(cors());

frontendApp.get('/', (req, res) => {
  try {
    const html = fs.readFileSync('simple-frontend.html', 'utf8');
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.send(html);
  } catch (error) {
    res.status(500).send('Error loading frontend: ' + error.message);
  }
});

frontendApp.get('/health', (req, res) => {
  res.json({ status: 'Frontend OK', timestamp: new Date().toISOString() });
});

// Start servers
const apiPort = 3000;
const frontendPort = 3100;

apiApp.listen(apiPort, '0.0.0.0', () => {
  console.log('');
  console.log('🚀 ═══════════════════════════════════════════════════════════');
  console.log('🚀 WS TRANSFIR SYSTEM RUNNING SUCCESSFULLY');
  console.log('🚀 ═══════════════════════════════════════════════════════════');
  console.log(`🔧 API Server: http://localhost:${apiPort}`);
  console.log(`📊 Health Check: http://localhost:${apiPort}/api/health`);
  console.log('🚀 ═══════════════════════════════════════════════════════════');
  console.log('');
});

frontendApp.listen(frontendPort, '0.0.0.0', () => {
  console.log('');
  console.log('🌐 ═══════════════════════════════════════════════════════════');
  console.log('🌐 WS TRANSFIR FRONTEND RUNNING');
  console.log('🌐 ═══════════════════════════════════════════════════════════');
  console.log(`📱 Frontend: http://localhost:${frontendPort}`);
  console.log(`🔐 Admin Login: <EMAIL> / admin123`);
  console.log(`👤 User Login: <EMAIL> / password123`);
  console.log('🌐 ═══════════════════════════════════════════════════════════');
  console.log('');
  console.log('✅ System is ready! Open http://localhost:3100 in your browser');
  console.log('');
});
