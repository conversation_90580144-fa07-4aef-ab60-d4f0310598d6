-- Migration: Create Notification System Tables
-- Description: إنشاء جداول نظام الإشعارات المتقدم
-- Version: 006
-- Created: 2024-01-16

-- Create notification templates table
CREATE TABLE IF NOT EXISTS notification_templates (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN (
        'transaction_success', 'transaction_failed', 'account_verification',
        'security_alert', 'payment_reminder', 'system_maintenance',
        'agent_commission', 'kyc_update', 'wallet_balance', 'promotional'
    )),
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('email', 'sms', 'push', 'in_app', 'webhook')),
    language VARCHAR(5) NOT NULL DEFAULT 'ar' CHECK (language IN ('ar', 'en', 'fr')),
    
    -- Template content
    subject_template TEXT NOT NULL,
    body_template TEXT NOT NULL,
    variables TEXT[] DEFAULT '{}',
    
    -- Metadata
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Constraints
    UNIQUE(notification_type, channel, language)
);

-- Create template variables table
CREATE TABLE IF NOT EXISTS template_variables (
    id VARCHAR(50) PRIMARY KEY,
    template_id VARCHAR(50) NOT NULL,
    variable_name VARCHAR(100) NOT NULL,
    variable_type VARCHAR(20) NOT NULL DEFAULT 'string' CHECK (variable_type IN ('string', 'number', 'date', 'boolean')),
    description TEXT,
    is_required BOOLEAN NOT NULL DEFAULT true,
    default_value TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_template_variables_template FOREIGN KEY (template_id) REFERENCES notification_templates(id) ON DELETE CASCADE,
    
    -- Constraints
    UNIQUE(template_id, variable_name)
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    notification_type VARCHAR(50) NOT NULL,
    channel VARCHAR(20) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    
    -- Template and content
    template_id VARCHAR(50),
    subject TEXT,
    body TEXT,
    
    -- Status and delivery
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'cancelled')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    
    -- Scheduling
    scheduled_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Data and metadata
    data JSONB DEFAULT '{}',
    provider_response JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_notifications_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_notifications_template FOREIGN KEY (template_id) REFERENCES notification_templates(id)
);

-- Create notification events table (for tracking opens, clicks, etc.)
CREATE TABLE IF NOT EXISTS notification_events (
    id VARCHAR(50) PRIMARY KEY,
    notification_id VARCHAR(50) NOT NULL,
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('opened', 'clicked', 'bounced', 'unsubscribed')),
    
    -- Event details
    ip_address INET,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    location_info JSONB DEFAULT '{}',
    
    -- Timestamps
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Foreign keys
    CONSTRAINT fk_notification_events_notification FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE
);

-- Create scheduled notifications table
CREATE TABLE IF NOT EXISTS scheduled_notifications (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Schedule configuration
    schedule_type VARCHAR(20) NOT NULL CHECK (schedule_type IN ('one_time', 'recurring', 'conditional')),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'cancelled', 'failed')),
    
    -- Notification details
    notification_type VARCHAR(50) NOT NULL,
    channel VARCHAR(20) NOT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    template_id VARCHAR(50),
    
    -- Recipient and data
    notification_data JSONB DEFAULT '{}',
    recipient_data JSONB DEFAULT '{}',
    
    -- Scheduling details
    scheduled_at TIMESTAMP WITH TIME ZONE,
    cron_expression VARCHAR(100),
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    
    -- Execution tracking
    max_executions INTEGER,
    execution_count INTEGER NOT NULL DEFAULT 0,
    last_executed_at TIMESTAMP WITH TIME ZONE,
    next_execution_at TIMESTAMP WITH TIME ZONE,
    last_error TEXT,
    
    -- Conditional execution
    condition_query TEXT,
    condition_params JSONB DEFAULT '{}',
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Foreign keys
    CONSTRAINT fk_scheduled_notifications_template FOREIGN KEY (template_id) REFERENCES notification_templates(id),
    CONSTRAINT fk_scheduled_notifications_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_scheduled_notifications_updated_by FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- Create in-app notifications table
CREATE TABLE IF NOT EXISTS in_app_notifications (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    
    -- Notification details
    notification_type VARCHAR(50),
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    action_url VARCHAR(500),
    action_label VARCHAR(100),
    
    -- Status
    is_read BOOLEAN NOT NULL DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Data and metadata
    data JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign keys
    CONSTRAINT fk_in_app_notifications_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create notification preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    
    -- Channel preferences
    email_enabled BOOLEAN NOT NULL DEFAULT true,
    sms_enabled BOOLEAN NOT NULL DEFAULT true,
    push_enabled BOOLEAN NOT NULL DEFAULT true,
    in_app_enabled BOOLEAN NOT NULL DEFAULT true,
    
    -- Type preferences
    transaction_notifications BOOLEAN NOT NULL DEFAULT true,
    security_notifications BOOLEAN NOT NULL DEFAULT true,
    marketing_notifications BOOLEAN NOT NULL DEFAULT false,
    system_notifications BOOLEAN NOT NULL DEFAULT true,
    
    -- Timing preferences
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    
    -- Language preference
    preferred_language VARCHAR(5) DEFAULT 'ar',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_notification_preferences_user FOREIGN KEY (user_id) REFERENCES users(id),
    
    -- Constraints
    UNIQUE(user_id)
);

-- Create notification costs table (for analytics)
CREATE TABLE IF NOT EXISTS notification_costs (
    id VARCHAR(50) PRIMARY KEY,
    channel VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    
    -- Cost metrics
    total_notifications INTEGER NOT NULL DEFAULT 0,
    total_cost DECIMAL(10,4) NOT NULL DEFAULT 0,
    cost_per_notification DECIMAL(10,6) NOT NULL DEFAULT 0,
    
    -- Provider details
    provider_name VARCHAR(100),
    currency VARCHAR(3) DEFAULT 'SAR',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(channel, date, provider_name)
);

-- Create notification delivery logs table
CREATE TABLE IF NOT EXISTS notification_delivery_logs (
    id VARCHAR(50) PRIMARY KEY,
    notification_id VARCHAR(50) NOT NULL,
    
    -- Delivery attempt details
    attempt_number INTEGER NOT NULL DEFAULT 1,
    status VARCHAR(20) NOT NULL,
    
    -- Provider details
    provider_name VARCHAR(100),
    provider_message_id VARCHAR(200),
    provider_response JSONB DEFAULT '{}',
    
    -- Timing
    attempted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP WITH TIME ZONE,
    
    -- Error details
    error_code VARCHAR(50),
    error_message TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Foreign keys
    CONSTRAINT fk_delivery_logs_notification FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE
);

-- Create indexes for better performance

-- Notification templates indexes
CREATE INDEX idx_notification_templates_type_channel ON notification_templates(notification_type, channel);
CREATE INDEX idx_notification_templates_is_active ON notification_templates(is_active);
CREATE INDEX idx_notification_templates_created_at ON notification_templates(created_at);

-- Template variables indexes
CREATE INDEX idx_template_variables_template_id ON template_variables(template_id);
CREATE INDEX idx_template_variables_name ON template_variables(variable_name);

-- Notifications indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_channel ON notifications(channel);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_sent_at ON notifications(sent_at);
CREATE INDEX idx_notifications_scheduled_at ON notifications(scheduled_at);
CREATE INDEX idx_notifications_user_type ON notifications(user_id, notification_type);
CREATE INDEX idx_notifications_user_channel ON notifications(user_id, channel);

-- Notification events indexes
CREATE INDEX idx_notification_events_notification_id ON notification_events(notification_id);
CREATE INDEX idx_notification_events_type ON notification_events(event_type);
CREATE INDEX idx_notification_events_opened_at ON notification_events(opened_at);
CREATE INDEX idx_notification_events_clicked_at ON notification_events(clicked_at);

-- Scheduled notifications indexes
CREATE INDEX idx_scheduled_notifications_status ON scheduled_notifications(status);
CREATE INDEX idx_scheduled_notifications_type ON scheduled_notifications(schedule_type);
CREATE INDEX idx_scheduled_notifications_next_execution ON scheduled_notifications(next_execution_at);
CREATE INDEX idx_scheduled_notifications_created_by ON scheduled_notifications(created_by);

-- In-app notifications indexes
CREATE INDEX idx_in_app_notifications_user_id ON in_app_notifications(user_id);
CREATE INDEX idx_in_app_notifications_is_read ON in_app_notifications(is_read);
CREATE INDEX idx_in_app_notifications_created_at ON in_app_notifications(created_at);
CREATE INDEX idx_in_app_notifications_expires_at ON in_app_notifications(expires_at);

-- Notification preferences indexes
CREATE INDEX idx_notification_preferences_user_id ON notification_preferences(user_id);

-- Notification costs indexes
CREATE INDEX idx_notification_costs_channel_date ON notification_costs(channel, date);
CREATE INDEX idx_notification_costs_date ON notification_costs(date);

-- Delivery logs indexes
CREATE INDEX idx_delivery_logs_notification_id ON notification_delivery_logs(notification_id);
CREATE INDEX idx_delivery_logs_status ON notification_delivery_logs(status);
CREATE INDEX idx_delivery_logs_attempted_at ON notification_delivery_logs(attempted_at);

-- Create GIN indexes for JSONB fields
CREATE INDEX idx_notifications_data ON notifications USING GIN(data);
CREATE INDEX idx_notifications_provider_response ON notifications USING GIN(provider_response);
CREATE INDEX idx_notification_events_device_info ON notification_events USING GIN(device_info);
CREATE INDEX idx_notification_events_location_info ON notification_events USING GIN(location_info);
CREATE INDEX idx_notification_events_metadata ON notification_events USING GIN(metadata);
CREATE INDEX idx_scheduled_notifications_notification_data ON scheduled_notifications USING GIN(notification_data);
CREATE INDEX idx_scheduled_notifications_recipient_data ON scheduled_notifications USING GIN(recipient_data);
CREATE INDEX idx_scheduled_notifications_condition_params ON scheduled_notifications USING GIN(condition_params);
CREATE INDEX idx_in_app_notifications_data ON in_app_notifications USING GIN(data);
CREATE INDEX idx_delivery_logs_provider_response ON notification_delivery_logs USING GIN(provider_response);
CREATE INDEX idx_delivery_logs_metadata ON notification_delivery_logs USING GIN(metadata);

-- Create functions for ID generation
CREATE OR REPLACE FUNCTION generate_notification_template_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'tpl_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_template_variable_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'var_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_notification_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'notif_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_notification_event_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'event_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_scheduled_notification_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'sched_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_in_app_notification_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'inapp_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_notification_preference_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'pref_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_notification_cost_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'cost_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_delivery_log_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'log_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for ID generation
CREATE TRIGGER generate_notification_template_id_trigger
    BEFORE INSERT ON notification_templates
    FOR EACH ROW
    EXECUTE FUNCTION generate_notification_template_id();

CREATE TRIGGER generate_template_variable_id_trigger
    BEFORE INSERT ON template_variables
    FOR EACH ROW
    EXECUTE FUNCTION generate_template_variable_id();

CREATE TRIGGER generate_notification_id_trigger
    BEFORE INSERT ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION generate_notification_id();

CREATE TRIGGER generate_notification_event_id_trigger
    BEFORE INSERT ON notification_events
    FOR EACH ROW
    EXECUTE FUNCTION generate_notification_event_id();

CREATE TRIGGER generate_scheduled_notification_id_trigger
    BEFORE INSERT ON scheduled_notifications
    FOR EACH ROW
    EXECUTE FUNCTION generate_scheduled_notification_id();

CREATE TRIGGER generate_in_app_notification_id_trigger
    BEFORE INSERT ON in_app_notifications
    FOR EACH ROW
    EXECUTE FUNCTION generate_in_app_notification_id();

CREATE TRIGGER generate_notification_preference_id_trigger
    BEFORE INSERT ON notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION generate_notification_preference_id();

CREATE TRIGGER generate_notification_cost_id_trigger
    BEFORE INSERT ON notification_costs
    FOR EACH ROW
    EXECUTE FUNCTION generate_notification_cost_id();

CREATE TRIGGER generate_delivery_log_id_trigger
    BEFORE INSERT ON notification_delivery_logs
    FOR EACH ROW
    EXECUTE FUNCTION generate_delivery_log_id();

-- Create triggers for updated_at
CREATE TRIGGER update_notification_templates_updated_at
    BEFORE UPDATE ON notification_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scheduled_notifications_updated_at
    BEFORE UPDATE ON scheduled_notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at
    BEFORE UPDATE ON notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create notification preferences for new users
CREATE OR REPLACE FUNCTION create_default_notification_preferences()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO notification_preferences (user_id)
    VALUES (NEW.id)
    ON CONFLICT (user_id) DO NOTHING;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for default notification preferences
CREATE TRIGGER create_default_notification_preferences_trigger
    AFTER INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_notification_preferences();

-- Create function to clean up expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired in-app notifications
    DELETE FROM in_app_notifications 
    WHERE expires_at IS NOT NULL AND expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Update expired scheduled notifications
    UPDATE scheduled_notifications 
    SET status = 'completed'
    WHERE status = 'active' 
    AND scheduled_at IS NOT NULL 
    AND scheduled_at < CURRENT_TIMESTAMP - INTERVAL '7 days'
    AND schedule_type = 'one_time';
    
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Add comments for documentation
COMMENT ON TABLE notification_templates IS 'جدول قوالب الإشعارات - يحتوي على قوالب الإشعارات المختلفة';
COMMENT ON COLUMN notification_templates.subject_template IS 'قالب عنوان الإشعار مع المتغيرات';
COMMENT ON COLUMN notification_templates.body_template IS 'قالب محتوى الإشعار مع المتغيرات';
COMMENT ON COLUMN notification_templates.variables IS 'قائمة المتغيرات المستخدمة في القالب';

COMMENT ON TABLE notifications IS 'جدول الإشعارات - يحتوي على جميع الإشعارات المرسلة';
COMMENT ON COLUMN notifications.data IS 'بيانات الإشعار بتنسيق JSON';
COMMENT ON COLUMN notifications.provider_response IS 'استجابة مقدم الخدمة بتنسيق JSON';

COMMENT ON TABLE scheduled_notifications IS 'جدول الإشعارات المجدولة - إدارة الإشعارات المؤجلة والمتكررة';
COMMENT ON COLUMN scheduled_notifications.cron_expression IS 'تعبير الجدولة للإشعارات المتكررة';
COMMENT ON COLUMN scheduled_notifications.condition_query IS 'استعلام الشرط للإشعارات المشروطة';

COMMENT ON TABLE in_app_notifications IS 'جدول الإشعارات داخل التطبيق - الإشعارات المعروضة في التطبيق';
COMMENT ON TABLE notification_preferences IS 'جدول تفضيلات الإشعارات - إعدادات المستخدم للإشعارات';
COMMENT ON TABLE notification_events IS 'جدول أحداث الإشعارات - تتبع فتح ونقر الإشعارات';

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON notification_templates TO ws_app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON template_variables TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON notifications TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON notification_events TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON scheduled_notifications TO ws_app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON in_app_notifications TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON notification_preferences TO ws_app_user;
GRANT SELECT, INSERT ON notification_costs TO ws_app_user;
GRANT SELECT, INSERT ON notification_delivery_logs TO ws_app_user;
