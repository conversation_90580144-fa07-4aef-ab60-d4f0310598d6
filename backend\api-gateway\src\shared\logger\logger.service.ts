import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

export interface LogContext {
  userId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  [key: string]: any;
}

@Injectable()
export class LoggerService implements NestLoggerService {
  private context = 'Application';

  /**
   * تسجيل رسالة معلومات
   */
  log(message: string, context?: LogContext) {
    this.writeLog(LogLevel.INFO, message, context);
  }

  /**
   * تسجيل رسالة خطأ
   */
  error(message: string, trace?: string, context?: LogContext) {
    this.writeLog(LogLevel.ERROR, message, { ...context, trace });
  }

  /**
   * تسجيل رسالة تحذير
   */
  warn(message: string, context?: LogContext) {
    this.writeLog(LogLevel.WARN, message, context);
  }

  /**
   * تسجيل رسالة تصحيح
   */
  debug(message: string, context?: LogContext) {
    if (process.env.NODE_ENV === 'development') {
      this.writeLog(LogLevel.DEBUG, message, context);
    }
  }

  /**
   * تسجيل نشاط المستخدم
   */
  logUserActivity(
    userId: string,
    action: string,
    details?: any,
    context?: LogContext,
  ) {
    this.log(`User Activity: ${action}`, {
      ...context,
      userId,
      action,
      details,
      type: 'user_activity',
    });
  }

  /**
   * تسجيل عملية مالية
   */
  logFinancialTransaction(
    transactionId: string,
    type: string,
    amount: number,
    currency: string,
    userId: string,
    context?: LogContext,
  ) {
    this.log(`Financial Transaction: ${type}`, {
      ...context,
      transactionId,
      type,
      amount,
      currency,
      userId,
      category: 'financial',
    });
  }

  /**
   * تسجيل محاولة أمنية مشبوهة
   */
  logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details?: any,
    context?: LogContext,
  ) {
    const level = severity === 'critical' || severity === 'high' 
      ? LogLevel.ERROR 
      : LogLevel.WARN;

    this.writeLog(level, `Security Event: ${event}`, {
      ...context,
      event,
      severity,
      details,
      category: 'security',
    });
  }

  /**
   * تسجيل أداء النظام
   */
  logPerformance(
    operation: string,
    duration: number,
    context?: LogContext,
  ) {
    this.log(`Performance: ${operation}`, {
      ...context,
      operation,
      duration,
      category: 'performance',
    });
  }

  /**
   * تسجيل طلب API
   */
  logApiRequest(
    method: string,
    url: string,
    statusCode: number,
    responseTime: number,
    userId?: string,
    context?: LogContext,
  ) {
    const level = statusCode >= 400 ? LogLevel.ERROR : LogLevel.INFO;
    
    this.writeLog(level, `API Request: ${method} ${url}`, {
      ...context,
      method,
      url,
      statusCode,
      responseTime,
      userId,
      category: 'api',
    });
  }

  /**
   * تسجيل خطأ في قاعدة البيانات
   */
  logDatabaseError(
    operation: string,
    error: Error,
    context?: LogContext,
  ) {
    this.error(`Database Error: ${operation}`, error.stack, {
      ...context,
      operation,
      error: error.message,
      category: 'database',
    });
  }

  /**
   * تسجيل خطأ خارجي
   */
  logExternalServiceError(
    service: string,
    operation: string,
    error: Error,
    context?: LogContext,
  ) {
    this.error(`External Service Error: ${service}`, error.stack, {
      ...context,
      service,
      operation,
      error: error.message,
      category: 'external_service',
    });
  }

  private writeLog(level: LogLevel, message: string, context?: LogContext) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      context: this.context,
      ...context,
    };

    // في البيئة التطويرية، اطبع في الكونسول
    if (process.env.NODE_ENV === 'development') {
      this.consoleLog(level, message, logEntry);
    }

    // في الإنتاج، احفظ في قاعدة البيانات أو خدمة خارجية
    if (process.env.NODE_ENV === 'production') {
      this.persistLog(logEntry);
    }
  }

  private consoleLog(level: LogLevel, message: string, logEntry: any) {
    const colors = {
      [LogLevel.ERROR]: '\x1b[31m', // أحمر
      [LogLevel.WARN]: '\x1b[33m',  // أصفر
      [LogLevel.INFO]: '\x1b[36m',  // سماوي
      [LogLevel.DEBUG]: '\x1b[37m', // أبيض
    };

    const reset = '\x1b[0m';
    const color = colors[level] || colors[LogLevel.INFO];

    console.log(
      `${color}[${logEntry.timestamp}] [${level.toUpperCase()}] ${message}${reset}`,
      logEntry.context ? JSON.stringify(logEntry.context, null, 2) : '',
    );
  }

  private async persistLog(logEntry: any) {
    try {
      // TODO: حفظ في MongoDB أو خدمة خارجية مثل ELK Stack
      // يمكن استخدام Winston أو Pino للتسجيل المتقدم
      
      // مثال: إرسال إلى MongoDB
      // await this.mongoService.insertLog(logEntry);
      
      // مثال: إرسال إلى خدمة خارجية
      // await this.externalLogService.send(logEntry);
      
    } catch (error) {
      // في حالة فشل التسجيل، اطبع في الكونسول كبديل
      console.error('فشل في حفظ السجل:', error);
      this.consoleLog(logEntry.level, logEntry.message, logEntry);
    }
  }

  setContext(context: string) {
    this.context = context;
  }
}
