{"version": 3, "file": "fetch.js", "sourceRoot": "", "sources": ["../../../../../src/internal/observable/dom/fetch.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,oCAAoC,CAAC;AAC9E,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AA4FvD,MAAM,UAAU,SAAS,CACvB,KAAuB,EACvB,mBAEI,EAAE;IAEN,MAAM,EAAE,QAAQ,KAAc,gBAAgB,EAAzB,IAAI,UAAK,gBAAgB,EAAxC,YAAqB,CAAmB,CAAC;IAC/C,OAAO,IAAI,UAAU,CAAe,CAAC,UAAU,EAAE,EAAE;QAKjD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;QAK9B,IAAI,SAAS,GAAG,IAAI,CAAC;QAKrB,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QACrC,IAAI,WAAW,EAAE;YACf,IAAI,WAAW,CAAC,OAAO,EAAE;gBACvB,UAAU,CAAC,KAAK,EAAE,CAAC;aACpB;iBAAM;gBAGL,MAAM,kBAAkB,GAAG,GAAG,EAAE;oBAC9B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;wBACnB,UAAU,CAAC,KAAK,EAAE,CAAC;qBACpB;gBACH,CAAC,CAAC;gBACF,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;gBAC1D,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC,CAAC;aACpF;SACF;QAOD,MAAM,iBAAiB,mCAAqB,IAAI,KAAE,MAAM,GAAE,CAAC;QAE3D,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAE,EAAE;YAC/B,SAAS,GAAG,KAAK,CAAC;YAClB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC;QAEF,KAAK,CAAC,KAAK,EAAE,iBAAiB,CAAC;aAC5B,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjB,IAAI,QAAQ,EAAE;gBAIZ,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CACrC,wBAAwB,CACtB,UAAU,EAEV,SAAS,EAET,GAAG,EAAE;oBACH,SAAS,GAAG,KAAK,CAAC;oBAClB,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,CAAC,EACD,WAAW,CACZ,CACF,CAAC;aACH;iBAAM;gBACL,SAAS,GAAG,KAAK,CAAC;gBAClB,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1B,UAAU,CAAC,QAAQ,EAAE,CAAC;aACvB;QACH,CAAC,CAAC;aACD,KAAK,CAAC,WAAW,CAAC,CAAC;QAEtB,OAAO,GAAG,EAAE;YACV,IAAI,SAAS,EAAE;gBACb,UAAU,CAAC,KAAK,EAAE,CAAC;aACpB;QACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}