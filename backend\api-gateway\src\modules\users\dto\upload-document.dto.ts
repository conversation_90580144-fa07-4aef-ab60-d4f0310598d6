import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, IsOptional } from 'class-validator';

export enum DocumentType {
  NATIONAL_ID = 'national_id',
  PASSPORT = 'passport',
  DRIVING_LICENSE = 'driving_license',
  UTILITY_BILL = 'utility_bill',
  BANK_STATEMENT = 'bank_statement',
  SALARY_CERTIFICATE = 'salary_certificate',
  EMPLOYMENT_LETTER = 'employment_letter',
  SELFIE = 'selfie',
  SIGNATURE = 'signature',
  OTHER = 'other',
}

export enum DocumentSide {
  FRONT = 'front',
  BACK = 'back',
  BOTH = 'both',
}

export class UploadDocumentDto {
  @ApiProperty({
    description: 'نوع الوثيقة',
    enum: DocumentType,
    example: DocumentType.NATIONAL_ID,
  })
  @IsEnum(DocumentType, { message: 'نوع الوثيقة غير صحيح' })
  @IsNotEmpty({ message: 'نوع الوثيقة مطلوب' })
  documentType: DocumentType;

  @ApiProperty({
    description: 'جانب الوثيقة',
    enum: DocumentSide,
    example: DocumentSide.FRONT,
    required: false,
  })
  @IsOptional()
  @IsEnum(DocumentSide, { message: 'جانب الوثيقة غير صحيح' })
  documentSide?: DocumentSide;

  @ApiProperty({
    description: 'وصف الوثيقة',
    example: 'صورة الهوية الوطنية - الوجه الأمامي',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'وصف الوثيقة يجب أن يكون نص' })
  description?: string;

  @ApiProperty({
    description: 'رقم الوثيقة',
    example: '1234567890',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الوثيقة يجب أن يكون نص' })
  documentNumber?: string;

  @ApiProperty({
    description: 'تاريخ انتهاء الوثيقة',
    example: '2030-12-31',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'تاريخ انتهاء الوثيقة يجب أن يكون نص' })
  expiryDate?: string;

  @ApiProperty({
    description: 'بلد إصدار الوثيقة',
    example: 'SA',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'بلد إصدار الوثيقة يجب أن يكون نص' })
  issuingCountry?: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'ملف الوثيقة',
  })
  file: any;
}
