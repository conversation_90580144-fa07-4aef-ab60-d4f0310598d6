#!/usr/bin/env python3
"""
Environment Setup Script
========================
سكريبت إعداد البيئة التطويرية
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path
from typing import List, Dict, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Project configuration
PROJECT_ROOT = Path(__file__).parent
PYTHON_VERSION = "3.11"
REQUIRED_PYTHON_VERSION = (3, 8)

def check_python_version():
    """فحص إصدار Python"""
    current_version = sys.version_info[:2]
    
    if current_version < REQUIRED_PYTHON_VERSION:
        logger.error(f"Python {REQUIRED_PYTHON_VERSION[0]}.{REQUIRED_PYTHON_VERSION[1]}+ required, got {current_version[0]}.{current_version[1]}")
        return False
    
    logger.info(f"✅ Python {current_version[0]}.{current_version[1]} detected")
    return True

def run_command(cmd: List[str], cwd: Optional[Path] = None, check: bool = True) -> bool:
    """تشغيل أمر وإرجاع النتيجة"""
    logger.info(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd or PROJECT_ROOT,
            capture_output=True,
            text=True,
            check=check
        )
        
        if result.stdout:
            logger.debug(result.stdout)
        
        if result.stderr and result.returncode != 0:
            logger.error(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        if e.stderr:
            logger.error(e.stderr)
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

def create_virtual_environment():
    """إنشاء البيئة الافتراضية"""
    venv_path = PROJECT_ROOT / ".venv"
    
    if venv_path.exists():
        logger.info("Virtual environment already exists")
        return True
    
    logger.info("🔧 Creating virtual environment...")
    
    # Create virtual environment
    if not run_command([sys.executable, "-m", "venv", str(venv_path)]):
        logger.error("Failed to create virtual environment")
        return False
    
    logger.info("✅ Virtual environment created")
    return True

def get_venv_python():
    """الحصول على مسار Python في البيئة الافتراضية"""
    venv_path = PROJECT_ROOT / ".venv"
    
    if platform.system() == "Windows":
        return venv_path / "Scripts" / "python.exe"
    else:
        return venv_path / "bin" / "python"

def get_venv_pip():
    """الحصول على مسار pip في البيئة الافتراضية"""
    venv_path = PROJECT_ROOT / ".venv"
    
    if platform.system() == "Windows":
        return venv_path / "Scripts" / "pip.exe"
    else:
        return venv_path / "bin" / "pip"

def upgrade_pip():
    """ترقية pip"""
    logger.info("📦 Upgrading pip...")
    
    pip_path = get_venv_pip()
    
    if not run_command([str(pip_path), "install", "--upgrade", "pip"]):
        logger.error("Failed to upgrade pip")
        return False
    
    logger.info("✅ pip upgraded")
    return True

def install_dependencies():
    """تثبيت التبعيات"""
    logger.info("📦 Installing dependencies...")
    
    pip_path = get_venv_pip()
    requirements_file = PROJECT_ROOT / "requirements.txt"
    
    if not requirements_file.exists():
        logger.warning("requirements.txt not found, installing basic dependencies")
        basic_deps = [
            "fastapi>=0.104.1",
            "uvicorn[standard]>=0.24.0",
            "pydantic>=2.5.0",
            "asyncpg>=0.29.0",
            "aiohttp>=3.9.0",
            "PyJWT>=2.8.0",
            "cryptography>=41.0.0",
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
            "black>=23.10.0",
            "flake8>=6.1.0",
            "mypy>=1.7.0"
        ]
        
        for dep in basic_deps:
            if not run_command([str(pip_path), "install", dep]):
                logger.error(f"Failed to install {dep}")
                return False
    else:
        if not run_command([str(pip_path), "install", "-r", str(requirements_file)]):
            logger.error("Failed to install requirements")
            return False
    
    logger.info("✅ Dependencies installed")
    return True

def install_dev_dependencies():
    """تثبيت تبعيات التطوير"""
    logger.info("🛠️ Installing development dependencies...")
    
    pip_path = get_venv_pip()
    dev_deps = [
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.1.0",
        "pytest-mock>=3.12.0",
        "pytest-html>=4.1.0",
        "pytest-json-report>=1.5.0",
        "pytest-xdist>=3.5.0",
        "black>=23.10.0",
        "flake8>=6.1.0",
        "mypy>=1.7.0",
        "isort>=5.12.0",
        "pre-commit>=3.5.0",
        "bandit>=1.7.5",
        "safety>=2.3.0",
        "jupyter>=1.0.0",
        "ipython>=8.17.0"
    ]
    
    for dep in dev_deps:
        if not run_command([str(pip_path), "install", dep], check=False):
            logger.warning(f"Failed to install {dep} (optional)")
    
    logger.info("✅ Development dependencies installed")
    return True

def setup_pre_commit():
    """إعداد pre-commit hooks"""
    logger.info("🔧 Setting up pre-commit hooks...")
    
    venv_path = PROJECT_ROOT / ".venv"
    
    if platform.system() == "Windows":
        pre_commit_path = venv_path / "Scripts" / "pre-commit.exe"
    else:
        pre_commit_path = venv_path / "bin" / "pre-commit"
    
    if not pre_commit_path.exists():
        logger.warning("pre-commit not installed, skipping hooks setup")
        return True
    
    # Install pre-commit hooks
    if not run_command([str(pre_commit_path), "install"], check=False):
        logger.warning("Failed to install pre-commit hooks (optional)")
    
    logger.info("✅ Pre-commit hooks setup")
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    logger.info("📁 Creating project directories...")
    
    directories = [
        "reports",
        "logs",
        "htmlcov",
        "docs/_build",
        "database/backups",
        "config/local",
        "frontend/web-app/build",
        "frontend/mobile-app/build"
    ]
    
    for directory in directories:
        dir_path = PROJECT_ROOT / directory
        dir_path.mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ Directories created")
    return True

def create_env_file():
    """إنشاء ملف البيئة"""
    env_file = PROJECT_ROOT / ".env"
    
    if env_file.exists():
        logger.info(".env file already exists")
        return True
    
    logger.info("📝 Creating .env file...")
    
    env_content = """# WalletSystem Environment Configuration
# =====================================

# Environment
ENVIRONMENT=development
DEBUG=true

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/ws_transfir
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis (optional)
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=WalletSystem
VERSION=1.0.0

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# Email (optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Monitoring (optional)
SENTRY_DSN=your-sentry-dsn-here

# File Storage (optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_BUCKET_NAME=your-bucket-name

# External APIs
BANK_API_KEY=your-bank-api-key
PAYMENT_GATEWAY_API_KEY=your-payment-gateway-key
"""
    
    try:
        with open(env_file, "w", encoding="utf-8") as f:
            f.write(env_content)
        
        logger.info("✅ .env file created")
        logger.warning("⚠️  Please update the .env file with your actual configuration")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create .env file: {e}")
        return False

def display_next_steps():
    """عرض الخطوات التالية"""
    logger.info("\n🎉 Environment setup completed!")
    
    venv_path = PROJECT_ROOT / ".venv"
    
    if platform.system() == "Windows":
        activate_cmd = f"{venv_path}\\Scripts\\activate"
    else:
        activate_cmd = f"source {venv_path}/bin/activate"
    
    print(f"""
📋 Next Steps:
==============

1. Activate virtual environment:
   {activate_cmd}

2. Update .env file with your configuration:
   nano .env

3. Set up your database:
   python database/migrate.py

4. Seed initial data:
   python database/seed.py

5. Run tests:
   python run_tests.py

6. Start development server:
   python -m uvicorn backend.main:app --reload

7. Access the application:
   - API: http://localhost:8000
   - Docs: http://localhost:8000/docs
   - Admin: http://localhost:8000/admin

📚 Documentation:
   - README: README_NEW.md
   - API Docs: http://localhost:8000/docs
   - Coverage: htmlcov/index.html

🔧 Development Commands:
   - Format code: black backend tests
   - Lint code: flake8 backend tests
   - Type check: mypy backend
   - Run tests: python run_tests.py
   - Pre-commit: pre-commit run --all-files
""")

def main():
    """الدالة الرئيسية"""
    logger.info("🚀 Setting up WalletSystem development environment...")
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Create virtual environment
    if not create_virtual_environment():
        return 1
    
    # Upgrade pip
    if not upgrade_pip():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Install development dependencies
    if not install_dev_dependencies():
        return 1
    
    # Setup pre-commit
    if not setup_pre_commit():
        return 1
    
    # Create directories
    if not create_directories():
        return 1
    
    # Create .env file
    if not create_env_file():
        return 1
    
    # Display next steps
    display_next_steps()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
