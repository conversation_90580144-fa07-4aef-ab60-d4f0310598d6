{"version": 3, "file": "isValid.test.js", "names": ["metadata", "type", "_isValidNumber", "parsePhoneNumber", "isValidNumber", "v2", "parameters", "length", "push", "undefined", "extract", "apply", "describe", "it", "should", "equal", "defaultCountry", "country", "phone", "phoneNumberTypePatterns", "countries", "UZ", "thrower"], "sources": ["../source/isValid.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport _isValidNumber from './isValid.js'\r\nimport parsePhoneNumber from './parsePhoneNumber.js'\r\n\r\nfunction isValidNumber(...parameters) {\r\n\tlet v2\r\n\tif (parameters.length < 1) {\r\n\t\t// `input` parameter.\r\n\t\tparameters.push(undefined)\r\n\t} else {\r\n\t\t// Convert string `input` to a `PhoneNumber` instance.\r\n\t\tif (typeof parameters[0] === 'string') {\r\n\t\t\tv2 = true\r\n\t\t\tparameters[0] = parsePhoneNumber(parameters[0], {\r\n\t\t\t\t...parameters[1],\r\n\t\t\t\textract: false\r\n\t\t\t}, metadata)\r\n\t\t}\r\n\t}\r\n\tif (parameters.length < 2) {\r\n\t\t// `options` parameter.\r\n\t\tparameters.push(undefined)\r\n\t}\r\n\t// Set `v2` flag.\r\n\tparameters[1] = {\r\n\t\tv2,\r\n\t\t...parameters[1]\r\n\t}\r\n\t// Add `metadata` parameter.\r\n\tparameters.push(metadata)\r\n\t// Call the function.\r\n\treturn _isValidNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('validate', () => {\r\n\tit('should validate phone numbers', () => {\r\n\t\tisValidNumber('******-373-4253').should.equal(true)\r\n\t\tisValidNumber('******-373').should.equal(false)\r\n\r\n\t\tisValidNumber('******-373-4253', undefined).should.equal(true)\r\n\r\n\t\tisValidNumber('(*************', { defaultCountry: 'US' }).should.equal(true)\r\n\t\tisValidNumber('(213) 37', { defaultCountry: 'US' }).should.equal(false)\r\n\r\n\t\tisValidNumber({ country: 'US', phone: '2133734253' }).should.equal(true)\r\n\r\n\t\t// No \"types\" info: should return `true`.\r\n\t\tisValidNumber('+380972423740').should.equal(true)\r\n\r\n\t\tisValidNumber('0912345678', { defaultCountry: 'TW' }).should.equal(true)\r\n\r\n\t\t// Moible numbers starting 07624* are Isle of Man\r\n\t\t// which has its own \"country code\" \"IM\"\r\n\t\t// which is in the \"GB\" \"country calling code\" zone.\r\n\t\t// So while this number is for \"IM\" it's still supposed to\r\n\t\t// be valid when passed \"GB\" as a default country.\r\n\t\tisValidNumber('07624369230', { defaultCountry: 'GB' }).should.equal(true)\r\n\t})\r\n\r\n\tit('should refine phone number validation in case extended regular expressions are set for a country', () => {\r\n\t\t// Germany general validation must pass\r\n\t\tisValidNumber('961111111', { defaultCountry: 'UZ' }).should.equal(true)\r\n\r\n\t\tconst phoneNumberTypePatterns = metadata.countries.UZ[11]\r\n\r\n\t\t// Different regular expressions for precise national number validation.\r\n\t\t// `types` index in compressed array is `9` for v1.\r\n\t\t// For v2 it's 10.\r\n\t\t// For v3 it's 11.\r\n\t\tmetadata.countries.UZ[11] =\r\n\t\t[\r\n\t\t\t[\"(?:6(?:1(?:22|3[124]|4[1-4]|5[123578]|64)|2(?:22|3[0-57-9]|41)|5(?:22|3[3-7]|5[024-8])|6\\\\d{2}|7(?:[23]\\\\d|7[69])|9(?:22|4[1-8]|6[135]))|7(?:0(?:5[4-9]|6[0146]|7[12456]|9[135-8])|1[12]\\\\d|2(?:22|3[1345789]|4[123579]|5[14])|3(?:2\\\\d|3[1578]|4[1-35-7]|5[1-57]|61)|4(?:2\\\\d|3[1-579]|7[1-79])|5(?:22|5[1-9]|6[1457])|6(?:22|3[12457]|4[13-8])|9(?:22|5[1-9])))\\\\d{5}\"],\r\n\t\t\t[\"6(?:1(?:2(?:98|2[01])|35[0-4]|50\\\\d|61[23]|7(?:[01][017]|4\\\\d|55|9[5-9]))|2(?:11\\\\d|2(?:[12]1|9[01379])|5(?:[126]\\\\d|3[0-4])|7\\\\d{2})|5(?:19[01]|2(?:27|9[26])|30\\\\d|59\\\\d|7\\\\d{2})|6(?:2(?:1[5-9]|2[0367]|38|41|52|60)|3[79]\\\\d|4(?:56|83)|7(?:[07]\\\\d|1[017]|3[07]|4[047]|5[057]|67|8[0178]|9[79])|9[0-3]\\\\d)|7(?:2(?:24|3[237]|4[5-9]|7[15-8])|5(?:7[12]|8[0589])|7(?:0\\\\d|[39][07])|9(?:0\\\\d|7[079]))|9(?:2(?:1[1267]|5\\\\d|3[01]|7[0-4])|5[67]\\\\d|6(?:2[0-26]|8\\\\d)|7\\\\d{2}))\\\\d{4}|7(?:0\\\\d{3}|1(?:13[01]|6(?:0[47]|1[67]|66)|71[3-69]|98\\\\d)|2(?:2(?:2[79]|95)|3(?:2[5-9]|6[0-6])|57\\\\d|7(?:0\\\\d|1[17]|2[27]|3[37]|44|5[057]|66|88))|3(?:2(?:1[0-6]|21|3[469]|7[159])|33\\\\d|5(?:0[0-4]|5[579]|9\\\\d)|7(?:[0-3579]\\\\d|4[0467]|6[67]|8[078])|9[4-6]\\\\d)|4(?:2(?:29|5[0257]|6[0-7]|7[1-57])|5(?:1[0-4]|8\\\\d|9[5-9])|7(?:0\\\\d|1[024589]|2[0127]|3[0137]|[46][07]|5[01]|7[5-9]|9[079])|9(?:7[015-9]|[89]\\\\d))|5(?:112|2(?:0\\\\d|2[29]|[49]4)|3[1568]\\\\d|52[6-9]|7(?:0[01578]|1[017]|[23]7|4[047]|[5-7]\\\\d|8[78]|9[079]))|6(?:2(?:2[1245]|4[2-4])|39\\\\d|41[179]|5(?:[349]\\\\d|5[0-2])|7(?:0[017]|[13]\\\\d|22|44|55|67|88))|9(?:22[128]|3(?:2[0-4]|7\\\\d)|57[05629]|7(?:2[05-9]|3[37]|4\\\\d|60|7[2579]|87|9[07])))\\\\d{4}|9[0-57-9]\\\\d{7}\"]\r\n\t\t]\r\n\r\n\t\t// Extended validation must not pass for an invalid phone number\r\n\t\tisValidNumber('961111111', { defaultCountry: 'UZ' }).should.equal(false)\r\n\r\n\t\t// Extended validation must pass for a valid phone number\r\n\t\tisValidNumber('912345678', { defaultCountry: 'UZ' }).should.equal(true)\r\n\r\n\t\tmetadata.countries.UZ[11] = phoneNumberTypePatterns\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// No metadata\r\n\t\tlet thrower = () => _isValidNumber('+78005553535')\r\n\t\tthrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// // Non-phone-number characters in a phone number\r\n\t\t// isValidNumber('+499821958a').should.equal(false)\r\n\t\t// isValidNumber('88005553535x', { defaultCountry: 'RU' }).should.equal(false)\r\n\r\n\t\t// Doesn't have `types` regexps in default metadata.\r\n\t\tisValidNumber({ country: 'UA', phone: '300000000' }).should.equal(true)\r\n\t\tisValidNumber({ country: 'UA', phone: '200000000' }).should.equal(false)\r\n\r\n\t\t// // Numerical `value`\r\n\t\t// thrower = () => isValidNumber(88005553535, { defaultCountry: 'RU' })\r\n\t\t// thrower.should.throw('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\t\t// Long country phone code\r\n\t\tisValidNumber('+3725555555').should.equal(true)\r\n\r\n\t\t// // Invalid country\r\n\t\t// thrower = () => isValidNumber({ phone: '8005553535', country: 'RUS' })\r\n\t\t// thrower.should.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should accept phone number extensions', () => {\r\n\t\t// International\r\n\t\tisValidNumber('+12133734253 ext. 123').should.equal(true)\r\n\t\t// National\r\n\t\tisValidNumber('88005553535 x123', { defaultCountry: 'RU' }).should.equal(true)\r\n\t})\r\n\r\n\tit('should validate non-geographic toll-free phone numbers', () => {\r\n\t\tisValidNumber('+80074454123').should.equal(true)\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA,OAAOA,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AACA,OAAOC,cAAP,MAA2B,cAA3B;AACA,OAAOC,gBAAP,MAA6B,uBAA7B;;AAEA,SAASC,aAAT,GAAsC;EACrC,IAAIC,EAAJ;;EADqC,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAErC,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAxB,EAA2B;IAC1B;IACAD,UAAU,CAACE,IAAX,CAAgBC,SAAhB;EACA,CAHD,MAGO;IACN;IACA,IAAI,OAAOH,UAAU,CAAC,CAAD,CAAjB,KAAyB,QAA7B,EAAuC;MACtCD,EAAE,GAAG,IAAL;MACAC,UAAU,CAAC,CAAD,CAAV,GAAgBH,gBAAgB,CAACG,UAAU,CAAC,CAAD,CAAX,kCAC5BA,UAAU,CAAC,CAAD,CADkB;QAE/BI,OAAO,EAAE;MAFsB,IAG7BV,QAH6B,CAAhC;IAIA;EACD;;EACD,IAAIM,UAAU,CAACC,MAAX,GAAoB,CAAxB,EAA2B;IAC1B;IACAD,UAAU,CAACE,IAAX,CAAgBC,SAAhB;EACA,CAlBoC,CAmBrC;;;EACAH,UAAU,CAAC,CAAD,CAAV;IACCD,EAAE,EAAFA;EADD,GAEIC,UAAU,CAAC,CAAD,CAFd,EApBqC,CAwBrC;;EACAA,UAAU,CAACE,IAAX,CAAgBR,QAAhB,EAzBqC,CA0BrC;;EACA,OAAOE,cAAc,CAACS,KAAf,CAAqB,IAArB,EAA2BL,UAA3B,CAAP;AACA;;AAEDM,QAAQ,CAAC,UAAD,EAAa,YAAM;EAC1BC,EAAE,CAAC,+BAAD,EAAkC,YAAM;IACzCT,aAAa,CAAC,iBAAD,CAAb,CAAiCU,MAAjC,CAAwCC,KAAxC,CAA8C,IAA9C;IACAX,aAAa,CAAC,YAAD,CAAb,CAA4BU,MAA5B,CAAmCC,KAAnC,CAAyC,KAAzC;IAEAX,aAAa,CAAC,iBAAD,EAAoBK,SAApB,CAAb,CAA4CK,MAA5C,CAAmDC,KAAnD,CAAyD,IAAzD;IAEAX,aAAa,CAAC,gBAAD,EAAmB;MAAEY,cAAc,EAAE;IAAlB,CAAnB,CAAb,CAA0DF,MAA1D,CAAiEC,KAAjE,CAAuE,IAAvE;IACAX,aAAa,CAAC,UAAD,EAAa;MAAEY,cAAc,EAAE;IAAlB,CAAb,CAAb,CAAoDF,MAApD,CAA2DC,KAA3D,CAAiE,KAAjE;IAEAX,aAAa,CAAC;MAAEa,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAD,CAAb,CAAsDJ,MAAtD,CAA6DC,KAA7D,CAAmE,IAAnE,EATyC,CAWzC;;IACAX,aAAa,CAAC,eAAD,CAAb,CAA+BU,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C;IAEAX,aAAa,CAAC,YAAD,EAAe;MAAEY,cAAc,EAAE;IAAlB,CAAf,CAAb,CAAsDF,MAAtD,CAA6DC,KAA7D,CAAmE,IAAnE,EAdyC,CAgBzC;IACA;IACA;IACA;IACA;;IACAX,aAAa,CAAC,aAAD,EAAgB;MAAEY,cAAc,EAAE;IAAlB,CAAhB,CAAb,CAAuDF,MAAvD,CAA8DC,KAA9D,CAAoE,IAApE;EACA,CAtBC,CAAF;EAwBAF,EAAE,CAAC,kGAAD,EAAqG,YAAM;IAC5G;IACAT,aAAa,CAAC,WAAD,EAAc;MAAEY,cAAc,EAAE;IAAlB,CAAd,CAAb,CAAqDF,MAArD,CAA4DC,KAA5D,CAAkE,IAAlE;IAEA,IAAMI,uBAAuB,GAAGnB,QAAQ,CAACoB,SAAT,CAAmBC,EAAnB,CAAsB,EAAtB,CAAhC,CAJ4G,CAM5G;IACA;IACA;IACA;;IACArB,QAAQ,CAACoB,SAAT,CAAmBC,EAAnB,CAAsB,EAAtB,IACA,CACC,CAAC,yWAAD,CADD,EAEC,CAAC,mqCAAD,CAFD,CADA,CAV4G,CAgB5G;;IACAjB,aAAa,CAAC,WAAD,EAAc;MAAEY,cAAc,EAAE;IAAlB,CAAd,CAAb,CAAqDF,MAArD,CAA4DC,KAA5D,CAAkE,KAAlE,EAjB4G,CAmB5G;;IACAX,aAAa,CAAC,WAAD,EAAc;MAAEY,cAAc,EAAE;IAAlB,CAAd,CAAb,CAAqDF,MAArD,CAA4DC,KAA5D,CAAkE,IAAlE;IAEAf,QAAQ,CAACoB,SAAT,CAAmBC,EAAnB,CAAsB,EAAtB,IAA4BF,uBAA5B;EACA,CAvBC,CAAF;EAyBAN,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACA,IAAIS,OAAO,GAAG,SAAVA,OAAU;MAAA,OAAMpB,cAAc,CAAC,cAAD,CAApB;IAAA,CAAd;;IACAoB,OAAO,CAACR,MAAR,UAAqB,gCAArB,EAHqC,CAKrC;IACA;IACA;IAEA;;IACAV,aAAa,CAAC;MAAEa,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAD,CAAb,CAAqDJ,MAArD,CAA4DC,KAA5D,CAAkE,IAAlE;IACAX,aAAa,CAAC;MAAEa,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAD,CAAb,CAAqDJ,MAArD,CAA4DC,KAA5D,CAAkE,KAAlE,EAXqC,CAarC;IACA;IACA;IAEA;;IACAX,aAAa,CAAC,aAAD,CAAb,CAA6BU,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C,EAlBqC,CAoBrC;IACA;IACA;EACA,CAvBC,CAAF;EAyBAF,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjD;IACAT,aAAa,CAAC,uBAAD,CAAb,CAAuCU,MAAvC,CAA8CC,KAA9C,CAAoD,IAApD,EAFiD,CAGjD;;IACAX,aAAa,CAAC,kBAAD,EAAqB;MAAEY,cAAc,EAAE;IAAlB,CAArB,CAAb,CAA4DF,MAA5D,CAAmEC,KAAnE,CAAyE,IAAzE;EACA,CALC,CAAF;EAOAF,EAAE,CAAC,wDAAD,EAA2D,YAAM;IAClET,aAAa,CAAC,cAAD,CAAb,CAA8BU,MAA9B,CAAqCC,KAArC,CAA2C,IAA3C;EACA,CAFC,CAAF;AAGA,CArFO,CAAR"}