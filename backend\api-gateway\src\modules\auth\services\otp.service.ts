import { Injectable, BadRequestException } from '@nestjs/common';
import { OtpType } from '../dto/verify-otp.dto';

@Injectable()
export class OtpService {
  /**
   * إرسال رمز التحقق للتسجيل
   */
  async sendVerificationOtp(userId: string, phone: string) {
    const otp = this.generateOTP();
    
    // حفظ الرمز في Redis
    await this.storeOTP(userId, otp, OtpType.VERIFICATION, 600); // 10 دقائق
    
    // إرسال SMS
    await this.sendSMS(phone, otp);
    
    return { message: 'تم إرسال رمز التحقق' };
  }

  /**
   * إرسال رمز إعادة تعيين كلمة المرور
   */
  async sendPasswordResetOtp(userId: string, phone: string) {
    const otp = this.generateOTP();
    
    // حفظ الرمز في Redis
    await this.storeOTP(userId, otp, OtpType.PASSWORD_RESET, 300); // 5 دقائق
    
    // إرسال SMS
    await this.sendSMS(phone, otp);
    
    return { message: 'تم إرسال رمز إعادة التعيين' };
  }

  /**
   * التحقق من رمز OTP
   */
  async verifyOtp(userId: string, otp: string, type: OtpType): Promise<boolean> {
    const storedOtp = await this.getStoredOTP(userId, type);
    
    if (!storedOtp) {
      return false;
    }

    const isValid = storedOtp === otp;
    
    if (isValid) {
      // حذف الرمز بعد الاستخدام
      await this.deleteOTP(userId, type);
      
      // تسجيل الاستخدام الناجح
      await this.logOTPUsage(userId, type, true);
    } else {
      // تسجيل المحاولة الفاشلة
      await this.logOTPUsage(userId, type, false);
      
      // زيادة عداد المحاولات الفاشلة
      await this.incrementFailedAttempts(userId, type);
    }

    return isValid;
  }

  /**
   * إرسال SMS
   */
  async sendSMS(phone: string, message: string) {
    try {
      // TODO: تكامل مع خدمة SMS (مثل Twilio أو AWS SNS)
      console.log(`إرسال SMS إلى ${phone}: ${message}`);
      
      // محاكاة الإرسال
      await this.delay(1000);
      
      // تسجيل الإرسال
      await this.logSMSSent(phone, message);
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في إرسال SMS:', error);
      throw new BadRequestException('فشل في إرسال رسالة التحقق');
    }
  }

  /**
   * إرسال Email
   */
  async sendEmail(email: string, message: string) {
    try {
      // TODO: تكامل مع خدمة البريد الإلكتروني
      console.log(`إرسال بريد إلكتروني إلى ${email}: ${message}`);
      
      // محاكاة الإرسال
      await this.delay(1000);
      
      // تسجيل الإرسال
      await this.logEmailSent(email, message);
      
      return { success: true };
    } catch (error) {
      console.error('خطأ في إرسال البريد الإلكتروني:', error);
      throw new BadRequestException('فشل في إرسال بريد التحقق');
    }
  }

  /**
   * التحقق من حد المحاولات
   */
  async checkRateLimit(userId: string, type: OtpType): Promise<boolean> {
    const attempts = await this.getFailedAttempts(userId, type);
    const maxAttempts = this.getMaxAttempts(type);
    
    return attempts < maxAttempts;
  }

  /**
   * إعادة إرسال رمز OTP
   */
  async resendOtp(userId: string, type: OtpType) {
    // التحقق من حد الإرسال
    const canResend = await this.checkResendLimit(userId, type);
    if (!canResend) {
      throw new BadRequestException('تم تجاوز حد إعادة الإرسال. يرجى المحاولة لاحقاً');
    }

    const user = await this.findUserById(userId);
    if (!user) {
      throw new BadRequestException('المستخدم غير موجود');
    }

    switch (type) {
      case OtpType.VERIFICATION:
        return await this.sendVerificationOtp(userId, user.phone);
      
      case OtpType.PASSWORD_RESET:
        return await this.sendPasswordResetOtp(userId, user.phone);
      
      default:
        throw new BadRequestException('نوع OTP غير مدعوم');
    }
  }

  // Private Methods

  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private async storeOTP(userId: string, otp: string, type: OtpType, ttl: number) {
    const key = `otp:${userId}:${type}`;
    // TODO: حفظ في Redis مع TTL
    console.log(`حفظ OTP: ${key} = ${otp} (TTL: ${ttl}s)`);
  }

  private async getStoredOTP(userId: string, type: OtpType): Promise<string | null> {
    const key = `otp:${userId}:${type}`;
    // TODO: الحصول من Redis
    console.log(`الحصول على OTP: ${key}`);
    return '123456'; // مثال للاختبار
  }

  private async deleteOTP(userId: string, type: OtpType) {
    const key = `otp:${userId}:${type}`;
    // TODO: حذف من Redis
    console.log(`حذف OTP: ${key}`);
  }

  private async incrementFailedAttempts(userId: string, type: OtpType) {
    const key = `otp_failed:${userId}:${type}`;
    // TODO: زيادة العداد في Redis
    console.log(`زيادة المحاولات الفاشلة: ${key}`);
  }

  private async getFailedAttempts(userId: string, type: OtpType): Promise<number> {
    const key = `otp_failed:${userId}:${type}`;
    // TODO: الحصول من Redis
    return 0;
  }

  private getMaxAttempts(type: OtpType): number {
    switch (type) {
      case OtpType.VERIFICATION:
        return 5;
      case OtpType.PASSWORD_RESET:
        return 3;
      case OtpType.TWO_FACTOR:
        return 3;
      default:
        return 3;
    }
  }

  private async checkResendLimit(userId: string, type: OtpType): Promise<boolean> {
    const key = `otp_resend:${userId}:${type}`;
    // TODO: التحقق من حد إعادة الإرسال في Redis
    return true;
  }

  private async findUserById(userId: string) {
    // TODO: البحث في قاعدة البيانات
    return {
      id: userId,
      phone: '+966501234567',
      email: '<EMAIL>',
    };
  }

  private async logOTPUsage(userId: string, type: OtpType, success: boolean) {
    // TODO: تسجيل استخدام OTP في قاعدة البيانات
    console.log(`OTP Usage: ${userId} - ${type} - ${success ? 'نجح' : 'فشل'}`);
  }

  private async logSMSSent(phone: string, message: string) {
    // TODO: تسجيل إرسال SMS
    console.log(`SMS Sent to ${phone}: ${message}`);
  }

  private async logEmailSent(email: string, message: string) {
    // TODO: تسجيل إرسال البريد الإلكتروني
    console.log(`Email Sent to ${email}: ${message}`);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
