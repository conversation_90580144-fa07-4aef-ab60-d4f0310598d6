import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsArray,
  IsEnum,
  IsOptional,
  IsDateString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum KycLevel {
  BASIC = 'basic',
  ENHANCED = 'enhanced',
  PREMIUM = 'premium',
}

export enum IdentityDocumentType {
  NATIONAL_ID = 'national_id',
  PASSPORT = 'passport',
  DRIVING_LICENSE = 'driving_license',
}

export class IdentityDocumentDto {
  @ApiProperty({
    description: 'نوع وثيقة الهوية',
    enum: IdentityDocumentType,
    example: IdentityDocumentType.NATIONAL_ID,
  })
  @IsEnum(IdentityDocumentType, { message: 'نوع وثيقة الهوية غير صحيح' })
  @IsNotEmpty({ message: 'نوع وثيقة الهوية مطلوب' })
  type: IdentityDocumentType;

  @ApiProperty({
    description: 'رقم الوثيقة',
    example: '**********',
  })
  @IsString({ message: 'رقم الوثيقة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم الوثيقة مطلوب' })
  number: string;

  @ApiProperty({
    description: 'تاريخ الإصدار',
    example: '2020-01-01',
  })
  @IsDateString({}, { message: 'تاريخ الإصدار غير صحيح' })
  @IsNotEmpty({ message: 'تاريخ الإصدار مطلوب' })
  issueDate: string;

  @ApiProperty({
    description: 'تاريخ انتهاء الصلاحية',
    example: '2030-01-01',
  })
  @IsDateString({}, { message: 'تاريخ انتهاء الصلاحية غير صحيح' })
  @IsNotEmpty({ message: 'تاريخ انتهاء الصلاحية مطلوب' })
  expiryDate: string;

  @ApiProperty({
    description: 'بلد الإصدار',
    example: 'SA',
  })
  @IsString({ message: 'بلد الإصدار يجب أن يكون نص' })
  @IsNotEmpty({ message: 'بلد الإصدار مطلوب' })
  issuingCountry: string;

  @ApiProperty({
    description: 'معرف الوثيقة المرفوعة',
    example: 'doc_123456789',
  })
  @IsString({ message: 'معرف الوثيقة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'معرف الوثيقة مطلوب' })
  documentId: string;
}

export class AddressVerificationDto {
  @ApiProperty({
    description: 'نوع وثيقة إثبات العنوان',
    example: 'utility_bill',
  })
  @IsString({ message: 'نوع وثيقة إثبات العنوان يجب أن يكون نص' })
  @IsNotEmpty({ message: 'نوع وثيقة إثبات العنوان مطلوب' })
  documentType: string;

  @ApiProperty({
    description: 'معرف الوثيقة المرفوعة',
    example: 'doc_987654321',
  })
  @IsString({ message: 'معرف الوثيقة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'معرف الوثيقة مطلوب' })
  documentId: string;

  @ApiProperty({
    description: 'تاريخ الوثيقة',
    example: '2024-01-01',
  })
  @IsDateString({}, { message: 'تاريخ الوثيقة غير صحيح' })
  @IsNotEmpty({ message: 'تاريخ الوثيقة مطلوب' })
  documentDate: string;
}

export class IncomeVerificationDto {
  @ApiProperty({
    description: 'نوع وثيقة إثبات الدخل',
    example: 'salary_certificate',
  })
  @IsString({ message: 'نوع وثيقة إثبات الدخل يجب أن يكون نص' })
  @IsNotEmpty({ message: 'نوع وثيقة إثبات الدخل مطلوب' })
  documentType: string;

  @ApiProperty({
    description: 'معرف الوثيقة المرفوعة',
    example: 'doc_555666777',
  })
  @IsString({ message: 'معرف الوثيقة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'معرف الوثيقة مطلوب' })
  documentId: string;

  @ApiProperty({
    description: 'الدخل الشهري المعلن',
    example: 15000,
  })
  @IsNotEmpty({ message: 'الدخل الشهري مطلوب' })
  monthlyIncome: number;

  @ApiProperty({
    description: 'مصدر الدخل',
    example: 'راتب شهري',
  })
  @IsString({ message: 'مصدر الدخل يجب أن يكون نص' })
  @IsNotEmpty({ message: 'مصدر الدخل مطلوب' })
  incomeSource: string;
}

export class KycSubmissionDto {
  @ApiProperty({
    description: 'مستوى KYC المطلوب',
    enum: KycLevel,
    example: KycLevel.ENHANCED,
  })
  @IsEnum(KycLevel, { message: 'مستوى KYC غير صحيح' })
  @IsNotEmpty({ message: 'مستوى KYC مطلوب' })
  kycLevel: KycLevel;

  @ApiProperty({
    description: 'وثيقة الهوية',
    type: IdentityDocumentDto,
  })
  @ValidateNested()
  @Type(() => IdentityDocumentDto)
  @IsNotEmpty({ message: 'وثيقة الهوية مطلوبة' })
  identityDocument: IdentityDocumentDto;

  @ApiProperty({
    description: 'معرف صورة السيلفي',
    example: 'doc_selfie_123',
  })
  @IsString({ message: 'معرف صورة السيلفي يجب أن يكون نص' })
  @IsNotEmpty({ message: 'صورة السيلفي مطلوبة' })
  selfieDocumentId: string;

  @ApiProperty({
    description: 'إثبات العنوان (مطلوب للمستوى المحسن وما فوق)',
    type: AddressVerificationDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressVerificationDto)
  addressVerification?: AddressVerificationDto;

  @ApiProperty({
    description: 'إثبات الدخل (مطلوب للمستوى المتميز)',
    type: IncomeVerificationDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => IncomeVerificationDto)
  incomeVerification?: IncomeVerificationDto;

  @ApiProperty({
    description: 'الغرض من استخدام الخدمة',
    example: 'تحويل أموال للعائلة',
  })
  @IsString({ message: 'الغرض من استخدام الخدمة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'الغرض من استخدام الخدمة مطلوب' })
  purposeOfService: string;

  @ApiProperty({
    description: 'المبلغ المتوقع للتحويلات الشهرية',
    example: 5000,
  })
  @IsNotEmpty({ message: 'المبلغ المتوقع للتحويلات الشهرية مطلوب' })
  expectedMonthlyVolume: number;

  @ApiProperty({
    description: 'البلدان المتوقع التحويل إليها',
    example: ['US', 'IN', 'EG'],
  })
  @IsArray({ message: 'البلدان المتوقعة يجب أن تكون مصفوفة' })
  @IsString({ each: true, message: 'كل بلد يجب أن يكون نص' })
  @IsNotEmpty({ message: 'البلدان المتوقعة مطلوبة' })
  expectedDestinationCountries: string[];

  @ApiProperty({
    description: 'معلومات إضافية',
    example: 'أعمل في مجال التقنية وأحتاج لتحويل أموال بانتظام',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'المعلومات الإضافية يجب أن تكون نص' })
  additionalInfo?: string;

  @ApiProperty({
    description: 'الموافقة على الشروط والأحكام',
    example: true,
  })
  @IsNotEmpty({ message: 'الموافقة على الشروط والأحكام مطلوبة' })
  termsAccepted: boolean;

  @ApiProperty({
    description: 'الموافقة على سياسة الخصوصية',
    example: true,
  })
  @IsNotEmpty({ message: 'الموافقة على سياسة الخصوصية مطلوبة' })
  privacyPolicyAccepted: boolean;
}
