'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  EyeIcon, 
  EyeSlashIcon,
  ArrowRightIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Link from 'next/link';

// مخطط التحقق
const registerSchema = z.object({
  firstName: z
    .string()
    .min(1, 'الاسم الأول مطلوب')
    .min(2, 'الاسم الأول يجب أن يكون حرفين على الأقل')
    .max(50, 'الاسم الأول طويل جداً'),
  lastName: z
    .string()
    .min(1, 'الاسم الأخير مطلوب')
    .min(2, 'الاسم الأخير يجب أن يكون حرفين على الأقل')
    .max(50, 'الاسم الأخير طويل جداً'),
  email: z
    .string()
    .min(1, 'البريد الإلكتروني مطلوب')
    .email('البريد الإلكتروني غير صحيح'),
  phone: z
    .string()
    .min(1, 'رقم الهاتف مطلوب')
    .regex(/^\+966[0-9]{9}$/, 'رقم الهاتف يجب أن يبدأ بـ +966 ويتكون من 13 رقم'),
  nationalId: z
    .string()
    .min(1, 'رقم الهوية مطلوب')
    .regex(/^[0-9]{10}$/, 'رقم الهوية يجب أن يتكون من 10 أرقام'),
  password: z
    .string()
    .min(1, 'كلمة المرور مطلوبة')
    .min(8, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم'),
  confirmPassword: z
    .string()
    .min(1, 'تأكيد كلمة المرور مطلوب'),
  acceptTerms: z
    .boolean()
    .refine(val => val === true, 'يجب الموافقة على الشروط والأحكام'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'كلمات المرور غير متطابقة',
  path: ['confirmPassword'],
});

type RegisterFormData = z.infer<typeof registerSchema>;

// متطلبات كلمة المرور
const passwordRequirements = [
  { key: 'length', label: '8 أحرف على الأقل', regex: /.{8,}/ },
  { key: 'lowercase', label: 'حرف صغير', regex: /[a-z]/ },
  { key: 'uppercase', label: 'حرف كبير', regex: /[A-Z]/ },
  { key: 'number', label: 'رقم', regex: /\d/ },
];

export default function RegisterPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const router = useRouter();
  const { register: registerUser, state: authState } = useAuth();
  const { t, isRTL } = useLanguage();

  // إعداد النموذج
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
    setError,
    trigger,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    mode: 'onChange',
  });

  const watchedPassword = watch('password', '');

  // إعادة توجيه المستخدمين المسجلين
  useEffect(() => {
    if (authState.isAuthenticated) {
      router.push('/dashboard');
    }
  }, [authState.isAuthenticated, router]);

  // التحقق من متطلبات كلمة المرور
  const checkPasswordRequirement = (requirement: typeof passwordRequirements[0]) => {
    return requirement.regex.test(watchedPassword);
  };

  // الانتقال للخطوة التالية
  const nextStep = async () => {
    const fieldsToValidate = currentStep === 1 
      ? ['firstName', 'lastName', 'email'] 
      : ['phone', 'nationalId'];
    
    const isValid = await trigger(fieldsToValidate as any);
    if (isValid) {
      setCurrentStep(currentStep + 1);
    }
  };

  // العودة للخطوة السابقة
  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  // معالجة التسجيل
  const onSubmit = async (data: RegisterFormData) => {
    try {
      await registerUser({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        nationalId: data.nationalId,
        password: data.password,
      });
    } catch (error: any) {
      if (error.response?.status === 409) {
        setError('root', {
          message: 'البريد الإلكتروني أو رقم الهاتف مستخدم بالفعل',
        });
      } else {
        setError('root', {
          message: error.message || 'حدث خطأ أثناء التسجيل',
        });
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* الشعار */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-700 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold">WS</span>
            </div>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              WS Transfir
            </span>
          </div>
        </motion.div>

        {/* مؤشر التقدم */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mb-8"
        >
          <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors duration-200 ${
                    step <= currentStep
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                  }`}
                >
                  {step < currentStep ? (
                    <CheckCircleIcon className="w-5 h-5" />
                  ) : (
                    step
                  )}
                </div>
                {step < 3 && (
                  <div
                    className={`w-12 h-0.5 mx-2 transition-colors duration-200 ${
                      step < currentStep
                        ? 'bg-primary-600'
                        : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </motion.div>

        {/* بطاقة التسجيل */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="p-8">
            {/* العنوان */}
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {currentStep === 1 && 'المعلومات الشخصية'}
                {currentStep === 2 && 'معلومات الاتصال'}
                {currentStep === 3 && 'كلمة المرور والأمان'}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {currentStep === 1 && 'أدخل اسمك وبريدك الإلكتروني'}
                {currentStep === 2 && 'أدخل رقم هاتفك ورقم الهوية'}
                {currentStep === 3 && 'اختر كلمة مرور قوية'}
              </p>
            </div>

            {/* رسالة الخطأ العامة */}
            {errors.root && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mb-4 p-4 bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-lg flex items-center space-x-3 rtl:space-x-reverse"
              >
                <ExclamationTriangleIcon className="w-5 h-5 text-error-600 dark:text-error-400 flex-shrink-0" />
                <span className="text-error-700 dark:text-error-300 text-sm">
                  {errors.root.message}
                </span>
              </motion.div>
            )}

            {/* النموذج */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* الخطوة الأولى: المعلومات الشخصية */}
              {currentStep === 1 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-4"
                >
                  {/* الاسم الأول */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      الاسم الأول
                    </label>
                    <input
                      {...register('firstName')}
                      type="text"
                      className={`input ${errors.firstName ? 'input-error' : ''}`}
                      placeholder="أحمد"
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                        {errors.firstName.message}
                      </p>
                    )}
                  </div>

                  {/* الاسم الأخير */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      الاسم الأخير
                    </label>
                    <input
                      {...register('lastName')}
                      type="text"
                      className={`input ${errors.lastName ? 'input-error' : ''}`}
                      placeholder="محمد"
                    />
                    {errors.lastName && (
                      <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                        {errors.lastName.message}
                      </p>
                    )}
                  </div>

                  {/* البريد الإلكتروني */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      البريد الإلكتروني
                    </label>
                    <input
                      {...register('email')}
                      type="email"
                      className={`input ${errors.email ? 'input-error' : ''}`}
                      placeholder="<EMAIL>"
                      dir="ltr"
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                        {errors.email.message}
                      </p>
                    )}
                  </div>
                </motion.div>
              )}

              {/* الخطوة الثانية: معلومات الاتصال */}
              {currentStep === 2 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-4"
                >
                  {/* رقم الهاتف */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      رقم الهاتف
                    </label>
                    <input
                      {...register('phone')}
                      type="tel"
                      className={`input ${errors.phone ? 'input-error' : ''}`}
                      placeholder="+966501234567"
                      dir="ltr"
                    />
                    {errors.phone && (
                      <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                        {errors.phone.message}
                      </p>
                    )}
                  </div>

                  {/* رقم الهوية */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      رقم الهوية الوطنية
                    </label>
                    <input
                      {...register('nationalId')}
                      type="text"
                      className={`input ${errors.nationalId ? 'input-error' : ''}`}
                      placeholder="1234567890"
                      dir="ltr"
                    />
                    {errors.nationalId && (
                      <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                        {errors.nationalId.message}
                      </p>
                    )}
                  </div>
                </motion.div>
              )}

              {/* الخطوة الثالثة: كلمة المرور */}
              {currentStep === 3 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-4"
                >
                  {/* كلمة المرور */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      كلمة المرور
                    </label>
                    <div className="relative">
                      <input
                        {...register('password')}
                        type={showPassword ? 'text' : 'password'}
                        className={`input pr-10 ${errors.password ? 'input-error' : ''}`}
                        placeholder="••••••••"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        {showPassword ? (
                          <EyeSlashIcon className="w-5 h-5" />
                        ) : (
                          <EyeIcon className="w-5 h-5" />
                        )}
                      </button>
                    </div>
                    
                    {/* متطلبات كلمة المرور */}
                    <div className="mt-2 space-y-1">
                      {passwordRequirements.map((req) => (
                        <div
                          key={req.key}
                          className={`flex items-center space-x-2 rtl:space-x-reverse text-xs ${
                            checkPasswordRequirement(req)
                              ? 'text-success-600 dark:text-success-400'
                              : 'text-gray-500 dark:text-gray-400'
                          }`}
                        >
                          <CheckCircleIcon
                            className={`w-3 h-3 ${
                              checkPasswordRequirement(req)
                                ? 'text-success-600 dark:text-success-400'
                                : 'text-gray-300 dark:text-gray-600'
                            }`}
                          />
                          <span>{req.label}</span>
                        </div>
                      ))}
                    </div>
                    
                    {errors.password && (
                      <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                        {errors.password.message}
                      </p>
                    )}
                  </div>

                  {/* تأكيد كلمة المرور */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      تأكيد كلمة المرور
                    </label>
                    <div className="relative">
                      <input
                        {...register('confirmPassword')}
                        type={showConfirmPassword ? 'text' : 'password'}
                        className={`input pr-10 ${errors.confirmPassword ? 'input-error' : ''}`}
                        placeholder="••••••••"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        {showConfirmPassword ? (
                          <EyeSlashIcon className="w-5 h-5" />
                        ) : (
                          <EyeIcon className="w-5 h-5" />
                        )}
                      </button>
                    </div>
                    {errors.confirmPassword && (
                      <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                        {errors.confirmPassword.message}
                      </p>
                    )}
                  </div>

                  {/* الموافقة على الشروط */}
                  <div>
                    <label className="flex items-start space-x-3 rtl:space-x-reverse">
                      <input
                        {...register('acceptTerms')}
                        type="checkbox"
                        className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        أوافق على{' '}
                        <Link
                          href="/terms"
                          className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
                        >
                          الشروط والأحكام
                        </Link>{' '}
                        و{' '}
                        <Link
                          href="/privacy"
                          className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
                        >
                          سياسة الخصوصية
                        </Link>
                      </span>
                    </label>
                    {errors.acceptTerms && (
                      <p className="mt-1 text-sm text-error-600 dark:text-error-400">
                        {errors.acceptTerms.message}
                      </p>
                    )}
                  </div>
                </motion.div>
              )}

              {/* أزرار التنقل */}
              <div className="flex space-x-4 rtl:space-x-reverse">
                {currentStep > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    className="flex-1"
                  >
                    السابق
                  </Button>
                )}
                
                {currentStep < 3 ? (
                  <Button
                    type="button"
                    onClick={nextStep}
                    className="flex-1"
                    rightIcon={<ArrowRightIcon className="w-5 h-5" />}
                  >
                    التالي
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="flex-1"
                    isLoading={isSubmitting}
                    loadingText="جاري إنشاء الحساب..."
                    rightIcon={<ArrowRightIcon className="w-5 h-5" />}
                  >
                    إنشاء الحساب
                  </Button>
                )}
              </div>
            </form>

            {/* رابط تسجيل الدخول */}
            <div className="mt-6 text-center">
              <p className="text-gray-600 dark:text-gray-400">
                لديك حساب بالفعل؟{' '}
                <Link
                  href="/auth/login"
                  className="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
                >
                  تسجيل الدخول
                </Link>
              </p>
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
