{"version": 3, "file": "target-map.interface.js", "sourceRoot": "", "sources": ["../../../src/interfaces/target-map.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Allows to specify a map of Types in the object without using @Type decorator.\n * This is useful when you have external classes.\n */\nexport interface TargetMap {\n  /**\n   * Target which Types are being specified.\n   */\n  target: Function;\n\n  /**\n   * List of properties and their Types.\n   */\n  properties: { [key: string]: Function };\n}\n"]}