import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ScheduleModule } from '@nestjs/schedule';

// Modules
import { PaymentsModule } from './modules/payments/payments.module';
import { GatewaysModule } from './modules/gateways/gateways.module';
import { TransactionsModule } from './modules/transactions/transactions.module';
import { WebhooksModule } from './modules/webhooks/webhooks.module';
import { ExchangeRatesModule } from './modules/exchange-rates/exchange-rates.module';

// Entities
import { Payment } from './modules/payments/entities/payment.entity';
import { PaymentGateway } from './modules/gateways/entities/gateway.entity';
import { Transaction } from './modules/transactions/entities/transaction.entity';
import { WebhookLog } from './modules/webhooks/entities/webhook-log.entity';
import { ExchangeRate } from './modules/exchange-rates/entities/exchange-rate.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('PAYMENT_DB_HOST', 'localhost'),
        port: configService.get('PAYMENT_DB_PORT', 5432),
        username: configService.get('PAYMENT_DB_USERNAME', 'postgres'),
        password: configService.get('PAYMENT_DB_PASSWORD', 'password'),
        database: configService.get('PAYMENT_DB_NAME', 'ws_payments'),
        entities: [
          Payment,
          PaymentGateway,
          Transaction,
          WebhookLog,
          ExchangeRate,
        ],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development',
        ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
      }),
      inject: [ConfigService],
    }),

    // HTTP Client for external APIs
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: configService.get('HTTP_TIMEOUT', 30000),
        maxRedirects: 5,
        retries: 3,
      }),
      inject: [ConfigService],
    }),

    // JWT Authentication
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),

    // Passport
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // Task Scheduling for exchange rates
    ScheduleModule.forRoot(),

    // Feature Modules
    PaymentsModule,
    GatewaysModule,
    TransactionsModule,
    WebhooksModule,
    ExchangeRatesModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    console.log('💳 Payment Gateway Service initialized');
    console.log(`🔄 Stripe enabled: ${this.configService.get('STRIPE_ENABLED', 'false')}`);
    console.log(`🅿️  PayPal enabled: ${this.configService.get('PAYPAL_ENABLED', 'false')}`);
    console.log(`🏦 SADAD enabled: ${this.configService.get('SADAD_ENABLED', 'false')}`);
    console.log(`💳 mada enabled: ${this.configService.get('MADA_ENABLED', 'false')}`);
    console.log(`📊 Database: ${this.configService.get('PAYMENT_DB_NAME', 'ws_payments')}`);
    console.log(`🔄 Exchange rates auto-update: ${this.configService.get('AUTO_UPDATE_RATES', 'true')}`);
  }
}
