-- Seed: Agent System Data
-- Description: إنشاء بيانات نظام الوكلاء التجريبية
-- Version: 002
-- Created: 2024-01-15

-- Insert Agent Profiles
INSERT INTO agent_profiles (
    user_id, agent_type, parent_agent_id, business_name, region, city, district,
    address, primary_phone, business_email, bank_name, bank_account_number, iban,
    base_commission_rate, tier_commission_rate, volume_bonus_rate,
    daily_transaction_limit, monthly_transaction_limit, single_transaction_limit,
    status, training_completed, certification_level, onboarding_completed,
    onboarding_completed_at, approved_by, approved_at, created_by
) VALUES 
-- Level 1 Agent Manager (Root)
(
    'user_agent_manager_001',
    'business',
    NULL,
    'مؤسسة الرياض للخدمات المالية',
    'الرياض',
    'الرياض',
    'العليا',
    'شارع الملك فهد، مجمع الأعمال التجاري',
    '+************',
    '<EMAIL>',
    'البنك الأهلي السعودي',
    '****************',
    '************************',
    0.0150,  -- 1.5% base commission
    0.0075,  -- 0.75% tier commission
    0.0040,  -- 0.4% volume bonus
    200000.00,
    5000000.00,
    100000.00,
    'active',
    true,
    5,
    true,
    CURRENT_TIMESTAMP,
    'user_admin_001',
    CURRENT_TIMESTAMP,
    'user_admin_001'
),

-- Level 2 Regional Agents
(
    'user_agent_001',
    'individual',
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    'مكتب أحمد الراشد للتحويلات',
    'الرياض',
    'الرياض',
    'الملز',
    'شارع التحلية، مجمع الملز التجاري',
    '+966501234570',
    '<EMAIL>',
    'بنك الراجحي',
    '2345678901234567',
    'SA2345678901234567890123',
    0.0100,  -- 1% base commission
    0.0050,  -- 0.5% tier commission
    0.0025,  -- 0.25% volume bonus
    100000.00,
    2000000.00,
    50000.00,
    'active',
    true,
    3,
    true,
    CURRENT_TIMESTAMP,
    'user_agent_manager_001',
    CURRENT_TIMESTAMP,
    'user_agent_manager_001'
),

-- Additional Regional Agent
(
    'user_customer_001',  -- Promoting customer to agent
    'individual',
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    'مكتب محمد الفهد للخدمات المالية',
    'الرياض',
    'الرياض',
    'النسيم',
    'شارع الأمير محمد بن عبدالعزيز',
    '+966501234571',
    '<EMAIL>',
    'البنك السعودي للاستثمار',
    '3456789012345678',
    'SA3456789012345678901234',
    0.0100,
    0.0050,
    0.0025,
    100000.00,
    2000000.00,
    50000.00,
    'active',
    true,
    2,
    true,
    CURRENT_TIMESTAMP,
    'user_agent_manager_001',
    CURRENT_TIMESTAMP,
    'user_agent_manager_001'
),

-- Level 3 Sub-Agents
(
    'user_customer_002',  -- Promoting customer to sub-agent
    'individual',
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_001'),
    'مكتب فاطمة الزهراء للتحويلات',
    'الرياض',
    'الرياض',
    'الروضة',
    'شارع العروبة، مجمع الروضة',
    '+966501234572',
    '<EMAIL>',
    'بنك سامبا',
    '4567890123456789',
    'SA4567890123456789012345',
    0.0080,  -- 0.8% base commission
    0.0040,  -- 0.4% tier commission
    0.0020,  -- 0.2% volume bonus
    75000.00,
    1500000.00,
    40000.00,
    'active',
    true,
    2,
    true,
    CURRENT_TIMESTAMP,
    'user_agent_001',
    CURRENT_TIMESTAMP,
    'user_agent_001'
),

-- Jeddah Regional Agent
(
    'user_customer_003',  -- Promoting customer to agent
    'business',
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    'شركة جدة للخدمات المالية المحدودة',
    'مكة المكرمة',
    'جدة',
    'الحمراء',
    'شارع الأمير سلطان، برج التجارة',
    '+966501234573',
    '<EMAIL>',
    'البنك الأهلي السعودي',
    '5678901234567890',
    'SA567890****************',
    0.0120,  -- 1.2% base commission
    0.0060,  -- 0.6% tier commission
    0.0030,  -- 0.3% volume bonus
    150000.00,
    3000000.00,
    75000.00,
    'active',
    true,
    4,
    true,
    CURRENT_TIMESTAMP,
    'user_agent_manager_001',
    CURRENT_TIMESTAMP,
    'user_agent_manager_001'
) ON CONFLICT (user_id) DO NOTHING;

-- Update users table to reflect agent roles
UPDATE users SET role = 'agent' 
WHERE id IN ('user_customer_001', 'user_customer_002', 'user_customer_003')
AND role = 'user';

-- Insert Agent-Customer Relationships
INSERT INTO agent_customers (
    agent_id, customer_id, relationship_type, acquisition_date, 
    acquisition_channel, status, total_transactions, total_volume, 
    total_commission, last_transaction_date, satisfaction_rating, created_by
) VALUES 
-- Agent Manager's direct customers
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    'user_customer_001',
    'direct',
    '2024-01-01',
    'referral',
    'active',
    15,
    75000.00,
    750.00,
    '2024-01-14',
    4.8,
    'user_agent_manager_001'
),

-- Ahmed Al-Rashid's customers
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_001'),
    'user_customer_002',
    'direct',
    '2024-01-05',
    'walk_in',
    'active',
    8,
    40000.00,
    400.00,
    '2024-01-13',
    4.5,
    'user_agent_001'
),

-- Mohammed Al-Fahad's customers
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_customer_001'),
    'user_customer_003',
    'direct',
    '2024-01-10',
    'online',
    'active',
    5,
    25000.00,
    250.00,
    '2024-01-12',
    4.7,
    'user_customer_001'
) ON CONFLICT (agent_id, customer_id) DO NOTHING;

-- Insert Sample Commissions
INSERT INTO agent_commissions (
    agent_id, transaction_id, commission_type, commission_rate,
    transaction_amount, commission_amount, status, commission_period, 
    due_date, created_by
) VALUES 
-- Commissions for Agent Manager
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    (SELECT id FROM transactions WHERE sender_id = 'user_customer_001' LIMIT 1),
    'direct',
    0.0150,
    1000.00,
    15.00,
    'approved',
    '2024-01',
    '2024-02-01',
    'system'
),

-- Commissions for Ahmed Al-Rashid
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_001'),
    (SELECT id FROM transactions WHERE sender_id = 'user_customer_002' LIMIT 1),
    'direct',
    0.0100,
    500.00,
    5.00,
    'approved',
    '2024-01',
    '2024-02-01',
    'system'
),

-- Tier commission for Agent Manager from sub-agent
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    (SELECT id FROM transactions WHERE sender_id = 'user_customer_002' LIMIT 1),
    'tier',
    0.0075,
    500.00,
    3.75,
    'approved',
    '2024-01',
    '2024-02-01',
    'system'
),

-- Volume bonus commission
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_001'),
    (SELECT id FROM transactions WHERE sender_id = 'user_customer_003' LIMIT 1),
    'volume_bonus',
    0.0025,
    750.00,
    1.88,
    'pending',
    '2024-01',
    '2024-02-01',
    'system'
) ON CONFLICT (agent_id, transaction_id) DO NOTHING;

-- Insert Agent Performance Records
INSERT INTO agent_performance (
    agent_id, period_type, period_start, period_end,
    transaction_count, transaction_volume, average_transaction_amount,
    total_commission, direct_commission, tier_commission, bonus_commission,
    new_customers, active_customers, customer_retention_rate,
    success_rate, average_processing_time, customer_satisfaction,
    regional_rank, national_rank, volume_target, volume_achievement_rate,
    transaction_target, transaction_achievement_rate
) VALUES 
-- Agent Manager Performance
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    'monthly',
    '2024-01-01',
    '2024-01-31',
    25,
    125000.00,
    5000.00,
    1875.00,
    1500.00,
    300.00,
    75.00,
    3,
    8,
    0.9500,
    0.9800,
    120,
    4.8,
    1,
    1,
    150000.00,
    0.8333,
    30,
    0.8333
),

-- Ahmed Al-Rashid Performance
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_001'),
    'monthly',
    '2024-01-01',
    '2024-01-31',
    18,
    90000.00,
    5000.00,
    900.00,
    850.00,
    0.00,
    50.00,
    2,
    5,
    0.9200,
    0.9600,
    150,
    4.6,
    3,
    5,
    100000.00,
    0.9000,
    20,
    0.9000
),

-- Mohammed Al-Fahad Performance
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_customer_001'),
    'monthly',
    '2024-01-01',
    '2024-01-31',
    12,
    60000.00,
    5000.00,
    600.00,
    580.00,
    0.00,
    20.00,
    1,
    3,
    0.8800,
    0.9400,
    180,
    4.4,
    5,
    8,
    75000.00,
    0.8000,
    15,
    0.8000
) ON CONFLICT (agent_id, period_type, period_start) DO NOTHING;

-- Insert Agent Training Records
INSERT INTO agent_training (
    agent_id, training_type, training_title, description,
    duration_hours, training_date, completion_date, status,
    score, passed, certificate_issued, certificate_number,
    trainer_name, training_provider, training_location, training_method, created_by
) VALUES 
-- Basic Training for all agents
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    'onboarding',
    'دورة الإعداد الأساسي للوكلاء',
    'دورة تدريبية شاملة تغطي أساسيات العمل كوكيل مالي',
    8,
    '2024-01-01',
    '2024-01-01',
    'completed',
    95.50,
    true,
    true,
    'CERT-AGT-001-2024',
    'أحمد محمد السالم',
    'معهد WS Transfir للتدريب',
    'مركز التدريب الرئيسي - الرياض',
    'offline',
    'user_admin_001'
),

(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_001'),
    'onboarding',
    'دورة الإعداد الأساسي للوكلاء',
    'دورة تدريبية شاملة تغطي أساسيات العمل كوكيل مالي',
    8,
    '2024-01-02',
    '2024-01-02',
    'completed',
    88.75,
    true,
    true,
    'CERT-AGT-002-2024',
    'أحمد محمد السالم',
    'معهد WS Transfir للتدريب',
    'مركز التدريب الرئيسي - الرياض',
    'offline',
    'user_agent_manager_001'
),

-- Advanced Training
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_manager_001'),
    'advanced',
    'إدارة الفرق والقيادة',
    'دورة متقدمة في إدارة فرق الوكلاء والقيادة الفعالة',
    16,
    '2024-01-08',
    '2024-01-10',
    'completed',
    92.00,
    true,
    true,
    'CERT-ADV-001-2024',
    'سارة عبدالله النمر',
    'معهد WS Transfir للتدريب',
    'مركز التدريب المتقدم - الرياض',
    'hybrid',
    'user_admin_001'
),

-- Compliance Training
(
    (SELECT id FROM agent_profiles WHERE user_id = 'user_agent_001'),
    'compliance',
    'مكافحة غسيل الأموال والامتثال',
    'دورة تدريبية في قوانين مكافحة غسيل الأموال والامتثال التنظيمي',
    4,
    '2024-01-15',
    '2024-01-15',
    'completed',
    91.25,
    true,
    true,
    'CERT-AML-001-2024',
    'خالد عبدالرحمن الشمري',
    'معهد WS Transfir للتدريب',
    'منصة التدريب الإلكتروني',
    'online',
    'user_agent_manager_001'
) ON CONFLICT (agent_id, training_type, training_date) DO NOTHING;

-- Update agent profiles with performance data
UPDATE agent_profiles SET 
    total_transactions = 25,
    total_volume = 125000.00,
    total_commission_earned = 1875.00,
    customer_count = 8,
    rating = 4.8,
    review_count = 15,
    last_training_date = '2024-01-10',
    next_training_due = '2024-07-10'
WHERE user_id = 'user_agent_manager_001';

UPDATE agent_profiles SET 
    total_transactions = 18,
    total_volume = 90000.00,
    total_commission_earned = 900.00,
    customer_count = 5,
    rating = 4.6,
    review_count = 12,
    last_training_date = '2024-01-15',
    next_training_due = '2024-07-15'
WHERE user_id = 'user_agent_001';

UPDATE agent_profiles SET 
    total_transactions = 12,
    total_volume = 60000.00,
    total_commission_earned = 600.00,
    customer_count = 3,
    rating = 4.4,
    review_count = 8,
    last_training_date = '2024-01-02',
    next_training_due = '2024-07-02'
WHERE user_id = 'user_customer_001';

-- Add some metadata to agent profiles
UPDATE agent_profiles SET metadata = jsonb_build_object(
    'specializations', ARRAY['domestic_transfers', 'international_transfers', 'bill_payments'],
    'languages', ARRAY['ar', 'en'],
    'operating_hours', jsonb_build_object(
        'sunday', jsonb_build_object('open', '08:00', 'close', '20:00'),
        'monday', jsonb_build_object('open', '08:00', 'close', '20:00'),
        'tuesday', jsonb_build_object('open', '08:00', 'close', '20:00'),
        'wednesday', jsonb_build_object('open', '08:00', 'close', '20:00'),
        'thursday', jsonb_build_object('open', '08:00', 'close', '20:00'),
        'friday', jsonb_build_object('open', '14:00', 'close', '20:00'),
        'saturday', jsonb_build_object('open', '08:00', 'close', '20:00')
    ),
    'contact_preferences', ARRAY['phone', 'whatsapp', 'email'],
    'service_areas', ARRAY['الرياض', 'الخرج', 'الدرعية']
) WHERE user_id = 'user_agent_manager_001';

-- Add tags to agents
UPDATE agent_profiles SET tags = ARRAY['seed_data', 'level_1', 'manager', 'riyadh', 'business'] 
WHERE user_id = 'user_agent_manager_001';

UPDATE agent_profiles SET tags = ARRAY['seed_data', 'level_2', 'individual', 'riyadh', 'experienced'] 
WHERE user_id = 'user_agent_001';

UPDATE agent_profiles SET tags = ARRAY['seed_data', 'level_2', 'individual', 'riyadh', 'new'] 
WHERE user_id = 'user_customer_001';

UPDATE agent_profiles SET tags = ARRAY['seed_data', 'level_3', 'individual', 'riyadh', 'sub_agent'] 
WHERE user_id = 'user_customer_002';

UPDATE agent_profiles SET tags = ARRAY['seed_data', 'level_2', 'business', 'jeddah', 'regional'] 
WHERE user_id = 'user_customer_003';

-- Log the seeding
INSERT INTO schema_migrations (version, name, file_path, checksum, execution_time_ms, success)
VALUES (
    'seed_002',
    'Agent System Data Seed',
    'database/seeds/002_agent_system_data.sql',
    'agent_system_seed_checksum',
    0,
    true
) ON CONFLICT (version) DO NOTHING;
