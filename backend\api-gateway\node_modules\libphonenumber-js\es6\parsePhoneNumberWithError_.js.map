{"version": 3, "file": "parsePhoneNumberWithError_.js", "names": ["parse", "parsePhoneNumberWithError", "text", "options", "metadata", "v2"], "sources": ["../source/parsePhoneNumberWithError_.js"], "sourcesContent": ["import parse from './parse.js'\r\n\r\nexport default function parsePhoneNumberWithError(text, options, metadata) {\r\n\treturn parse(text, { ...options, v2: true }, metadata)\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,KAAP,MAAkB,YAAlB;AAEA,eAAe,SAASC,yBAAT,CAAmCC,IAAnC,EAAyCC,OAAzC,EAAkDC,QAAlD,EAA4D;EAC1E,OAAOJ,KAAK,CAACE,IAAD,kCAAYC,OAAZ;IAAqBE,EAAE,EAAE;EAAzB,IAAiCD,QAAjC,CAAZ;AACA"}