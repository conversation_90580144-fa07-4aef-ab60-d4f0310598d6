{"version": 3, "file": "getCountries.js", "names": ["<PERSON><PERSON><PERSON>", "getCountries", "metadata"], "sources": ["../source/getCountries.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\n\r\nexport default function getCountries(metadata) {\r\n\treturn new Metadata(metadata).getCountries()\r\n}"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,eAArB;AAEA,eAAe,SAASC,YAAT,CAAsBC,QAAtB,EAAgC;EAC9C,OAAO,IAAIF,QAAJ,CAAaE,QAAb,EAAuBD,YAAvB,EAAP;AACA"}