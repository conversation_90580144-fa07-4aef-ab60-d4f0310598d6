import CryptoJS from 'crypto-js';

// مفتاح التشفير (يجب أن يكون في متغيرات البيئة في الإنتاج)
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'ws-transfir-default-key';

// مفاتيح التخزين
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'ws_access_token',
  REFRESH_TOKEN: 'ws_refresh_token',
  USER_PREFERENCES: 'ws_user_preferences',
  LANGUAGE: 'ws_language',
  THEME: 'ws_theme',
  NOTIFICATION_SETTINGS: 'ws_notification_settings',
  RECENT_TRANSFERS: 'ws_recent_transfers',
  SAVED_RECIPIENTS: 'ws_saved_recipients',
  EXCHANGE_RATES_CACHE: 'ws_exchange_rates_cache',
  FORM_DRAFTS: 'ws_form_drafts',
} as const;

// فئة التخزين الآمن
class SecureStorage {
  private isClient = typeof window !== 'undefined';

  /**
   * تشفير البيانات
   */
  private encrypt(data: string): string {
    try {
      return CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
    } catch (error) {
      console.error('Encryption error:', error);
      return data; // إرجاع البيانات غير مشفرة في حالة الخطأ
    }
  }

  /**
   * فك تشفير البيانات
   */
  private decrypt(encryptedData: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Decryption error:', error);
      return encryptedData; // إرجاع البيانات كما هي في حالة الخطأ
    }
  }

  /**
   * حفظ بيانات مشفرة
   */
  setSecure(key: string, value: any): void {
    if (!this.isClient) return;

    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      const encryptedValue = this.encrypt(stringValue);
      localStorage.setItem(key, encryptedValue);
    } catch (error) {
      console.error('Secure storage set error:', error);
    }
  }

  /**
   * جلب بيانات مشفرة
   */
  getSecure<T = any>(key: string): T | null {
    if (!this.isClient) return null;

    try {
      const encryptedValue = localStorage.getItem(key);
      if (!encryptedValue) return null;

      const decryptedValue = this.decrypt(encryptedValue);
      if (!decryptedValue) return null;

      // محاولة تحويل إلى JSON
      try {
        return JSON.parse(decryptedValue);
      } catch {
        return decryptedValue as T;
      }
    } catch (error) {
      console.error('Secure storage get error:', error);
      return null;
    }
  }

  /**
   * حفظ بيانات عادية
   */
  set(key: string, value: any): void {
    if (!this.isClient) return;

    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      localStorage.setItem(key, stringValue);
    } catch (error) {
      console.error('Storage set error:', error);
    }
  }

  /**
   * جلب بيانات عادية
   */
  get<T = any>(key: string): T | null {
    if (!this.isClient) return null;

    try {
      const value = localStorage.getItem(key);
      if (!value) return null;

      // محاولة تحويل إلى JSON
      try {
        return JSON.parse(value);
      } catch {
        return value as T;
      }
    } catch (error) {
      console.error('Storage get error:', error);
      return null;
    }
  }

  /**
   * حذف عنصر
   */
  remove(key: string): void {
    if (!this.isClient) return;

    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Storage remove error:', error);
    }
  }

  /**
   * مسح جميع البيانات
   */
  clear(): void {
    if (!this.isClient) return;

    try {
      localStorage.clear();
    } catch (error) {
      console.error('Storage clear error:', error);
    }
  }

  /**
   * التحقق من وجود مفتاح
   */
  has(key: string): boolean {
    if (!this.isClient) return false;
    return localStorage.getItem(key) !== null;
  }

  /**
   * الحصول على حجم التخزين
   */
  getSize(): number {
    if (!this.isClient) return 0;

    let total = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total;
  }
}

// إنشاء مثيل التخزين
const storage = new SecureStorage();

// إدارة الرموز المميزة
export const tokenStorage = {
  /**
   * حفظ الرموز المميزة
   */
  setTokens(accessToken: string, refreshToken: string): void {
    storage.setSecure(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
    storage.setSecure(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
  },

  /**
   * الحصول على رمز الوصول
   */
  getAccessToken(): string | null {
    return storage.getSecure<string>(STORAGE_KEYS.ACCESS_TOKEN);
  },

  /**
   * الحصول على رمز التجديد
   */
  getRefreshToken(): string | null {
    return storage.getSecure<string>(STORAGE_KEYS.REFRESH_TOKEN);
  },

  /**
   * مسح الرموز المميزة
   */
  clearTokens(): void {
    storage.remove(STORAGE_KEYS.ACCESS_TOKEN);
    storage.remove(STORAGE_KEYS.REFRESH_TOKEN);
  },

  /**
   * التحقق من وجود رموز صالحة
   */
  hasValidTokens(): boolean {
    const accessToken = this.getAccessToken();
    const refreshToken = this.getRefreshToken();
    return !!(accessToken && refreshToken);
  },
};

// إدارة تفضيلات المستخدم
export const preferencesStorage = {
  /**
   * حفظ التفضيلات
   */
  setPreferences(preferences: any): void {
    storage.set(STORAGE_KEYS.USER_PREFERENCES, preferences);
  },

  /**
   * الحصول على التفضيلات
   */
  getPreferences(): any {
    return storage.get(STORAGE_KEYS.USER_PREFERENCES) || {};
  },

  /**
   * تحديث تفضيل معين
   */
  updatePreference(key: string, value: any): void {
    const preferences = this.getPreferences();
    preferences[key] = value;
    this.setPreferences(preferences);
  },

  /**
   * مسح التفضيلات
   */
  clearPreferences(): void {
    storage.remove(STORAGE_KEYS.USER_PREFERENCES);
  },
};

// إدارة اللغة
export const languageStorage = {
  /**
   * حفظ اللغة
   */
  setLanguage(language: string): void {
    storage.set(STORAGE_KEYS.LANGUAGE, language);
  },

  /**
   * الحصول على اللغة
   */
  getLanguage(): string {
    return storage.get<string>(STORAGE_KEYS.LANGUAGE) || 'ar';
  },
};

// إدارة السمة
export const themeStorage = {
  /**
   * حفظ السمة
   */
  setTheme(theme: string): void {
    storage.set(STORAGE_KEYS.THEME, theme);
  },

  /**
   * الحصول على السمة
   */
  getTheme(): string {
    return storage.get<string>(STORAGE_KEYS.THEME) || 'light';
  },
};

// إدارة التحويلات الأخيرة
export const transfersStorage = {
  /**
   * حفظ التحويلات الأخيرة
   */
  setRecentTransfers(transfers: any[]): void {
    storage.set(STORAGE_KEYS.RECENT_TRANSFERS, transfers.slice(0, 10)); // الاحتفاظ بآخر 10 فقط
  },

  /**
   * الحصول على التحويلات الأخيرة
   */
  getRecentTransfers(): any[] {
    return storage.get<any[]>(STORAGE_KEYS.RECENT_TRANSFERS) || [];
  },

  /**
   * إضافة تحويل جديد
   */
  addRecentTransfer(transfer: any): void {
    const recent = this.getRecentTransfers();
    recent.unshift(transfer);
    this.setRecentTransfers(recent);
  },
};

// إدارة المستلمين المحفوظين
export const recipientsStorage = {
  /**
   * حفظ المستلمين
   */
  setSavedRecipients(recipients: any[]): void {
    storage.setSecure(STORAGE_KEYS.SAVED_RECIPIENTS, recipients);
  },

  /**
   * الحصول على المستلمين المحفوظين
   */
  getSavedRecipients(): any[] {
    return storage.getSecure<any[]>(STORAGE_KEYS.SAVED_RECIPIENTS) || [];
  },

  /**
   * إضافة مستلم جديد
   */
  addRecipient(recipient: any): void {
    const recipients = this.getSavedRecipients();
    recipients.push({ ...recipient, id: Date.now().toString() });
    this.setSavedRecipients(recipients);
  },

  /**
   * حذف مستلم
   */
  removeRecipient(recipientId: string): void {
    const recipients = this.getSavedRecipients();
    const filtered = recipients.filter(r => r.id !== recipientId);
    this.setSavedRecipients(filtered);
  },
};

// إدارة مسودات النماذج
export const formDraftsStorage = {
  /**
   * حفظ مسودة
   */
  saveDraft(formId: string, data: any): void {
    const drafts = storage.get(STORAGE_KEYS.FORM_DRAFTS) || {};
    drafts[formId] = {
      data,
      timestamp: Date.now(),
    };
    storage.set(STORAGE_KEYS.FORM_DRAFTS, drafts);
  },

  /**
   * الحصول على مسودة
   */
  getDraft(formId: string): any {
    const drafts = storage.get(STORAGE_KEYS.FORM_DRAFTS) || {};
    return drafts[formId]?.data || null;
  },

  /**
   * حذف مسودة
   */
  removeDraft(formId: string): void {
    const drafts = storage.get(STORAGE_KEYS.FORM_DRAFTS) || {};
    delete drafts[formId];
    storage.set(STORAGE_KEYS.FORM_DRAFTS, drafts);
  },

  /**
   * مسح المسودات القديمة (أكثر من 7 أيام)
   */
  cleanOldDrafts(): void {
    const drafts = storage.get(STORAGE_KEYS.FORM_DRAFTS) || {};
    const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    Object.keys(drafts).forEach(key => {
      if (drafts[key].timestamp < weekAgo) {
        delete drafts[key];
      }
    });
    
    storage.set(STORAGE_KEYS.FORM_DRAFTS, drafts);
  },
};

// تصدير التخزين الافتراضي
export default storage;
