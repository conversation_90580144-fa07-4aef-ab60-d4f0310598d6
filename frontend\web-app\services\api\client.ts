/**
 * Lightning Fast API Client - Zero Dependencies
 * عميل API فائق السرعة - بدون تبعيات خارجية
 * Optimized for maximum performance and instant loading
 */

// Types for lightning fast API client
interface LightningApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp?: string;
  requestId?: string;
}

interface LightningRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
}

// Lightning fast configuration
const LIGHTNING_CONFIG = {
  baseURL: (typeof process !== 'undefined' && process.env?.NEXT_PUBLIC_API_URL) || 'http://localhost:8080',
  timeout: 8000, // 8 seconds for optimal speed
  retries: 1, // Minimal retries for speed
};

// Lightning fast token storage
const tokenStorage = {
  getAccessToken: (): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem('ws_transfir_token');
    } catch {
      return null;
    }
  },

  setAccessToken: (token: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem('ws_transfir_token', token);
    } catch {
      // Silent fail for speed
    }
  },

  removeAccessToken: (): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem('ws_transfir_token');
    } catch {
      // Silent fail for speed
    }
  },

  getRefreshToken: (): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem('ws_transfir_refresh_token');
    } catch {
      return null;
    }
  },

  setTokens: (accessToken: string, refreshToken: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem('ws_transfir_token', accessToken);
      localStorage.setItem('ws_transfir_refresh_token', refreshToken);
    } catch {
      // Silent fail for speed
    }
  },

  clearTokens: (): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem('ws_transfir_token');
      localStorage.removeItem('ws_transfir_refresh_token');
    } catch {
      // Silent fail for speed
    }
  }
};

// Lightning fast toast notifications
const toast = {
  success: (message: string) => {
    console.log('✅ Success:', message);
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('lightning-toast', {
        detail: { type: 'success', message }
      }));
    }
  },

  error: (message: string) => {
    console.error('❌ Error:', message);
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('lightning-toast', {
        detail: { type: 'error', message }
      }));
    }
  },

  loading: (message: string) => {
    console.log('⏳ Loading:', message);
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('lightning-toast', {
        detail: { type: 'loading', message }
      }));
    }
  }
};

// Generate unique request ID for tracking
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
};

// Lightning fast HTTP client using native fetch
class LightningApiClient {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = LIGHTNING_CONFIG.baseURL;
    this.timeout = LIGHTNING_CONFIG.timeout;
  }

  async get<T = any>(endpoint: string, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T = any>(endpoint: string, data?: any, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'POST', body: data });
  }

  async put<T = any>(endpoint: string, data?: any, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'PUT', body: data });
  }

  async patch<T = any>(endpoint: string, data?: any, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'PATCH', body: data });
  }

  async delete<T = any>(endpoint: string, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'DELETE' });
  }

  private async makeRequest<T = any>(
    endpoint: string,
    config: LightningRequestConfig = {}
  ): Promise<LightningApiResponse<T>> {
    const url = `${this.baseURL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    const method = config.method || 'GET';
    const requestId = generateRequestId();

    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Request-ID': requestId,
      'X-Powered-By': 'WS-Transfir-Lightning',
      ...config.headers,
    };

    // Add auth token if available
    const token = tokenStorage.getAccessToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    };

    // Add timeout using AbortController for better browser support
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || this.timeout);
    requestOptions.signal = controller.signal;

    // Add body for non-GET requests
    if (config.body && method !== 'GET') {
      requestOptions.body = typeof config.body === 'string'
        ? config.body
        : JSON.stringify(config.body);
    }

    try {
      const startTime = Date.now();
      const response = await fetch(url, requestOptions);
      const endTime = Date.now();

      clearTimeout(timeoutId);

      // Log performance in development
      if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
        console.log(`⚡ Lightning API: ${method} ${endpoint} - ${endTime - startTime}ms`);
      }

      // Parse response
      let data: any;
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      // Handle response
      if (response.ok) {
        return {
          success: true,
          data,
          timestamp: new Date().toISOString(),
          requestId,
        };
      } else {
        // Handle HTTP errors
        const errorMessage = data?.message || data?.error || `HTTP ${response.status}`;

        // Handle 401 Unauthorized
        if (response.status === 401) {
          tokenStorage.clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
        }

        return {
          success: false,
          error: errorMessage,
          timestamp: new Date().toISOString(),
          requestId,
        };
      }
    } catch (error: any) {
      clearTimeout(timeoutId);
      console.error('Lightning API Error:', error);

      let errorMessage = 'خطأ في الاتصال';
      if (error.name === 'AbortError') {
        errorMessage = 'انتهت مهلة الطلب';
      } else if (error.name === 'TypeError') {
        errorMessage = 'خطأ في الشبكة';
      }

      return {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
        requestId,
      };
    }
  }
}

    // إضافة اللغة
    const language = localStorage.getItem('language') || 'ar';
    config.headers['Accept-Language'] = language;

    // تسجيل الطلب في وضع التطوير
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data,
        params: config.params,
      });
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// اعتراض الاستجابات
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // تسجيل الاستجابة في وضع التطوير
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
        headers: response.headers,
      });
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // تسجيل الخطأ
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ API Error: ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
    }

    // معالجة خطأ 401 (غير مخول)
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // إضافة الطلب إلى الطابور
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${token}`;
          }
          return apiClient(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = tokenStorage.getRefreshToken();
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        // محاولة تجديد الرمز
        const response = await axios.post(`${API_BASE_URL}/api/auth/refresh`, {
          refreshToken,
        });

        const { accessToken, refreshToken: newRefreshToken } = response.data;
        
        // حفظ الرموز الجديدة
        tokenStorage.setTokens(accessToken, newRefreshToken);

        // معالجة الطابور
        processQueue(null, accessToken);

        // إعادة تنفيذ الطلب الأصلي
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        }
        
        return apiClient(originalRequest);
      } catch (refreshError) {
        // فشل في تجديد الرمز
        processQueue(refreshError, null);
        tokenStorage.clearTokens();
        
        // إعادة توجيه لصفحة تسجيل الدخول
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
        
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // معالجة الأخطاء الأخرى
    handleApiError(error);
    
    return Promise.reject(error);
  }
);

// معالجة أخطاء API
const handleApiError = (error: AxiosError) => {
  const status = error.response?.status;
  const data = error.response?.data as any;
  
  // عدم إظهار toast للأخطاء المتوقعة
  const silentErrors = [401, 422]; // غير مخول، خطأ في التحقق
  
  if (!silentErrors.includes(status || 0)) {
    let message = 'حدث خطأ غير متوقع';
    
    switch (status) {
      case 400:
        message = data?.message || 'طلب غير صحيح';
        break;
      case 403:
        message = 'ليس لديك صلاحية للوصول';
        break;
      case 404:
        message = 'المورد المطلوب غير موجود';
        break;
      case 429:
        message = 'تم تجاوز حد الطلبات. يرجى المحاولة لاحقاً';
        break;
      case 500:
        message = 'خطأ في الخادم. يرجى المحاولة لاحقاً';
        break;
      case 502:
      case 503:
      case 504:
        message = 'الخدمة غير متاحة مؤقتاً';
        break;
      default:
        if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
          message = 'خطأ في الاتصال. تحقق من الإنترنت';
        } else if (data?.message) {
          message = data.message;
        }
    }
    
    toast.error(message);
  }
};

// إنشاء معرف فريد للطلب
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// دوال مساعدة للطلبات
export const apiRequest = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.get(url, config),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.post(url, data, config),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.put(url, data, config),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.patch(url, data, config),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.delete(url, config),
};

// دالة لرفع الملفات
export const uploadFile = async (
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  return apiClient.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  });
};

// دالة لتحميل الملفات
export const downloadFile = async (
  url: string,
  filename?: string
): Promise<void> => {
  try {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('Download error:', error);
    toast.error('فشل في تحميل الملف');
  }
};

// تصدير العميل الافتراضي
export default apiClient;
