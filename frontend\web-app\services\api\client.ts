import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { toast } from 'react-hot-toast';
import { tokenStorage } from '@/utils/storage';

// إعدادات العميل الأساسية
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
const API_TIMEOUT = 30000; // 30 ثانية

// إنشاء عميل Axios
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// متغير لتتبع محاولات تجديد الرمز
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: any) => void;
  reject: (error: any) => void;
}> = [];

// معالجة طابور الطلبات المؤجلة
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

// اعتراض الطلبات
apiClient.interceptors.request.use(
  (config) => {
    // إضافة رمز المصادقة
    const token = tokenStorage.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // إضافة معرف الطلب للتتبع
    config.headers['X-Request-ID'] = generateRequestId();

    // إضافة اللغة
    const language = localStorage.getItem('language') || 'ar';
    config.headers['Accept-Language'] = language;

    // تسجيل الطلب في وضع التطوير
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data,
        params: config.params,
      });
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// اعتراض الاستجابات
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // تسجيل الاستجابة في وضع التطوير
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
        headers: response.headers,
      });
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // تسجيل الخطأ
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ API Error: ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
    }

    // معالجة خطأ 401 (غير مخول)
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // إضافة الطلب إلى الطابور
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${token}`;
          }
          return apiClient(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = tokenStorage.getRefreshToken();
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        // محاولة تجديد الرمز
        const response = await axios.post(`${API_BASE_URL}/api/auth/refresh`, {
          refreshToken,
        });

        const { accessToken, refreshToken: newRefreshToken } = response.data;
        
        // حفظ الرموز الجديدة
        tokenStorage.setTokens(accessToken, newRefreshToken);

        // معالجة الطابور
        processQueue(null, accessToken);

        // إعادة تنفيذ الطلب الأصلي
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        }
        
        return apiClient(originalRequest);
      } catch (refreshError) {
        // فشل في تجديد الرمز
        processQueue(refreshError, null);
        tokenStorage.clearTokens();
        
        // إعادة توجيه لصفحة تسجيل الدخول
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
        
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // معالجة الأخطاء الأخرى
    handleApiError(error);
    
    return Promise.reject(error);
  }
);

// معالجة أخطاء API
const handleApiError = (error: AxiosError) => {
  const status = error.response?.status;
  const data = error.response?.data as any;
  
  // عدم إظهار toast للأخطاء المتوقعة
  const silentErrors = [401, 422]; // غير مخول، خطأ في التحقق
  
  if (!silentErrors.includes(status || 0)) {
    let message = 'حدث خطأ غير متوقع';
    
    switch (status) {
      case 400:
        message = data?.message || 'طلب غير صحيح';
        break;
      case 403:
        message = 'ليس لديك صلاحية للوصول';
        break;
      case 404:
        message = 'المورد المطلوب غير موجود';
        break;
      case 429:
        message = 'تم تجاوز حد الطلبات. يرجى المحاولة لاحقاً';
        break;
      case 500:
        message = 'خطأ في الخادم. يرجى المحاولة لاحقاً';
        break;
      case 502:
      case 503:
      case 504:
        message = 'الخدمة غير متاحة مؤقتاً';
        break;
      default:
        if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
          message = 'خطأ في الاتصال. تحقق من الإنترنت';
        } else if (data?.message) {
          message = data.message;
        }
    }
    
    toast.error(message);
  }
};

// إنشاء معرف فريد للطلب
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// دوال مساعدة للطلبات
export const apiRequest = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.get(url, config),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.post(url, data, config),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.put(url, data, config),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.patch(url, data, config),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.delete(url, config),
};

// دالة لرفع الملفات
export const uploadFile = async (
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  return apiClient.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  });
};

// دالة لتحميل الملفات
export const downloadFile = async (
  url: string,
  filename?: string
): Promise<void> => {
  try {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('Download error:', error);
    toast.error('فشل في تحميل الملف');
  }
};

// تصدير العميل الافتراضي
export default apiClient;
