-- Seed: Payment System Data
-- Description: إنشاء بيانات نظام الدفع والتسوية التجريبية
-- Version: 005
-- Created: 2024-01-16

-- Insert sample account balances
INSERT INTO account_balances (account_id, currency, available_balance, pending_balance, reserved_balance) VALUES
('user_customer_001', 'SAR', 15000.00, 500.00, 200.00),
('user_customer_001', 'USD', 2500.00, 100.00, 50.00),
('user_customer_002', 'SAR', 8500.00, 300.00, 100.00),
('user_customer_003', 'SAR', 12000.00, 800.00, 150.00),
('user_agent_001', 'SAR', 25000.00, 1200.00, 500.00),
('user_agent_002', 'SAR', 18000.00, 900.00, 300.00),
('user_merchant_001', 'SAR', 45000.00, 2500.00, 1000.00),
('user_merchant_001', 'USD', 8000.00, 400.00, 200.00),
('user_merchant_002', 'SAR', 32000.00, 1800.00, 700.00)
ON CONFLICT (account_id, currency) DO UPDATE SET
    available_balance = EXCLUDED.available_balance,
    pending_balance = EXCLUDED.pending_balance,
    reserved_balance = EXCLUDED.reserved_balance;

-- Insert sample payments
INSERT INTO payments (
    customer_id, amount, currency, payment_method, provider, description, 
    status, provider_transaction_id, fees_amount, metadata, created_at
) VALUES 
-- Completed payments
(
    'user_customer_001', 1500.00, 'SAR', 'credit_card', 'stripe',
    'دفع فاتورة خدمات', 'completed', 'pi_1234567890abcdef',
    43.50, '{"invoice_id": "inv_001", "customer_email": "<EMAIL>"}',
    CURRENT_TIMESTAMP - INTERVAL '2 hours'
),
(
    'user_customer_001', 750.00, 'SAR', 'debit_card', 'mada',
    'شراء منتجات', 'completed', 'MADA20240116001',
    13.13, '{"order_id": "ord_001", "items_count": 3}',
    CURRENT_TIMESTAMP - INTERVAL '4 hours'
),
(
    'user_customer_002', 2200.00, 'SAR', 'digital_wallet', 'stcpay',
    'تحويل أموال', 'completed', 'STC20240116001',
    44.00, '{"transfer_type": "p2p", "recipient": "user_customer_003"}',
    CURRENT_TIMESTAMP - INTERVAL '6 hours'
),
(
    'user_customer_003', 500.00, 'SAR', 'mobile_payment', 'applepay',
    'دفع اشتراك', 'completed', 'AP_TXN_001234',
    15.00, '{"subscription_id": "sub_001", "period": "monthly"}',
    CURRENT_TIMESTAMP - INTERVAL '8 hours'
),
(
    'user_customer_001', 3200.00, 'USD', 'credit_card', 'paypal',
    'دفع دولي', 'completed', 'PAYID-ABCD1234',
    108.80, '{"currency_rate": 3.75, "original_amount": 12000, "original_currency": "SAR"}',
    CURRENT_TIMESTAMP - INTERVAL '12 hours'
),

-- Pending payments
(
    'user_customer_002', 1800.00, 'SAR', 'bank_transfer', 'sadad',
    'تحويل بنكي', 'pending', NULL,
    36.00, '{"bank_code": "RIBLSARI", "account_number": "****1234"}',
    CURRENT_TIMESTAMP - INTERVAL '30 minutes'
),
(
    'user_customer_003', 950.00, 'SAR', 'credit_card', 'visa',
    'دفع فاتورة', 'processing', 'VISA_TXN_789',
    27.55, '{"card_last4": "1234", "card_type": "visa"}',
    CURRENT_TIMESTAMP - INTERVAL '15 minutes'
),

-- Failed payments
(
    'user_customer_001', 5000.00, 'SAR', 'credit_card', 'mastercard',
    'دفع كبير', 'failed', 'MC_FAIL_001',
    0.00, '{"error_code": "insufficient_funds", "decline_reason": "رصيد غير كافي"}',
    CURRENT_TIMESTAMP - INTERVAL '1 hour'
),
(
    'user_customer_002', 300.00, 'SAR', 'debit_card', 'mada',
    'دفع صغير', 'failed', NULL,
    0.00, '{"error_code": "card_declined", "decline_reason": "بطاقة مرفوضة"}',
    CURRENT_TIMESTAMP - INTERVAL '45 minutes'
);

-- Insert sample refunds
INSERT INTO refunds (
    payment_id, amount, currency, reason, status, provider_refund_id, metadata, created_at
) VALUES 
(
    (SELECT id FROM payments WHERE provider_transaction_id = 'pi_1234567890abcdef' LIMIT 1),
    150.00, 'SAR', 'طلب العميل', 'completed', 're_1234567890abcdef',
    '{"refund_type": "partial", "requested_by": "customer"}',
    CURRENT_TIMESTAMP - INTERVAL '1 hour'
),
(
    (SELECT id FROM payments WHERE provider_transaction_id = 'STC20240116001' LIMIT 1),
    2200.00, 'SAR', 'خطأ في المعاملة', 'processing', 'STC_REF_001',
    '{"refund_type": "full", "requested_by": "system"}',
    CURRENT_TIMESTAMP - INTERVAL '30 minutes'
);

-- Insert sample balance changes
INSERT INTO balance_changes (
    account_id, currency, amount, transaction_type, old_balance, new_balance, reference_id
) VALUES 
('user_customer_001', 'SAR', 1500.00, 'debit', 16500.00, 15000.00, 
 (SELECT id FROM payments WHERE provider_transaction_id = 'pi_1234567890abcdef' LIMIT 1)),
('user_customer_001', 'SAR', 750.00, 'debit', 15750.00, 15000.00,
 (SELECT id FROM payments WHERE provider_transaction_id = 'MADA20240116001' LIMIT 1)),
('user_customer_002', 'SAR', 2200.00, 'debit', 10700.00, 8500.00,
 (SELECT id FROM payments WHERE provider_transaction_id = 'STC20240116001' LIMIT 1)),
('user_customer_003', 'SAR', 500.00, 'debit', 12500.00, 12000.00,
 (SELECT id FROM payments WHERE provider_transaction_id = 'AP_TXN_001234' LIMIT 1)),
('user_customer_001', 'USD', 3200.00, 'debit', 5700.00, 2500.00,
 (SELECT id FROM payments WHERE provider_transaction_id = 'PAYID-ABCD1234' LIMIT 1));

-- Insert sample settlements
INSERT INTO settlements (
    account_id, amount, currency, settlement_type, status, fees_amount, net_amount,
    description, reference_number, bank_reference, created_at, completed_at
) VALUES 
-- Completed settlements
(
    'user_merchant_001', 15000.00, 'SAR', 'daily', 'completed', 75.00, 14925.00,
    'تسوية يومية للمعاملات', 'SET20240116001', 'BNK20240116001',
    CURRENT_TIMESTAMP - INTERVAL '1 day',
    CURRENT_TIMESTAMP - INTERVAL '23 hours'
),
(
    'user_agent_001', 8500.00, 'SAR', 'weekly', 'completed', 25.50, 8474.50,
    'تسوية أسبوعية للعمولات', 'SET20240116002', 'BNK20240116002',
    CURRENT_TIMESTAMP - INTERVAL '2 days',
    CURRENT_TIMESTAMP - INTERVAL '1 day 20 hours'
),
(
    'user_merchant_002', 12000.00, 'SAR', 'instant', 'completed', 120.00, 11880.00,
    'تسوية فورية', 'SET20240116003', 'BNK20240116003',
    CURRENT_TIMESTAMP - INTERVAL '3 hours',
    CURRENT_TIMESTAMP - INTERVAL '3 hours'
),

-- Pending settlements
(
    'user_agent_002', 5500.00, 'SAR', 'daily', 'pending', 27.50, 5472.50,
    'تسوية يومية معلقة', 'SET20240116004', NULL,
    CURRENT_TIMESTAMP - INTERVAL '2 hours',
    NULL
),
(
    'user_merchant_001', 3200.00, 'USD', 'weekly', 'processing', 6.40, 3193.60,
    'تسوية أسبوعية بالدولار', 'SET20240116005', NULL,
    CURRENT_TIMESTAMP - INTERVAL '1 hour',
    NULL
);

-- Insert sample risk assessments
INSERT INTO risk_assessments (
    user_id, transaction_amount, currency, transaction_type, payment_method,
    risk_score, risk_level, recommended_action, requires_manual_review,
    ip_address, device_fingerprint, metadata, created_at
) VALUES 
(
    'user_customer_001', 1500.00, 'SAR', 'payment', 'credit_card',
    25.5, 'low', 'allow', false,
    '*************', 'fp_abc123def456',
    '{"location": {"country": "SA", "city": "Riyadh"}, "device_type": "mobile"}',
    CURRENT_TIMESTAMP - INTERVAL '2 hours'
),
(
    'user_customer_002', 2200.00, 'SAR', 'transfer', 'digital_wallet',
    45.8, 'medium', 'review', false,
    '*************', 'fp_def456ghi789',
    '{"location": {"country": "SA", "city": "Jeddah"}, "device_type": "desktop"}',
    CURRENT_TIMESTAMP - INTERVAL '6 hours'
),
(
    'user_customer_001', 5000.00, 'SAR', 'payment', 'credit_card',
    78.2, 'high', 'require_verification', true,
    '*********', 'fp_ghi789jkl012',
    '{"location": {"country": "US", "city": "New York"}, "device_type": "mobile", "new_device": true}',
    CURRENT_TIMESTAMP - INTERVAL '1 hour'
),
(
    'user_customer_003', 500.00, 'SAR', 'subscription', 'mobile_payment',
    15.3, 'low', 'allow', false,
    '*************', 'fp_jkl012mno345',
    '{"location": {"country": "SA", "city": "Dammam"}, "device_type": "mobile"}',
    CURRENT_TIMESTAMP - INTERVAL '8 hours'
),
(
    'user_customer_002', 300.00, 'SAR', 'payment', 'debit_card',
    92.1, 'critical', 'block', true,
    '***********', 'fp_suspicious001',
    '{"location": {"country": "XX", "city": "Unknown"}, "device_type": "unknown", "multiple_failures": true}',
    CURRENT_TIMESTAMP - INTERVAL '45 minutes'
);

-- Insert sample risk factors
INSERT INTO risk_factors (assessment_id, risk_type, risk_score, description, details) 
SELECT 
    ra.id,
    'fraud',
    30.0,
    'New device detected',
    '{"new_device": true, "device_fingerprint": "fp_ghi789jkl012", "previous_devices": 2}'
FROM risk_assessments ra 
WHERE ra.user_id = 'user_customer_001' AND ra.risk_score = 78.2;

INSERT INTO risk_factors (assessment_id, risk_type, risk_score, description, details) 
SELECT 
    ra.id,
    'geographic',
    25.0,
    'Transaction from different country',
    '{"user_country": "SA", "transaction_country": "US", "distance_km": 12000}'
FROM risk_assessments ra 
WHERE ra.user_id = 'user_customer_001' AND ra.risk_score = 78.2;

INSERT INTO risk_factors (assessment_id, risk_type, risk_score, description, details) 
SELECT 
    ra.id,
    'amount',
    23.2,
    'Large transaction amount',
    '{"amount": 5000.00, "user_avg_amount": 800.00, "amount_ratio": 6.25}'
FROM risk_assessments ra 
WHERE ra.user_id = 'user_customer_001' AND ra.risk_score = 78.2;

INSERT INTO risk_factors (assessment_id, risk_type, risk_score, description, details) 
SELECT 
    ra.id,
    'fraud',
    60.0,
    'Multiple failed attempts detected',
    '{"failed_attempts": 5, "time_window": "1 hour", "suspicious_patterns": true}'
FROM risk_assessments ra 
WHERE ra.user_id = 'user_customer_002' AND ra.risk_score = 92.1;

INSERT INTO risk_factors (assessment_id, risk_type, risk_score, description, details) 
SELECT 
    ra.id,
    'geographic',
    32.1,
    'High-risk country detected',
    '{"country": "XX", "risk_level": "high", "sanctions_check": false}'
FROM risk_assessments ra 
WHERE ra.user_id = 'user_customer_002' AND ra.risk_score = 92.1;

-- Insert sample risk rules
INSERT INTO risk_rules (name, risk_type, condition_text, risk_score, action, is_active, priority) VALUES
('High Amount Transaction', 'amount', 'amount > 10000', 30.0, 'require_verification', true, 1),
('Multiple Failed Attempts', 'fraud', 'failed_attempts > 3 in 1 hour', 40.0, 'block', true, 1),
('New Device High Amount', 'behavioral', 'new_device AND amount > 1000', 25.0, 'review', true, 2),
('High Risk Country', 'geographic', 'country IN high_risk_list', 50.0, 'block', true, 1),
('Off Hours Transaction', 'behavioral', 'hour BETWEEN 2 AND 6', 15.0, 'review', true, 4),
('Velocity Check', 'velocity', 'transactions > 5 in 1 hour', 35.0, 'delay_processing', true, 2),
('Unusual Payment Method', 'behavioral', 'new_payment_method AND amount > 500', 20.0, 'review', true, 3),
('Large Cash Equivalent', 'compliance', 'amount > 15000', 25.0, 'require_verification', true, 2);

-- Insert sample report metadata
INSERT INTO report_metadata (
    report_type, period, start_date, end_date, format, status, 
    file_path, file_size, requested_by, generated_at, expires_at
) VALUES 
(
    'revenue', 'daily', CURRENT_DATE - INTERVAL '7 days', CURRENT_DATE - INTERVAL '1 day',
    'json', 'completed', '/reports/revenue_daily_001.json', 15420,
    'user_admin_001', CURRENT_TIMESTAMP - INTERVAL '2 hours',
    CURRENT_TIMESTAMP + INTERVAL '22 hours'
),
(
    'transactions', 'weekly', CURRENT_DATE - INTERVAL '14 days', CURRENT_DATE - INTERVAL '7 days',
    'csv', 'completed', '/reports/transactions_weekly_001.csv', 45680,
    'user_admin_001', CURRENT_TIMESTAMP - INTERVAL '1 day',
    CURRENT_TIMESTAMP + INTERVAL '23 hours'
),
(
    'risk_analysis', 'monthly', CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE,
    'pdf', 'processing', NULL, NULL,
    'user_admin_002', CURRENT_TIMESTAMP - INTERVAL '30 minutes', NULL
),
(
    'settlements', 'custom', CURRENT_DATE - INTERVAL '10 days', CURRENT_DATE - INTERVAL '3 days',
    'excel', 'completed', '/reports/settlements_custom_001.xlsx', 28950,
    'user_manager_001', CURRENT_TIMESTAMP - INTERVAL '4 hours',
    CURRENT_TIMESTAMP + INTERVAL '20 hours'
);

-- Insert sample scheduled reports
INSERT INTO scheduled_reports (
    report_type, period, format, filters, schedule_type, schedule_config,
    is_active, requested_by, next_execution_at
) VALUES 
(
    'revenue', 'daily', 'json', '{"provider": "all", "currency": "SAR"}',
    'daily', '{"time": "08:00", "timezone": "Asia/Riyadh"}',
    true, 'user_admin_001', CURRENT_TIMESTAMP + INTERVAL '1 day'
),
(
    'transactions', 'weekly', 'csv', '{"status": "completed", "min_amount": 100}',
    'weekly', '{"day": "monday", "time": "09:00", "timezone": "Asia/Riyadh"}',
    true, 'user_admin_001', CURRENT_TIMESTAMP + INTERVAL '3 days'
),
(
    'risk_analysis', 'monthly', 'pdf', '{"risk_level": ["high", "critical"]}',
    'monthly', '{"day": 1, "time": "10:00", "timezone": "Asia/Riyadh"}',
    true, 'user_admin_002', CURRENT_TIMESTAMP + INTERVAL '15 days'
),
(
    'settlements', 'daily', 'json', '{"settlement_type": "instant", "min_amount": 1000}',
    'daily', '{"time": "18:00", "timezone": "Asia/Riyadh"}',
    false, 'user_manager_001', NULL
);

-- Update statistics for existing users
UPDATE users SET 
    total_transactions = (
        SELECT COUNT(*) FROM payments WHERE customer_id = users.id AND status = 'completed'
    ),
    total_volume = (
        SELECT COALESCE(SUM(amount), 0) FROM payments WHERE customer_id = users.id AND status = 'completed'
    ),
    last_transaction_at = (
        SELECT MAX(created_at) FROM payments WHERE customer_id = users.id
    )
WHERE id IN ('user_customer_001', 'user_customer_002', 'user_customer_003');

-- Create some sample webhook logs (if webhook_logs table exists)
-- This would be created in a separate webhook system

-- Log the seeding
INSERT INTO schema_migrations (version, name, file_path, checksum, execution_time_ms, success)
VALUES (
    'seed_005',
    'Payment System Data Seed',
    'database/seeds/005_payment_system_data.sql',
    'payment_system_seed_checksum',
    0,
    true
) ON CONFLICT (version) DO NOTHING;
