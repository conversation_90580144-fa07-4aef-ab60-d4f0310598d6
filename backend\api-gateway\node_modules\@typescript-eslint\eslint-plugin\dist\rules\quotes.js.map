{"version": 3, "file": "quotes.js", "sourceRoot": "", "sources": ["../../src/rules/quotes.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAM1D,kCAAqC;AACrC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,QAAQ,CAAC,CAAC;AAK7C,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,QAAQ;IACd,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,sBAAsB,CAAC;QACpC,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EACT,0EAA0E;YAC5E,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;KAC7B;IACD,cAAc,EAAE;QACd,QAAQ;QACR;YACE,qBAAqB,EAAE,KAAK;YAC5B,WAAW,EAAE,KAAK;SACnB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,SAAS,sBAAsB,CAAC,IAAsB;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,sBAAc,CAAC,0BAA0B,CAAC;gBAC/C,KAAK,sBAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,aAAa,CAAC;gBAClC,KAAK,sBAAc,CAAC,yBAAyB;oBAC3C,OAAO,IAAI,CAAC;gBAEd,KAAK,sBAAc,CAAC,YAAY;oBAC9B,OAAO,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;gBAE5B,KAAK,sBAAc,CAAC,4BAA4B,CAAC;gBACjD,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC;gBAE7B;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,CAAC,IAAI;gBACV,IAAI,MAAM,KAAK,UAAU,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC1D,OAAO;gBACT,CAAC;gBAED,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;YAED,eAAe,CAAC,IAAI;gBAClB,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}