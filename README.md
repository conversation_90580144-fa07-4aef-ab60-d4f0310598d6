# 🌟 WS Transfir - نظام تحويل الأموال المتقدم

<div align="center">

**منصة متكاملة لخدمات التحويلات المالية والمدفوعات الرقمية**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Flutter](https://img.shields.io/badge/Flutter-3.16+-blue.svg)](https://flutter.dev/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)

</div>

---

## 🎯 نظرة عامة

**WS Transfir** هو نظام متقدم لتحويل الأموال والمدفوعات الرقمية مصمم خصيصاً للسوق العربي. يوفر النظام حلولاً شاملة للأفراد والشركات لإجراء التحويلات المحلية والدولية بأمان وسرعة عالية.

### 🏆 المزايا التنافسية

- **🔒 أمان متقدم**: تشفير من الطرف إلى الطرف مع مصادقة ثنائية
- **⚡ سرعة فائقة**: معالجة فورية للتحويلات المحلية
- **🌍 تغطية عالمية**: دعم أكثر من 50 دولة
- **💱 أسعار تنافسية**: رسوم منخفضة وأسعار صرف مميزة
- **📱 تجربة متميزة**: واجهات سهلة الاستخدام عبر الويب والجوال
- **🤖 ذكاء اصطناعي**: كشف الاحتيال والتوصيات الذكية

---

## ✨ الميزات الرئيسية

### 💸 خدمات التحويلات
- **تحويلات محلية فورية** بين البنوك السعودية
- **تحويلات دولية** إلى أكثر من 50 دولة
- **تتبع مباشر** لحالة التحويلات
- **إشعارات فورية** عبر SMS والبريد الإلكتروني
- **سجل شامل** لجميع المعاملات

### 💳 المحفظة الرقمية
- **إدارة متعددة العملات** (ريال، دولار، يورو، جنيه)
- **شحن فوري** من البطاقات البنكية
- **سحب سريع** إلى الحسابات البنكية
- **دفع الفواتير** (كهرباء، ماء، اتصالات)
- **مدفوعات QR Code** للمتاجر

### 👤 إدارة المستخدمين
- **تسجيل مبسط** مع التحقق الفوري
- **KYC متقدم** مع التحقق من الهوية
- **مصادقة بيومترية** (بصمة، وجه)
- **إدارة المستفيدين** مع حفظ البيانات
- **حدود مرنة** حسب مستوى التحقق

### 📊 التقارير والتحليلات
- **لوحة تحكم تفاعلية** مع الرسوم البيانية
- **تقارير مالية مفصلة** (يومية، شهرية، سنوية)
- **تحليل الإنفاق** مع التصنيفات
- **إحصائيات الاستخدام** والأداء
- **تصدير البيانات** بصيغ متعددة (PDF, Excel, CSV)

### 🔐 الأمان والامتثال
- **تشفير AES-256** لجميع البيانات
- **مصادقة ثنائية** (SMS, Email, Authenticator)
- **كشف الاحتيال** بالذكاء الاصطناعي
- **امتثال PCI DSS** و ISO 27001
- **مراقبة AML** لمكافحة غسيل الأموال

---

## 🏗️ البنية التقنية

### 🖥️ Backend (Microservices)
```
├── api-gateway/          # بوابة API الرئيسية
├── auth-service/         # خدمة المصادقة والتفويض
├── user-service/         # إدارة المستخدمين والملفات الشخصية
├── transfer-service/     # معالجة التحويلات
├── wallet-service/       # إدارة المحافظ والأرصدة
├── notification-service/ # الإشعارات والتنبيهات
├── analytics-service/    # التقارير والتحليلات
└── ai-engine/           # محرك الذكاء الاصطناعي
```

### 🌐 Frontend
```
├── web-app/             # تطبيق الويب (Next.js)
├── admin-panel/         # لوحة الإدارة
└── agent-portal/        # بوابة الوكلاء
```

### 📱 Mobile Apps
```
└── flutter_app/         # تطبيق Flutter للجوال
```

### 🗄️ قواعد البيانات
- **PostgreSQL**: البيانات الرئيسية والمعاملات
- **Redis**: التخزين المؤقت والجلسات
- **MongoDB**: السجلات والتحليلات
- **Elasticsearch**: البحث والفهرسة

---

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية

- **Docker** 20.10+
- **Docker Compose** 2.0+
- **Node.js** 18+ (للتطوير المحلي)
- **Flutter** 3.16+ (لتطبيق الجوال)

### التثبيت السريع

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-org/ws-transfir.git
cd ws-transfir

# 2. إعداد البيئة
make setup

# 3. تحرير ملف البيئة
nano .env

# 4. تشغيل النظام
make dev

# 5. تهيئة قاعدة البيانات
make db-migrate
make db-seed
```

### الوصول للخدمات

| الخدمة | الرابط | الوصف |
|--------|--------|--------|
| 🌐 تطبيق الويب | http://localhost:3100 | الواجهة الرئيسية |
| 🔌 API Gateway | http://localhost:3000 | بوابة API |
| 📊 Grafana | http://localhost:3007 | المراقبة والتصور |
| 🐰 RabbitMQ | http://localhost:15672 | إدارة الرسائل |
| 🗄️ Adminer | http://localhost:8080 | إدارة قواعد البيانات |

---

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](./CONTRIBUTING.md) قبل البدء.

### خطوات المساهمة

1. **Fork** المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/amazing-feature`)
3. **Commit** التغييرات (`git commit -m 'Add amazing feature'`)
4. **Push** للفرع (`git push origin feature/amazing-feature`)
5. فتح **Pull Request**

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 الدعم والتواصل

- **📧 البريد الإلكتروني**: <EMAIL>
- **🌐 الموقع الإلكتروني**: https://wstransfir.com
- **📖 التوثيق**: https://docs.wstransfir.com

---

<div align="center">

**صُنع بـ ❤️ في المملكة العربية السعودية**

</div>
