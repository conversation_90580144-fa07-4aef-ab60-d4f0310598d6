import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, Length } from 'class-validator';

export enum OtpType {
  VERIFICATION = 'verification',
  PASSWORD_RESET = 'password_reset',
  TWO_FACTOR = 'two_factor',
}

export class VerifyOtpDto {
  @ApiProperty({
    description: 'معرف المستخدم',
    example: 'user-uuid-here',
  })
  @IsString({ message: 'معرف المستخدم يجب أن يكون نص' })
  @IsNotEmpty({ message: 'معرف المستخدم مطلوب' })
  userId: string;

  @ApiProperty({
    description: 'رمز التحقق',
    example: '123456',
  })
  @IsString({ message: 'رمز التحقق يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رمز التحقق مطلوب' })
  @Length(6, 6, { message: 'رمز التحقق يجب أن يكون 6 أرقام' })
  otp: string;

  @ApiProperty({
    description: 'نوع رمز التحقق',
    enum: OtpType,
    example: OtpType.VERIFICATION,
  })
  @IsEnum(OtpType, { message: 'نوع رمز التحقق غير صحيح' })
  @IsNotEmpty({ message: 'نوع رمز التحقق مطلوب' })
  type: OtpType;
}
