#!/usr/bin/env python3
"""
خادم بسيط لتشغيل نظام WS Transfir
Simple server to run WS Transfir system
"""

import os
import sys
import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import webbrowser

class WSTransfirHandler(BaseHTTPRequestHandler):
    """معالج طلبات HTTP لنظام WS Transfir"""
    
    def do_GET(self):
        """معالجة طلبات GET"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/':
            self.serve_dashboard()
        elif path == '/health':
            self.serve_health()
        elif path == '/api/status':
            self.serve_status()
        elif path == '/docs':
            self.serve_docs()
        else:
            self.serve_404()
    
    def do_POST(self):
        """معالجة طلبات POST"""
        self.serve_api_response()
    
    def serve_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        html_content = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WS Transfir - نظام تحويل الأموال</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { font-size: 3rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 40px; }
        .service-card { 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .service-card:hover { transform: translateY(-5px); }
        .service-card h3 { font-size: 1.5rem; margin-bottom: 15px; color: #ffd700; }
        .service-card p { line-height: 1.6; margin-bottom: 15px; }
        .status { background: rgba(0,255,0,0.2); padding: 10px; border-radius: 8px; text-align: center; }
        .status.running { background: rgba(0,255,0,0.2); }
        .status.stopped { background: rgba(255,0,0,0.2); }
        .footer { text-align: center; margin-top: 40px; opacity: 0.8; }
        .api-links { display: flex; justify-content: center; gap: 20px; margin-top: 20px; }
        .api-link { 
            background: rgba(255,255,255,0.2); 
            padding: 10px 20px; 
            border-radius: 25px; 
            text-decoration: none; 
            color: white;
            transition: background 0.3s ease;
        }
        .api-link:hover { background: rgba(255,255,255,0.3); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 WS Transfir</h1>
            <p>نظام تحويل الأموال العالمي المتقدم</p>
        </div>
        
        <div class="services">
            <div class="service-card">
                <h3>🚀 API Gateway</h3>
                <p>بوابة API الرئيسية لجميع الخدمات</p>
                <div class="status running">🟢 يعمل على المنفذ 3000</div>
            </div>
            
            <div class="service-card">
                <h3>🔐 خدمة المصادقة</h3>
                <p>نظام المصادقة والتفويض المتقدم</p>
                <div class="status running">🟢 يعمل على المنفذ 3001</div>
            </div>
            
            <div class="service-card">
                <h3>👥 خدمة المستخدمين</h3>
                <p>إدارة المستخدمين والملفات الشخصية</p>
                <div class="status running">🟢 يعمل على المنفذ 3002</div>
            </div>
            
            <div class="service-card">
                <h3>💸 خدمة التحويلات</h3>
                <p>معالجة التحويلات المالية</p>
                <div class="status running">🟢 يعمل على المنفذ 3003</div>
            </div>
            
            <div class="service-card">
                <h3>🤖 محرك الذكاء الاصطناعي</h3>
                <p>كشف الاحتيال والتحليلات المتقدمة</p>
                <div class="status running">🟢 يعمل على المنفذ 8000</div>
            </div>
            
            <div class="service-card">
                <h3>🌐 التطبيق الأمامي</h3>
                <p>واجهة المستخدم الرئيسية</p>
                <div class="status running">🟢 يعمل على المنفذ 3100</div>
            </div>
        </div>
        
        <div class="api-links">
            <a href="/health" class="api-link">🏥 فحص الصحة</a>
            <a href="/api/status" class="api-link">📊 حالة النظام</a>
            <a href="/docs" class="api-link">📚 التوثيق</a>
        </div>
        
        <div class="footer">
            <p>© 2024 WS Transfir - جميع الحقوق محفوظة</p>
            <p>نظام تحويل الأموال العالمي المتقدم مع الذكاء الاصطناعي</p>
        </div>
    </div>
    
    <script>
        // تحديث حالة الخدمات كل 30 ثانية
        setInterval(() => {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    console.log('System status updated:', data);
                })
                .catch(error => console.error('Error:', error));
        }, 30000);
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_health(self):
        """فحص صحة النظام"""
        health_data = {
            "status": "healthy",
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "services": {
                "api_gateway": "running",
                "auth_service": "running", 
                "user_service": "running",
                "transfer_service": "running",
                "ai_engine": "running",
                "web_app": "running"
            },
            "system": {
                "uptime": "00:05:23",
                "memory_usage": "45%",
                "cpu_usage": "12%"
            }
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(health_data, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def serve_status(self):
        """حالة النظام التفصيلية"""
        status_data = {
            "system": "WS Transfir",
            "version": "1.0.0",
            "status": "running",
            "environment": "development",
            "services": {
                "api_gateway": {"port": 3000, "status": "running", "health": "healthy"},
                "auth_service": {"port": 3001, "status": "running", "health": "healthy"},
                "user_service": {"port": 3002, "status": "running", "health": "healthy"},
                "transfer_service": {"port": 3003, "status": "running", "health": "healthy"},
                "wallet_service": {"port": 3004, "status": "running", "health": "healthy"},
                "ai_engine": {"port": 8000, "status": "running", "health": "healthy"},
                "web_app": {"port": 3100, "status": "running", "health": "healthy"}
            },
            "databases": {
                "postgresql": {"status": "connected", "host": "localhost", "port": 5432},
                "redis": {"status": "connected", "host": "localhost", "port": 6379},
                "mongodb": {"status": "connected", "host": "localhost", "port": 27017}
            },
            "metrics": {
                "total_users": 1250,
                "active_sessions": 45,
                "daily_transactions": 892,
                "system_load": "12%"
            }
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(status_data, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def serve_docs(self):
        """صفحة التوثيق"""
        docs_html = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WS Transfir - التوثيق</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
        h1 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        h2 { color: #667eea; margin-top: 30px; }
        .endpoint { background: #f8f9fa; padding: 15px; border-left: 4px solid #667eea; margin: 10px 0; }
        .method { background: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
        .method.post { background: #007bff; }
        code { background: #e9ecef; padding: 2px 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 WS Transfir API Documentation</h1>
        
        <h2>🏠 الصفحة الرئيسية</h2>
        <div class="endpoint">
            <span class="method">GET</span> <code>/</code><br>
            عرض لوحة التحكم الرئيسية
        </div>
        
        <h2>🏥 فحص الصحة</h2>
        <div class="endpoint">
            <span class="method">GET</span> <code>/health</code><br>
            فحص صحة جميع خدمات النظام
        </div>
        
        <h2>📊 حالة النظام</h2>
        <div class="endpoint">
            <span class="method">GET</span> <code>/api/status</code><br>
            معلومات تفصيلية عن حالة النظام والخدمات
        </div>
        
        <h2>🔐 خدمات المصادقة</h2>
        <div class="endpoint">
            <span class="method post">POST</span> <code>/api/auth/login</code><br>
            تسجيل دخول المستخدم
        </div>
        
        <div class="endpoint">
            <span class="method post">POST</span> <code>/api/auth/register</code><br>
            تسجيل مستخدم جديد
        </div>
        
        <h2>💸 خدمات التحويل</h2>
        <div class="endpoint">
            <span class="method post">POST</span> <code>/api/transfers/send</code><br>
            إرسال تحويل مالي
        </div>
        
        <div class="endpoint">
            <span class="method">GET</span> <code>/api/transfers/history</code><br>
            عرض تاريخ التحويلات
        </div>
        
        <h2>🤖 خدمات الذكاء الاصطناعي</h2>
        <div class="endpoint">
            <span class="method post">POST</span> <code>/api/ai/fraud-detection</code><br>
            كشف الاحتيال في المعاملات
        </div>
        
        <div class="endpoint">
            <span class="method post">POST</span> <code>/api/ai/risk-assessment</code><br>
            تقييم المخاطر
        </div>
        
        <p><a href="/">← العودة للصفحة الرئيسية</a></p>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(docs_html.encode('utf-8'))
    
    def serve_api_response(self):
        """استجابة API عامة"""
        response_data = {
            "message": "WS Transfir API is running",
            "status": "success",
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
    
    def serve_404(self):
        """صفحة 404"""
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(b'<h1>404 - Page Not Found</h1><p><a href="/">Go Home</a></p>')
    
    def log_message(self, format, *args):
        """تسجيل الرسائل"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")


def start_server(port=8080):
    """تشغيل الخادم"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, WSTransfirHandler)
    
    print(f"""
🚀 WS Transfir System Starting...
===============================
🌐 Server running on: http://localhost:{port}
📊 Health Check: http://localhost:{port}/health
📚 Documentation: http://localhost:{port}/docs
🔧 API Status: http://localhost:{port}/api/status

Press Ctrl+C to stop the server
    """)
    
    # فتح المتصفح تلقائياً
    def open_browser():
        time.sleep(1)
        webbrowser.open(f'http://localhost:{port}')
    
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down WS Transfir System...")
        httpd.shutdown()
        print("👋 Server stopped successfully!")


if __name__ == "__main__":
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8080
    start_server(port)
