import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import * as helmet from 'helmet';
import * as compression from 'compression';
import * as cors from 'cors';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // الأمان والحماية
  app.use(helmet());
  app.use(compression());
  app.use(cors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3100'],
    credentials: true,
  }));

  // التحقق من صحة البيانات
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // إعداد Swagger للوثائق
  const config = new DocumentBuilder()
    .setTitle('WS Transfir API')
    .setDescription('نظام تحويل الأموال العالمي - واجهة برمجة التطبيقات')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('auth', 'خدمات المصادقة والتوثيق')
    .addTag('users', 'إدارة المستخدمين')
    .addTag('transfers', 'عمليات التحويل')
    .addTag('wallets', 'إدارة المحافظ')
    .addTag('agents', 'إدارة الوكلاء')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    customSiteTitle: 'WS Transfir API Documentation',
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info .title { color: #1976d2; }
    `,
  });

  // تشغيل الخادم
  const port = process.env.PORT || 3000;
  await app.listen(port);
  
  console.log(`🚀 API Gateway يعمل على المنفذ ${port}`);
  console.log(`📚 الوثائق متاحة على: http://localhost:${port}/api/docs`);
}

bootstrap();
