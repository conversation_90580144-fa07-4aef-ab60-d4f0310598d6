{"name": "class-transformer", "version": "0.5.1", "description": "Proper decorator-based transformation / serialization / deserialization of plain javascript objects to class constructors", "author": "TypeStack contributors", "license": "MIT", "readmeFilename": "README.md", "sideEffects": false, "main": "./cjs/index.js", "module": "./esm5/index.js", "es2015": "./esm2015/index.js", "typings": "./types/index.d.ts", "repository": {"type": "git", "url": "https://github.com/typestack/class-transformer.git"}, "tags": ["serialization", "deserialization", "serializer", "typescript", "object-to-class", "typescript-serializer"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.md": ["npm run prettier:fix"], "*.ts": ["npm run prettier:fix"]}}