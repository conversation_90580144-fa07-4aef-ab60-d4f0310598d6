import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsS<PERSON>,
  <PERSON>N<PERSON>ber,
  IsEnum,
  IsOptional,
  IsEmail,
  Is<PERSON>hone<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum TransferType {
  CASH_PICKUP = 'cash_pickup',
  BANK_DEPOSIT = 'bank_deposit',
  MOBILE_WALLET = 'mobile_wallet',
  HOME_DELIVERY = 'home_delivery',
}

export enum TransferPurpose {
  FAMILY_SUPPORT = 'family_support',
  EDUCATION = 'education',
  MEDICAL = 'medical',
  BUSINESS = 'business',
  INVESTMENT = 'investment',
  OTHER = 'other',
}

export class RecipientDto {
  @ApiProperty({
    description: 'اسم المستلم الأول',
    example: 'فاطمة',
  })
  @IsString({ message: 'اسم المستلم الأول يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم المستلم الأول مطلوب' })
  firstName: string;

  @ApiProperty({
    description: 'اسم المستلم الأخير',
    example: 'أحمد',
  })
  @IsString({ message: 'اسم المستلم الأخير يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم المستلم الأخير مطلوب' })
  lastName: string;

  @ApiProperty({
    description: 'رقم هاتف المستلم',
    example: '+************',
  })
  @IsPhoneNumber('SA', { message: 'رقم الهاتف غير صحيح' })
  @IsNotEmpty({ message: 'رقم الهاتف مطلوب' })
  phone: string;

  @ApiProperty({
    description: 'البريد الإلكتروني للمستلم',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'البريد الإلكتروني غير صحيح' })
  email?: string;

  @ApiProperty({
    description: 'رقم الهوية الوطنية للمستلم',
    example: '**********',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الهوية يجب أن يكون نص' })
  nationalId?: string;

  @ApiProperty({
    description: 'العنوان',
    example: 'الرياض، المملكة العربية السعودية',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'العنوان يجب أن يكون نص' })
  address?: string;
}

export class BankDetailsDto {
  @ApiProperty({
    description: 'اسم البنك',
    example: 'البنك الأهلي السعودي',
  })
  @IsString({ message: 'اسم البنك يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم البنك مطلوب' })
  bankName: string;

  @ApiProperty({
    description: 'رقم الحساب البنكي',
    example: '****************',
  })
  @IsString({ message: 'رقم الحساب يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم الحساب مطلوب' })
  accountNumber: string;

  @ApiProperty({
    description: 'رمز البنك (SWIFT/IBAN)',
    example: 'NCBKSARI',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رمز البنك يجب أن يكون نص' })
  bankCode?: string;
}

export class CreateTransferDto {
  @ApiProperty({
    description: 'المبلغ المرسل',
    example: 1000,
    minimum: 1,
    maximum: 50000,
  })
  @IsNumber({}, { message: 'المبلغ يجب أن يكون رقم' })
  @IsNotEmpty({ message: 'المبلغ مطلوب' })
  @Min(1, { message: 'المبلغ يجب أن يكون أكبر من صفر' })
  @Max(50000, { message: 'المبلغ يجب أن يكون أقل من 50,000' })
  amount: number;

  @ApiProperty({
    description: 'عملة الإرسال',
    example: 'SAR',
  })
  @IsString({ message: 'عملة الإرسال يجب أن تكون نص' })
  @IsNotEmpty({ message: 'عملة الإرسال مطلوبة' })
  fromCurrency: string;

  @ApiProperty({
    description: 'عملة الاستلام',
    example: 'USD',
  })
  @IsString({ message: 'عملة الاستلام يجب أن تكون نص' })
  @IsNotEmpty({ message: 'عملة الاستلام مطلوبة' })
  toCurrency: string;

  @ApiProperty({
    description: 'بلد الاستلام',
    example: 'US',
  })
  @IsString({ message: 'بلد الاستلام يجب أن يكون نص' })
  @IsNotEmpty({ message: 'بلد الاستلام مطلوب' })
  destinationCountry: string;

  @ApiProperty({
    description: 'نوع التحويل',
    enum: TransferType,
    example: TransferType.CASH_PICKUP,
  })
  @IsEnum(TransferType, { message: 'نوع التحويل غير صحيح' })
  @IsNotEmpty({ message: 'نوع التحويل مطلوب' })
  transferType: TransferType;

  @ApiProperty({
    description: 'بيانات المستلم',
    type: RecipientDto,
  })
  @ValidateNested()
  @Type(() => RecipientDto)
  @IsNotEmpty({ message: 'بيانات المستلم مطلوبة' })
  recipient: RecipientDto;

  @ApiProperty({
    description: 'تفاصيل البنك (مطلوب للتحويل البنكي)',
    type: BankDetailsDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BankDetailsDto)
  bankDetails?: BankDetailsDto;

  @ApiProperty({
    description: 'غرض التحويل',
    enum: TransferPurpose,
    example: TransferPurpose.FAMILY_SUPPORT,
  })
  @IsEnum(TransferPurpose, { message: 'غرض التحويل غير صحيح' })
  @IsNotEmpty({ message: 'غرض التحويل مطلوب' })
  purpose: TransferPurpose;

  @ApiProperty({
    description: 'ملاحظات إضافية',
    example: 'تحويل شهري للعائلة',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الملاحظات يجب أن تكون نص' })
  notes?: string;

  @ApiProperty({
    description: 'رسالة للمستلم',
    example: 'مع تحيات العائلة',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الرسالة يجب أن تكون نص' })
  messageToRecipient?: string;
}
