/**
 * اختبار صحة الكود النحوية لخدمة المصادقة
 * Test Auth Service Code Syntax
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 فحص صحة الكود النحوية');
console.log('==========================');

// قائمة الملفات للفحص
const filesToCheck = [
  'backend/auth-service/src/main.ts',
  'backend/auth-service/src/app.module.ts',
  'backend/auth-service/src/modules/users/entities/user.entity.ts',
  'backend/auth-service/src/modules/auth/services/auth.service.ts',
  'backend/auth-service/src/modules/auth/controllers/auth.controller.ts',
  'backend/auth-service/src/modules/auth/dto/register.dto.ts',
  'backend/auth-service/src/shared/health/health.service.ts',
];

let totalFiles = 0;
let validFiles = 0;
let issues = [];

filesToCheck.forEach(filePath => {
  totalFiles++;
  console.log(`\n📄 فحص: ${filePath}`);
  
  try {
    if (!fs.existsSync(filePath)) {
      console.log('   ❌ الملف غير موجود');
      issues.push(`${filePath}: ملف غير موجود`);
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    // فحوصات أساسية
    const checks = [
      {
        name: 'طول الملف',
        test: () => content.length > 0,
        message: 'الملف فارغ'
      },
      {
        name: 'استيراد الوحدات',
        test: () => content.includes('import'),
        message: 'لا يحتوي على استيرادات'
      },
      {
        name: 'تصدير الوحدات',
        test: () => content.includes('export'),
        message: 'لا يحتوي على تصديرات'
      },
      {
        name: 'أقواس متوازنة',
        test: () => {
          const openBraces = (content.match(/\{/g) || []).length;
          const closeBraces = (content.match(/\}/g) || []).length;
          return openBraces === closeBraces;
        },
        message: 'أقواس غير متوازنة'
      },
      {
        name: 'أقواس دائرية متوازنة',
        test: () => {
          const openParens = (content.match(/\(/g) || []).length;
          const closeParens = (content.match(/\)/g) || []).length;
          return openParens === closeParens;
        },
        message: 'أقواس دائرية غير متوازنة'
      }
    ];

    let fileValid = true;
    checks.forEach(check => {
      try {
        if (check.test()) {
          console.log(`   ✅ ${check.name}`);
        } else {
          console.log(`   ❌ ${check.name}: ${check.message}`);
          issues.push(`${filePath}: ${check.message}`);
          fileValid = false;
        }
      } catch (error) {
        console.log(`   ⚠️  ${check.name}: خطأ في الفحص`);
      }
    });

    // فحوصات خاصة بنوع الملف
    if (filePath.includes('.entity.ts')) {
      if (content.includes('@Entity')) {
        console.log('   ✅ كيان TypeORM');
      } else {
        console.log('   ❌ مفقود: @Entity decorator');
        issues.push(`${filePath}: مفقود @Entity decorator`);
        fileValid = false;
      }
    }

    if (filePath.includes('.controller.ts')) {
      if (content.includes('@Controller')) {
        console.log('   ✅ NestJS Controller');
      } else {
        console.log('   ❌ مفقود: @Controller decorator');
        issues.push(`${filePath}: مفقود @Controller decorator`);
        fileValid = false;
      }
    }

    if (filePath.includes('.service.ts')) {
      if (content.includes('@Injectable')) {
        console.log('   ✅ NestJS Service');
      } else {
        console.log('   ❌ مفقود: @Injectable decorator');
        issues.push(`${filePath}: مفقود @Injectable decorator`);
        fileValid = false;
      }
    }

    if (filePath.includes('.module.ts')) {
      if (content.includes('@Module')) {
        console.log('   ✅ NestJS Module');
      } else {
        console.log('   ❌ مفقود: @Module decorator');
        issues.push(`${filePath}: مفقود @Module decorator`);
        fileValid = false;
      }
    }

    if (filePath.includes('.dto.ts')) {
      if (content.includes('@ApiProperty') || content.includes('@IsString') || content.includes('@IsEmail')) {
        console.log('   ✅ DTO Validation');
      } else {
        console.log('   ❌ مفقود: validation decorators');
        issues.push(`${filePath}: مفقود validation decorators`);
        fileValid = false;
      }
    }

    if (fileValid) {
      validFiles++;
      console.log('   🎉 الملف صحيح');
    }

  } catch (error) {
    console.log(`   ❌ خطأ في قراءة الملف: ${error.message}`);
    issues.push(`${filePath}: خطأ في القراءة - ${error.message}`);
  }
});

// فحص package.json
console.log('\n📦 فحص package.json:');
try {
  const packagePath = 'backend/auth-service/package.json';
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // فحص البرامج النصية المطلوبة
  const requiredScripts = ['start', 'start:dev', 'build', 'test'];
  requiredScripts.forEach(script => {
    if (packageContent.scripts && packageContent.scripts[script]) {
      console.log(`   ✅ Script: ${script}`);
    } else {
      console.log(`   ❌ Script مفقود: ${script}`);
      issues.push(`package.json: مفقود script ${script}`);
    }
  });

  // فحص التبعيات الأساسية
  const requiredDeps = [
    '@nestjs/common',
    '@nestjs/core',
    '@nestjs/platform-express',
    'reflect-metadata',
    'rxjs'
  ];

  requiredDeps.forEach(dep => {
    if (packageContent.dependencies && packageContent.dependencies[dep]) {
      console.log(`   ✅ Dependency: ${dep}`);
    } else {
      console.log(`   ❌ Dependency مفقود: ${dep}`);
      issues.push(`package.json: مفقود dependency ${dep}`);
    }
  });

} catch (error) {
  console.log(`   ❌ خطأ في فحص package.json: ${error.message}`);
  issues.push(`package.json: خطأ في القراءة - ${error.message}`);
}

// فحص tsconfig.json
console.log('\n⚙️  فحص tsconfig.json:');
try {
  const tsconfigPath = 'backend/auth-service/tsconfig.json';
  const tsconfigContent = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
  
  if (tsconfigContent.compilerOptions) {
    console.log('   ✅ compilerOptions موجود');
    
    const requiredOptions = ['target', 'module', 'outDir', 'experimentalDecorators', 'emitDecoratorMetadata'];
    requiredOptions.forEach(option => {
      if (tsconfigContent.compilerOptions[option] !== undefined) {
        console.log(`   ✅ ${option}: ${tsconfigContent.compilerOptions[option]}`);
      } else {
        console.log(`   ❌ مفقود: ${option}`);
        issues.push(`tsconfig.json: مفقود ${option}`);
      }
    });
  } else {
    console.log('   ❌ compilerOptions مفقود');
    issues.push('tsconfig.json: compilerOptions مفقود');
  }

} catch (error) {
  console.log(`   ❌ خطأ في فحص tsconfig.json: ${error.message}`);
  issues.push(`tsconfig.json: خطأ في القراءة - ${error.message}`);
}

// النتائج النهائية
console.log('\n📊 نتائج الفحص:');
console.log(`✅ ملفات صحيحة: ${validFiles}/${totalFiles}`);
console.log(`❌ مشاكل وجدت: ${issues.length}`);
console.log(`📈 نسبة النجاح: ${Math.round((validFiles / totalFiles) * 100)}%`);

if (issues.length > 0) {
  console.log('\n❌ المشاكل المكتشفة:');
  issues.forEach((issue, index) => {
    console.log(`   ${index + 1}. ${issue}`);
  });
}

console.log('\n🎯 التقييم العام:');
if (issues.length === 0) {
  console.log('🎉 ممتاز! الكود يبدو صحيحاً نحوياً');
  console.log('💡 الخطوة التالية: تثبيت التبعيات وتشغيل الخدمة');
} else if (issues.length <= 3) {
  console.log('⚠️  جيد مع بعض المشاكل البسيطة');
  console.log('💡 يمكن إصلاح هذه المشاكل بسهولة');
} else {
  console.log('❌ يحتاج إلى مراجعة وإصلاح');
  console.log('💡 يرجى إصلاح المشاكل المذكورة أعلاه');
}

// فحص إضافي للبنية
console.log('\n🏗️  فحص البنية العامة:');
const structureChecks = [
  {
    name: 'مجلد src',
    path: 'backend/auth-service/src',
    type: 'directory'
  },
  {
    name: 'مجلد modules',
    path: 'backend/auth-service/src/modules',
    type: 'directory'
  },
  {
    name: 'مجلد shared',
    path: 'backend/auth-service/src/shared',
    type: 'directory'
  },
  {
    name: 'مجلد test',
    path: 'backend/auth-service/test',
    type: 'directory'
  }
];

structureChecks.forEach(check => {
  try {
    if (fs.existsSync(check.path)) {
      const stats = fs.statSync(check.path);
      if (check.type === 'directory' && stats.isDirectory()) {
        const files = fs.readdirSync(check.path);
        console.log(`   ✅ ${check.name} (${files.length} عنصر)`);
      } else if (check.type === 'file' && stats.isFile()) {
        console.log(`   ✅ ${check.name}`);
      }
    } else {
      console.log(`   ❌ ${check.name}: غير موجود`);
    }
  } catch (error) {
    console.log(`   ❌ ${check.name}: خطأ في الفحص`);
  }
});

console.log('\n✨ انتهى فحص الكود!');
