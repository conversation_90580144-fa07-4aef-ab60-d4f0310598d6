{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestProvider", "title": "Nest Provider Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the provider.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the provider?"}, "path": {"type": "string", "format": "path", "description": "The path to create the provider."}, "language": {"type": "string", "description": "Nest provider language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest provider source root directory."}, "flat": {"type": "boolean", "default": true, "description": "Flag to indicate if a directory is created."}, "spec": {"type": "boolean", "default": true, "description": "Specifies if a spec file is generated."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}, "className": {"type": "string", "description": "Class name to be used internally."}}, "required": ["name"]}