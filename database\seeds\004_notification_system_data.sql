-- Seed: Notification System Data
-- Description: إنشاء بيانات نظام الإشعارات التجريبية
-- Version: 004
-- Created: 2024-01-16

-- Insert default notification templates

-- Transaction Success Templates
INSERT INTO notification_templates (
    notification_type, channel, language, name, subject_template, body_template, variables, description, created_by
) VALUES 
-- Arabic Email Template for Transaction Success
(
    'transaction_success',
    'email',
    'ar',
    'نجح التحويل - بريد إلكتروني',
    'تم تنفيذ عملية التحويل بنجاح',
    'مرحباً {user_name},

تم تنفيذ عملية التحويل الخاصة بك بنجاح.

تفاصيل العملية:
- رقم العملية: {transaction_id}
- نوع العملية: {transaction_type}
- المبلغ: {amount} {currency}
- التاريخ: {timestamp}

شكراً لاستخدامك خدماتنا.

فريق WS Transfir',
    ARRAY['user_name', 'transaction_id', 'transaction_type', 'amount', 'currency', 'timestamp'],
    'قالب البريد الإلكتروني لنجاح التحويل باللغة العربية',
    'user_admin_001'
),

-- Arabic SMS Template for Transaction Success
(
    'transaction_success',
    'sms',
    'ar',
    'نجح التحويل - رسالة نصية',
    '',
    'تم تنفيذ التحويل {transaction_id} بمبلغ {amount} {currency} بنجاح. WS Transfir',
    ARRAY['transaction_id', 'amount', 'currency'],
    'قالب الرسالة النصية لنجاح التحويل باللغة العربية',
    'user_admin_001'
),

-- Arabic In-App Template for Transaction Success
(
    'transaction_success',
    'in_app',
    'ar',
    'نجح التحويل - داخل التطبيق',
    'تم التحويل بنجاح',
    'تم تنفيذ عملية التحويل {transaction_id} بمبلغ {amount} {currency} بنجاح.',
    ARRAY['transaction_id', 'amount', 'currency'],
    'قالب الإشعار داخل التطبيق لنجاح التحويل باللغة العربية',
    'user_admin_001'
),

-- Transaction Failed Templates
(
    'transaction_failed',
    'email',
    'ar',
    'فشل التحويل - بريد إلكتروني',
    'فشل في تنفيذ عملية التحويل',
    'مرحباً {user_name},

نأسف لإبلاغك بأن عملية التحويل الخاصة بك لم تكتمل.

تفاصيل العملية:
- رقم العملية: {transaction_id}
- نوع العملية: {transaction_type}
- المبلغ: {amount} {currency}
- التاريخ: {timestamp}
- سبب الفشل: {failure_reason}

يرجى المحاولة مرة أخرى أو التواصل مع خدمة العملاء.

فريق WS Transfir',
    ARRAY['user_name', 'transaction_id', 'transaction_type', 'amount', 'currency', 'timestamp', 'failure_reason'],
    'قالب البريد الإلكتروني لفشل التحويل باللغة العربية',
    'user_admin_001'
),

-- Security Alert Templates
(
    'security_alert',
    'email',
    'ar',
    'تنبيه أمني - بريد إلكتروني',
    'تنبيه أمني مهم',
    'مرحباً {user_name},

تم اكتشاف نشاط مشبوه في حسابك.

تفاصيل التنبيه:
- نوع التنبيه: {alert_type}
- التاريخ: {timestamp}
- عنوان IP: {ip_address}

إذا لم تكن أنت من قام بهذا النشاط، يرجى تغيير كلمة المرور فوراً والتواصل معنا.

فريق الأمان - WS Transfir',
    ARRAY['user_name', 'alert_type', 'timestamp', 'ip_address'],
    'قالب البريد الإلكتروني للتنبيه الأمني باللغة العربية',
    'user_admin_001'
),

(
    'security_alert',
    'sms',
    'ar',
    'تنبيه أمني - رسالة نصية',
    '',
    'تنبيه أمني: تم اكتشاف {alert_type} في حسابك. إذا لم تكن أنت، غير كلمة المرور فوراً. WS Transfir',
    ARRAY['alert_type'],
    'قالب الرسالة النصية للتنبيه الأمني باللغة العربية',
    'user_admin_001'
),

-- Account Verification Templates
(
    'account_verification',
    'email',
    'ar',
    'تحقق من الحساب - بريد إلكتروني',
    'تحقق من حسابك في WS Transfir',
    'مرحباً {user_name},

مرحباً بك في WS Transfir!

لإكمال تسجيلك، يرجى تأكيد بريدك الإلكتروني بالنقر على الرابط أدناه:

{verification_link}

هذا الرابط صالح لمدة 24 ساعة.

إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذه الرسالة.

فريق WS Transfir',
    ARRAY['user_name', 'verification_link'],
    'قالب البريد الإلكتروني للتحقق من الحساب باللغة العربية',
    'user_admin_001'
),

-- KYC Update Templates
(
    'kyc_update',
    'email',
    'ar',
    'تحديث KYC - بريد إلكتروني',
    'تحديث حالة التحقق من الهوية',
    'مرحباً {user_name},

تم تحديث حالة التحقق من هويتك.

الحالة الجديدة: {kyc_status}
المستوى: {kyc_level}

{additional_message}

فريق WS Transfir',
    ARRAY['user_name', 'kyc_status', 'kyc_level', 'additional_message'],
    'قالب البريد الإلكتروني لتحديث KYC باللغة العربية',
    'user_admin_001'
),

-- Agent Commission Templates
(
    'agent_commission',
    'email',
    'ar',
    'عمولة الوكيل - بريد إلكتروني',
    'تم إضافة عمولة جديدة',
    'مرحباً {agent_name},

تم إضافة عمولة جديدة إلى حسابك.

تفاصيل العمولة:
- المبلغ: {commission_amount} {currency}
- نوع العمولة: {commission_type}
- من المعاملة: {transaction_id}
- التاريخ: {timestamp}

إجمالي عمولاتك هذا الشهر: {monthly_total} {currency}

فريق WS Transfir',
    ARRAY['agent_name', 'commission_amount', 'currency', 'commission_type', 'transaction_id', 'timestamp', 'monthly_total'],
    'قالب البريد الإلكتروني لعمولة الوكيل باللغة العربية',
    'user_admin_001'
),

-- System Maintenance Templates
(
    'system_maintenance',
    'email',
    'ar',
    'صيانة النظام - بريد إلكتروني',
    '{title}',
    'مرحباً,

{message}

نعتذر عن أي إزعاج قد يسببه هذا.

فريق WS Transfir',
    ARRAY['title', 'message'],
    'قالب البريد الإلكتروني لصيانة النظام باللغة العربية',
    'user_admin_001'
),

(
    'system_maintenance',
    'in_app',
    'ar',
    'صيانة النظام - داخل التطبيق',
    '{title}',
    '{message}',
    ARRAY['title', 'message'],
    'قالب الإشعار داخل التطبيق لصيانة النظام باللغة العربية',
    'user_admin_001'
),

-- Wallet Balance Templates
(
    'wallet_balance',
    'sms',
    'ar',
    'رصيد المحفظة - رسالة نصية',
    '',
    'رصيدك الحالي: {balance} {currency}. آخر معاملة: {last_transaction_amount} {currency}. WS Transfir',
    ARRAY['balance', 'currency', 'last_transaction_amount'],
    'قالب الرسالة النصية لرصيد المحفظة باللغة العربية',
    'user_admin_001'
),

-- Promotional Templates
(
    'promotional',
    'email',
    'ar',
    'عرض ترويجي - بريد إلكتروني',
    '{offer_title}',
    'مرحباً {user_name},

{offer_description}

تفاصيل العرض:
- العرض: {offer_title}
- الخصم: {discount_percentage}%
- صالح حتى: {expiry_date}

لا تفوت هذه الفرصة!

{call_to_action}

فريق WS Transfir',
    ARRAY['user_name', 'offer_title', 'offer_description', 'discount_percentage', 'expiry_date', 'call_to_action'],
    'قالب البريد الإلكتروني للعروض الترويجية باللغة العربية',
    'user_admin_001'
) ON CONFLICT (notification_type, channel, language) DO NOTHING;

-- Insert template variables for the templates
INSERT INTO template_variables (template_id, variable_name, variable_type, description, is_required) 
SELECT 
    nt.id,
    unnest(nt.variables) as variable_name,
    'string' as variable_type,
    'متغير القالب: ' || unnest(nt.variables) as description,
    true as is_required
FROM notification_templates nt
WHERE nt.variables IS NOT NULL AND array_length(nt.variables, 1) > 0
ON CONFLICT (template_id, variable_name) DO NOTHING;

-- Insert sample notifications
INSERT INTO notifications (
    user_id, notification_type, channel, priority, template_id, subject, body, status, data, sent_at, delivered_at
) VALUES 
(
    'user_customer_001',
    'transaction_success',
    'email',
    'high',
    (SELECT id FROM notification_templates WHERE notification_type = 'transaction_success' AND channel = 'email' AND language = 'ar' LIMIT 1),
    'تم تنفيذ عملية التحويل بنجاح',
    'مرحباً أحمد محمد، تم تنفيذ عملية التحويل الخاصة بك بنجاح...',
    'delivered',
    '{"transaction_id": "txn_001", "amount": 1000.00, "currency": "SAR", "transaction_type": "transfer"}',
    CURRENT_TIMESTAMP - INTERVAL '2 hours',
    CURRENT_TIMESTAMP - INTERVAL '2 hours' + INTERVAL '30 seconds'
),
(
    'user_customer_001',
    'transaction_success',
    'sms',
    'high',
    (SELECT id FROM notification_templates WHERE notification_type = 'transaction_success' AND channel = 'sms' AND language = 'ar' LIMIT 1),
    '',
    'تم تنفيذ التحويل txn_001 بمبلغ 1000.00 SAR بنجاح. WS Transfir',
    'delivered',
    '{"transaction_id": "txn_001", "amount": 1000.00, "currency": "SAR"}',
    CURRENT_TIMESTAMP - INTERVAL '2 hours',
    CURRENT_TIMESTAMP - INTERVAL '2 hours' + INTERVAL '10 seconds'
),
(
    'user_customer_002',
    'security_alert',
    'email',
    'critical',
    (SELECT id FROM notification_templates WHERE notification_type = 'security_alert' AND channel = 'email' AND language = 'ar' LIMIT 1),
    'تنبيه أمني مهم',
    'مرحباً فاطمة علي، تم اكتشاف نشاط مشبوه في حسابك...',
    'delivered',
    '{"alert_type": "محاولة دخول مشبوهة", "ip_address": "*************"}',
    CURRENT_TIMESTAMP - INTERVAL '1 hour',
    CURRENT_TIMESTAMP - INTERVAL '1 hour' + INTERVAL '45 seconds'
),
(
    'user_agent_001',
    'agent_commission',
    'email',
    'medium',
    (SELECT id FROM notification_templates WHERE notification_type = 'agent_commission' AND channel = 'email' AND language = 'ar' LIMIT 1),
    'تم إضافة عمولة جديدة',
    'مرحباً خالد أحمد، تم إضافة عمولة جديدة إلى حسابك...',
    'sent',
    '{"commission_amount": 50.00, "currency": "SAR", "commission_type": "transaction", "transaction_id": "txn_001", "monthly_total": 500.00}',
    CURRENT_TIMESTAMP - INTERVAL '30 minutes',
    NULL
),
(
    'user_customer_003',
    'account_verification',
    'email',
    'high',
    (SELECT id FROM notification_templates WHERE notification_type = 'account_verification' AND channel = 'email' AND language = 'ar' LIMIT 1),
    'تحقق من حسابك في WS Transfir',
    'مرحباً سارة محمد، مرحباً بك في WS Transfir!...',
    'pending',
    '{"verification_link": "https://app.wstransfir.com/verify?token=abc123"}',
    NULL,
    NULL
);

-- Insert sample notification events
INSERT INTO notification_events (
    notification_id, event_type, ip_address, user_agent, opened_at, clicked_at
) VALUES 
(
    (SELECT id FROM notifications WHERE user_id = 'user_customer_001' AND notification_type = 'transaction_success' AND channel = 'email' LIMIT 1),
    'opened',
    '*************',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
    CURRENT_TIMESTAMP - INTERVAL '1 hour 30 minutes',
    NULL
),
(
    (SELECT id FROM notifications WHERE user_id = 'user_customer_002' AND notification_type = 'security_alert' LIMIT 1),
    'opened',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    CURRENT_TIMESTAMP - INTERVAL '45 minutes',
    NULL
),
(
    (SELECT id FROM notifications WHERE user_id = 'user_customer_002' AND notification_type = 'security_alert' LIMIT 1),
    'clicked',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    CURRENT_TIMESTAMP - INTERVAL '45 minutes',
    CURRENT_TIMESTAMP - INTERVAL '44 minutes'
);

-- Insert sample scheduled notifications
INSERT INTO scheduled_notifications (
    name, description, schedule_type, status, notification_type, channel, priority,
    notification_data, recipient_data, scheduled_at, max_executions, created_by
) VALUES 
(
    'تذكير رصيد المحفظة الأسبوعي',
    'إرسال تذكير أسبوعي برصيد المحفظة لجميع المستخدمين النشطين',
    'recurring',
    'active',
    'wallet_balance',
    'sms',
    'low',
    '{"balance": "{user_balance}", "currency": "SAR", "last_transaction_amount": "{last_amount}"}',
    '{"user_id": "all_active", "language": "ar", "timezone": "Asia/Riyadh"}',
    CURRENT_TIMESTAMP + INTERVAL '1 week',
    NULL,
    'user_admin_001'
),
(
    'إشعار صيانة النظام',
    'إشعار مجدول لصيانة النظام القادمة',
    'one_time',
    'active',
    'system_maintenance',
    'email',
    'high',
    '{"title": "صيانة مجدولة للنظام", "message": "سيكون النظام متوقفاً للصيانة من الساعة 2:00 إلى 4:00 صباحاً يوم الأحد القادم."}',
    '{"user_id": "all_users", "language": "ar", "timezone": "Asia/Riyadh"}',
    CURRENT_TIMESTAMP + INTERVAL '3 days',
    1,
    'user_admin_001'
),
(
    'تذكير KYC للمستخدمين الجدد',
    'تذكير المستخدمين الجدد بإكمال عملية التحقق من الهوية',
    'conditional',
    'active',
    'kyc_update',
    'email',
    'medium',
    '{"kyc_status": "pending", "kyc_level": 0, "additional_message": "يرجى إكمال عملية التحقق من الهوية لتفعيل جميع ميزات الحساب."}',
    '{"user_id": "new_users", "language": "ar", "timezone": "Asia/Riyadh"}',
    NULL,
    5,
    'user_admin_001'
);

-- Insert sample in-app notifications
INSERT INTO in_app_notifications (
    user_id, title, message, notification_type, priority, action_url, action_label, data
) VALUES 
(
    'user_customer_001',
    'مرحباً بك في WS Transfir',
    'نرحب بك في منصة WS Transfir للتحويلات المالية. ابدأ رحلتك معنا الآن!',
    'promotional',
    'medium',
    '/dashboard/getting-started',
    'ابدأ الآن',
    '{"welcome_bonus": true, "first_transaction_free": true}'
),
(
    'user_customer_001',
    'تم تنفيذ التحويل بنجاح',
    'تم تنفيذ عملية التحويل txn_001 بمبلغ 1000.00 ريال سعودي بنجاح.',
    'transaction_success',
    'high',
    '/transactions/txn_001',
    'عرض التفاصيل',
    '{"transaction_id": "txn_001", "amount": 1000.00, "currency": "SAR"}'
),
(
    'user_agent_001',
    'عمولة جديدة',
    'تم إضافة عمولة بقيمة 50.00 ريال سعودي إلى حسابك.',
    'agent_commission',
    'medium',
    '/agent/commissions',
    'عرض العمولات',
    '{"commission_amount": 50.00, "currency": "SAR", "commission_type": "transaction"}'
),
(
    'user_customer_002',
    'تنبيه أمني',
    'تم اكتشاف محاولة دخول مشبوهة إلى حسابك. يرجى مراجعة إعدادات الأمان.',
    'security_alert',
    'critical',
    '/security/alerts',
    'مراجعة الأمان',
    '{"alert_type": "suspicious_login", "ip_address": "*************"}'
),
(
    'user_customer_003',
    'أكمل التحقق من الهوية',
    'لتفعيل جميع ميزات حسابك، يرجى إكمال عملية التحقق من الهوية.',
    'kyc_update',
    'high',
    '/kyc/complete',
    'أكمل الآن',
    '{"kyc_status": "pending", "kyc_level": 0, "required_documents": ["national_id", "bank_statement"]}'
);

-- Mark some in-app notifications as read
UPDATE in_app_notifications 
SET is_read = true, read_at = CURRENT_TIMESTAMP - INTERVAL '1 hour'
WHERE user_id = 'user_customer_001' AND notification_type = 'transaction_success';

-- Insert sample notification preferences
INSERT INTO notification_preferences (
    user_id, email_enabled, sms_enabled, push_enabled, in_app_enabled,
    transaction_notifications, security_notifications, marketing_notifications, system_notifications,
    quiet_hours_start, quiet_hours_end, preferred_language
) VALUES 
(
    'user_customer_001',
    true, true, true, true,
    true, true, false, true,
    '22:00', '08:00', 'ar'
),
(
    'user_customer_002',
    true, true, false, true,
    true, true, false, true,
    '23:00', '07:00', 'ar'
),
(
    'user_agent_001',
    true, false, true, true,
    true, true, true, true,
    '21:00', '09:00', 'ar'
),
(
    'user_customer_003',
    true, true, true, true,
    true, true, true, true,
    NULL, NULL, 'ar'
) ON CONFLICT (user_id) DO NOTHING;

-- Insert sample notification costs
INSERT INTO notification_costs (
    channel, date, total_notifications, total_cost, cost_per_notification, provider_name, currency
) VALUES 
('email', CURRENT_DATE - INTERVAL '1 day', 150, 7.50, 0.0500, 'SendGrid', 'SAR'),
('sms', CURRENT_DATE - INTERVAL '1 day', 75, 22.50, 0.3000, 'Twilio', 'SAR'),
('push', CURRENT_DATE - INTERVAL '1 day', 200, 0.00, 0.0000, 'FCM', 'SAR'),
('in_app', CURRENT_DATE - INTERVAL '1 day', 300, 0.00, 0.0000, 'Internal', 'SAR'),

('email', CURRENT_DATE - INTERVAL '2 days', 120, 6.00, 0.0500, 'SendGrid', 'SAR'),
('sms', CURRENT_DATE - INTERVAL '2 days', 60, 18.00, 0.3000, 'Twilio', 'SAR'),
('push', CURRENT_DATE - INTERVAL '2 days', 180, 0.00, 0.0000, 'FCM', 'SAR'),
('in_app', CURRENT_DATE - INTERVAL '2 days', 250, 0.00, 0.0000, 'Internal', 'SAR'),

('email', CURRENT_DATE - INTERVAL '3 days', 200, 10.00, 0.0500, 'SendGrid', 'SAR'),
('sms', CURRENT_DATE - INTERVAL '3 days', 90, 27.00, 0.3000, 'Twilio', 'SAR'),
('push', CURRENT_DATE - INTERVAL '3 days', 220, 0.00, 0.0000, 'FCM', 'SAR'),
('in_app', CURRENT_DATE - INTERVAL '3 days', 350, 0.00, 0.0000, 'Internal', 'SAR');

-- Insert sample delivery logs
INSERT INTO notification_delivery_logs (
    notification_id, attempt_number, status, provider_name, provider_message_id, 
    attempted_at, delivered_at, provider_response
) VALUES 
(
    (SELECT id FROM notifications WHERE user_id = 'user_customer_001' AND notification_type = 'transaction_success' AND channel = 'email' LIMIT 1),
    1, 'delivered', 'SendGrid', 'sg_msg_001',
    CURRENT_TIMESTAMP - INTERVAL '2 hours',
    CURRENT_TIMESTAMP - INTERVAL '2 hours' + INTERVAL '30 seconds',
    '{"message_id": "sg_msg_001", "status": "delivered", "timestamp": "2024-01-16T10:30:30Z"}'
),
(
    (SELECT id FROM notifications WHERE user_id = 'user_customer_001' AND notification_type = 'transaction_success' AND channel = 'sms' LIMIT 1),
    1, 'delivered', 'Twilio', 'tw_msg_001',
    CURRENT_TIMESTAMP - INTERVAL '2 hours',
    CURRENT_TIMESTAMP - INTERVAL '2 hours' + INTERVAL '10 seconds',
    '{"message_sid": "tw_msg_001", "status": "delivered", "price": "0.30"}'
),
(
    (SELECT id FROM notifications WHERE user_id = 'user_customer_002' AND notification_type = 'security_alert' LIMIT 1),
    1, 'delivered', 'SendGrid', 'sg_msg_002',
    CURRENT_TIMESTAMP - INTERVAL '1 hour',
    CURRENT_TIMESTAMP - INTERVAL '1 hour' + INTERVAL '45 seconds',
    '{"message_id": "sg_msg_002", "status": "delivered", "timestamp": "2024-01-16T11:30:45Z"}'
),
(
    (SELECT id FROM notifications WHERE user_id = 'user_agent_001' AND notification_type = 'agent_commission' LIMIT 1),
    1, 'sent', 'SendGrid', 'sg_msg_003',
    CURRENT_TIMESTAMP - INTERVAL '30 minutes',
    NULL,
    '{"message_id": "sg_msg_003", "status": "sent", "timestamp": "2024-01-16T12:00:00Z"}'
);

-- Log the seeding
INSERT INTO schema_migrations (version, name, file_path, checksum, execution_time_ms, success)
VALUES (
    'seed_004',
    'Notification System Data Seed',
    'database/seeds/004_notification_system_data.sql',
    'notification_system_seed_checksum',
    0,
    true
) ON CONFLICT (version) DO NOTHING;
