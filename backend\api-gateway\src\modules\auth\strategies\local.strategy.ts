import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import * as bcrypt from 'bcrypt';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      usernameField: 'email',
      passwordField: 'password',
    });
  }

  async validate(email: string, password: string): Promise<any> {
    // البحث عن المستخدم
    const user = await this.findUserByEmail(email);
    
    if (!user) {
      throw new UnauthorizedException('بيانات الدخول غير صحيحة');
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      throw new UnauthorizedException('بيانات الدخول غير صحيحة');
    }

    // التحقق من حالة الحساب
    if (!user.isActive) {
      throw new UnauthorizedException('الحساب معطل');
    }

    // تسجيل محاولة الدخول
    await this.logLoginAttempt(user.id, true);

    // إرجاع بيانات المستخدم (بدون كلمة المرور)
    const { password: _, ...result } = user;
    return result;
  }

  // Helper methods (سيتم تنفيذها لاحقاً)
  private async findUserByEmail(email: string) {
    // TODO: البحث في قاعدة البيانات
    return {
      id: 'user-id',
      email,
      password: '$2b$12$hashedPassword', // كلمة مرور مشفرة
      firstName: 'أحمد',
      lastName: 'محمد',
      isActive: true,
      isVerified: true,
      is2FAEnabled: false,
      role: 'user',
    };
  }

  private async logLoginAttempt(userId: string, success: boolean) {
    // TODO: تسجيل محاولة الدخول
    console.log(`Login attempt for user ${userId}: ${success ? 'success' : 'failed'}`);
  }
}
