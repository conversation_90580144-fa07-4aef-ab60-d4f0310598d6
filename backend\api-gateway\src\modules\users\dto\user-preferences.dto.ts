import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsBoolean, IsString, IsEnum, IsArray } from 'class-validator';

export enum Language {
  AR = 'ar',
  EN = 'en',
  FR = 'fr',
  ES = 'es',
}

export enum Currency {
  SAR = 'SAR',
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
}

export enum NotificationType {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
}

export class NotificationPreferencesDto {
  @ApiProperty({
    description: 'إشعارات التحويلات',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'إشعارات التحويلات يجب أن تكون true أو false' })
  transfers?: boolean;

  @ApiProperty({
    description: 'إشعارات الأمان',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'إشعارات الأمان يجب أن تكون true أو false' })
  security?: boolean;

  @ApiProperty({
    description: 'إشعارات التسويق',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'إشعارات التسويق يجب أن تكون true أو false' })
  marketing?: boolean;

  @ApiProperty({
    description: 'إشعارات العروض الخاصة',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'إشعارات العروض الخاصة يجب أن تكون true أو false' })
  promotions?: boolean;

  @ApiProperty({
    description: 'إشعارات تحديثات النظام',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'إشعارات تحديثات النظام يجب أن تكون true أو false' })
  systemUpdates?: boolean;

  @ApiProperty({
    description: 'طرق الإشعار المفضلة',
    enum: NotificationType,
    isArray: true,
    example: [NotificationType.EMAIL, NotificationType.SMS],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'طرق الإشعار يجب أن تكون مصفوفة' })
  @IsEnum(NotificationType, { each: true, message: 'طريقة الإشعار غير صحيحة' })
  preferredMethods?: NotificationType[];
}

export class SecurityPreferencesDto {
  @ApiProperty({
    description: 'تفعيل المصادقة الثنائية',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تفعيل المصادقة الثنائية يجب أن يكون true أو false' })
  twoFactorEnabled?: boolean;

  @ApiProperty({
    description: 'تسجيل الخروج التلقائي (بالدقائق)',
    example: 30,
    required: false,
  })
  @IsOptional()
  autoLogoutMinutes?: number;

  @ApiProperty({
    description: 'إشعار عند تسجيل الدخول من جهاز جديد',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'إشعار الجهاز الجديد يجب أن يكون true أو false' })
  newDeviceAlert?: boolean;

  @ApiProperty({
    description: 'طلب كلمة المرور للعمليات الحساسة',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'طلب كلمة المرور يجب أن يكون true أو false' })
  requirePasswordForSensitive?: boolean;
}

export class DisplayPreferencesDto {
  @ApiProperty({
    description: 'اللغة المفضلة',
    enum: Language,
    example: Language.AR,
    required: false,
  })
  @IsOptional()
  @IsEnum(Language, { message: 'اللغة غير صحيحة' })
  language?: Language;

  @ApiProperty({
    description: 'العملة المفضلة للعرض',
    enum: Currency,
    example: Currency.SAR,
    required: false,
  })
  @IsOptional()
  @IsEnum(Currency, { message: 'العملة غير صحيحة' })
  preferredCurrency?: Currency;

  @ApiProperty({
    description: 'المنطقة الزمنية',
    example: 'Asia/Riyadh',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'المنطقة الزمنية يجب أن تكون نص' })
  timezone?: string;

  @ApiProperty({
    description: 'تنسيق التاريخ',
    example: 'DD/MM/YYYY',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'تنسيق التاريخ يجب أن يكون نص' })
  dateFormat?: string;

  @ApiProperty({
    description: 'الوضع المظلم',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'الوضع المظلم يجب أن يكون true أو false' })
  darkMode?: boolean;
}

export class TransferPreferencesDto {
  @ApiProperty({
    description: 'العملة المفضلة للإرسال',
    enum: Currency,
    example: Currency.SAR,
    required: false,
  })
  @IsOptional()
  @IsEnum(Currency, { message: 'عملة الإرسال غير صحيحة' })
  defaultSendCurrency?: Currency;

  @ApiProperty({
    description: 'طريقة التحويل المفضلة',
    example: 'cash_pickup',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'طريقة التحويل يجب أن تكون نص' })
  defaultTransferMethod?: string;

  @ApiProperty({
    description: 'حفظ المستفيدين تلقائياً',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'حفظ المستفيدين يجب أن يكون true أو false' })
  autoSaveBeneficiaries?: boolean;

  @ApiProperty({
    description: 'طلب تأكيد قبل الإرسال',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'طلب التأكيد يجب أن يكون true أو false' })
  requireConfirmation?: boolean;
}

export class UserPreferencesDto {
  @ApiProperty({
    description: 'إعدادات الإشعارات',
    type: NotificationPreferencesDto,
    required: false,
  })
  @IsOptional()
  notifications?: NotificationPreferencesDto;

  @ApiProperty({
    description: 'إعدادات الأمان',
    type: SecurityPreferencesDto,
    required: false,
  })
  @IsOptional()
  security?: SecurityPreferencesDto;

  @ApiProperty({
    description: 'إعدادات العرض',
    type: DisplayPreferencesDto,
    required: false,
  })
  @IsOptional()
  display?: DisplayPreferencesDto;

  @ApiProperty({
    description: 'إعدادات التحويل',
    type: TransferPreferencesDto,
    required: false,
  })
  @IsOptional()
  transfers?: TransferPreferencesDto;
}
