{"version": 3, "file": "DeclarationReference.d.ts", "sourceRoot": "", "sources": ["../../src/beta/DeclarationReference.ts"], "names": [], "mappings": "AAUA;;;GAGG;AACH,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,OAAO,CAA0C;IACzD,OAAO,CAAC,WAAW,CAAqD;IACxE,OAAO,CAAC,OAAO,CAA8B;gBAG3C,MAAM,CAAC,EAAE,YAAY,GAAG,YAAY,EACpC,UAAU,CAAC,EAAE,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,OAAO,EACnD,MAAM,CAAC,EAAE,eAAe;IAO1B,IAAW,MAAM,IAAI,YAAY,GAAG,YAAY,GAAG,SAAS,CAE3D;IAED,IAAW,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,OAAO,GAAG,SAAS,CAW1E;IAED,IAAW,MAAM,IAAI,eAAe,GAAG,SAAS,CAE/C;IAED,IAAW,OAAO,IAAI,OAAO,CAE5B;WAEa,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,oBAAoB;WAYzC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS;IAQrD;;OAEG;WACW,2BAA2B,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAShE;;;OAGG;WACW,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAWzD;;OAEG;WACW,uBAAuB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAc3D;;;OAGG;WACW,8BAA8B,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAUnE;;OAEG;WACW,wBAAwB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAW5D;;OAEG;WACW,0BAA0B,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;WAchD,KAAK,IAAI,oBAAoB;WAI7B,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,oBAAoB;WAIvE,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,oBAAoB;WAIjE,MAAM,IAAI,oBAAoB;WAI9B,IAAI,CAAC,IAAI,EAAE,oBAAoB,GAAG,SAAS,GAAG,oBAAoB;IAIzE,UAAU,CAAC,MAAM,EAAE,YAAY,GAAG,YAAY,GAAG,SAAS,GAAG,oBAAoB;IAIjF,cAAc,CACnB,UAAU,EAAE,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,OAAO,GAAG,SAAS,GAC7D,oBAAoB;IAMhB,UAAU,CAAC,MAAM,EAAE,eAAe,GAAG,SAAS,GAAG,oBAAoB;IAIrE,iBAAiB,CAAC,aAAa,EAAE,aAAa,GAAG,oBAAoB;IAMrE,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,SAAS,GAAG,oBAAoB;IAU/D,iBAAiB,CAAC,aAAa,EAAE,MAAM,GAAG,SAAS,GAAG,oBAAoB;IAU1E,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,GAAG,oBAAoB;IAWzF,QAAQ,IAAI,MAAM;CAO1B;AAED;;;GAGG;AACH,oBAAY,UAAU;IACpB,OAAO,MAAM;IACb,OAAO,MAAM;IACb,MAAM,MAAM;CACb;AAED;;;GAGG;AACH,qBAAa,YAAY;IACvB,SAAgB,WAAW,EAAE,MAAM,CAAC;IACpC,OAAO,CAAC,KAAK,CAAqB;IAElC,OAAO,CAAC,eAAe,CAA6B;gBAEjC,IAAI,EAAE,MAAM,EAAE,WAAW,GAAE,OAAc;IAK5D,IAAW,IAAI,IAAI,MAAM,CAExB;IAED,IAAW,WAAW,IAAI,MAAM,CAE/B;IAED,IAAW,SAAS,IAAI,MAAM,CAG7B;IAED,IAAW,mBAAmB,IAAI,MAAM,CAEvC;IAED,IAAW,UAAU,IAAI,MAAM,CAE9B;WAEa,iBAAiB,CAC7B,SAAS,EAAE,MAAM,GAAG,SAAS,EAC7B,mBAAmB,EAAE,MAAM,EAC3B,UAAU,CAAC,EAAE,MAAM,GAClB,YAAY;WAaD,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,YAAY;IAIjF,OAAO,CAAC,MAAM,CAAC,gBAAgB;IA4BxB,QAAQ,IAAI,MAAM;IAIzB,OAAO,CAAC,yBAAyB;CAiBlC;AA2CD;;;GAGG;AACH,qBAAa,YAAY;IACvB,gBAAuB,QAAQ,EAAE,YAAY,CAAsB;IAEnE,OAAO;IAEA,QAAQ,IAAI,MAAM;CAG1B;AAED;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG,eAAe,GAAG,kBAAkB,CAAC;AAE7D;;GAEG;AAEH,yBAAiB,SAAS,CAAC;IACzB,SAAgB,IAAI,CAAC,KAAK,EAAE,aAAa,GAAG,SAAS,CAQpD;CACF;AAED;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,SAAS,GAAG,oBAAoB,GAAG,MAAM,CAAC;AAEtE;;GAEG;AACH,qBAAa,eAAe;IAC1B,SAAgB,IAAI,EAAE,MAAM,CAAC;gBAEV,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO;IAI/C,QAAQ,IAAI,MAAM;CAG1B;AAID;;GAEG;AACH,qBAAa,kBAAkB;IAC7B,SAAgB,SAAS,EAAE,oBAAoB,CAAC;gBAE7B,SAAS,EAAE,oBAAoB;WAIpC,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,kBAAkB;IAO9C,aAAa,CAAC,SAAS,EAAE,oBAAoB,GAAG,kBAAkB;IAIlE,QAAQ,IAAI,MAAM;CAG1B;AAED;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,aAAa,GAAG,mBAAmB,CAAC;AAEhE;;GAEG;AACH,8BAAsB,iBAAiB;IACrC,SAAgB,SAAS,EAAE,SAAS,CAAC;gBAElB,SAAS,EAAE,SAAS;IAIhC,iBAAiB,CACtB,IAAI,EAAE,aAAa,EACnB,UAAU,EAAE,UAAU,EACtB,SAAS,EAAE,aAAa,GACvB,aAAa;aAKA,QAAQ,IAAI,MAAM;CACnC;AAED;;GAEG;AACH,qBAAa,aAAc,SAAQ,iBAAiB;IAC3C,aAAa,CAAC,SAAS,EAAE,aAAa,GAAG,aAAa;IAItD,QAAQ,IAAI,MAAM;CAG1B;AAED;;GAEG;AACH,qBAAa,mBAAoB,SAAQ,iBAAiB;IACxD,SAAgB,MAAM,EAAE,aAAa,CAAC;IACtC,SAAgB,UAAU,EAAE,UAAU,CAAC;gBAEpB,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS;IAM/E,UAAU,CAAC,MAAM,EAAE,aAAa,GAAG,mBAAmB;IAItD,cAAc,CAAC,UAAU,EAAE,UAAU,GAAG,mBAAmB;IAM3D,aAAa,CAAC,SAAS,EAAE,aAAa,GAAG,mBAAmB;IAM5D,QAAQ,IAAI,MAAM;CAG1B;AAED;;GAEG;AACH,oBAAY,OAAO;IACjB,KAAK,UAAU,CAAE,oBAAoB;IACrC,SAAS,cAAc,CAAE,wBAAwB;IACjD,SAAS,SAAS,CAAE,wBAAwB;IAC5C,IAAI,SAAS,CAAE,mBAAmB;IAClC,SAAS,cAAc,CAAE,qBAAqB;IAC9C,QAAQ,aAAa,CAAE,uBAAuB;IAC9C,QAAQ,QAAQ,CAAE,uBAAuB;IACzC,WAAW,gBAAgB,CAAE,0BAA0B;IACvD,MAAM,WAAW,CAAE,mDAAmD;IACtE,KAAK,UAAU,CAAE,EAAE;IACnB,aAAa,SAAS,CAAE,qCAAqC;IAC7D,kBAAkB,QAAQ,CAAE,oCAAoC;IAChE,cAAc,UAAU,CAAE,sCAAsC;IAChE,WAAW,YAAY;CACxB;AAED;;GAEG;AACH,MAAM,WAAW,uBAAuB;IACtC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED;;;GAGG;AACH,qBAAa,eAAe;IAC1B,SAAgB,aAAa,EAAE,aAAa,GAAG,SAAS,CAAC;IACzD,SAAgB,OAAO,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7C,SAAgB,aAAa,EAAE,MAAM,GAAG,SAAS,CAAC;gBAGhD,SAAS,EAAE,aAAa,GAAG,SAAS,EACpC,EAAE,OAAO,EAAE,aAAa,EAAE,GAAE,uBAA4B;WAO5C,KAAK,IAAI,eAAe;IAI/B,iBAAiB,CAAC,aAAa,EAAE,aAAa,GAAG,SAAS,GAAG,eAAe;IAS5E,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,SAAS,GAAG,eAAe;IAS1D,iBAAiB,CAAC,aAAa,EAAE,MAAM,GAAG,SAAS,GAAG,eAAe;IASrE,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,GAAG,eAAe;IAOpF,QAAQ,IAAI,MAAM;CAW1B"}