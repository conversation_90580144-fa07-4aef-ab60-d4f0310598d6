{"extends": "./tsconfig.json", "compilerOptions": {"module": "commonjs", "declaration": true, "noImplicitAny": false, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es6", "sourceMap": false, "outDir": "./dist", "rootDir": "./lib", "skipLibCheck": true}, "include": ["lib/**/*"], "exclude": ["node_modules", "test", "dist", "**/*spec.ts", "e2e"]}