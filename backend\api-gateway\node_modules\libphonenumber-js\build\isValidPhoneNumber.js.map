{"version": 3, "file": "isValidPhoneNumber.js", "names": ["isValidPhoneNumber", "normalizeArguments", "arguments", "text", "options", "metadata", "extract", "phoneNumber", "parsePhoneNumber", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../source/isValidPhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumber from './parsePhoneNumber_.js'\r\n\r\nexport default function isValidPhoneNumber() {\r\n\tlet { text, options, metadata } = normalizeArguments(arguments)\r\n\toptions = {\r\n\t\t...options,\r\n\t\textract: false\r\n\t}\r\n\tconst phoneNumber = parsePhoneNumber(text, options, metadata)\r\n\treturn phoneNumber && phoneNumber.isValid() || false\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;;;;;;;AAEe,SAASA,kBAAT,GAA8B;EAC5C,0BAAkC,IAAAC,+BAAA,EAAmBC,SAAnB,CAAlC;EAAA,IAAMC,IAAN,uBAAMA,IAAN;EAAA,IAAYC,OAAZ,uBAAYA,OAAZ;EAAA,IAAqBC,QAArB,uBAAqBA,QAArB;;EACAD,OAAO,mCACHA,OADG;IAENE,OAAO,EAAE;EAFH,EAAP;EAIA,IAAMC,WAAW,GAAG,IAAAC,6BAAA,EAAiBL,IAAjB,EAAuBC,OAAvB,EAAgCC,QAAhC,CAApB;EACA,OAAOE,WAAW,IAAIA,WAAW,CAACE,OAAZ,EAAf,IAAwC,KAA/C;AACA"}