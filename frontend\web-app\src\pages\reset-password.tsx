import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Link,
  Container,
  Paper,
  LinearProgress,
} from '@mui/material';
import {
  Lock,
  Visibility,
  VisibilityOff,
  CheckCircle,
  Error,
  Security,
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Head from 'next/head';

const validationSchema = Yup.object({
  newPassword: Yup.string()
    .min(8, 'كلمة المرور يجب أن تكون على الأقل 8 أحرف')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/, 
      'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص')
    .required('كلمة المرور الجديدة مطلوبة'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('newPassword')], 'كلمات المرور غير متطابقة')
    .required('تأكيد كلمة المرور مطلوب'),
});

const ResetPasswordPage: React.FC = () => {
  const router = useRouter();
  const { token } = router.query;
  
  const [loading, setLoading] = useState(false);
  const [validatingToken, setValidatingToken] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    if (token) {
      validateToken(token as string);
    }
  }, [token]);

  const validateToken = async (resetToken: string) => {
    try {
      setValidatingToken(true);
      const response = await fetch(`/api/auth/validate-reset-token?token=${resetToken}`);
      
      if (response.ok) {
        setTokenValid(true);
      } else {
        const data = await response.json();
        setError(data.message || 'رمز إعادة التعيين غير صالح أو منتهي الصلاحية');
        setTokenValid(false);
      }
    } catch (err) {
      setError('حدث خطأ في التحقق من الرمز');
      setTokenValid(false);
    } finally {
      setValidatingToken(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      newPassword: '',
      confirmPassword: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/auth/reset-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token: token as string,
            newPassword: values.newPassword,
            confirmPassword: values.confirmPassword,
          }),
        });

        const data = await response.json();

        if (response.ok) {
          setSuccess(true);
        } else {
          setError(data.message || 'فشل في إعادة تعيين كلمة المرور');
        }
      } catch (err) {
        setError('حدث خطأ في إعادة تعيين كلمة المرور');
      } finally {
        setLoading(false);
      }
    },
  });

  const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[@$!%*?&]/.test(password)) score++;

    const levels = [
      { score: 0, label: 'ضعيف جداً', color: 'error' },
      { score: 1, label: 'ضعيف', color: 'error' },
      { score: 2, label: 'متوسط', color: 'warning' },
      { score: 3, label: 'جيد', color: 'info' },
      { score: 4, label: 'قوي', color: 'success' },
      { score: 5, label: 'قوي جداً', color: 'success' },
    ];

    return levels[score] || levels[0];
  };

  const passwordStrength = getPasswordStrength(formik.values.newPassword);

  if (validatingToken) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 2,
        }}
      >
        <Container maxWidth="sm">
          <Paper elevation={24} sx={{ borderRadius: 4, p: 4, textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom>
              جاري التحقق من الرمز...
            </Typography>
            <LinearProgress sx={{ mt: 2 }} />
          </Paper>
        </Container>
      </Box>
    );
  }

  if (!tokenValid) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 2,
        }}
      >
        <Container maxWidth="sm">
          <Paper elevation={24} sx={{ borderRadius: 4, overflow: 'hidden' }}>
            <Box
              sx={{
                background: 'linear-gradient(45deg, #f44336 30%, #e57373 90%)',
                color: 'white',
                padding: 3,
                textAlign: 'center',
              }}
            >
              <Error sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h4" component="h1" fontWeight="bold">
                رمز غير صالح
              </Typography>
            </Box>
            <CardContent sx={{ padding: 4, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                رمز إعادة تعيين كلمة المرور غير صالح
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                {error || 'الرمز منتهي الصلاحية أو تم استخدامه مسبقاً'}
              </Typography>
              <Button
                variant="contained"
                onClick={() => router.push('/forgot-password')}
                sx={{
                  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                }}
              >
                طلب رمز جديد
              </Button>
            </CardContent>
          </Paper>
        </Container>
      </Box>
    );
  }

  if (success) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 2,
        }}
      >
        <Container maxWidth="sm">
          <Paper elevation={24} sx={{ borderRadius: 4, overflow: 'hidden' }}>
            <Box
              sx={{
                background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                color: 'white',
                padding: 3,
                textAlign: 'center',
              }}
            >
              <CheckCircle sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h4" component="h1" fontWeight="bold">
                تم بنجاح!
              </Typography>
            </Box>
            <CardContent sx={{ padding: 4, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                تم تغيير كلمة المرور بنجاح
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة
              </Typography>
              <Button
                variant="contained"
                onClick={() => router.push('/login')}
                sx={{
                  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                }}
              >
                تسجيل الدخول
              </Button>
            </CardContent>
          </Paper>
        </Container>
      </Box>
    );
  }

  return (
    <>
      <Head>
        <title>إعادة تعيين كلمة المرور - WS Transfir</title>
        <meta name="description" content="إعادة تعيين كلمة المرور في نظام WS Transfir" />
      </Head>

      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 2,
        }}
      >
        <Container maxWidth="sm">
          <Paper
            elevation={24}
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
            }}
          >
            {/* Header */}
            <Box
              sx={{
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                color: 'white',
                padding: 3,
                textAlign: 'center',
              }}
            >
              <Security sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                WS Transfir
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                إعادة تعيين كلمة المرور
              </Typography>
            </Box>

            <CardContent sx={{ padding: 4 }}>
              <Typography variant="h5" component="h2" textAlign="center" gutterBottom>
                كلمة مرور جديدة
              </Typography>
              
              <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 3 }}>
                أدخل كلمة المرور الجديدة لحسابك
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <form onSubmit={formik.handleSubmit}>
                <TextField
                  fullWidth
                  id="newPassword"
                  name="newPassword"
                  label="كلمة المرور الجديدة"
                  type={showPassword ? 'text' : 'password'}
                  value={formik.values.newPassword}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.newPassword && Boolean(formik.errors.newPassword)}
                  helperText={formik.touched.newPassword && formik.errors.newPassword}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 2 }}
                  dir="ltr"
                />

                {/* Password Strength Indicator */}
                {formik.values.newPassword && (
                  <Box sx={{ mb: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="caption">قوة كلمة المرور:</Typography>
                      <Typography variant="caption" color={`${passwordStrength.color}.main`}>
                        {passwordStrength.label}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={(passwordStrength.score / 5) * 100}
                      color={passwordStrength.color as any}
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                  </Box>
                )}

                <TextField
                  fullWidth
                  id="confirmPassword"
                  name="confirmPassword"
                  label="تأكيد كلمة المرور"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formik.values.confirmPassword}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
                  helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          edge="end"
                        >
                          {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 3 }}
                  dir="ltr"
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  startIcon={<Security />}
                  sx={{
                    mb: 3,
                    py: 1.5,
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
                    },
                  }}
                >
                  {loading ? 'جاري التحديث...' : 'تحديث كلمة المرور'}
                </Button>

                <Box textAlign="center">
                  <Link
                    href="/login"
                    variant="body2"
                    sx={{ textDecoration: 'none', fontWeight: 'bold' }}
                  >
                    العودة لتسجيل الدخول
                  </Link>
                </Box>
              </form>

              {/* Security Tips */}
              <Box sx={{ mt: 4, p: 2, backgroundColor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  نصائح لكلمة مرور قوية:
                </Typography>
                <ul style={{ margin: 0, paddingRight: '20px' }}>
                  <li>
                    <Typography variant="body2" color="text.secondary">
                      استخدم على الأقل 8 أحرف
                    </Typography>
                  </li>
                  <li>
                    <Typography variant="body2" color="text.secondary">
                      امزج بين الأحرف الكبيرة والصغيرة
                    </Typography>
                  </li>
                  <li>
                    <Typography variant="body2" color="text.secondary">
                      أضف أرقام ورموز خاصة
                    </Typography>
                  </li>
                  <li>
                    <Typography variant="body2" color="text.secondary">
                      تجنب المعلومات الشخصية
                    </Typography>
                  </li>
                </ul>
              </Box>
            </CardContent>
          </Paper>
        </Container>
      </Box>
    </>
  );
};

export default ResetPasswordPage;
