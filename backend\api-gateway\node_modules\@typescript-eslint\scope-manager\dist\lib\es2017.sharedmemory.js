"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2017_sharedmemory = void 0;
const base_config_1 = require("./base-config");
const es2015_symbol_1 = require("./es2015.symbol");
const es2015_symbol_wellknown_1 = require("./es2015.symbol.wellknown");
exports.es2017_sharedmemory = {
    ...es2015_symbol_1.es2015_symbol,
    ...es2015_symbol_wellknown_1.es2015_symbol_wellknown,
    SharedArrayBuffer: base_config_1.TYPE_VALUE,
    SharedArrayBufferConstructor: base_config_1.TYPE,
    ArrayBufferTypes: base_config_1.TYPE,
    Atomics: base_config_1.TYPE_VALUE,
};
//# sourceMappingURL=es2017.sharedmemory.js.map