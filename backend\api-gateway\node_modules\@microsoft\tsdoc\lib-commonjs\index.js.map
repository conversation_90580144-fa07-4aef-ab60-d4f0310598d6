{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;AAE3D,iEAIwC;AAHtC,gHAAA,cAAc,OAAA;AAIhB,yEAAwE;AAA/D,wHAAA,kBAAkB,OAAA;AAC3B,yEAI4C;AAF1C,wHAAA,kBAAkB,OAAA;AAClB,wHAAA,kBAAkB,OAAA;AAEpB,6FAA4F;AAAnF,4IAAA,4BAA4B,OAAA;AAErC,uDAAsD;AAA7C,4GAAA,YAAY,OAAA;AACrB,6DAA4D;AAAnD,kHAAA,eAAe,OAAA;AACxB,2EAA0E;AAAjE,gIAAA,sBAAsB,OAAA;AAC/B,2DAA0D;AAAjD,gHAAA,cAAc,OAAA;AAEvB,gEAA+D;AAAtD,oHAAA,gBAAgB,OAAA;AACzB,0DAA8E;AAArE,8GAAA,aAAa,OAAA;AACtB,wDAAuD;AAA9C,4GAAA,YAAY,OAAA;AAErB,0CAAwB;AAExB,wDAAuD;AAA9C,8GAAA,aAAa,OAAA;AACtB,wDAAsF;AAA7E,8GAAA,aAAa,OAAA;AACtB,8DAA6D;AAApD,oHAAA,gBAAgB,OAAA;AACzB,gDAAmE;AAA1D,sGAAA,SAAS,OAAA;AAClB,wCAAkD;AAAzC,8FAAA,KAAK,OAAA;AAAE,kGAAA,SAAS,OAAA;AACzB,wDAAsF;AAA7E,8GAAA,aAAa,OAAA;AACtB,0DAAyD;AAAhD,gHAAA,cAAc,OAAA;AACvB,oDAAmD;AAA1C,0GAAA,WAAW,OAAA;AAEpB,oEAAmE;AAA1D,sHAAA,iBAAiB,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nexport {\r\n  DocNodeManager,\r\n  type IDocNodeDefinition,\r\n  type DocNodeConstructor\r\n} from './configuration/DocNodeManager';\r\nexport { TSDocConfiguration } from './configuration/TSDocConfiguration';\r\nexport {\r\n  type ITSDocTagDefinitionParameters,\r\n  TSDocTagSyntaxKind,\r\n  TSDocTagDefinition\r\n} from './configuration/TSDocTagDefinition';\r\nexport { TSDocValidationConfiguration } from './configuration/TSDocValidationConfiguration';\r\n\r\nexport { StandardTags } from './details/StandardTags';\r\nexport { Standardization } from './details/Standardization';\r\nexport { StandardModifierTagSet } from './details/StandardModifierTagSet';\r\nexport { ModifierTagSet } from './details/ModifierTagSet';\r\n\r\nexport { PlainTextEmitter } from './emitters/PlainTextEmitter';\r\nexport { StringBuilder, type IStringBuilder } from './emitters/StringBuilder';\r\nexport { TSDocEmitter } from './emitters/TSDocEmitter';\r\n\r\nexport * from './nodes';\r\n\r\nexport { ParserContext } from './parser/ParserContext';\r\nexport { ParserMessage, type IParserMessageParameters } from './parser/ParserMessage';\r\nexport { ParserMessageLog } from './parser/ParserMessageLog';\r\nexport { TextRange, type ITextLocation } from './parser/TextRange';\r\nexport { Token, TokenKind } from './parser/Token';\r\nexport { TokenSequence, type ITokenSequenceParameters } from './parser/TokenSequence';\r\nexport { TSDocMessageId } from './parser/TSDocMessageId';\r\nexport { TSDocParser } from './parser/TSDocParser';\r\n\r\nexport { DocNodeTransforms } from './transforms/DocNodeTransforms';\r\n"]}