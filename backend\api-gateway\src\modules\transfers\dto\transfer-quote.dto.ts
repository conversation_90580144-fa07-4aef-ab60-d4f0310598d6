import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  <PERSON>N<PERSON><PERSON>,
  IsEnum,
  Min,
  <PERSON>,
} from 'class-validator';
import { TransferType } from './create-transfer.dto';

export class TransferQuoteDto {
  @ApiProperty({
    description: 'المبلغ المرسل',
    example: 1000,
    minimum: 1,
    maximum: 50000,
  })
  @IsNumber({}, { message: 'المبلغ يجب أن يكون رقم' })
  @IsNotEmpty({ message: 'المبلغ مطلوب' })
  @Min(1, { message: 'المبلغ يجب أن يكون أكبر من صفر' })
  @Max(50000, { message: 'المبلغ يجب أن يكون أقل من 50,000' })
  amount: number;

  @ApiProperty({
    description: 'عملة الإرسال',
    example: 'SAR',
  })
  @IsString({ message: 'عملة الإرسال يجب أن تكون نص' })
  @IsNotEmpty({ message: 'عملة الإرسال مطلوبة' })
  fromCurrency: string;

  @ApiProperty({
    description: 'عملة الاستلام',
    example: 'USD',
  })
  @IsString({ message: 'عملة الاستلام يجب أن تكون نص' })
  @IsNotEmpty({ message: 'عملة الاستلام مطلوبة' })
  toCurrency: string;

  @ApiProperty({
    description: 'بلد الاستلام',
    example: 'US',
  })
  @IsString({ message: 'بلد الاستلام يجب أن يكون نص' })
  @IsNotEmpty({ message: 'بلد الاستلام مطلوب' })
  destinationCountry: string;

  @ApiProperty({
    description: 'نوع التحويل',
    enum: TransferType,
    example: TransferType.CASH_PICKUP,
  })
  @IsEnum(TransferType, { message: 'نوع التحويل غير صحيح' })
  @IsNotEmpty({ message: 'نوع التحويل مطلوب' })
  transferType: TransferType;
}
