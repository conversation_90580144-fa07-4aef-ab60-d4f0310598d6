import { CanActivate, ExceptionFilter, INestMicroservice, NestInterceptor, PipeTransform, WebSocketAdapter } from '@nestjs/common';
import { NestMicroserviceOptions } from '@nestjs/common/interfaces/microservices/nest-microservice-options.interface';
import { Logger } from '@nestjs/common/services/logger.service';
import { ApplicationConfig } from '@nestjs/core/application-config';
import { NestContainer } from '@nestjs/core/injector/container';
import { GraphInspector } from '@nestjs/core/inspector/graph-inspector';
import { NestApplicationContext } from '@nestjs/core/nest-application-context';
import { MicroserviceOptions } from './interfaces/microservice-configuration.interface';
export declare class NestMicroservice extends NestApplicationContext<NestMicroserviceOptions> implements INestMicroservice {
    private readonly graphInspector;
    private readonly applicationConfig;
    protected readonly logger: Logger;
    private readonly microservicesModule;
    private readonly socketModule;
    private microserviceConfig;
    private server;
    private isTerminated;
    private isInitHookCalled;
    constructor(container: NestContainer, config: NestMicroserviceOptions & MicroserviceOptions, graphInspector: GraphInspector, applicationConfig: ApplicationConfig);
    createServer(config: NestMicroserviceOptions & MicroserviceOptions): void;
    registerModules(): Promise<any>;
    registerListeners(): void;
    useWebSocketAdapter(adapter: WebSocketAdapter): this;
    useGlobalFilters(...filters: ExceptionFilter[]): this;
    useGlobalPipes(...pipes: PipeTransform<any>[]): this;
    useGlobalInterceptors(...interceptors: NestInterceptor[]): this;
    useGlobalGuards(...guards: CanActivate[]): this;
    init(): Promise<this>;
    listen(): Promise<any>;
    close(): Promise<any>;
    setIsInitialized(isInitialized: boolean): void;
    setIsTerminated(isTerminated: boolean): void;
    setIsInitHookCalled(isInitHookCalled: boolean): void;
    protected closeApplication(): Promise<any>;
    protected dispose(): Promise<void>;
}
