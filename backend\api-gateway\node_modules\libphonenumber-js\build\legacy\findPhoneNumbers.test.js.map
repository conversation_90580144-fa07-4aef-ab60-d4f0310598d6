{"version": 3, "file": "findPhoneNumbers.test.js", "names": ["describe", "it", "findNumbers", "metadata", "should", "deep", "equal", "phone", "country", "startsAt", "endsAt", "leniency", "ext", "expected_numbers", "searchPhoneNumbers", "number", "shift", "length", "thrower", "possibleNumbers", "extended", "finder", "PhoneNumberSearch", "defaultCountry", "hasNext", "next", "undefined", "search"], "sources": ["../../source/legacy/findPhoneNumbers.test.js"], "sourcesContent": ["// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport findNumbers, { searchPhoneNumbers } from './findPhoneNumbers.js'\r\nimport { PhoneNumberSearch } from './findPhoneNumbersInitialImplementation.js'\r\nimport metadata from '../../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('findPhoneNumbers', () => {\r\n\tit('should find numbers', () => {\r\n\t\tfindNumbers('2133734253', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 10\r\n\t\t}])\r\n\r\n\t\tfindNumbers('(*************', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 14\r\n\t\t}])\r\n\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// Opening parenthesis issue.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/252\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 and not (************* (that\\'s not even in the same country!) as written in the document.', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// No default country.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options` and default country.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', 'US', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options`.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Not a phone number and a phone number.\r\n\t\tfindNumbers('Digits 12 are not a number, but +7 (800) 555-35-35 is.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 32,\r\n\t\t\tendsAt   : 50\r\n\t\t}])\r\n\r\n\t\t// Phone number extension.\r\n\t\tfindNumbers('Date 02/17/2018 is not a number, but +7 (800) 555-35-35 ext. 123 is.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\text      : '123',\r\n\t\t\tstartsAt : 37,\r\n\t\t\tendsAt   : 64\r\n\t\t}])\r\n\t})\r\n\r\n\tit('shouldn\\'t find non-valid numbers', () => {\r\n\t\t// Not a valid phone number for US.\r\n\t\tfindNumbers('1111111111', 'US', metadata).should.deep.equal([])\r\n\t})\r\n\r\n\tit('should find non-European digits', () => {\r\n\t\t// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n\t\tfindNumbers('العَرَبِيَّة‎ +٤٤٣٣٣٣٣٣٣٣٣٣عَرَبِيّ‎', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'GB',\r\n\t\t\tphone    : '3333333333',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 27\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should iterate', () => {\r\n\t\tconst expected_numbers = [{\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\t// number   : '(*************',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}]\r\n\r\n\t\tfor (const number of searchPhoneNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\tnumber.should.deep.equal(expected_numbers.shift())\r\n\t\t}\r\n\r\n\t\texpected_numbers.length.should.equal(0)\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No input\r\n\t\tfindNumbers('', metadata).should.deep.equal([])\r\n\r\n\t\t// No country metadata for this `require` country code\r\n\t\tthrower = () => findNumbers('123', 'ZZ', metadata)\r\n\t\tthrower.should.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => findNumbers(2141111111, 'US')\r\n\t\tthrower.should.throw('A text for parsing must be a string.')\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => findNumbers('')\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\t})\r\n\r\n\tit('shouldn\\'t find phone numbers which are not phone numbers', () => {\r\n\t\t// A timestamp.\r\n\t\tfindNumbers('2012-01-02 08:00', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// A valid number (not a complete timestamp).\r\n\t\tfindNumbers('2012-01-02 08', 'US', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'US',\r\n\t\t\tphone    : '2012010208',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 13\r\n\t\t}])\r\n\r\n\t\t// Invalid parens.\r\n\t\tfindNumbers('213(3734253', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// Letters after phone number.\r\n\t\tfindNumbers('2133734253a', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// Valid phone (same as the one found in the UUID below).\r\n\t\tfindNumbers('The phone number is 231354125.', 'FR', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'FR',\r\n\t\t\tphone    : '231354125',\r\n\t\t\tstartsAt : 20,\r\n\t\t\tendsAt   : 29\r\n\t\t}])\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Should parse in `{ extended: true }` mode.\r\n\t\tconst possibleNumbers = findNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', { extended: true }, metadata)\r\n\t\tpossibleNumbers.length.should.equal(3)\r\n\t\tpossibleNumbers[1].country.should.equal('FR')\r\n\t\tpossibleNumbers[1].phone.should.equal('231354125')\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Shouldn't parse by default.\r\n\t\tfindNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', metadata).should.deep.equal([])\r\n\t})\r\n})\r\n\r\ndescribe('PhoneNumberSearch', () => {\r\n\tit('should search for phone numbers', () => {\r\n\t\tconst finder = new PhoneNumberSearch('The number is +7 (800) 555-35-35 and not (************* as written in the document.', { defaultCountry: 'US' }, metadata)\r\n\r\n\t\tfinder.hasNext().should.equal(true)\r\n\t\tfinder.next().should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t})\r\n\r\n\t\tfinder.hasNext().should.equal(true)\r\n\t\tfinder.next().should.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\t// number   : '(*************',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t})\r\n\r\n\t\tfinder.hasNext().should.equal(false)\r\n\t})\r\n\r\n\tit('should search for phone numbers (no options)', () => {\r\n\t\tconst finder = new PhoneNumberSearch('The number is +7 (800) 555-35-35', undefined, metadata)\r\n\t\tfinder.hasNext().should.equal(true)\r\n\t\tfinder.next().should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t})\r\n\t\tfinder.hasNext().should.equal(false)\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// No options\r\n\t\tconst search = new PhoneNumberSearch('', undefined, metadata)\r\n\r\n\t\t// No next element\r\n\t\tlet thrower = () => search.next()\r\n\t\tthrower.should.throw('No next element')\r\n\t})\r\n})"], "mappings": ";;;;AAGA;;AACA;;AACA;;;;;;;;;;;;;;AAEAA,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,qBAAD,EAAwB,YAAM;IAC/B,IAAAC,4BAAA,EAAY,YAAZ,EAA0B,IAA1B,EAAgCC,uBAAhC,EAA0CC,MAA1C,CAAiDC,IAAjD,CAAsDC,KAAtD,CAA4D,CAAC;MAC5DC,KAAK,EAAM,YADiD;MAE5DC,OAAO,EAAI,IAFiD;MAG5DC,QAAQ,EAAG,CAHiD;MAI5DC,MAAM,EAAK;IAJiD,CAAD,CAA5D;IAOA,IAAAR,4BAAA,EAAY,gBAAZ,EAA8B,IAA9B,EAAoCC,uBAApC,EAA8CC,MAA9C,CAAqDC,IAArD,CAA0DC,KAA1D,CAAgE,CAAC;MAChEC,KAAK,EAAM,YADqD;MAEhEC,OAAO,EAAI,IAFqD;MAGhEC,QAAQ,EAAG,CAHqD;MAIhEC,MAAM,EAAK;IAJqD,CAAD,CAAhE;IAOA,IAAAR,4BAAA,EAAY,qFAAZ,EAAmG,IAAnG,EAAyGC,uBAAzG,EAAmHC,MAAnH,CAA0HC,IAA1H,CAA+HC,KAA/H,CAAqI,CAAC;MACrIC,KAAK,EAAM,YAD0H;MAErIC,OAAO,EAAI,IAF0H;MAGrIC,QAAQ,EAAG,EAH0H;MAIrIC,MAAM,EAAK;IAJ0H,CAAD,EAKlI;MACFH,KAAK,EAAM,YADT;MAEFC,OAAO,EAAI,IAFT;MAGFC,QAAQ,EAAG,EAHT;MAIFC,MAAM,EAAK;IAJT,CALkI,CAArI,EAf+B,CA2B/B;IACA;;IACA,IAAAR,4BAAA,EAAY,6HAAZ,EAA2I,IAA3I,EAAiJC,uBAAjJ,EAA2JC,MAA3J,CAAkKC,IAAlK,CAAuKC,KAAvK,CAA6K,CAAC;MAC7KC,KAAK,EAAM,YADkK;MAE7KC,OAAO,EAAI,IAFkK;MAG7KC,QAAQ,EAAG,EAHkK;MAI7KC,MAAM,EAAK;IAJkK,CAAD,EAK1K;MACFH,KAAK,EAAM,YADT;MAEFC,OAAO,EAAI,IAFT;MAGFC,QAAQ,EAAG,EAHT;MAIFC,MAAM,EAAK;IAJT,CAL0K,CAA7K,EA7B+B,CAyC/B;;IACA,IAAAR,4BAAA,EAAY,8DAAZ,EAA4EC,uBAA5E,EAAsFC,MAAtF,CAA6FC,IAA7F,CAAkGC,KAAlG,CAAwG,CAAC;MACxGC,KAAK,EAAM,YAD6F;MAExGC,OAAO,EAAI,IAF6F;MAGxGC,QAAQ,EAAG,EAH6F;MAIxGC,MAAM,EAAK;IAJ6F,CAAD,CAAxG,EA1C+B,CAiD/B;;IACA,IAAAR,4BAAA,EAAY,8DAAZ,EAA4E,IAA5E,EAAkF;MAAES,QAAQ,EAAE;IAAZ,CAAlF,EAAyGR,uBAAzG,EAAmHC,MAAnH,CAA0HC,IAA1H,CAA+HC,KAA/H,CAAqI,CAAC;MACrIC,KAAK,EAAM,YAD0H;MAErIC,OAAO,EAAI,IAF0H;MAGrIC,QAAQ,EAAG,EAH0H;MAIrIC,MAAM,EAAK;IAJ0H,CAAD,CAArI,EAlD+B,CAyD/B;;IACA,IAAAR,4BAAA,EAAY,8DAAZ,EAA4E;MAAES,QAAQ,EAAE;IAAZ,CAA5E,EAAmGR,uBAAnG,EAA6GC,MAA7G,CAAoHC,IAApH,CAAyHC,KAAzH,CAA+H,CAAC;MAC/HC,KAAK,EAAM,YADoH;MAE/HC,OAAO,EAAI,IAFoH;MAG/HC,QAAQ,EAAG,EAHoH;MAI/HC,MAAM,EAAK;IAJoH,CAAD,CAA/H,EA1D+B,CAiE/B;;IACA,IAAAR,4BAAA,EAAY,wDAAZ,EAAsE;MAAES,QAAQ,EAAE;IAAZ,CAAtE,EAA6FR,uBAA7F,EAAuGC,MAAvG,CAA8GC,IAA9G,CAAmHC,KAAnH,CAAyH,CAAC;MACzHC,KAAK,EAAM,YAD8G;MAEzHC,OAAO,EAAI,IAF8G;MAGzHC,QAAQ,EAAG,EAH8G;MAIzHC,MAAM,EAAK;IAJ8G,CAAD,CAAzH,EAlE+B,CAyE/B;;IACA,IAAAR,4BAAA,EAAY,sEAAZ,EAAoF;MAAES,QAAQ,EAAE;IAAZ,CAApF,EAA2GR,uBAA3G,EAAqHC,MAArH,CAA4HC,IAA5H,CAAiIC,KAAjI,CAAuI,CAAC;MACvIC,KAAK,EAAM,YAD4H;MAEvIC,OAAO,EAAI,IAF4H;MAGvII,GAAG,EAAQ,KAH4H;MAIvIH,QAAQ,EAAG,EAJ4H;MAKvIC,MAAM,EAAK;IAL4H,CAAD,CAAvI;EAOA,CAjFC,CAAF;EAmFAT,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C;IACA,IAAAC,4BAAA,EAAY,YAAZ,EAA0B,IAA1B,EAAgCC,uBAAhC,EAA0CC,MAA1C,CAAiDC,IAAjD,CAAsDC,KAAtD,CAA4D,EAA5D;EACA,CAHC,CAAF;EAKAL,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C;IACA,IAAAC,4BAAA,EAAY,sCAAZ,EAAoDC,uBAApD,EAA8DC,MAA9D,CAAqEC,IAArE,CAA0EC,KAA1E,CAAgF,CAAC;MAChFE,OAAO,EAAI,IADqE;MAEhFD,KAAK,EAAM,YAFqE;MAGhFE,QAAQ,EAAG,EAHqE;MAIhFC,MAAM,EAAK;IAJqE,CAAD,CAAhF;EAMA,CARC,CAAF;EAUAT,EAAE,CAAC,gBAAD,EAAmB,YAAM;IAC1B,IAAMY,gBAAgB,GAAG,CAAC;MACzBL,OAAO,EAAG,IADe;MAEzBD,KAAK,EAAK,YAFe;MAGzB;MACAE,QAAQ,EAAG,EAJc;MAKzBC,MAAM,EAAK;IALc,CAAD,EAMtB;MACFF,OAAO,EAAG,IADR;MAEFD,KAAK,EAAK,YAFR;MAGF;MACAE,QAAQ,EAAG,EAJT;MAKFC,MAAM,EAAK;IALT,CANsB,CAAzB;;IAcA,qDAAqB,IAAAI,oCAAA,EAAmB,qFAAnB,EAA0G,IAA1G,EAAgHX,uBAAhH,CAArB,wCAAgJ;MAAA,IAArIY,MAAqI;MAC/IA,MAAM,CAACX,MAAP,CAAcC,IAAd,CAAmBC,KAAnB,CAAyBO,gBAAgB,CAACG,KAAjB,EAAzB;IACA;;IAEDH,gBAAgB,CAACI,MAAjB,CAAwBb,MAAxB,CAA+BE,KAA/B,CAAqC,CAArC;EACA,CApBC,CAAF;EAsBAL,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAIiB,OAAJ,CADqC,CAGrC;;IACA,IAAAhB,4BAAA,EAAY,EAAZ,EAAgBC,uBAAhB,EAA0BC,MAA1B,CAAiCC,IAAjC,CAAsCC,KAAtC,CAA4C,EAA5C,EAJqC,CAMrC;;IACAY,OAAO,GAAG;MAAA,OAAM,IAAAhB,4BAAA,EAAY,KAAZ,EAAmB,IAAnB,EAAyBC,uBAAzB,CAAN;IAAA,CAAV;;IACAe,OAAO,CAACd,MAAR,UAAqB,iBAArB,EARqC,CAUrC;;IACAc,OAAO,GAAG;MAAA,OAAM,IAAAhB,4BAAA,EAAY,UAAZ,EAAwB,IAAxB,CAAN;IAAA,CAAV;;IACAgB,OAAO,CAACd,MAAR,UAAqB,sCAArB,EAZqC,CAcrC;IACA;IACA;EACA,CAjBC,CAAF;EAmBAH,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE;IACA,IAAAC,4BAAA,EAAY,kBAAZ,EAAgC,IAAhC,EAAsCC,uBAAtC,EAAgDC,MAAhD,CAAuDC,IAAvD,CAA4DC,KAA5D,CAAkE,EAAlE,EAFqE,CAIrE;;IACA,IAAAJ,4BAAA,EAAY,eAAZ,EAA6B,IAA7B,EAAmCC,uBAAnC,EAA6CC,MAA7C,CAAoDC,IAApD,CAAyDC,KAAzD,CAA+D,CAAC;MAC/DE,OAAO,EAAI,IADoD;MAE/DD,KAAK,EAAM,YAFoD;MAG/DE,QAAQ,EAAG,CAHoD;MAI/DC,MAAM,EAAK;IAJoD,CAAD,CAA/D,EALqE,CAYrE;;IACA,IAAAR,4BAAA,EAAY,aAAZ,EAA2B,IAA3B,EAAiCC,uBAAjC,EAA2CC,MAA3C,CAAkDC,IAAlD,CAAuDC,KAAvD,CAA6D,EAA7D,EAbqE,CAerE;;IACA,IAAAJ,4BAAA,EAAY,aAAZ,EAA2B,IAA3B,EAAiCC,uBAAjC,EAA2CC,MAA3C,CAAkDC,IAAlD,CAAuDC,KAAvD,CAA6D,EAA7D,EAhBqE,CAkBrE;;IACA,IAAAJ,4BAAA,EAAY,gCAAZ,EAA8C,IAA9C,EAAoDC,uBAApD,EAA8DC,MAA9D,CAAqEC,IAArE,CAA0EC,KAA1E,CAAgF,CAAC;MAChFE,OAAO,EAAI,IADqE;MAEhFD,KAAK,EAAM,WAFqE;MAGhFE,QAAQ,EAAG,EAHqE;MAIhFC,MAAM,EAAK;IAJqE,CAAD,CAAhF,EAnBqE,CA0BrE;IACA;;IACA,IAAMS,eAAe,GAAG,IAAAjB,4BAAA,EAAY,iDAAZ,EAA+D,IAA/D,EAAqE;MAAEkB,QAAQ,EAAE;IAAZ,CAArE,EAAyFjB,uBAAzF,CAAxB;IACAgB,eAAe,CAACF,MAAhB,CAAuBb,MAAvB,CAA8BE,KAA9B,CAAoC,CAApC;IACAa,eAAe,CAAC,CAAD,CAAf,CAAmBX,OAAnB,CAA2BJ,MAA3B,CAAkCE,KAAlC,CAAwC,IAAxC;IACAa,eAAe,CAAC,CAAD,CAAf,CAAmBZ,KAAnB,CAAyBH,MAAzB,CAAgCE,KAAhC,CAAsC,WAAtC,EA/BqE,CAiCrE;IACA;;IACA,IAAAJ,4BAAA,EAAY,iDAAZ,EAA+D,IAA/D,EAAqEC,uBAArE,EAA+EC,MAA/E,CAAsFC,IAAtF,CAA2FC,KAA3F,CAAiG,EAAjG;EACA,CApCC,CAAF;AAqCA,CAjLO,CAAR;AAmLAN,QAAQ,CAAC,mBAAD,EAAsB,YAAM;EACnCC,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C,IAAMoB,MAAM,GAAG,IAAIC,wDAAJ,CAAsB,qFAAtB,EAA6G;MAAEC,cAAc,EAAE;IAAlB,CAA7G,EAAuIpB,uBAAvI,CAAf;IAEAkB,MAAM,CAACG,OAAP,GAAiBpB,MAAjB,CAAwBE,KAAxB,CAA8B,IAA9B;IACAe,MAAM,CAACI,IAAP,GAAcrB,MAAd,CAAqBC,IAArB,CAA0BC,KAA1B,CAAgC;MAC/BE,OAAO,EAAG,IADqB;MAE/BD,KAAK,EAAK,YAFqB;MAG/B;MACAE,QAAQ,EAAG,EAJoB;MAK/BC,MAAM,EAAK;IALoB,CAAhC;IAQAW,MAAM,CAACG,OAAP,GAAiBpB,MAAjB,CAAwBE,KAAxB,CAA8B,IAA9B;IACAe,MAAM,CAACI,IAAP,GAAcrB,MAAd,CAAqBC,IAArB,CAA0BC,KAA1B,CAAgC;MAC/BE,OAAO,EAAG,IADqB;MAE/BD,KAAK,EAAK,YAFqB;MAG/B;MACAE,QAAQ,EAAG,EAJoB;MAK/BC,MAAM,EAAK;IALoB,CAAhC;IAQAW,MAAM,CAACG,OAAP,GAAiBpB,MAAjB,CAAwBE,KAAxB,CAA8B,KAA9B;EACA,CAtBC,CAAF;EAwBAL,EAAE,CAAC,8CAAD,EAAiD,YAAM;IACxD,IAAMoB,MAAM,GAAG,IAAIC,wDAAJ,CAAsB,kCAAtB,EAA0DI,SAA1D,EAAqEvB,uBAArE,CAAf;IACAkB,MAAM,CAACG,OAAP,GAAiBpB,MAAjB,CAAwBE,KAAxB,CAA8B,IAA9B;IACAe,MAAM,CAACI,IAAP,GAAcrB,MAAd,CAAqBC,IAArB,CAA0BC,KAA1B,CAAgC;MAC/BE,OAAO,EAAG,IADqB;MAE/BD,KAAK,EAAK,YAFqB;MAG/B;MACAE,QAAQ,EAAG,EAJoB;MAK/BC,MAAM,EAAK;IALoB,CAAhC;IAOAW,MAAM,CAACG,OAAP,GAAiBpB,MAAjB,CAAwBE,KAAxB,CAA8B,KAA9B;EACA,CAXC,CAAF;EAaAL,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACA,IAAM0B,MAAM,GAAG,IAAIL,wDAAJ,CAAsB,EAAtB,EAA0BI,SAA1B,EAAqCvB,uBAArC,CAAf,CAFqC,CAIrC;;IACA,IAAIe,OAAO,GAAG,SAAVA,OAAU;MAAA,OAAMS,MAAM,CAACF,IAAP,EAAN;IAAA,CAAd;;IACAP,OAAO,CAACd,MAAR,UAAqB,iBAArB;EACA,CAPC,CAAF;AAQA,CA9CO,CAAR"}