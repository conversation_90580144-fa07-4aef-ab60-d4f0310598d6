{"version": 3, "file": "ValidationError.js", "sourceRoot": "", "sources": ["../../../src/validation/ValidationError.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,MAAa,eAAe;IAuC1B;;;;;;OAMG;IACH,QAAQ,CACN,iBAA0B,KAAK,EAC/B,YAAqB,KAAK,EAC1B,aAAqB,EAAE,EACvB,yBAAkC,KAAK;QAEvC,MAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,MAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QACjD,MAAM,mBAAmB,GAAG,GAAG,EAAE,WAC/B,OAAA,CAAC,sBAAsB,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAA,IAAI,CAAC,WAAW,mCAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,EAAA,CAAC;QAC5F,MAAM,oBAAoB,GAAG,CAAC,YAAoB,EAAU,EAAE,CAC5D,eAAe,SAAS,GAAG,UAAU,GAAG,YAAY,GAAG,OAAO,0CAA0C,SAAS,GAAG,mBAAmB,EAAE,GAAG,OAAO,KAAK,CAAC;QAE3J,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CACL,kBAAkB,SAAS,GACzB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAC/C,GAAG,OAAO,+BAA+B;gBACzC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7D,CAAC,IAAI,CAAC,QAAQ;oBACZ,CAAC,CAAC,IAAI,CAAC,QAAQ;yBACV,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,sBAAsB,CAAC,CAAC;yBACnG,IAAI,CAAC,EAAE,CAAC;oBACb,CAAC,CAAC,EAAE,CAAC,CACR,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,6DAA6D;YAC7D,MAAM,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACxD,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,GAAG;gBACtB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE/C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,OAAO,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC,QAAQ;oBAClB,CAAC,CAAC,IAAI,CAAC,QAAQ;yBACV,GAAG,CAAC,UAAU,CAAC,EAAE,CAChB,UAAU,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,GAAG,UAAU,GAAG,iBAAiB,EAAE,EAAE,sBAAsB,CAAC,CACvG;yBACA,IAAI,CAAC,EAAE,CAAC;oBACb,CAAC,CAAC,EAAE,CAAC;YACT,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA1FD,0CA0FC", "sourcesContent": ["/**\n * Validation error description.\n */\nexport class ValidationError {\n  /**\n   * Object that was validated.\n   *\n   * OPTIONAL - configurable via the ValidatorOptions.validationError.target option\n   */\n  target?: object;\n\n  /**\n   * Object's property that haven't pass validation.\n   */\n  property: string;\n\n  /**\n   * Value that haven't pass a validation.\n   *\n   * OPTIONAL - configurable via the ValidatorOptions.validationError.value option\n   */\n  value?: any;\n\n  /**\n   * Constraints that failed validation with error messages.\n   */\n  constraints?: {\n    [type: string]: string;\n  };\n\n  /**\n   * Contains all nested validation errors of the property.\n   */\n  children?: ValidationError[];\n\n  /*\n   * A transient set of data passed through to the validation result for response mapping\n   */\n  contexts?: {\n    [type: string]: any;\n  };\n\n  /**\n   *\n   * @param shouldDecorate decorate the message with ANSI formatter escape codes for better readability\n   * @param hasParent true when the error is a child of an another one\n   * @param parentPath path as string to the parent of this property\n   * @param showConstraintMessages show constraint messages instead of constraint names\n   */\n  toString(\n    shouldDecorate: boolean = false,\n    hasParent: boolean = false,\n    parentPath: string = ``,\n    showConstraintMessages: boolean = false\n  ): string {\n    const boldStart = shouldDecorate ? `\\x1b[1m` : ``;\n    const boldEnd = shouldDecorate ? `\\x1b[22m` : ``;\n    const constraintsToString = () =>\n      (showConstraintMessages ? Object.values : Object.keys)(this.constraints ?? {}).join(`, `);\n    const propConstraintFailed = (propertyName: string): string =>\n      ` - property ${boldStart}${parentPath}${propertyName}${boldEnd} has failed the following constraints: ${boldStart}${constraintsToString()}${boldEnd} \\n`;\n\n    if (!hasParent) {\n      return (\n        `An instance of ${boldStart}${\n          this.target ? this.target.constructor.name : 'an object'\n        }${boldEnd} has failed the validation:\\n` +\n        (this.constraints ? propConstraintFailed(this.property) : ``) +\n        (this.children\n          ? this.children\n              .map(childError => childError.toString(shouldDecorate, true, this.property, showConstraintMessages))\n              .join(``)\n          : ``)\n      );\n    } else {\n      // we format numbers as array indexes for better readability.\n      const formattedProperty = Number.isInteger(+this.property)\n        ? `[${this.property}]`\n        : `${parentPath ? `.` : ``}${this.property}`;\n\n      if (this.constraints) {\n        return propConstraintFailed(formattedProperty);\n      } else {\n        return this.children\n          ? this.children\n              .map(childError =>\n                childError.toString(shouldDecorate, true, `${parentPath}${formattedProperty}`, showConstraintMessages)\n              )\n              .join(``)\n          : ``;\n      }\n    }\n  }\n}\n"]}