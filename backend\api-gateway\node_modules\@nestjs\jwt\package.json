{"name": "@nestjs/jwt", "version": "10.2.0", "description": "Nest - modern, fast, powerful node.js web framework (@jwt)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"format": "prettier --write \"**/*.ts\"", "lint": "eslint \"lib/**/*.ts\" --fix", "test": "jest --config=jest.json", "test:watch": "jest --config=jest.json --watch", "test:coverage": "jest --config=jest.json --coverage --coverageDirectory=coverage", "build": "rm -rf dist && tsc -p tsconfig.json", "precommit": "lint-staged", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prerelease": "npm run build", "release": "release-it", "prepare": "husky install"}, "peerDependencies": {"@nestjs/common": "^8.0.0 || ^9.0.0 || ^10.0.0"}, "devDependencies": {"@commitlint/cli": "18.2.0", "@commitlint/config-angular": "18.1.0", "@nestjs/common": "10.2.8", "@nestjs/core": "10.2.8", "@nestjs/testing": "10.2.8", "@types/jest": "29.5.8", "@types/node": "20.9.0", "@typescript-eslint/eslint-plugin": "6.10.0", "@typescript-eslint/parser": "6.10.0", "eslint": "8.53.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-import": "2.29.0", "husky": "8.0.3", "jest": "29.7.0", "lint-staged": "15.0.2", "prettier": "3.0.3", "reflect-metadata": "0.1.13", "release-it": "16.2.1", "rxjs": "7.8.1", "ts-jest": "29.1.1", "typescript": "5.2.2"}, "dependencies": {"@types/jsonwebtoken": "9.0.5", "jsonwebtoken": "9.0.2"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/jwt"}}