{"version": 3, "names": ["_OverloadYield", "require", "_regeneratorDefine", "AsyncIterator", "generator", "PromiseImpl", "next", "define", "prototype", "Symbol", "asyncIterator", "invoke", "method", "arg", "resolve", "reject", "result", "value", "OverloadYield", "v", "then", "err", "unwrapped", "error", "previousPromise", "enqueue", "i", "callInvokeWithMethodAndArg"], "sources": ["../../src/helpers/regeneratorAsyncIterator.ts"], "sourcesContent": ["/* @minVersion 7.27.0 */\n/* @mangleFns */\n/* @internal */\n\nimport OverloadYield from \"./OverloadYield.ts\";\nimport define from \"./regeneratorDefine.ts\";\n\nexport default /* @no-mangle */ function AsyncIterator(\n  this: any,\n  generator: Generator,\n  PromiseImpl: PromiseConstructor,\n) {\n  if (!this.next) {\n    define(AsyncIterator.prototype);\n    define(\n      AsyncIterator.prototype,\n      (typeof Symbol === \"function\" && Symbol.asyncIterator) ||\n        \"@asyncIterator\",\n      function (this: any) {\n        return this;\n      },\n    );\n  }\n\n  function invoke(\n    method: \"next\" | \"throw\" | \"return\",\n    arg: any,\n    resolve: (value: any) => void,\n    reject: (error: any) => void,\n  ): any {\n    try {\n      var result = generator[method](arg);\n      var value = result.value;\n      if (value instanceof OverloadYield) {\n        return PromiseImpl.resolve(value.v).then(\n          function (value) {\n            invoke(\"next\", value, resolve, reject);\n          },\n          function (err) {\n            invoke(\"throw\", err, resolve, reject);\n          },\n        );\n      }\n\n      return PromiseImpl.resolve(value).then(\n        function (unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        },\n        function (error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        },\n      );\n    } catch (error) {\n      reject(error);\n    }\n  }\n\n  var previousPromise: Promise<any>;\n\n  function enqueue(method: \"next\" | \"throw\" | \"return\", i: number, arg: any) {\n    function callInvokeWithMethodAndArg() {\n      return new PromiseImpl(function (resolve, reject) {\n        invoke(method, arg, resolve, reject);\n      });\n    }\n\n    return (previousPromise =\n      // If enqueue has been called before, then we want to wait until\n      // all previous Promises have been resolved before calling invoke,\n      // so that results are always delivered in the correct order. If\n      // enqueue has not been called before, then it is important to\n      // call invoke immediately, without waiting on a callback to fire,\n      // so that the async generator function has the opportunity to do\n      // any necessary setup in a predictable way. This predictability\n      // is why the Promise constructor synchronously invokes its\n      // executor callback, and why async functions synchronously\n      // execute code before the first await. Since we implement simple\n      // async functions in terms of async generators, it is especially\n      // important to get this right, even though it requires care.\n      previousPromise\n        ? previousPromise.then(\n            callInvokeWithMethodAndArg,\n            // Avoid propagating failures to Promises returned by later\n            // invocations of the iterator.\n            callInvokeWithMethodAndArg,\n          )\n        : callInvokeWithMethodAndArg());\n  }\n\n  // Define the unified helper method that is used to implement .next,\n  // .throw, and .return (see defineIteratorMethods).\n  define(this, \"_invoke\", enqueue, true);\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAEgC,SAASE,aAAaA,CAEpDC,SAAoB,EACpBC,WAA+B,EAC/B;EACA,IAAI,CAAC,IAAI,CAACC,IAAI,EAAE;IACd,IAAAC,0BAAM,EAACJ,aAAa,CAACK,SAAS,CAAC;IAC/B,IAAAD,0BAAM,EACJJ,aAAa,CAACK,SAAS,EACtB,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,aAAa,IACnD,gBAAgB,EAClB,YAAqB;MACnB,OAAO,IAAI;IACb,CACF,CAAC;EACH;EAEA,SAASC,MAAMA,CACbC,MAAmC,EACnCC,GAAQ,EACRC,OAA6B,EAC7BC,MAA4B,EACvB;IACL,IAAI;MACF,IAAIC,MAAM,GAAGZ,SAAS,CAACQ,MAAM,CAAC,CAACC,GAAG,CAAC;MACnC,IAAII,KAAK,GAAGD,MAAM,CAACC,KAAK;MACxB,IAAIA,KAAK,YAAYC,sBAAa,EAAE;QAClC,OAAOb,WAAW,CAACS,OAAO,CAACG,KAAK,CAACE,CAAC,CAAC,CAACC,IAAI,CACtC,UAAUH,KAAK,EAAE;UACfN,MAAM,CAAC,MAAM,EAAEM,KAAK,EAAEH,OAAO,EAAEC,MAAM,CAAC;QACxC,CAAC,EACD,UAAUM,GAAG,EAAE;UACbV,MAAM,CAAC,OAAO,EAAEU,GAAG,EAAEP,OAAO,EAAEC,MAAM,CAAC;QACvC,CACF,CAAC;MACH;MAEA,OAAOV,WAAW,CAACS,OAAO,CAACG,KAAK,CAAC,CAACG,IAAI,CACpC,UAAUE,SAAS,EAAE;QAInBN,MAAM,CAACC,KAAK,GAAGK,SAAS;QACxBR,OAAO,CAACE,MAAM,CAAC;MACjB,CAAC,EACD,UAAUO,KAAK,EAAE;QAGf,OAAOZ,MAAM,CAAC,OAAO,EAAEY,KAAK,EAAET,OAAO,EAAEC,MAAM,CAAC;MAChD,CACF,CAAC;IACH,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdR,MAAM,CAACQ,KAAK,CAAC;IACf;EACF;EAEA,IAAIC,eAA6B;EAEjC,SAASC,OAAOA,CAACb,MAAmC,EAAEc,CAAS,EAAEb,GAAQ,EAAE;IACzE,SAASc,0BAA0BA,CAAA,EAAG;MACpC,OAAO,IAAItB,WAAW,CAAC,UAAUS,OAAO,EAAEC,MAAM,EAAE;QAChDJ,MAAM,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,CAAC;MACtC,CAAC,CAAC;IACJ;IAEA,OAAQS,eAAe,GAarBA,eAAe,GACXA,eAAe,CAACJ,IAAI,CAClBO,0BAA0B,EAG1BA,0BACF,CAAC,GACDA,0BAA0B,CAAC,CAAC;EACpC;EAIA,IAAApB,0BAAM,EAAC,IAAI,EAAE,SAAS,EAAEkB,OAAO,EAAE,IAAI,CAAC;AACxC", "ignoreList": []}