/**
 * اختبار بسيط لخدمة المصادقة
 * Simple test for Auth Service
 */

const http = require('http');

// Test configuration
const TEST_CONFIG = {
  host: 'localhost',
  port: 3001,
  timeout: 5000,
};

// Test data
const TEST_USER = {
  firstName: 'أحمد',
  lastName: 'محمد',
  email: `test${Date.now()}@example.com`,
  phone: `+96650${Math.floor(Math.random() * 10000000)}`,
  password: 'SecurePass123!',
  confirmPassword: 'SecurePass123!',
  acceptTerms: 'true',
  acceptPrivacy: 'true',
};

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedBody = body ? JSON.parse(body) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedBody,
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.setTimeout(TEST_CONFIG.timeout);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test functions
async function testHealthCheck() {
  console.log('🏥 Testing Health Check...');
  
  try {
    const response = await makeRequest({
      hostname: TEST_CONFIG.host,
      port: TEST_CONFIG.port,
      path: '/api/v1/health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.statusCode === 200) {
      console.log('✅ Health Check: PASSED');
      console.log(`   Status: ${response.body.status}`);
      console.log(`   Service: ${response.body.service}`);
      return true;
    } else {
      console.log('❌ Health Check: FAILED');
      console.log(`   Status Code: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Health Check: ERROR');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function testServiceInfo() {
  console.log('ℹ️  Testing Service Info...');
  
  try {
    const response = await makeRequest({
      hostname: TEST_CONFIG.host,
      port: TEST_CONFIG.port,
      path: '/api/v1/health/live',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.statusCode === 200) {
      console.log('✅ Service Info: PASSED');
      console.log(`   Status: ${response.body.status}`);
      return true;
    } else {
      console.log('❌ Service Info: FAILED');
      console.log(`   Status Code: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Service Info: ERROR');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function testRegisterValidation() {
  console.log('📝 Testing Registration Validation...');
  
  try {
    // Test with invalid data
    const invalidData = {
      firstName: 'A', // Too short
      lastName: 'B', // Too short
      email: 'invalid-email', // Invalid format
      phone: '123', // Invalid format
      password: '123', // Too weak
      confirmPassword: '456', // Doesn't match
    };

    const response = await makeRequest({
      hostname: TEST_CONFIG.host,
      port: TEST_CONFIG.port,
      path: '/api/v1/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    }, invalidData);

    if (response.statusCode === 400) {
      console.log('✅ Registration Validation: PASSED');
      console.log('   Correctly rejected invalid data');
      return true;
    } else {
      console.log('❌ Registration Validation: FAILED');
      console.log(`   Expected 400, got ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Registration Validation: ERROR');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function testLoginValidation() {
  console.log('🔐 Testing Login Validation...');
  
  try {
    // Test with invalid credentials
    const invalidLogin = {
      email: '<EMAIL>',
      password: 'wrongpassword',
    };

    const response = await makeRequest({
      hostname: TEST_CONFIG.host,
      port: TEST_CONFIG.port,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    }, invalidLogin);

    if (response.statusCode === 401) {
      console.log('✅ Login Validation: PASSED');
      console.log('   Correctly rejected invalid credentials');
      return true;
    } else {
      console.log('❌ Login Validation: FAILED');
      console.log(`   Expected 401, got ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Login Validation: ERROR');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function testSwaggerDocs() {
  console.log('📚 Testing Swagger Documentation...');
  
  try {
    const response = await makeRequest({
      hostname: TEST_CONFIG.host,
      port: TEST_CONFIG.port,
      path: '/docs',
      method: 'GET',
      headers: {
        'Accept': 'text/html',
      },
    });

    if (response.statusCode === 200 || response.statusCode === 404) {
      console.log('✅ Swagger Docs: PASSED');
      if (response.statusCode === 404) {
        console.log('   Swagger disabled (expected in test mode)');
      } else {
        console.log('   Swagger documentation available');
      }
      return true;
    } else {
      console.log('❌ Swagger Docs: FAILED');
      console.log(`   Status Code: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Swagger Docs: ERROR');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting WS Transfir Auth Service Tests');
  console.log('==========================================');
  console.log(`Target: http://${TEST_CONFIG.host}:${TEST_CONFIG.port}`);
  console.log('');

  const tests = [
    testHealthCheck,
    testServiceInfo,
    testSwaggerDocs,
    testRegisterValidation,
    testLoginValidation,
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Test failed with error: ${error.message}`);
      failed++;
    }
    console.log('');
  }

  console.log('==========================================');
  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Check the service status.');
    process.exit(1);
  }
}

// Check if service is running before starting tests
async function checkServiceAvailability() {
  console.log('🔍 Checking if Auth Service is running...');
  
  try {
    const response = await makeRequest({
      hostname: TEST_CONFIG.host,
      port: TEST_CONFIG.port,
      path: '/api/v1/health',
      method: 'GET',
      timeout: 2000,
    });

    if (response.statusCode === 200) {
      console.log('✅ Auth Service is running');
      console.log('');
      return true;
    } else {
      console.log('❌ Auth Service returned unexpected status:', response.statusCode);
      return false;
    }
  } catch (error) {
    console.log('❌ Auth Service is not running or not accessible');
    console.log(`   Error: ${error.message}`);
    console.log('');
    console.log('💡 To start the service, run:');
    console.log('   cd backend/auth-service');
    console.log('   npm install');
    console.log('   npm run start:dev');
    console.log('');
    return false;
  }
}

// Start the test suite
checkServiceAvailability().then((isAvailable) => {
  if (isAvailable) {
    runTests();
  } else {
    process.exit(1);
  }
});
