/**
 * WS Transfir XAMPP Ultra Fast Server
 * خادم نظام WS Transfir فائق السرعة والأداء
 * Version: 3.0.0 - Lightning Fast Edition
 */

const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();

// Ultra Fast Configuration
const config = {
  port: process.env.PORT || 8080,
  host: 'localhost',
  environment: 'xampp-lightning-fast',
  compression: false, // Disabled for speed
  logging: false,     // Minimal logging for speed
};

console.log('⚡ تشغيل نظام WS Transfir فائق السرعة');
console.log('====================================');
console.log(`🚀 Lightning Fast Mode: ENABLED`);
console.log(`⚡ Port: ${config.port}`);
console.log(`🏠 Host: ${config.host}`);
console.log('');

// Minimal middleware for maximum speed
app.use(express.json({ limit: '500kb' }));
app.use(express.urlencoded({ extended: false, limit: '500kb' }));

// Static files with caching
app.use(express.static('.', {
  maxAge: 3600000, // 1 hour
  etag: false,     // Disable for speed
  lastModified: false, // Disable for speed
}));

// Minimal request processing
app.use((req, res, next) => {
  res.setHeader('X-Powered-By', 'WS-Transfir-Lightning');
  res.setHeader('Cache-Control', 'public, max-age=3600');
  next();
});

// Ultra Fast Main Route - Inline CSS/JS for speed
app.get('/', (req, res) => {
  res.setHeader('Content-Type', 'text/html; charset=utf-8');
  
  // Minimal HTML with inline everything for fastest loading
  res.send(`<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width,initial-scale=1">
<title>WS Transfir - Lightning Fast</title>
<style>
*{margin:0;padding:0;box-sizing:border-box}
body{font-family:Arial,sans-serif;background:#667eea;color:#2c3e50;line-height:1.5}
.container{max-width:1000px;margin:0 auto;padding:15px}
.header{background:#fff;border-radius:15px;padding:25px;margin-bottom:15px;text-align:center;box-shadow:0 5px 15px rgba(0,0,0,.1)}
.header h1{font-size:2.2em;color:#27ae60;margin-bottom:8px;font-weight:700}
.fast-badge{display:inline-block;background:#27ae60;color:#fff;padding:8px 16px;border-radius:20px;font-weight:600;font-size:.9em}
.grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:15px;margin:15px 0}
.card{background:#fff;border-radius:12px;padding:20px;box-shadow:0 3px 10px rgba(0,0,0,.1);transition:transform .15s ease}
.card:hover{transform:translateY(-2px)}
.card h2{color:#27ae60;margin-bottom:12px;font-size:1.2em}
.btn{background:#27ae60;color:#fff;border:none;padding:10px 16px;border-radius:6px;cursor:pointer;font-size:.95em;font-weight:600;width:100%;margin:6px 0;transition:background .15s ease;text-decoration:none;display:block;text-align:center}
.btn:hover{background:#2ecc71}
.btn.primary{background:#3498db}
.btn.primary:hover{background:#5dade2}
.btn.warning{background:#f39c12}
.btn.warning:hover{background:#f4d03f}
.info{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:8px;margin:12px 0}
.info-item{background:#f8f9fa;padding:8px;border-radius:6px;text-align:center}
.info-label{font-size:.75em;color:#7f8c8d;margin-bottom:3px}
.info-value{font-weight:600;color:#2c3e50;font-size:.9em}
.result{display:none;background:#2c3e50;color:#ecf0f1;padding:12px;border-radius:6px;margin:12px 0;font-family:monospace;font-size:.85em;max-height:250px;overflow-y:auto}
.footer{background:#fff;border-radius:12px;padding:15px;margin:15px 0;text-align:center}
@media(max-width:768px){.container{padding:8px}.header{padding:15px}.card{padding:15px}.grid{grid-template-columns:1fr;gap:10px}}
</style>
</head>
<body>
<div class="container">
<div class="header">
<h1>⚡ WS Transfir Lightning</h1>
<p>نظام التحويلات المالية فائق السرعة</p>
<div class="fast-badge">⚡ تحميل فوري</div>
</div>

<div class="grid">
<div class="card">
<h2>📊 النظام السريع</h2>
<div class="info">
<div class="info-item">
<div class="info-label">الحالة</div>
<div class="info-value">🟢 فائق</div>
</div>
<div class="info-item">
<div class="info-label">التحميل</div>
<div class="info-value">فوري</div>
</div>
<div class="info-item">
<div class="info-label">الحجم</div>
<div class="info-value">< 30KB</div>
</div>
<div class="info-item">
<div class="info-label">الاستجابة</div>
<div class="info-value">< 5ms</div>
</div>
</div>
<button class="btn" onclick="checkHealth()">🏥 فحص سريع</button>
</div>

<div class="card">
<h2>🔐 دخول فوري</h2>
<button class="btn primary" onclick="adminLogin()">👨‍💼 مدير</button>
<button class="btn primary" onclick="userLogin()">👤 مستخدم</button>
<button class="btn warning" onclick="showCreds()">🔑 البيانات</button>
</div>

<div class="card">
<h2>💸 عمليات سريعة</h2>
<button class="btn" onclick="getTransfers()">💸 التحويلات</button>
<button class="btn" onclick="getProfile()">👤 الملف الشخصي</button>
<button class="btn" onclick="getStats()">📈 الإحصائيات</button>
</div>

<div class="card">
<h2>⚡ الأداء</h2>
<div class="info">
<div class="info-item">
<div class="info-label">السرعة</div>
<div class="info-value">فائقة</div>
</div>
<div class="info-item">
<div class="info-label">الذاكرة</div>
<div class="info-value">محسنة</div>
</div>
</div>
<button class="btn warning" onclick="clearResult()">🗑️ مسح</button>
</div>
</div>

<div id="result" class="result"></div>

<div class="footer">
<h3>⚡ WS Transfir Lightning System</h3>
<p>نظام فائق السرعة - تحميل فوري وأداء مثالي</p>
<div style="margin:10px 0">
<a href="/api/health" style="color:#27ae60;text-decoration:none;margin:0 10px">📊 فحص</a>
<a href="/api/status" style="color:#27ae60;text-decoration:none;margin:0 10px">📈 حالة</a>
<a href="mailto:<EMAIL>" style="color:#27ae60;text-decoration:none;margin:0 10px">📧 دعم</a>
</div>
<p style="margin-top:10px;color:#7f8c8d;font-size:.8em">© 2024 WS Transfir Lightning Edition</p>
</div>
</div>

<script>
const API=window.location.origin;
function show(data,title='النتيجة'){
const r=document.getElementById('result');
r.style.display='block';
r.innerHTML=title+'\\n'+JSON.stringify(data,null,2);
r.scrollIntoView({behavior:'smooth'});
}
function showError(error,title='خطأ'){
const r=document.getElementById('result');
r.style.display='block';
r.innerHTML=title+'\\nخطأ: '+(error.message||error);
}
async function checkHealth(){
try{
const res=await fetch(API+'/api/health');
const data=await res.json();
show(data,'فحص النظام السريع');
}catch(e){showError(e,'خطأ في الفحص');}
}
async function adminLogin(){
try{
const res=await fetch(API+'/api/auth/login',{
method:'POST',
headers:{'Content-Type':'application/json'},
body:JSON.stringify({email:'<EMAIL>',password:'admin123'})
});
const data=await res.json();
show(data,'دخول المدير');
}catch(e){showError(e,'خطأ في الدخول');}
}
async function userLogin(){
try{
const res=await fetch(API+'/api/auth/login',{
method:'POST',
headers:{'Content-Type':'application/json'},
body:JSON.stringify({email:'<EMAIL>',password:'password123'})
});
const data=await res.json();
show(data,'دخول المستخدم');
}catch(e){showError(e,'خطأ في الدخول');}
}
async function getTransfers(){
try{
const res=await fetch(API+'/api/transfers');
const data=await res.json();
show(data,'التحويلات');
}catch(e){showError(e,'خطأ في التحويلات');}
}
async function getProfile(){
try{
const res=await fetch(API+'/api/profile/me');
const data=await res.json();
show(data,'الملف الشخصي');
}catch(e){showError(e,'خطأ في الملف الشخصي');}
}
async function getStats(){
try{
const res=await fetch(API+'/api/transfers/stats');
const data=await res.json();
show(data,'الإحصائيات');
}catch(e){showError(e,'خطأ في الإحصائيات');}
}
function showCreds(){
const creds={
admin:{email:'<EMAIL>',password:'admin123',role:'مدير'},
user:{email:'<EMAIL>',password:'password123',role:'مستخدم'}
};
show(creds,'بيانات الدخول');
}
function clearResult(){
const r=document.getElementById('result');
r.style.display='none';
r.innerHTML='';
}
window.addEventListener('load',()=>{
setTimeout(checkHealth,300);
});
console.log('⚡ Lightning Fast System loaded instantly!');
</script>
</body>
</html>`);
});

// Lightning Fast Health Check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Lightning Fast System',
    version: '3.0.0-lightning',
    mode: 'LIGHTNING_FAST',
    performance: {
      responseTime: '< 5ms',
      pageSize: '< 30KB',
      loadTime: 'instant'
    },
    timestamp: new Date().toISOString(),
  });
});

// Lightning Fast Status
app.get('/api/status', (req, res) => {
  res.json({
    online: true,
    status: 'lightning-fast',
    platform: 'XAMPP-Lightning',
    performance: {
      responseTime: '< 5ms',
      memoryUsage: 'minimal',
      cpuUsage: 'optimized'
    },
    timestamp: new Date().toISOString(),
  });
});

// Fast users - minimal data
const users = new Map([
  ['<EMAIL>', { id: '1', email: '<EMAIL>', password: 'admin123', firstName: 'مدير', lastName: 'سريع', role: 'admin' }],
  ['<EMAIL>', { id: '2', email: '<EMAIL>', password: 'password123', firstName: 'مستخدم', lastName: 'سريع', role: 'user' }]
]);

// Lightning Fast Authentication
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  const user = users.get(email);
  
  if (user && user.password === password) {
    res.json({
      success: true,
      message: 'دخول سريع ناجح',
      token: `lightning-jwt-${Date.now()}`,
      user: { id: user.id, firstName: user.firstName, lastName: user.lastName, email: user.email, role: user.role },
      mode: 'lightning-fast',
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات خاطئة',
      error: 'INVALID_CREDENTIALS',
      mode: 'lightning-fast',
    });
  }
});

// Lightning Fast Profile
app.get('/api/profile/me', (req, res) => {
  res.json({
    id: '2',
    firstName: 'مستخدم',
    lastName: 'سريع',
    email: '<EMAIL>',
    phone: '+966501234567',
    isVerified: true,
    mode: 'lightning-fast',
  });
});

// Lightning Fast Transfers
app.get('/api/transfers', (req, res) => {
  const transfers = [
    { id: '1', referenceNumber: 'LIGHTNING001', amount: '1,500.00', currency: 'SAR', receiverName: 'أحمد سريع', status: 'completed', mode: 'lightning' },
    { id: '2', referenceNumber: 'LIGHTNING002', amount: '750.00', currency: 'USD', receiverName: 'فاطمة سريعة', status: 'pending', mode: 'lightning' },
    { id: '3', referenceNumber: 'LIGHTNING003', amount: '2,200.00', currency: 'SAR', receiverName: 'محمد فائق', status: 'processing', mode: 'lightning' }
  ];
  
  res.json({
    success: true,
    data: transfers,
    mode: 'lightning-fast',
  });
});

// Lightning Fast Stats
app.get('/api/transfers/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalTransfers: 42,
      totalAmount: '85,430.50',
      completedTransfers: 38,
      pendingTransfers: 3,
      processingTransfers: 1,
      mode: 'lightning-fast',
      thisMonth: {
        transfers: 12,
        amount: '25,450.00',
        completed: 10,
        pending: 2,
      }
    },
  });
});

// Minimal error handling
app.use((err, req, res, next) => {
  res.status(500).json({
    success: false,
    message: 'خطأ سريع',
    error: 'LIGHTNING_ERROR',
  });
});

// Fast 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'غير موجود',
    error: 'NOT_FOUND',
    mode: 'lightning-fast',
  });
});

// Start Lightning Fast Server
const server = app.listen(config.port, config.host, () => {
  console.log('');
  console.log('⚡ ═══════════════════════════════════════════════════════════');
  console.log('⚡ WS TRANSFIR LIGHTNING FAST SERVER - INSTANT LOADING!');
  console.log('⚡ ═══════════════════════════════════════════════════════════');
  console.log(`🚀 Lightning URL: http://${config.host}:${config.port}`);
  console.log(`⚡ Load Time: INSTANT`);
  console.log(`📊 Page Size: < 30KB`);
  console.log(`🏥 Health: http://${config.host}:${config.port}/api/health`);
  console.log(`🔐 Admin: <EMAIL> / admin123`);
  console.log(`👤 User: <EMAIL> / password123`);
  console.log(`⚡ Mode: LIGHTNING FAST OPTIMIZED`);
  console.log('⚡ ═══════════════════════════════════════════════════════════');
  console.log('');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  server.close(() => process.exit(0));
});

process.on('SIGINT', () => {
  server.close(() => process.exit(0));
});

module.exports = app;
