version: '3.8'

services:
  # قواعد البيانات
  postgres:
    image: postgres:15-alpine
    container_name: ws-postgres
    environment:
      POSTGRES_DB: ws_transfir
      POSTGRES_USER: ws_user
      POSTGRES_PASSWORD: ws_password
      POSTGRES_MULTIPLE_DATABASES: ws_transfir,ws_transfir_test,ws_transfir_analytics
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init:/docker-entrypoint-initdb.d
      - ./backend/database/migrations:/migrations
      - ./backend/database/seeds:/seeds
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ws_user"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: ws-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./backend/config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - ws-network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  mongodb:
    image: mongo:7
    container_name: ws-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ws_user
      MONGO_INITDB_ROOT_PASSWORD: ws_password
      MONGO_INITDB_DATABASE: ws_transfir_logs
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./backend/database/mongo-init:/docker-entrypoint-initdb.d
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch للبحث والتحليلات
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: ws-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ لإدارة الرسائل
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: ws-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ws_user
      RABBITMQ_DEFAULT_PASS: ws_password
      RABBITMQ_DEFAULT_VHOST: ws_transfir
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./backend/config/rabbitmq:/etc/rabbitmq
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 10s
      retries: 3

  # الخدمات الخلفية
  api-gateway:
    build:
      context: ./backend/api-gateway
      dockerfile: Dockerfile
      target: development
    container_name: ws-api-gateway
    ports:
      - "3000:3000"
      - "9229:9229" # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3000
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
      - MONGODB_URL=************************************************************
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - RABBITMQ_URL=amqp://ws_user:ws_password@rabbitmq:5672/ws_transfir
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
      - ENCRYPTION_KEY=your-encryption-key-32-chars-long
      - CORS_ORIGIN=http://localhost:3100,http://localhost:3000
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./backend/api-gateway:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  auth-service:
    build:
      context: ./backend/auth-service
      dockerfile: Dockerfile
      target: development
    container_name: ws-auth-service
    ports:
      - "3001:3001"
      - "9230:9229" # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3001
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://ws_user:ws_password@rabbitmq:5672/ws_transfir
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
      - ENCRYPTION_KEY=your-encryption-key-32-chars-long
      - EMAIL_SERVICE=gmail
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASS=your-app-password
      - SMS_PROVIDER=twilio
      - TWILIO_ACCOUNT_SID=your-twilio-sid
      - TWILIO_AUTH_TOKEN=your-twilio-token
      - TWILIO_PHONE_NUMBER=+**********
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./backend/auth-service:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  user-service:
    build:
      context: ./backend/user-service
      dockerfile: Dockerfile
      target: development
    container_name: ws-user-service
    ports:
      - "3002:3002"
      - "9231:9229" # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3002
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://ws_user:ws_password@rabbitmq:5672/ws_transfir
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - FILE_UPLOAD_MAX_SIZE=10485760
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    volumes:
      - ./backend/user-service:/app
      - /app/node_modules
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3002/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  transfer-service:
    build:
      context: ./backend/transfer-service
      dockerfile: Dockerfile
      target: development
    container_name: ws-transfer-service
    ports:
      - "3003:3003"
      - "9232:9229" # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3003
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://ws_user:ws_password@rabbitmq:5672/ws_transfir
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
      - PAYMENT_GATEWAY_KEY=your-payment-gateway-key
      - EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key
      - WEBHOOK_SECRET=your-webhook-secret
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    volumes:
      - ./backend/transfer-service:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3003/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  wallet-service:
    build:
      context: ./backend/wallet-service
      dockerfile: Dockerfile
      target: development
    container_name: ws-wallet-service
    ports:
      - "3004:3004"
      - "9233:9229" # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3004
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://ws_user:ws_password@rabbitmq:5672/ws_transfir
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
      - PAYMENT_GATEWAY_KEY=your-payment-gateway-key
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    volumes:
      - ./backend/wallet-service:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3004/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # خدمة الإشعارات
  notification-service:
    build:
      context: ./backend/notification-service
      dockerfile: Dockerfile
      target: development
    container_name: ws-notification-service
    ports:
      - "3005:3005"
      - "9234:9229" # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3005
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://ws_user:ws_password@rabbitmq:5672/ws_transfir
      - EMAIL_SERVICE=gmail
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASS=your-app-password
      - SMS_PROVIDER=twilio
      - TWILIO_ACCOUNT_SID=your-twilio-sid
      - TWILIO_AUTH_TOKEN=your-twilio-token
      - TWILIO_PHONE_NUMBER=+**********
      - FIREBASE_PROJECT_ID=your-firebase-project-id
      - FIREBASE_PRIVATE_KEY=your-firebase-private-key
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./backend/notification-service:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3005/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # محرك الذكاء الاصطناعي
  ai-engine:
    build:
      context: ./backend/ai-engine
      dockerfile: Dockerfile
      target: development
    container_name: ws-ai-engine
    ports:
      - "8000:8000"
      - "5678:5678" # Debug port for Python
    environment:
      - ENVIRONMENT=development
      - POSTGRES_URL=**********************************************/ws_transfir
      - MONGODB_URL=************************************************************
      - REDIS_URL=redis://redis:6379
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ML_MODEL_PATH=/app/models
      - FRAUD_DETECTION_THRESHOLD=0.7
      - LOG_LEVEL=DEBUG
    depends_on:
      postgres:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    volumes:
      - ./backend/ai-engine:/app
      - ./backend/ai-engine/models:/app/models
      - ./backend/logs:/app/logs
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # خدمة التقارير والتحليلات
  analytics-service:
    build:
      context: ./backend/analytics-service
      dockerfile: Dockerfile
      target: development
    container_name: ws-analytics-service
    ports:
      - "3006:3006"
      - "9235:9229" # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3006
      - POSTGRES_URL=**********************************************/ws_transfir
      - MONGODB_URL=************************************************************
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=debug
    depends_on:
      postgres:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/analytics-service:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3006/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # التطبيق الأمامي
  web-app:
    build:
      context: ./frontend/web-app
      dockerfile: Dockerfile
      target: development
    container_name: ws-web-app
    ports:
      - "3100:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3000
      - NEXT_PUBLIC_WS_URL=ws://localhost:3000
      - NEXT_PUBLIC_APP_URL=http://localhost:3100
      - NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key-32-chars-long
    depends_on:
      api-gateway:
        condition: service_healthy
    volumes:
      - ./frontend/web-app:/app
      - /app/node_modules
      - /app/.next
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # أدوات المراقبة والتطوير
  nginx:
    image: nginx:alpine
    container_name: ws-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - web-app
      - api-gateway
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus للمراقبة
  prometheus:
    image: prom/prometheus:latest
    container_name: ws-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ws-network
    restart: unless-stopped

  # Grafana للتصور
  grafana:
    image: grafana/grafana:latest
    container_name: ws-grafana
    ports:
      - "3007:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - ws-network
    restart: unless-stopped

  # Jaeger للتتبع الموزع
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: ws-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - ws-network
    restart: unless-stopped

  # Kibana لـ Elasticsearch
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: ws-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - ws-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Adminer لإدارة قواعد البيانات
  adminer:
    image: adminer:latest
    container_name: ws-adminer
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - ws-network
    restart: unless-stopped

  # Redis Commander لإدارة Redis
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ws-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - ws-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  mongodb_data:
  elasticsearch_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:

networks:
  ws-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
