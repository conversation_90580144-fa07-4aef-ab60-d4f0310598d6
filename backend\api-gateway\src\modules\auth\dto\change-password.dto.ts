import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength, Matches } from 'class-validator';

export class ChangePasswordDto {
  @ApiProperty({
    description: 'كلمة المرور الحالية',
    example: 'CurrentPassword123!',
  })
  @IsString({ message: 'كلمة المرور الحالية يجب أن تكون نص' })
  @IsNotEmpty({ message: 'كلمة المرور الحالية مطلوبة' })
  currentPassword: string;

  @ApiProperty({
    description: 'كلمة المرور الجديدة',
    example: 'NewSecurePassword123!',
    minLength: 8,
  })
  @IsString({ message: 'كلمة المرور الجديدة يجب أن تكون نص' })
  @IsNotEmpty({ message: 'كلمة المرور الجديدة مطلوبة' })
  @MinLength(8, { message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    {
      message: 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص',
    },
  )
  newPassword: string;
}
