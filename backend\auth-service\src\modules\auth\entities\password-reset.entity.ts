import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';

@Entity('password_resets')
@Index(['userId', 'createdAt'])
@Index(['token'], { unique: true })
@Index(['expiresAt'])
@Index(['isUsed'])
export class PasswordReset {
  @ApiProperty({ description: 'معرف طلب إعادة تعيين كلمة المرور' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'معرف المستخدم' })
  @Column('uuid')
  @Index()
  userId: string;

  @ApiProperty({ description: 'رمز إعادة التعيين المشفر' })
  @Column('text')
  @Index()
  token: string;

  @ApiProperty({ description: 'تاريخ انتهاء صلاحية الرمز' })
  @Column('timestamp')
  @Index()
  expiresAt: Date;

  @ApiProperty({ description: 'هل تم استخدام الرمز' })
  @Column('boolean', { default: false })
  @Index()
  isUsed: boolean;

  @ApiProperty({ description: 'تاريخ الاستخدام', required: false })
  @Column('timestamp', { nullable: true })
  usedAt?: Date;

  @ApiProperty({ description: 'عنوان IP الذي طلب إعادة التعيين', required: false })
  @Column({ length: 45, nullable: true })
  ipAddress?: string;

  @ApiProperty({ description: 'معلومات المتصفح', required: false })
  @Column('text', { nullable: true })
  userAgent?: string;

  @ApiProperty({ description: 'عنوان IP الذي استخدم الرمز', required: false })
  @Column({ length: 45, nullable: true })
  usedFromIp?: string;

  @ApiProperty({ description: 'معلومات المتصفح عند الاستخدام', required: false })
  @Column('text', { nullable: true })
  usedFromUserAgent?: string;

  @ApiProperty({ description: 'عدد محاولات الاستخدام' })
  @Column('int', { default: 0 })
  attemptCount: number;

  @ApiProperty({ description: 'آخر محاولة استخدام', required: false })
  @Column('timestamp', { nullable: true })
  lastAttemptAt?: Date;

  @ApiProperty({ description: 'بيانات وصفية إضافية', required: false })
  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'تاريخ آخر تحديث' })
  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, user => user.passwordResets)
  @JoinColumn({ name: 'userId' })
  user: User;

  // Helper methods
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  isValid(): boolean {
    return !this.isUsed && !this.isExpired();
  }

  markAsUsed(ipAddress?: string, userAgent?: string): void {
    this.isUsed = true;
    this.usedAt = new Date();
    this.usedFromIp = ipAddress;
    this.usedFromUserAgent = userAgent;
  }

  recordAttempt(): void {
    this.attemptCount++;
    this.lastAttemptAt = new Date();
  }

  getTimeUntilExpiry(): number {
    if (this.isExpired()) return 0;
    return this.expiresAt.getTime() - Date.now();
  }

  getTimeUntilExpiryMinutes(): number {
    return Math.ceil(this.getTimeUntilExpiry() / (1000 * 60));
  }

  wasUsedFromSameLocation(): boolean {
    if (!this.ipAddress || !this.usedFromIp) return false;
    return this.ipAddress === this.usedFromIp;
  }

  getUsageInfo(): any {
    return {
      id: this.id,
      isUsed: this.isUsed,
      isExpired: this.isExpired(),
      isValid: this.isValid(),
      createdAt: this.createdAt,
      expiresAt: this.expiresAt,
      usedAt: this.usedAt,
      timeUntilExpiryMinutes: this.getTimeUntilExpiryMinutes(),
      attemptCount: this.attemptCount,
      lastAttemptAt: this.lastAttemptAt,
      wasUsedFromSameLocation: this.wasUsedFromSameLocation(),
    };
  }
}

@Entity('password_reset_attempts')
@Index(['userId', 'createdAt'])
@Index(['ipAddress', 'createdAt'])
@Index(['success'])
export class PasswordResetAttempt {
  @ApiProperty({ description: 'معرف محاولة إعادة التعيين' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'معرف المستخدم', required: false })
  @Column('uuid', { nullable: true })
  userId?: string;

  @ApiProperty({ description: 'البريد الإلكتروني المستخدم' })
  @Column({ length: 255 })
  email: string;

  @ApiProperty({ description: 'عنوان IP' })
  @Column({ length: 45 })
  @Index()
  ipAddress: string;

  @ApiProperty({ description: 'معلومات المتصفح', required: false })
  @Column('text', { nullable: true })
  userAgent?: string;

  @ApiProperty({ description: 'هل نجحت المحاولة' })
  @Column('boolean')
  @Index()
  success: boolean;

  @ApiProperty({ description: 'سبب الفشل', required: false })
  @Column('text', { nullable: true })
  failureReason?: string;

  @ApiProperty({ description: 'معرف طلب إعادة التعيين المرتبط', required: false })
  @Column('uuid', { nullable: true })
  passwordResetId?: string;

  @ApiProperty({ description: 'بيانات إضافية', required: false })
  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user?: User;

  @ManyToOne(() => PasswordReset)
  @JoinColumn({ name: 'passwordResetId' })
  passwordReset?: PasswordReset;
}
