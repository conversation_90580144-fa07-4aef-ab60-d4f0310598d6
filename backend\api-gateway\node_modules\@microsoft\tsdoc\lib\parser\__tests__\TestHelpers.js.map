{"version": 3, "file": "TestHelpers.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/TestHelpers.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,OAAO,EAAE,YAAY,EAAiC,YAAY,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAEpG,OAAO,EAAE,kBAAkB,EAAE,MAAM,wCAAwC,CAAC;AAC5E,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAgB9D;IAAA;IA8JA,CAAC;IA7JC;;OAEG;IACW,0BAAc,GAA5B,UAA6B,IAAe,EAAE,KAAgB;QAC5D,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAM,WAAW,GAAa,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC7D,IAAM,YAAY,GAAa,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC9D,IAAM,YAAY,GAAa,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE9D,IAAM,MAAM,GAAW,IAAI,CAAC,MAAM,CAAC;QAEnC,IAAI,IAAI,GAAW,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,GAAW,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;YAC7B,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;gBACzB,IAAI,IAAI,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC9D,EAAE,CAAC,CAAC;YACN,CAAC;YACD,IAAI,IAAI,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC/D,EAAE,CAAC,CAAC;YACJ,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;gBACrB,IAAI,IAAI,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC9D,EAAE,CAAC,CAAC;YACN,CAAC;YACD,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;gBACnB,IAAI,IAAI,GAAG,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;gBAC/D,EAAE,CAAC,CAAC;gBACJ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACpB,IAAI,IAAI,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;oBAC9D,EAAE,CAAC,CAAC;gBACN,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACW,sBAAU,GAAxB,UAAyB,CAAS;QAChC,OAAO,CAAC;aACL,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;aACpB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACW,2CAA+B,GAA7C,UAA8C,MAAc,EAAE,MAA2B;QACvF,IAAM,aAAa,GAAuB,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,IAAI,kBAAkB,EAAE,CAAC;QAE7E,oFAAoF;QACpF,aAAa,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEpD,IAAM,WAAW,GAAgB,IAAI,WAAW,CAAC,aAAa,CAAC,CAAC;QAChE,IAAM,aAAa,GAAkB,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAErE,MAAM,CAAC;YACL,MAAM,EAAE,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,EAApC,CAAoC,CAAC;YAC3E,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAC,OAAO,IAAK,OAAA,OAAO,CAAC,IAAI,EAAZ,CAAY,CAAC;YACtE,KAAK,EAAE,WAAW,CAAC,kBAAkB,CAAC,aAAa,CAAC,UAAU,CAAC;YAC/D,IAAI,EAAE,IAAI,CAAC,6BAA6B,CAAC,aAAa,CAAC;SACxD,CAAC,CAAC,eAAe,EAAE,CAAC;QAErB,WAAW,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACW,2CAA+B,GAA7C,UACE,MAAc,EACd,aAAkC;QAElC,IAAM,WAAW,GAAgB,IAAI,WAAW,CAAC,aAAa,CAAC,CAAC;QAChE,IAAM,aAAa,GAAkB,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACrE,IAAM,UAAU,GAAe,aAAa,CAAC,UAAU,CAAC;QAExD,MAAM,CAAC;YACL,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,EAApC,CAAoC,CAAC;YAC/E,QAAQ,EAAE,IAAI,CAAC,6BAA6B,CAAC,aAAa,CAAC;YAC3D,kBAAkB,EAAE,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC;YAC7E,gBAAgB,EAAE,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,YAAY,CAAC;YACzE,uBAAuB,EAAE,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC;YAClF,mBAAmB,EAAE,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,eAAe,CAAC;YAC/E,eAAe,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAjC,CAAiC,CAAC;YACvF,mBAAmB,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAjC,CAAiC,CAAC;YAC/F,gBAAgB,EAAE,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,YAAY,CAAC;YACzE,gBAAgB,EAAE,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAjC,CAAiC,CAAC;YACvF,iBAAiB,EAAE,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,aAAa,CAAC;YAC3E,gBAAgB,EAAE,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAjC,CAAiC,CAAC;YAC/F,eAAe,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAC,OAAO,IAAK,OAAA,OAAO,CAAC,IAAI,EAAZ,CAAY,CAAC;SAC3E,CAAC,CAAC,eAAe,EAAE,CAAC;QAErB,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACW,8BAAkB,GAAhC,UAAiC,OAA4B;QAC3D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAM,IAAI,GAAkB;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,CAAC;QAEF,IAAI,OAAO,YAAY,UAAU,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC;YAExC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,OAAO,YAAY,YAAY,EAAE,CAAC;YACpC,IAAM,YAAY,GAAiB,OAAuB,CAAC;YAC3D,IAAI,YAAY,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC3C,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,IAAI,OAAO,YAAY,YAAY,EAAE,CAAC;YACpC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACjE,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9E,IAAI,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBACzC,mFAAmF;gBACnF,qBAAqB;gBACrB,IAAI,CAAC,2BAA2B;oBAC9B,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAChG,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAE,EAAlC,CAAkC,CAAC,CAAC;QACtF,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEc,yCAA6B,GAA5C,UAA6C,aAA4B;QACvE,IAAM,oBAAoB,GAAyB,IAAI,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC3F,OAAO,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,CAAC,CAAC;IACzF,CAAC;IACH,kBAAC;AAAD,CAAC,AA9JD,IA8JC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { TSDocParser } from '../TSDocParser';\r\nimport type { TextRange } from '../TextRange';\r\nimport { DocErrorText, type DocNode, type DocComment, DocPlainText, DocExcerpt } from '../../nodes';\r\nimport type { ParserContext } from '../ParserContext';\r\nimport { TSDocConfiguration } from '../../configuration/TSDocConfiguration';\r\nimport { TokenCoverageChecker } from './TokenCoverageChecker';\r\n\r\ninterface ISnapshotItem {\r\n  kind: string;\r\n  errorMessage?: string;\r\n  errorLocation?: string;\r\n  errorLocationPrecedingToken?: string;\r\n  nodeExcerpt?: string;\r\n  nodeSpacing?: string;\r\n\r\n  // If it's a DocPlainText node and the plain text is different from the excerpt,\r\n  // this shows the DocPlainText.text\r\n  nodePlainText?: string;\r\n  nodes?: ISnapshotItem[];\r\n}\r\n\r\nexport class TestHelpers {\r\n  /**\r\n   * Pretty print a line with \"<\" and \">\" markers to indicate a text range.\r\n   */\r\n  public static formatLineSpan(line: TextRange, range: TextRange): string {\r\n    if (range.pos < line.pos || range.end > line.end) {\r\n      throw new Error('Range must fall within the associated line');\r\n    }\r\n\r\n    const paddedSpace: string[] = ['', ' ', '  ', '   ', '    '];\r\n    const paddedLArrow: string[] = ['', '>', ' >', '  >', '   >'];\r\n    const paddedRArrow: string[] = ['', '<', '< ', '<  ', '<   '];\r\n\r\n    const buffer: string = line.buffer;\r\n\r\n    let span: string = '';\r\n    if (line.end > 0) {\r\n      let i: number = line.pos - 1;\r\n      while (i < range.pos - 1) {\r\n        span += paddedSpace[TestHelpers.getEscaped(buffer[i]).length];\r\n        ++i;\r\n      }\r\n      span += paddedLArrow[TestHelpers.getEscaped(buffer[i]).length];\r\n      ++i;\r\n      while (i < range.end) {\r\n        span += paddedSpace[TestHelpers.getEscaped(buffer[i]).length];\r\n        ++i;\r\n      }\r\n      if (i === line.end) {\r\n        span += '<';\r\n      } else {\r\n        span += paddedRArrow[TestHelpers.getEscaped(buffer[i]).length];\r\n        ++i;\r\n        while (i < line.end) {\r\n          span += paddedSpace[TestHelpers.getEscaped(buffer[i]).length];\r\n          ++i;\r\n        }\r\n      }\r\n    }\r\n    return span;\r\n  }\r\n\r\n  /**\r\n   * Workaround various characters that get ugly escapes in Jest snapshots\r\n   */\r\n  public static getEscaped(s: string): string {\r\n    return s\r\n      .replace(/\\n/g, '[n]')\r\n      .replace(/\\r/g, '[r]')\r\n      .replace(/\\t/g, '[t]')\r\n      .replace(/\\f/g, '[f]')\r\n      .replace(/\\\\/g, '[b]')\r\n      .replace(/\\\"/g, '[q]')\r\n      .replace(/`/g, '[c]')\r\n      .replace(/\\</g, '[<]')\r\n      .replace(/\\>/g, '[>]');\r\n  }\r\n\r\n  /**\r\n   * Main harness for tests under `./parser/*`.\r\n   */\r\n  public static parseAndMatchNodeParserSnapshot(buffer: string, config?: TSDocConfiguration): void {\r\n    const configuration: TSDocConfiguration = config ?? new TSDocConfiguration();\r\n\r\n    // For the parser tests, we use lots of custom tags without bothering to define them\r\n    configuration.validation.ignoreUndefinedTags = true;\r\n\r\n    const tsdocParser: TSDocParser = new TSDocParser(configuration);\r\n    const parserContext: ParserContext = tsdocParser.parseString(buffer);\r\n\r\n    expect({\r\n      buffer: TestHelpers.getEscaped(buffer),\r\n      lines: parserContext.lines.map((x) => TestHelpers.getEscaped(x.toString())),\r\n      logMessages: parserContext.log.messages.map((message) => message.text),\r\n      nodes: TestHelpers.getDocNodeSnapshot(parserContext.docComment),\r\n      gaps: this._getTokenCoverageGapsSnapshot(parserContext)\r\n    }).toMatchSnapshot();\r\n\r\n    TestHelpers._getTokenCoverageGapsSnapshot(parserContext);\r\n  }\r\n\r\n  /**\r\n   * Main harness for tests under `./details/*`.\r\n   */\r\n  public static parseAndMatchDocCommentSnapshot(\r\n    buffer: string,\r\n    configuration?: TSDocConfiguration\r\n  ): ParserContext {\r\n    const tsdocParser: TSDocParser = new TSDocParser(configuration);\r\n    const parserContext: ParserContext = tsdocParser.parseString(buffer);\r\n    const docComment: DocComment = parserContext.docComment;\r\n\r\n    expect({\r\n      s00_lines: parserContext.lines.map((x) => TestHelpers.getEscaped(x.toString())),\r\n      s01_gaps: this._getTokenCoverageGapsSnapshot(parserContext),\r\n      s02_summarySection: TestHelpers.getDocNodeSnapshot(docComment.summarySection),\r\n      s03_remarksBlock: TestHelpers.getDocNodeSnapshot(docComment.remarksBlock),\r\n      s04_privateRemarksBlock: TestHelpers.getDocNodeSnapshot(docComment.privateRemarks),\r\n      s05_deprecatedBlock: TestHelpers.getDocNodeSnapshot(docComment.deprecatedBlock),\r\n      s06_paramBlocks: docComment.params.blocks.map((x) => TestHelpers.getDocNodeSnapshot(x)),\r\n      s07_typeParamBlocks: docComment.typeParams.blocks.map((x) => TestHelpers.getDocNodeSnapshot(x)),\r\n      s08_returnsBlock: TestHelpers.getDocNodeSnapshot(docComment.returnsBlock),\r\n      s09_customBlocks: docComment.customBlocks.map((x) => TestHelpers.getDocNodeSnapshot(x)),\r\n      s10_inheritDocTag: TestHelpers.getDocNodeSnapshot(docComment.inheritDocTag),\r\n      s11_modifierTags: docComment.modifierTagSet.nodes.map((x) => TestHelpers.getDocNodeSnapshot(x)),\r\n      s12_logMessages: parserContext.log.messages.map((message) => message.text)\r\n    }).toMatchSnapshot();\r\n\r\n    return parserContext;\r\n  }\r\n\r\n  /**\r\n   * Render a nice Jest snapshot object for a DocNode tree.\r\n   */\r\n  public static getDocNodeSnapshot(docNode: DocNode | undefined): ISnapshotItem | undefined {\r\n    if (!docNode) {\r\n      return undefined;\r\n    }\r\n\r\n    const item: ISnapshotItem = {\r\n      kind: docNode.kind\r\n    };\r\n\r\n    if (docNode instanceof DocExcerpt) {\r\n      item.kind += ': ' + docNode.excerptKind;\r\n\r\n      item.nodeExcerpt = TestHelpers.getEscaped(docNode.content.toString());\r\n    }\r\n\r\n    if (docNode instanceof DocPlainText) {\r\n      const docPlainText: DocPlainText = docNode as DocPlainText;\r\n      if (docPlainText.textExcerpt === undefined) {\r\n        item.nodePlainText = TestHelpers.getEscaped(docPlainText.text);\r\n      }\r\n    }\r\n\r\n    if (docNode instanceof DocErrorText) {\r\n      item.errorMessage = TestHelpers.getEscaped(docNode.errorMessage);\r\n      item.errorLocation = TestHelpers.getEscaped(docNode.errorLocation.toString());\r\n      if (docNode.errorLocation.startIndex > 0) {\r\n        // Show the preceding token to provide some context (e.g. is this the opening quote\r\n        // or closing quote?)\r\n        item.errorLocationPrecedingToken =\r\n          docNode.errorLocation.parserContext.tokens[docNode.errorLocation.startIndex - 1].toString();\r\n      }\r\n    }\r\n\r\n    if (docNode.getChildNodes().length > 0) {\r\n      item.nodes = docNode.getChildNodes().map((x) => TestHelpers.getDocNodeSnapshot(x)!);\r\n    }\r\n\r\n    return item;\r\n  }\r\n\r\n  private static _getTokenCoverageGapsSnapshot(parserContext: ParserContext): string[] {\r\n    const tokenCoverageChecker: TokenCoverageChecker = new TokenCoverageChecker(parserContext);\r\n    return tokenCoverageChecker.getGaps(parserContext.docComment).map((x) => x.toString());\r\n  }\r\n}\r\n"]}