"""
Feature Engineering Service
===========================
خدمة هندسة الميزات المتقدمة
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
from dataclasses import dataclass
import math

from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder # type: ignore
from sklearn.feature_extraction.text import TfidfVectorizer # type: ignore

logger = logging.getLogger(__name__)


@dataclass
class FeatureConfig:
    """إعدادات الميزات"""
    name: str
    feature_type: str  # numerical, categorical, text, temporal
    scaling_method: str  # standard, minmax, none
    encoding_method: str  # onehot, label, none
    aggregation_methods: List[str]  # mean, sum, count, std, etc.


class FeatureEngineer:
    """مهندس الميزات المتقدم"""
    
    def __init__(self):
        # Scalers and encoders
        self.scalers = {}
        self.encoders = {}
        self.vectorizers = {}
        
        # Feature configurations
        self.feature_configs = self._load_feature_configs()
        
        # Statistics
        self.total_features_extracted = 0
        self.feature_extraction_time = 0
        
    async def initialize(self):
        """تهيئة مهندس الميزات"""
        try:
            logger.info("🔧 Initializing Feature Engineer...")
            
            # Initialize scalers and encoders
            await self._initialize_transformers()
            
            logger.info("✅ Feature Engineer initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Feature Engineer: {e}")
            raise
    
    def _load_feature_configs(self) -> Dict[str, FeatureConfig]:
        """تحميل إعدادات الميزات"""
        return {
            "amount": FeatureConfig(
                name="amount",
                feature_type="numerical",
                scaling_method="standard",
                encoding_method="none",
                aggregation_methods=["mean", "sum", "std", "min", "max"]
            ),
            "timestamp": FeatureConfig(
                name="timestamp",
                feature_type="temporal",
                scaling_method="none",
                encoding_method="none",
                aggregation_methods=["count", "frequency"]
            ),
            "category": FeatureConfig(
                name="category",
                feature_type="categorical",
                scaling_method="none",
                encoding_method="onehot",
                aggregation_methods=["count", "mode"]
            ),
            "description": FeatureConfig(
                name="description",
                feature_type="text",
                scaling_method="none",
                encoding_method="tfidf",
                aggregation_methods=["count", "length"]
            )
        }
    
    async def _initialize_transformers(self):
        """تهيئة المحولات"""
        try:
            # Initialize scalers
            self.scalers["standard"] = StandardScaler()
            self.scalers["minmax"] = MinMaxScaler()
            
            # Initialize encoders
            self.encoders["label"] = LabelEncoder()
            
            # Initialize vectorizers
            self.vectorizers["tfidf"] = TfidfVectorizer(
                max_features=1000,
                stop_words=None,  # Add Arabic stop words if needed
                ngram_range=(1, 2)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize transformers: {e}")
            raise
    
    async def extract_transaction_features(
        self,
        transaction_data: Any,
        user_profile: Dict[str, Any],
        merchant_profile: Dict[str, Any]
    ) -> Dict[str, float]:
        """استخراج ميزات المعاملة"""
        try:
            features = {}
            
            # Basic transaction features
            features["amount"] = float(transaction_data.amount)
            features["log_amount"] = math.log(max(transaction_data.amount, 1))
            features["amount_squared"] = transaction_data.amount ** 2
            
            # Currency features
            features["is_local_currency"] = 1.0 if transaction_data.currency == "SAR" else 0.0
            features["is_usd"] = 1.0 if transaction_data.currency == "USD" else 0.0
            features["is_eur"] = 1.0 if transaction_data.currency == "EUR" else 0.0
            
            # Merchant features
            features["merchant_risk_score"] = merchant_profile.get("risk_score", 0.5)
            features["merchant_transaction_volume"] = merchant_profile.get("transaction_volume", 0)
            features["merchant_chargeback_rate"] = merchant_profile.get("chargeback_rate", 0.01)
            
            # User features
            features["user_age"] = user_profile.get("age", 30)
            features["user_account_age_days"] = user_profile.get("account_age_days", 365)
            features["user_kyc_level"] = user_profile.get("kyc_level", 1)
            features["user_avg_transaction_amount"] = user_profile.get("avg_transaction_amount", 1000)
            
            # Relative features
            if user_profile.get("avg_transaction_amount", 0) > 0:
                features["amount_vs_user_avg"] = transaction_data.amount / user_profile["avg_transaction_amount"]
            else:
                features["amount_vs_user_avg"] = 1.0
            
            # Location features
            location = transaction_data.location
            features["has_location"] = 1.0 if location else 0.0
            features["is_domestic"] = 1.0 if location.get("country") == "SA" else 0.0
            
            # Device features
            device_info = transaction_data.device_info
            features["is_mobile"] = 1.0 if device_info.get("type") == "mobile" else 0.0
            features["is_new_device"] = 1.0 if device_info.get("is_new", False) else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Transaction feature extraction failed: {e}")
            return {}
    
    async def extract_temporal_features(self, timestamp: datetime) -> Dict[str, float]:
        """استخراج الميزات الزمنية"""
        try:
            features = {}
            
            # Basic time features
            features["hour"] = float(timestamp.hour)
            features["day_of_week"] = float(timestamp.weekday())
            features["day_of_month"] = float(timestamp.day)
            features["month"] = float(timestamp.month)
            features["quarter"] = float((timestamp.month - 1) // 3 + 1)
            
            # Cyclical encoding
            features["hour_sin"] = math.sin(2 * math.pi * timestamp.hour / 24)
            features["hour_cos"] = math.cos(2 * math.pi * timestamp.hour / 24)
            features["day_sin"] = math.sin(2 * math.pi * timestamp.weekday() / 7)
            features["day_cos"] = math.cos(2 * math.pi * timestamp.weekday() / 7)
            features["month_sin"] = math.sin(2 * math.pi * timestamp.month / 12)
            features["month_cos"] = math.cos(2 * math.pi * timestamp.month / 12)
            
            # Business time features
            features["is_weekend"] = 1.0 if timestamp.weekday() >= 5 else 0.0
            features["is_business_hours"] = 1.0 if 9 <= timestamp.hour <= 17 else 0.0
            features["is_night_time"] = 1.0 if timestamp.hour < 6 or timestamp.hour > 22 else 0.0
            
            # Holiday features (simplified)
            features["is_ramadan"] = self._is_ramadan(timestamp)
            features["is_hajj_season"] = self._is_hajj_season(timestamp)
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Temporal feature extraction failed: {e}")
            return {}
    
    async def extract_behavioral_features(
        self,
        user_id: str,
        current_timestamp: datetime
    ) -> Dict[str, float]:
        """استخراج الميزات السلوكية"""
        try:
            features = {}
            
            # This would typically query the database for user's transaction history
            # For now, we'll simulate some behavioral features
            
            # Transaction frequency features
            features["transactions_last_hour"] = 0.0  # Would be calculated from DB
            features["transactions_last_day"] = 5.0   # Simulated
            features["transactions_last_week"] = 25.0  # Simulated
            features["transactions_last_month"] = 100.0  # Simulated
            
            # Amount patterns
            features["avg_amount_last_week"] = 1500.0  # Simulated
            features["std_amount_last_week"] = 500.0   # Simulated
            features["max_amount_last_month"] = 5000.0  # Simulated
            
            # Time patterns
            features["most_active_hour"] = 14.0  # 2 PM
            features["most_active_day"] = 2.0    # Tuesday
            features["time_since_last_transaction"] = 2.5  # Hours
            
            # Channel preferences
            features["mobile_usage_ratio"] = 0.8  # 80% mobile usage
            features["web_usage_ratio"] = 0.2     # 20% web usage
            
            # Merchant patterns
            features["unique_merchants_last_month"] = 15.0
            features["repeat_merchant_ratio"] = 0.6  # 60% repeat merchants
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Behavioral feature extraction failed: {e}")
            return {}
    
    async def extract_velocity_features(
        self,
        user_id: str,
        current_timestamp: datetime
    ) -> Dict[str, float]:
        """استخراج ميزات السرعة والتكرار"""
        try:
            features = {}
            
            # Transaction velocity (transactions per time period)
            features["velocity_1h"] = 0.0   # Transactions in last hour
            features["velocity_24h"] = 3.0  # Transactions in last 24 hours
            features["velocity_7d"] = 20.0  # Transactions in last 7 days
            
            # Amount velocity (total amount per time period)
            features["amount_velocity_1h"] = 0.0     # Amount in last hour
            features["amount_velocity_24h"] = 3000.0 # Amount in last 24 hours
            features["amount_velocity_7d"] = 15000.0 # Amount in last 7 days
            
            # Unique merchant velocity
            features["merchant_velocity_24h"] = 2.0  # Unique merchants in 24h
            features["merchant_velocity_7d"] = 8.0   # Unique merchants in 7d
            
            # Location velocity
            features["location_velocity_24h"] = 1.0  # Unique locations in 24h
            features["location_velocity_7d"] = 3.0   # Unique locations in 7d
            
            # Device velocity
            features["device_velocity_24h"] = 1.0    # Unique devices in 24h
            features["device_velocity_7d"] = 2.0     # Unique devices in 7d
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Velocity feature extraction failed: {e}")
            return {}
    
    async def extract_demographic_features(self, demographic_data: Dict[str, Any]) -> Dict[str, float]:
        """استخراج الميزات الديموغرافية"""
        try:
            features = {}
            
            # Age features
            age = demographic_data.get("age", 30)
            features["age"] = float(age)
            features["age_group_young"] = 1.0 if age < 25 else 0.0
            features["age_group_middle"] = 1.0 if 25 <= age <= 45 else 0.0
            features["age_group_senior"] = 1.0 if age > 45 else 0.0
            
            # Gender features
            gender = demographic_data.get("gender", "unknown")
            features["is_male"] = 1.0 if gender == "male" else 0.0
            features["is_female"] = 1.0 if gender == "female" else 0.0
            
            # Location features
            country = demographic_data.get("country", "SA")
            features["is_saudi"] = 1.0 if country == "SA" else 0.0
            features["is_gcc"] = 1.0 if country in ["SA", "AE", "KW", "QA", "BH", "OM"] else 0.0
            
            # Income features
            income = demographic_data.get("income", 50000)
            features["income"] = float(income)
            features["log_income"] = math.log(max(income, 1))
            features["income_bracket_low"] = 1.0 if income < 30000 else 0.0
            features["income_bracket_medium"] = 1.0 if 30000 <= income <= 100000 else 0.0
            features["income_bracket_high"] = 1.0 if income > 100000 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Demographic feature extraction failed: {e}")
            return {}
    
    async def extract_financial_features(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """استخراج الميزات المالية"""
        try:
            features = {}
            
            # Credit score features
            credit_score = financial_data.get("credit_score", 700)
            features["credit_score"] = float(credit_score)
            features["credit_score_normalized"] = credit_score / 850.0  # Normalize to 0-1
            features["is_excellent_credit"] = 1.0 if credit_score >= 750 else 0.0
            features["is_good_credit"] = 1.0 if 650 <= credit_score < 750 else 0.0
            features["is_poor_credit"] = 1.0 if credit_score < 650 else 0.0
            
            # Income and debt features
            income = financial_data.get("income", 50000)
            debt = financial_data.get("total_debt", 10000)
            features["debt_to_income_ratio"] = debt / max(income, 1)
            features["has_high_debt"] = 1.0 if features["debt_to_income_ratio"] > 0.4 else 0.0
            
            # Account features
            features["num_bank_accounts"] = float(financial_data.get("num_accounts", 2))
            features["num_credit_cards"] = float(financial_data.get("num_credit_cards", 1))
            features["total_credit_limit"] = float(financial_data.get("total_credit_limit", 20000))
            
            # Investment features
            features["has_investments"] = 1.0 if financial_data.get("has_investments", False) else 0.0
            features["investment_value"] = float(financial_data.get("investment_value", 0))
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Financial feature extraction failed: {e}")
            return {}
    
    async def extract_kyc_features(self, kyc_data: Dict[str, Any]) -> Dict[str, float]:
        """استخراج ميزات KYC"""
        try:
            features = {}
            
            # KYC level
            kyc_level = kyc_data.get("level", 1)
            features["kyc_level"] = float(kyc_level)
            features["is_kyc_basic"] = 1.0 if kyc_level == 1 else 0.0
            features["is_kyc_enhanced"] = 1.0 if kyc_level == 2 else 0.0
            features["is_kyc_premium"] = 1.0 if kyc_level >= 3 else 0.0
            
            # Verification status
            features["is_identity_verified"] = 1.0 if kyc_data.get("identity_verified", False) else 0.0
            features["is_address_verified"] = 1.0 if kyc_data.get("address_verified", False) else 0.0
            features["is_phone_verified"] = 1.0 if kyc_data.get("phone_verified", False) else 0.0
            features["is_email_verified"] = 1.0 if kyc_data.get("email_verified", False) else 0.0
            
            # Document features
            features["has_national_id"] = 1.0 if kyc_data.get("has_national_id", False) else 0.0
            features["has_passport"] = 1.0 if kyc_data.get("has_passport", False) else 0.0
            features["has_driving_license"] = 1.0 if kyc_data.get("has_driving_license", False) else 0.0
            
            # Verification completeness
            total_verifications = sum([
                features["is_identity_verified"],
                features["is_address_verified"],
                features["is_phone_verified"],
                features["is_email_verified"]
            ])
            features["verification_completeness"] = total_verifications / 4.0
            
            return features
            
        except Exception as e:
            logger.error(f"❌ KYC feature extraction failed: {e}")
            return {}
    
    async def extract_compliance_features(self, compliance_status: Dict[str, Any]) -> Dict[str, float]:
        """استخراج ميزات الامتثال"""
        try:
            features = {}
            
            # AML status
            aml_status = compliance_status.get("aml_status", "clear")
            features["aml_clear"] = 1.0 if aml_status == "clear" else 0.0
            features["aml_flagged"] = 1.0 if aml_status == "flagged" else 0.0
            features["aml_under_review"] = 1.0 if aml_status == "under_review" else 0.0
            
            # Sanctions screening
            features["is_sanctioned"] = 1.0 if compliance_status.get("is_sanctioned", False) else 0.0
            features["sanctions_score"] = float(compliance_status.get("sanctions_score", 0.0))
            
            # PEP (Politically Exposed Person) status
            features["is_pep"] = 1.0 if compliance_status.get("is_pep", False) else 0.0
            features["pep_risk_level"] = float(compliance_status.get("pep_risk_level", 0))
            
            # Compliance history
            features["compliance_violations"] = float(compliance_status.get("violations_count", 0))
            features["has_violations"] = 1.0 if features["compliance_violations"] > 0 else 0.0
            
            # Risk rating
            risk_rating = compliance_status.get("risk_rating", "medium")
            features["risk_low"] = 1.0 if risk_rating == "low" else 0.0
            features["risk_medium"] = 1.0 if risk_rating == "medium" else 0.0
            features["risk_high"] = 1.0 if risk_rating == "high" else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Compliance feature extraction failed: {e}")
            return {}
    
    async def extract_user_recommendation_features(
        self,
        user_profile: Dict[str, Any],
        transaction_history: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """استخراج ميزات التوصيات للمستخدم"""
        try:
            features = {}
            
            # Basic user features
            features.update(await self.extract_demographic_features(user_profile.get("demographics", {})))
            features.update(await self.extract_financial_features(user_profile.get("financial", {})))
            
            # Transaction behavior features
            if transaction_history:
                amounts = [t.get("amount", 0) for t in transaction_history]
                features["avg_transaction_amount"] = np.mean(amounts) if amounts else 0
                features["std_transaction_amount"] = np.std(amounts) if amounts else 0
                features["total_transaction_amount"] = sum(amounts)
                features["transaction_count"] = len(transaction_history)
                
                # Category preferences
                categories = [t.get("category", "unknown") for t in transaction_history]
                category_counts = {}
                for cat in categories:
                    category_counts[cat] = category_counts.get(cat, 0) + 1
                
                # Top 5 categories as features
                top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:5]
                for i, (cat, count) in enumerate(top_categories):
                    features[f"top_category_{i+1}_count"] = count
                    features[f"top_category_{i+1}_ratio"] = count / len(transaction_history)
            
            return features
            
        except Exception as e:
            logger.error(f"❌ User recommendation feature extraction failed: {e}")
            return {}
    
    # Helper methods
    def _is_ramadan(self, timestamp: datetime) -> float:
        """فحص إذا كان التاريخ في رمضان (مبسط)"""
        # This is a simplified check - in production, use proper Islamic calendar
        # Ramadan typically falls in different months each year
        return 0.0  # Placeholder
    
    def _is_hajj_season(self, timestamp: datetime) -> float:
        """فحص إذا كان التاريخ في موسم الحج (مبسط)"""
        # Hajj is typically in the 12th month of Islamic calendar
        # This is a simplified check
        return 0.0  # Placeholder
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات مهندس الميزات"""
        return {
            "total_features_extracted": self.total_features_extracted,
            "average_extraction_time": self.feature_extraction_time / max(self.total_features_extracted, 1),
            "available_scalers": list(self.scalers.keys()),
            "available_encoders": list(self.encoders.keys()),
            "available_vectorizers": list(self.vectorizers.keys()),
            "feature_configs_count": len(self.feature_configs)
        }
