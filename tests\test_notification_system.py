"""
Notification System Tests
========================
اختبارات نظام الإشعارات الشاملة
"""

import pytest
import asyncio
from datetime import datetime, date, timedelta
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from notifications.notification_service import (
    NotificationService, NotificationRequest, NotificationRecipient,
    NotificationChannel, NotificationType, NotificationPriority, NotificationStatus
)
from notifications.template_manager import (
    TemplateManager, TemplateLanguage, TemplateVariable, TemplateValidationResult
)
from notifications.scheduler_service import (
    NotificationScheduler, ScheduleType, ScheduleStatus, ScheduledNotification
)
from notifications.analytics_service import (
    NotificationAnalytics, AnalyticsPeriod
)


class TestNotificationService:
    """اختبارات خدمة الإشعارات الأساسية"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def notification_service(self, mock_db_connection):
        """خدمة الإشعارات للاختبار"""
        db_connection, _ = mock_db_connection
        return NotificationService(db_connection)
    
    @pytest.fixture
    def sample_recipient(self):
        """مستقبل تجريبي"""
        return NotificationRecipient(
            user_id="user_test_001",
            email="<EMAIL>",
            phone="+966501234567",
            language="ar",
            timezone="Asia/Riyadh"
        )
    
    @pytest.fixture
    def sample_notification_request(self, sample_recipient):
        """طلب إشعار تجريبي"""
        return NotificationRequest(
            type=NotificationType.TRANSACTION_SUCCESS,
            channel=NotificationChannel.EMAIL,
            recipient=sample_recipient,
            priority=NotificationPriority.HIGH,
            data={
                "transaction_id": "txn_test_001",
                "amount": 1000.0,
                "currency": "SAR",
                "user_name": "أحمد محمد"
            }
        )
    
    @pytest.mark.asyncio
    async def test_send_notification_success(
        self, 
        notification_service, 
        sample_notification_request,
        mock_db_connection
    ):
        """اختبار إرسال إشعار بنجاح"""
        db_connection, conn = mock_db_connection
        
        # Mock template retrieval
        notification_service._get_template = AsyncMock(return_value=Mock(
            id="template_001",
            subject_template="تم التحويل بنجاح",
            body_template="مرحباً {user_name}, تم تنفيذ التحويل {transaction_id} بنجاح"
        ))
        
        # Mock template rendering
        notification_service._render_notification = AsyncMock(
            return_value=("تم التحويل بنجاح", "مرحباً أحمد محمد, تم تنفيذ التحويل txn_test_001 بنجاح")
        )
        
        # Mock storage and sending
        notification_service._store_notification = AsyncMock()
        notification_service._send_via_channel = AsyncMock(return_value=Mock(
            status=NotificationStatus.SENT,
            sent_at=datetime.now()
        ))
        
        # Send notification
        result = await notification_service.send_notification(sample_notification_request)
        
        # Assertions
        assert result.status == NotificationStatus.SENT
        assert result.sent_at is not None
        assert result.notification_id is not None
        
        # Verify method calls
        notification_service._get_template.assert_called_once()
        notification_service._render_notification.assert_called_once()
        notification_service._store_notification.assert_called_once()
        notification_service._send_via_channel.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_notification_no_template(
        self, 
        notification_service, 
        sample_notification_request
    ):
        """اختبار إرسال إشعار بدون قالب"""
        # Mock no template found
        notification_service._get_template = AsyncMock(return_value=None)
        
        # Send notification
        result = await notification_service.send_notification(sample_notification_request)
        
        # Assertions
        assert result.status == NotificationStatus.FAILED
        assert result.error_message is not None
        assert "No template found" in result.error_message
    
    @pytest.mark.asyncio
    async def test_send_bulk_notifications(
        self, 
        notification_service, 
        sample_recipient
    ):
        """اختبار إرسال إشعارات متعددة"""
        # Create multiple notification requests
        requests = []
        for i in range(5):
            request = NotificationRequest(
                type=NotificationType.TRANSACTION_SUCCESS,
                channel=NotificationChannel.EMAIL,
                recipient=sample_recipient,
                priority=NotificationPriority.MEDIUM,
                data={"transaction_id": f"txn_test_{i:03d}", "amount": 100.0 * (i + 1)}
            )
            requests.append(request)
        
        # Mock send_notification to return success for all
        notification_service.send_notification = AsyncMock(return_value=Mock(
            status=NotificationStatus.SENT,
            sent_at=datetime.now(),
            notification_id="test_notif_id"
        ))
        
        # Send bulk notifications
        results = await notification_service.send_bulk_notifications(requests)
        
        # Assertions
        assert len(results) == 5
        assert all(r.status == NotificationStatus.SENT for r in results)
        assert notification_service.send_notification.call_count == 5
    
    @pytest.mark.asyncio
    async def test_send_transaction_notification(
        self, 
        notification_service,
        mock_db_connection
    ):
        """اختبار إرسال إشعار معاملة"""
        db_connection, conn = mock_db_connection
        
        # Mock user details
        notification_service._get_user_details = AsyncMock(return_value={
            "full_name": "أحمد محمد",
            "email": "<EMAIL>",
            "phone": "+966501234567",
            "language": "ar"
        })
        
        # Mock send_notification
        notification_service.send_notification = AsyncMock(return_value=Mock(
            status=NotificationStatus.SENT,
            sent_at=datetime.now()
        ))
        
        # Send transaction notification
        results = await notification_service.send_transaction_notification(
            transaction_id="txn_test_001",
            user_id="user_test_001",
            transaction_type="transfer",
            amount=1000.0,
            currency="SAR",
            status="completed"
        )
        
        # Assertions
        assert len(results) == 2  # EMAIL and IN_APP by default
        assert all(r.status == NotificationStatus.SENT for r in results)
        
        # Verify user details were fetched
        notification_service._get_user_details.assert_called_once_with("user_test_001")
    
    @pytest.mark.asyncio
    async def test_send_security_alert(
        self, 
        notification_service
    ):
        """اختبار إرسال تنبيه أمني"""
        # Mock user details
        notification_service._get_user_details = AsyncMock(return_value={
            "full_name": "أحمد محمد",
            "email": "<EMAIL>",
            "phone": "+966501234567",
            "language": "ar"
        })
        
        # Mock send_notification
        notification_service.send_notification = AsyncMock(return_value=Mock(
            status=NotificationStatus.SENT,
            sent_at=datetime.now()
        ))
        
        # Send security alert
        results = await notification_service.send_security_alert(
            user_id="user_test_001",
            alert_type="محاولة دخول مشبوهة",
            details={"ip_address": "*************", "location": "الرياض"},
            priority=NotificationPriority.CRITICAL
        )
        
        # Assertions
        assert len(results) == 3  # EMAIL, SMS, IN_APP for security alerts
        assert all(r.status == NotificationStatus.SENT for r in results)
        
        # Verify all calls were made with CRITICAL priority
        calls = notification_service.send_notification.call_args_list
        for call in calls:
            request = call[0][0]  # First argument is the NotificationRequest
            assert request.priority == NotificationPriority.CRITICAL
    
    @pytest.mark.asyncio
    async def test_get_notification_statistics(
        self, 
        notification_service,
        mock_db_connection
    ):
        """اختبار الحصول على إحصائيات الإشعارات"""
        db_connection, conn = mock_db_connection
        
        # Mock statistics data
        stats_data = {
            'total_notifications': 1000,
            'sent_count': 950,
            'delivered_count': 900,
            'failed_count': 50,
            'pending_count': 0
        }
        
        channel_stats = [
            {'channel': 'email', 'count': 500, 'sent_count': 475, 'failed_count': 25},
            {'channel': 'sms', 'count': 300, 'sent_count': 285, 'failed_count': 15},
            {'channel': 'in_app', 'count': 200, 'sent_count': 190, 'failed_count': 10}
        ]
        
        type_stats = [
            {'notification_type': 'transaction_success', 'count': 600, 'sent_count': 570},
            {'notification_type': 'security_alert', 'count': 200, 'sent_count': 190},
            {'notification_type': 'system_maintenance', 'count': 200, 'sent_count': 190}
        ]
        
        conn.fetchrow.return_value = stats_data
        conn.fetch.side_effect = [channel_stats, type_stats]
        
        # Get statistics
        result = await notification_service.get_notification_statistics()
        
        # Assertions
        assert result['overview']['total_notifications'] == 1000
        assert result['overview']['delivery_rate'] == 90.0  # 900/1000 * 100
        assert result['overview']['failure_rate'] == 5.0   # 50/1000 * 100
        
        assert len(result['by_channel']) == 3
        assert result['by_channel'][0]['channel'] == 'email'
        assert result['by_channel'][0]['success_rate'] == 95.0  # 475/500 * 100
        
        assert len(result['by_type']) == 3
        assert result['by_type'][0]['type'] == 'transaction_success'


class TestTemplateManager:
    """اختبارات مدير القوالب"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def template_manager(self, mock_db_connection):
        """مدير القوالب للاختبار"""
        db_connection, _ = mock_db_connection
        return TemplateManager(db_connection)
    
    @pytest.fixture
    def sample_variables(self):
        """متغيرات تجريبية"""
        return [
            TemplateVariable(
                name="user_name",
                type="string",
                description="اسم المستخدم",
                required=True
            ),
            TemplateVariable(
                name="transaction_id",
                type="string",
                description="معرف المعاملة",
                required=True
            ),
            TemplateVariable(
                name="amount",
                type="number",
                description="المبلغ",
                required=True
            )
        ]
    
    @pytest.mark.asyncio
    async def test_create_template_success(
        self, 
        template_manager, 
        sample_variables,
        mock_db_connection
    ):
        """اختبار إنشاء قالب بنجاح"""
        db_connection, conn = mock_db_connection
        
        # Mock successful creation
        conn.fetchval.return_value = "template_test_001"
        
        # Mock validation
        template_manager.validate_template = Mock(return_value=TemplateValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            variables_found=["user_name", "transaction_id", "amount"],
            missing_variables=[]
        ))
        
        # Create template
        template_id = await template_manager.create_template(
            name="قالب اختبار",
            notification_type=NotificationType.TRANSACTION_SUCCESS,
            channel=NotificationChannel.EMAIL,
            language=TemplateLanguage.ARABIC,
            subject_template="تم التحويل بنجاح",
            body_template="مرحباً {user_name}, تم تنفيذ التحويل {transaction_id} بمبلغ {amount}",
            variables=sample_variables,
            created_by="test_user"
        )
        
        # Assertions
        assert template_id == "template_test_001"
        
        # Verify database calls
        assert conn.fetchval.call_count == 1  # Template insert
        assert conn.execute.call_count == 3  # Variable inserts
    
    def test_validate_template_success(self, template_manager, sample_variables):
        """اختبار التحقق من صحة القالب - نجح"""
        subject = "تم التحويل بنجاح"
        body = "مرحباً {user_name}, تم تنفيذ التحويل {transaction_id} بمبلغ {amount}"
        
        result = template_manager.validate_template(subject, body, sample_variables)
        
        # Assertions
        assert result.is_valid == True
        assert len(result.errors) == 0
        assert set(result.variables_found) == {"user_name", "transaction_id", "amount"}
        assert len(result.missing_variables) == 0
    
    def test_validate_template_missing_variables(self, template_manager, sample_variables):
        """اختبار التحقق من صحة القالب - متغيرات مفقودة"""
        subject = "تم التحويل بنجاح"
        body = "مرحباً {user_name}, تم تنفيذ التحويل {transaction_id}"  # Missing {amount}
        
        result = template_manager.validate_template(subject, body, sample_variables)
        
        # Assertions
        assert result.is_valid == False
        assert len(result.errors) > 0
        assert "Required variables not used in template" in result.errors[0]
        assert "amount" in result.errors[0]
    
    def test_validate_template_undefined_variables(self, template_manager, sample_variables):
        """اختبار التحقق من صحة القالب - متغيرات غير معرفة"""
        subject = "تم التحويل بنجاح"
        body = "مرحباً {user_name}, تم تنفيذ التحويل {transaction_id} بمبلغ {amount} في {undefined_var}"
        
        result = template_manager.validate_template(subject, body, sample_variables)
        
        # Assertions
        assert result.is_valid == False
        assert len(result.errors) > 0
        assert "Undefined variables in template" in result.errors[0]
        assert "undefined_var" in result.errors[0]
    
    def test_validate_template_malformed_variables(self, template_manager, sample_variables):
        """اختبار التحقق من صحة القالب - متغيرات مشوهة"""
        subject = "تم التحويل بنجاح"
        body = "مرحباً {user name}, تم تنفيذ التحويل {123invalid} بمبلغ {amount}"
        
        result = template_manager.validate_template(subject, body, sample_variables)
        
        # Assertions
        assert result.is_valid == False
        assert len(result.errors) > 0
        assert "Malformed variable names" in result.errors[0]
    
    @pytest.mark.asyncio
    async def test_preview_template(
        self, 
        template_manager,
        mock_db_connection
    ):
        """اختبار معاينة القالب"""
        db_connection, conn = mock_db_connection
        
        # Mock template data
        template_data = {
            "id": "template_test_001",
            "subject_template": "تم التحويل بنجاح",
            "body_template": "مرحباً {user_name}, تم تنفيذ التحويل {transaction_id} بمبلغ {amount} {currency}"
        }
        
        template_manager.get_template = AsyncMock(return_value=template_data)
        
        # Sample data for preview
        sample_data = {
            "user_name": "أحمد محمد",
            "transaction_id": "txn_test_001",
            "amount": "1000.00",
            "currency": "SAR"
        }
        
        # Preview template
        result = await template_manager.preview_template("template_test_001", sample_data)
        
        # Assertions
        assert result["subject"] == "تم التحويل بنجاح"
        assert "أحمد محمد" in result["body"]
        assert "txn_test_001" in result["body"]
        assert "1000.00" in result["body"]
        assert "SAR" in result["body"]
        assert result["template_id"] == "template_test_001"


class TestNotificationScheduler:
    """اختبارات جدولة الإشعارات"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def notification_scheduler(self, mock_db_connection):
        """جدولة الإشعارات للاختبار"""
        db_connection, _ = mock_db_connection
        notification_service = AsyncMock()
        return NotificationScheduler(db_connection, notification_service)
    
    @pytest.fixture
    def sample_notification_request(self):
        """طلب إشعار تجريبي للجدولة"""
        recipient = NotificationRecipient(
            user_id="user_test_001",
            email="<EMAIL>",
            phone="+966501234567"
        )
        
        return NotificationRequest(
            type=NotificationType.SYSTEM_MAINTENANCE,
            channel=NotificationChannel.EMAIL,
            recipient=recipient,
            priority=NotificationPriority.HIGH,
            data={"title": "صيانة مجدولة", "message": "سيتم إجراء صيانة للنظام"}
        )
    
    @pytest.mark.asyncio
    async def test_schedule_one_time_notification(
        self, 
        notification_scheduler, 
        sample_notification_request,
        mock_db_connection
    ):
        """اختبار جدولة إشعار لمرة واحدة"""
        db_connection, conn = mock_db_connection
        
        # Mock storage
        notification_scheduler._store_scheduled_notification = AsyncMock()
        
        # Schedule notification
        scheduled_at = datetime.now() + timedelta(hours=2)
        schedule_id = await notification_scheduler.schedule_notification(
            name="صيانة مجدولة",
            notification_request=sample_notification_request,
            schedule_type=ScheduleType.ONE_TIME,
            scheduled_at=scheduled_at,
            created_by="test_user"
        )
        
        # Assertions
        assert schedule_id is not None
        assert schedule_id.startswith("sched_")
        
        # Verify storage was called
        notification_scheduler._store_scheduled_notification.assert_called_once()
    
    def test_validate_schedule_params_one_time_valid(self, notification_scheduler):
        """اختبار التحقق من معاملات الجدولة - مرة واحدة صحيح"""
        scheduled_at = datetime.now() + timedelta(hours=1)
        
        # Should not raise exception
        notification_scheduler._validate_schedule_params(
            ScheduleType.ONE_TIME,
            scheduled_at,
            None
        )
    
    def test_validate_schedule_params_one_time_invalid(self, notification_scheduler):
        """اختبار التحقق من معاملات الجدولة - مرة واحدة خطأ"""
        # No scheduled_at provided
        with pytest.raises(ValueError, match="scheduled_at is required"):
            notification_scheduler._validate_schedule_params(
                ScheduleType.ONE_TIME,
                None,
                None
            )
        
        # Past scheduled_at
        past_time = datetime.now() - timedelta(hours=1)
        with pytest.raises(ValueError, match="must be in the future"):
            notification_scheduler._validate_schedule_params(
                ScheduleType.ONE_TIME,
                past_time,
                None
            )
    
    def test_validate_schedule_params_recurring_valid(self, notification_scheduler):
        """اختبار التحقق من معاملات الجدولة - متكرر صحيح"""
        # Should not raise exception for valid cron
        notification_scheduler._validate_schedule_params(
            ScheduleType.RECURRING,
            None,
            "0 9 * * *"  # Daily at 9 AM
        )
    
    def test_validate_schedule_params_recurring_invalid(self, notification_scheduler):
        """اختبار التحقق من معاملات الجدولة - متكرر خطأ"""
        # No cron expression
        with pytest.raises(ValueError, match="cron_expression is required"):
            notification_scheduler._validate_schedule_params(
                ScheduleType.RECURRING,
                None,
                None
            )
    
    def test_calculate_next_execution_one_time(self, notification_scheduler):
        """اختبار حساب وقت التنفيذ التالي - مرة واحدة"""
        scheduled_at = datetime.now() + timedelta(hours=2)
        
        next_execution = notification_scheduler._calculate_next_execution(
            ScheduleType.ONE_TIME,
            scheduled_at,
            None
        )
        
        assert next_execution == scheduled_at
    
    def test_calculate_next_execution_recurring(self, notification_scheduler):
        """اختبار حساب وقت التنفيذ التالي - متكرر"""
        # This test might need to be adjusted based on croniter availability
        next_execution = notification_scheduler._calculate_next_execution(
            ScheduleType.RECURRING,
            None,
            "0 9 * * *"  # Daily at 9 AM
        )
        
        assert next_execution is not None
        assert next_execution > datetime.now()


class TestNotificationAnalytics:
    """اختبارات تحليلات الإشعارات"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def notification_analytics(self, mock_db_connection):
        """تحليلات الإشعارات للاختبار"""
        db_connection, _ = mock_db_connection
        return NotificationAnalytics(db_connection)
    
    @pytest.mark.asyncio
    async def test_get_delivery_analytics(
        self, 
        notification_analytics,
        mock_db_connection
    ):
        """اختبار الحصول على تحليلات التسليم"""
        db_connection, conn = mock_db_connection
        
        # Mock analytics data
        analytics_data = [
            {
                'period_label': '2024-01-15',
                'total_notifications': 100,
                'sent_count': 95,
                'delivered_count': 90,
                'failed_count': 5,
                'pending_count': 5,
                'avg_delivery_time_seconds': 30.5
            },
            {
                'period_label': '2024-01-16',
                'total_notifications': 120,
                'sent_count': 115,
                'delivered_count': 110,
                'failed_count': 5,
                'pending_count': 5,
                'avg_delivery_time_seconds': 25.2
            }
        ]
        
        channel_data = [
            {'channel': 'email', 'total': 150, 'delivered': 140, 'failed': 10, 'avg_delivery_time': 35.0},
            {'channel': 'sms', 'total': 70, 'delivered': 65, 'failed': 5, 'avg_delivery_time': 15.0}
        ]
        
        type_data = [
            {'notification_type': 'transaction_success', 'total': 120, 'delivered': 115, 'failed': 5},
            {'notification_type': 'security_alert', 'total': 100, 'delivered': 90, 'failed': 10}
        ]
        
        conn.fetch.side_effect = [analytics_data, channel_data, type_data]
        
        # Get analytics
        start_date = date(2024, 1, 15)
        end_date = date(2024, 1, 16)
        
        result = await notification_analytics.get_delivery_analytics(
            start_date=start_date,
            end_date=end_date,
            group_by=AnalyticsPeriod.DAY
        )
        
        # Assertions
        assert result['period']['start_date'] == '2024-01-15'
        assert result['period']['end_date'] == '2024-01-16'
        assert result['period']['group_by'] == 'day'
        
        # Overall metrics
        assert result['overall_metrics']['total_notifications'] == 220  # 100 + 120
        assert result['overall_metrics']['total_delivered'] == 200  # 90 + 110
        assert result['overall_metrics']['delivery_rate'] == 90.91  # 200/220 * 100
        
        # Time series
        assert len(result['time_series']) == 2
        assert result['time_series'][0]['period'] == '2024-01-15'
        assert result['time_series'][0]['delivery_rate'] == 90.0  # 90/100 * 100
        
        # Channel breakdown
        assert len(result['by_channel']) == 2
        assert result['by_channel'][0]['channel'] == 'email'
        assert result['by_channel'][0]['delivery_rate'] == 93.33  # 140/150 * 100
        
        # Type breakdown
        assert len(result['by_type']) == 2
        assert result['by_type'][0]['type'] == 'transaction_success'
        assert result['by_type'][0]['delivery_rate'] == 95.83  # 115/120 * 100


# Integration Tests
class TestNotificationSystemIntegration:
    """اختبارات التكامل لنظام الإشعارات"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_notification_flow(self):
        """اختبار تدفق الإشعارات من البداية للنهاية"""
        # This would test the complete flow:
        # 1. Create template
        # 2. Send notification using template
        # 3. Track delivery
        # 4. Generate analytics
        pass


# Performance Tests
class TestNotificationSystemPerformance:
    """اختبارات الأداء لنظام الإشعارات"""
    
    def test_bulk_notification_performance(self):
        """اختبار أداء الإشعارات المتعددة"""
        # Test that bulk notifications complete within acceptable time
        pass
    
    def test_template_cache_performance(self):
        """اختبار أداء التخزين المؤقت للقوالب"""
        # Test that template caching improves performance
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
