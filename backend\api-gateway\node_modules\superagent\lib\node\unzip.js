"use strict";

/**
 * Module dependencies.
 */

const _require = require('string_decoder'),
  StringDecoder = _require.StringDecoder;
const Stream = require('stream');
const zlib = require('zlib');

/**
 * Buffers response data events and re-emits when they're unzipped.
 *
 * @param {Request} req
 * @param {Response} res
 * @api private
 */

exports.unzip = (request, res) => {
  const unzip = zlib.createUnzip();
  const stream = new Stream();
  let decoder;

  // make node responseOnEnd() happy
  stream.req = request;
  unzip.on('error', error => {
    if (error && error.code === 'Z_BUF_ERROR') {
      // unexpected end of file is ignored by browsers and curl
      stream.emit('end');
      return;
    }
    stream.emit('error', error);
  });

  // pipe to unzip
  res.pipe(unzip);

  // override `setEncoding` to capture encoding
  res.setEncoding = type => {
    decoder = new StringDecoder(type);
  };

  // decode upon decompressing with captured encoding
  unzip.on('data', buf => {
    if (decoder) {
      const string_ = decoder.write(buf);
      if (string_.length > 0) stream.emit('data', string_);
    } else {
      stream.emit('data', buf);
    }
  });
  unzip.on('end', () => {
    stream.emit('end');
  });

  // override `on` to capture data listeners
  const _on = res.on;
  res.on = function (type, fn) {
    if (type === 'data' || type === 'end') {
      stream.on(type, fn.bind(res));
    } else if (type === 'error') {
      stream.on(type, fn.bind(res));
      _on.call(res, type, fn);
    } else {
      _on.call(res, type, fn);
    }
    return this;
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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