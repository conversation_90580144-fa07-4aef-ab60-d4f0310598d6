{"version": 3, "file": "IsHash.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsHash.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAGtC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;;GAIG;AACH,SAAgB,MAAM,CAAC,KAAc,EAAE,SAAoC;IACzE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACxE,CAAC;AAFD,wBAEC;AAED;;;;GAIG;AACH,SAAgB,MAAM,CAAC,SAAiB,EAAE,iBAAqC;IAC7E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,WAAW,EAAE,CAAC,SAAS,CAAC;QACxB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,+CAA+C,EAC1E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,wBAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isHashValidator from 'validator/lib/isHash';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_HASH = 'isHash';\n\n/**\n * Check if the string is a hash of type algorithm.\n * Algorithm is one of ['md4', 'md5', 'sha1', 'sha256', 'sha384', 'sha512', 'ripemd128', 'ripemd160', 'tiger128',\n * 'tiger160', 'tiger192', 'crc32', 'crc32b']\n */\nexport function isHash(value: unknown, algorithm: ValidatorJS.HashAlgorithm): boolean {\n  return typeof value === 'string' && isHashValidator(value, algorithm);\n}\n\n/**\n * Check if the string is a hash of type algorithm.\n * Algorithm is one of ['md4', 'md5', 'sha1', 'sha256', 'sha384', 'sha512', 'ripemd128', 'ripemd160', 'tiger128',\n * 'tiger160', 'tiger192', 'crc32', 'crc32b']\n */\nexport function IsHash(algorithm: string, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_HASH,\n      constraints: [algorithm],\n      validator: {\n        validate: (value, args): boolean => isHash(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a hash of type $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}