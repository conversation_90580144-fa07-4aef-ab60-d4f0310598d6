# استخدام Node.js 18 Alpine كصورة أساسية
FROM node:18-alpine AS base

# تثبيت المتطلبات الأساسية
RUN apk add --no-cache dumb-init

# إنشاء مجلد التطبيق
WORKDIR /app

# نسخ ملفات package
COPY package*.json ./

# مرحلة التطوير
FROM base AS development
RUN npm ci --only=development
COPY . .
EXPOSE 3000
CMD ["dumb-init", "npm", "run", "start:dev"]

# مرحلة البناء
FROM base AS build
RUN npm ci --only=development
COPY . .
RUN npm run build && npm prune --production

# مرحلة الإنتاج
FROM base AS production

# إنشاء مستخدم غير جذر
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# نسخ الملفات المبنية
COPY --from=build --chown=nestjs:nodejs /app/dist ./dist
COPY --from=build --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --chown=nestjs:nodejs package*.json ./

# التبديل للمستخدم غير الجذر
USER nestjs

# تعريف المنفذ
EXPOSE 3000

# إعدادات الصحة
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# تشغيل التطبيق
CMD ["dumb-init", "node", "dist/main"]
