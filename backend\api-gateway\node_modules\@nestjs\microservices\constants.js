"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CONNECTION_FAILED_MESSAGE = exports.EADDRINUSE = exports.CONN_ERR = exports.ECONNREFUSED = exports.MQTT_WILDCARD_ALL = exports.MQTT_WILDCARD_SINGLE = exports.MQTT_SEPARATOR = exports.KAFKA_DEFAULT_GROUP = exports.KAFKA_DEFAULT_CLIENT = exports.DISCONNECTED_RMQ_MESSAGE = exports.NO_MESSAGE_HANDLER = exports.NO_EVENT_HANDLER = exports.GRPC_DEFAULT_PROTO_LOADER = exports.RQM_NO_MESSAGE_HANDLER = exports.RQM_NO_EVENT_HANDLER = exports.RQM_DEFAULT_NO_ASSERT = exports.RQM_DEFAULT_PERSISTENT = exports.RQM_DEFAULT_NOACK = exports.RQM_DEFAULT_QUEUE_OPTIONS = exports.RQM_DEFAULT_IS_GLOBAL_PREFETCH_COUNT = exports.RQM_DEFAULT_PREFETCH_COUNT = exports.RQM_DEFAULT_QUEUE = exports.REPLY_PATTERN_METADATA = exports.REQUEST_PATTERN_METADATA = exports.PARAM_ARGS_METADATA = exports.CLIENT_METADATA = exports.PATTERN_HANDLER_METADATA = exports.CLIENT_CONFIGURATION_METADATA = exports.TRANSPORT_METADATA = exports.PATTERN_EXTRAS_METADATA = exports.PATTERN_METADATA = exports.CANCEL_EVENT = exports.SUBSCRIBE = exports.CLOSE_EVENT = exports.ERROR_EVENT = exports.DATA_EVENT = exports.MESSAGE_EVENT = exports.CONNECT_FAILED_EVENT = exports.DISCONNECT_EVENT = exports.CONNECT_EVENT = exports.KAFKA_DEFAULT_BROKER = exports.RQM_DEFAULT_URL = exports.GRPC_DEFAULT_URL = exports.MQTT_DEFAULT_URL = exports.NATS_DEFAULT_URL = exports.REDIS_DEFAULT_HOST = exports.REDIS_DEFAULT_PORT = exports.TCP_DEFAULT_HOST = exports.TCP_DEFAULT_PORT = void 0;
const constants_1 = require("@nestjs/common/constants");
exports.TCP_DEFAULT_PORT = 3000;
exports.TCP_DEFAULT_HOST = 'localhost';
exports.REDIS_DEFAULT_PORT = 6379;
exports.REDIS_DEFAULT_HOST = 'localhost';
exports.NATS_DEFAULT_URL = 'nats://localhost:4222';
exports.MQTT_DEFAULT_URL = 'mqtt://localhost:1883';
exports.GRPC_DEFAULT_URL = 'localhost:5000';
exports.RQM_DEFAULT_URL = 'amqp://localhost';
exports.KAFKA_DEFAULT_BROKER = 'localhost:9092';
exports.CONNECT_EVENT = 'connect';
exports.DISCONNECT_EVENT = 'disconnect';
exports.CONNECT_FAILED_EVENT = 'connectFailed';
exports.MESSAGE_EVENT = 'message';
exports.DATA_EVENT = 'data';
exports.ERROR_EVENT = 'error';
exports.CLOSE_EVENT = 'close';
exports.SUBSCRIBE = 'subscribe';
exports.CANCEL_EVENT = 'cancelled';
exports.PATTERN_METADATA = 'microservices:pattern';
exports.PATTERN_EXTRAS_METADATA = 'microservices:pattern_extras';
exports.TRANSPORT_METADATA = 'microservices:transport';
exports.CLIENT_CONFIGURATION_METADATA = 'microservices:client';
exports.PATTERN_HANDLER_METADATA = 'microservices:handler_type';
exports.CLIENT_METADATA = 'microservices:is_client_instance';
exports.PARAM_ARGS_METADATA = constants_1.ROUTE_ARGS_METADATA;
exports.REQUEST_PATTERN_METADATA = 'microservices:request_pattern';
exports.REPLY_PATTERN_METADATA = 'microservices:reply_pattern';
exports.RQM_DEFAULT_QUEUE = 'default';
exports.RQM_DEFAULT_PREFETCH_COUNT = 0;
exports.RQM_DEFAULT_IS_GLOBAL_PREFETCH_COUNT = false;
exports.RQM_DEFAULT_QUEUE_OPTIONS = {};
exports.RQM_DEFAULT_NOACK = true;
exports.RQM_DEFAULT_PERSISTENT = false;
exports.RQM_DEFAULT_NO_ASSERT = false;
const RQM_NO_EVENT_HANDLER = (text, pattern) => `An unsupported event was received. It has been negative acknowledged, so it will not be re-delivered. Pattern: ${pattern}`;
exports.RQM_NO_EVENT_HANDLER = RQM_NO_EVENT_HANDLER;
const RQM_NO_MESSAGE_HANDLER = (text, pattern) => `An unsupported message was received. It has been negative acknowledged, so it will not be re-delivered. Pattern: ${pattern}`;
exports.RQM_NO_MESSAGE_HANDLER = RQM_NO_MESSAGE_HANDLER;
exports.GRPC_DEFAULT_PROTO_LOADER = '@grpc/proto-loader';
const NO_EVENT_HANDLER = (text, pattern) => `There is no matching event handler defined in the remote service. Event pattern: ${pattern}`;
exports.NO_EVENT_HANDLER = NO_EVENT_HANDLER;
exports.NO_MESSAGE_HANDLER = `There is no matching message handler defined in the remote service.`;
exports.DISCONNECTED_RMQ_MESSAGE = `Disconnected from RMQ. Trying to reconnect.`;
exports.KAFKA_DEFAULT_CLIENT = 'nestjs-consumer';
exports.KAFKA_DEFAULT_GROUP = 'nestjs-group';
exports.MQTT_SEPARATOR = '/';
exports.MQTT_WILDCARD_SINGLE = '+';
exports.MQTT_WILDCARD_ALL = '#';
exports.ECONNREFUSED = 'ECONNREFUSED';
exports.CONN_ERR = 'CONN_ERR';
exports.EADDRINUSE = 'EADDRINUSE';
exports.CONNECTION_FAILED_MESSAGE = 'Connection to transport failed. Trying to reconnect...';
