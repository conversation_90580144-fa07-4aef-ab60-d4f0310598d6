/**
 * WS Transfir XAMPP Server
 * خادم نظام WS Transfir المخصص لـ XAMPP
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();

// XAMPP Configuration
const config = {
  port: process.env.PORT || 8080, // XAMPP usually uses 8080 for custom apps
  host: '0.0.0.0',
  environment: 'xampp-production',
  xamppPath: process.cwd(),
};

console.log('🔥 تشغيل نظام WS Transfir على XAMPP');
console.log('=====================================');
console.log(`🌐 XAMPP Path: ${config.xamppPath}`);
console.log(`🔧 Port: ${config.port}`);
console.log(`🏠 Host: ${config.host}`);
console.log('');

// XAMPP-friendly middleware
app.use(cors({
  origin: '*', // Allow all origins for XAMPP
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Serve static files from XAMPP htdocs
app.use(express.static('.'));
app.use('/assets', express.static(path.join(__dirname, 'assets')));
app.use('/css', express.static(path.join(__dirname, 'css')));
app.use('/js', express.static(path.join(__dirname, 'js')));
app.use('/images', express.static(path.join(__dirname, 'images')));

// Request logging for XAMPP
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  
  console.log(`[${timestamp}] XAMPP: ${method} ${url} - ${ip}`);
  
  req.requestId = Date.now().toString();
  res.setHeader('X-Powered-By', 'WS-Transfir-XAMPP');
  res.setHeader('X-Request-ID', req.requestId);
  
  next();
});

// Main route - serve XAMPP frontend
app.get('/', (req, res) => {
  try {
    // Check if we have the online frontend
    if (fs.existsSync('online-frontend.html')) {
      const html = fs.readFileSync('online-frontend.html', 'utf8');
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
      res.send(html);
    } else {
      // Fallback XAMPP page
      res.send(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>WS Transfir - XAMPP System</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0; padding: 20px; color: #333;
                }
                .container { 
                    max-width: 800px; margin: 0 auto; 
                    background: white; padding: 30px; 
                    border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                }
                h1 { color: #2563eb; text-align: center; margin-bottom: 30px; }
                .status { 
                    background: #10b981; color: white; 
                    padding: 15px; border-radius: 10px; 
                    text-align: center; margin: 20px 0;
                }
                .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                .link { 
                    background: #f8fafc; border: 2px solid #e5e7eb; 
                    padding: 15px; border-radius: 10px; text-align: center; 
                    text-decoration: none; color: #374151; transition: all 0.3s;
                }
                .link:hover { border-color: #2563eb; transform: translateY(-2px); }
                .info { background: #fef3c7; padding: 15px; border-radius: 10px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔥 WS Transfir - XAMPP System</h1>
                
                <div class="status">
                    🟢 النظام يعمل على XAMPP بنجاح!
                </div>
                
                <div class="info">
                    <h3>📋 معلومات النظام:</h3>
                    <p><strong>🌐 المنفذ:</strong> ${config.port}</p>
                    <p><strong>🏠 المضيف:</strong> ${config.host}</p>
                    <p><strong>📁 المسار:</strong> ${config.xamppPath}</p>
                    <p><strong>⏰ الوقت:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                </div>
                
                <div class="links">
                    <a href="/api/health" class="link">
                        <h4>📊 فحص الصحة</h4>
                        <p>فحص حالة النظام</p>
                    </a>
                    <a href="/api/status" class="link">
                        <h4>📈 حالة النظام</h4>
                        <p>معلومات مفصلة</p>
                    </a>
                    <a href="/api/auth/login" class="link">
                        <h4>🔐 نظام المصادقة</h4>
                        <p>تسجيل الدخول</p>
                    </a>
                    <a href="/api/transfers" class="link">
                        <h4>💸 التحويلات</h4>
                        <p>إدارة التحويلات</p>
                    </a>
                </div>
                
                <div class="info">
                    <h3>🔐 بيانات الدخول التجريبية:</h3>
                    <p><strong>👨‍💼 مدير:</strong> <EMAIL> / admin123</p>
                    <p><strong>👤 مستخدم:</strong> <EMAIL> / password123</p>
                </div>
            </div>
        </body>
        </html>
      `);
    }
  } catch (error) {
    res.status(500).send(`
      <h1>خطأ في تحميل النظام</h1>
      <p>Error: ${error.message}</p>
      <p><a href="/api/health">جرب فحص الصحة</a></p>
    `);
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  res.json({
    status: 'OK',
    message: 'WS Transfir XAMPP System is running successfully',
    timestamp: new Date().toISOString(),
    version: '1.0.0-xampp',
    environment: config.environment,
    xampp: {
      path: config.xamppPath,
      port: config.port,
      host: config.host,
    },
    server: {
      uptime: {
        seconds: Math.floor(uptime),
        human: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
      },
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        usage: `${Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)}%`,
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
      },
    },
    services: {
      api: 'healthy',
      xampp: 'integrated',
      database: 'simulated',
      cache: 'simulated',
    },
    requestId: req.requestId,
  });
});

// System status endpoint
app.get('/api/status', (req, res) => {
  res.json({
    online: true,
    status: 'operational',
    platform: 'XAMPP',
    services: {
      api: { status: 'up', responseTime: '< 50ms' },
      xampp: { status: 'integrated', responseTime: '< 10ms' },
      database: { status: 'simulated', responseTime: '< 30ms' },
    },
    lastUpdated: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Mock users for XAMPP demo
const users = new Map([
  ['<EMAIL>', {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'مدير',
    lastName: 'النظام',
    role: 'admin',
    isVerified: true,
  }],
  ['<EMAIL>', {
    id: '2',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'أحمد',
    lastName: 'محمد',
    role: 'user',
    isVerified: true,
  }]
]);

// Authentication endpoint
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  const timestamp = new Date().toISOString();
  
  console.log(`[${timestamp}] XAMPP Login attempt: ${email}`);
  
  const user = users.get(email);
  
  if (user && user.password === password) {
    const token = `xampp-jwt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح على XAMPP',
      token,
      expiresIn: 3600,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
      },
      platform: 'XAMPP',
      timestamp,
      requestId: req.requestId,
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة',
      error: 'INVALID_CREDENTIALS',
      platform: 'XAMPP',
      timestamp,
      requestId: req.requestId,
    });
  }
});

// Profile endpoint
app.get('/api/profile/me', (req, res) => {
  res.json({
    id: '2',
    firstName: 'أحمد',
    lastName: 'محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    isVerified: true,
    platform: 'XAMPP',
    completionPercentage: 90,
    address: {
      city: 'الرياض',
      country: 'SA',
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Transfers endpoint
app.get('/api/transfers', (req, res) => {
  const mockTransfers = [
    {
      id: '1',
      referenceNumber: 'XAMPP20241225001',
      amount: '1,500.00',
      currency: 'SAR',
      receiverName: 'أحمد محمد',
      receiverCountry: 'مصر',
      status: 'completed',
      createdAt: '2024-12-25T10:30:00Z',
      platform: 'XAMPP',
    },
    {
      id: '2',
      referenceNumber: 'XAMPP20241224002',
      amount: '750.00',
      currency: 'USD',
      receiverName: 'فاطمة علي',
      receiverCountry: 'الأردن',
      status: 'pending',
      createdAt: '2024-12-24T15:45:00Z',
      platform: 'XAMPP',
    }
  ];
  
  res.json({
    success: true,
    data: mockTransfers,
    platform: 'XAMPP',
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Transfer statistics
app.get('/api/transfers/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalTransfers: 15,
      totalAmount: '25,430.50',
      completedTransfers: 12,
      pendingTransfers: 3,
      platform: 'XAMPP',
      thisMonth: {
        transfers: 5,
        amount: '8,450.00',
      }
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(`[${new Date().toISOString()}] XAMPP Error:`, err);
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'حدث خطأ في الخادم',
    error: 'XAMPP_SERVER_ERROR',
    platform: 'XAMPP',
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود',
    error: 'NOT_FOUND',
    path: req.originalUrl,
    platform: 'XAMPP',
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Start XAMPP server
const server = app.listen(config.port, config.host, () => {
  console.log('');
  console.log('🔥 ═══════════════════════════════════════════════════════════');
  console.log('🔥 WS TRANSFIR XAMPP SERVER - FULLY OPERATIONAL');
  console.log('🔥 ═══════════════════════════════════════════════════════════');
  console.log(`🌐 XAMPP URL: http://localhost:${config.port}`);
  console.log(`🌐 External URL: http://127.0.0.1:${config.port}`);
  console.log(`📊 Health Check: http://localhost:${config.port}/api/health`);
  console.log(`🔐 Admin Login: <EMAIL> / admin123`);
  console.log(`👤 User Login: <EMAIL> / password123`);
  console.log(`📁 XAMPP Path: ${config.xamppPath}`);
  console.log(`🏠 Host: ${config.host}:${config.port}`);
  console.log('🔥 ═══════════════════════════════════════════════════════════');
  console.log('');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 XAMPP Server shutting down gracefully...');
  server.close(() => {
    console.log('✅ XAMPP Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 XAMPP Server shutting down gracefully...');
  server.close(() => {
    console.log('✅ XAMPP Server closed');
    process.exit(0);
  });
});

module.exports = app;
