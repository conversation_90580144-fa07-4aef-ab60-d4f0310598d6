module.exports = {
  // Basic formatting
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  doubleQuote: false,
  
  // Indentation
  tabWidth: 2,
  useTabs: false,
  
  // Line length
  printWidth: 80,
  
  // Brackets
  bracketSpacing: true,
  bracketSameLine: false,
  
  // Arrows
  arrowParens: 'avoid',
  
  // Quotes
  quoteProps: 'as-needed',
  
  // JSX
  jsxSingleQuote: true,
  jsxBracketSameLine: false,
  
  // End of line
  endOfLine: 'lf',
  
  // Embedded languages
  embeddedLanguageFormatting: 'auto',
  
  // HTML
  htmlWhitespaceSensitivity: 'css',
  
  // Vue
  vueIndentScriptAndStyle: false,
  
  // Prose
  proseWrap: 'preserve',
  
  // Range formatting
  rangeStart: 0,
  rangeEnd: Infinity,
  
  // Parser
  requirePragma: false,
  insertPragma: false,
  
  // Override for specific file types
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
        tabWidth: 2,
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 100,
        proseWrap: 'always',
      },
    },
    {
      files: '*.yml',
      options: {
        tabWidth: 2,
        singleQuote: false,
      },
    },
    {
      files: '*.yaml',
      options: {
        tabWidth: 2,
        singleQuote: false,
      },
    },
    {
      files: '*.html',
      options: {
        printWidth: 120,
        htmlWhitespaceSensitivity: 'ignore',
      },
    },
    {
      files: '*.css',
      options: {
        printWidth: 120,
      },
    },
    {
      files: '*.scss',
      options: {
        printWidth: 120,
      },
    },
    {
      files: '*.less',
      options: {
        printWidth: 120,
      },
    },
    {
      files: '*.tsx',
      options: {
        jsxSingleQuote: true,
        jsxBracketSameLine: false,
      },
    },
    {
      files: '*.jsx',
      options: {
        jsxSingleQuote: true,
        jsxBracketSameLine: false,
      },
    },
  ],
};
