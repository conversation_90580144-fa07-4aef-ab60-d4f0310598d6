/**
 * Lightning Fast API Client - Zero Dependencies
 * عميل API فائق السرعة - بدون تبعيات خارجية
 * Optimized for maximum performance and instant loading
 */

// Types for lightning fast API client
interface LightningApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp?: string;
  requestId?: string;
}

interface LightningRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
}

// Lightning fast configuration
const LIGHTNING_CONFIG = {
  baseURL: (typeof process !== 'undefined' && process.env?.NEXT_PUBLIC_API_URL) || 'http://localhost:8080',
  timeout: 8000, // 8 seconds for optimal speed
  retries: 1, // Minimal retries for speed
};

// Lightning fast token storage
const tokenStorage = {
  getAccessToken: (): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem('ws_transfir_token');
    } catch {
      return null;
    }
  },
  
  setAccessToken: (token: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem('ws_transfir_token', token);
    } catch {
      // Silent fail for speed
    }
  },
  
  removeAccessToken: (): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem('ws_transfir_token');
    } catch {
      // Silent fail for speed
    }
  },
  
  getRefreshToken: (): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem('ws_transfir_refresh_token');
    } catch {
      return null;
    }
  },
  
  setTokens: (accessToken: string, refreshToken: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem('ws_transfir_token', accessToken);
      localStorage.setItem('ws_transfir_refresh_token', refreshToken);
    } catch {
      // Silent fail for speed
    }
  },
  
  clearTokens: (): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem('ws_transfir_token');
      localStorage.removeItem('ws_transfir_refresh_token');
    } catch {
      // Silent fail for speed
    }
  }
};

// Lightning fast toast notifications
const toast = {
  success: (message: string) => {
    console.log('✅ Success:', message);
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('lightning-toast', { 
        detail: { type: 'success', message } 
      }));
    }
  },
  
  error: (message: string) => {
    console.error('❌ Error:', message);
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('lightning-toast', { 
        detail: { type: 'error', message } 
      }));
    }
  },
  
  loading: (message: string) => {
    console.log('⏳ Loading:', message);
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('lightning-toast', { 
        detail: { type: 'loading', message } 
      }));
    }
  }
};

// Generate unique request ID for tracking
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
};

// Lightning fast HTTP client using native fetch
class LightningApiClient {
  private baseURL: string;
  private timeout: number;
  
  constructor() {
    this.baseURL = LIGHTNING_CONFIG.baseURL;
    this.timeout = LIGHTNING_CONFIG.timeout;
  }
  
  async get<T = any>(endpoint: string, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'GET' });
  }
  
  async post<T = any>(endpoint: string, data?: any, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'POST', body: data });
  }
  
  async put<T = any>(endpoint: string, data?: any, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'PUT', body: data });
  }
  
  async patch<T = any>(endpoint: string, data?: any, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'PATCH', body: data });
  }
  
  async delete<T = any>(endpoint: string, config: LightningRequestConfig = {}): Promise<LightningApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...config, method: 'DELETE' });
  }
  
  private async makeRequest<T = any>(
    endpoint: string, 
    config: LightningRequestConfig = {}
  ): Promise<LightningApiResponse<T>> {
    const url = `${this.baseURL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    const method = config.method || 'GET';
    const requestId = generateRequestId();
    
    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Request-ID': requestId,
      'X-Powered-By': 'WS-Transfir-Lightning',
      ...config.headers,
    };
    
    // Add auth token if available
    const token = tokenStorage.getAccessToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    };
    
    // Add timeout using AbortController for better browser support
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || this.timeout);
    requestOptions.signal = controller.signal;
    
    // Add body for non-GET requests
    if (config.body && method !== 'GET') {
      requestOptions.body = typeof config.body === 'string' 
        ? config.body 
        : JSON.stringify(config.body);
    }
    
    try {
      const startTime = Date.now();
      const response = await fetch(url, requestOptions);
      const endTime = Date.now();
      
      clearTimeout(timeoutId);
      
      // Log performance in development
      if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
        console.log(`⚡ Lightning API: ${method} ${endpoint} - ${endTime - startTime}ms`);
      }
      
      // Parse response
      let data: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }
      
      // Handle response
      if (response.ok) {
        return {
          success: true,
          data,
          timestamp: new Date().toISOString(),
          requestId,
        };
      } else {
        // Handle HTTP errors
        const errorMessage = data?.message || data?.error || `HTTP ${response.status}`;
        
        // Handle 401 Unauthorized
        if (response.status === 401) {
          tokenStorage.clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
        }
        
        return {
          success: false,
          error: errorMessage,
          timestamp: new Date().toISOString(),
          requestId,
        };
      }
    } catch (error: any) {
      clearTimeout(timeoutId);
      console.error('Lightning API Error:', error);
      
      let errorMessage = 'خطأ في الاتصال';
      if (error.name === 'AbortError') {
        errorMessage = 'انتهت مهلة الطلب';
      } else if (error.name === 'TypeError') {
        errorMessage = 'خطأ في الشبكة';
      }
      
      return {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
        requestId,
      };
    }
  }
}

// Create and export lightning fast API client instance
const lightningApiClient = new LightningApiClient();

// Lightning fast API request helpers
export const apiRequest = {
  get: <T = any>(endpoint: string, config?: LightningRequestConfig): Promise<LightningApiResponse<T>> =>
    lightningApiClient.get<T>(endpoint, config),
    
  post: <T = any>(endpoint: string, data?: any, config?: LightningRequestConfig): Promise<LightningApiResponse<T>> =>
    lightningApiClient.post<T>(endpoint, data, config),
    
  put: <T = any>(endpoint: string, data?: any, config?: LightningRequestConfig): Promise<LightningApiResponse<T>> =>
    lightningApiClient.put<T>(endpoint, data, config),
    
  patch: <T = any>(endpoint: string, data?: any, config?: LightningRequestConfig): Promise<LightningApiResponse<T>> =>
    lightningApiClient.patch<T>(endpoint, data, config),
    
  delete: <T = any>(endpoint: string, config?: LightningRequestConfig): Promise<LightningApiResponse<T>> =>
    lightningApiClient.delete<T>(endpoint, config),
};

// Lightning fast file upload
export const uploadFile = async (
  endpoint: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<LightningApiResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  
  return lightningApiClient.post(endpoint, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// Lightning fast file download
export const downloadFile = async (
  endpoint: string,
  filename?: string
): Promise<void> => {
  try {
    const response = await lightningApiClient.get(endpoint);
    
    if (response.success && response.data) {
      // Create download link
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    }
  } catch (error) {
    console.error('Download error:', error);
    toast.error('فشل في تحميل الملف');
  }
};

// Handle API errors with user-friendly messages
export const handleApiError = (error: any) => {
  const message = error?.error || error?.message || 'حدث خطأ غير متوقع';
  toast.error(message);
  console.error('API Error:', error);
};

// Export the main client and utilities
export { lightningApiClient as apiClient, tokenStorage, toast };
export default lightningApiClient;
