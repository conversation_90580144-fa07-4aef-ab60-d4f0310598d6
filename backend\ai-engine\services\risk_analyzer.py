"""
Risk Assessment Service
======================
خدمة تقييم المخاطر المتقدمة
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from enum import Enum

import joblib # type: ignore
from sklearn.ensemble import RandomForestClassifier # type: ignore
from sklearn.preprocessing import StandardScaler # type: ignore
import tensorflow as tf # type: ignore

from core.config import settings, MODEL_CONFIGS
from core.database import DatabaseManager
from core.redis_client import RedisClient
from services.model_manager import ModelManager
from services.feature_engineer import FeatureEngineer
from utils.metrics import MetricsCollector # type: ignore

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """مستويات المخاطر"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class RiskCategory(Enum):
    """فئات المخاطر"""
    CREDIT = "credit"
    OPERATIONAL = "operational"
    COMPLIANCE = "compliance"
    FRAUD = "fraud"
    LIQUIDITY = "liquidity"
    MARKET = "market"


@dataclass
class RiskAssessment:
    """نتيجة تقييم المخاطر"""
    user_id: str
    overall_risk_score: float
    risk_level: RiskLevel
    risk_categories: Dict[RiskCategory, float]
    risk_factors: List[str]
    recommendations: List[str]
    confidence: float
    timestamp: datetime
    model_version: str
    expires_at: datetime


@dataclass
class UserRiskProfile:
    """ملف المخاطر للمستخدم"""
    user_id: str
    demographic_data: Dict[str, Any]
    financial_data: Dict[str, Any]
    behavioral_data: Dict[str, Any]
    transaction_history: List[Dict[str, Any]]
    kyc_data: Dict[str, Any]
    compliance_status: Dict[str, Any]


class RiskAnalyzer:
    """محلل المخاطر المتقدم"""
    
    def __init__(
        self,
        model_manager: ModelManager,
        db_manager: DatabaseManager,
        redis_client: RedisClient
    ):
        self.model_manager = model_manager
        self.db_manager = db_manager
        self.redis_client = redis_client
        self.feature_engineer = FeatureEngineer()
        self.metrics_collector = MetricsCollector()
        
        # Models
        self.credit_risk_model = None
        self.operational_risk_model = None
        self.compliance_risk_model = None
        self.lstm_model = None
        self.scaler = None
        
        # Configuration
        self.risk_thresholds = {
            RiskLevel.VERY_LOW: settings.RISK_THRESHOLD_LOW * 0.5,
            RiskLevel.LOW: settings.RISK_THRESHOLD_LOW,
            RiskLevel.MEDIUM: settings.RISK_THRESHOLD_MEDIUM,
            RiskLevel.HIGH: settings.RISK_THRESHOLD_HIGH,
            RiskLevel.VERY_HIGH: 1.0
        }
        
        self.model_version = settings.RISK_MODEL_VERSION
        self.feature_names = MODEL_CONFIGS["risk_assessment"]["features"]
        
        # Cache
        self.risk_profiles_cache = {}
        self.country_risk_cache = {}
        
        # Statistics
        self.total_assessments = 0
        self.high_risk_users = 0
        
    async def initialize(self):
        """تهيئة محلل المخاطر"""
        try:
            logger.info("⚖️ Initializing Risk Analyzer...")
            
            # Load models
            await self._load_models()
            
            # Initialize feature engineer
            await self.feature_engineer.initialize()
            
            # Load risk data
            await self._load_risk_data()
            
            logger.info("✅ Risk Analyzer initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Risk Analyzer: {e}")
            raise
    
    async def _load_models(self):
        """تحميل نماذج تقييم المخاطر"""
        try:
            # Load Credit Risk model
            credit_path = f"{settings.MODEL_PATH}/credit_risk_{self.model_version}.joblib"
            self.credit_risk_model = await self.model_manager.load_model("credit_risk", credit_path)
            
            # Load Operational Risk model
            operational_path = f"{settings.MODEL_PATH}/operational_risk_{self.model_version}.joblib"
            self.operational_risk_model = await self.model_manager.load_model("operational_risk", operational_path)
            
            # Load Compliance Risk model
            compliance_path = f"{settings.MODEL_PATH}/compliance_risk_{self.model_version}.joblib"
            self.compliance_risk_model = await self.model_manager.load_model("compliance_risk", compliance_path)
            
            # Load LSTM model for time series risk prediction
            lstm_path = f"{settings.MODEL_PATH}/risk_lstm_{self.model_version}.h5"
            self.lstm_model = await self.model_manager.load_model("risk_lstm", lstm_path)
            
            # Load scaler
            scaler_path = f"{settings.MODEL_PATH}/risk_scaler_{self.model_version}.joblib"
            self.scaler = await self.model_manager.load_model("risk_scaler", scaler_path)
            
            logger.info("✅ Risk assessment models loaded")
            
        except Exception as e:
            logger.error(f"❌ Failed to load risk models: {e}")
            await self._create_default_models()
    
    async def _create_default_models(self):
        """إنشاء نماذج افتراضية"""
        logger.info("🔧 Creating default risk assessment models...")
        
        # Create Random Forest models
        self.credit_risk_model = RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            random_state=42
        )
        
        self.operational_risk_model = RandomForestClassifier(
            n_estimators=150,
            max_depth=8,
            random_state=42
        )
        
        self.compliance_risk_model = RandomForestClassifier(
            n_estimators=100,
            max_depth=6,
            random_state=42
        )
        
        # Create LSTM model
        self.lstm_model = tf.keras.Sequential([
            tf.keras.layers.LSTM(64, return_sequences=True, input_shape=(30, len(self.feature_names))),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.LSTM(32, return_sequences=False),
            tf.keras.layers.Dropout(0.2),
            tf.keras.layers.Dense(16, activation='relu'),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        self.lstm_model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        # Create scaler
        self.scaler = StandardScaler()
        
        logger.info("✅ Default risk models created")
    
    async def _load_risk_data(self):
        """تحميل بيانات المخاطر"""
        try:
            # Load country risk data
            cached_country_risk = await self.redis_client.get("country_risk_data")
            if cached_country_risk:
                self.country_risk_cache = json.loads(cached_country_risk)
            else:
                await self._refresh_country_risk_data()
            
            # Load user risk profiles
            cached_profiles = await self.redis_client.get("risk_profiles")
            if cached_profiles:
                self.risk_profiles_cache = json.loads(cached_profiles)
            else:
                await self._refresh_risk_profiles()
                
        except Exception as e:
            logger.error(f"❌ Failed to load risk data: {e}")
    
    async def assess_user_risk(self, user_id: str, context: Dict[str, Any] = None) -> RiskAssessment:
        """تقييم مخاطر المستخدم"""
        try:
            start_time = datetime.now()
            
            logger.info(f"⚖️ Assessing risk for user: {user_id}")
            
            # Get user risk profile
            user_profile = await self._get_user_risk_profile(user_id)
            
            # Extract features
            features = await self._extract_risk_features(user_profile, context)
            
            # Assess different risk categories
            credit_risk = await self._assess_credit_risk(features)
            operational_risk = await self._assess_operational_risk(features)
            compliance_risk = await self._assess_compliance_risk(features)
            fraud_risk = await self._assess_fraud_risk(features)
            liquidity_risk = await self._assess_liquidity_risk(features)
            market_risk = await self._assess_market_risk(features)
            
            # Calculate overall risk score
            risk_categories = {
                RiskCategory.CREDIT: credit_risk,
                RiskCategory.OPERATIONAL: operational_risk,
                RiskCategory.COMPLIANCE: compliance_risk,
                RiskCategory.FRAUD: fraud_risk,
                RiskCategory.LIQUIDITY: liquidity_risk,
                RiskCategory.MARKET: market_risk
            }
            
            overall_risk_score = await self._calculate_overall_risk(risk_categories)
            
            # Determine risk level
            risk_level = self._get_risk_level(overall_risk_score)
            
            # Generate risk factors and recommendations
            risk_factors = await self._identify_risk_factors(features, risk_categories)
            recommendations = await self._generate_recommendations(risk_level, risk_factors)
            
            # Calculate confidence
            confidence = await self._calculate_confidence(risk_categories)
            
            # Create assessment
            assessment = RiskAssessment(
                user_id=user_id,
                overall_risk_score=overall_risk_score,
                risk_level=risk_level,
                risk_categories=risk_categories,
                risk_factors=risk_factors,
                recommendations=recommendations,
                confidence=confidence,
                timestamp=datetime.now(),
                model_version=self.model_version,
                expires_at=datetime.now() + timedelta(hours=24)
            )
            
            # Cache assessment
            await self._cache_assessment(assessment)
            
            # Update statistics
            self.total_assessments += 1
            if risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
                self.high_risk_users += 1
            
            # Log metrics
            processing_time = (datetime.now() - start_time).total_seconds()
            await self.metrics_collector.record_prediction(
                model_type="risk_assessment",
                score=overall_risk_score,
                processing_time=processing_time,
                risk_level=risk_level.value
            )
            
            logger.info(f"✅ Risk assessment completed: {user_id} - Score: {overall_risk_score:.3f} - Level: {risk_level.value}")
            
            return assessment
            
        except Exception as e:
            logger.error(f"❌ Risk assessment failed: {e}")
            raise
    
    async def _extract_risk_features(self, user_profile: UserRiskProfile, context: Dict[str, Any] = None) -> np.ndarray:
        """استخراج ميزات تقييم المخاطر"""
        try:
            # Demographic features
            demographic_features = await self.feature_engineer.extract_demographic_features(
                user_profile.demographic_data
            )
            
            # Financial features
            financial_features = await self.feature_engineer.extract_financial_features(
                user_profile.financial_data
            )
            
            # Behavioral features
            behavioral_features = await self.feature_engineer.extract_behavioral_features_from_history(
                user_profile.behavioral_data
            )
            
            # Transaction features
            transaction_features = await self.feature_engineer.extract_transaction_risk_features(
                user_profile.transaction_history
            )
            
            # KYC features
            kyc_features = await self.feature_engineer.extract_kyc_features(
                user_profile.kyc_data
            )
            
            # Compliance features
            compliance_features = await self.feature_engineer.extract_compliance_features(
                user_profile.compliance_status
            )
            
            # Context features (if provided)
            context_features = {}
            if context:
                context_features = await self.feature_engineer.extract_context_features(context)
            
            # Combine all features
            all_features = {
                **demographic_features,
                **financial_features,
                **behavioral_features,
                **transaction_features,
                **kyc_features,
                **compliance_features,
                **context_features
            }
            
            # Convert to array in correct order
            feature_array = np.array([all_features.get(name, 0) for name in self.feature_names])
            
            return feature_array.reshape(1, -1)
            
        except Exception as e:
            logger.error(f"❌ Risk feature extraction failed: {e}")
            raise
    
    async def _assess_credit_risk(self, features: np.ndarray) -> float:
        """تقييم المخاطر الائتمانية"""
        try:
            if self.credit_risk_model is None:
                return 0.5  # Default score
            
            # Scale features
            scaled_features = self.scaler.transform(features)
            
            # Get prediction probability
            prob = self.credit_risk_model.predict_proba(scaled_features)[0][1]
            
            return float(prob)
            
        except Exception as e:
            logger.error(f"❌ Credit risk assessment failed: {e}")
            return 0.5
    
    async def _assess_operational_risk(self, features: np.ndarray) -> float:
        """تقييم المخاطر التشغيلية"""
        try:
            if self.operational_risk_model is None:
                return 0.5  # Default score
            
            # Scale features
            scaled_features = self.scaler.transform(features)
            
            # Get prediction probability
            prob = self.operational_risk_model.predict_proba(scaled_features)[0][1]
            
            return float(prob)
            
        except Exception as e:
            logger.error(f"❌ Operational risk assessment failed: {e}")
            return 0.5
    
    async def _assess_compliance_risk(self, features: np.ndarray) -> float:
        """تقييم مخاطر الامتثال"""
        try:
            if self.compliance_risk_model is None:
                return 0.5  # Default score
            
            # Scale features
            scaled_features = self.scaler.transform(features)
            
            # Get prediction probability
            prob = self.compliance_risk_model.predict_proba(scaled_features)[0][1]
            
            return float(prob)
            
        except Exception as e:
            logger.error(f"❌ Compliance risk assessment failed: {e}")
            return 0.5
    
    async def _assess_fraud_risk(self, features: np.ndarray) -> float:
        """تقييم مخاطر الاحتيال"""
        try:
            # Use simplified fraud risk calculation
            # In production, this would use the fraud detection service
            
            # Extract relevant features for fraud risk
            amount_features = features[0][:5]  # First 5 features assumed to be amount-related
            behavioral_features = features[0][5:10]  # Next 5 features for behavior
            
            # Simple risk calculation
            amount_risk = np.mean(amount_features) * 0.3
            behavioral_risk = np.std(behavioral_features) * 0.7
            
            fraud_risk = min(max(amount_risk + behavioral_risk, 0.0), 1.0)
            
            return float(fraud_risk)
            
        except Exception as e:
            logger.error(f"❌ Fraud risk assessment failed: {e}")
            return 0.5
    
    async def _assess_liquidity_risk(self, features: np.ndarray) -> float:
        """تقييم مخاطر السيولة"""
        try:
            # Simplified liquidity risk assessment
            # Based on transaction patterns and account balance trends
            
            # Extract liquidity-related features
            balance_features = features[0][10:15]  # Assumed balance-related features
            transaction_features = features[0][15:20]  # Transaction frequency features
            
            # Calculate liquidity risk
            balance_risk = 1.0 - np.mean(balance_features)  # Lower balance = higher risk
            transaction_risk = np.std(transaction_features)  # High volatility = higher risk
            
            liquidity_risk = min(max((balance_risk * 0.6 + transaction_risk * 0.4), 0.0), 1.0)
            
            return float(liquidity_risk)
            
        except Exception as e:
            logger.error(f"❌ Liquidity risk assessment failed: {e}")
            return 0.5
    
    async def _assess_market_risk(self, features: np.ndarray) -> float:
        """تقييم مخاطر السوق"""
        try:
            # Simplified market risk assessment
            # Based on currency exposure and market volatility
            
            # Extract market-related features
            currency_features = features[0][20:25]  # Currency exposure features
            volatility_features = features[0][25:30]  # Market volatility features
            
            # Calculate market risk
            currency_risk = np.std(currency_features)  # Currency diversification
            volatility_risk = np.mean(volatility_features)  # Market volatility exposure
            
            market_risk = min(max((currency_risk * 0.4 + volatility_risk * 0.6), 0.0), 1.0)
            
            return float(market_risk)
            
        except Exception as e:
            logger.error(f"❌ Market risk assessment failed: {e}")
            return 0.5
    
    async def _calculate_overall_risk(self, risk_categories: Dict[RiskCategory, float]) -> float:
        """حساب درجة المخاطر الإجمالية"""
        # Weighted average of risk categories
        weights = {
            RiskCategory.CREDIT: 0.25,
            RiskCategory.OPERATIONAL: 0.20,
            RiskCategory.COMPLIANCE: 0.20,
            RiskCategory.FRAUD: 0.15,
            RiskCategory.LIQUIDITY: 0.10,
            RiskCategory.MARKET: 0.10
        }
        
        overall_risk = sum(
            weights[category] * score
            for category, score in risk_categories.items()
        )
        
        return min(max(overall_risk, 0.0), 1.0)
    
    def _get_risk_level(self, risk_score: float) -> RiskLevel:
        """تحديد مستوى المخاطر"""
        for level, threshold in sorted(self.risk_thresholds.items(), key=lambda x: x[1]):
            if risk_score <= threshold:
                return level
        return RiskLevel.VERY_HIGH
    
    async def _identify_risk_factors(self, features: np.ndarray, risk_categories: Dict[RiskCategory, float]) -> List[str]:
        """تحديد عوامل المخاطر"""
        risk_factors = []
        
        # Identify high-risk categories
        for category, score in risk_categories.items():
            if score >= 0.7:
                risk_factors.append(f"مخاطر عالية في فئة {category.value}")
            elif score >= 0.5:
                risk_factors.append(f"مخاطر متوسطة في فئة {category.value}")
        
        # Add specific risk factors based on features
        # This would be more sophisticated in production
        if np.mean(features[0][:5]) > 0.8:
            risk_factors.append("أنماط معاملات غير عادية")
        
        if np.std(features[0][5:10]) > 0.6:
            risk_factors.append("تقلبات عالية في السلوك")
        
        return risk_factors
    
    async def _generate_recommendations(self, risk_level: RiskLevel, risk_factors: List[str]) -> List[str]:
        """توليد التوصيات"""
        recommendations = []
        
        if risk_level == RiskLevel.VERY_HIGH:
            recommendations.extend([
                "مراجعة فورية للحساب",
                "تطبيق حدود صارمة للمعاملات",
                "مراقبة مكثفة للنشاط"
            ])
        elif risk_level == RiskLevel.HIGH:
            recommendations.extend([
                "مراجعة دورية للحساب",
                "تطبيق حدود إضافية",
                "تحديث بيانات KYC"
            ])
        elif risk_level == RiskLevel.MEDIUM:
            recommendations.extend([
                "مراقبة منتظمة للنشاط",
                "تحديث المعلومات الشخصية"
            ])
        
        # Add specific recommendations based on risk factors
        for factor in risk_factors:
            if "معاملات غير عادية" in factor:
                recommendations.append("تحليل أنماط المعاملات")
            elif "تقلبات عالية" in factor:
                recommendations.append("تقييم السلوك المالي")
        
        return recommendations
    
    async def _calculate_confidence(self, risk_categories: Dict[RiskCategory, float]) -> float:
        """حساب مستوى الثقة في التقييم"""
        # Calculate confidence based on consistency across categories
        scores = list(risk_categories.values())
        variance = np.var(scores)
        
        # Lower variance = higher confidence
        confidence = 1.0 - min(variance * 2, 0.4)
        
        return max(confidence, 0.6)  # Minimum confidence of 60%
    
    async def _get_user_risk_profile(self, user_id: str) -> UserRiskProfile:
        """الحصول على ملف مخاطر المستخدم"""
        if user_id in self.risk_profiles_cache:
            return self.risk_profiles_cache[user_id]
        
        # Load from database
        profile_data = await self.db_manager.get_user_risk_profile(user_id)
        
        if profile_data:
            profile = UserRiskProfile(**profile_data)
            self.risk_profiles_cache[user_id] = profile
            return profile
        
        # Return default profile
        return UserRiskProfile(
            user_id=user_id,
            demographic_data={"age": 30, "country": "SA"},
            financial_data={"income": 50000, "credit_score": 700},
            behavioral_data={"avg_transaction_amount": 1000},
            transaction_history=[],
            kyc_data={"level": 1, "verified": True},
            compliance_status={"aml_status": "clear"}
        )
    
    async def _cache_assessment(self, assessment: RiskAssessment):
        """حفظ التقييم في الكاش"""
        try:
            cache_key = f"risk_assessment:{assessment.user_id}"
            cache_data = {
                "overall_risk_score": assessment.overall_risk_score,
                "risk_level": assessment.risk_level.value,
                "risk_categories": {k.value: v for k, v in assessment.risk_categories.items()},
                "confidence": assessment.confidence,
                "timestamp": assessment.timestamp.isoformat(),
                "expires_at": assessment.expires_at.isoformat(),
                "model_version": assessment.model_version
            }
            
            # Cache until expiration
            ttl = int((assessment.expires_at - datetime.now()).total_seconds())
            await self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(cache_data)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to cache risk assessment: {e}")
    
    async def _refresh_country_risk_data(self):
        """تحديث بيانات مخاطر البلدان"""
        try:
            # Load country risk data from database or external source
            country_risk_data = await self.db_manager.get_country_risk_data()
            self.country_risk_cache = country_risk_data or {}
            
            # Cache in Redis
            await self.redis_client.setex(
                "country_risk_data",
                86400,  # 24 hours TTL
                json.dumps(self.country_risk_cache)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to refresh country risk data: {e}")
    
    async def _refresh_risk_profiles(self):
        """تحديث ملفات المخاطر"""
        try:
            # This would load recent risk profiles from database
            # For now, we'll keep the cache empty and load on-demand
            pass
            
        except Exception as e:
            logger.error(f"❌ Failed to refresh risk profiles: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات محلل المخاطر"""
        return {
            "total_assessments": self.total_assessments,
            "high_risk_users": self.high_risk_users,
            "high_risk_rate": self.high_risk_users / max(self.total_assessments, 1),
            "model_version": self.model_version,
            "risk_thresholds": {level.value: threshold for level, threshold in self.risk_thresholds.items()},
            "cache_size": {
                "risk_profiles": len(self.risk_profiles_cache),
                "country_risk": len(self.country_risk_cache)
            }
        }
    
    async def cleanup(self):
        """تنظيف الموارد"""
        try:
            logger.info("🧹 Cleaning up Risk Analyzer...")
            
            # Clear caches
            self.risk_profiles_cache.clear()
            self.country_risk_cache.clear()
            
            # Cleanup models
            if self.lstm_model:
                del self.lstm_model
            
            logger.info("✅ Risk Analyzer cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Risk Analyzer cleanup failed: {e}")
