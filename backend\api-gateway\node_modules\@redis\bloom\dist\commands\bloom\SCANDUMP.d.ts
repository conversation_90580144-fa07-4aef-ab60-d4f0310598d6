export declare const FIRST_KEY_INDEX = 1;
export declare const IS_READ_ONLY = true;
export declare function transformArguments(key: string, iterator: number): Array<string>;
type ScanDumpRawReply = [
    iterator: number,
    chunk: string
];
interface ScanDumpReply {
    iterator: number;
    chunk: string;
}
export declare function transformReply([iterator, chunk]: ScanDumpRawReply): ScanDumpReply;
export {};
