'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';

// أنواع اللغات المدعومة
export type Language = 'ar' | 'en';

// واجهة سياق اللغة
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  isRTL: boolean;
  t: (key: string, params?: Record<string, string | number>) => string;
  formatNumber: (num: number) => string;
  formatCurrency: (amount: number, currency: string) => string;
  formatDate: (date: Date | string) => string;
  formatTime: (date: Date | string) => string;
}

// إنشاء السياق
const LanguageContext = createContext<LanguageContextType | null>(null);

// ترجمات التطبيق
const translations = {
  ar: {
    // العامة
    'common.loading': 'جاري التحميل...',
    'common.error': 'حدث خطأ',
    'common.success': 'تم بنجاح',
    'common.cancel': 'إلغاء',
    'common.confirm': 'تأكيد',
    'common.save': 'حفظ',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',
    'common.search': 'بحث',
    'common.filter': 'تصفية',
    'common.sort': 'ترتيب',
    'common.next': 'التالي',
    'common.previous': 'السابق',
    'common.close': 'إغلاق',
    'common.open': 'فتح',
    'common.view': 'عرض',
    'common.download': 'تحميل',
    'common.upload': 'رفع',
    'common.send': 'إرسال',
    'common.receive': 'استلام',
    'common.amount': 'المبلغ',
    'common.currency': 'العملة',
    'common.date': 'التاريخ',
    'common.time': 'الوقت',
    'common.status': 'الحالة',
    'common.type': 'النوع',
    'common.description': 'الوصف',
    'common.notes': 'ملاحظات',
    'common.total': 'الإجمالي',
    'common.subtotal': 'المجموع الفرعي',
    'common.fees': 'الرسوم',
    'common.discount': 'الخصم',
    'common.tax': 'الضريبة',

    // التنقل
    'nav.home': 'الرئيسية',
    'nav.dashboard': 'لوحة التحكم',
    'nav.transfers': 'التحويلات',
    'nav.wallet': 'المحفظة',
    'nav.profile': 'الملف الشخصي',
    'nav.settings': 'الإعدادات',
    'nav.help': 'المساعدة',
    'nav.logout': 'تسجيل الخروج',

    // المصادقة
    'auth.login': 'تسجيل الدخول',
    'auth.register': 'إنشاء حساب',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.confirmPassword': 'تأكيد كلمة المرور',
    'auth.firstName': 'الاسم الأول',
    'auth.lastName': 'الاسم الأخير',
    'auth.phone': 'رقم الهاتف',
    'auth.nationalId': 'رقم الهوية',
    'auth.forgotPassword': 'نسيت كلمة المرور؟',
    'auth.rememberMe': 'تذكرني',
    'auth.loginSuccess': 'تم تسجيل الدخول بنجاح',
    'auth.loginError': 'فشل في تسجيل الدخول',
    'auth.registerSuccess': 'تم إنشاء الحساب بنجاح',
    'auth.registerError': 'فشل في إنشاء الحساب',
    'auth.logoutSuccess': 'تم تسجيل الخروج بنجاح',
    'auth.2fa.title': 'المصادقة الثنائية',
    'auth.2fa.description': 'يرجى إدخال رمز التحقق',
    'auth.2fa.code': 'رمز التحقق',
    'auth.2fa.verify': 'تحقق',

    // التحويلات
    'transfer.send': 'إرسال حوالة',
    'transfer.receive': 'استلام حوالة',
    'transfer.history': 'سجل التحويلات',
    'transfer.amount': 'مبلغ التحويل',
    'transfer.recipient': 'المستلم',
    'transfer.sender': 'المرسل',
    'transfer.fees': 'رسوم التحويل',
    'transfer.exchangeRate': 'سعر الصرف',
    'transfer.estimatedDelivery': 'وقت التسليم المتوقع',
    'transfer.trackingCode': 'رمز التتبع',
    'transfer.purpose': 'غرض التحويل',
    'transfer.method': 'طريقة التحويل',
    'transfer.status.pending': 'قيد الانتظار',
    'transfer.status.processing': 'قيد المعالجة',
    'transfer.status.completed': 'مكتمل',
    'transfer.status.cancelled': 'ملغي',
    'transfer.status.failed': 'فاشل',

    // المحفظة
    'wallet.balance': 'الرصيد',
    'wallet.topUp': 'شحن المحفظة',
    'wallet.withdraw': 'سحب من المحفظة',
    'wallet.transfer': 'تحويل',
    'wallet.transactions': 'المعاملات',
    'wallet.payBill': 'دفع فاتورة',
    'wallet.exchangeRate': 'أسعار الصرف',
    'wallet.limits': 'الحدود',
    'wallet.cards': 'البطاقات',

    // الملف الشخصي
    'profile.personalInfo': 'المعلومات الشخصية',
    'profile.contactInfo': 'معلومات الاتصال',
    'profile.documents': 'الوثائق',
    'profile.kyc': 'التحقق من الهوية',
    'profile.security': 'الأمان',
    'profile.preferences': 'التفضيلات',
    'profile.notifications': 'الإشعارات',

    // الأخطاء
    'error.network': 'خطأ في الشبكة',
    'error.server': 'خطأ في الخادم',
    'error.unauthorized': 'غير مخول',
    'error.forbidden': 'ممنوع',
    'error.notFound': 'غير موجود',
    'error.validation': 'خطأ في التحقق من البيانات',
    'error.unknown': 'خطأ غير معروف',

    // الرسائل
    'message.noData': 'لا توجد بيانات',
    'message.noResults': 'لا توجد نتائج',
    'message.comingSoon': 'قريباً',
    'message.underMaintenance': 'تحت الصيانة',
  },
  en: {
    // General
    'common.loading': 'Loading...',
    'common.error': 'Error occurred',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.sort': 'Sort',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.close': 'Close',
    'common.open': 'Open',
    'common.view': 'View',
    'common.download': 'Download',
    'common.upload': 'Upload',
    'common.send': 'Send',
    'common.receive': 'Receive',
    'common.amount': 'Amount',
    'common.currency': 'Currency',
    'common.date': 'Date',
    'common.time': 'Time',
    'common.status': 'Status',
    'common.type': 'Type',
    'common.description': 'Description',
    'common.notes': 'Notes',
    'common.total': 'Total',
    'common.subtotal': 'Subtotal',
    'common.fees': 'Fees',
    'common.discount': 'Discount',
    'common.tax': 'Tax',

    // Navigation
    'nav.home': 'Home',
    'nav.dashboard': 'Dashboard',
    'nav.transfers': 'Transfers',
    'nav.wallet': 'Wallet',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
    'nav.help': 'Help',
    'nav.logout': 'Logout',

    // Authentication
    'auth.login': 'Login',
    'auth.register': 'Register',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.confirmPassword': 'Confirm Password',
    'auth.firstName': 'First Name',
    'auth.lastName': 'Last Name',
    'auth.phone': 'Phone Number',
    'auth.nationalId': 'National ID',
    'auth.forgotPassword': 'Forgot Password?',
    'auth.rememberMe': 'Remember Me',
    'auth.loginSuccess': 'Login successful',
    'auth.loginError': 'Login failed',
    'auth.registerSuccess': 'Registration successful',
    'auth.registerError': 'Registration failed',
    'auth.logoutSuccess': 'Logout successful',
    'auth.2fa.title': 'Two-Factor Authentication',
    'auth.2fa.description': 'Please enter verification code',
    'auth.2fa.code': 'Verification Code',
    'auth.2fa.verify': 'Verify',

    // Add more English translations...
  },
};

// مزود سياق اللغة
export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguageState] = useState<Language>('ar');
  const router = useRouter();
  const pathname = usePathname();

  // تحميل اللغة المحفوظة عند بدء التطبيق
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && ['ar', 'en'].includes(savedLanguage)) {
      setLanguageState(savedLanguage);
    }
  }, []);

  // تحديث اتجاه الصفحة عند تغيير اللغة
  useEffect(() => {
    const html = document.documentElement;
    html.lang = language;
    html.dir = language === 'ar' ? 'rtl' : 'ltr';
  }, [language]);

  // تغيير اللغة
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('language', lang);
    
    // تحديث URL إذا لزم الأمر
    const newPath = pathname.replace(/^\/(ar|en)/, `/${lang}`);
    if (newPath !== pathname) {
      router.push(newPath);
    }
  };

  // ترجمة النصوص
  const t = (key: string, params?: Record<string, string | number>): string => {
    let translation = translations[language][key as keyof typeof translations[typeof language]] || key;
    
    // استبدال المعاملات
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        translation = translation.replace(`{{${paramKey}}}`, String(value));
      });
    }
    
    return translation;
  };

  // تنسيق الأرقام
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US').format(num);
  };

  // تنسيق العملة
  const formatCurrency = (amount: number, currency: string): string => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // تنسيق التاريخ
  const formatDate = (date: Date | string): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(dateObj);
  };

  // تنسيق الوقت
  const formatTime = (date: Date | string): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(dateObj);
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    isRTL: language === 'ar',
    t,
    formatNumber,
    formatCurrency,
    formatDate,
    formatTime,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

// خطاف استخدام سياق اللغة
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
