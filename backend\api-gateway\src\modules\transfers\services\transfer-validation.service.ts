import {
  Injectable,
  BadRequestException,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../../../shared/redis/redis.service';
import { LoggerService } from '../../../shared/logger/logger.service';

// DTOs
import { CreateTransferDto, TransferType, TransferPurpose } from '../dto/create-transfer.dto';
import { TransferQuoteDto } from '../dto/transfer-quote.dto';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  riskScore: number;
}

export interface ComplianceCheck {
  amlCheck: boolean;
  sanctionCheck: boolean;
  kycStatus: boolean;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  requiresManualReview: boolean;
}

@Injectable()
export class TransferValidationService {
  private readonly maxDailyAmount: number;
  private readonly maxMonthlyAmount: number;
  private readonly maxSingleTransfer: number;
  private readonly restrictedCountries: Set<string>;
  private readonly highRiskCountries: Set<string>;

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext('TransferValidationService');
    
    // تحميل الإعدادات من متغيرات البيئة
    this.maxDailyAmount = this.configService.get<number>('MAX_DAILY_TRANSFER', 10000);
    this.maxMonthlyAmount = this.configService.get<number>('MAX_MONTHLY_TRANSFER', 50000);
    this.maxSingleTransfer = this.configService.get<number>('MAX_SINGLE_TRANSFER', 5000);
    
    // البلدان المحظورة والعالية المخاطر
    this.restrictedCountries = new Set(['IR', 'KP', 'SY', 'AF']); // إيران، كوريا الشمالية، سوريا، أفغانستان
    this.highRiskCountries = new Set(['PK', 'BD', 'MM', 'LK', 'NP']); // باكستان، بنغلاديش، ميانمار، سريلانكا، نيبال
  }

  /**
   * التحقق من صحة طلب عرض السعر
   */
  async validateQuoteRequest(quoteDto: TransferQuoteDto): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let riskScore = 0;

    // التحقق من المبلغ
    if (quoteDto.amount <= 0) {
      errors.push('المبلغ يجب أن يكون أكبر من صفر');
    }

    if (quoteDto.amount > this.maxSingleTransfer) {
      errors.push(`المبلغ يتجاوز الحد الأقصى المسموح (${this.maxSingleTransfer})`);
    }

    // التحقق من العملات
    if (!this.isCurrencySupported(quoteDto.fromCurrency)) {
      errors.push(`عملة الإرسال ${quoteDto.fromCurrency} غير مدعومة`);
    }

    if (!this.isCurrencySupported(quoteDto.toCurrency)) {
      errors.push(`عملة الاستلام ${quoteDto.toCurrency} غير مدعومة`);
    }

    // التحقق من البلد
    if (this.restrictedCountries.has(quoteDto.destinationCountry)) {
      errors.push('التحويل إلى هذا البلد غير مسموح');
      riskScore += 100;
    }

    if (this.highRiskCountries.has(quoteDto.destinationCountry)) {
      warnings.push('هذا البلد يتطلب فحص إضافي');
      riskScore += 30;
    }

    // التحقق من نوع التحويل
    if (!this.isTransferTypeSupported(quoteDto.transferType, quoteDto.destinationCountry)) {
      errors.push('نوع التحويل غير مدعوم في هذا البلد');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      riskScore,
    };
  }

  /**
   * التحقق من صحة طلب التحويل
   */
  async validateTransferRequest(
    userId: string,
    transferDto: CreateTransferDto,
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let riskScore = 0;

    try {
      // التحقق الأساسي من البيانات
      const basicValidation = await this.validateQuoteRequest({
        amount: transferDto.amount,
        fromCurrency: transferDto.fromCurrency,
        toCurrency: transferDto.toCurrency,
        destinationCountry: transferDto.destinationCountry,
        transferType: transferDto.transferType,
      });

      errors.push(...basicValidation.errors);
      warnings.push(...basicValidation.warnings);
      riskScore += basicValidation.riskScore;

      // التحقق من حدود المستخدم
      const limitsCheck = await this.checkUserLimits(userId, transferDto.amount, transferDto.fromCurrency);
      if (!limitsCheck.isValid) {
        errors.push(...limitsCheck.errors);
        riskScore += 20;
      }

      // التحقق من KYC
      const kycStatus = await this.checkKYCStatus(userId);
      if (!kycStatus.isVerified) {
        if (transferDto.amount > kycStatus.allowedAmount) {
          errors.push('يتطلب التحقق من الهوية لهذا المبلغ');
        } else {
          warnings.push('يُنصح بإكمال التحقق من الهوية');
          riskScore += 10;
        }
      }

      // التحقق من AML/CFT
      const complianceCheck = await this.performComplianceCheck(userId, transferDto);
      if (complianceCheck.requiresManualReview) {
        warnings.push('التحويل يتطلب مراجعة يدوية');
        riskScore += 50;
      }

      // التحقق من بيانات المستلم
      const recipientValidation = this.validateRecipientData(transferDto.recipient);
      errors.push(...recipientValidation.errors);
      warnings.push(...recipientValidation.warnings);

      // التحقق من البيانات البنكية (إذا كانت مطلوبة)
      if (transferDto.transferType === TransferType.BANK_DEPOSIT && transferDto.bankDetails) {
        const bankValidation = this.validateBankDetails(transferDto.bankDetails);
        errors.push(...bankValidation.errors);
      }

      // التحقق من الأنماط المشبوهة
      const patternCheck = await this.checkSuspiciousPatterns(userId, transferDto);
      if (patternCheck.isSuspicious) {
        warnings.push('تم اكتشاف نمط غير عادي');
        riskScore += patternCheck.riskScore;
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        riskScore,
      };
    } catch (error) {
      this.logger.error('خطأ في التحقق من صحة التحويل', error.stack, { userId, transferDto });
      throw new BadRequestException('فشل في التحقق من صحة البيانات');
    }
  }

  /**
   * التحقق من حدود المستخدم
   */
  async checkUserLimits(
    userId: string,
    amount: number,
    currency: string,
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      // التحقق من الحد اليومي
      const dailyUsage = await this.getDailyTransferAmount(userId, currency);
      if (dailyUsage + amount > this.maxDailyAmount) {
        errors.push(`تجاوز الحد اليومي المسموح (${this.maxDailyAmount} ${currency})`);
      }

      // التحقق من الحد الشهري
      const monthlyUsage = await this.getMonthlyTransferAmount(userId, currency);
      if (monthlyUsage + amount > this.maxMonthlyAmount) {
        errors.push(`تجاوز الحد الشهري المسموح (${this.maxMonthlyAmount} ${currency})`);
      }

      // التحقق من عدد التحويلات اليومية
      const dailyCount = await this.getDailyTransferCount(userId);
      if (dailyCount >= 10) {
        errors.push('تجاوز العدد المسموح من التحويلات اليومية');
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    } catch (error) {
      this.logger.error('خطأ في التحقق من حدود المستخدم', error.stack, { userId });
      return {
        isValid: false,
        errors: ['فشل في التحقق من الحدود'],
      };
    }
  }

  /**
   * التحقق من حالة KYC
   */
  async checkKYCStatus(userId: string): Promise<{
    isVerified: boolean;
    level: number;
    allowedAmount: number;
  }> {
    try {
      // TODO: جلب حالة KYC من قاعدة البيانات
      // هذا مثال وهمي
      const kycData = await this.getUserKYCData(userId);
      
      return {
        isVerified: kycData?.isVerified || false,
        level: kycData?.level || 1,
        allowedAmount: kycData?.level === 3 ? 50000 : kycData?.level === 2 ? 10000 : 1000,
      };
    } catch (error) {
      this.logger.error('خطأ في التحقق من KYC', error.stack, { userId });
      return {
        isVerified: false,
        level: 0,
        allowedAmount: 500,
      };
    }
  }

  /**
   * فحص الامتثال (AML/CFT)
   */
  async performComplianceCheck(
    userId: string,
    transferDto: CreateTransferDto,
  ): Promise<ComplianceCheck> {
    try {
      // فحص قوائم العقوبات
      const sanctionCheck = await this.checkSanctionsList(
        transferDto.recipient.firstName,
        transferDto.recipient.lastName,
        transferDto.destinationCountry,
      );

      // فحص AML
      const amlCheck = await this.performAMLCheck(userId, transferDto);

      // تحديد مستوى المخاطر
      let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
      let requiresManualReview = false;

      if (!sanctionCheck || transferDto.amount > 10000) {
        riskLevel = 'critical';
        requiresManualReview = true;
      } else if (this.highRiskCountries.has(transferDto.destinationCountry) || transferDto.amount > 5000) {
        riskLevel = 'high';
        requiresManualReview = true;
      } else if (transferDto.amount > 2000) {
        riskLevel = 'medium';
      }

      return {
        amlCheck,
        sanctionCheck,
        kycStatus: (await this.checkKYCStatus(userId)).isVerified,
        riskLevel,
        requiresManualReview,
      };
    } catch (error) {
      this.logger.error('خطأ في فحص الامتثال', error.stack, { userId });
      return {
        amlCheck: false,
        sanctionCheck: false,
        kycStatus: false,
        riskLevel: 'critical',
        requiresManualReview: true,
      };
    }
  }

  // Private Helper Methods

  private isCurrencySupported(currency: string): boolean {
    const supportedCurrencies = [
      'SAR', 'USD', 'EUR', 'GBP', 'AED', 'KWD', 'QAR', 'BHD', 'OMR',
      'EGP', 'JOD', 'LBP', 'INR', 'PKR', 'BDT', 'LKR', 'NPR'
    ];
    return supportedCurrencies.includes(currency.toUpperCase());
  }

  private isTransferTypeSupported(transferType: TransferType, country: string): boolean {
    // بعض البلدان لا تدعم أنواع معينة من التحويل
    const restrictions = {
      'AF': [TransferType.BANK_DEPOSIT], // أفغانستان - لا يدعم التحويل البنكي
      'MM': [TransferType.MOBILE_WALLET], // ميانمار - لا يدعم المحفظة الإلكترونية
    };

    const countryRestrictions = restrictions[country];
    return !countryRestrictions || !countryRestrictions.includes(transferType);
  }

  private validateRecipientData(recipient: any): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!recipient.firstName || recipient.firstName.length < 2) {
      errors.push('اسم المستلم الأول غير صحيح');
    }

    if (!recipient.lastName || recipient.lastName.length < 2) {
      errors.push('اسم المستلم الأخير غير صحيح');
    }

    if (!recipient.phone || !/^\+\d{10,15}$/.test(recipient.phone)) {
      errors.push('رقم هاتف المستلم غير صحيح');
    }

    if (recipient.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(recipient.email)) {
      warnings.push('البريد الإلكتروني للمستلم غير صحيح');
    }

    return { errors, warnings };
  }

  private validateBankDetails(bankDetails: any): { errors: string[] } {
    const errors: string[] = [];

    if (!bankDetails.bankName || bankDetails.bankName.length < 3) {
      errors.push('اسم البنك غير صحيح');
    }

    if (!bankDetails.accountNumber || bankDetails.accountNumber.length < 8) {
      errors.push('رقم الحساب البنكي غير صحيح');
    }

    return { errors };
  }

  private async getDailyTransferAmount(userId: string, currency: string): Promise<number> {
    const today = new Date().toISOString().split('T')[0];
    const key = `daily_transfer:${userId}:${currency}:${today}`;
    const amount = await this.redisService.get(key);
    return parseFloat(amount || '0');
  }

  private async getMonthlyTransferAmount(userId: string, currency: string): Promise<number> {
    const month = new Date().toISOString().substring(0, 7);
    const key = `monthly_transfer:${userId}:${currency}:${month}`;
    const amount = await this.redisService.get(key);
    return parseFloat(amount || '0');
  }

  private async getDailyTransferCount(userId: string): Promise<number> {
    const today = new Date().toISOString().split('T')[0];
    const key = `daily_count:${userId}:${today}`;
    const count = await this.redisService.get(key);
    return parseInt(count || '0');
  }

  private async getUserKYCData(userId: string): Promise<any> {
    // TODO: جلب بيانات KYC من قاعدة البيانات
    return {
      isVerified: true,
      level: 2,
    };
  }

  private async checkSanctionsList(
    firstName: string,
    lastName: string,
    country: string,
  ): Promise<boolean> {
    // TODO: فحص قوائم العقوبات الدولية
    // يمكن استخدام خدمات مثل World-Check أو Dow Jones
    return true;
  }

  private async performAMLCheck(userId: string, transferDto: CreateTransferDto): Promise<boolean> {
    // TODO: فحص AML متقدم
    return true;
  }

  private async checkSuspiciousPatterns(
    userId: string,
    transferDto: CreateTransferDto,
  ): Promise<{ isSuspicious: boolean; riskScore: number }> {
    // TODO: فحص الأنماط المشبوهة باستخدام ML
    return {
      isSuspicious: false,
      riskScore: 0,
    };
  }
}
