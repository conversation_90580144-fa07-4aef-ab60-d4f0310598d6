import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum ReportType {
  DAILY_SUMMARY = 'daily_summary',
  WEEKLY_SUMMARY = 'weekly_summary',
  MONTHLY_SUMMARY = 'monthly_summary',
  USER_ACTIVITY = 'user_activity',
  TRANSFER_ANALYSIS = 'transfer_analysis',
  PAYMENT_ANALYSIS = 'payment_analysis',
  SECURITY_REPORT = 'security_report',
  PERFORMANCE_REPORT = 'performance_report',
  CUSTOM = 'custom',
}

export enum ReportStatus {
  GENERATING = 'generating',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SCHEDULED = 'scheduled',
}

@Entity('reports')
@Index(['reportType', 'generatedAt'])
@Index(['status'])
@Index(['startDate', 'endDate'])
export class Report {
  @ApiProperty({ description: 'معرف التقرير' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'نوع التقرير', enum: ReportType })
  @Column({
    type: 'enum',
    enum: ReportType,
  })
  @Index()
  reportType: ReportType;

  @ApiProperty({ description: 'عنوان التقرير' })
  @Column({ length: 200 })
  title: string;

  @ApiProperty({ description: 'وصف التقرير', required: false })
  @Column('text', { nullable: true })
  description?: string;

  @ApiProperty({ description: 'تاريخ بداية البيانات' })
  @Column('date')
  @Index()
  startDate: Date;

  @ApiProperty({ description: 'تاريخ نهاية البيانات' })
  @Column('date')
  @Index()
  endDate: Date;

  @ApiProperty({ description: 'فلاتر التقرير', required: false })
  @Column('jsonb', { nullable: true })
  filters?: Record<string, any>;

  @ApiProperty({ description: 'بيانات التقرير' })
  @Column('jsonb')
  data: any;

  @ApiProperty({ description: 'ملخص التقرير', required: false })
  @Column('jsonb', { nullable: true })
  summary?: {
    totalRecords: number;
    keyMetrics: Record<string, any>;
    insights: string[];
    recommendations: string[];
  };

  @ApiProperty({ description: 'حالة التقرير', enum: ReportStatus })
  @Column({
    type: 'enum',
    enum: ReportStatus,
    default: ReportStatus.GENERATING,
  })
  @Index()
  status: ReportStatus;

  @ApiProperty({ description: 'تاريخ إنشاء التقرير' })
  @Column('timestamp')
  @Index()
  generatedAt: Date;

  @ApiProperty({ description: 'معرف المستخدم المنشئ', required: false })
  @Column('uuid', { nullable: true })
  generatedBy?: string;

  @ApiProperty({ description: 'مدة إنشاء التقرير بالميلي ثانية', required: false })
  @Column('int', { nullable: true })
  generationDuration?: number;

  @ApiProperty({ description: 'حجم البيانات بالبايت', required: false })
  @Column('bigint', { nullable: true })
  dataSize?: number;

  @ApiProperty({ description: 'رسالة الخطأ في حالة الفشل', required: false })
  @Column('text', { nullable: true })
  errorMessage?: string;

  @ApiProperty({ description: 'معلومات إضافية', required: false })
  @Column('jsonb', { nullable: true })
  metadata?: {
    format: string;
    version: string;
    exportUrl?: string;
    charts?: any[];
    tables?: any[];
  };

  @ApiProperty({ description: 'تصنيفات التقرير', required: false })
  @Column('simple-array', { nullable: true })
  tags?: string[];

  @ApiProperty({ description: 'هل التقرير عام' })
  @Column('boolean', { default: false })
  isPublic: boolean;

  @ApiProperty({ description: 'تاريخ انتهاء صلاحية التقرير', required: false })
  @Column('timestamp', { nullable: true })
  expiresAt?: Date;

  @ApiProperty({ description: 'عدد مرات التحميل' })
  @Column('int', { default: 0 })
  downloadCount: number;

  @ApiProperty({ description: 'آخر تاريخ تحميل', required: false })
  @Column('timestamp', { nullable: true })
  lastDownloadedAt?: Date;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'تاريخ آخر تحديث' })
  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  isCompleted(): boolean {
    return this.status === ReportStatus.COMPLETED;
  }

  isFailed(): boolean {
    return this.status === ReportStatus.FAILED;
  }

  isGenerating(): boolean {
    return this.status === ReportStatus.GENERATING;
  }

  isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  markAsCompleted(data: any, summary?: any): void {
    this.status = ReportStatus.COMPLETED;
    this.data = data;
    this.summary = summary;
    this.generatedAt = new Date();
  }

  markAsFailed(errorMessage: string): void {
    this.status = ReportStatus.FAILED;
    this.errorMessage = errorMessage;
  }

  incrementDownloadCount(): void {
    this.downloadCount++;
    this.lastDownloadedAt = new Date();
  }

  getDateRange(): string {
    const start = this.startDate.toLocaleDateString('ar-SA');
    const end = this.endDate.toLocaleDateString('ar-SA');
    return `${start} - ${end}`;
  }

  getDurationInDays(): number {
    const diffTime = Math.abs(this.endDate.getTime() - this.startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getGenerationDurationInSeconds(): number {
    return this.generationDuration ? this.generationDuration / 1000 : 0;
  }

  getDataSizeInMB(): number {
    return this.dataSize ? this.dataSize / (1024 * 1024) : 0;
  }

  addTag(tag: string): void {
    if (!this.tags) {
      this.tags = [];
    }
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  removeTag(tag: string): void {
    if (this.tags) {
      this.tags = this.tags.filter(t => t !== tag);
    }
  }

  hasTag(tag: string): boolean {
    return this.tags ? this.tags.includes(tag) : false;
  }

  getKeyMetric(key: string): any {
    return this.summary?.keyMetrics ? this.summary.keyMetrics[key] : undefined;
  }

  setKeyMetric(key: string, value: any): void {
    if (!this.summary) {
      this.summary = {
        totalRecords: 0,
        keyMetrics: {},
        insights: [],
        recommendations: [],
      };
    }
    if (!this.summary.keyMetrics) {
      this.summary.keyMetrics = {};
    }
    this.summary.keyMetrics[key] = value;
  }

  addInsight(insight: string): void {
    if (!this.summary) {
      this.summary = {
        totalRecords: 0,
        keyMetrics: {},
        insights: [],
        recommendations: [],
      };
    }
    if (!this.summary.insights.includes(insight)) {
      this.summary.insights.push(insight);
    }
  }

  addRecommendation(recommendation: string): void {
    if (!this.summary) {
      this.summary = {
        totalRecords: 0,
        keyMetrics: {},
        insights: [],
        recommendations: [],
      };
    }
    if (!this.summary.recommendations.includes(recommendation)) {
      this.summary.recommendations.push(recommendation);
    }
  }

  getReportTypeLabel(): string {
    const labels = {
      [ReportType.DAILY_SUMMARY]: 'ملخص يومي',
      [ReportType.WEEKLY_SUMMARY]: 'ملخص أسبوعي',
      [ReportType.MONTHLY_SUMMARY]: 'ملخص شهري',
      [ReportType.USER_ACTIVITY]: 'نشاط المستخدمين',
      [ReportType.TRANSFER_ANALYSIS]: 'تحليل التحويلات',
      [ReportType.PAYMENT_ANALYSIS]: 'تحليل المدفوعات',
      [ReportType.SECURITY_REPORT]: 'تقرير الأمان',
      [ReportType.PERFORMANCE_REPORT]: 'تقرير الأداء',
      [ReportType.CUSTOM]: 'تقرير مخصص',
    };
    return labels[this.reportType] || this.reportType;
  }

  toSummary(): any {
    return {
      id: this.id,
      title: this.title,
      reportType: this.reportType,
      reportTypeLabel: this.getReportTypeLabel(),
      status: this.status,
      dateRange: this.getDateRange(),
      durationInDays: this.getDurationInDays(),
      generatedAt: this.generatedAt,
      downloadCount: this.downloadCount,
      dataSize: this.getDataSizeInMB(),
      isExpired: this.isExpired(),
      tags: this.tags,
    };
  }
}
