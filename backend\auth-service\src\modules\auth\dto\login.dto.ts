import { ApiProperty } from '@nestjs/swagger';
import {
  Is<PERSON><PERSON>,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  IsOptional,
} from 'class-validator';

export class LoginDto {
  @ApiProperty({
    description: 'البريد الإلكتروني',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'البريد الإلكتروني غير صالح' })
  @MaxLength(255, { message: 'البريد الإلكتروني طويل جداً' })
  email: string;

  @ApiProperty({
    description: 'كلمة المرور',
    example: 'MySecurePassword123!',
  })
  @IsString({ message: 'كلمة المرور يجب أن تكون نص' })
  @MinLength(1, { message: 'كلمة المرور مطلوبة' })
  @MaxLength(128, { message: 'كلمة المرور طويلة جداً' })
  password: string;

  @ApiProperty({
    description: 'تذكرني (البقاء مسجل الدخول)',
    example: false,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تذكرني يجب أن يكون قيمة منطقية' })
  rememberMe?: boolean = false;
}

export class VerifyEmailDto {
  @ApiProperty({
    description: 'رمز التحقق من البريد الإلكتروني',
    example: 'abc123def456ghi789',
  })
  @IsString({ message: 'رمز التحقق يجب أن يكون نص' })
  @MinLength(1, { message: 'رمز التحقق مطلوب' })
  token: string;
}

export class VerifyPhoneDto {
  @ApiProperty({
    description: 'رقم الهاتف',
    example: '+966501234567',
  })
  @IsString({ message: 'رقم الهاتف يجب أن يكون نص' })
  phone: string;

  @ApiProperty({
    description: 'رمز التحقق المرسل للهاتف',
    example: '123456',
  })
  @IsString({ message: 'رمز التحقق يجب أن يكون نص' })
  @MinLength(6, { message: 'رمز التحقق يجب أن يكون 6 أرقام' })
  @MaxLength(6, { message: 'رمز التحقق يجب أن يكون 6 أرقام' })
  code: string;
}

export class ResendVerificationDto {
  @ApiProperty({
    description: 'البريد الإلكتروني',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'البريد الإلكتروني غير صالح' })
  email: string;

  @ApiProperty({
    description: 'نوع التحقق',
    example: 'email',
    enum: ['email', 'phone'],
  })
  @IsString({ message: 'نوع التحقق يجب أن يكون نص' })
  type: 'email' | 'phone';
}

export class RefreshTokenDto {
  @ApiProperty({
    description: 'رمز التحديث',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString({ message: 'رمز التحديث يجب أن يكون نص' })
  @MinLength(1, { message: 'رمز التحديث مطلوب' })
  refreshToken: string;
}
