"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeModulesEngineHost = exports.NodePackageDoesNotSupportSchematics = void 0;
const core_1 = require("@angular-devkit/core");
const path_1 = require("path");
const export_ref_1 = require("./export-ref");
const file_system_engine_host_base_1 = require("./file-system-engine-host-base");
const file_system_utility_1 = require("./file-system-utility");
class NodePackageDoesNotSupportSchematics extends core_1.BaseException {
    constructor(name) {
        super(`Package ${JSON.stringify(name)} was found but does not support schematics.`);
    }
}
exports.NodePackageDoesNotSupportSchematics = NodePackageDoesNotSupportSchematics;
/**
 * A simple EngineHost that uses NodeModules to resolve collections.
 */
class NodeModulesEngineHost extends file_system_engine_host_base_1.FileSystemEngineHostBase {
    paths;
    constructor(paths) {
        super();
        this.paths = paths;
    }
    resolve(name, requester, references = new Set()) {
        // Keep track of the package requesting the schematic, in order to avoid infinite recursion
        if (requester) {
            if (references.has(requester)) {
                references.add(requester);
                throw new Error('Circular schematic reference detected: ' + JSON.stringify(Array.from(references)));
            }
            else {
                references.add(requester);
            }
        }
        const relativeBase = requester ? (0, path_1.dirname)(requester) : process.cwd();
        let collectionPath = undefined;
        if (name.startsWith('.')) {
            name = (0, path_1.resolve)(relativeBase, name);
        }
        const resolveOptions = {
            paths: requester ? [(0, path_1.dirname)(requester), ...(this.paths || [])] : this.paths,
        };
        // Try to resolve as a package
        try {
            const packageJsonPath = require.resolve((0, path_1.join)(name, 'package.json'), resolveOptions);
            const { schematics } = require(packageJsonPath);
            if (!schematics || typeof schematics !== 'string') {
                throw new NodePackageDoesNotSupportSchematics(name);
            }
            // If this is a relative path to the collection, then create the collection
            // path in relation to the package path
            if (schematics.startsWith('.')) {
                const packageDirectory = (0, path_1.dirname)(packageJsonPath);
                collectionPath = (0, path_1.resolve)(packageDirectory, schematics);
            }
            // Otherwise treat this as a package, and recurse to find the collection path
            else {
                collectionPath = this.resolve(schematics, packageJsonPath, references);
            }
        }
        catch (e) {
            if (e.code !== 'MODULE_NOT_FOUND') {
                throw e;
            }
        }
        // If not a package, try to resolve as a file
        if (!collectionPath) {
            try {
                collectionPath = require.resolve(name, resolveOptions);
            }
            catch (e) {
                if (e.code !== 'MODULE_NOT_FOUND') {
                    throw e;
                }
            }
        }
        // If not a package or a file, error
        if (!collectionPath) {
            throw new file_system_engine_host_base_1.CollectionCannotBeResolvedException(name);
        }
        return collectionPath;
    }
    _resolveCollectionPath(name, requester) {
        const collectionPath = this.resolve(name, requester);
        (0, file_system_utility_1.readJsonFile)(collectionPath);
        return collectionPath;
    }
    _resolveReferenceString(refString, parentPath, collectionDescription) {
        const ref = new export_ref_1.ExportStringRef(refString, parentPath);
        if (!ref.ref) {
            return null;
        }
        return { ref: ref.ref, path: ref.module };
    }
    _transformCollectionDescription(name, desc) {
        if (!desc.schematics || typeof desc.schematics != 'object') {
            throw new file_system_engine_host_base_1.CollectionMissingSchematicsMapException(name);
        }
        return {
            ...desc,
            name,
        };
    }
    _transformSchematicDescription(name, _collection, desc) {
        if (!desc.factoryFn || !desc.path || !desc.description) {
            throw new file_system_engine_host_base_1.SchematicMissingFieldsException(name);
        }
        return desc;
    }
}
exports.NodeModulesEngineHost = NodeModulesEngineHost;
