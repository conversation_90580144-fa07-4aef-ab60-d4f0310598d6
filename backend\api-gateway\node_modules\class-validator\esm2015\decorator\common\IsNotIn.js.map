{"version": 3, "file": "IsNotIn.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsNotIn.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,SAAS,GAAG,SAAS,CAAC;AAEnC;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,KAAc,EAAE,cAAkC;IACxE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,aAAa,KAAK,KAAK,CAAC,CAAC;AAC1G,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,MAAsB,EAAE,iBAAqC;IACnF,OAAO,UAAU,CACf;QACE,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACxE,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,mEAAmE,EAC9F,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_NOT_IN = 'isNotIn';\n\n/**\n * Checks if given value not in a array of allowed values.\n */\nexport function isNotIn(value: unknown, possibleValues: readonly unknown[]): boolean {\n  return !Array.isArray(possibleValues) || !possibleValues.some(possibleValue => possibleValue === value);\n}\n\n/**\n * Checks if given value not in a array of allowed values.\n */\nexport function IsNotIn(values: readonly any[], validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_NOT_IN,\n      constraints: [values],\n      validator: {\n        validate: (value, args): boolean => isNotIn(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property should not be one of the following values: $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}