"""
Financial Reports Service
========================
خدمة التقارير المالية للوحة الإدارة
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
import json

import asyncpg
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class ReportType(Enum):
    """أنواع التقارير"""
    REVENUE = "revenue"
    TRANSACTIONS = "transactions"
    COMMISSIONS = "commissions"
    AGENTS = "agents"
    USERS = "users"
    COMPLIANCE = "compliance"


class ReportFormat(Enum):
    """تنسيقات التقارير"""
    JSON = "json"
    CSV = "csv"
    PDF = "pdf"
    EXCEL = "excel"


@dataclass
class RevenueReport:
    """تقرير الإيرادات"""
    period_start: date
    period_end: date
    total_revenue: Decimal
    transaction_fees: Decimal
    commission_expenses: Decimal
    net_profit: Decimal
    transaction_count: int
    average_transaction_value: Decimal
    growth_rate: Decimal
    daily_breakdown: List[Dict[str, Any]]


@dataclass
class TransactionReport:
    """تقرير المعاملات"""
    period_start: date
    period_end: date
    total_transactions: int
    successful_transactions: int
    failed_transactions: int
    pending_transactions: int
    success_rate: Decimal
    total_volume: Decimal
    average_amount: Decimal
    transaction_types: Dict[str, int]
    hourly_distribution: List[Dict[str, Any]]


class FinancialReports:
    """خدمة التقارير المالية"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Report cache
        self.report_cache = {}
        self.cache_duration = 1800  # 30 minutes
        
        # Report generation statistics
        self.reports_generated = 0
        self.cache_hits = 0
    
    async def generate_revenue_report(
        self,
        start_date: date,
        end_date: date,
        group_by: str = "day",
        include_projections: bool = False
    ) -> Dict[str, Any]:
        """إنشاء تقرير الإيرادات"""
        try:
            logger.info(f"💰 Generating revenue report from {start_date} to {end_date}")
            
            # Check cache
            cache_key = f"revenue_{start_date}_{end_date}_{group_by}"
            if self._is_cached(cache_key):
                self.cache_hits += 1
                return self.report_cache[cache_key]['data']
            
            async with self.db_connection.get_connection() as conn:
                # Main revenue query
                revenue_query = """
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as transaction_count,
                        SUM(amount) as total_volume,
                        SUM(fee_amount) as total_fees,
                        AVG(amount) as avg_amount,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_count,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
                    FROM transactions 
                    WHERE DATE(created_at) BETWEEN $1 AND $2
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """
                
                revenue_data = await conn.fetch(revenue_query, start_date, end_date)
                
                # Commission expenses query
                commission_query = """
                    SELECT 
                        DATE(created_at) as date,
                        SUM(commission_amount) as total_commissions,
                        COUNT(*) as commission_count
                    FROM agent_commissions 
                    WHERE DATE(created_at) BETWEEN $1 AND $2
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """
                
                commission_data = await conn.fetch(commission_query, start_date, end_date)
                
                # Create commission lookup
                commission_lookup = {
                    row['date']: {
                        'total_commissions': float(row['total_commissions']),
                        'commission_count': row['commission_count']
                    }
                    for row in commission_data
                }
                
                # Calculate totals
                total_revenue = sum(float(row['total_fees']) for row in revenue_data)
                total_commissions = sum(float(row['total_commissions']) for row in commission_data)
                net_profit = total_revenue - total_commissions
                total_transactions = sum(row['transaction_count'] for row in revenue_data)
                total_volume = sum(float(row['total_volume']) for row in revenue_data)
                
                # Calculate growth rate (compare with previous period)
                prev_start = start_date - (end_date - start_date + timedelta(days=1))
                prev_end = start_date - timedelta(days=1)
                
                prev_revenue_query = """
                    SELECT COALESCE(SUM(fee_amount), 0) as prev_revenue
                    FROM transactions 
                    WHERE DATE(created_at) BETWEEN $1 AND $2
                    AND status = 'completed'
                """
                
                prev_revenue = await conn.fetchval(prev_revenue_query, prev_start, prev_end) or 0
                growth_rate = ((total_revenue - float(prev_revenue)) / max(float(prev_revenue), 1)) * 100
                
                # Build daily breakdown
                daily_breakdown = []
                for row in revenue_data:
                    date_str = row['date'].isoformat()
                    commission_info = commission_lookup.get(row['date'], {
                        'total_commissions': 0,
                        'commission_count': 0
                    })
                    
                    daily_breakdown.append({
                        'date': date_str,
                        'transaction_count': row['transaction_count'],
                        'total_volume': float(row['total_volume']),
                        'total_fees': float(row['total_fees']),
                        'total_commissions': commission_info['total_commissions'],
                        'net_profit': float(row['total_fees']) - commission_info['total_commissions'],
                        'avg_amount': float(row['avg_amount']),
                        'success_rate': (row['successful_count'] / max(row['transaction_count'], 1)) * 100
                    })
                
                # Add projections if requested
                projections = None
                if include_projections:
                    projections = await self._calculate_revenue_projections(
                        conn, daily_breakdown, end_date
                    )
                
                report = {
                    'report_type': 'revenue',
                    'period': {
                        'start_date': start_date.isoformat(),
                        'end_date': end_date.isoformat(),
                        'days': (end_date - start_date).days + 1
                    },
                    'summary': {
                        'total_revenue': total_revenue,
                        'total_commissions': total_commissions,
                        'net_profit': net_profit,
                        'total_transactions': total_transactions,
                        'total_volume': total_volume,
                        'average_transaction_value': total_volume / max(total_transactions, 1),
                        'growth_rate': growth_rate,
                        'profit_margin': (net_profit / max(total_revenue, 1)) * 100
                    },
                    'daily_breakdown': daily_breakdown,
                    'projections': projections,
                    'generated_at': datetime.now().isoformat()
                }
                
                # Cache the report
                self._cache_report(cache_key, report)
                self.reports_generated += 1
                
                logger.info(f"✅ Revenue report generated successfully")
                return report
                
        except Exception as e:
            logger.error(f"❌ Failed to generate revenue report: {e}")
            return {'error': str(e)}
    
    async def generate_transaction_report(
        self,
        start_date: date,
        end_date: date,
        transaction_type: str = None,
        agent_id: str = None
    ) -> Dict[str, Any]:
        """إنشاء تقرير المعاملات"""
        try:
            logger.info(f"📊 Generating transaction report from {start_date} to {end_date}")
            
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = ["DATE(created_at) BETWEEN $1 AND $2"]
                params = [start_date, end_date]
                
                if transaction_type:
                    params.append(transaction_type)
                    where_conditions.append(f"type = ${len(params)}")
                
                if agent_id:
                    params.append(agent_id)
                    where_conditions.append(f"agent_id = ${len(params)}")
                
                where_clause = " AND ".join(where_conditions)
                
                # Main transaction statistics
                stats_query = f"""
                    SELECT 
                        COUNT(*) as total_transactions,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_transactions,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_volume,
                        AVG(CASE WHEN status = 'completed' THEN amount END) as avg_amount,
                        MIN(amount) as min_amount,
                        MAX(amount) as max_amount
                    FROM transactions 
                    WHERE {where_clause}
                """
                
                stats = await conn.fetchrow(stats_query, *params)
                
                # Transaction types breakdown
                types_query = f"""
                    SELECT 
                        type,
                        COUNT(*) as count,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as volume,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_count
                    FROM transactions 
                    WHERE {where_clause}
                    GROUP BY type
                    ORDER BY count DESC
                """
                
                types_data = await conn.fetch(types_query, *params)
                
                # Hourly distribution
                hourly_query = f"""
                    SELECT 
                        EXTRACT(HOUR FROM created_at) as hour,
                        COUNT(*) as transaction_count,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as volume
                    FROM transactions 
                    WHERE {where_clause}
                    GROUP BY EXTRACT(HOUR FROM created_at)
                    ORDER BY hour
                """
                
                hourly_data = await conn.fetch(hourly_query, *params)
                
                # Daily trend
                daily_query = f"""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as transaction_count,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_count,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as volume
                    FROM transactions 
                    WHERE {where_clause}
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """
                
                daily_data = await conn.fetch(daily_query, *params)
                
                # Calculate success rate
                success_rate = (stats['successful_transactions'] / max(stats['total_transactions'], 1)) * 100
                
                report = {
                    'report_type': 'transactions',
                    'period': {
                        'start_date': start_date.isoformat(),
                        'end_date': end_date.isoformat()
                    },
                    'filters': {
                        'transaction_type': transaction_type,
                        'agent_id': agent_id
                    },
                    'summary': {
                        'total_transactions': stats['total_transactions'],
                        'successful_transactions': stats['successful_transactions'],
                        'failed_transactions': stats['failed_transactions'],
                        'pending_transactions': stats['pending_transactions'],
                        'success_rate': success_rate,
                        'total_volume': float(stats['total_volume'] or 0),
                        'average_amount': float(stats['avg_amount'] or 0),
                        'min_amount': float(stats['min_amount'] or 0),
                        'max_amount': float(stats['max_amount'] or 0)
                    },
                    'transaction_types': [
                        {
                            'type': row['type'],
                            'count': row['count'],
                            'volume': float(row['volume']),
                            'successful_count': row['successful_count'],
                            'success_rate': (row['successful_count'] / max(row['count'], 1)) * 100
                        }
                        for row in types_data
                    ],
                    'hourly_distribution': [
                        {
                            'hour': int(row['hour']),
                            'transaction_count': row['transaction_count'],
                            'volume': float(row['volume'])
                        }
                        for row in hourly_data
                    ],
                    'daily_trend': [
                        {
                            'date': row['date'].isoformat(),
                            'transaction_count': row['transaction_count'],
                            'successful_count': row['successful_count'],
                            'volume': float(row['volume']),
                            'success_rate': (row['successful_count'] / max(row['transaction_count'], 1)) * 100
                        }
                        for row in daily_data
                    ],
                    'generated_at': datetime.now().isoformat()
                }
                
                logger.info(f"✅ Transaction report generated successfully")
                return report
                
        except Exception as e:
            logger.error(f"❌ Failed to generate transaction report: {e}")
            return {'error': str(e)}
    
    async def generate_commission_report(
        self,
        start_date: date,
        end_date: date,
        agent_id: str = None,
        commission_type: str = None
    ) -> Dict[str, Any]:
        """إنشاء تقرير العمولات"""
        try:
            logger.info(f"💼 Generating commission report from {start_date} to {end_date}")
            
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = ["DATE(created_at) BETWEEN $1 AND $2"]
                params = [start_date, end_date]
                
                if agent_id:
                    params.append(agent_id)
                    where_conditions.append(f"agent_id = ${len(params)}")
                
                if commission_type:
                    params.append(commission_type)
                    where_conditions.append(f"commission_type = ${len(params)}")
                
                where_clause = " AND ".join(where_conditions)
                
                # Commission statistics
                stats_query = f"""
                    SELECT 
                        COUNT(*) as total_commissions,
                        SUM(commission_amount) as total_amount,
                        AVG(commission_amount) as avg_amount,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
                        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count,
                        SUM(CASE WHEN status = 'paid' THEN commission_amount ELSE 0 END) as paid_amount
                    FROM agent_commissions 
                    WHERE {where_clause}
                """
                
                stats = await conn.fetchrow(stats_query, *params)
                
                # Commission by type
                types_query = f"""
                    SELECT 
                        commission_type,
                        COUNT(*) as count,
                        SUM(commission_amount) as total_amount,
                        AVG(commission_amount) as avg_amount
                    FROM agent_commissions 
                    WHERE {where_clause}
                    GROUP BY commission_type
                    ORDER BY total_amount DESC
                """
                
                types_data = await conn.fetch(types_query, *params)
                
                # Top earning agents
                agents_query = f"""
                    SELECT 
                        ac.agent_id,
                        ap.agent_code,
                        ap.business_name,
                        COUNT(*) as commission_count,
                        SUM(ac.commission_amount) as total_earned,
                        AVG(ac.commission_amount) as avg_commission
                    FROM agent_commissions ac
                    JOIN agent_profiles ap ON ac.agent_id = ap.id
                    WHERE {where_clause}
                    GROUP BY ac.agent_id, ap.agent_code, ap.business_name
                    ORDER BY total_earned DESC
                    LIMIT 20
                """
                
                agents_data = await conn.fetch(agents_query, *params)
                
                # Monthly breakdown
                monthly_query = f"""
                    SELECT 
                        DATE_TRUNC('month', created_at) as month,
                        COUNT(*) as commission_count,
                        SUM(commission_amount) as total_amount
                    FROM agent_commissions 
                    WHERE {where_clause}
                    GROUP BY DATE_TRUNC('month', created_at)
                    ORDER BY month
                """
                
                monthly_data = await conn.fetch(monthly_query, *params)
                
                report = {
                    'report_type': 'commissions',
                    'period': {
                        'start_date': start_date.isoformat(),
                        'end_date': end_date.isoformat()
                    },
                    'filters': {
                        'agent_id': agent_id,
                        'commission_type': commission_type
                    },
                    'summary': {
                        'total_commissions': stats['total_commissions'],
                        'total_amount': float(stats['total_amount'] or 0),
                        'average_amount': float(stats['avg_amount'] or 0),
                        'pending_count': stats['pending_count'],
                        'approved_count': stats['approved_count'],
                        'paid_count': stats['paid_count'],
                        'paid_amount': float(stats['paid_amount'] or 0),
                        'payment_rate': (stats['paid_count'] / max(stats['total_commissions'], 1)) * 100
                    },
                    'commission_types': [
                        {
                            'type': row['commission_type'],
                            'count': row['count'],
                            'total_amount': float(row['total_amount']),
                            'average_amount': float(row['avg_amount'])
                        }
                        for row in types_data
                    ],
                    'top_agents': [
                        {
                            'agent_id': row['agent_id'],
                            'agent_code': row['agent_code'],
                            'business_name': row['business_name'],
                            'commission_count': row['commission_count'],
                            'total_earned': float(row['total_earned']),
                            'average_commission': float(row['avg_commission'])
                        }
                        for row in agents_data
                    ],
                    'monthly_breakdown': [
                        {
                            'month': row['month'].strftime('%Y-%m'),
                            'commission_count': row['commission_count'],
                            'total_amount': float(row['total_amount'])
                        }
                        for row in monthly_data
                    ],
                    'generated_at': datetime.now().isoformat()
                }
                
                logger.info(f"✅ Commission report generated successfully")
                return report
                
        except Exception as e:
            logger.error(f"❌ Failed to generate commission report: {e}")
            return {'error': str(e)}
    
    async def export_report(
        self,
        report_data: Dict[str, Any],
        format: ReportFormat = ReportFormat.JSON
    ) -> bytes:
        """تصدير التقرير بالتنسيق المطلوب"""
        try:
            logger.info(f"📤 Exporting report in {format.value} format")
            
            if format == ReportFormat.JSON:
                return json.dumps(report_data, indent=2, default=str).encode('utf-8')
            
            elif format == ReportFormat.CSV:
                return await self._export_to_csv(report_data)
            
            elif format == ReportFormat.PDF:
                return await self._export_to_pdf(report_data)
            
            elif format == ReportFormat.EXCEL:
                return await self._export_to_excel(report_data)
            
            else:
                raise ValueError(f"Unsupported format: {format.value}")
                
        except Exception as e:
            logger.error(f"❌ Failed to export report: {e}")
            raise
    
    # Helper methods
    def _is_cached(self, cache_key: str) -> bool:
        """فحص إذا كان التقرير محفوظ في الذاكرة المؤقتة"""
        if cache_key not in self.report_cache:
            return False
        
        cached_report = self.report_cache[cache_key]
        age = (datetime.now() - cached_report['timestamp']).seconds
        
        return age < self.cache_duration
    
    def _cache_report(self, cache_key: str, report_data: Dict[str, Any]):
        """حفظ التقرير في الذاكرة المؤقتة"""
        self.report_cache[cache_key] = {
            'data': report_data,
            'timestamp': datetime.now()
        }
    
    async def _calculate_revenue_projections(
        self,
        conn,
        daily_breakdown: List[Dict[str, Any]],
        end_date: date
    ) -> Dict[str, Any]:
        """حساب توقعات الإيرادات"""
        # Simple projection based on recent trends
        if len(daily_breakdown) < 7:
            return None
        
        # Calculate average daily revenue for last 7 days
        recent_days = daily_breakdown[-7:]
        avg_daily_revenue = sum(day['total_fees'] for day in recent_days) / 7
        
        # Project next 30 days
        projected_monthly_revenue = avg_daily_revenue * 30
        
        return {
            'next_30_days': {
                'projected_revenue': projected_monthly_revenue,
                'confidence': 'medium',
                'based_on_days': 7
            }
        }
    
    async def _export_to_csv(self, report_data: Dict[str, Any]) -> bytes:
        """تصدير التقرير إلى CSV"""
        # Implementation would go here
        return b"CSV export not implemented yet"
    
    async def _export_to_pdf(self, report_data: Dict[str, Any]) -> bytes:
        """تصدير التقرير إلى PDF"""
        # Implementation would go here
        return b"PDF export not implemented yet"
    
    async def _export_to_excel(self, report_data: Dict[str, Any]) -> bytes:
        """تصدير التقرير إلى Excel"""
        # Implementation would go here
        return b"Excel export not implemented yet"
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات خدمة التقارير"""
        return {
            "reports_generated": self.reports_generated,
            "cache_hits": self.cache_hits,
            "cache_size": len(self.report_cache),
            "cache_hit_rate": (self.cache_hits / max(self.reports_generated, 1)) * 100
        }
