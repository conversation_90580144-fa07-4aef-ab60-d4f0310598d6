import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsDateString,
  IsEnum,
  <PERSON><PERSON>ength,
  <PERSON>Length,
  Matches,
} from 'class-validator';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
}

export enum MaritalStatus {
  SINGLE = 'single',
  MARRIED = 'married',
  DIVORCED = 'divorced',
  WIDOWED = 'widowed',
}

export class AddressDto {
  @ApiProperty({
    description: 'الشارع',
    example: 'شارع الملك فهد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الشارع يجب أن يكون نص' })
  street?: string;

  @ApiProperty({
    description: 'المدينة',
    example: 'الرياض',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'المدينة يجب أن تكون نص' })
  city?: string;

  @ApiProperty({
    description: 'المنطقة/الولاية',
    example: 'منطقة الرياض',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'المنطقة يجب أن تكون نص' })
  state?: string;

  @ApiProperty({
    description: 'الرمز البريدي',
    example: '12345',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الرمز البريدي يجب أن يكون نص' })
  postalCode?: string;

  @ApiProperty({
    description: 'البلد',
    example: 'SA',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'البلد يجب أن يكون نص' })
  country?: string;
}

export class UpdateProfileDto {
  @ApiProperty({
    description: 'الاسم الأول',
    example: 'أحمد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الاسم الأول يجب أن يكون نص' })
  @MinLength(2, { message: 'الاسم الأول يجب أن يكون حرفين على الأقل' })
  @MaxLength(50, { message: 'الاسم الأول يجب أن يكون أقل من 50 حرف' })
  firstName?: string;

  @ApiProperty({
    description: 'الاسم الأخير',
    example: 'محمد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الاسم الأخير يجب أن يكون نص' })
  @MinLength(2, { message: 'الاسم الأخير يجب أن يكون حرفين على الأقل' })
  @MaxLength(50, { message: 'الاسم الأخير يجب أن يكون أقل من 50 حرف' })
  lastName?: string;

  @ApiProperty({
    description: 'الاسم الأوسط',
    example: 'عبدالله',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الاسم الأوسط يجب أن يكون نص' })
  @MaxLength(50, { message: 'الاسم الأوسط يجب أن يكون أقل من 50 حرف' })
  middleName?: string;

  @ApiProperty({
    description: 'تاريخ الميلاد',
    example: '1990-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ الميلاد غير صحيح' })
  dateOfBirth?: string;

  @ApiProperty({
    description: 'الجنس',
    enum: Gender,
    example: Gender.MALE,
    required: false,
  })
  @IsOptional()
  @IsEnum(Gender, { message: 'الجنس غير صحيح' })
  gender?: Gender;

  @ApiProperty({
    description: 'الحالة الاجتماعية',
    enum: MaritalStatus,
    example: MaritalStatus.SINGLE,
    required: false,
  })
  @IsOptional()
  @IsEnum(MaritalStatus, { message: 'الحالة الاجتماعية غير صحيحة' })
  maritalStatus?: MaritalStatus;

  @ApiProperty({
    description: 'الجنسية',
    example: 'SA',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الجنسية يجب أن تكون نص' })
  @Matches(/^[A-Z]{2}$/, { message: 'الجنسية يجب أن تكون رمز بلد مكون من حرفين' })
  nationality?: string;

  @ApiProperty({
    description: 'المهنة',
    example: 'مهندس',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'المهنة يجب أن تكون نص' })
  @MaxLength(100, { message: 'المهنة يجب أن تكون أقل من 100 حرف' })
  occupation?: string;

  @ApiProperty({
    description: 'جهة العمل',
    example: 'شركة التقنية المتقدمة',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'جهة العمل يجب أن تكون نص' })
  @MaxLength(100, { message: 'جهة العمل يجب أن تكون أقل من 100 حرف' })
  employer?: string;

  @ApiProperty({
    description: 'الدخل الشهري',
    example: 15000,
    required: false,
  })
  @IsOptional()
  monthlyIncome?: number;

  @ApiProperty({
    description: 'مصدر الدخل',
    example: 'راتب',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'مصدر الدخل يجب أن يكون نص' })
  incomeSource?: string;

  @ApiProperty({
    description: 'العنوان',
    type: AddressDto,
    required: false,
  })
  @IsOptional()
  address?: AddressDto;

  @ApiProperty({
    description: 'رقم هاتف إضافي',
    example: '+966501234568',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الهاتف الإضافي يجب أن يكون نص' })
  alternativePhone?: string;

  @ApiProperty({
    description: 'اسم جهة الاتصال في حالات الطوارئ',
    example: 'فاطمة أحمد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'اسم جهة الاتصال يجب أن يكون نص' })
  emergencyContactName?: string;

  @ApiProperty({
    description: 'رقم هاتف جهة الاتصال في حالات الطوارئ',
    example: '+966501234569',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم هاتف جهة الاتصال يجب أن يكون نص' })
  emergencyContactPhone?: string;

  @ApiProperty({
    description: 'العلاقة بجهة الاتصال في حالات الطوارئ',
    example: 'زوجة',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'العلاقة يجب أن تكون نص' })
  emergencyContactRelation?: string;
}
