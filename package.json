{"name": "ws-transfir", "version": "1.0.0", "description": "نظام تحويل الأموال العالمي المتقدم", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:user\" \"npm run dev:transfer\"", "dev:gateway": "cd backend/api-gateway && npm run start:dev", "dev:auth": "cd backend/auth-service && npm run start:dev", "dev:user": "cd backend/user-service && npm run start:dev", "dev:transfer": "cd backend/transfer-service && npm run start:dev", "dev:wallet": "cd backend/wallet-service && npm run start:dev", "dev:web": "cd frontend/web-app && npm run dev", "dev:mobile": "cd frontend/mobile-app && flutter run", "build": "npm run build:backend && npm run build:frontend", "build:backend": "concurrently \"npm run build:gateway\" \"npm run build:auth\" \"npm run build:user\" \"npm run build:transfer\"", "build:gateway": "cd backend/api-gateway && npm run build", "build:auth": "cd backend/auth-service && npm run build", "build:user": "cd backend/user-service && npm run build", "build:transfer": "cd backend/transfer-service && npm run build", "build:wallet": "cd backend/wallet-service && npm run build", "build:frontend": "cd frontend/web-app && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "concurrently \"npm run test:gateway\" \"npm run test:auth\" \"npm run test:user\" \"npm run test:transfer\"", "test:gateway": "cd backend/api-gateway && npm run test", "test:auth": "cd backend/auth-service && npm run test", "test:user": "cd backend/user-service && npm run test", "test:transfer": "cd backend/transfer-service && npm run test", "test:wallet": "cd backend/wallet-service && npm run test", "test:frontend": "cd frontend/web-app && npm run test", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "npm run setup:backend && npm run setup:frontend", "setup:backend": "npm run setup:gateway && npm run setup:auth && npm run setup:user && npm run setup:transfer && npm run setup:wallet", "setup:gateway": "cd backend/api-gateway && npm install", "setup:auth": "cd backend/auth-service && npm install", "setup:user": "cd backend/user-service && npm install", "setup:transfer": "cd backend/transfer-service && npm install", "setup:wallet": "cd backend/wallet-service && npm install", "setup:frontend": "cd frontend/web-app && npm install", "migrate": "cd database && npm run migrate", "seed": "cd database && npm run seed"}, "keywords": ["money-transfer", "fintech", "microservices", "<PERSON><PERSON><PERSON>", "nextjs", "flutter", "ai", "blockchain"], "author": "WS Transfir Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "eslint": "^8.55.0", "prettier": "^3.1.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"cors": "^2.8.5", "express": "^5.1.0"}}