{"name": "ws-transfir", "version": "1.0.0", "description": "WS Transfir - Advanced Money Transfer System | نظام تحويل الأموال المتقدم", "main": "api-server.js", "scripts": {"start": "node api-server.js", "dev": "node start-simple.js", "simple": "node start-simple.js", "api": "node api-server.js", "frontend": "cd frontend/web-app && npm run dev", "full": "concurrently \"npm run api\" \"npm run frontend\"", "build": "npm run build:api && npm run build:frontend", "build:api": "echo 'API build completed'", "build:frontend": "cd frontend/web-app && npm run build", "install:all": "npm install && cd frontend/web-app && npm install", "setup": "npm run install:all && npm run check", "clean": "npm run clean:cache && npm run clean:modules", "clean:cache": "npm cache clean --force", "clean:modules": "rimraf node_modules frontend/web-app/node_modules", "lint": "eslint . --ext .js,.ts,.tsx --ignore-path .gitignore", "lint:fix": "eslint . --ext .js,.ts,.tsx --fix --ignore-path .gitignore", "format": "prettier --write . --ignore-path .gitignore", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "check": "node system-diagnostics.js", "status": "node system-status.js", "health": "curl -s http://localhost:3000/api/health || echo 'API not running'", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --remove-orphans", "dev:gateway": "cd backend/api-gateway && npm run start:dev", "dev:auth": "cd backend/auth-service && npm run start:dev", "dev:user": "cd backend/user-service && npm run start:dev", "dev:transfer": "cd backend/transfer-service && npm run start:dev", "dev:wallet": "cd backend/wallet-service && npm run start:dev", "dev:notification": "cd backend/notification-service && npm run start:dev", "dev:analytics": "cd backend/analytics-service && npm run start:dev", "dev:compliance": "cd backend/compliance-service && npm run start:dev", "dev:payment": "cd backend/payment-gateway-service && npm run start:dev"}, "keywords": ["money-transfer", "fintech", "payment-gateway", "remittance", "financial-services", "banking", "digital-wallet", "cross-border-payments", "compliance", "kyc", "aml", "microservices", "<PERSON><PERSON><PERSON>", "nextjs", "react", "typescript", "nodejs", "express", "api", "rest", "security", "authentication", "authorization"], "author": {"name": "WS Transfir Team", "email": "<EMAIL>", "url": "https://wstransfir.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/wstransfir/ws-transfir.git"}, "bugs": {"url": "https://github.com/wstransfir/ws-transfir/issues"}, "homepage": "https://wstransfir.com", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "os": ["win32", "darwin", "linux"], "cpu": ["x64", "arm64"], "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "morgan": "^1.10.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/multer": "^1.4.11", "@types/morgan": "^1.9.9", "concurrently": "^8.2.2", "eslint": "^8.56.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "jest": "^29.7.0", "supertest": "^6.3.3", "rimraf": "^5.0.5", "nodemon": "^3.0.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}