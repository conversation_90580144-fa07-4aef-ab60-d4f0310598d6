{"version": 3, "file": "DocParagraph.js", "sourceRoot": "", "sources": ["../../src/nodes/DocParagraph.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;AAE3D,qCAAsD;AACtD,uDAAwF;AAOxF;;;;GAIG;AACH;IAAkC,gCAAgB;IAChD;;;OAGG;IACH,sBAAmB,UAAmC,EAAE,UAAmC;QACzF,OAAA,MAAK,YAAC,UAAU,EAAE,UAAU,CAAC,SAAC;IAChC,CAAC;IAGD,sBAAW,8BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,SAAS,CAAC;QAC/B,CAAC;;;OAAA;IACH,mBAAC;AAAD,CAAC,AAbD,CAAkC,mCAAgB,GAajD;AAbY,oCAAY", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { DocNodeKind, type DocNode } from './DocNode';\r\nimport { DocNodeContainer, type IDocNodeContainerParameters } from './DocNodeContainer';\r\n\r\n/**\r\n * Constructor parameters for {@link DocParagraph}.\r\n */\r\nexport interface IDocParagraphParameters extends IDocNodeContainerParameters {}\r\n\r\n/**\r\n * Represents a paragraph of text, similar to a `<p>` element in HTML.\r\n * Like CommonMark, the TSDoc syntax uses blank lines to delineate paragraphs\r\n * instead of explicitly notating them.\r\n */\r\nexport class DocParagraph extends DocNodeContainer {\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocParagraphParameters, childNodes?: ReadonlyArray<DocNode>) {\r\n    super(parameters, childNodes);\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.Paragraph;\r\n  }\r\n}\r\n"]}