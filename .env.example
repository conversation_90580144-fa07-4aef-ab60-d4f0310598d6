# =================================
# WS Transfir Environment Variables
# =================================

# Environment
NODE_ENV=development
ENVIRONMENT=development

# Database Configuration
POSTGRES_USER=ws_user
POSTGRES_PASSWORD=ws_password_dev_123
POSTGRES_DB=ws_transfir
DATABASE_URL=postgresql://ws_user:ws_password_dev_123@localhost:5432/ws_transfir

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# MongoDB Configuration
MONGO_USER=ws_user
MONGO_PASSWORD=mongo_password_dev_123
MONGODB_URL=*************************************************************************

# Elasticsearch Configuration
ELASTIC_PASSWORD=elastic_password_dev_123
ELASTICSEARCH_URL=******************************************************

# RabbitMQ Configuration
RABBITMQ_USER=ws_user
RABBITMQ_PASSWORD=rabbitmq_password_dev_123
RABBITMQ_URL=amqp://ws_user:rabbitmq_password_dev_123@localhost:5672/ws_transfir

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-min-32-chars
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Encryption
ENCRYPTION_KEY=your-encryption-key-must-be-32-characters-long
API_KEY_SECRET=your-api-key-secret-for-internal-services

# CORS Configuration
CORS_ORIGIN=http://localhost:3100,http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
FILE_UPLOAD_MAX_SIZE=********
UPLOAD_PATH=./uploads

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=WS Transfir

# SMS Configuration
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Payment Gateway
PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
PAYMENT_GATEWAY_KEY=your-payment-gateway-api-key
PAYMENT_GATEWAY_SECRET=your-payment-gateway-secret

# Exchange Rate API
EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key
EXCHANGE_RATE_API_URL=https://api.exchangerate-api.com/v4/latest

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret-key

# Logging
LOG_LEVEL=debug
LOG_FILE_PATH=./logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key
CSRF_SECRET=your-csrf-secret-key

# Firebase Configuration (for mobile notifications)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY=your-firebase-private-key
FIREBASE_CLIENT_EMAIL=your-firebase-client-email

# Google Services
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Apple Services (for iOS)
APPLE_TEAM_ID=your-apple-team-id
APPLE_KEY_ID=your-apple-key-id
APPLE_PRIVATE_KEY=your-apple-private-key

# Monitoring & Analytics
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-google-analytics-id

# AI & ML Configuration
ML_MODEL_PATH=./models
FRAUD_DETECTION_THRESHOLD=0.7
AI_SERVICE_URL=http://localhost:8000

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3100
NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key-must-be-32-characters-long

# Mobile App Configuration
MOBILE_APP_STORE_URL=https://apps.apple.com/app/ws-transfir
MOBILE_PLAY_STORE_URL=https://play.google.com/store/apps/details?id=com.wstransfir

# API Versioning
API_VERSION=v1
API_PREFIX=/api

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000

# Session Configuration
SESSION_TIMEOUT=1800
SESSION_CLEANUP_INTERVAL=300

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=ws-transfir-backups
BACKUP_S3_REGION=us-east-1

# SSL Configuration
SSL_ENABLED=false
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Development Tools
DEBUG_MODE=true
SWAGGER_ENABLED=true
GRAPHQL_PLAYGROUND=true

# Testing
TEST_DATABASE_URL=postgresql://ws_user:ws_password_dev_123@localhost:5432/ws_transfir_test
TEST_REDIS_URL=redis://localhost:6379/1

# Docker Configuration
COMPOSE_PROJECT_NAME=ws-transfir
COMPOSE_FILE=docker-compose.yml

# Health Check URLs
HEALTH_CHECK_URL=http://localhost:3000/health
READINESS_CHECK_URL=http://localhost:3000/ready

# Microservices Ports
API_GATEWAY_PORT=3000
AUTH_SERVICE_PORT=3001
USER_SERVICE_PORT=3002
TRANSFER_SERVICE_PORT=3003
WALLET_SERVICE_PORT=3004
NOTIFICATION_SERVICE_PORT=3005
ANALYTICS_SERVICE_PORT=3006
AI_ENGINE_PORT=8000

# External Services
EXTERNAL_API_TIMEOUT=30000
EXTERNAL_API_RETRIES=3
EXTERNAL_API_RETRY_DELAY=1000

# Feature Flags
FEATURE_BIOMETRIC_AUTH=true
FEATURE_QR_PAYMENTS=true
FEATURE_INTERNATIONAL_TRANSFERS=true
FEATURE_CRYPTO_SUPPORT=false
FEATURE_AI_FRAUD_DETECTION=true

# Compliance & Regulatory
KYC_VERIFICATION_REQUIRED=true
AML_MONITORING_ENABLED=true
TRANSACTION_LIMIT_DAILY=50000
TRANSACTION_LIMIT_MONTHLY=200000

# Localization
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en
DEFAULT_CURRENCY=SAR
SUPPORTED_CURRENCIES=SAR,USD,EUR,GBP

# Time Zone
DEFAULT_TIMEZONE=Asia/Riyadh

# Performance
MAX_CONCURRENT_REQUESTS=1000
REQUEST_TIMEOUT=30000
DATABASE_POOL_SIZE=20
REDIS_POOL_SIZE=10

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_ENABLED=true

# Content Delivery
CDN_URL=https://cdn.wstransfir.com
STATIC_FILES_URL=https://static.wstransfir.com

# Social Media
FACEBOOK_APP_ID=your-facebook-app-id
TWITTER_API_KEY=your-twitter-api-key
LINKEDIN_CLIENT_ID=your-linkedin-client-id

# Customer Support
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+966112345678
SUPPORT_CHAT_ENABLED=true

# Legal
TERMS_URL=https://wstransfir.com/terms
PRIVACY_URL=https://wstransfir.com/privacy
COOKIES_URL=https://wstransfir.com/cookies
