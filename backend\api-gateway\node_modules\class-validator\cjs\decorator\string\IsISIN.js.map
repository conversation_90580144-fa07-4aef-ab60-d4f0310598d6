{"version": 3, "file": "IsISIN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISIN.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAEtC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAFD,wBAEC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,iBAAqC;IAC1D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YACjD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,uDAAuD,EAClF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,wBAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIsinValidator from 'validator/lib/isISIN';\n\nexport const IS_ISIN = 'isIsin';\n\n/**\n * Checks if the string is an ISIN (stock/security identifier).\n * If given value is not a string, then it returns false.\n */\nexport function isISIN(value: unknown): boolean {\n  return typeof value === 'string' && isIsinValidator(value);\n}\n\n/**\n * Checks if the string is an ISIN (stock/security identifier).\n * If given value is not a string, then it returns false.\n */\nexport function IsISIN(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISIN,\n      validator: {\n        validate: (value, args): boolean => isISIN(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be an ISIN (stock/security identifier)',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}