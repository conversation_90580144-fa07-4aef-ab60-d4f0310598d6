const express = require('express'); 
const cors = require('cors'); 
const app = express(); 
app.use(cors()); 
app.use(express.json()); 
app.get('/api/health', (req, res) => res.json({status: 'OK', message: 'WS Transfir API Running'})); 
app.post('/api/auth/login', (req, res) => { 
  const {email, password} = req.body; 
  if (email === '<EMAIL>' && password === 'admin123') { 
    res.json({success: true, token: 'admin-token', user: {firstName: 'Admin', lastName: 'User', email, role: 'admin'}}); 
  } else if (email && password) { 
    res.json({success: true, token: 'user-token', user: {firstName: 'Ahmed', lastName: 'Mohamed', email, role: 'user'}}); 
  } else { 
    res.status(401).json({success: false, message: 'Invalid credentials'}); 
  } 
}); 
app.get('/api/profile/me', (req, res) => res.json({id: '1', firstName: '<PERSON>', lastName: 'Mohamed', email: '<EMAIL>', phone: '+966501234567', isVerified: true})); 
app.get('/api/transfers', (req, res) => res.json({data: [{id: '1', referenceNumber: 'WS001', amount: '1500', currency: 'SAR', receiverName: 'Ahmed Mohamed', status: 'completed'}], total: 1})); 
app.listen(3000, () => console.log('API Server running on http://localhost:3000')); 
