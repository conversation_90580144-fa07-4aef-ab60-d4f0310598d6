{"version": 3, "file": "matchesEntirely.js", "names": ["matchesEntirely", "text", "regular_expression", "RegExp", "test"], "sources": ["../../source/helpers/matchesEntirely.js"], "sourcesContent": ["/**\r\n * Checks whether the entire input sequence can be matched\r\n * against the regular expression.\r\n * @return {boolean}\r\n */\r\nexport default function matchesEntirely(text, regular_expression) {\r\n\t// If assigning the `''` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\ttext = text || ''\r\n\treturn new RegExp('^(?:' + regular_expression + ')$').test(text)\r\n}"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACe,SAASA,eAAT,CAAyBC,IAAzB,EAA+BC,kBAA/B,EAAmD;EACjE;EACA;EACAD,IAAI,GAAGA,IAAI,IAAI,EAAf;EACA,OAAO,IAAIE,MAAJ,CAAW,SAASD,kBAAT,GAA8B,IAAzC,EAA+CE,IAA/C,CAAoDH,IAApD,CAAP;AACA"}