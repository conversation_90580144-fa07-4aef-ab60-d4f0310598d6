import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsS<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  IsPhoneNumber,
} from 'class-validator';

export class TransferBetweenWalletsDto {
  @ApiProperty({
    description: 'المبلغ المراد تحويله',
    example: 250,
    minimum: 1,
    maximum: 10000,
  })
  @IsNumber({}, { message: 'المبلغ يجب أن يكون رقم' })
  @IsNotEmpty({ message: 'المبلغ مطلوب' })
  @Min(1, { message: 'الحد الأدنى للتحويل 1' })
  @Max(10000, { message: 'الحد الأقصى للتحويل 10,000' })
  amount: number;

  @ApiProperty({
    description: 'العملة',
    example: 'SAR',
  })
  @IsString({ message: 'العملة يجب أن تكون نص' })
  @IsNotEmpty({ message: 'العملة مطلوبة' })
  currency: string;

  @ApiProperty({
    description: 'معرف المستلم (يمكن أن يكون معرف المستخدم، البريد الإلكتروني، أو رقم الهاتف)',
    example: '<EMAIL>',
  })
  @IsString({ message: 'معرف المستلم يجب أن يكون نص' })
  @IsNotEmpty({ message: 'معرف المستلم مطلوب' })
  recipientIdentifier: string;

  @ApiProperty({
    description: 'نوع معرف المستلم',
    example: 'email',
    enum: ['user_id', 'email', 'phone'],
  })
  @IsString({ message: 'نوع معرف المستلم يجب أن يكون نص' })
  @IsNotEmpty({ message: 'نوع معرف المستلم مطلوب' })
  identifierType: 'user_id' | 'email' | 'phone';

  @ApiProperty({
    description: 'رسالة للمستلم',
    example: 'تحويل من صديق',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الرسالة يجب أن تكون نص' })
  message?: string;

  @ApiProperty({
    description: 'مرجع التحويل',
    example: 'REF123456',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'مرجع التحويل يجب أن يكون نص' })
  reference?: string;

  @ApiProperty({
    description: 'إشعار المستلم',
    example: true,
    required: false,
  })
  @IsOptional()
  notifyRecipient?: boolean;
}
