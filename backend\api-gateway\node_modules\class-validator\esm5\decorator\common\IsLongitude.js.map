{"version": 3, "file": "IsLongitude.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsLongitude.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,MAAM,CAAC,IAAM,YAAY,GAAG,aAAa,CAAC;AAE1C;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,KAAa;IACvC,OAAO,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,IAAI,SAAS,CAAC,YAAK,KAAK,CAAE,CAAC,CAAC;AAC7F,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,iBAAqC;IAC/D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,WAAW,CAAC,KAAK,CAAC,EAAlB,CAAkB;YACtD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,gDAAgD,EAA7D,CAA6D,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from './ValidateBy';\nimport { isLatLong } from './IsLatLong';\n\nexport const IS_LONGITUDE = 'isLongitude';\n\n/**\n * Checks if a given value is a longitude.\n */\nexport function isLongitude(value: string): boolean {\n  return (typeof value === 'number' || typeof value === 'string') && isLatLong(`0,${value}`);\n}\n\n/**\n * Checks if a given value is a longitude.\n */\nexport function IsLongitude(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_LONGITUDE,\n      validator: {\n        validate: (value, args): boolean => isLongitude(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a longitude string or number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}