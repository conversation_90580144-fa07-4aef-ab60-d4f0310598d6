[flake8]
# Flake8 Configuration
# ====================
# إعدادات flake8 للتحقق من جودة الكود

# Maximum line length
max-line-length = 88

# Maximum complexity
max-complexity = 10

# Ignore specific errors and warnings
ignore = 
    # Black compatibility
    E203,  # whitespace before ':'
    W503,  # line break before binary operator
    W504,  # line break after binary operator
    
    # Documentation
    D100,  # Missing docstring in public module
    D101,  # Missing docstring in public class
    D102,  # Missing docstring in public method
    D103,  # Missing docstring in public function
    D104,  # Missing docstring in public package
    D105,  # Missing docstring in magic method
    D107,  # Missing docstring in __init__
    
    # Import related
    F401,  # module imported but unused (handled by isort)
    
    # Whitespace
    W291,  # trailing whitespace
    W292,  # no newline at end of file
    W293,  # blank line contains whitespace

# Select specific checks
select = 
    E,    # pycodestyle errors
    W,    # pycodestyle warnings
    F,    # pyflakes
    C,    # mccabe complexity
    B,    # flake8-bugbear
    I,    # isort
    N,    # pep8-naming

# Exclude directories and files
exclude = 
    .git,
    __pycache__,
    .pytest_cache,
    .mypy_cache,
    .venv,
    venv,
    env,
    ENV,
    .env,
    build,
    dist,
    *.egg-info,
    migrations,
    node_modules,
    frontend/web-app/.next,
    frontend/web-app/build,
    frontend/mobile-app/build,
    database/migrations,
    database/seeds,
    docs/_build,
    htmlcov,
    .coverage,
    coverage.xml,
    *.min.js,
    *.min.css

# Per-file ignores
per-file-ignores = 
    # Tests can have longer lines and unused imports
    tests/*:E501,F401,D
    
    # Configuration files
    */settings.py:E501
    */config.py:E501
    
    # Migration files
    migrations/*:E501,F401,D
    
    # Seed files
    seeds/*:E501,F401,D
    
    # Init files can have unused imports
    __init__.py:F401,D
    
    # API files can have longer lines
    *api.py:E501
    
    # Model files can have longer lines
    *models.py:E501
    
    # Schema files can have longer lines
    *schemas.py:E501

# Naming conventions
classmethod-decorators = 
    classmethod,
    pydantic.validator,
    pydantic.root_validator

staticmethod-decorators = 
    staticmethod

# Import order
import-order-style = google
application-import-names = 
    backend,
    frontend,
    tests,
    database,
    config

# Docstring conventions
docstring-convention = google

# Enable specific plugins
enable-extensions = 
    B,    # flake8-bugbear
    C,    # mccabe
    I,    # isort
    N,    # pep8-naming

# Inline comments
inline-quotes = double
multiline-quotes = double

# Hang closing bracket
hang-closing = True

# Show source code
show-source = True

# Show pep8 text
show-pep8 = True

# Count errors
count = True

# Statistics
statistics = True

# Benchmark
benchmark = False

# Jobs (parallel processing)
jobs = auto

# Format
format = default

# Tee output
tee = False

# Output file
output-file = flake8-report.txt

# Verbose
verbose = 1
