"use strict";

var _applyInternationalSeparatorStyle = _interopRequireDefault(require("./applyInternationalSeparatorStyle.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

describe('applyInternationalSeparatorStyle', function () {
  it('should change Google\'s international format style', function () {
    (0, _applyInternationalSeparatorStyle["default"])('(xxx) xxx-xx-xx').should.equal('xxx xxx xx xx');
    (0, _applyInternationalSeparatorStyle["default"])('(xxx)xxx').should.equal('xxx xxx');
  });
});
//# sourceMappingURL=applyInternationalSeparatorStyle.test.js.map