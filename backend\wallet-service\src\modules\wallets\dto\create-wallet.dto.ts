import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsN<PERSON>ber,
  IsBoolean,
  Min,
  Max,
  Length,
  IsObject,
} from 'class-validator';
import { WalletType } from '../entities/wallet.entity';

export class CreateWalletDto {
  @ApiProperty({
    description: 'نوع المحفظة',
    enum: WalletType,
    example: WalletType.PERSONAL,
  })
  @IsEnum(WalletType, { message: 'نوع المحفظة غير صالح' })
  type: WalletType;

  @ApiProperty({
    description: 'العملة',
    example: 'SAR',
    enum: ['SAR', 'USD', 'EUR', 'GBP', 'AED'],
  })
  @IsString({ message: 'العملة يجب أن تكون نص' })
  @Length(3, 3, { message: 'العملة يجب أن تكون 3 أحرف' })
  currency: string;

  @ApiProperty({
    description: 'اسم المحفظة',
    example: 'المحفظة الرئيسية',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'اسم المحفظة يجب أن يكون نص' })
  @Length(1, 100, { message: 'اسم المحفظة يجب أن يكون بين 1 و 100 حرف' })
  name?: string;

  @ApiProperty({
    description: 'وصف المحفظة',
    example: 'محفظة للمدخرات الشخصية',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الوصف يجب أن يكون نص' })
  @Length(0, 500, { message: 'الوصف يجب أن يكون أقل من 500 حرف' })
  description?: string;

  @ApiProperty({
    description: 'الحد اليومي للإنفاق',
    example: 5000,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'الحد اليومي يجب أن يكون رقم صالح' })
  @Min(0, { message: 'الحد اليومي يجب أن يكون أكبر من أو يساوي 0' })
  @Max(100000, { message: 'الحد اليومي يجب أن يكون أقل من 100000' })
  dailyLimit?: number;

  @ApiProperty({
    description: 'الحد الشهري للإنفاق',
    example: 50000,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'الحد الشهري يجب أن يكون رقم صالح' })
  @Min(0, { message: 'الحد الشهري يجب أن يكون أكبر من أو يساوي 0' })
  @Max(1000000, { message: 'الحد الشهري يجب أن يكون أقل من 1000000' })
  monthlyLimit?: number;

  @ApiProperty({
    description: 'الحد الأقصى للمعاملة الواحدة',
    example: 10000,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'حد المعاملة يجب أن يكون رقم صالح' })
  @Min(0, { message: 'حد المعاملة يجب أن يكون أكبر من أو يساوي 0' })
  @Max(50000, { message: 'حد المعاملة يجب أن يكون أقل من 50000' })
  transactionLimit?: number;

  @ApiProperty({
    description: 'السماح بالتحويلات الواردة',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'السماح بالتحويلات الواردة يجب أن يكون قيمة منطقية' })
  allowIncoming?: boolean = true;

  @ApiProperty({
    description: 'السماح بالتحويلات الصادرة',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'السماح بالتحويلات الصادرة يجب أن يكون قيمة منطقية' })
  allowOutgoing?: boolean = true;

  @ApiProperty({
    description: 'تتطلب موافقة للمعاملات',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تتطلب موافقة يجب أن يكون قيمة منطقية' })
  requiresApproval?: boolean = false;

  @ApiProperty({
    description: 'تفعيل الإشعارات',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تفعيل الإشعارات يجب أن يكون قيمة منطقية' })
  notificationsEnabled?: boolean = true;

  @ApiProperty({
    description: 'بيانات إضافية',
    example: { category: 'savings', purpose: 'emergency_fund' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
