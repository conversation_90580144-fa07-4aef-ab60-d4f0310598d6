{"version": 3, "file": "IsInt.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsInt.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,MAAM,GAAG,OAAO,CAAC;AAE9B;;GAEG;AACH,MAAM,UAAU,KAAK,CAAC,GAAY;IAChC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC1D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,KAAK,CAAC,iBAAqC;IACzD,OAAO,UAAU,CACf;QACE,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,KAAK,CAAC,KAAK,CAAC,EAAZ,CAAY;YAChD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,qCAAqC,EAAlD,CAAkD,EAChE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_INT = 'isInt';\n\n/**\n * Checks if value is an integer.\n */\nexport function isInt(val: unknown): val is Number {\n  return typeof val === 'number' && Number.isInteger(val);\n}\n\n/**\n * Checks if value is an integer.\n */\nexport function IsInt(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_INT,\n      validator: {\n        validate: (value, args): boolean => isInt(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be an integer number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}