import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class RedisService {
  private readonly logger = new Logger(RedisService.name);

  // Mock Redis implementation for testing
  private cache = new Map<string, { value: string; expiry: number }>();

  async set(key: string, value: string, ttl?: number): Promise<void> {
    const expiry = ttl ? Date.now() + (ttl * 1000) : 0;
    this.cache.set(key, { value, expiry });
    this.logger.debug(`Redis SET: ${key} (TTL: ${ttl}s)`);
  }

  async get(key: string): Promise<string | null> {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    if (item.expiry > 0 && Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    this.logger.debug(`Redis GET: ${key}`);
    return item.value;
  }

  async del(key: string): Promise<void> {
    this.cache.delete(key);
    this.logger.debug(`Redis DEL: ${key}`);
  }

  async exists(key: string): Promise<boolean> {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }

    if (item.expiry > 0 && Date.now() > item.expiry) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  async flushall(): Promise<void> {
    this.cache.clear();
    this.logger.debug('Redis FLUSHALL');
  }
}
