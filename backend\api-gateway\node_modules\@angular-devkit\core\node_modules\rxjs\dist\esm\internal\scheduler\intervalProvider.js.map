{"version": 3, "file": "intervalProvider.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/intervalProvider.ts"], "names": [], "mappings": "AAeA,MAAM,CAAC,MAAM,gBAAgB,GAAqB;IAGhD,WAAW,CAAC,OAAmB,EAAE,OAAgB,EAAE,GAAG,IAAI;QACxD,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC;QACtC,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,WAAW,EAAE;YACzB,OAAO,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;SACxD;QACD,OAAO,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAChD,CAAC;IACD,aAAa,CAAC,MAAM;QAClB,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC;QACtC,OAAO,CAAC,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,aAAa,KAAI,aAAa,CAAC,CAAC,MAAa,CAAC,CAAC;IACnE,CAAC;IACD,QAAQ,EAAE,SAAS;CACpB,CAAC"}