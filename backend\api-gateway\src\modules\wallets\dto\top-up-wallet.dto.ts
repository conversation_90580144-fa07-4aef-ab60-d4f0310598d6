import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsN<PERSON>ber,
  IsEnum,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum TopUpMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  MOBILE_PAYMENT = 'mobile_payment',
  CASH_DEPOSIT = 'cash_deposit',
  CRYPTO = 'crypto',
}

export class PaymentCardDto {
  @ApiProperty({
    description: 'رقم البطاقة',
    example: '****************',
  })
  @IsString({ message: 'رقم البطاقة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم البطاقة مطلوب' })
  cardNumber: string;

  @ApiProperty({
    description: 'شهر انتهاء الصلاحية',
    example: '12',
  })
  @IsString({ message: 'شهر انتهاء الصلاحية يجب أن يكون نص' })
  @IsNotEmpty({ message: 'شهر انتهاء الصلاحية مطلوب' })
  expiryMonth: string;

  @ApiProperty({
    description: 'سنة انتهاء الصلاحية',
    example: '2025',
  })
  @IsString({ message: 'سنة انتهاء الصلاحية يجب أن تكون نص' })
  @IsNotEmpty({ message: 'سنة انتهاء الصلاحية مطلوبة' })
  expiryYear: string;

  @ApiProperty({
    description: 'رمز الأمان (CVV)',
    example: '123',
  })
  @IsString({ message: 'رمز الأمان يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رمز الأمان مطلوب' })
  cvv: string;

  @ApiProperty({
    description: 'اسم حامل البطاقة',
    example: 'Ahmed Mohammed',
  })
  @IsString({ message: 'اسم حامل البطاقة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم حامل البطاقة مطلوب' })
  cardHolderName: string;
}

export class BankTransferDto {
  @ApiProperty({
    description: 'اسم البنك',
    example: 'البنك الأهلي السعودي',
  })
  @IsString({ message: 'اسم البنك يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم البنك مطلوب' })
  bankName: string;

  @ApiProperty({
    description: 'رقم الحساب',
    example: '****************',
  })
  @IsString({ message: 'رقم الحساب يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم الحساب مطلوب' })
  accountNumber: string;

  @ApiProperty({
    description: 'رقم التحويل المرجعي',
    example: 'TXN123456789',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم التحويل المرجعي يجب أن يكون نص' })
  referenceNumber?: string;
}

export class MobilePaymentDto {
  @ApiProperty({
    description: 'مقدم الخدمة',
    example: 'STC Pay',
  })
  @IsString({ message: 'مقدم الخدمة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'مقدم الخدمة مطلوب' })
  provider: string;

  @ApiProperty({
    description: 'رقم الهاتف المرتبط بالمحفظة',
    example: '+************',
  })
  @IsString({ message: 'رقم الهاتف يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم الهاتف مطلوب' })
  phoneNumber: string;
}

export class TopUpWalletDto {
  @ApiProperty({
    description: 'المبلغ المراد شحنه',
    example: 1000,
    minimum: 10,
    maximum: 10000,
  })
  @IsNumber({}, { message: 'المبلغ يجب أن يكون رقم' })
  @IsNotEmpty({ message: 'المبلغ مطلوب' })
  @Min(10, { message: 'الحد الأدنى للشحن 10' })
  @Max(10000, { message: 'الحد الأقصى للشحن 10,000' })
  amount: number;

  @ApiProperty({
    description: 'العملة',
    example: 'SAR',
  })
  @IsString({ message: 'العملة يجب أن تكون نص' })
  @IsNotEmpty({ message: 'العملة مطلوبة' })
  currency: string;

  @ApiProperty({
    description: 'طريقة الشحن',
    enum: TopUpMethod,
    example: TopUpMethod.CREDIT_CARD,
  })
  @IsEnum(TopUpMethod, { message: 'طريقة الشحن غير صحيحة' })
  @IsNotEmpty({ message: 'طريقة الشحن مطلوبة' })
  method: TopUpMethod;

  @ApiProperty({
    description: 'بيانات البطاقة (مطلوب للدفع بالبطاقة)',
    type: PaymentCardDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PaymentCardDto)
  cardDetails?: PaymentCardDto;

  @ApiProperty({
    description: 'بيانات التحويل البنكي (مطلوب للتحويل البنكي)',
    type: BankTransferDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BankTransferDto)
  bankTransferDetails?: BankTransferDto;

  @ApiProperty({
    description: 'بيانات الدفع المحمول (مطلوب للدفع المحمول)',
    type: MobilePaymentDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MobilePaymentDto)
  mobilePaymentDetails?: MobilePaymentDto;

  @ApiProperty({
    description: 'حفظ طريقة الدفع للاستخدام المستقبلي',
    example: true,
    required: false,
  })
  @IsOptional()
  savePaymentMethod?: boolean;

  @ApiProperty({
    description: 'ملاحظات إضافية',
    example: 'شحن المحفظة للتحويلات الشهرية',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الملاحظات يجب أن تكون نص' })
  notes?: string;
}
