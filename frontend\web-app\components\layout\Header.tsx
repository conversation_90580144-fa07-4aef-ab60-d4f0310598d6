'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bars3Icon, 
  XMarkIcon,
  SunIcon,
  MoonIcon,
  LanguageIcon,
  UserCircleIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from 'next-themes';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Button from '@/components/ui/Button';
import { clsx } from 'clsx';

// عناصر التنقل
const navigationItems = [
  { key: 'nav.home', href: '/' },
  { key: 'nav.services', href: '/services' },
  { key: 'nav.rates', href: '/rates' },
  { key: 'nav.about', href: '/about' },
  { key: 'nav.contact', href: '/contact' },
];

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false);
  
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const { state: authState } = useAuth();
  const { language, setLanguage, t, isRTL } = useLanguage();

  // تبديل القائمة المحمولة
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // تبديل السمة
  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  // تغيير اللغة
  const changeLanguage = (newLanguage: 'ar' | 'en') => {
    setLanguage(newLanguage);
    setIsLanguageMenuOpen(false);
  };

  return (
    <header className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
      <nav className="container-custom">
        <div className="flex items-center justify-between h-16">
          {/* الشعار */}
          <motion.div
            initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2 rtl:space-x-reverse"
          >
            <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-primary-700 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">WS</span>
            </div>
            <span className="text-xl font-bold text-gray-900 dark:text-white">
              WS Transfir
            </span>
          </motion.div>

          {/* التنقل الرئيسي - الشاشات الكبيرة */}
          <div className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {navigationItems.map((item, index) => (
              <motion.a
                key={item.key}
                href={item.href}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 font-medium"
              >
                {t(item.key)}
              </motion.a>
            ))}
          </div>

          {/* أدوات التحكم */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* تبديل السمة */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={toggleTheme}
              className="p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
              aria-label={t('common.toggleTheme')}
            >
              {theme === 'dark' ? (
                <SunIcon className="w-5 h-5" />
              ) : (
                <MoonIcon className="w-5 h-5" />
              )}
            </motion.button>

            {/* تبديل اللغة */}
            <div className="relative">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsLanguageMenuOpen(!isLanguageMenuOpen)}
                className="p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 flex items-center space-x-1 rtl:space-x-reverse"
                aria-label={t('common.changeLanguage')}
              >
                <LanguageIcon className="w-5 h-5" />
                <span className="text-sm font-medium uppercase">
                  {language}
                </span>
              </motion.button>

              {/* قائمة اللغات */}
              <AnimatePresence>
                {isLanguageMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    className={clsx(
                      'absolute top-full mt-2 w-32 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1',
                      isRTL ? 'left-0' : 'right-0'
                    )}
                  >
                    <button
                      onClick={() => changeLanguage('ar')}
                      className={clsx(
                        'w-full px-4 py-2 text-right hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200',
                        language === 'ar' && 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                      )}
                    >
                      العربية
                    </button>
                    <button
                      onClick={() => changeLanguage('en')}
                      className={clsx(
                        'w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200',
                        language === 'en' && 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                      )}
                    >
                      English
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* أزرار المصادقة أو الملف الشخصي */}
            {authState.isAuthenticated ? (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.push('/dashboard')}
                className="flex items-center space-x-2 rtl:space-x-reverse p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
              >
                <UserCircleIcon className="w-6 h-6" />
                <span className="hidden sm:block text-sm font-medium">
                  {authState.user?.firstName}
                </span>
              </motion.button>
            ) : (
              <div className="hidden sm:flex items-center space-x-3 rtl:space-x-reverse">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push('/auth/login')}
                >
                  {t('auth.login')}
                </Button>
                <Button
                  size="sm"
                  onClick={() => router.push('/auth/register')}
                >
                  {t('auth.register')}
                </Button>
              </div>
            )}

            {/* زر القائمة المحمولة */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={toggleMobileMenu}
              className="lg:hidden p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
              aria-label={t('common.toggleMenu')}
            >
              {isMobileMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </motion.button>
          </div>
        </div>

        {/* القائمة المحمولة */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden border-t border-gray-200 dark:border-gray-700 py-4"
            >
              <div className="flex flex-col space-y-4">
                {/* عناصر التنقل */}
                {navigationItems.map((item, index) => (
                  <motion.a
                    key={item.key}
                    href={item.href}
                    initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 font-medium py-2"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {t(item.key)}
                  </motion.a>
                ))}

                {/* أزرار المصادقة للشاشات الصغيرة */}
                {!authState.isAuthenticated && (
                  <div className="flex flex-col space-y-2 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <Button
                      variant="outline"
                      fullWidth
                      onClick={() => {
                        router.push('/auth/login');
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      {t('auth.login')}
                    </Button>
                    <Button
                      fullWidth
                      onClick={() => {
                        router.push('/auth/register');
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      {t('auth.register')}
                    </Button>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </header>
  );
}
