{"version": 3, "file": "DocInheritDocTag.js", "sourceRoot": "", "sources": ["../../src/nodes/DocInheritDocTag.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;AAE3D,qCAAsD;AAEtD,uDAI4B;AAgB5B;;GAEG;AACH;IAAsC,oCAAgB;IAGpD;;;OAGG;IACH,0BAAmB,UAA2E;QAC5F,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,KAAI,CAAC,oBAAoB,KAAK,aAAa,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;QAClF,CAAC;QAED,KAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,oBAAoB,CAAC;;IAC/D,CAAC;IAGD,sBAAW,kCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,aAAa,CAAC;QACnC,CAAC;;;OAAA;IAMD,sBAAW,kDAAoB;QAJ/B;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;;;OAAA;IAED,gBAAgB;IACN,kDAAuB,GAAjC;QACE,WAAW;QACX,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACtC,CAAC;IACH,uBAAC;AAAD,CAAC,AAnCD,CAAsC,mCAAgB,GAmCrD;AAnCY,4CAAgB", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, type DocNode } from './DocNode';\r\nimport type { DocDeclarationReference } from './DocDeclarationReference';\r\nimport {\r\n  DocInlineTagBase,\r\n  type IDocInlineTagBaseParsedParameters,\r\n  type IDocInlineTagBaseParameters\r\n} from './DocInlineTagBase';\r\n\r\n/**\r\n * Constructor parameters for {@link DocInheritDocTag}.\r\n */\r\nexport interface IDocInheritDocTagParameters extends IDocInlineTagBaseParameters {\r\n  declarationReference?: DocDeclarationReference;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocInheritDocTag}.\r\n */\r\nexport interface IDocInheritDocTagParsedParameters extends IDocInlineTagBaseParsedParameters {\r\n  declarationReference?: DocDeclarationReference;\r\n}\r\n\r\n/**\r\n * Represents an `{@inheritDoc}` tag.\r\n */\r\nexport class DocInheritDocTag extends DocInlineTagBase {\r\n  private readonly _declarationReference: DocDeclarationReference | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocInheritDocTagParameters | IDocInheritDocTagParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (this.tagNameWithUpperCase !== '@INHERITDOC') {\r\n      throw new Error('DocInheritDocTag requires the tag name to be \"{@inheritDoc}\"');\r\n    }\r\n\r\n    this._declarationReference = parameters.declarationReference;\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.InheritDocTag;\r\n  }\r\n\r\n  /**\r\n   * The declaration that the documentation will be inherited from.\r\n   * If omitted, the documentation will be inherited from the parent class.\r\n   */\r\n  public get declarationReference(): DocDeclarationReference | undefined {\r\n    return this._declarationReference;\r\n  }\r\n\r\n  /** @override */\r\n  protected getChildNodesForContent(): ReadonlyArray<DocNode | undefined> {\r\n    // abstract\r\n    return [this._declarationReference];\r\n  }\r\n}\r\n"]}