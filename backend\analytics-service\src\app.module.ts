import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ScheduleModule } from '@nestjs/schedule';

// Modules
import { AnalyticsModule } from './modules/analytics/analytics.module';
import { ReportsModule } from './modules/reports/reports.module';
import { DashboardsModule } from './modules/dashboards/dashboards.module';
import { KpisModule } from './modules/kpis/kpis.module';
import { TrendsModule } from './modules/trends/trends.module';
import { DataCollectionModule } from './modules/data-collection/data-collection.module';

// Entities (PostgreSQL)
import { AnalyticsEvent } from './modules/analytics/entities/analytics-event.entity';
import { Report } from './modules/reports/entities/report.entity';
import { Dashboard } from './modules/dashboards/entities/dashboard.entity';
import { KpiMetric } from './modules/kpis/entities/kpi-metric.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // PostgreSQL Database for structured data
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('ANALYTICS_DB_HOST', 'localhost'),
        port: configService.get('ANALYTICS_DB_PORT', 5432),
        username: configService.get('ANALYTICS_DB_USERNAME', 'postgres'),
        password: configService.get('ANALYTICS_DB_PASSWORD', 'password'),
        database: configService.get('ANALYTICS_DB_NAME', 'ws_analytics'),
        entities: [
          AnalyticsEvent,
          Report,
          Dashboard,
          KpiMetric,
        ],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development',
        ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
      }),
      inject: [ConfigService],
    }),

    // MongoDB for unstructured analytics data
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get('MONGODB_URI', 'mongodb://localhost:27017/ws_analytics'),
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }),
      inject: [ConfigService],
    }),

    // JWT Authentication
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),

    // Passport
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // Task Scheduling for data aggregation
    ScheduleModule.forRoot(),

    // Feature Modules
    AnalyticsModule,
    ReportsModule,
    DashboardsModule,
    KpisModule,
    TrendsModule,
    DataCollectionModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    console.log('📊 Analytics Service initialized');
    console.log(`🗄️  PostgreSQL: ${this.configService.get('ANALYTICS_DB_NAME', 'ws_analytics')}`);
    console.log(`🍃 MongoDB enabled: ${this.configService.get('MONGODB_ENABLED', 'true')}`);
    console.log(`🔍 Elasticsearch enabled: ${this.configService.get('ELASTICSEARCH_ENABLED', 'false')}`);
    console.log(`🤖 ML predictions enabled: ${this.configService.get('ML_PREDICTIONS', 'false')}`);
    console.log(`⚡ Real-time analytics: ${this.configService.get('REAL_TIME_ANALYTICS', 'true')}`);
  }
}
