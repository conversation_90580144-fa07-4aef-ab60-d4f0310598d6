{"version": 3, "file": "isPossible.js", "names": ["isPossiblePhoneNumber", "input", "options", "metadata", "undefined", "<PERSON><PERSON><PERSON>", "v2", "countryCallingCode", "Error", "selectNumberingPlan", "phone", "country", "hasCountry", "possibleLengths", "isPossibleNumber", "nationalNumber", "isNonGeographicCallingCode", "checkNumberLength"], "sources": ["../source/isPossible.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\nimport checkNumberLength from './helpers/checkNumberLength.js'\r\n\r\n/**\r\n * Checks if a phone number is \"possible\" (basically just checks its length).\r\n *\r\n * isPossible(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function isPossiblePhoneNumber(input, options, metadata) {\r\n\t/* istanbul ignore if */\r\n\tif (options === undefined) {\r\n\t\toptions = {}\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tif (options.v2) {\r\n\t\tif (!input.countryCallingCode) {\r\n\t\t\tthrow new Error('Invalid phone number object passed')\r\n\t\t}\r\n\t\tmetadata.selectNumberingPlan(input.countryCallingCode)\r\n\t} else {\r\n\t\tif (!input.phone) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\tif (input.country) {\r\n\t\t\tif (!metadata.hasCountry(input.country)) {\r\n\t\t\t\tthrow new Error(`Unknown country: ${input.country}`)\r\n\t\t\t}\r\n\t\t\tmetadata.country(input.country)\r\n\t\t} else {\r\n\t\t\tif (!input.countryCallingCode) {\r\n\t\t\t\tthrow new Error('Invalid phone number object passed')\r\n\t\t\t}\r\n\t\t\tmetadata.selectNumberingPlan(input.countryCallingCode)\r\n\t\t}\r\n\t}\r\n\r\n\t// Old metadata (< 1.0.18) had no \"possible length\" data.\r\n\tif (metadata.possibleLengths()) {\r\n\t\treturn isPossibleNumber(input.phone || input.nationalNumber, metadata)\r\n\t} else {\r\n\t\t// There was a bug between `1.7.35` and `1.7.37` where \"possible_lengths\"\r\n\t\t// were missing for \"non-geographical\" numbering plans.\r\n\t\t// Just assume the number is possible in such cases:\r\n\t\t// it's unlikely that anyone generated their custom metadata\r\n\t\t// in that short period of time (one day).\r\n\t\t// This code can be removed in some future major version update.\r\n\t\tif (input.countryCallingCode && metadata.isNonGeographicCallingCode(input.countryCallingCode)) {\r\n\t\t\t// \"Non-geographic entities\" did't have `possibleLengths`\r\n\t\t\t// due to a bug in metadata generation process.\r\n\t\t\treturn true\r\n\t\t} else {\r\n\t\t\tthrow new Error('Missing \"possibleLengths\" in metadata. Perhaps the metadata has been generated before v1.0.18.');\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport function isPossibleNumber(nationalNumber, metadata) { //, isInternational) {\r\n\tswitch (checkNumberLength(nationalNumber, metadata)) {\r\n\t\tcase 'IS_POSSIBLE':\r\n\t\t\treturn true\r\n\t\t// This library ignores \"local-only\" phone numbers (for simplicity).\r\n\t\t// See the readme for more info on what are \"local-only\" phone numbers.\r\n\t\t// case 'IS_POSSIBLE_LOCAL_ONLY':\r\n\t\t// \treturn !isInternational\r\n\t\tdefault:\r\n\t\t\treturn false\r\n\t}\r\n}"], "mappings": ";;;;;;;;AAAA;;AACA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,qBAAT,CAA+BC,KAA/B,EAAsCC,OAAtC,EAA+CC,QAA/C,EAAyD;EACvE;EACA,IAAID,OAAO,KAAKE,SAAhB,EAA2B;IAC1BF,OAAO,GAAG,EAAV;EACA;;EAEDC,QAAQ,GAAG,IAAIE,oBAAJ,CAAaF,QAAb,CAAX;;EAEA,IAAID,OAAO,CAACI,EAAZ,EAAgB;IACf,IAAI,CAACL,KAAK,CAACM,kBAAX,EAA+B;MAC9B,MAAM,IAAIC,KAAJ,CAAU,oCAAV,CAAN;IACA;;IACDL,QAAQ,CAACM,mBAAT,CAA6BR,KAAK,CAACM,kBAAnC;EACA,CALD,MAKO;IACN,IAAI,CAACN,KAAK,CAACS,KAAX,EAAkB;MACjB,OAAO,KAAP;IACA;;IACD,IAAIT,KAAK,CAACU,OAAV,EAAmB;MAClB,IAAI,CAACR,QAAQ,CAACS,UAAT,CAAoBX,KAAK,CAACU,OAA1B,CAAL,EAAyC;QACxC,MAAM,IAAIH,KAAJ,4BAA8BP,KAAK,CAACU,OAApC,EAAN;MACA;;MACDR,QAAQ,CAACQ,OAAT,CAAiBV,KAAK,CAACU,OAAvB;IACA,CALD,MAKO;MACN,IAAI,CAACV,KAAK,CAACM,kBAAX,EAA+B;QAC9B,MAAM,IAAIC,KAAJ,CAAU,oCAAV,CAAN;MACA;;MACDL,QAAQ,CAACM,mBAAT,CAA6BR,KAAK,CAACM,kBAAnC;IACA;EACD,CA5BsE,CA8BvE;;;EACA,IAAIJ,QAAQ,CAACU,eAAT,EAAJ,EAAgC;IAC/B,OAAOC,gBAAgB,CAACb,KAAK,CAACS,KAAN,IAAeT,KAAK,CAACc,cAAtB,EAAsCZ,QAAtC,CAAvB;EACA,CAFD,MAEO;IACN;IACA;IACA;IACA;IACA;IACA;IACA,IAAIF,KAAK,CAACM,kBAAN,IAA4BJ,QAAQ,CAACa,0BAAT,CAAoCf,KAAK,CAACM,kBAA1C,CAAhC,EAA+F;MAC9F;MACA;MACA,OAAO,IAAP;IACA,CAJD,MAIO;MACN,MAAM,IAAIC,KAAJ,CAAU,gGAAV,CAAN;IACA;EACD;AACD;;AAEM,SAASM,gBAAT,CAA0BC,cAA1B,EAA0CZ,QAA1C,EAAoD;EAAE;EAC5D,QAAQ,IAAAc,6BAAA,EAAkBF,cAAlB,EAAkCZ,QAAlC,CAAR;IACC,KAAK,aAAL;MACC,OAAO,IAAP;IACD;IACA;IACA;IACA;;IACA;MACC,OAAO,KAAP;EARF;AAUA"}