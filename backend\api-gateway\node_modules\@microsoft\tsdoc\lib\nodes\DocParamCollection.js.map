{"version": 3, "file": "DocParamCollection.js", "sourceRoot": "", "sources": ["../../src/nodes/DocParamCollection.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAE,OAAO,EAA2B,MAAM,WAAW,CAAC;AAQ1E;;;GAGG;AACH;IAAwC,sCAAO;IAI7C;;;OAGG;IACH,4BAAmB,UAAyC;QAC1D,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QARH,aAAO,GAAoB,EAAE,CAAC;;IAS/C,CAAC;IAGD,sBAAW,oCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,eAAe,CAAC;QACrC,CAAC;;;OAAA;IAED;;OAEG;IACI,6BAAC,MAAM,CAAC,QAAQ,CAAC,GAAxB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IACzC,CAAC;IAKD,sBAAW,sCAAM;QAHjB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAKD,sBAAW,qCAAK;QAHhB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC7B,CAAC;;;OAAA;IAED;;OAEG;IACI,gCAAG,GAAV,UAAW,aAA4B;QACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEjC,wFAAwF;QACxF,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACrC,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAyB,CAAC;QACxD,CAAC;QAED,+CAA+C;QAC/C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kCAAK,GAAZ;QACE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;IACjC,CAAC;IAED;;;;;;;;;OASG;IACI,8CAAiB,GAAxB,UAAyB,aAAqB;QAC5C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC/C,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gBAAgB;IACN,4CAAe,GAAzB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IACH,yBAAC;AAAD,CAAC,AApFD,CAAwC,OAAO,GAoF9C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICEN<PERSON> in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode, type IDocNodeParameters } from './DocNode';\r\nimport type { DocParamBlock } from './DocParamBlock';\r\n\r\n/**\r\n * Constructor parameters for {@link DocParamCollection}.\r\n */\r\nexport interface IDocParamCollectionParameters extends IDocNodeParameters {}\r\n\r\n/**\r\n * Represents a collection of DocParamBlock objects and provides efficient operations for looking up the\r\n * documentation for a specified parameter name.\r\n */\r\nexport class DocParamCollection extends DocNode {\r\n  private readonly _blocks: DocParamBlock[] = [];\r\n  private _blocksByName: Map<string, DocParamBlock> | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocParamCollectionParameters) {\r\n    super(parameters);\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.ParamCollection;\r\n  }\r\n\r\n  /**\r\n   * Provide an iterator for callers that support it.\r\n   */\r\n  public [Symbol.iterator](): IterableIterator<DocParamBlock> {\r\n    return this._blocks[Symbol.iterator]();\r\n  }\r\n\r\n  /**\r\n   * Returns the blocks in this collection.\r\n   */\r\n  public get blocks(): ReadonlyArray<DocParamBlock> {\r\n    return this._blocks;\r\n  }\r\n\r\n  /**\r\n   * Returns the number of blocks in this collection.\r\n   */\r\n  public get count(): number {\r\n    return this._blocks.length;\r\n  }\r\n\r\n  /**\r\n   * Adds a new block to the collection.\r\n   */\r\n  public add(docParamBlock: DocParamBlock): void {\r\n    this._blocks.push(docParamBlock);\r\n\r\n    // Allocate the map on demand, since most DocComment parameter collections will be empty\r\n    if (this._blocksByName === undefined) {\r\n      this._blocksByName = new Map<string, DocParamBlock>();\r\n    }\r\n\r\n    // The first block to be added takes precedence\r\n    if (!this._blocksByName.has(docParamBlock.parameterName)) {\r\n      this._blocksByName.set(docParamBlock.parameterName, docParamBlock);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes all blocks from the collection\r\n   */\r\n  public clear(): void {\r\n    this._blocks.length = 0;\r\n    this._blocksByName = undefined;\r\n  }\r\n\r\n  /**\r\n   * Returns the first block whose `parameterName` matches the specified string.\r\n   *\r\n   * @remarks\r\n   * If the collection was parsed from an input containing errors, there could potentially be more than\r\n   * one DocParamBlock with the same name.  In this situation, tryGetBlockByName() will return the first match\r\n   * that it finds.\r\n   *\r\n   * This lookup is optimized using a dictionary.\r\n   */\r\n  public tryGetBlockByName(parameterName: string): DocParamBlock | undefined {\r\n    if (this._blocksByName) {\r\n      return this._blocksByName.get(parameterName);\r\n    }\r\n    return undefined;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return this._blocks;\r\n  }\r\n}\r\n"]}