"""
Authentication Service Tests
===========================
اختبارات خدمة المصادقة
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend', 'shared'))

from auth.jwt_manager import J<PERSON>TManager, UserRole, Permission, TokenType
from auth.auth_service import AuthService, RegistrationData, UserData


class TestJWTManager:
    """اختبارات مدير JWT"""
    
    @pytest.fixture
    def jwt_manager(self):
        """إنشاء مدير JWT للاختبار"""
        return JWTManager(
            secret_key="test_secret_key_12345",
            refresh_secret_key="test_refresh_secret_key_12345",
            access_token_expire_minutes=15,
            refresh_token_expire_days=30
        )
    
    def test_password_hashing(self, jwt_manager):
        """اختبار تشفير كلمة المرور"""
        password = "TestPassword123!"
        
        # Hash password
        hashed = jwt_manager.hash_password(password)
        
        # Verify password
        assert jwt_manager.verify_password(password, hashed)
        assert not jwt_manager.verify_password("WrongPassword", hashed)
        assert isinstance(hashed, str)
        assert len(hashed) > 50  # bcrypt hashes are typically 60 characters
    
    def test_session_id_generation(self, jwt_manager):
        """اختبار توليد معرف الجلسة"""
        session_id1 = jwt_manager.generate_session_id()
        session_id2 = jwt_manager.generate_session_id()
        
        assert isinstance(session_id1, str)
        assert isinstance(session_id2, str)
        assert session_id1 != session_id2
        assert len(session_id1) > 20
    
    def test_access_token_creation_and_verification(self, jwt_manager):
        """اختبار إنشاء والتحقق من رمز الوصول"""
        user_id = "test_user_123"
        email = "<EMAIL>"
        role = UserRole.USER
        session_id = jwt_manager.generate_session_id()
        
        # Create access token
        token = jwt_manager.create_access_token(
            user_id=user_id,
            email=email,
            role=role,
            session_id=session_id,
            device_id="test_device",
            ip_address="***********"
        )
        
        assert isinstance(token, str)
        assert len(token) > 100
        
        # Verify token
        payload = jwt_manager.verify_token(token, TokenType.ACCESS)
        
        assert payload is not None
        assert payload.user_id == user_id
        assert payload.email == email
        assert payload.role == role
        assert payload.session_id == session_id
        assert payload.device_id == "test_device"
        assert payload.ip_address == "***********"
        assert payload.token_type == TokenType.ACCESS
        assert Permission.READ_USER in payload.permissions
    
    def test_refresh_token_creation_and_verification(self, jwt_manager):
        """اختبار إنشاء والتحقق من رمز التحديث"""
        user_id = "test_user_123"
        session_id = jwt_manager.generate_session_id()
        
        # Create refresh token
        token = jwt_manager.create_refresh_token(user_id, session_id)
        
        assert isinstance(token, str)
        assert len(token) > 100
        
        # Verify token
        payload = jwt_manager.verify_token(token, TokenType.REFRESH)
        
        assert payload is not None
        assert payload.user_id == user_id
        assert payload.session_id == session_id
        assert payload.token_type == TokenType.REFRESH
    
    def test_token_blacklisting(self, jwt_manager):
        """اختبار إضافة الرمز للقائمة السوداء"""
        user_id = "test_user_123"
        email = "<EMAIL>"
        role = UserRole.USER
        session_id = jwt_manager.generate_session_id()
        
        # Create and verify token
        token = jwt_manager.create_access_token(user_id, email, role, session_id)
        payload = jwt_manager.verify_token(token)
        assert payload is not None
        
        # Blacklist token
        success = jwt_manager.blacklist_token(token)
        assert success
        
        # Verify blacklisted token
        payload = jwt_manager.verify_token(token)
        assert payload is None
    
    def test_session_management(self, jwt_manager):
        """اختبار إدارة الجلسات"""
        user_id = "test_user_123"
        email = "<EMAIL>"
        role = UserRole.USER
        session_id = jwt_manager.generate_session_id()
        
        # Create token (creates session)
        token = jwt_manager.create_access_token(user_id, email, role, session_id)
        
        # Check active sessions
        sessions = jwt_manager.get_active_sessions(user_id)
        assert len(sessions) == 1
        assert sessions[0]["session_id"] == session_id
        assert sessions[0]["user_id"] == user_id
        
        # Logout session
        success = jwt_manager.logout_session(session_id)
        assert success
        
        # Check sessions after logout
        sessions = jwt_manager.get_active_sessions(user_id)
        assert len(sessions) == 0
    
    def test_failed_login_tracking(self, jwt_manager):
        """اختبار تتبع محاولات تسجيل الدخول الفاشلة"""
        identifier = "<EMAIL>"
        ip_address = "***********"
        
        # Track failed attempts
        for i in range(3):
            attempt_info = jwt_manager.track_failed_login(identifier, ip_address)
            assert attempt_info["count"] == i + 1
        
        # Check if account is not locked yet (less than 5 attempts)
        assert not jwt_manager.is_account_locked(identifier, ip_address)
        
        # Add more failed attempts to trigger lock
        for i in range(3, 6):
            jwt_manager.track_failed_login(identifier, ip_address)
        
        # Check if account is locked
        assert jwt_manager.is_account_locked(identifier, ip_address)
        
        # Clear failed attempts
        jwt_manager.clear_failed_attempts(identifier, ip_address)
        assert not jwt_manager.is_account_locked(identifier, ip_address)
    
    def test_role_permissions(self, jwt_manager):
        """اختبار صلاحيات الأدوار"""
        # Test admin permissions
        admin_permissions = jwt_manager.role_permissions[UserRole.ADMIN]
        assert Permission.CREATE_USER in admin_permissions
        assert Permission.VIEW_ANALYTICS in admin_permissions
        assert Permission.MANAGE_SETTINGS in admin_permissions
        
        # Test user permissions
        user_permissions = jwt_manager.role_permissions[UserRole.USER]
        assert Permission.READ_USER in user_permissions
        assert Permission.TRANSFER_MONEY in user_permissions
        assert Permission.CREATE_USER not in user_permissions
        
        # Test super admin has all permissions
        super_admin_permissions = jwt_manager.role_permissions[UserRole.SUPER_ADMIN]
        assert len(super_admin_permissions) == len(list(Permission))


class TestAuthService:
    """اختبارات خدمة المصادقة"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """إنشاء التبعيات المزيفة"""
        jwt_manager = Mock(spec=JWTManager)
        db_connection = AsyncMock()
        email_service = AsyncMock()
        sms_service = AsyncMock()
        encryption_service = Mock()
        
        return {
            'jwt_manager': jwt_manager,
            'db_connection': db_connection,
            'email_service': email_service,
            'sms_service': sms_service,
            'encryption_service': encryption_service
        }
    
    @pytest.fixture
    def auth_service(self, mock_dependencies):
        """إنشاء خدمة المصادقة للاختبار"""
        return AuthService(**mock_dependencies)
    
    @pytest.mark.asyncio
    async def test_user_registration_success(self, auth_service, mock_dependencies):
        """اختبار تسجيل المستخدم بنجاح"""
        # Setup mocks
        mock_dependencies['jwt_manager'].hash_password.return_value = "hashed_password"
        auth_service._get_user_by_email = AsyncMock(return_value=None)
        auth_service._get_user_by_phone = AsyncMock(return_value=None)
        auth_service._create_user_in_db = AsyncMock(return_value=True)
        auth_service._send_email_verification = AsyncMock()
        
        # Test data
        registration_data = RegistrationData(
            email="<EMAIL>",
            phone="+************",
            password="TestPassword123!",
            first_name="Test",
            last_name="User",
            date_of_birth="1990-01-01",
            nationality="SAU"
        )
        
        # Register user
        result = await auth_service.register_user(registration_data)
        
        # Assertions
        assert result.success
        assert result.user_id is not None
        assert "تم إنشاء الحساب بنجاح" in result.error
        mock_dependencies['jwt_manager'].hash_password.assert_called_once_with("TestPassword123!")
        auth_service._create_user_in_db.assert_called_once()
        auth_service._send_email_verification.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_user_registration_duplicate_email(self, auth_service):
        """اختبار تسجيل مستخدم ببريد إلكتروني موجود"""
        # Setup mocks
        existing_user = UserData(
            id="existing_user",
            email="<EMAIL>",
            phone="+************",
            password_hash="hash",
            role=UserRole.USER,
            is_active=True,
            is_verified=True,
            two_factor_enabled=False,
            two_factor_secret=None,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            last_login=None,
            failed_login_attempts=0,
            locked_until=None
        )
        auth_service._get_user_by_email = AsyncMock(return_value=existing_user)
        
        # Test data
        registration_data = RegistrationData(
            email="<EMAIL>",
            phone="+966501234568",
            password="TestPassword123!",
            first_name="Test",
            last_name="User",
            date_of_birth="1990-01-01",
            nationality="SAU"
        )
        
        # Register user
        result = await auth_service.register_user(registration_data)
        
        # Assertions
        assert not result.success
        assert "المستخدم موجود بالفعل" in result.error
    
    @pytest.mark.asyncio
    async def test_user_login_success(self, auth_service, mock_dependencies):
        """اختبار تسجيل دخول المستخدم بنجاح"""
        # Setup mocks
        user = UserData(
            id="test_user_123",
            email="<EMAIL>",
            phone="+************",
            password_hash="hashed_password",
            role=UserRole.USER,
            is_active=True,
            is_verified=True,
            two_factor_enabled=False,
            two_factor_secret=None,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            last_login=None,
            failed_login_attempts=0,
            locked_until=None
        )
        
        mock_dependencies['jwt_manager'].is_account_locked.return_value = False
        mock_dependencies['jwt_manager'].verify_password.return_value = True
        mock_dependencies['jwt_manager'].generate_session_id.return_value = "session_123"
        mock_dependencies['jwt_manager'].create_access_token.return_value = "access_token_123"
        mock_dependencies['jwt_manager'].create_refresh_token.return_value = "refresh_token_123"
        mock_dependencies['jwt_manager'].clear_failed_attempts = Mock()
        
        auth_service._get_user_by_email = AsyncMock(return_value=user)
        auth_service._clear_failed_login_attempts = AsyncMock()
        auth_service._update_last_login = AsyncMock()
        
        # Login user
        result = await auth_service.login_user(
            email="<EMAIL>",
            password="TestPassword123!",
            device_id="device_123",
            ip_address="***********"
        )
        
        # Assertions
        assert result.success
        assert result.user_id == "test_user_123"
        assert result.access_token == "access_token_123"
        assert result.refresh_token == "refresh_token_123"
        assert result.session_id == "session_123"
        assert not result.requires_2fa
    
    @pytest.mark.asyncio
    async def test_user_login_invalid_credentials(self, auth_service, mock_dependencies):
        """اختبار تسجيل دخول ببيانات خاطئة"""
        # Setup mocks
        mock_dependencies['jwt_manager'].is_account_locked.return_value = False
        auth_service._get_user_by_email = AsyncMock(return_value=None)
        mock_dependencies['jwt_manager'].track_failed_login = Mock()
        
        # Login with invalid credentials
        result = await auth_service.login_user(
            email="<EMAIL>",
            password="WrongPassword"
        )
        
        # Assertions
        assert not result.success
        assert "بيانات تسجيل الدخول غير صحيحة" in result.error
        mock_dependencies['jwt_manager'].track_failed_login.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_user_login_account_locked(self, auth_service, mock_dependencies):
        """اختبار تسجيل دخول لحساب مقفل"""
        # Setup mocks
        mock_dependencies['jwt_manager'].is_account_locked.return_value = True
        
        # Login with locked account
        result = await auth_service.login_user(
            email="<EMAIL>",
            password="TestPassword123!"
        )
        
        # Assertions
        assert not result.success
        assert "الحساب مقفل مؤقتاً" in result.error
    
    def test_password_strength_validation(self, auth_service):
        """اختبار التحقق من قوة كلمة المرور"""
        # Test weak passwords
        weak_passwords = [
            "123456",  # Too short
            "password",  # No uppercase, numbers, or special chars
            "Password",  # No numbers or special chars
            "Password123",  # No special chars
            "password123!",  # No uppercase
        ]
        
        for password in weak_passwords:
            result = auth_service._validate_password_strength(password)
            assert not result["valid"]
            assert "message" in result
        
        # Test strong password
        strong_password = "StrongPassword123!"
        result = auth_service._validate_password_strength(strong_password)
        assert result["valid"]
        assert result["message"] == "كلمة المرور قوية"


# Integration Tests
class TestAuthIntegration:
    """اختبارات التكامل للمصادقة"""
    
    @pytest.mark.asyncio
    async def test_full_authentication_flow(self):
        """اختبار تدفق المصادقة الكامل"""
        # This would test the full flow with real database connections
        # For now, we'll skip this as it requires database setup
        pass


# Performance Tests
class TestAuthPerformance:
    """اختبارات الأداء للمصادقة"""
    
    def test_password_hashing_performance(self):
        """اختبار أداء تشفير كلمة المرور"""
        jwt_manager = JWTManager("test_secret", "test_refresh_secret")
        password = "TestPassword123!"
        
        import time
        start_time = time.time()
        
        # Hash 10 passwords
        for _ in range(10):
            jwt_manager.hash_password(password)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should complete within reasonable time (less than 5 seconds for 10 hashes)
        assert total_time < 5.0
        
        # Average time per hash should be reasonable (less than 500ms)
        avg_time = total_time / 10
        assert avg_time < 0.5


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
