import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsPhoneNumber,
  MinLength,
  MaxLength,
  Matches,
  IsOptional,
  IsDateString,
  IsIn,
  IsBoolean,
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({
    description: 'الاسم الأول',
    example: 'أحمد',
    minLength: 2,
    maxLength: 50,
  })
  @IsString({ message: 'الاسم الأول يجب أن يكون نص' })
  @MinLength(2, { message: 'الاسم الأول يجب أن يكون على الأقل حرفين' })
  @MaxLength(50, { message: 'الاسم الأول يجب أن يكون أقل من 50 حرف' })
  @Matches(/^[\u0600-\u06FFa-zA-Z\s]+$/, {
    message: 'الاسم الأول يجب أن يحتوي على أحرف عربية أو إنجليزية فقط',
  })
  firstName: string;

  @ApiProperty({
    description: 'الاسم الأخير',
    example: 'محمد',
    minLength: 2,
    maxLength: 50,
  })
  @IsString({ message: 'الاسم الأخير يجب أن يكون نص' })
  @MinLength(2, { message: 'الاسم الأخير يجب أن يكون على الأقل حرفين' })
  @MaxLength(50, { message: 'الاسم الأخير يجب أن يكون أقل من 50 حرف' })
  @Matches(/^[\u0600-\u06FFa-zA-Z\s]+$/, {
    message: 'الاسم الأخير يجب أن يحتوي على أحرف عربية أو إنجليزية فقط',
  })
  lastName: string;

  @ApiProperty({
    description: 'البريد الإلكتروني',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'البريد الإلكتروني غير صالح' })
  @MaxLength(255, { message: 'البريد الإلكتروني طويل جداً' })
  email: string;

  @ApiProperty({
    description: 'رقم الهاتف',
    example: '+966501234567',
  })
  @IsPhoneNumber('SA', { message: 'رقم الهاتف غير صالح' })
  phone: string;

  @ApiProperty({
    description: 'كلمة المرور',
    example: 'MySecurePassword123!',
    minLength: 8,
  })
  @IsString({ message: 'كلمة المرور يجب أن تكون نص' })
  @MinLength(8, { message: 'كلمة المرور يجب أن تكون على الأقل 8 أحرف' })
  @MaxLength(128, { message: 'كلمة المرور طويلة جداً' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    {
      message: 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص',
    },
  )
  password: string;

  @ApiProperty({
    description: 'رقم الهوية الوطنية (اختياري)',
    example: '1234567890',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الهوية يجب أن يكون نص' })
  @Matches(/^\d{10}$/, { message: 'رقم الهوية يجب أن يكون 10 أرقام' })
  nationalId?: string;

  @ApiProperty({
    description: 'تاريخ الميلاد (اختياري)',
    example: '1990-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ الميلاد غير صالح' })
  dateOfBirth?: string;

  @ApiProperty({
    description: 'الجنس (اختياري)',
    example: 'male',
    enum: ['male', 'female'],
    required: false,
  })
  @IsOptional()
  @IsIn(['male', 'female'], { message: 'الجنس يجب أن يكون ذكر أو أنثى' })
  gender?: string;

  @ApiProperty({
    description: 'الجنسية (اختياري)',
    example: 'SA',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الجنسية يجب أن تكون نص' })
  @MaxLength(2, { message: 'الجنسية يجب أن تكون رمز من حرفين' })
  nationality?: string;

  @ApiProperty({
    description: 'اللغة المفضلة',
    example: 'ar',
    enum: ['ar', 'en'],
    default: 'ar',
    required: false,
  })
  @IsOptional()
  @IsIn(['ar', 'en'], { message: 'اللغة يجب أن تكون عربية أو إنجليزية' })
  preferredLanguage?: string = 'ar';

  @ApiProperty({
    description: 'العملة المفضلة',
    example: 'SAR',
    enum: ['SAR', 'USD', 'EUR', 'GBP', 'AED'],
    default: 'SAR',
    required: false,
  })
  @IsOptional()
  @IsIn(['SAR', 'USD', 'EUR', 'GBP', 'AED'], {
    message: 'العملة غير مدعومة',
  })
  preferredCurrency?: string = 'SAR';

  @ApiProperty({
    description: 'تفعيل الإشعارات',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تفعيل الإشعارات يجب أن يكون قيمة منطقية' })
  notificationsEnabled?: boolean = true;
}
