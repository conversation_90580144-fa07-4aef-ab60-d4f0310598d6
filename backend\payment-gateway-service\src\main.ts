import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('PaymentGatewayService');
  
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: [
      'http://localhost:3000',
      'http://localhost:3100',
      'https://ws-transfir.com',
      'https://app.ws-transfir.com',
    ],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true,
  });

  // API prefix
  app.setGlobalPrefix('api/v1');

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('WS Transfir Payment Gateway Service')
    .setDescription('خدمة بوابات الدفع لنظام WS Transfir - معالجة المدفوعات والتحويلات المالية')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag('Payments', 'معالجة المدفوعات')
    .addTag('Gateways', 'بوابات الدفع')
    .addTag('Transactions', 'المعاملات المالية')
    .addTag('Webhooks', 'إشعارات بوابات الدفع')
    .addTag('Exchange Rates', 'أسعار الصرف')
    .addTag('Health', 'فحص صحة النظام')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  // Health check endpoint
  app.getHttpAdapter().get('/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      service: 'payment-gateway-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: configService.get('NODE_ENV'),
      version: '1.0.0',
      gateways: {
        stripe: configService.get('STRIPE_ENABLED') === 'true',
        paypal: configService.get('PAYPAL_ENABLED') === 'true',
        sadad: configService.get('SADAD_ENABLED') === 'true',
        mada: configService.get('MADA_ENABLED') === 'true',
      },
    });
  });

  // Start server
  const port = configService.get('PAYMENT_GATEWAY_SERVICE_PORT') || 3004;
  await app.listen(port);

  logger.log(`🚀 Payment Gateway Service is running on: http://localhost:${port}`);
  logger.log(`📚 API Documentation: http://localhost:${port}/docs`);
  logger.log(`🏥 Health Check: http://localhost:${port}/health`);
  logger.log(`🌍 Environment: ${configService.get('NODE_ENV')}`);
  logger.log(`💳 Enabled Gateways: ${getEnabledGateways(configService)}`);
}

function getEnabledGateways(configService: ConfigService): string {
  const gateways = [];
  
  if (configService.get('STRIPE_ENABLED') === 'true') gateways.push('Stripe');
  if (configService.get('PAYPAL_ENABLED') === 'true') gateways.push('PayPal');
  if (configService.get('SADAD_ENABLED') === 'true') gateways.push('SADAD');
  if (configService.get('MADA_ENABLED') === 'true') gateways.push('mada');
  
  return gateways.length > 0 ? gateways.join(', ') : 'None';
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start Payment Gateway Service:', error);
  process.exit(1);
});
