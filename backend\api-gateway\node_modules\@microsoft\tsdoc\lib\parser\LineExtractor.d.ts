import type { ParserContext } from './ParserContext';
/**
 * The main API for parsing TSDoc comments.
 */
export declare class LineExtractor {
    private static readonly _whitespaceCharacterRegExp;
    /**
     * This step parses an entire code comment from slash-star-star until star-slash,
     * and extracts the content lines.  The lines are stored in IDocCommentParameters.lines
     * and the overall text range is assigned to IDocCommentParameters.range.
     */
    static extract(parserContext: ParserContext): boolean;
}
//# sourceMappingURL=LineExtractor.d.ts.map