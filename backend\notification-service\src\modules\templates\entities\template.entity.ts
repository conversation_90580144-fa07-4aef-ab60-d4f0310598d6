import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { NotificationType } from '../../notifications/entities/notification.entity';

export enum TemplateStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

export enum TemplateCategory {
  TRANSACTION = 'transaction',
  SECURITY = 'security',
  MARKETING = 'marketing',
  SYSTEM = 'system',
  REMINDER = 'reminder',
}

@Entity('notification_templates')
@Index(['type', 'status'])
@Index(['category', 'status'])
@Index(['code'], { unique: true })
export class NotificationTemplate {
  @ApiProperty({ description: 'معرف القالب' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'كود القالب الفريد' })
  @Column({ unique: true, length: 100 })
  code: string;

  @ApiProperty({ description: 'اسم القالب' })
  @Column({ length: 200 })
  name: string;

  @ApiProperty({ description: 'وصف القالب' })
  @Column('text', { nullable: true })
  description?: string;

  @ApiProperty({ description: 'نوع الإشعار', enum: NotificationType })
  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @ApiProperty({ description: 'فئة القالب', enum: TemplateCategory })
  @Column({
    type: 'enum',
    enum: TemplateCategory,
  })
  category: TemplateCategory;

  @ApiProperty({ description: 'حالة القالب', enum: TemplateStatus })
  @Column({
    type: 'enum',
    enum: TemplateStatus,
    default: TemplateStatus.DRAFT,
  })
  status: TemplateStatus;

  @ApiProperty({ description: 'عنوان القالب' })
  @Column({ length: 500 })
  title: string;

  @ApiProperty({ description: 'محتوى القالب' })
  @Column('text')
  content: string;

  @ApiProperty({ description: 'قالب HTML للبريد الإلكتروني', required: false })
  @Column('text', { nullable: true })
  htmlContent?: string;

  @ApiProperty({ description: 'متغيرات القالب' })
  @Column('jsonb', { default: '[]' })
  variables: string[];

  @ApiProperty({ description: 'إعدادات القالب' })
  @Column('jsonb', { nullable: true })
  settings?: {
    priority?: string;
    expiryHours?: number;
    maxAttempts?: number;
    allowBatching?: boolean;
    trackEngagement?: boolean;
    requiresApproval?: boolean;
  };

  @ApiProperty({ description: 'بيانات وصفية' })
  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'اللغة' })
  @Column({ length: 5, default: 'ar' })
  language: string;

  @ApiProperty({ description: 'الإصدار' })
  @Column('int', { default: 1 })
  version: number;

  @ApiProperty({ description: 'معرف المستخدم المنشئ' })
  @Column('uuid', { nullable: true })
  createdBy?: string;

  @ApiProperty({ description: 'معرف آخر مستخدم محدث' })
  @Column('uuid', { nullable: true })
  updatedBy?: string;

  @ApiProperty({ description: 'تاريخ آخر استخدام' })
  @Column('timestamp', { nullable: true })
  lastUsedAt?: Date;

  @ApiProperty({ description: 'عدد مرات الاستخدام' })
  @Column('int', { default: 0 })
  usageCount: number;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'تاريخ آخر تحديث' })
  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  activate(): void {
    this.status = TemplateStatus.ACTIVE;
  }

  deactivate(): void {
    this.status = TemplateStatus.INACTIVE;
  }

  incrementUsage(): void {
    this.usageCount++;
    this.lastUsedAt = new Date();
  }

  renderContent(variables: Record<string, any>): string {
    let renderedContent = this.content;
    
    // Replace variables in content
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = variables[key] || '';
      renderedContent = renderedContent.replace(new RegExp(placeholder, 'g'), value);
    });

    return renderedContent;
  }

  renderTitle(variables: Record<string, any>): string {
    let renderedTitle = this.title;
    
    // Replace variables in title
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = variables[key] || '';
      renderedTitle = renderedTitle.replace(new RegExp(placeholder, 'g'), value);
    });

    return renderedTitle;
  }

  renderHtmlContent(variables: Record<string, any>): string | null {
    if (!this.htmlContent) return null;

    let renderedHtml = this.htmlContent;
    
    // Replace variables in HTML content
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = variables[key] || '';
      renderedHtml = renderedHtml.replace(new RegExp(placeholder, 'g'), value);
    });

    return renderedHtml;
  }

  validateVariables(variables: Record<string, any>): { isValid: boolean; missingVariables: string[] } {
    const missingVariables = this.variables.filter(variable => 
      !(variable in variables) || variables[variable] === undefined || variables[variable] === null
    );

    return {
      isValid: missingVariables.length === 0,
      missingVariables,
    };
  }

  isActive(): boolean {
    return this.status === TemplateStatus.ACTIVE;
  }

  canBeUsed(): boolean {
    return this.isActive();
  }

  createNewVersion(): Partial<NotificationTemplate> {
    return {
      code: this.code,
      name: this.name,
      description: this.description,
      type: this.type,
      category: this.category,
      title: this.title,
      content: this.content,
      htmlContent: this.htmlContent,
      variables: [...this.variables],
      settings: { ...this.settings },
      metadata: { ...this.metadata },
      language: this.language,
      version: this.version + 1,
      status: TemplateStatus.DRAFT,
    };
  }
}
