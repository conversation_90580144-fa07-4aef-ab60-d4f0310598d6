import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../src/modules/users/entities/user.entity';
import { UserSession } from '../src/modules/auth/entities/user-session.entity';

describe('Auth Controller (e2e)', () => {
  let app: INestApplication;
  let userRepository: any;
  let sessionRepository: any;

  const mockUserRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockSessionRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(getRepositoryToken(User))
      .useValue(mockUserRepository)
      .overrideProvider(getRepositoryToken(UserSession))
      .useValue(mockSessionRepository)
      .compile();

    app = moduleFixture.createNestApplication();
    
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    await app.init();

    userRepository = moduleFixture.get(getRepositoryToken(User));
    sessionRepository = moduleFixture.get(getRepositoryToken(UserSession));
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('/api/v1/auth/register (POST)', () => {
    const validRegisterData = {
      firstName: 'أحمد',
      lastName: 'محمد',
      email: '<EMAIL>',
      phone: '+************',
      password: 'SecurePass123!',
      confirmPassword: 'SecurePass123!',
      acceptTerms: 'true',
      acceptPrivacy: 'true',
    };

    it('should register a new user successfully', async () => {
      // Mock user not exists
      mockUserRepository.findOne.mockResolvedValue(null);
      
      // Mock user creation
      const mockUser = {
        id: 'uuid-123',
        ...validRegisterData,
        status: 'pending',
        emailVerificationToken: 'token-123',
        phoneVerificationCode: '123456',
      };
      
      mockUserRepository.create.mockReturnValue(mockUser);
      mockUserRepository.save.mockResolvedValue(mockUser);

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(validRegisterData)
        .expect(201);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('userId');
      expect(response.body.message).toContain('تم إنشاء الحساب بنجاح');
    });

    it('should fail with invalid email', async () => {
      const invalidData = {
        ...validRegisterData,
        email: 'invalid-email',
      };

      await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(invalidData)
        .expect(400);
    });

    it('should fail with weak password', async () => {
      const invalidData = {
        ...validRegisterData,
        password: '123',
        confirmPassword: '123',
      };

      await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(invalidData)
        .expect(400);
    });

    it('should fail if user already exists', async () => {
      // Mock user exists
      mockUserRepository.findOne.mockResolvedValue({ id: 'existing-user' });

      await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(validRegisterData)
        .expect(409);
    });
  });

  describe('/api/v1/auth/login (POST)', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'SecurePass123!',
    };

    it('should login successfully with valid credentials', async () => {
      const mockUser = {
        id: 'uuid-123',
        email: '<EMAIL>',
        status: 'active',
        emailVerified: true,
        phoneVerified: true,
        isLocked: false,
        validatePassword: jest.fn().mockResolvedValue(true),
        updateLastLogin: jest.fn(),
        incrementFailedAttempts: jest.fn(),
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockUserRepository.save.mockResolvedValue(mockUser);
      mockSessionRepository.create.mockReturnValue({});
      mockSessionRepository.save.mockResolvedValue({});

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send(validLoginData)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body).toHaveProperty('expiresIn');
    });

    it('should fail with invalid credentials', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send(validLoginData)
        .expect(401);
    });

    it('should fail with wrong password', async () => {
      const mockUser = {
        id: 'uuid-123',
        email: '<EMAIL>',
        status: 'active',
        validatePassword: jest.fn().mockResolvedValue(false),
        incrementFailedAttempts: jest.fn(),
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockUserRepository.save.mockResolvedValue(mockUser);

      await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send(validLoginData)
        .expect(401);
    });

    it('should fail with blocked account', async () => {
      const mockUser = {
        id: 'uuid-123',
        email: '<EMAIL>',
        status: 'blocked',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);

      await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send(validLoginData)
        .expect(403);
    });
  });

  describe('/api/v1/health (GET)', () => {
    it('should return health status', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/health')
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('service');
      expect(response.body.service).toBe('auth-service');
    });
  });

  describe('/api/v1/auth/verify-email (POST)', () => {
    it('should verify email successfully', async () => {
      const mockUser = {
        id: 'uuid-123',
        emailVerificationToken: 'valid-token',
        emailVerificationExpires: new Date(Date.now() + 3600000), // 1 hour from now
        emailVerified: false,
        phoneVerified: true,
        status: 'pending',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockUserRepository.save.mockResolvedValue({
        ...mockUser,
        emailVerified: true,
        status: 'active',
      });

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/verify-email')
        .send({ token: 'valid-token' })
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('تم تفعيل البريد الإلكتروني بنجاح');
    });

    it('should fail with invalid token', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      await request(app.getHttpServer())
        .post('/api/v1/auth/verify-email')
        .send({ token: 'invalid-token' })
        .expect(400);
    });

    it('should fail with expired token', async () => {
      const mockUser = {
        id: 'uuid-123',
        emailVerificationToken: 'expired-token',
        emailVerificationExpires: new Date(Date.now() - 3600000), // 1 hour ago
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);

      await request(app.getHttpServer())
        .post('/api/v1/auth/verify-email')
        .send({ token: 'expired-token' })
        .expect(400);
    });
  });

  describe('/api/v1/auth/verify-phone (POST)', () => {
    it('should verify phone successfully', async () => {
      const mockUser = {
        id: 'uuid-123',
        phone: '+************',
        phoneVerificationCode: '123456',
        phoneVerificationExpires: new Date(Date.now() + 600000), // 10 minutes from now
        phoneVerified: false,
        emailVerified: true,
        status: 'pending',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockUserRepository.save.mockResolvedValue({
        ...mockUser,
        phoneVerified: true,
        status: 'active',
      });

      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/verify-phone')
        .send({ 
          phone: '+************',
          code: '123456'
        })
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('تم تفعيل رقم الهاتف بنجاح');
    });

    it('should fail with invalid code', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      await request(app.getHttpServer())
        .post('/api/v1/auth/verify-phone')
        .send({ 
          phone: '+************',
          code: '000000'
        })
        .expect(400);
    });
  });
});
