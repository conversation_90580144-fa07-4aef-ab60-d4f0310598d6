# 🚀 دليل تشغيل نظام WS Transfir

## نظرة عامة
نظام WS Transfir هو منصة متكاملة للتحويلات المالية تتكون من 9 خدمات backend و 12 صفحة frontend مع نظام مراقبة متقدم.

## 📋 المتطلبات الأساسية

### 1. البرامج المطلوبة
- **Docker Desktop** (الإصدار 20.10 أو أحدث)
- **Docker Compose** (الإصدار 2.0 أو أحدث)
- **Git** (لتحميل الكود)
- **Node.js** (الإصدار 18 أو أحدث) - اختياري للتطوير

### 2. متطلبات النظام
- **الذاكرة**: 8 GB RAM كحد أدنى (16 GB مُوصى به)
- **التخزين**: 20 GB مساحة فارغة
- **المعالج**: 4 cores كحد أدنى
- **نظام التشغيل**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+

## 🚀 تشغيل النظام

### للـ Linux/macOS:
```bash
# تشغيل النظام
./start-system.sh

# مراقبة النظام
./monitor-system.sh

# إيقاف النظام
./stop-system.sh
```

### للـ Windows:
```cmd
# تشغيل النظام
start-system.bat

# مراقبة النظام (استخدم PowerShell أو Git Bash)
bash monitor-system.sh

# إيقاف النظام
docker-compose down
```

## 🌐 روابط الوصول

### التطبيق الرئيسي
- **الموقع الإلكتروني**: http://localhost
- **التطبيق المباشر**: http://localhost:3100

### واجهات الإدارة
- **Grafana (المراقبة)**: http://localhost:3007
  - المستخدم: `admin`
  - كلمة المرور: `admin123`
- **Prometheus**: http://localhost:9090
- **Kibana (البحث)**: http://localhost:5601
- **Adminer (قاعدة البيانات)**: http://localhost:8080
- **Redis Commander**: http://localhost:8081
- **RabbitMQ Management**: http://localhost:15672
  - المستخدم: `ws_user`
  - كلمة المرور: `ws_password`
- **Jaeger (التتبع)**: http://localhost:16686

### APIs الخدمات
- **API Gateway**: http://localhost:3000
- **Auth Service**: http://localhost:3001
- **User Service**: http://localhost:3002
- **Transfer Service**: http://localhost:3003
- **Wallet Service**: http://localhost:3004
- **Notification Service**: http://localhost:3005
- **Analytics Service**: http://localhost:3006
- **AI Engine**: http://localhost:8000

## 📊 مراقبة النظام

### أوامر المراقبة
```bash
# عرض حالة النظام العامة
./monitor-system.sh status

# فحص صحة الخدمات
./monitor-system.sh health

# عرض استخدام الموارد
./monitor-system.sh resources

# عرض سجلات خدمة معينة
./monitor-system.sh logs api-gateway

# عرض سجلات جميع الخدمات
./monitor-system.sh logs-all

# مراقبة مباشرة
./monitor-system.sh monitor
```

## 🛠️ إدارة النظام

### إيقاف النظام
```bash
# إيقاف عادي
./stop-system.sh

# إيقاف مع حذف البيانات
./stop-system.sh --volumes

# إيقاف مع حذف كل شيء
./stop-system.sh --all

# تنظيف الحاويات المهجورة
./stop-system.sh --cleanup
```

### إعادة تشغيل خدمة معينة
```bash
# إعادة تشغيل خدمة واحدة
docker-compose restart auth-service

# إعادة بناء وتشغيل خدمة
docker-compose up -d --build auth-service
```

### عرض السجلات
```bash
# عرض سجلات خدمة معينة
docker-compose logs -f auth-service

# عرض آخر 100 سطر
docker-compose logs --tail 100 auth-service
```

## ⚙️ الإعدادات

### ملف البيئة (.env)
يتم إنشاء ملف `.env` تلقائياً عند أول تشغيل. يمكنك تعديل الإعدادات التالية:

```env
# إعدادات قاعدة البيانات
POSTGRES_USER=ws_user
POSTGRES_PASSWORD=ws_password
POSTGRES_DB=ws_transfir

# إعدادات JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key

# إعدادات البريد الإلكتروني
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# إعدادات الرسائل النصية
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token

# بوابات الدفع
STRIPE_SECRET_KEY=sk_test_your_stripe_key
PAYPAL_CLIENT_ID=your_paypal_client_id
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل في بدء الخدمات
```bash
# تحقق من حالة Docker
docker info

# تحقق من المنافذ المستخدمة
netstat -tulpn | grep :3000

# إعادة تشغيل Docker
sudo systemctl restart docker  # Linux
# أو إعادة تشغيل Docker Desktop
```

#### 2. نفاد الذاكرة
```bash
# تحقق من استخدام الذاكرة
docker stats

# زيادة ذاكرة Docker Desktop
# Settings > Resources > Memory > 8GB+
```

#### 3. مشاكل قاعدة البيانات
```bash
# إعادة تشغيل قاعدة البيانات
docker-compose restart postgres

# فحص سجلات قاعدة البيانات
docker-compose logs postgres

# الاتصال بقاعدة البيانات
docker-compose exec postgres psql -U ws_user -d ws_transfir
```

#### 4. مشاكل الشبكة
```bash
# فحص الشبكات
docker network ls

# إعادة إنشاء الشبكة
docker-compose down
docker network prune
docker-compose up -d
```

## 📈 مؤشرات الأداء

### مؤشرات مهمة للمراقبة
- **استخدام المعالج**: يجب أن يكون أقل من 80%
- **استخدام الذاكرة**: يجب أن يكون أقل من 85%
- **زمن الاستجابة**: يجب أن يكون أقل من 500ms
- **معدل الأخطاء**: يجب أن يكون أقل من 1%

### تحسين الأداء
```bash
# تنظيف الصور غير المستخدمة
docker image prune -a

# تنظيف وحدات التخزين غير المستخدمة
docker volume prune

# تنظيف الشبكات غير المستخدمة
docker network prune
```

## 🔒 الأمان

### إعدادات الأمان المهمة
1. **تغيير كلمات المرور الافتراضية**
2. **تفعيل HTTPS في الإنتاج**
3. **تحديث المفاتيح السرية**
4. **تفعيل جدار الحماية**
5. **مراقبة السجلات الأمنية**

### نسخ احتياطية
```bash
# نسخ احتياطي لقاعدة البيانات
docker-compose exec postgres pg_dump -U ws_user ws_transfir > backup.sql

# استعادة النسخة الاحتياطية
docker-compose exec -T postgres psql -U ws_user ws_transfir < backup.sql
```

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. تحقق من السجلات: `./monitor-system.sh logs-all`
2. تحقق من حالة الخدمات: `./monitor-system.sh health`
3. تحقق من استخدام الموارد: `./monitor-system.sh resources`
4. راجع هذا الدليل للحلول الشائعة

### معلومات النظام
- **الإصدار**: 1.0.0
- **تاريخ الإصدار**: ديسمبر 2024
- **البيئة**: Development
- **الخدمات**: 9 خدمات backend + 12 صفحة frontend

---

**ملاحظة**: هذا النظام مُعد للتطوير والاختبار. للاستخدام في الإنتاج، يرجى مراجعة إعدادات الأمان وتحديث المتغيرات البيئية.
