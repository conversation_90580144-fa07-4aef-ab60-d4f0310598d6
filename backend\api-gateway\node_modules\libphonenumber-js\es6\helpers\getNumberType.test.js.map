{"version": 3, "file": "getNumberType.test.js", "names": ["getNumberType", "oldMetadata", "type", "<PERSON><PERSON><PERSON>", "describe", "it", "nationalNumber", "country", "v2", "should", "equal", "expect", "to", "undefined"], "sources": ["../../source/helpers/getNumberType.test.js"], "sourcesContent": ["import getNumberType from './getNumberType.js'\r\n\r\nimport oldMetadata from '../../test/metadata/1.0.0/metadata.min.json' assert { type: 'json' }\r\n\r\nimport Metadata from '../metadata.js'\r\n\r\ndescribe('getNumberType', function() {\r\n\tit('should get number type when using old metadata', function() {\r\n\t\tgetNumberType(\r\n\t\t\t{\r\n\t\t\t\tnationalNumber: '2133734253',\r\n\t\t\t\tcountry: 'US'\r\n\t\t\t},\r\n\t\t\t{ v2: true },\r\n\t\t\toldMetadata\r\n\t\t).should.equal('FIXED_LINE_OR_MOBILE')\r\n\t})\r\n\r\n\tit('should return `undefined` when the phone number is a malformed one', function() {\r\n\t\texpect(getNumberType(\r\n\t\t\t{},\r\n\t\t\t{ v2: true },\r\n\t\t\toldMetadata\r\n\t\t)).to.equal(undefined)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,oBAA1B;AAEA,OAAOC,WAAP,MAAwB,6CAAxB,UAA+EC,IAAI,EAAE,MAArF;AAEA,OAAOC,QAAP,MAAqB,gBAArB;AAEAC,QAAQ,CAAC,eAAD,EAAkB,YAAW;EACpCC,EAAE,CAAC,gDAAD,EAAmD,YAAW;IAC/DL,aAAa,CACZ;MACCM,cAAc,EAAE,YADjB;MAECC,OAAO,EAAE;IAFV,CADY,EAKZ;MAAEC,EAAE,EAAE;IAAN,CALY,EAMZP,WANY,CAAb,CAOEQ,MAPF,CAOSC,KAPT,CAOe,sBAPf;EAQA,CATC,CAAF;EAWAL,EAAE,CAAC,oEAAD,EAAuE,YAAW;IACnFM,MAAM,CAACX,aAAa,CACnB,EADmB,EAEnB;MAAEQ,EAAE,EAAE;IAAN,CAFmB,EAGnBP,WAHmB,CAAd,CAAN,CAIGW,EAJH,CAIMF,KAJN,CAIYG,SAJZ;EAKA,CANC,CAAF;AAOA,CAnBO,CAAR"}