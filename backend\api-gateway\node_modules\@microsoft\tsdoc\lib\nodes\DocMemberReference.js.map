{"version": 3, "file": "DocMemberReference.js", "sourceRoot": "", "sources": ["../../src/nodes/DocMemberReference.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,OAAO,EAAE,WAAW,EAA0D,MAAM,WAAW,CAAC;AAKzG,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAuCvD;;;;;;;;GAQG;AACH;IAAwC,sCAAO;IAyB7C;;;OAGG;IACH,4BAAmB,UAA+E;QAChG,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,OAAO,GAAG,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC;YACvC,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC1B,KAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC;oBAChC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,mBAAmB;oBAC5C,OAAO,EAAE,UAAU,CAAC,UAAU;iBAC/B,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,sBAAsB,EAAE,CAAC;gBACtC,KAAI,CAAC,uBAAuB,GAAG,IAAI,UAAU,CAAC;oBAC5C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,sBAAsB;iBAC3C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,sBAAsB,EAAE,CAAC;gBACtC,KAAI,CAAC,uBAAuB,GAAG,IAAI,UAAU,CAAC;oBAC5C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,+BAA+B;oBACxD,OAAO,EAAE,UAAU,CAAC,sBAAsB;iBAC3C,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,kCAAkC,EAAE,CAAC;gBAClD,KAAI,CAAC,mCAAmC,GAAG,IAAI,UAAU,CAAC;oBACxD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,kCAAkC;iBACvD,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,yBAAyB,EAAE,CAAC;gBACzC,KAAI,CAAC,0BAA0B,GAAG,IAAI,UAAU,CAAC;oBAC/C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,yBAAyB;iBAC9C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC5B,KAAI,CAAC,aAAa,GAAG,IAAI,UAAU,CAAC;oBAClC,WAAW,EAAE,WAAW,CAAC,qBAAqB;oBAC9C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,OAAO,EAAE,UAAU,CAAC,YAAY;iBACjC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,wBAAwB,EAAE,CAAC;gBACxC,KAAI,CAAC,yBAAyB,GAAG,IAAI,UAAU,CAAC;oBAC9C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,wBAAwB;iBAC7C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,2BAA2B,EAAE,CAAC;gBAC3C,KAAI,CAAC,4BAA4B,GAAG,IAAI,UAAU,CAAC;oBACjD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,2BAA2B;iBAChD,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,uBAAuB,EAAE,CAAC;gBACvC,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;oBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,gCAAgC;oBACzD,OAAO,EAAE,UAAU,CAAC,uBAAuB;iBAC5C,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,mCAAmC,EAAE,CAAC;gBACnD,KAAI,CAAC,oCAAoC,GAAG,IAAI,UAAU,CAAC;oBACzD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,mCAAmC;iBACxD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;QACnC,CAAC;QAED,KAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,gBAAgB,CAAC;QACrD,KAAI,CAAC,aAAa,GAAG,UAAU,CAAC,YAAY,CAAC;QAC7C,KAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;;IACvC,CAAC;IAGD,sBAAW,oCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,eAAe,CAAC;QACrC,CAAC;;;OAAA;IAMD,sBAAW,sCAAM;QAJjB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAOD,sBAAW,gDAAgB;QAL3B;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,CAAC;;;OAAA;IAQD,sBAAW,4CAAY;QANvB;;;;;WAKG;aACH;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;;;OAAA;IAMD,sBAAW,wCAAQ;QAJnB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAED,gBAAgB;IACN,4CAAe,GAAzB;QACE,OAAO;YACL,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,uBAAuB;YAE5B,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,mCAAmC;YAExC,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,0BAA0B;YAE/B,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,yBAAyB;YAE9B,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,4BAA4B;YAEjC,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,oCAAoC;SAC1C,CAAC;IACJ,CAAC;IACH,yBAAC;AAAD,CAAC,AArLD,CAAwC,OAAO,GAqL9C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNode, DocNodeKind, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { DocMemberIdentifier } from './DocMemberIdentifier';\r\nimport type { DocMemberSymbol } from './DocMemberSymbol';\r\nimport type { DocMemberSelector } from './DocMemberSelector';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberReference}.\r\n */\r\nexport interface IDocMemberReferenceParameters extends IDocNodeParameters {\r\n  hasDot: boolean;\r\n\r\n  memberIdentifier?: DocMemberIdentifier;\r\n  memberSymbol?: DocMemberSymbol;\r\n\r\n  selector?: DocMemberSelector;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberReference}.\r\n */\r\nexport interface IDocMemberReferenceParsedParameters extends IDocNodeParsedParameters {\r\n  dotExcerpt?: TokenSequence;\r\n  spacingAfterDotExcerpt?: TokenSequence;\r\n\r\n  leftParenthesisExcerpt?: TokenSequence;\r\n  spacingAfterLeftParenthesisExcerpt?: TokenSequence;\r\n\r\n  memberIdentifier?: DocMemberIdentifier;\r\n  memberSymbol?: DocMemberSymbol;\r\n\r\n  spacingAfterMemberExcerpt?: TokenSequence;\r\n\r\n  colonExcerpt?: TokenSequence;\r\n  spacingAfterColonExcerpt?: TokenSequence;\r\n\r\n  selector?: DocMemberSelector;\r\n  spacingAfterSelectorExcerpt?: TokenSequence;\r\n\r\n  rightParenthesisExcerpt?: TokenSequence;\r\n  spacingAfterRightParenthesisExcerpt?: TokenSequence;\r\n}\r\n\r\n/**\r\n * A {@link DocDeclarationReference | declaration reference} includes a chain of\r\n * member references represented using `DocMemberReference` nodes.\r\n *\r\n * @remarks\r\n * For example, `example-library#ui.controls.Button.(render:static)` is a\r\n * declaration reference that contains three member references:\r\n * `ui`, `.controls`, and `.Button`, and `.(render:static)`.\r\n */\r\nexport class DocMemberReference extends DocNode {\r\n  // The \".\" token if unless this was the member reference in the chain\r\n  private readonly _hasDot: boolean;\r\n  private readonly _dotExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterDotExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _leftParenthesisExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterLeftParenthesisExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _memberIdentifier: DocMemberIdentifier | undefined;\r\n\r\n  private readonly _memberSymbol: DocMemberSymbol | undefined;\r\n\r\n  private readonly _spacingAfterMemberExcerpt: DocExcerpt | undefined;\r\n\r\n  // The \":\" token that separates the identifier and selector parts\r\n  private readonly _colonExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterColonExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _selector: DocMemberSelector | undefined;\r\n  private readonly _spacingAfterSelectorExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _rightParenthesisExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterRightParenthesisExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocMemberReferenceParameters | IDocMemberReferenceParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._hasDot = !!parameters.dotExcerpt;\r\n      if (parameters.dotExcerpt) {\r\n        this._dotExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.MemberReference_Dot,\r\n          content: parameters.dotExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterDotExcerpt) {\r\n        this._spacingAfterDotExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterDotExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.leftParenthesisExcerpt) {\r\n        this._leftParenthesisExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.MemberReference_LeftParenthesis,\r\n          content: parameters.leftParenthesisExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterLeftParenthesisExcerpt) {\r\n        this._spacingAfterLeftParenthesisExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterLeftParenthesisExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.spacingAfterMemberExcerpt) {\r\n        this._spacingAfterMemberExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterMemberExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.colonExcerpt) {\r\n        this._colonExcerpt = new DocExcerpt({\r\n          excerptKind: ExcerptKind.MemberReference_Colon,\r\n          configuration: this.configuration,\r\n          content: parameters.colonExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterColonExcerpt) {\r\n        this._spacingAfterColonExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterColonExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.spacingAfterSelectorExcerpt) {\r\n        this._spacingAfterSelectorExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterSelectorExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.rightParenthesisExcerpt) {\r\n        this._rightParenthesisExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.MemberReference_RightParenthesis,\r\n          content: parameters.rightParenthesisExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterRightParenthesisExcerpt) {\r\n        this._spacingAfterRightParenthesisExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterRightParenthesisExcerpt\r\n        });\r\n      }\r\n    } else {\r\n      this._hasDot = parameters.hasDot;\r\n    }\r\n\r\n    this._memberIdentifier = parameters.memberIdentifier;\r\n    this._memberSymbol = parameters.memberSymbol;\r\n    this._selector = parameters.selector;\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.MemberReference;\r\n  }\r\n\r\n  /**\r\n   * True if this member reference is preceded by a dot (\".\") token.\r\n   * It should be false only for the first member in the chain.\r\n   */\r\n  public get hasDot(): boolean {\r\n    return this._hasDot;\r\n  }\r\n\r\n  /**\r\n   * The identifier for the referenced member.\r\n   * @remarks\r\n   * Either `memberIdentifier` or `memberSymbol` may be specified, but not both.\r\n   */\r\n  public get memberIdentifier(): DocMemberIdentifier | undefined {\r\n    return this._memberIdentifier;\r\n  }\r\n\r\n  /**\r\n   * The ECMAScript 6 symbol expression, which may be used instead of an identifier\r\n   * to indicate the referenced member.\r\n   * @remarks\r\n   * Either `memberIdentifier` or `memberSymbol` may be specified, but not both.\r\n   */\r\n  public get memberSymbol(): DocMemberSymbol | undefined {\r\n    return this._memberSymbol;\r\n  }\r\n\r\n  /**\r\n   * A TSDoc selector, which may be optionally when the identifier or symbol is insufficient\r\n   * to unambiguously determine the referenced declaration.\r\n   */\r\n  public get selector(): DocMemberSelector | undefined {\r\n    return this._selector;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this._dotExcerpt,\r\n      this._spacingAfterDotExcerpt,\r\n\r\n      this._leftParenthesisExcerpt,\r\n      this._spacingAfterLeftParenthesisExcerpt,\r\n\r\n      this._memberIdentifier,\r\n      this._memberSymbol,\r\n      this._spacingAfterMemberExcerpt,\r\n\r\n      this._colonExcerpt,\r\n      this._spacingAfterColonExcerpt,\r\n\r\n      this._selector,\r\n      this._spacingAfterSelectorExcerpt,\r\n\r\n      this._rightParenthesisExcerpt,\r\n      this._spacingAfterRightParenthesisExcerpt\r\n    ];\r\n  }\r\n}\r\n"]}