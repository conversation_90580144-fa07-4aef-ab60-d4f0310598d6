"""
Notification Scheduler Service
=============================
خدمة جدولة الإشعارات المتقدمة
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import json
import uuid
try:
    from croniter import croniter
except ImportError:
    # Fallback if croniter is not available
    class croniter:
        def __init__(self, cron_expr, base_time):
            self.cron_expr = cron_expr
            self.base_time = base_time

        def get_next(self, ret_type):
            # Simple fallback - add 1 hour
            return self.base_time + timedelta(hours=1)

import asyncpg
from ..shared.database.connection import DatabaseConnection
from .notification_service import NotificationService, NotificationRequest, NotificationChannel, NotificationType, NotificationPriority

logger = logging.getLogger(__name__)


class ScheduleType(Enum):
    """أنواع الجدولة"""
    ONE_TIME = "one_time"
    RECURRING = "recurring"
    CONDITIONAL = "conditional"


class ScheduleStatus(Enum):
    """حالات الجدولة"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    FAILED = "failed"


@dataclass
class ScheduledNotification:
    """إشعار مجدول"""
    id: str
    name: str
    description: str
    schedule_type: ScheduleType
    status: ScheduleStatus
    notification_request: NotificationRequest
    
    # Scheduling details
    scheduled_at: Optional[datetime] = None
    cron_expression: Optional[str] = None
    timezone: str = "Asia/Riyadh"
    
    # Recurrence settings
    max_executions: Optional[int] = None
    execution_count: int = 0
    
    # Conditional settings
    condition_query: Optional[str] = None
    condition_params: Optional[Dict[str, Any]] = None
    
    # Metadata
    created_at: datetime = None
    updated_at: datetime = None
    created_by: str = None
    last_executed_at: Optional[datetime] = None
    next_execution_at: Optional[datetime] = None


class NotificationScheduler:
    """خدمة جدولة الإشعارات"""
    
    def __init__(self, db_connection: DatabaseConnection, notification_service: NotificationService):
        self.db_connection = db_connection
        self.notification_service = notification_service
        
        # Scheduler settings
        self.check_interval = 60  # seconds
        self.max_concurrent_jobs = 10
        self.is_running = False
        
        # Statistics
        self.jobs_executed = 0
        self.jobs_failed = 0
        self.last_check_time = None
        
        # Active jobs tracking
        self.active_jobs = {}
        
        # Event handlers
        self.job_handlers = {
            'before_execute': [],
            'after_execute': [],
            'on_error': []
        }
    
    async def schedule_notification(
        self,
        name: str,
        notification_request: NotificationRequest,
        schedule_type: ScheduleType,
        scheduled_at: datetime = None,
        cron_expression: str = None,
        max_executions: int = None,
        condition_query: str = None,
        condition_params: Dict[str, Any] = None,
        created_by: str = None,
        description: str = None
    ) -> str:
        """جدولة إشعار"""
        try:
            logger.info(f"📅 Scheduling notification: {name}")
            
            # Generate schedule ID
            schedule_id = f"sched_{uuid.uuid4().hex[:12]}"
            
            # Validate schedule parameters
            self._validate_schedule_params(schedule_type, scheduled_at, cron_expression)
            
            # Calculate next execution time
            next_execution = self._calculate_next_execution(
                schedule_type, scheduled_at, cron_expression
            )
            
            # Create scheduled notification
            scheduled_notification = ScheduledNotification(
                id=schedule_id,
                name=name,
                description=description or f"Scheduled {notification_request.type.value}",
                schedule_type=schedule_type,
                status=ScheduleStatus.ACTIVE,
                notification_request=notification_request,
                scheduled_at=scheduled_at,
                cron_expression=cron_expression,
                max_executions=max_executions,
                condition_query=condition_query,
                condition_params=condition_params,
                created_at=datetime.now(),
                created_by=created_by,
                next_execution_at=next_execution
            )
            
            # Store in database
            await self._store_scheduled_notification(scheduled_notification)
            
            logger.info(f"✅ Notification scheduled successfully: {schedule_id}")
            return schedule_id
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule notification: {e}")
            raise
    
    async def update_schedule(
        self,
        schedule_id: str,
        name: str = None,
        scheduled_at: datetime = None,
        cron_expression: str = None,
        max_executions: int = None,
        status: ScheduleStatus = None,
        updated_by: str = None
    ) -> bool:
        """تحديث جدولة موجودة"""
        try:
            logger.info(f"📝 Updating schedule: {schedule_id}")
            
            async with self.db_connection.get_connection() as conn:
                # Get current schedule
                current_schedule = await conn.fetchrow(
                    "SELECT * FROM scheduled_notifications WHERE id = $1",
                    schedule_id
                )
                
                if not current_schedule:
                    raise ValueError(f"Schedule not found: {schedule_id}")
                
                # Prepare update fields
                update_fields = []
                params = []
                param_count = 0
                
                if name is not None:
                    param_count += 1
                    update_fields.append(f"name = ${param_count}")
                    params.append(name)
                
                if scheduled_at is not None:
                    param_count += 1
                    update_fields.append(f"scheduled_at = ${param_count}")
                    params.append(scheduled_at)
                    
                    # Recalculate next execution
                    schedule_type = ScheduleType(current_schedule['schedule_type'])
                    next_execution = self._calculate_next_execution(
                        schedule_type, scheduled_at, current_schedule['cron_expression']
                    )
                    
                    param_count += 1
                    update_fields.append(f"next_execution_at = ${param_count}")
                    params.append(next_execution)
                
                if cron_expression is not None:
                    param_count += 1
                    update_fields.append(f"cron_expression = ${param_count}")
                    params.append(cron_expression)
                    
                    # Recalculate next execution
                    next_execution = self._calculate_next_execution(
                        ScheduleType.RECURRING, current_schedule['scheduled_at'], cron_expression
                    )
                    
                    param_count += 1
                    update_fields.append(f"next_execution_at = ${param_count}")
                    params.append(next_execution)
                
                if max_executions is not None:
                    param_count += 1
                    update_fields.append(f"max_executions = ${param_count}")
                    params.append(max_executions)
                
                if status is not None:
                    param_count += 1
                    update_fields.append(f"status = ${param_count}")
                    params.append(status.value)
                
                if updated_by is not None:
                    param_count += 1
                    update_fields.append(f"updated_by = ${param_count}")
                    params.append(updated_by)
                
                # Always update timestamp
                param_count += 1
                update_fields.append(f"updated_at = ${param_count}")
                params.append(datetime.now())
                
                # Add schedule_id for WHERE clause
                param_count += 1
                params.append(schedule_id)
                
                if update_fields:
                    update_query = f"""
                        UPDATE scheduled_notifications 
                        SET {', '.join(update_fields)}
                        WHERE id = ${param_count}
                    """
                    
                    await conn.execute(update_query, *params)
                
                logger.info(f"✅ Schedule updated successfully: {schedule_id}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to update schedule: {e}")
            return False
    
    async def cancel_schedule(self, schedule_id: str, cancelled_by: str = None) -> bool:
        """إلغاء جدولة"""
        try:
            logger.info(f"❌ Cancelling schedule: {schedule_id}")
            
            async with self.db_connection.get_connection() as conn:
                result = await conn.execute(
                    """
                    UPDATE scheduled_notifications 
                    SET status = $1, updated_at = $2, updated_by = $3
                    WHERE id = $4 AND status IN ('active', 'paused')
                    """,
                    ScheduleStatus.CANCELLED.value,
                    datetime.now(),
                    cancelled_by,
                    schedule_id
                )
                
                if result == "UPDATE 1":
                    logger.info(f"✅ Schedule cancelled successfully: {schedule_id}")
                    return True
                else:
                    logger.warning(f"⚠️ Schedule not found or already completed: {schedule_id}")
                    return False
                
        except Exception as e:
            logger.error(f"❌ Failed to cancel schedule: {e}")
            return False
    
    async def pause_schedule(self, schedule_id: str, paused_by: str = None) -> bool:
        """إيقاف جدولة مؤقتاً"""
        try:
            return await self.update_schedule(
                schedule_id=schedule_id,
                status=ScheduleStatus.PAUSED,
                updated_by=paused_by
            )
        except Exception as e:
            logger.error(f"❌ Failed to pause schedule: {e}")
            return False
    
    async def resume_schedule(self, schedule_id: str, resumed_by: str = None) -> bool:
        """استئناف جدولة"""
        try:
            return await self.update_schedule(
                schedule_id=schedule_id,
                status=ScheduleStatus.ACTIVE,
                updated_by=resumed_by
            )
        except Exception as e:
            logger.error(f"❌ Failed to resume schedule: {e}")
            return False
    
    async def get_scheduled_notifications(
        self,
        status: ScheduleStatus = None,
        schedule_type: ScheduleType = None,
        created_by: str = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """الحصول على الإشعارات المجدولة"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = []
                params = []
                param_count = 0
                
                if status:
                    param_count += 1
                    where_conditions.append(f"status = ${param_count}")
                    params.append(status.value)
                
                if schedule_type:
                    param_count += 1
                    where_conditions.append(f"schedule_type = ${param_count}")
                    params.append(schedule_type.value)
                
                if created_by:
                    param_count += 1
                    where_conditions.append(f"created_by = ${param_count}")
                    params.append(created_by)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Add limit
                param_count += 1
                params.append(limit)
                
                query = f"""
                    SELECT 
                        id, name, description, schedule_type, status,
                        notification_type, channel, priority,
                        scheduled_at, cron_expression, timezone,
                        max_executions, execution_count,
                        created_at, updated_at, created_by,
                        last_executed_at, next_execution_at
                    FROM scheduled_notifications 
                    WHERE {where_clause}
                    ORDER BY created_at DESC
                    LIMIT ${param_count}
                """
                
                rows = await conn.fetch(query, *params)
                
                return [
                    {
                        "id": row["id"],
                        "name": row["name"],
                        "description": row["description"],
                        "schedule_type": row["schedule_type"],
                        "status": row["status"],
                        "notification_type": row["notification_type"],
                        "channel": row["channel"],
                        "priority": row["priority"],
                        "scheduled_at": row["scheduled_at"].isoformat() if row["scheduled_at"] else None,
                        "cron_expression": row["cron_expression"],
                        "timezone": row["timezone"],
                        "max_executions": row["max_executions"],
                        "execution_count": row["execution_count"],
                        "created_at": row["created_at"].isoformat(),
                        "updated_at": row["updated_at"].isoformat(),
                        "created_by": row["created_by"],
                        "last_executed_at": row["last_executed_at"].isoformat() if row["last_executed_at"] else None,
                        "next_execution_at": row["next_execution_at"].isoformat() if row["next_execution_at"] else None
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get scheduled notifications: {e}")
            return []
    
    async def start_scheduler(self):
        """بدء خدمة الجدولة"""
        try:
            if self.is_running:
                logger.warning("⚠️ Scheduler is already running")
                return
            
            logger.info("🚀 Starting notification scheduler")
            self.is_running = True
            
            while self.is_running:
                try:
                    await self._check_and_execute_jobs()
                    self.last_check_time = datetime.now()
                    await asyncio.sleep(self.check_interval)
                    
                except Exception as e:
                    logger.error(f"❌ Error in scheduler loop: {e}")
                    await asyncio.sleep(self.check_interval)
            
            logger.info("🛑 Notification scheduler stopped")
            
        except Exception as e:
            logger.error(f"❌ Failed to start scheduler: {e}")
            self.is_running = False
    
    async def stop_scheduler(self):
        """إيقاف خدمة الجدولة"""
        logger.info("🛑 Stopping notification scheduler")
        self.is_running = False
        
        # Wait for active jobs to complete
        if self.active_jobs:
            logger.info(f"⏳ Waiting for {len(self.active_jobs)} active jobs to complete")
            await asyncio.gather(*self.active_jobs.values(), return_exceptions=True)
    
    async def _check_and_execute_jobs(self):
        """فحص وتنفيذ المهام المجدولة"""
        try:
            # Get jobs ready for execution
            ready_jobs = await self._get_ready_jobs()
            
            if not ready_jobs:
                return
            
            logger.info(f"📋 Found {len(ready_jobs)} jobs ready for execution")
            
            # Execute jobs concurrently (with limit)
            semaphore = asyncio.Semaphore(self.max_concurrent_jobs)
            
            tasks = []
            for job in ready_jobs:
                task = asyncio.create_task(self._execute_job_with_semaphore(semaphore, job))
                tasks.append(task)
                self.active_jobs[job['id']] = task
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Clean up completed tasks
            for job in ready_jobs:
                self.active_jobs.pop(job['id'], None)
            
            # Log results
            successful = sum(1 for r in results if r is True)
            failed = len(results) - successful
            
            logger.info(f"✅ Job execution completed: {successful} successful, {failed} failed")
            
        except Exception as e:
            logger.error(f"❌ Error checking and executing jobs: {e}")
    
    async def _execute_job_with_semaphore(self, semaphore: asyncio.Semaphore, job: Dict[str, Any]) -> bool:
        """تنفيذ مهمة مع تحديد التزامن"""
        async with semaphore:
            return await self._execute_job(job)
    
    async def _execute_job(self, job: Dict[str, Any]) -> bool:
        """تنفيذ مهمة واحدة"""
        try:
            job_id = job['id']
            logger.info(f"🔄 Executing job: {job_id}")
            
            # Call before_execute handlers
            for handler in self.job_handlers['before_execute']:
                await handler(job)
            
            # Check conditions if applicable
            if job['schedule_type'] == ScheduleType.CONDITIONAL.value:
                if not await self._check_condition(job):
                    logger.info(f"⏭️ Job condition not met, skipping: {job_id}")
                    return True
            
            # Reconstruct notification request
            notification_request = self._reconstruct_notification_request(job)
            
            # Send notification
            result = await self.notification_service.send_notification(notification_request)
            
            # Update job execution info
            await self._update_job_execution(job, result.status.value == "sent")
            
            # Call after_execute handlers
            for handler in self.job_handlers['after_execute']:
                await handler(job, result)
            
            self.jobs_executed += 1
            logger.info(f"✅ Job executed successfully: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to execute job {job.get('id', 'unknown')}: {e}")
            
            # Call error handlers
            for handler in self.job_handlers['on_error']:
                await handler(job, e)
            
            # Update job as failed
            await self._update_job_execution(job, False, str(e))
            
            self.jobs_failed += 1
            return False
    
    async def _get_ready_jobs(self) -> List[Dict[str, Any]]:
        """الحصول على المهام الجاهزة للتنفيذ"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM scheduled_notifications 
                    WHERE status = 'active' 
                    AND next_execution_at <= CURRENT_TIMESTAMP
                    AND (max_executions IS NULL OR execution_count < max_executions)
                    ORDER BY next_execution_at
                    LIMIT 100
                """
                
                rows = await conn.fetch(query)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Failed to get ready jobs: {e}")
            return []
    
    async def _check_condition(self, job: Dict[str, Any]) -> bool:
        """فحص شرط التنفيذ"""
        try:
            if not job.get('condition_query'):
                return True
            
            async with self.db_connection.get_connection() as conn:
                # Execute condition query
                condition_params = json.loads(job.get('condition_params', '{}'))
                result = await conn.fetchval(job['condition_query'], *condition_params.values())
                
                # Convert result to boolean
                return bool(result)
                
        except Exception as e:
            logger.error(f"❌ Failed to check condition for job {job['id']}: {e}")
            return False
    
    def _reconstruct_notification_request(self, job: Dict[str, Any]) -> NotificationRequest:
        """إعادة بناء طلب الإشعار من بيانات المهمة"""
        from .notification_service import NotificationRecipient
        
        # Parse notification data
        notification_data = json.loads(job.get('notification_data', '{}'))
        recipient_data = json.loads(job.get('recipient_data', '{}'))
        
        # Create recipient
        recipient = NotificationRecipient(
            user_id=recipient_data.get('user_id'),
            email=recipient_data.get('email'),
            phone=recipient_data.get('phone'),
            language=recipient_data.get('language', 'ar'),
            timezone=recipient_data.get('timezone', 'Asia/Riyadh')
        )
        
        # Create notification request
        return NotificationRequest(
            type=NotificationType(job['notification_type']),
            channel=NotificationChannel(job['channel']),
            recipient=recipient,
            priority=NotificationPriority(job['priority']),
            data=notification_data,
            template_id=job.get('template_id')
        )
    
    async def _update_job_execution(self, job: Dict[str, Any], success: bool, error_message: str = None):
        """تحديث معلومات تنفيذ المهمة"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Calculate next execution time
                next_execution = None
                status = job['status']
                
                if success:
                    execution_count = job['execution_count'] + 1
                    
                    # Check if job should continue
                    if job['schedule_type'] == ScheduleType.RECURRING.value:
                        # Check max executions
                        if job['max_executions'] and execution_count >= job['max_executions']:
                            status = ScheduleStatus.COMPLETED.value
                        else:
                            # Calculate next execution
                            next_execution = self._calculate_next_execution(
                                ScheduleType.RECURRING,
                                None,
                                job['cron_expression']
                            )
                    else:
                        # One-time or conditional job completed
                        status = ScheduleStatus.COMPLETED.value
                else:
                    execution_count = job['execution_count']
                    # Keep status as active for retry (could implement retry logic here)
                
                # Update job
                await conn.execute(
                    """
                    UPDATE scheduled_notifications 
                    SET execution_count = $1,
                        last_executed_at = $2,
                        next_execution_at = $3,
                        status = $4,
                        last_error = $5,
                        updated_at = $6
                    WHERE id = $7
                    """,
                    execution_count,
                    datetime.now(),
                    next_execution,
                    status,
                    error_message,
                    datetime.now(),
                    job['id']
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to update job execution: {e}")
    
    def _validate_schedule_params(
        self,
        schedule_type: ScheduleType,
        scheduled_at: datetime,
        cron_expression: str
    ):
        """التحقق من صحة معاملات الجدولة"""
        if schedule_type == ScheduleType.ONE_TIME:
            if not scheduled_at:
                raise ValueError("scheduled_at is required for one-time schedules")
            if scheduled_at <= datetime.now():
                raise ValueError("scheduled_at must be in the future")
        
        elif schedule_type == ScheduleType.RECURRING:
            if not cron_expression:
                raise ValueError("cron_expression is required for recurring schedules")
            
            # Validate cron expression
            try:
                croniter(cron_expression)
            except Exception as e:
                raise ValueError(f"Invalid cron expression: {e}")
    
    def _calculate_next_execution(
        self,
        schedule_type: ScheduleType,
        scheduled_at: datetime,
        cron_expression: str
    ) -> Optional[datetime]:
        """حساب وقت التنفيذ التالي"""
        if schedule_type == ScheduleType.ONE_TIME:
            return scheduled_at
        
        elif schedule_type == ScheduleType.RECURRING and cron_expression:
            try:
                cron = croniter(cron_expression, datetime.now())
                return cron.get_next(datetime)
            except Exception as e:
                logger.error(f"❌ Failed to calculate next execution: {e}")
                return None
        
        return None
    
    async def _store_scheduled_notification(self, scheduled_notification: ScheduledNotification):
        """حفظ الإشعار المجدول في قاعدة البيانات"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Serialize notification request data
                notification_data = json.dumps(asdict(scheduled_notification.notification_request.data))
                recipient_data = json.dumps(asdict(scheduled_notification.notification_request.recipient))
                
                query = """
                    INSERT INTO scheduled_notifications (
                        id, name, description, schedule_type, status,
                        notification_type, channel, priority,
                        notification_data, recipient_data, template_id,
                        scheduled_at, cron_expression, timezone,
                        max_executions, condition_query, condition_params,
                        created_at, created_by, next_execution_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
                """
                
                await conn.execute(
                    query,
                    scheduled_notification.id,
                    scheduled_notification.name,
                    scheduled_notification.description,
                    scheduled_notification.schedule_type.value,
                    scheduled_notification.status.value,
                    scheduled_notification.notification_request.type.value,
                    scheduled_notification.notification_request.channel.value,
                    scheduled_notification.notification_request.priority.value,
                    notification_data,
                    recipient_data,
                    scheduled_notification.notification_request.template_id,
                    scheduled_notification.scheduled_at,
                    scheduled_notification.cron_expression,
                    scheduled_notification.timezone,
                    scheduled_notification.max_executions,
                    scheduled_notification.condition_query,
                    json.dumps(scheduled_notification.condition_params or {}),
                    scheduled_notification.created_at,
                    scheduled_notification.created_by,
                    scheduled_notification.next_execution_at
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store scheduled notification: {e}")
            raise
    
    def add_event_handler(self, event: str, handler: Callable):
        """إضافة معالج أحداث"""
        if event in self.job_handlers:
            self.job_handlers[event].append(handler)
    
    async def get_scheduler_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الجدولة"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Overall statistics
                stats_query = """
                    SELECT 
                        COUNT(*) as total_schedules,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_schedules,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_schedules,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_schedules,
                        SUM(execution_count) as total_executions
                    FROM scheduled_notifications
                """
                
                stats = await conn.fetchrow(stats_query)
                
                return {
                    "scheduler_status": {
                        "is_running": self.is_running,
                        "last_check_time": self.last_check_time.isoformat() if self.last_check_time else None,
                        "active_jobs": len(self.active_jobs),
                        "check_interval": self.check_interval
                    },
                    "execution_stats": {
                        "jobs_executed": self.jobs_executed,
                        "jobs_failed": self.jobs_failed,
                        "success_rate": (self.jobs_executed / max(self.jobs_executed + self.jobs_failed, 1)) * 100
                    },
                    "schedule_stats": {
                        "total_schedules": stats["total_schedules"],
                        "active_schedules": stats["active_schedules"],
                        "completed_schedules": stats["completed_schedules"],
                        "failed_schedules": stats["failed_schedules"],
                        "total_executions": stats["total_executions"]
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get scheduler statistics: {e}")
            return {}
