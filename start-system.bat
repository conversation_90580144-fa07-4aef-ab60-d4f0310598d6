@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: WS Transfir System Startup Script for Windows
:: نص تشغيل نظام WS Transfir لنظام Windows

echo 🚀 بدء تشغيل نظام WS Transfir
echo ==================================
echo.

:: Check if Docker is installed and running
echo 🔍 فحص Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker غير مثبت. يرجى تثبيت Docker Desktop أولاً.
    pause
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker غير يعمل. يرجى تشغيل Docker Desktop أولاً.
    pause
    exit /b 1
)

echo ✅ Docker متاح ويعمل بشكل صحيح
echo.

:: Check if Docker Compose is available
echo 🔍 فحص Docker Compose...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Docker Compose غير متاح.
        pause
        exit /b 1
    ) else (
        set COMPOSE_CMD=docker compose
    )
) else (
    set COMPOSE_CMD=docker-compose
)

echo ✅ Docker Compose متاح
echo.

:: Create necessary directories
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "backend\logs" mkdir "backend\logs"
if not exist "backend\uploads" mkdir "backend\uploads"
if not exist "backend\database\init" mkdir "backend\database\init"
if not exist "backend\database\migrations" mkdir "backend\database\migrations"
if not exist "backend\database\seeds" mkdir "backend\database\seeds"
if not exist "backend\config" mkdir "backend\config"
if not exist "nginx\logs" mkdir "nginx\logs"
if not exist "nginx\ssl" mkdir "nginx\ssl"
if not exist "monitoring\prometheus" mkdir "monitoring\prometheus"
if not exist "monitoring\grafana\provisioning" mkdir "monitoring\grafana\provisioning"
if not exist "monitoring\grafana\dashboards" mkdir "monitoring\grafana\dashboards"
echo ✅ تم إنشاء المجلدات المطلوبة
echo.

:: Create .env file if it doesn't exist
if not exist ".env" (
    echo ⚙️ إنشاء ملف البيئة...
    (
        echo # WS Transfir Environment Configuration
        echo NODE_ENV=development
        echo LOG_LEVEL=debug
        echo.
        echo # Database Configuration
        echo POSTGRES_USER=ws_user
        echo POSTGRES_PASSWORD=ws_password
        echo POSTGRES_DB=ws_transfir
        echo.
        echo # Redis Configuration
        echo REDIS_PASSWORD=
        echo.
        echo # MongoDB Configuration
        echo MONGO_INITDB_ROOT_USERNAME=ws_user
        echo MONGO_INITDB_ROOT_PASSWORD=ws_password
        echo.
        echo # JWT Configuration
        echo JWT_SECRET=your-super-secret-jwt-key-change-in-production
        echo JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
        echo ENCRYPTION_KEY=your-encryption-key-32-chars-long
        echo.
        echo # External Services
        echo EMAIL_USER=<EMAIL>
        echo EMAIL_PASS=your-app-password
        echo TWILIO_ACCOUNT_SID=your-twilio-sid
        echo TWILIO_AUTH_TOKEN=your-twilio-token
        echo TWILIO_PHONE_NUMBER=+**********
        echo.
        echo # Payment Gateways
        echo STRIPE_SECRET_KEY=sk_test_your_stripe_key
        echo PAYPAL_CLIENT_ID=your_paypal_client_id
        echo.
        echo # Monitoring
        echo GRAFANA_ADMIN_PASSWORD=admin123
    ) > .env
    echo ✅ تم إنشاء ملف .env
    echo.
)

:: Create Redis configuration
if not exist "backend\config\redis.conf" (
    echo 📝 إنشاء ملف إعداد Redis...
    (
        echo # Redis Configuration for WS Transfir
        echo bind 0.0.0.0
        echo port 6379
        echo timeout 0
        echo tcp-keepalive 300
        echo daemonize no
        echo supervised no
        echo pidfile /var/run/redis_6379.pid
        echo loglevel notice
        echo logfile ""
        echo databases 16
        echo save 900 1
        echo save 300 10
        echo save 60 10000
        echo stop-writes-on-bgsave-error yes
        echo rdbcompression yes
        echo rdbchecksum yes
        echo dbfilename dump.rdb
        echo dir ./
        echo maxmemory 256mb
        echo maxmemory-policy allkeys-lru
        echo appendonly yes
        echo appendfilename "appendonly.aof"
        echo appendfsync everysec
        echo no-appendfsync-on-rewrite no
        echo auto-aof-rewrite-percentage 100
        echo auto-aof-rewrite-min-size 64mb
    ) > "backend\config\redis.conf"
    echo ✅ تم إنشاء ملف إعداد Redis
    echo.
)

:: Create Prometheus configuration
if not exist "monitoring\prometheus\prometheus.yml" (
    echo 📝 إنشاء ملف إعداد Prometheus...
    (
        echo global:
        echo   scrape_interval: 15s
        echo   evaluation_interval: 15s
        echo.
        echo rule_files:
        echo   # - "first_rules.yml"
        echo   # - "second_rules.yml"
        echo.
        echo scrape_configs:
        echo   - job_name: 'prometheus'
        echo     static_configs:
        echo       - targets: ['localhost:9090']
        echo.
        echo   - job_name: 'api-gateway'
        echo     static_configs:
        echo       - targets: ['api-gateway:3000']
        echo     metrics_path: '/metrics'
        echo.
        echo   - job_name: 'auth-service'
        echo     static_configs:
        echo       - targets: ['auth-service:3001']
        echo     metrics_path: '/metrics'
        echo.
        echo   - job_name: 'user-service'
        echo     static_configs:
        echo       - targets: ['user-service:3002']
        echo     metrics_path: '/metrics'
        echo.
        echo   - job_name: 'transfer-service'
        echo     static_configs:
        echo       - targets: ['transfer-service:3003']
        echo     metrics_path: '/metrics'
        echo.
        echo   - job_name: 'wallet-service'
        echo     static_configs:
        echo       - targets: ['wallet-service:3004']
        echo     metrics_path: '/metrics'
        echo.
        echo   - job_name: 'notification-service'
        echo     static_configs:
        echo       - targets: ['notification-service:3005']
        echo     metrics_path: '/metrics'
        echo.
        echo   - job_name: 'analytics-service'
        echo     static_configs:
        echo       - targets: ['analytics-service:3006']
        echo     metrics_path: '/metrics'
    ) > "monitoring\prometheus\prometheus.yml"
    echo ✅ تم إنشاء ملف إعداد Prometheus
    echo.
)

:: Create Nginx configuration
if not exist "nginx\nginx.conf" (
    echo 🌐 إنشاء إعداد Nginx...
    (
        echo events {
        echo     worker_connections 1024;
        echo }
        echo.
        echo http {
        echo     upstream api_backend {
        echo         server api-gateway:3000;
        echo     }
        echo.    
        echo     upstream web_frontend {
        echo         server web-app:3000;
        echo     }
        echo.    
        echo     server {
        echo         listen 80;
        echo         server_name localhost;
        echo.        
        echo         # Frontend
        echo         location / {
        echo             proxy_pass http://web_frontend;
        echo             proxy_set_header Host $host;
        echo             proxy_set_header X-Real-IP $remote_addr;
        echo             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        echo             proxy_set_header X-Forwarded-Proto $scheme;
        echo         }
        echo.        
        echo         # API
        echo         location /api/ {
        echo             proxy_pass http://api_backend/;
        echo             proxy_set_header Host $host;
        echo             proxy_set_header X-Real-IP $remote_addr;
        echo             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        echo             proxy_set_header X-Forwarded-Proto $scheme;
        echo         }
        echo.        
        echo         # Health check
        echo         location /health {
        echo             access_log off;
        echo             return 200 "healthy\n";
        echo             add_header Content-Type text/plain;
        echo         }
        echo     }
        echo }
    ) > "nginx\nginx.conf"
    echo ✅ تم إنشاء ملف إعداد Nginx
    echo.
)

:: Pull latest images
echo 📥 تحميل أحدث الصور...
%COMPOSE_CMD% pull
echo.

:: Build and start services
echo 🏗️ بناء وتشغيل الخدمات...
echo بناء الصور المخصصة...
%COMPOSE_CMD% build

echo تشغيل الخدمات...
%COMPOSE_CMD% up -d
echo.

:: Wait a bit for services to start
echo ⏳ انتظار بدء الخدمات...
timeout /t 10 /nobreak >nul
echo.

:: Show service status
echo 📊 حالة الخدمات:
%COMPOSE_CMD% ps
echo.

:: Show access URLs
echo 🌐 روابط الوصول:
echo.
echo التطبيق الرئيسي:
echo   🌐 الموقع الإلكتروني: http://localhost
echo   🌐 التطبيق المباشر: http://localhost:3100
echo.
echo واجهات الإدارة:
echo   📊 Grafana (المراقبة): http://localhost:3007 (admin/admin123)
echo   🔍 Prometheus: http://localhost:9090
echo   📈 Kibana (البحث): http://localhost:5601
echo   🗄️  Adminer (قاعدة البيانات): http://localhost:8080
echo   🔴 Redis Commander: http://localhost:8081
echo   🐰 RabbitMQ Management: http://localhost:15672 (ws_user/ws_password)
echo   🔍 Jaeger (التتبع): http://localhost:16686
echo.
echo APIs الخدمات:
echo   🚪 API Gateway: http://localhost:3000
echo   🔐 Auth Service: http://localhost:3001
echo   👤 User Service: http://localhost:3002
echo   💸 Transfer Service: http://localhost:3003
echo   💰 Wallet Service: http://localhost:3004
echo   🔔 Notification Service: http://localhost:3005
echo   📊 Analytics Service: http://localhost:3006
echo   🤖 AI Engine: http://localhost:8000
echo.

echo ✅ تم تشغيل النظام بنجاح!
echo يمكنك الآن الوصول للتطبيق على: http://localhost
echo.
echo ⚠️ تأكد من تحديث متغيرات البيئة في ملف .env قبل الاستخدام في الإنتاج
echo.

pause
