/**
 * اختبار شامل لنظام WS Transfir
 * Complete System Test for WS Transfir
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 اختبار شامل لنظام WS Transfir');
console.log('=====================================');

// قائمة الخدمات والملفات المطلوبة
const systemStructure = {
  'Environment & Config': [
    '.env',
    'docker-compose.yml',
    'package.json'
  ],
  
  'API Gateway': [
    'backend/api-gateway/package.json',
    'backend/api-gateway/src/main.ts',
    'backend/api-gateway/src/app.module.ts'
  ],
  
  'Auth Service': [
    'backend/auth-service/package.json',
    'backend/auth-service/src/main.ts',
    'backend/auth-service/src/app.module.ts',
    'backend/auth-service/src/modules/auth/auth.module.ts',
    'backend/auth-service/src/modules/auth/services/auth.service.ts',
    'backend/auth-service/src/modules/auth/controllers/auth.controller.ts',
    'backend/auth-service/src/modules/users/entities/user.entity.ts'
  ],
  
  'User Service': [
    'backend/user-service/package.json',
    'backend/user-service/src/main.ts',
    'backend/user-service/src/app.module.ts',
    'backend/user-service/src/modules/users/entities/user.entity.ts',
    'backend/user-service/src/modules/profile/entities/user-profile.entity.ts'
  ],
  
  'Transfer Service': [
    'backend/transfer-service/package.json',
    'backend/transfer-service/src/main.ts',
    'backend/transfer-service/src/modules/transfers/entities/transfer.entity.ts'
  ],
  
  'Wallet Service': [
    'backend/wallet-service/package.json',
    'backend/wallet-service/src/modules/wallets/entities/wallet.entity.ts'
  ],
  
  'AI Engine': [
    'backend/ai-engine/main.py',
    'backend/ai-engine/requirements.txt'
  ],
  
  'Frontend': [
    'frontend/web-app/package.json',
    'frontend/web-app/src/pages/index.tsx'
  ],
  
  'Database': [
    'database/migrations/001_create_users_table.sql',
    'database/seeds/001_seed_users.sql'
  ],
  
  'Tests': [
    'backend/auth-service/test/auth.e2e-spec.ts',
    'test-auth-structure.js',
    'test-auth-syntax.js'
  ]
};

let totalFiles = 0;
let existingFiles = 0;
let missingFiles = [];
let serviceStatus = {};

// فحص كل خدمة
Object.keys(systemStructure).forEach(serviceName => {
  console.log(`\n📦 فحص ${serviceName}:`);
  
  const files = systemStructure[serviceName];
  let serviceExisting = 0;
  let serviceMissing = [];
  
  files.forEach(filePath => {
    totalFiles++;
    if (fs.existsSync(filePath)) {
      console.log(`   ✅ ${filePath}`);
      existingFiles++;
      serviceExisting++;
    } else {
      console.log(`   ❌ ${filePath}`);
      missingFiles.push(filePath);
      serviceMissing.push(filePath);
    }
  });
  
  const completionRate = Math.round((serviceExisting / files.length) * 100);
  serviceStatus[serviceName] = {
    total: files.length,
    existing: serviceExisting,
    missing: serviceMissing.length,
    completionRate
  };
  
  console.log(`   📊 اكتمال ${serviceName}: ${completionRate}% (${serviceExisting}/${files.length})`);
});

// تقرير شامل
console.log('\n📊 التقرير الشامل:');
console.log('==================');
console.log(`✅ ملفات موجودة: ${existingFiles}/${totalFiles}`);
console.log(`❌ ملفات مفقودة: ${missingFiles.length}`);
console.log(`📈 نسبة الاكتمال الإجمالية: ${Math.round((existingFiles / totalFiles) * 100)}%`);

// تفاصيل كل خدمة
console.log('\n🔧 تفاصيل الخدمات:');
Object.keys(serviceStatus).forEach(serviceName => {
  const status = serviceStatus[serviceName];
  const statusIcon = status.completionRate === 100 ? '🟢' : 
                    status.completionRate >= 80 ? '🟡' : '🔴';
  
  console.log(`${statusIcon} ${serviceName}: ${status.completionRate}% (${status.existing}/${status.total})`);
});

// الخدمات المكتملة
const completeServices = Object.keys(serviceStatus).filter(
  service => serviceStatus[service].completionRate === 100
);

const partialServices = Object.keys(serviceStatus).filter(
  service => serviceStatus[service].completionRate >= 80 && serviceStatus[service].completionRate < 100
);

const incompleteServices = Object.keys(serviceStatus).filter(
  service => serviceStatus[service].completionRate < 80
);

console.log('\n🎯 حالة الخدمات:');
if (completeServices.length > 0) {
  console.log(`🟢 خدمات مكتملة (${completeServices.length}): ${completeServices.join(', ')}`);
}

if (partialServices.length > 0) {
  console.log(`🟡 خدمات شبه مكتملة (${partialServices.length}): ${partialServices.join(', ')}`);
}

if (incompleteServices.length > 0) {
  console.log(`🔴 خدمات ناقصة (${incompleteServices.length}): ${incompleteServices.join(', ')}`);
}

// فحص package.json files
console.log('\n📦 فحص ملفات Package.json:');
const packageFiles = [
  'package.json',
  'backend/api-gateway/package.json',
  'backend/auth-service/package.json',
  'backend/user-service/package.json',
  'backend/transfer-service/package.json',
  'backend/wallet-service/package.json',
  'frontend/web-app/package.json'
];

packageFiles.forEach(packagePath => {
  if (fs.existsSync(packagePath)) {
    try {
      const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      console.log(`   ✅ ${packagePath}: ${packageContent.name} v${packageContent.version}`);
    } catch (error) {
      console.log(`   ❌ ${packagePath}: خطأ في القراءة`);
    }
  } else {
    console.log(`   ❌ ${packagePath}: غير موجود`);
  }
});

// فحص Docker files
console.log('\n🐳 فحص ملفات Docker:');
const dockerFiles = [
  'docker-compose.yml',
  'backend/auth-service/Dockerfile',
  'backend/api-gateway/Dockerfile'
];

dockerFiles.forEach(dockerPath => {
  if (fs.existsSync(dockerPath)) {
    console.log(`   ✅ ${dockerPath}`);
  } else {
    console.log(`   ❌ ${dockerPath}`);
  }
});

// فحص قواعد البيانات
console.log('\n🗄️  فحص ملفات قاعدة البيانات:');
const dbFiles = [
  'database/migrations',
  'database/seeds',
  'database/init'
];

dbFiles.forEach(dbPath => {
  if (fs.existsSync(dbPath)) {
    const stats = fs.statSync(dbPath);
    if (stats.isDirectory()) {
      const files = fs.readdirSync(dbPath);
      console.log(`   ✅ ${dbPath}: ${files.length} ملف`);
    } else {
      console.log(`   ✅ ${dbPath}`);
    }
  } else {
    console.log(`   ❌ ${dbPath}: غير موجود`);
  }
});

// التوصيات
console.log('\n💡 التوصيات:');
const overallCompletion = Math.round((existingFiles / totalFiles) * 100);

if (overallCompletion >= 90) {
  console.log('🎉 ممتاز! النظام شبه مكتمل');
  console.log('📝 التوصيات:');
  console.log('   1. اختبار الخدمات الموجودة');
  console.log('   2. تشغيل Docker Compose');
  console.log('   3. اختبار APIs');
  console.log('   4. إكمال الملفات المفقودة القليلة');
} else if (overallCompletion >= 70) {
  console.log('👍 جيد! معظم النظام موجود');
  console.log('📝 التوصيات:');
  console.log('   1. إكمال الخدمات الناقصة');
  console.log('   2. اختبار الخدمات المكتملة');
  console.log('   3. إعداد قواعد البيانات');
} else if (overallCompletion >= 50) {
  console.log('⚠️  متوسط - يحتاج المزيد من العمل');
  console.log('📝 التوصيات:');
  console.log('   1. التركيز على الخدمات الأساسية أولاً');
  console.log('   2. إكمال Auth Service و API Gateway');
  console.log('   3. إعداد قواعد البيانات');
} else {
  console.log('🚧 النظام في بداية التطوير');
  console.log('📝 التوصيات:');
  console.log('   1. البدء بـ Auth Service');
  console.log('   2. إعداد قاعدة البيانات');
  console.log('   3. إنشاء API Gateway');
}

// خطة العمل
console.log('\n📋 خطة العمل المقترحة:');
console.log('========================');

if (incompleteServices.length > 0) {
  console.log('🔴 أولوية عالية - إكمال الخدمات الناقصة:');
  incompleteServices.forEach((service, index) => {
    console.log(`   ${index + 1}. ${service} (${serviceStatus[service].completionRate}%)`);
  });
}

if (partialServices.length > 0) {
  console.log('\n🟡 أولوية متوسطة - إكمال الخدمات الجزئية:');
  partialServices.forEach((service, index) => {
    console.log(`   ${index + 1}. ${service} (${serviceStatus[service].completionRate}%)`);
  });
}

if (completeServices.length > 0) {
  console.log('\n🟢 جاهز للاختبار:');
  completeServices.forEach((service, index) => {
    console.log(`   ${index + 1}. ${service} ✓`);
  });
}

// الملفات المفقودة الحرجة
const criticalMissing = missingFiles.filter(file => 
  file.includes('main.ts') || 
  file.includes('app.module.ts') || 
  file.includes('package.json') ||
  file.includes('.env')
);

if (criticalMissing.length > 0) {
  console.log('\n🚨 ملفات مفقودة حرجة:');
  criticalMissing.forEach((file, index) => {
    console.log(`   ${index + 1}. ${file}`);
  });
}

// أوامر التشغيل المقترحة
console.log('\n🚀 أوامر التشغيل المقترحة:');
console.log('============================');

if (serviceStatus['Auth Service']?.completionRate === 100) {
  console.log('# تشغيل خدمة المصادقة:');
  console.log('cd backend/auth-service');
  console.log('npm install');
  console.log('npm run start:dev');
  console.log('');
}

if (serviceStatus['API Gateway']?.completionRate === 100) {
  console.log('# تشغيل API Gateway:');
  console.log('cd backend/api-gateway');
  console.log('npm install');
  console.log('npm run start:dev');
  console.log('');
}

if (fs.existsSync('docker-compose.yml')) {
  console.log('# تشغيل قواعد البيانات:');
  console.log('docker-compose up -d postgres redis mongodb');
  console.log('');
}

if (serviceStatus['Frontend']?.completionRate >= 80) {
  console.log('# تشغيل التطبيق الأمامي:');
  console.log('cd frontend/web-app');
  console.log('npm install');
  console.log('npm run dev');
  console.log('');
}

console.log('✨ انتهى الفحص الشامل للنظام!');
