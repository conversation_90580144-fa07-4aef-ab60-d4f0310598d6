import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../shared/guards/jwt-auth.guard';
import { RolesGuard } from '../../../shared/guards/roles.guard';
import { Roles } from '../../../shared/decorators/roles.decorator';
import { CurrentUser } from '../../../shared/decorators/current-user.decorator';
import { AnalyticsService, AnalyticsQuery } from '../services/analytics.service';
import { CreateAnalyticsEventDto } from '../dto/create-analytics-event.dto';
import { AnalyticsEvent } from '../entities/analytics-event.entity';
import { Report } from '../entities/report.entity';

@ApiTags('Analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Post('events')
  @ApiOperation({ summary: 'تتبع حدث تحليلي جديد' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'تم تتبع الحدث بنجاح',
    type: AnalyticsEvent,
  })
  async trackEvent(
    @Body() createEventDto: CreateAnalyticsEventDto,
    @CurrentUser() user: any,
  ): Promise<AnalyticsEvent> {
    return this.analyticsService.trackEvent({
      ...createEventDto,
      userId: user.id,
    });
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'الحصول على مقاييس لوحة التحكم' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'مقاييس لوحة التحكم',
  })
  @UseGuards(RolesGuard)
  @Roles('admin', 'analyst')
  async getDashboardMetrics(): Promise<any> {
    return this.analyticsService.getDashboardMetrics();
  }

  @Get('data')
  @ApiOperation({ summary: 'الحصول على بيانات التحليلات' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'بيانات التحليلات',
  })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiQuery({ name: 'eventType', required: false, type: String })
  @ApiQuery({ name: 'groupBy', required: false, enum: ['day', 'week', 'month'] })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @UseGuards(RolesGuard)
  @Roles('admin', 'analyst')
  async getAnalytics(@Query() query: any): Promise<any> {
    const analyticsQuery: AnalyticsQuery = {
      startDate: query.startDate ? new Date(query.startDate) : undefined,
      endDate: query.endDate ? new Date(query.endDate) : undefined,
      eventType: query.eventType,
      groupBy: query.groupBy || 'day',
      limit: query.limit ? parseInt(query.limit) : undefined,
    };

    return this.analyticsService.getAnalytics(analyticsQuery);
  }

  @Get('users/:userId')
  @ApiOperation({ summary: 'الحصول على تحليلات مستخدم محدد' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'تحليلات المستخدم',
  })
  @ApiQuery({ name: 'days', required: false, type: Number })
  @UseGuards(RolesGuard)
  @Roles('admin', 'analyst')
  async getUserAnalytics(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('days') days?: string,
  ): Promise<any> {
    const daysNumber = days ? parseInt(days) : 30;
    return this.analyticsService.getUserAnalytics(userId, daysNumber);
  }

  @Get('my-analytics')
  @ApiOperation({ summary: 'الحصول على تحليلات المستخدم الحالي' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'تحليلات المستخدم الحالي',
  })
  @ApiQuery({ name: 'days', required: false, type: Number })
  async getMyAnalytics(
    @CurrentUser() user: any,
    @Query('days') days?: string,
  ): Promise<any> {
    const daysNumber = days ? parseInt(days) : 30;
    return this.analyticsService.getUserAnalytics(user.id, daysNumber);
  }

  @Post('reports')
  @ApiOperation({ summary: 'إنشاء تقرير جديد' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'تم إنشاء التقرير بنجاح',
    type: Report,
  })
  @UseGuards(RolesGuard)
  @Roles('admin', 'analyst')
  async generateReport(@Body() reportData: any): Promise<Report> {
    const {
      reportType,
      startDate,
      endDate,
      filters,
    } = reportData;

    return this.analyticsService.generateReport(
      reportType,
      new Date(startDate),
      new Date(endDate),
      filters,
    );
  }

  @Get('reports')
  @ApiOperation({ summary: 'الحصول على قائمة التقارير' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'قائمة التقارير',
    type: [Report],
  })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @UseGuards(RolesGuard)
  @Roles('admin', 'analyst')
  async getReports(@Query('limit') limit?: string): Promise<Report[]> {
    const limitNumber = limit ? parseInt(limit) : 20;
    return this.analyticsService.getReports(limitNumber);
  }

  @Get('reports/:reportId')
  @ApiOperation({ summary: 'الحصول على تقرير محدد' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'تفاصيل التقرير',
    type: Report,
  })
  async getReport(
    @Param('reportId', ParseUUIDPipe) reportId: string,
  ): Promise<Report> {
    return this.analyticsService.getReport(reportId);
  }

  @Get('events/types')
  @ApiOperation({ summary: 'الحصول على أنواع الأحداث المتاحة' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'أنواع الأحداث',
  })
  async getEventTypes(): Promise<any> {
    return {
      eventTypes: [
        {
          type: 'user_registered',
          description: 'تسجيل مستخدم جديد',
          category: 'user',
        },
        {
          type: 'user_login',
          description: 'تسجيل دخول المستخدم',
          category: 'user',
        },
        {
          type: 'user_logout',
          description: 'تسجيل خروج المستخدم',
          category: 'user',
        },
        {
          type: 'transfer_initiated',
          description: 'بدء تحويل مالي',
          category: 'transfer',
        },
        {
          type: 'transfer_completed',
          description: 'إكمال تحويل مالي',
          category: 'transfer',
        },
        {
          type: 'transfer_failed',
          description: 'فشل تحويل مالي',
          category: 'transfer',
        },
        {
          type: 'payment_initiated',
          description: 'بدء عملية دفع',
          category: 'payment',
        },
        {
          type: 'payment_completed',
          description: 'إكمال عملية دفع',
          category: 'payment',
        },
        {
          type: 'payment_failed',
          description: 'فشل عملية دفع',
          category: 'payment',
        },
        {
          type: 'wallet_created',
          description: 'إنشاء محفظة جديدة',
          category: 'wallet',
        },
        {
          type: 'wallet_deposit',
          description: 'إيداع في المحفظة',
          category: 'wallet',
        },
        {
          type: 'wallet_withdrawal',
          description: 'سحب من المحفظة',
          category: 'wallet',
        },
        {
          type: 'notification_sent',
          description: 'إرسال إشعار',
          category: 'notification',
        },
        {
          type: 'notification_read',
          description: 'قراءة إشعار',
          category: 'notification',
        },
        {
          type: 'profile_updated',
          description: 'تحديث الملف الشخصي',
          category: 'profile',
        },
        {
          type: 'security_event',
          description: 'حدث أمني',
          category: 'security',
        },
        {
          type: 'api_call',
          description: 'استدعاء API',
          category: 'system',
        },
        {
          type: 'error_occurred',
          description: 'حدوث خطأ',
          category: 'system',
        },
      ],
      categories: [
        'user',
        'transfer',
        'payment',
        'wallet',
        'notification',
        'profile',
        'security',
        'system',
      ],
    };
  }

  @Get('stats/summary')
  @ApiOperation({ summary: 'الحصول على ملخص الإحصائيات' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'ملخص الإحصائيات',
  })
  @UseGuards(RolesGuard)
  @Roles('admin', 'analyst')
  async getStatsSummary(): Promise<any> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const [
      monthlyData,
      weeklyData,
      dailyData,
    ] = await Promise.all([
      this.analyticsService.getAnalytics({
        startDate: thirtyDaysAgo,
        groupBy: 'day',
      }),
      this.analyticsService.getAnalytics({
        startDate: sevenDaysAgo,
        groupBy: 'day',
      }),
      this.analyticsService.getAnalytics({
        startDate: oneDayAgo,
        groupBy: 'day',
      }),
    ]);

    return {
      monthly: {
        totalEvents: monthlyData.totalEvents,
        uniqueUsers: monthlyData.uniqueUsers,
        averageDaily: monthlyData.totalEvents / 30,
      },
      weekly: {
        totalEvents: weeklyData.totalEvents,
        uniqueUsers: weeklyData.uniqueUsers,
        averageDaily: weeklyData.totalEvents / 7,
      },
      daily: {
        totalEvents: dailyData.totalEvents,
        uniqueUsers: dailyData.uniqueUsers,
      },
      trends: {
        eventsGrowth: this.calculateGrowth(weeklyData.totalEvents, monthlyData.totalEvents),
        usersGrowth: this.calculateGrowth(weeklyData.uniqueUsers, monthlyData.uniqueUsers),
      },
    };
  }

  private calculateGrowth(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }
}
