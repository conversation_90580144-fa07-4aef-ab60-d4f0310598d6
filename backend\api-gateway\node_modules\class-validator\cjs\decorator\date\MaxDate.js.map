{"version": 3, "file": "MaxDate.js", "sourceRoot": "", "sources": ["../../../../src/decorator/date/MaxDate.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,QAAQ,GAAG,SAAS,CAAC;AAElC;;GAEG;AACH,SAAgB,OAAO,CAAC,IAAa,EAAE,OAA4B;IACjE,OAAO,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,YAAY,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;AAC7G,CAAC;AAFD,0BAEC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAC,IAAyB,EAAE,iBAAqC;IACtF,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gBAAQ;QACd,WAAW,EAAE,CAAC,IAAI,CAAC;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACxE,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,UAAU,GAAG,2BAA2B,EACpF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,0BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const MAX_DATE = 'maxDate';\n\n/**\n * Checks if the value is a date that's before the specified date.\n */\nexport function maxDate(date: unknown, maxDate: Date | (() => Date)): boolean {\n  return date instanceof Date && date.getTime() <= (maxDate instanceof Date ? maxDate : maxDate()).getTime();\n}\n\n/**\n * Checks if the value is a date that's before the specified date.\n */\nexport function MaxDate(date: Date | (() => Date), validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: MAX_DATE,\n      constraints: [date],\n      validator: {\n        validate: (value, args): boolean => maxDate(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => 'maximal allowed date for ' + eachPrefix + '$property is $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}