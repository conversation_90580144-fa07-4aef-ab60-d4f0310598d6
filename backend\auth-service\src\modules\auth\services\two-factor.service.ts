import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import { TwoFactorAuth } from '../entities/two-factor-auth.entity';
import { User } from '../../users/entities/user.entity';

export interface TwoFactorSetupResult {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

export interface TwoFactorVerificationResult {
  success: boolean;
  backupCodeUsed?: boolean;
  remainingBackupCodes?: number;
}

@Injectable()
export class TwoFactorService {
  private readonly logger = new Logger(TwoFactorService.name);

  constructor(
    @InjectRepository(TwoFactorAuth)
    private readonly twoFactorRepository: Repository<TwoFactorAuth>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
  ) {}

  async setupTwoFactor(userId: string): Promise<TwoFactorSetupResult> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('المستخدم غير موجود');
    }

    // Check if 2FA is already enabled
    const existing = await this.twoFactorRepository.findOne({
      where: { userId, isEnabled: true },
    });

    if (existing) {
      throw new BadRequestException('المصادقة الثنائية مفعلة بالفعل');
    }

    // Generate secret
    const secret = speakeasy.generateSecret({
      name: `WS Transfir (${user.email})`,
      issuer: 'WS Transfir',
      length: 32,
    });

    // Generate backup codes
    const backupCodes = this.generateBackupCodes();

    // Create 2FA record
    const twoFactorAuth = this.twoFactorRepository.create({
      userId,
      secret: secret.base32,
      backupCodes: backupCodes.map(code => this.hashBackupCode(code)),
      isEnabled: false, // Will be enabled after verification
    });

    await this.twoFactorRepository.save(twoFactorAuth);

    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

    this.logger.log(`2FA setup initiated for user: ${userId}`);

    return {
      secret: secret.base32,
      qrCodeUrl,
      backupCodes,
    };
  }

  async enableTwoFactor(userId: string, token: string): Promise<boolean> {
    const twoFactorAuth = await this.twoFactorRepository.findOne({
      where: { userId, isEnabled: false },
    });

    if (!twoFactorAuth) {
      throw new BadRequestException('لم يتم إعداد المصادقة الثنائية');
    }

    // Verify the token
    const isValid = speakeasy.totp.verify({
      secret: twoFactorAuth.secret,
      encoding: 'base32',
      token,
      window: 2, // Allow 2 time steps before/after
    });

    if (!isValid) {
      throw new BadRequestException('رمز التحقق غير صحيح');
    }

    // Enable 2FA
    twoFactorAuth.isEnabled = true;
    twoFactorAuth.enabledAt = new Date();
    await this.twoFactorRepository.save(twoFactorAuth);

    // Update user record
    await this.userRepository.update(userId, { isTwoFactorEnabled: true });

    this.logger.log(`2FA enabled for user: ${userId}`);
    return true;
  }

  async verifyTwoFactor(userId: string, token: string): Promise<TwoFactorVerificationResult> {
    const twoFactorAuth = await this.twoFactorRepository.findOne({
      where: { userId, isEnabled: true },
    });

    if (!twoFactorAuth) {
      throw new UnauthorizedException('المصادقة الثنائية غير مفعلة');
    }

    // First try TOTP verification
    const isValidTotp = speakeasy.totp.verify({
      secret: twoFactorAuth.secret,
      encoding: 'base32',
      token,
      window: 2,
    });

    if (isValidTotp) {
      return { success: true };
    }

    // If TOTP fails, try backup codes
    const backupCodeResult = await this.verifyBackupCode(userId, token);
    if (backupCodeResult.success) {
      return {
        success: true,
        backupCodeUsed: true,
        remainingBackupCodes: backupCodeResult.remainingCodes,
      };
    }

    return { success: false };
  }

  async disableTwoFactor(userId: string, token: string): Promise<boolean> {
    const twoFactorAuth = await this.twoFactorRepository.findOne({
      where: { userId, isEnabled: true },
    });

    if (!twoFactorAuth) {
      throw new BadRequestException('المصادقة الثنائية غير مفعلة');
    }

    // Verify token before disabling
    const verification = await this.verifyTwoFactor(userId, token);
    if (!verification.success) {
      throw new BadRequestException('رمز التحقق غير صحيح');
    }

    // Disable 2FA
    twoFactorAuth.isEnabled = false;
    twoFactorAuth.disabledAt = new Date();
    await this.twoFactorRepository.save(twoFactorAuth);

    // Update user record
    await this.userRepository.update(userId, { isTwoFactorEnabled: false });

    this.logger.log(`2FA disabled for user: ${userId}`);
    return true;
  }

  async regenerateBackupCodes(userId: string, token: string): Promise<string[]> {
    const twoFactorAuth = await this.twoFactorRepository.findOne({
      where: { userId, isEnabled: true },
    });

    if (!twoFactorAuth) {
      throw new BadRequestException('المصادقة الثنائية غير مفعلة');
    }

    // Verify token before regenerating
    const verification = await this.verifyTwoFactor(userId, token);
    if (!verification.success) {
      throw new BadRequestException('رمز التحقق غير صحيح');
    }

    // Generate new backup codes
    const newBackupCodes = this.generateBackupCodes();
    twoFactorAuth.backupCodes = newBackupCodes.map(code => this.hashBackupCode(code));
    await this.twoFactorRepository.save(twoFactorAuth);

    this.logger.log(`Backup codes regenerated for user: ${userId}`);
    return newBackupCodes;
  }

  async isTwoFactorEnabled(userId: string): Promise<boolean> {
    const twoFactorAuth = await this.twoFactorRepository.findOne({
      where: { userId, isEnabled: true },
    });

    return !!twoFactorAuth;
  }

  async getTwoFactorStatus(userId: string): Promise<any> {
    const twoFactorAuth = await this.twoFactorRepository.findOne({
      where: { userId },
      order: { createdAt: 'DESC' },
    });

    if (!twoFactorAuth) {
      return {
        isEnabled: false,
        isSetup: false,
      };
    }

    const remainingBackupCodes = twoFactorAuth.backupCodes?.length || 0;

    return {
      isEnabled: twoFactorAuth.isEnabled,
      isSetup: true,
      enabledAt: twoFactorAuth.enabledAt,
      remainingBackupCodes,
      needsBackupCodeRegeneration: remainingBackupCodes < 3,
    };
  }

  private async verifyBackupCode(userId: string, code: string): Promise<{ success: boolean; remainingCodes: number }> {
    const twoFactorAuth = await this.twoFactorRepository.findOne({
      where: { userId, isEnabled: true },
    });

    if (!twoFactorAuth || !twoFactorAuth.backupCodes) {
      return { success: false, remainingCodes: 0 };
    }

    const hashedCode = this.hashBackupCode(code);
    const codeIndex = twoFactorAuth.backupCodes.indexOf(hashedCode);

    if (codeIndex === -1) {
      return { success: false, remainingCodes: twoFactorAuth.backupCodes.length };
    }

    // Remove used backup code
    twoFactorAuth.backupCodes.splice(codeIndex, 1);
    await this.twoFactorRepository.save(twoFactorAuth);

    this.logger.log(`Backup code used for user: ${userId}, remaining: ${twoFactorAuth.backupCodes.length}`);

    return {
      success: true,
      remainingCodes: twoFactorAuth.backupCodes.length,
    };
  }

  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      // Generate 8-digit backup codes
      const code = Math.random().toString().slice(2, 10);
      codes.push(code);
    }
    return codes;
  }

  private hashBackupCode(code: string): string {
    // Simple hash for backup codes (in production, use proper hashing)
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(code).digest('hex');
  }

  async cleanupExpiredSetups(): Promise<void> {
    // Remove 2FA setups that weren't completed within 24 hours
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    const result = await this.twoFactorRepository.delete({
      isEnabled: false,
      createdAt: { $lt: oneDayAgo } as any,
    });

    if (result.affected && result.affected > 0) {
      this.logger.log(`Cleaned up ${result.affected} expired 2FA setups`);
    }
  }

  async getUsersWithTwoFactor(): Promise<any[]> {
    return this.twoFactorRepository
      .createQueryBuilder('tfa')
      .leftJoinAndSelect('tfa.user', 'user')
      .where('tfa.isEnabled = :isEnabled', { isEnabled: true })
      .select([
        'tfa.userId',
        'tfa.enabledAt',
        'user.email',
        'user.firstName',
        'user.lastName',
      ])
      .getMany();
  }

  async getTwoFactorStats(): Promise<any> {
    const [
      totalUsers,
      usersWithTwoFactor,
      setupsInProgress,
    ] = await Promise.all([
      this.userRepository.count(),
      this.twoFactorRepository.count({ where: { isEnabled: true } }),
      this.twoFactorRepository.count({ where: { isEnabled: false } }),
    ]);

    const adoptionRate = totalUsers > 0 ? (usersWithTwoFactor / totalUsers) * 100 : 0;

    return {
      totalUsers,
      usersWithTwoFactor,
      setupsInProgress,
      adoptionRate: Math.round(adoptionRate * 100) / 100,
    };
  }
}
