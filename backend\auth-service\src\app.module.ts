import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ThrottlerModule } from '@nestjs/throttler';

// Modules
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { TwoFactorModule } from './modules/two-factor/two-factor.module';
import { PasswordModule } from './modules/password/password.module';
import { NotificationModule } from './modules/notification/notification.module';

// Shared modules
import { DatabaseModule } from './shared/database/database.module';
import { RedisModule } from './shared/redis/redis.module';
import { LoggerModule } from './shared/logger/logger.module';
import { HealthModule } from './shared/health/health.module';

// Entities
import { User } from './modules/users/entities/user.entity';
import { UserSession } from './modules/auth/entities/user-session.entity';
import { PasswordReset } from './modules/password/entities/password-reset.entity';
import { TwoFactorAuth } from './modules/two-factor/entities/two-factor-auth.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env', '.env.local'],
      cache: true,
    }),
    
    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('POSTGRES_HOST'),
        port: configService.get('POSTGRES_PORT'),
        username: configService.get('POSTGRES_USER'),
        password: configService.get('POSTGRES_PASSWORD'),
        database: configService.get('POSTGRES_DB'),
        entities: [User, UserSession, PasswordReset, TwoFactorAuth],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development',
        ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
      }),
      inject: [ConfigService],
    }),
    
    // JWT
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
      global: true,
    }),
    
    // Passport
    PassportModule.register({ defaultStrategy: 'jwt' }),
    
    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ttl: parseInt(configService.get('RATE_LIMIT_WINDOW_MS')) / 1000,
        limit: parseInt(configService.get('RATE_LIMIT_MAX_REQUESTS')),
      }),
      inject: [ConfigService],
    }),
    
    // Shared modules
    DatabaseModule,
    RedisModule,
    LoggerModule,
    HealthModule,
    
    // Feature modules
    AuthModule,
    UsersModule,
    TwoFactorModule,
    PasswordModule,
    NotificationModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
