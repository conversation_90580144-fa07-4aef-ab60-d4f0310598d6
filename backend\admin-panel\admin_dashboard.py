"""
Admin Dashboard Service
======================
خدمة لوحة الإدارة الشاملة
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

import asyncpg
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class DashboardPeriod(Enum):
    """فترات لوحة التحكم"""
    TODAY = "today"
    YESTERDAY = "yesterday"
    THIS_WEEK = "this_week"
    LAST_WEEK = "last_week"
    THIS_MONTH = "this_month"
    LAST_MONTH = "last_month"
    THIS_QUARTER = "this_quarter"
    LAST_QUARTER = "last_quarter"
    THIS_YEAR = "this_year"
    LAST_YEAR = "last_year"


@dataclass
class SystemMetrics:
    """مقاييس النظام"""
    total_users: int
    active_users: int
    new_users_today: int
    total_agents: int
    active_agents: int
    pending_agents: int
    total_transactions: int
    successful_transactions: int
    failed_transactions: int
    total_volume: Decimal
    total_fees: Decimal
    total_commissions: Decimal
    system_uptime: float
    response_time_avg: float


@dataclass
class FinancialSummary:
    """الملخص المالي"""
    total_revenue: Decimal
    transaction_fees: Decimal
    commission_expenses: Decimal
    net_profit: Decimal
    daily_revenue: Decimal
    monthly_revenue: Decimal
    revenue_growth: Decimal
    top_revenue_sources: List[Dict[str, Any]]


@dataclass
class UserAnalytics:
    """تحليلات المستخدمين"""
    total_users: int
    active_users: int
    verified_users: int
    kyc_approved_users: int
    user_growth_rate: Decimal
    user_retention_rate: Decimal
    average_session_duration: int
    user_demographics: Dict[str, Any]
    user_activity_trends: List[Dict[str, Any]]


class AdminDashboard:
    """خدمة لوحة الإدارة الشاملة"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Cache settings
        self.cache_duration = 300  # 5 minutes
        self.dashboard_cache = {}
        
        # Performance tracking
        self.query_performance = {}
        self.last_refresh = None
    
    async def get_system_overview(self, period: DashboardPeriod = DashboardPeriod.TODAY) -> Dict[str, Any]:
        """الحصول على نظرة عامة على النظام"""
        try:
            logger.info(f"📊 Getting system overview for period: {period.value}")
            
            # Check cache
            cache_key = f"system_overview_{period.value}"
            if self._is_cached(cache_key):
                return self.dashboard_cache[cache_key]['data']
            
            async with self.db_connection.get_connection() as conn:
                # Get date range for period
                start_date, end_date = self._get_date_range(period)
                
                # System metrics
                system_metrics = await self._get_system_metrics(conn, start_date, end_date)
                
                # Financial summary
                financial_summary = await self._get_financial_summary(conn, start_date, end_date)
                
                # User analytics
                user_analytics = await self._get_user_analytics(conn, start_date, end_date)
                
                # Transaction analytics
                transaction_analytics = await self._get_transaction_analytics(conn, start_date, end_date)
                
                # Agent analytics
                agent_analytics = await self._get_agent_analytics(conn, start_date, end_date)
                
                # Risk and compliance metrics
                risk_metrics = await self._get_risk_metrics(conn, start_date, end_date)
                
                # System health
                system_health = await self._get_system_health(conn)
                
                overview = {
                    'period': period.value,
                    'date_range': {
                        'start': start_date.isoformat(),
                        'end': end_date.isoformat()
                    },
                    'system_metrics': system_metrics,
                    'financial_summary': financial_summary,
                    'user_analytics': user_analytics,
                    'transaction_analytics': transaction_analytics,
                    'agent_analytics': agent_analytics,
                    'risk_metrics': risk_metrics,
                    'system_health': system_health,
                    'last_updated': datetime.now().isoformat()
                }
                
                # Cache the result
                self._cache_data(cache_key, overview)
                
                logger.info("✅ System overview retrieved successfully")
                return overview
                
        except Exception as e:
            logger.error(f"❌ Failed to get system overview: {e}")
            return {'error': str(e)}
    
    async def get_real_time_metrics(self) -> Dict[str, Any]:
        """الحصول على المقاييس في الوقت الفعلي"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Current active sessions
                active_sessions_query = """
                    SELECT COUNT(DISTINCT session_id) as active_sessions
                    FROM user_sessions 
                    WHERE expires_at > CURRENT_TIMESTAMP
                """
                active_sessions = await conn.fetchval(active_sessions_query) or 0
                
                # Transactions in last hour
                hourly_transactions_query = """
                    SELECT 
                        COUNT(*) as count,
                        COALESCE(SUM(amount), 0) as volume,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
                    FROM transactions 
                    WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
                """
                hourly_stats = await conn.fetchrow(hourly_transactions_query)
                
                # System load metrics
                system_load = await self._get_system_load_metrics(conn)
                
                # Pending approvals
                pending_approvals_query = """
                    SELECT 
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_agents,
                        COUNT(CASE WHEN kyc_status = 'in_review' THEN 1 END) as pending_kyc,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions
                    FROM (
                        SELECT status FROM agent_profiles WHERE deleted_at IS NULL
                        UNION ALL
                        SELECT kyc_status as status FROM users WHERE deleted_at IS NULL
                        UNION ALL
                        SELECT status FROM transactions WHERE created_at >= CURRENT_DATE
                    ) combined
                """
                pending_stats = await conn.fetchrow(pending_approvals_query)
                
                # Alert counts
                alerts = await self._get_active_alerts(conn)
                
                return {
                    'active_sessions': active_sessions,
                    'hourly_transactions': {
                        'count': hourly_stats['count'],
                        'volume': float(hourly_stats['volume']),
                        'successful': hourly_stats['successful'],
                        'failed': hourly_stats['failed'],
                        'success_rate': (hourly_stats['successful'] / max(hourly_stats['count'], 1)) * 100
                    },
                    'system_load': system_load,
                    'pending_approvals': {
                        'agents': pending_stats['pending_agents'] or 0,
                        'kyc': pending_stats['pending_kyc'] or 0,
                        'transactions': pending_stats['pending_transactions'] or 0
                    },
                    'alerts': alerts,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get real-time metrics: {e}")
            return {'error': str(e)}
    
    async def get_financial_dashboard(self, period: DashboardPeriod = DashboardPeriod.THIS_MONTH) -> Dict[str, Any]:
        """الحصول على لوحة التحكم المالية"""
        try:
            logger.info(f"💰 Getting financial dashboard for period: {period.value}")
            
            async with self.db_connection.get_connection() as conn:
                start_date, end_date = self._get_date_range(period)
                
                # Revenue breakdown
                revenue_query = """
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as transaction_count,
                        SUM(amount) as total_volume,
                        SUM(fee_amount) as total_fees,
                        AVG(amount) as avg_transaction_amount
                    FROM transactions 
                    WHERE created_at BETWEEN $1 AND $2
                    AND status = 'completed'
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """
                
                revenue_data = await conn.fetch(revenue_query, start_date, end_date)
                
                # Commission expenses
                commission_query = """
                    SELECT 
                        DATE(created_at) as date,
                        SUM(commission_amount) as total_commissions,
                        COUNT(*) as commission_count,
                        AVG(commission_amount) as avg_commission
                    FROM agent_commissions 
                    WHERE created_at BETWEEN $1 AND $2
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """
                
                commission_data = await conn.fetch(commission_query, start_date, end_date)
                
                # Top revenue sources
                top_sources_query = """
                    SELECT 
                        type as transaction_type,
                        COUNT(*) as count,
                        SUM(amount) as total_volume,
                        SUM(fee_amount) as total_fees
                    FROM transactions 
                    WHERE created_at BETWEEN $1 AND $2
                    AND status = 'completed'
                    GROUP BY type
                    ORDER BY total_fees DESC
                    LIMIT 10
                """
                
                top_sources = await conn.fetch(top_sources_query, start_date, end_date)
                
                # Calculate totals and growth
                current_total = sum(float(row['total_fees']) for row in revenue_data)
                
                # Previous period for comparison
                prev_start, prev_end = self._get_previous_period_range(period)
                prev_revenue_query = """
                    SELECT COALESCE(SUM(fee_amount), 0) as prev_total
                    FROM transactions 
                    WHERE created_at BETWEEN $1 AND $2
                    AND status = 'completed'
                """
                prev_total = await conn.fetchval(prev_revenue_query, prev_start, prev_end) or 0
                
                growth_rate = ((current_total - float(prev_total)) / max(float(prev_total), 1)) * 100
                
                return {
                    'period': period.value,
                    'summary': {
                        'total_revenue': current_total,
                        'previous_revenue': float(prev_total),
                        'growth_rate': growth_rate,
                        'total_transactions': sum(row['transaction_count'] for row in revenue_data),
                        'total_commissions': sum(float(row['total_commissions']) for row in commission_data if row['total_commissions'])
                    },
                    'daily_revenue': [
                        {
                            'date': row['date'].isoformat(),
                            'revenue': float(row['total_fees']),
                            'volume': float(row['total_volume']),
                            'transactions': row['transaction_count'],
                            'avg_amount': float(row['avg_transaction_amount'])
                        }
                        for row in revenue_data
                    ],
                    'daily_commissions': [
                        {
                            'date': row['date'].isoformat(),
                            'commissions': float(row['total_commissions']),
                            'count': row['commission_count'],
                            'avg_commission': float(row['avg_commission'])
                        }
                        for row in commission_data
                    ],
                    'top_revenue_sources': [
                        {
                            'type': row['transaction_type'],
                            'count': row['count'],
                            'volume': float(row['total_volume']),
                            'fees': float(row['total_fees']),
                            'avg_fee': float(row['total_fees']) / row['count']
                        }
                        for row in top_sources
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get financial dashboard: {e}")
            return {'error': str(e)}
    
    async def get_user_management_dashboard(self) -> Dict[str, Any]:
        """الحصول على لوحة إدارة المستخدمين"""
        try:
            logger.info("👥 Getting user management dashboard")
            
            async with self.db_connection.get_connection() as conn:
                # User statistics
                user_stats_query = """
                    SELECT 
                        role,
                        COUNT(*) as total,
                        COUNT(CASE WHEN is_active THEN 1 END) as active,
                        COUNT(CASE WHEN is_verified THEN 1 END) as verified,
                        COUNT(CASE WHEN kyc_status = 'approved' THEN 1 END) as kyc_approved,
                        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_last_30d
                    FROM users 
                    WHERE deleted_at IS NULL
                    GROUP BY role
                """
                
                user_stats = await conn.fetch(user_stats_query)
                
                # Recent registrations
                recent_users_query = """
                    SELECT 
                        id, email, first_name, last_name, role, 
                        is_active, is_verified, kyc_status, created_at
                    FROM users 
                    WHERE deleted_at IS NULL
                    ORDER BY created_at DESC 
                    LIMIT 20
                """
                
                recent_users = await conn.fetch(recent_users_query)
                
                # Pending verifications
                pending_verifications_query = """
                    SELECT 
                        COUNT(CASE WHEN is_verified = false THEN 1 END) as email_pending,
                        COUNT(CASE WHEN phone_verified_at IS NULL THEN 1 END) as phone_pending,
                        COUNT(CASE WHEN kyc_status = 'in_review' THEN 1 END) as kyc_pending,
                        COUNT(CASE WHEN kyc_status = 'pending' THEN 1 END) as kyc_not_submitted
                    FROM users 
                    WHERE deleted_at IS NULL AND is_active = true
                """
                
                pending_stats = await conn.fetchrow(pending_verifications_query)
                
                # User activity trends
                activity_query = """
                    SELECT 
                        DATE(last_login) as date,
                        COUNT(DISTINCT id) as active_users
                    FROM users 
                    WHERE last_login >= CURRENT_DATE - INTERVAL '30 days'
                    AND deleted_at IS NULL
                    GROUP BY DATE(last_login)
                    ORDER BY date
                """
                
                activity_trends = await conn.fetch(activity_query)
                
                # Geographic distribution
                geo_query = """
                    SELECT 
                        country,
                        COUNT(*) as user_count,
                        COUNT(CASE WHEN is_active THEN 1 END) as active_count
                    FROM users 
                    WHERE deleted_at IS NULL
                    GROUP BY country
                    ORDER BY user_count DESC
                    LIMIT 10
                """
                
                geo_distribution = await conn.fetch(geo_query)
                
                return {
                    'user_statistics': [
                        {
                            'role': row['role'],
                            'total': row['total'],
                            'active': row['active'],
                            'verified': row['verified'],
                            'kyc_approved': row['kyc_approved'],
                            'new_last_30d': row['new_last_30d'],
                            'active_rate': (row['active'] / row['total']) * 100,
                            'verification_rate': (row['verified'] / row['total']) * 100
                        }
                        for row in user_stats
                    ],
                    'recent_users': [
                        {
                            'id': row['id'],
                            'email': row['email'],
                            'name': f"{row['first_name']} {row['last_name']}",
                            'role': row['role'],
                            'is_active': row['is_active'],
                            'is_verified': row['is_verified'],
                            'kyc_status': row['kyc_status'],
                            'created_at': row['created_at'].isoformat()
                        }
                        for row in recent_users
                    ],
                    'pending_verifications': {
                        'email_pending': pending_stats['email_pending'],
                        'phone_pending': pending_stats['phone_pending'],
                        'kyc_pending': pending_stats['kyc_pending'],
                        'kyc_not_submitted': pending_stats['kyc_not_submitted']
                    },
                    'activity_trends': [
                        {
                            'date': row['date'].isoformat(),
                            'active_users': row['active_users']
                        }
                        for row in activity_trends
                    ],
                    'geographic_distribution': [
                        {
                            'country': row['country'],
                            'total_users': row['user_count'],
                            'active_users': row['active_count'],
                            'activity_rate': (row['active_count'] / row['user_count']) * 100
                        }
                        for row in geo_distribution
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get user management dashboard: {e}")
            return {'error': str(e)}
    
    # Helper methods
    def _get_date_range(self, period: DashboardPeriod) -> Tuple[datetime, datetime]:
        """الحصول على نطاق التاريخ للفترة"""
        now = datetime.now()
        today = now.date()
        
        if period == DashboardPeriod.TODAY:
            start = datetime.combine(today, datetime.min.time())
            end = datetime.combine(today, datetime.max.time())
        elif period == DashboardPeriod.YESTERDAY:
            yesterday = today - timedelta(days=1)
            start = datetime.combine(yesterday, datetime.min.time())
            end = datetime.combine(yesterday, datetime.max.time())
        elif period == DashboardPeriod.THIS_WEEK:
            start_of_week = today - timedelta(days=today.weekday())
            start = datetime.combine(start_of_week, datetime.min.time())
            end = now
        elif period == DashboardPeriod.THIS_MONTH:
            start = datetime.combine(today.replace(day=1), datetime.min.time())
            end = now
        elif period == DashboardPeriod.THIS_YEAR:
            start = datetime.combine(today.replace(month=1, day=1), datetime.min.time())
            end = now
        else:
            # Default to today
            start = datetime.combine(today, datetime.min.time())
            end = datetime.combine(today, datetime.max.time())
        
        return start, end
    
    def _get_previous_period_range(self, period: DashboardPeriod) -> Tuple[datetime, datetime]:
        """الحصول على نطاق الفترة السابقة للمقارنة"""
        current_start, current_end = self._get_date_range(period)
        duration = current_end - current_start
        
        prev_end = current_start - timedelta(seconds=1)
        prev_start = prev_end - duration
        
        return prev_start, prev_end
    
    def _is_cached(self, cache_key: str) -> bool:
        """فحص إذا كانت البيانات محفوظة في الذاكرة المؤقتة"""
        if cache_key not in self.dashboard_cache:
            return False
        
        cached_data = self.dashboard_cache[cache_key]
        age = (datetime.now() - cached_data['timestamp']).seconds
        
        return age < self.cache_duration
    
    def _cache_data(self, cache_key: str, data: Any):
        """حفظ البيانات في الذاكرة المؤقتة"""
        self.dashboard_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now()
        }
    
    async def _get_system_metrics(self, conn, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """الحصول على مقاييس النظام"""
        # Implementation would go here
        return {
            'total_users': 1000,
            'active_users': 850,
            'new_users_today': 25,
            'total_agents': 150,
            'active_agents': 120,
            'pending_agents': 15
        }
    
    async def _get_financial_summary(self, conn, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """الحصول على الملخص المالي"""
        # Implementation would go here
        return {
            'total_revenue': 125000.0,
            'transaction_fees': 100000.0,
            'commission_expenses': 25000.0,
            'net_profit': 75000.0
        }
    
    async def _get_user_analytics(self, conn, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """الحصول على تحليلات المستخدمين"""
        # Implementation would go here
        return {
            'user_growth_rate': 15.5,
            'user_retention_rate': 85.2,
            'average_session_duration': 1800
        }
    
    async def _get_transaction_analytics(self, conn, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """الحصول على تحليلات المعاملات"""
        # Implementation would go here
        return {
            'total_transactions': 5000,
            'successful_transactions': 4850,
            'failed_transactions': 150,
            'success_rate': 97.0
        }
    
    async def _get_agent_analytics(self, conn, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """الحصول على تحليلات الوكلاء"""
        # Implementation would go here
        return {
            'total_agents': 150,
            'active_agents': 120,
            'top_performing_agents': []
        }
    
    async def _get_risk_metrics(self, conn, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """الحصول على مقاييس المخاطر"""
        # Implementation would go here
        return {
            'high_risk_transactions': 25,
            'fraud_alerts': 5,
            'compliance_issues': 2
        }
    
    async def _get_system_health(self, conn) -> Dict[str, Any]:
        """الحصول على صحة النظام"""
        # Implementation would go here
        return {
            'uptime': 99.9,
            'response_time': 150,
            'error_rate': 0.1
        }
    
    async def _get_system_load_metrics(self, conn) -> Dict[str, Any]:
        """الحصول على مقاييس حمولة النظام"""
        # Implementation would go here
        return {
            'cpu_usage': 45.2,
            'memory_usage': 68.5,
            'disk_usage': 35.8,
            'network_io': 1250
        }
    
    async def _get_active_alerts(self, conn) -> Dict[str, Any]:
        """الحصول على التنبيهات النشطة"""
        # Implementation would go here
        return {
            'critical': 0,
            'warning': 3,
            'info': 8,
            'total': 11
        }
