import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../users/entities/user.entity';

@Entity('two_factor_auth')
@Index(['userId'], { unique: true })
@Index(['isEnabled'])
export class TwoFactorAuth {
  @ApiProperty({ description: 'معرف المصادقة الثنائية' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'معرف المستخدم' })
  @Column('uuid')
  @Index()
  userId: string;

  @ApiProperty({ description: 'المفتاح السري للمصادقة الثنائية' })
  @Column('text')
  secret: string;

  @ApiProperty({ description: 'هل المصادقة الثنائية مفعلة' })
  @Column('boolean', { default: false })
  isEnabled: boolean;

  @ApiProperty({ description: 'رموز النسخ الاحتياطي' })
  @Column('jsonb', { nullable: true })
  backupCodes?: string[];

  @ApiProperty({ description: 'تاريخ التفعيل', required: false })
  @Column('timestamp', { nullable: true })
  enabledAt?: Date;

  @ApiProperty({ description: 'تاريخ الإلغاء', required: false })
  @Column('timestamp', { nullable: true })
  disabledAt?: Date;

  @ApiProperty({ description: 'آخر استخدام للمصادقة الثنائية', required: false })
  @Column('timestamp', { nullable: true })
  lastUsedAt?: Date;

  @ApiProperty({ description: 'عدد مرات الاستخدام' })
  @Column('int', { default: 0 })
  usageCount: number;

  @ApiProperty({ description: 'عنوان IP عند التفعيل', required: false })
  @Column({ length: 45, nullable: true })
  enabledFromIp?: string;

  @ApiProperty({ description: 'معلومات المتصفح عند التفعيل', required: false })
  @Column('text', { nullable: true })
  enabledFromUserAgent?: string;

  @ApiProperty({ description: 'بيانات وصفية إضافية', required: false })
  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'تاريخ آخر تحديث' })
  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToOne(() => User, user => user.twoFactorAuth)
  @JoinColumn({ name: 'userId' })
  user: User;

  // Helper methods
  enable(ipAddress?: string, userAgent?: string): void {
    this.isEnabled = true;
    this.enabledAt = new Date();
    this.enabledFromIp = ipAddress;
    this.enabledFromUserAgent = userAgent;
  }

  disable(): void {
    this.isEnabled = false;
    this.disabledAt = new Date();
  }

  recordUsage(): void {
    this.usageCount++;
    this.lastUsedAt = new Date();
  }

  hasBackupCodes(): boolean {
    return !!(this.backupCodes && this.backupCodes.length > 0);
  }

  getRemainingBackupCodes(): number {
    return this.backupCodes ? this.backupCodes.length : 0;
  }

  needsBackupCodeRegeneration(): boolean {
    return this.getRemainingBackupCodes() < 3;
  }

  isRecentlyEnabled(): boolean {
    if (!this.enabledAt) return false;
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    return this.enabledAt > oneDayAgo;
  }

  getUsageStats(): any {
    return {
      isEnabled: this.isEnabled,
      enabledAt: this.enabledAt,
      lastUsedAt: this.lastUsedAt,
      usageCount: this.usageCount,
      remainingBackupCodes: this.getRemainingBackupCodes(),
      needsBackupCodeRegeneration: this.needsBackupCodeRegeneration(),
      isRecentlyEnabled: this.isRecentlyEnabled(),
    };
  }
}

@Entity('two_factor_auth_logs')
@Index(['userId', 'createdAt'])
@Index(['success'])
export class TwoFactorAuthLog {
  @ApiProperty({ description: 'معرف السجل' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'معرف المستخدم' })
  @Column('uuid')
  @Index()
  userId: string;

  @ApiProperty({ description: 'نوع المحاولة' })
  @Column({
    type: 'enum',
    enum: ['totp', 'backup_code'],
  })
  attemptType: 'totp' | 'backup_code';

  @ApiProperty({ description: 'هل نجحت المحاولة' })
  @Column('boolean')
  success: boolean;

  @ApiProperty({ description: 'عنوان IP' })
  @Column({ length: 45, nullable: true })
  ipAddress?: string;

  @ApiProperty({ description: 'معلومات المتصفح' })
  @Column('text', { nullable: true })
  userAgent?: string;

  @ApiProperty({ description: 'رسالة الخطأ في حالة الفشل', required: false })
  @Column('text', { nullable: true })
  errorMessage?: string;

  @ApiProperty({ description: 'بيانات إضافية', required: false })
  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  // Relations
  @OneToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;
}
