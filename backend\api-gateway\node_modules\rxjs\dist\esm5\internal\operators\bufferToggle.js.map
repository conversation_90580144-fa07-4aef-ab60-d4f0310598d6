{"version": 3, "file": "bufferToggle.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/bufferToggle.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AA6C9C,MAAM,UAAU,YAAY,CAC1B,QAA4B,EAC5B,eAAmD;IAEnD,OAAO,OAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAM,OAAO,GAAU,EAAE,CAAC;QAG1B,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAC3B,wBAAwB,CACtB,UAAU,EACV,UAAC,SAAS;YACR,IAAM,MAAM,GAAQ,EAAE,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAGrB,IAAM,mBAAmB,GAAG,IAAI,YAAY,EAAE,CAAC;YAE/C,IAAM,UAAU,GAAG;gBACjB,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC3B,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxB,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACpC,CAAC,CAAC;YAGF,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACnI,CAAC,EACD,IAAI,CACL,CACF,CAAC;QAEF,MAAM,CAAC,SAAS,CACd,wBAAwB,CACtB,UAAU,EACV,UAAC,KAAK;;;gBAEJ,KAAqB,IAAA,YAAA,SAAA,OAAO,CAAA,gCAAA,qDAAE;oBAAzB,IAAM,MAAM,oBAAA;oBACf,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACpB;;;;;;;;;QACH,CAAC,EACD;YAEE,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAG,CAAC,CAAC;aACnC;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}