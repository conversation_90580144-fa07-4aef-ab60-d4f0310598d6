{"version": 3, "file": "TextRange.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/TextRange.test.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,0CAAyC;AACzC,6CAA4C;AAE5C,SAAS,aAAa,CAAC,SAAoB;IACzC,KAAK,IAAI,CAAC,GAAW,CAAC,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QACrD,6BAA6B;QAC7B,IAAM,CAAC,GAAW,yBAAW,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAErF,yCAAyC;QACzC,IAAM,OAAO,GAAW,yBAAW,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAE5F,MAAM,CAAC;YACL,CAAC,EAAE,CAAC;YACJ,OAAO,EAAE,OAAO;YAChB,CAAC,EAAE,CAAC;YACJ,QAAQ,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;SACnC,CAAC,CAAC,eAAe,EAAE,CAAC;IACvB,CAAC;AACH,CAAC;AAED,IAAI,CAAC,wBAAwB,EAAE;IAC7B,IAAM,MAAM,GAAW,YAAY,CAAC;IACpC,IAAM,SAAS,GAAc,qBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC1D,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAE7C,IAAM,QAAQ,GAAc,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxD,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,qBAAqB,EAAE;IAC1B,IAAM,SAAS,GAAc,qBAAS,CAAC,UAAU,CAC/C;QACE,IAAI;QACJ,IAAI;QACJ,EAAE,EAAE,oBAAoB;QACxB,IAAI;QACJ,iCAAiC;KAClC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACF,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,4BAA4B,EAAE;IACjC,IAAM,SAAS,GAAc,qBAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IACtD,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yBAAyB,EAAE;IAC9B,IAAM,SAAS,GAAc,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACxD,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yBAAyB,EAAE;IAC9B,IAAM,SAAS,GAAc,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACxD,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,8BAA8B,EAAE;IACnC,4CAA4C;IAC5C,IAAM,SAAS,GAAc,qBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC1D,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { TextRange } from '../TextRange';\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\nfunction matchSnapshot(textRange: TextRange): void {\r\n  for (let i: number = -1; i <= textRange.end + 1; ++i) {\r\n    // Show the current character\r\n    const c: string = TestHelpers.getEscaped(textRange.buffer.substr(Math.max(i, 0), 1));\r\n\r\n    // Show the next 10 characters of context\r\n    const context: string = TestHelpers.getEscaped(textRange.buffer.substr(Math.max(i, 0), 10));\r\n\r\n    expect({\r\n      c: c,\r\n      context: context,\r\n      i: i,\r\n      location: textRange.getLocation(i)\r\n    }).toMatchSnapshot();\r\n  }\r\n}\r\n\r\ntest('construction scenarios', () => {\r\n  const buffer: string = '**********';\r\n  const textRange: TextRange = TextRange.fromString(buffer);\r\n  expect(textRange.toString()).toEqual(buffer);\r\n\r\n  const subRange: TextRange = textRange.getNewRange(3, 6);\r\n  expect(subRange).toMatchSnapshot('subRange');\r\n});\r\n\r\ntest('getLocation() basic', () => {\r\n  const textRange: TextRange = TextRange.fromString(\r\n    [\r\n      'L1',\r\n      'L2',\r\n      '', // (line 3 is blank)\r\n      'L4',\r\n      'L5+CR\\rL5+CRLF\\r\\nL6+LFCR\\n\\rL7'\r\n    ].join('\\n')\r\n  );\r\n  matchSnapshot(textRange);\r\n});\r\n\r\ntest('getLocation() empty string', () => {\r\n  const textRange: TextRange = TextRange.fromString('');\r\n  matchSnapshot(textRange);\r\n});\r\n\r\ntest('getLocation() CR string', () => {\r\n  const textRange: TextRange = TextRange.fromString('\\r');\r\n  matchSnapshot(textRange);\r\n});\r\n\r\ntest('getLocation() LF string', () => {\r\n  const textRange: TextRange = TextRange.fromString('\\n');\r\n  matchSnapshot(textRange);\r\n});\r\n\r\ntest('getLocation() tab characters', () => {\r\n  // Tab character advances by only one column\r\n  const textRange: TextRange = TextRange.fromString('1\\t3');\r\n  matchSnapshot(textRange);\r\n});\r\n"]}