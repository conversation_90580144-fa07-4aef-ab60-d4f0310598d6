name: "\U0001F41B Bug Report"
description: "If something isn't working as expected \U0001F914"
labels: ["needs triage", "bug"]
body:
  - type: markdown
    attributes:
      value: |
        ## :warning: We use GitHub Issues to track bug reports, feature requests and regressions
 
        If you are not sure that your issue is a bug, you could:

        - use our [Discord community](https://discord.gg/NestJS)
        - use [StackOverflow using the tag `nestjs`](https://stackoverflow.com/questions/tagged/nestjs)
        - If it's just a quick question you can ping [our Twitter](https://twitter.com/nestframework)

        **NOTE:** You don't need to answer questions that you know that aren't relevant.

        ---

  - type: checkboxes
    attributes:
      label: "Is there an existing issue for this?"
      description: "Please search [here](./?q=is%3Aissue) to see if an issue already exists for the bug you encountered"
      options:
      - label: "I have searched the existing issues"
        required: true

  - type: textarea
    validations:
      required: true
    attributes:
      label: "Current behavior"
      description: "How the issue manifests?"

  - type: input
    validations:
      required: true
    attributes:
      label: "Minimum reproduction code"
      description: "An URL to some git repository or gist that reproduces this issue. [Wtf is a minimum reproduction?](https://jmcdo29.github.io/wtf-is-a-minimum-reproduction)"
      placeholder: "https://github.com/..."

  - type: textarea
    attributes:
      label: "Steps to reproduce"
      description: |
        How the issue manifests?
        You could leave this blank if you alread write this in your reproduction code/repo
      placeholder: |
        1. `npm i`
        2. `npm start:dev`
        3. See error...

  - type: textarea
    validations:
      required: true
    attributes:
      label: "Expected behavior"
      description: "A clear and concise description of what you expected to happend (or code)"

  - type: markdown
    attributes:
      value: |
        ---

  - type: input
    validations:
      required: true
    attributes:
      label: "Package version"
      description: |
        Which version of `@nestjs/cli` are you using?
        **Tip**: Make sure that all of yours `@nestjs/*` dependencies are in sync!
      placeholder: "8.1.3"

  - type: input
    attributes:
      label: "NestJS version"
      description: "Which version of `@nestjs/core` are you using?"
      placeholder: "8.1.3"

  - type: input
    attributes:
      label: "Node.js version"
      description: "Which version of Node.js are you using?"
      placeholder: "14.17.6"

  - type: checkboxes
    attributes:
      label: "In which operating systems have you tested?"
      options:
        - label: macOS
        - label: Windows
        - label: Linux

  - type: markdown
    attributes:
      value: |
        ---

  - type: textarea
    attributes:
      label: "Other"
      description: |
        Anything else relevant? eg: Logs, OS version, IDE, package manager, etc.
        **Tip:** You can attach images, recordings or log files by clicking this area to highlight it and then dragging files in
