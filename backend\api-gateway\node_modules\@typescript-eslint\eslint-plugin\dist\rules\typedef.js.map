{"version": 3, "file": "typedef.js", "sourceRoot": "", "sources": ["../../src/rules/typedef.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAqC;AAiBrC,kBAAe,IAAA,iBAAU,EAAwB;IAC/C,IAAI,EAAE,SAAS;IACf,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,4CAA4C;SAC1D;QACD,QAAQ,EAAE;YACR,eAAe,EAAE,6BAA6B;YAC9C,oBAAoB,EAAE,8CAA8C;SACrE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,0DAA+B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACpD,kDAA2B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAChD,wEAAsC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC3D,4DAAgC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACrD,wCAAsB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC3C,4DAAgC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACrD,4DAAgC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACrD,wFAA8C,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;iBACpE;aACF;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE;QACd;YACE,0DAA+B,EAAE,KAAK;YACtC,kDAA2B,EAAE,KAAK;YAClC,wEAAsC,EAAE,KAAK;YAC7C,4DAAgC,EAAE,KAAK;YACvC,wCAAsB,EAAE,KAAK;YAC7B,4DAAgC,EAAE,KAAK;YACvC,4DAAgC,EAAE,KAAK;YACvC,wFAA8C,EAAE,KAAK;SACtD;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,kBAAkB,EAClB,cAAc,EACd,yBAAyB,EACzB,mBAAmB,EACnB,SAAS,EACT,mBAAmB,EACnB,mBAAmB,EACnB,iCAAiC,GAClC,EACF;QAED,SAAS,MAAM,CAAC,QAAuB,EAAE,IAAa;YACpD,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,iBAAiB;gBAC5D,IAAI,EAAE,EAAE,IAAI,EAAE;aACf,CAAC,CAAC;QACL,CAAC;QAED,SAAS,WAAW,CAClB,IAAgD;YAEhD,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QACzE,CAAC;QAED,SAAS,uBAAuB,CAC9B,IAAoD;YAEpD,IAAI,OAAO,GAA8B,IAAI,CAAC,MAAM,CAAC;YACrD,OAAO,OAAO,EAAE,CAAC;gBACf,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;oBACrB,KAAK,sBAAc,CAAC,kBAAkB,CAAC;oBACvC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;oBACxC,KAAK,sBAAc,CAAC,aAAa,CAAC;oBAClC,KAAK,sBAAc,CAAC,YAAY,CAAC;oBACjC,KAAK,sBAAc,CAAC,QAAQ;wBAC1B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;wBACzB,MAAM;oBAER,KAAK,sBAAc,CAAC,cAAc;wBAChC,OAAO,IAAI,CAAC;oBAEd;wBACE,OAAO,GAAG,SAAS,CAAC;gBACxB,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CAAC,MAA4B;YACnD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,IAAI,cAAyC,CAAC;gBAE9C,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,sBAAc,CAAC,iBAAiB;wBACnC,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC;wBAC5B,MAAM;oBACR,KAAK,sBAAc,CAAC,mBAAmB;wBACrC,cAAc,GAAG,KAAK,CAAC,SAAS,CAAC;wBAEjC,4GAA4G;wBAC5G,IAAI,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;4BAC7D,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC;wBACvC,CAAC;wBAED,MAAM;oBACR;wBACE,cAAc,GAAG,KAAK,CAAC;wBACvB,MAAM;gBACV,CAAC;gBAED,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;oBACnC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,mCAAmC,CAAC,IAAmB;YAC9D,OAAO,CACL,iCAAiC,KAAK,IAAI;gBAC1C,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;oBACnD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CACnD,CAAC;QACJ,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAoD;YAEpD,IAAI,QAAQ,GAA8B,IAAI,CAAC,MAAM,CAAC;YAEtD,OAAO,QAAQ,EAAE,CAAC;gBAChB,IACE,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;oBAC7C,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,CAAC;oBAChD,QAAQ,CAAC,cAAc,EACvB,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,GAAG,CAAC,kBAAkB,IAAI;gBACxB,YAAY,CAAC,IAAI;oBACf,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;wBAC/C,IAAI,CAAC,MAAM,CAAC,cAAc,EAC1B,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,IACE,CAAC,IAAI,CAAC,cAAc;wBACpB,CAAC,uBAAuB,CAAC,IAAI,CAAC;wBAC9B,CAAC,2BAA2B,CAAC,IAAI,CAAC;wBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB,EACxD,CAAC;wBACD,MAAM,CAAC,IAAI,CAAC,CAAC;oBACf,CAAC;gBACH,CAAC;aACF,CAAC;YACF,GAAG,CAAC,cAAc,IAAI;gBACpB,uBAAuB,CAAC,IAAI;oBAC1B,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,CAAC;aACF,CAAC;YACF,GAAG,CAAC,yBAAyB,IAAI;gBAC/B,kBAAkB,CAAC,IAAI;oBACrB,IACE,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,mCAAmC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAChE,CAAC,IAAI,CAAC,cAAc,EACpB,CAAC;wBACD,MAAM,CACJ,IAAI,EACJ,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BACzC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI;4BACf,CAAC,CAAC,SAAS,CACd,CAAC;oBACJ,CAAC;gBACH,CAAC;aACF,CAAC;YACF,GAAG,CAAC,SAAS,IAAI;gBACf,yCAAyC,CACvC,IAAgE;oBAEhE,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,CAAC;aACF,CAAC;YACF,GAAG,CAAC,mBAAmB,IAAI;gBACzB,aAAa,CAAC,IAAI;oBAChB,IACE,CAAC,IAAI,CAAC,cAAc;wBACpB,CAAC,uBAAuB,CAAC,IAAI,CAAC;wBAC9B,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAClC,CAAC;wBACD,MAAM,CAAC,IAAI,CAAC,CAAC;oBACf,CAAC;gBACH,CAAC;aACF,CAAC;YACF,GAAG,CAAC,mBAAmB,IAAI;gBACzB,uCAAuC,CACrC,IAA8D;oBAE9D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;wBACzB,MAAM,CACJ,IAAI,EACJ,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;4BAC9C,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC;4BACvB,CAAC,CAAC,SAAS,CACd,CAAC;oBACJ,CAAC;gBACH,CAAC;aACF,CAAC;YACF,kBAAkB,CAAC,IAAI;gBACrB,IACE,CAAC,mBAAmB;oBACpB,IAAI,CAAC,EAAE,CAAC,cAAc;oBACtB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY;wBAC3C,CAAC,kBAAkB,CAAC;oBACtB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;wBAC5C,CAAC,mBAAmB,CAAC;oBACvB,CAAC,IAAI,CAAC,IAAI,IAAI,mCAAmC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAC7D,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IAAI,OAAO,GAA8B,IAAI,CAAC,MAAM,CAAC;gBACrD,OAAO,OAAO,EAAE,CAAC;oBACf,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;wBACrB,KAAK,sBAAc,CAAC,mBAAmB;4BACrC,uBAAuB;4BACvB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;4BACzB,MAAM;wBACR,KAAK,sBAAc,CAAC,cAAc,CAAC;wBACnC,KAAK,sBAAc,CAAC,cAAc;4BAChC,4CAA4C;4BAC5C,OAAO;wBACT;4BACE,kBAAkB;4BAClB,OAAO,GAAG,SAAS,CAAC;4BACpB,MAAM;oBACV,CAAC;gBACH,CAAC;gBAED,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACrC,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}