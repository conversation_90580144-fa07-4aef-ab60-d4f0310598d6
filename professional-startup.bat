@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title WS Transfir - Professional Startup

:: WS Transfir Professional Startup Script
:: تشغيل احترافي لنظام WS Transfir

color 0A
cls

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🚀 WS TRANSFIR - PROFESSIONAL STARTUP SYSTEM 🚀        ██
echo ██                                                            ██
echo ██    نظام التحويلات المالية المتقدم                         ██
echo ██    Advanced Money Transfer System                          ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 📅 التاريخ: %DATE%
echo ⏰ الوقت: %TIME%
echo 💻 النظام: Windows
echo 🏢 المؤسسة: WS Transfir
echo 📍 البيئة: Development/Production Ready
echo.

:: Set startup timestamp
set START_TIME=%TIME%
echo 🕐 بدء التشغيل: %START_TIME%
echo.

:: Phase 1: System Validation
echo ═══════════════════════════════════════════════════════════════
echo 📋 المرحلة 1: فحص النظام والمتطلبات
echo ═══════════════════════════════════════════════════════════════
echo.

:: Check Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL ERROR: Node.js غير مثبت
    echo.
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    echo 🔧 الإصدار المطلوب: 18.0.0 أو أحدث
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js متاح - الإصدار: %NODE_VERSION%

:: Check npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL ERROR: npm غير متاح
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm متاح - الإصدار: %NPM_VERSION%

:: Check project structure
echo 🔍 فحص هيكل المشروع...
if not exist "frontend\web-app" (
    echo ❌ CRITICAL ERROR: مجلد frontend/web-app غير موجود
    pause
    exit /b 1
)
echo ✅ هيكل المشروع صحيح

:: Check available memory
echo 🔍 فحص موارد النظام...
for /f "skip=1" %%p in ('wmic os get TotalVisibleMemorySize /value') do (
    for /f "tokens=2 delims==" %%i in ("%%p") do set TOTAL_MEMORY=%%i
)
if defined TOTAL_MEMORY (
    set /a MEMORY_GB=!TOTAL_MEMORY!/1024/1024
    echo ✅ الذاكرة المتاحة: !MEMORY_GB! GB
) else (
    echo ⚠️ لا يمكن تحديد حجم الذاكرة
)

echo.
echo ✅ جميع المتطلبات متوفرة - النظام جاهز للتشغيل
echo.

:: Phase 2: Environment Setup
echo ═══════════════════════════════════════════════════════════════
echo ⚙️ المرحلة 2: إعداد البيئة والتكوين
echo ═══════════════════════════════════════════════════════════════
echo.

:: Create professional environment file
echo 🔧 إنشاء ملف البيئة الاحترافي...
if not exist ".env.production" (
    (
        echo # WS Transfir Professional Environment Configuration
        echo # Generated on %DATE% at %TIME%
        echo.
        echo # Application Settings
        echo NODE_ENV=production
        echo APP_NAME=WS Transfir
        echo APP_VERSION=1.0.0
        echo APP_DESCRIPTION=Advanced Money Transfer System
        echo.
        echo # Server Configuration
        echo PORT=3100
        echo HOST=0.0.0.0
        echo.
        echo # API Configuration
        echo NEXT_PUBLIC_API_URL=http://localhost:3000
        echo NEXT_PUBLIC_APP_NAME=WS Transfir
        echo NEXT_PUBLIC_APP_VERSION=1.0.0
        echo.
        echo # Security Configuration
        echo JWT_SECRET=ws-transfir-professional-jwt-secret-2024
        echo ENCRYPTION_KEY=ws-transfir-encryption-key-32-chars
        echo SESSION_SECRET=ws-transfir-session-secret-key
        echo.
        echo # Database Configuration
        echo DATABASE_URL=postgresql://ws_user:ws_password@localhost:5432/ws_transfir
        echo REDIS_URL=redis://localhost:6379
        echo MONGODB_URL=mongodb://localhost:27017/ws_transfir
        echo.
        echo # External Services
        echo EMAIL_SERVICE_ENABLED=true
        echo SMS_SERVICE_ENABLED=true
        echo PUSH_NOTIFICATIONS_ENABLED=true
        echo.
        echo # Payment Gateways
        echo STRIPE_ENABLED=true
        echo PAYPAL_ENABLED=true
        echo SADAD_ENABLED=true
        echo MADA_ENABLED=true
        echo.
        echo # Monitoring and Logging
        echo LOG_LEVEL=info
        echo ENABLE_METRICS=true
        echo ENABLE_TRACING=true
        echo ENABLE_HEALTH_CHECKS=true
        echo.
        echo # Performance Settings
        echo ENABLE_COMPRESSION=true
        echo ENABLE_CACHING=true
        echo CACHE_TTL=3600
        echo.
        echo # Security Settings
        echo ENABLE_RATE_LIMITING=true
        echo ENABLE_CORS=true
        echo ENABLE_HELMET=true
        echo ENABLE_2FA=true
        echo.
        echo # Feature Flags
        echo ENABLE_ANALYTICS=true
        echo ENABLE_NOTIFICATIONS=true
        echo ENABLE_KYC=true
        echo ENABLE_COMPLIANCE=true
    ) > .env.production
    echo ✅ تم إنشاء ملف البيئة الاحترافي
) else (
    echo ✅ ملف البيئة الاحترافي موجود
)

:: Create logs directory
if not exist "logs" (
    mkdir logs
    echo ✅ تم إنشاء مجلد السجلات
)

:: Create uploads directory
if not exist "uploads" (
    mkdir uploads
    echo ✅ تم إنشاء مجلد الرفع
)

echo.

:: Phase 3: Dependencies Installation
echo ═══════════════════════════════════════════════════════════════
echo 📦 المرحلة 3: تثبيت وتحديث Dependencies
echo ═══════════════════════════════════════════════════════════════
echo.

:: Install root dependencies
echo 🔧 تثبيت dependencies الجذر...
if not exist "node_modules" (
    npm install --silent --no-audit
    if errorlevel 1 (
        echo ❌ فشل في تثبيت dependencies الجذر
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت dependencies الجذر
) else (
    echo ✅ dependencies الجذر موجودة
)

:: Install frontend dependencies
echo 🎨 تثبيت dependencies الواجهة الأمامية...
cd frontend\web-app

if not exist "node_modules" (
    echo    📥 تحميل وتثبيت packages...
    npm install --silent --no-audit
    if errorlevel 1 (
        echo ❌ فشل في تثبيت dependencies الواجهة الأمامية
        cd ..\..
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت dependencies الواجهة الأمامية
) else (
    echo ✅ dependencies الواجهة الأمامية موجودة
)

:: Check for updates
echo 🔄 فحص التحديثات...
npm outdated --silent >nul 2>&1
if errorlevel 1 (
    echo ⚠️ توجد تحديثات متاحة (يمكن تحديثها لاحقاً)
) else (
    echo ✅ جميع الحزم محدثة
)

cd ..\..
echo.

:: Phase 4: Configuration Optimization
echo ═══════════════════════════════════════════════════════════════
echo ⚡ المرحلة 4: تحسين الإعدادات والأداء
echo ═══════════════════════════════════════════════════════════════
echo.

:: Create optimized Next.js config
echo 🔧 تحسين إعدادات Next.js...
cd frontend\web-app

if not exist "next.config.js" (
    (
        echo /** @type {import('next'^).NextConfig} */
        echo const nextConfig = {
        echo   // Performance Optimizations
        echo   reactStrictMode: true,
        echo   swcMinify: true,
        echo   compress: true,
        echo   poweredByHeader: false,
        echo.
        echo   // Image Optimization
        echo   images: {
        echo     domains: ['localhost', 'api.wstransfir.com'],
        echo     formats: ['image/webp', 'image/avif'],
        echo   },
        echo.
        echo   // Environment Variables
        echo   env: {
        echo     NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL ^|^| 'http://localhost:3000',
        echo     NEXT_PUBLIC_APP_NAME: 'WS Transfir',
        echo     NEXT_PUBLIC_APP_VERSION: '1.0.0',
        echo   },
        echo.
        echo   // API Rewrites
        echo   async rewrites(^) {
        echo     return [
        echo       {
        echo         source: '/api/:path*',
        echo         destination: 'http://localhost:3000/api/:path*',
        echo       },
        echo     ];
        echo   },
        echo.
        echo   // Headers for Security
        echo   async headers(^) {
        echo     return [
        echo       {
        echo         source: '/(.*^)',
        echo         headers: [
        echo           {
        echo             key: 'X-Frame-Options',
        echo             value: 'DENY',
        echo           },
        echo           {
        echo             key: 'X-Content-Type-Options',
        echo             value: 'nosniff',
        echo           },
        echo           {
        echo             key: 'Referrer-Policy',
        echo             value: 'origin-when-cross-origin',
        echo           },
        echo         ],
        echo       },
        echo     ];
        echo   },
        echo.
        echo   // Webpack Optimization
        echo   webpack: (config, { isServer }^) =^> {
        echo     if (!isServer^) {
        echo       config.resolve.fallback = {
        echo         ...config.resolve.fallback,
        echo         fs: false,
        echo       };
        echo     }
        echo     return config;
        echo   },
        echo };
        echo.
        echo module.exports = nextConfig;
    ) > next.config.js
    echo ✅ تم تحسين إعدادات Next.js
) else (
    echo ✅ إعدادات Next.js موجودة
)

:: Create TypeScript config if needed
if not exist "tsconfig.json" (
    echo 🔧 إنشاء إعدادات TypeScript...
    (
        echo {
        echo   "compilerOptions": {
        echo     "target": "es5",
        echo     "lib": ["dom", "dom.iterable", "es6"],
        echo     "allowJs": true,
        echo     "skipLibCheck": true,
        echo     "strict": true,
        echo     "forceConsistentCasingInFileNames": true,
        echo     "noEmit": true,
        echo     "esModuleInterop": true,
        echo     "module": "esnext",
        echo     "moduleResolution": "node",
        echo     "resolveJsonModule": true,
        echo     "isolatedModules": true,
        echo     "jsx": "preserve",
        echo     "incremental": true,
        echo     "plugins": [
        echo       {
        echo         "name": "next"
        echo       }
        echo     ],
        echo     "baseUrl": ".",
        echo     "paths": {
        echo       "@/*": ["./src/*"]
        echo     }
        echo   },
        echo   "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
        echo   "exclude": ["node_modules"]
        echo }
    ) > tsconfig.json
    echo ✅ تم إنشاء إعدادات TypeScript
)

cd ..\..
echo.

:: Phase 5: Mock API Server Setup
echo ═══════════════════════════════════════════════════════════════
echo 🔧 المرحلة 5: إعداد خادم API المتقدم
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🚀 إنشاء خادم API احترافي...
(
    echo const express = require('express'^);
    echo const cors = require('cors'^);
    echo const helmet = require('helmet'^);
    echo const compression = require('compression'^);
    echo const rateLimit = require('express-rate-limit'^);
    echo const app = express(^);
    echo.
    echo // Security Middleware
    echo app.use(helmet(^)^);
    echo app.use(compression(^)^);
    echo.
    echo // Rate Limiting
    echo const limiter = rateLimit({
    echo   windowMs: 15 * 60 * 1000, // 15 minutes
    echo   max: 100, // limit each IP to 100 requests per windowMs
    echo   message: 'Too many requests from this IP'
    echo }^);
    echo app.use('/api/', limiter^);
    echo.
    echo // CORS Configuration
    echo app.use(cors({
    echo   origin: ['http://localhost:3100', 'http://localhost:3000'],
    echo   credentials: true,
    echo   methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    echo   allowedHeaders: ['Content-Type', 'Authorization']
    echo }^)^);
    echo.
    echo app.use(express.json({ limit: '10mb' }^)^);
    echo app.use(express.urlencoded({ extended: true, limit: '10mb' }^)^);
    echo.
    echo // Logging Middleware
    echo app.use((req, res, next^) =^> {
    echo   const timestamp = new Date(^).toISOString(^);
    echo   console.log(`[${timestamp}] ${req.method} ${req.url} - ${req.ip}`^);
    echo   next(^);
    echo }^);
    echo.
    echo // Health Check
    echo app.get('/api/health', (req, res^) =^> {
    echo   res.json({
    echo     status: 'OK',
    echo     message: 'WS Transfir API Server is running',
    echo     timestamp: new Date(^).toISOString(^),
    echo     version: '1.0.0',
    echo     environment: 'development'
    echo   }^);
    echo }^);
    echo.
    echo // Authentication Endpoints
    echo app.post('/api/auth/login', (req, res^) =^> {
    echo   const { email, password } = req.body;
    echo   
    echo   // Mock authentication
    echo   if (email === '<EMAIL>' ^&^& password === 'admin123'^) {
    echo     res.json({
    echo       success: true,
    echo       token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock.token',
    echo       user: {
    echo         id: '1',
    echo         firstName: 'مدير',
    echo         lastName: 'النظام',
    echo         email: '<EMAIL>',
    echo         role: 'admin',
    echo         isVerified: true,
    echo         avatar: null
    echo       },
    echo       permissions: ['read', 'write', 'admin']
    echo     }^);
    echo   } else if (email ^&^& password^) {
    echo     res.json({
    echo       success: true,
    echo       token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock.user.token',
    echo       user: {
    echo         id: '2',
    echo         firstName: 'أحمد',
    echo         lastName: 'محمد',
    echo         email: email,
    echo         role: 'user',
    echo         isVerified: true,
    echo         avatar: null
    echo       },
    echo       permissions: ['read', 'write']
    echo     }^);
    echo   } else {
    echo     res.status(401^).json({
    echo       success: false,
    echo       message: 'بيانات الدخول غير صحيحة'
    echo     }^);
    echo   }
    echo }^);
    echo.
    echo app.post('/api/auth/register', (req, res^) =^> {
    echo   const { firstName, lastName, email, password, phone } = req.body;
    echo   
    echo   // Mock registration
    echo   res.json({
    echo     success: true,
    echo     message: 'تم إنشاء الحساب بنجاح',
    echo     user: {
    echo       id: Date.now(^).toString(^),
    echo       firstName,
    echo       lastName,
    echo       email,
    echo       phone,
    echo       isVerified: false
    echo     }
    echo   }^);
    echo }^);
    echo.
    echo // Profile Endpoints
    echo app.get('/api/profile/me', (req, res^) =^> {
    echo   res.json({
    echo     id: '1',
    echo     firstName: 'أحمد',
    echo     lastName: 'محمد',
    echo     email: '<EMAIL>',
    echo     phone: '+966501234567',
    echo     dateOfBirth: '1990-01-01',
    echo     nationality: 'SA',
    echo     isVerified: true,
    echo     profilePicture: null,
    echo     occupation: 'مهندس برمجيات',
    echo     employer: 'شركة التقنية المتقدمة',
    echo     monthlyIncome: 15000,
    echo     address: {
    echo       addressLine1: 'شارع الملك فهد',
    echo       city: 'الرياض',
    echo       country: 'SA',
    echo       postalCode: '12345'
    echo     },
    echo     emergencyContact: {
    echo       name: 'فاطمة محمد',
    echo       phone: '+966501234568',
    echo       relation: 'زوجة'
    echo     },
    echo     completionPercentage: 85
    echo   }^);
    echo }^);
    echo.
    echo // Transfers Endpoints
    echo app.get('/api/transfers', (req, res^) =^> {
    echo   const { page = 1, limit = 10, status, search } = req.query;
    echo   
    echo   const mockTransfers = [
    echo     {
    echo       id: '1',
    echo       referenceNumber: 'WS20241225001',
    echo       amount: '1,500.00',
    echo       currency: 'SAR',
    echo       receiverName: 'أحمد محمد',
    echo       receiverCountry: 'مصر',
    echo       status: 'completed',
    echo       createdAt: '2024-12-25T10:30:00Z',
    echo       estimatedDelivery: '2024-12-25T14:30:00Z',
    echo       fees: '15.00'
    echo     },
    echo     {
    echo       id: '2',
    echo       referenceNumber: 'WS20241224002',
    echo       amount: '750.00',
    echo       currency: 'USD',
    echo       receiverName: 'فاطمة علي',
    echo       receiverCountry: 'الأردن',
    echo       status: 'pending',
    echo       createdAt: '2024-12-24T15:45:00Z',
    echo       estimatedDelivery: '2024-12-26T15:45:00Z',
    echo       fees: '12.50'
    echo     },
    echo     {
    echo       id: '3',
    echo       referenceNumber: 'WS20241223003',
    echo       amount: '2,200.00',
    echo       currency: 'SAR',
    echo       receiverName: 'محمد حسن',
    echo       receiverCountry: 'لبنان',
    echo       status: 'processing',
    echo       createdAt: '2024-12-23T09:15:00Z',
    echo       estimatedDelivery: '2024-12-25T09:15:00Z',
    echo       fees: '22.00'
    echo     }
    echo   ];
    echo   
    echo   let filteredTransfers = mockTransfers;
    echo   
    echo   if (status^) {
    echo     filteredTransfers = filteredTransfers.filter(t =^> t.status === status^);
    echo   }
    echo   
    echo   if (search^) {
    echo     filteredTransfers = filteredTransfers.filter(t =^> 
    echo       t.referenceNumber.includes(search^) ^|^| 
    echo       t.receiverName.includes(search^)
    echo     ^);
    echo   }
    echo   
    echo   res.json({
    echo     data: filteredTransfers,
    echo     total: filteredTransfers.length,
    echo     page: parseInt(page^),
    echo     totalPages: Math.ceil(filteredTransfers.length / parseInt(limit^)^)
    echo   }^);
    echo }^);
    echo.
    echo app.get('/api/transfers/stats', (req, res^) =^> {
    echo   res.json({
    echo     totalTransfers: 24,
    echo     totalAmount: '45,230.50',
    echo     pendingTransfers: 3,
    echo     completedTransfers: 21
    echo   }^);
    echo }^);
    echo.
    echo // Error Handler
    echo app.use((err, req, res, next^) =^> {
    echo   console.error('Error:', err^);
    echo   res.status(500^).json({
    echo     success: false,
    echo     message: 'حدث خطأ في الخادم',
    echo     error: process.env.NODE_ENV === 'development' ? err.message : undefined
    echo   }^);
    echo }^);
    echo.
    echo // 404 Handler
    echo app.use('*', (req, res^) =^> {
    echo   res.status(404^).json({
    echo     success: false,
    echo     message: 'المسار غير موجود'
    echo   }^);
    echo }^);
    echo.
    echo const PORT = process.env.PORT ^|^| 3000;
    echo const server = app.listen(PORT, (^) =^> {
    echo   console.log('');
    echo   console.log('🚀 ═══════════════════════════════════════════════════════════');
    echo   console.log('🚀 WS TRANSFIR API SERVER - PROFESSIONAL MODE');
    echo   console.log('🚀 ═══════════════════════════════════════════════════════════');
    echo   console.log(`🌐 Server running on: http://localhost:${PORT}`^);
    echo   console.log(`📊 Health check: http://localhost:${PORT}/api/health`^);
    echo   console.log(`🔐 Admin login: <EMAIL> / admin123`^);
    echo   console.log(`👤 User login: <EMAIL> / password123`^);
    echo   console.log('🚀 ═══════════════════════════════════════════════════════════');
    echo   console.log('');
    echo }^);
    echo.
    echo // Graceful shutdown
    echo process.on('SIGTERM', (^) =^> {
    echo   console.log('🛑 Shutting down gracefully...'^^);
    echo   server.close((^) =^> {
    echo     console.log('✅ Server closed'^^);
    echo     process.exit(0^^);
    echo   }^^);
    echo }^^);
) > professional-api-server.js

echo ✅ تم إنشاء خادم API احترافي
echo.

:: Phase 6: Professional Startup
echo ═══════════════════════════════════════════════════════════════
echo 🚀 المرحلة 6: التشغيل الاحترافي
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🎬 بدء تشغيل النظام الاحترافي...
echo.

:: Start API Server
echo 🔧 تشغيل خادم API...
start "WS Transfir API Server" cmd /k "node professional-api-server.js"

:: Wait for API to start
echo ⏳ انتظار تشغيل خادم API...
timeout /t 5 /nobreak >nul

:: Start Frontend
echo 🎨 تشغيل الواجهة الأمامية...
cd frontend\web-app
start "WS Transfir Frontend" cmd /k "npm run dev"
cd ..\..

:: Wait for frontend to start
echo ⏳ انتظار تشغيل الواجهة الأمامية...
timeout /t 8 /nobreak >nul

:: Calculate startup time
set END_TIME=%TIME%
echo.
echo ═══════════════════════════════════════════════════════════════
echo ✅ تم تشغيل النظام بنجاح!
echo ═══════════════════════════════════════════════════════════════
echo.
echo 🕐 وقت البدء: %START_TIME%
echo 🕐 وقت الانتهاء: %END_TIME%
echo.

:: Open browser
echo 🌐 فتح المتصفح...
start "" "http://localhost:3100"
timeout /t 2 /nobreak >nul
start "" "http://localhost:3000/api/health"

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    ✅ WS TRANSFIR SYSTEM IS NOW RUNNING PROFESSIONALLY    ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🌐 روابط الوصول:
echo ═══════════════════════════════════════════════════════════════
echo 📱 التطبيق الرئيسي:     http://localhost:3100
echo 🔧 خادم API:            http://localhost:3000
echo 📊 فحص الصحة:          http://localhost:3000/api/health
echo.
echo 🔐 بيانات الدخول التجريبية:
echo ═══════════════════════════════════════════════════════════════
echo 👨‍💼 مدير النظام:        <EMAIL> / admin123
echo 👤 مستخدم عادي:        <EMAIL> / password123
echo.
echo 📋 الميزات المتاحة:
echo ═══════════════════════════════════════════════════════════════
echo ✅ تسجيل الدخول والخروج
echo ✅ إنشاء حساب جديد
echo ✅ إدارة الملف الشخصي
echo ✅ عرض التحويلات
echo ✅ تفاصيل التحويلات
echo ✅ استرداد كلمة المرور
echo ✅ إحصائيات متقدمة
echo ✅ واجهة مستخدم متطورة
echo.
echo 🛡️ الأمان والحماية:
echo ═══════════════════════════════════════════════════════════════
echo ✅ Rate Limiting
echo ✅ CORS Protection
echo ✅ Security Headers
echo ✅ Input Validation
echo ✅ Error Handling
echo.
echo 💡 نصائح الاستخدام:
echo ═══════════════════════════════════════════════════════════════
echo 🔹 استخدم بيانات الدخول التجريبية للاختبار
echo 🔹 جميع البيانات وهمية للعرض فقط
echo 🔹 تحقق من console للأخطاء
echo 🔹 اضغط Ctrl+C في نوافذ الأوامر للإيقاف
echo.
echo 📞 الدعم الفني:
echo ═══════════════════════════════════════════════════════════════
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📱 الهاتف: +966 11 123 4567
echo 🌐 الموقع: https://wstransfir.com
echo.

pause
