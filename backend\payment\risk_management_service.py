"""
Risk Management Service
======================
خدمة إدارة المخاطر المالية المتقدمة
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from decimal import Decimal
import json
import uuid
import statistics

import asyncpg
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """مستويات المخاطر"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskType(Enum):
    """أنواع المخاطر"""
    FRAUD = "fraud"
    VELOCITY = "velocity"
    AMOUNT = "amount"
    GEOGRAPHIC = "geographic"
    BEHAVIORAL = "behavioral"
    COMPLIANCE = "compliance"
    CREDIT = "credit"


class RiskAction(Enum):
    """إجراءات المخاطر"""
    ALLOW = "allow"
    REVIEW = "review"
    BLOCK = "block"
    REQUIRE_VERIFICATION = "require_verification"
    LIMIT_AMOUNT = "limit_amount"
    DELAY_PROCESSING = "delay_processing"


@dataclass
class RiskAssessmentRequest:
    """طلب تقييم المخاطر"""
    user_id: str
    transaction_amount: Decimal
    currency: str
    transaction_type: str
    payment_method: str
    ip_address: str = None
    device_fingerprint: str = None
    location: Dict[str, Any] = None
    metadata: Dict[str, Any] = None


@dataclass
class RiskAssessmentResult:
    """نتيجة تقييم المخاطر"""
    risk_score: float
    risk_level: RiskLevel
    recommended_action: RiskAction
    risk_factors: List[Dict[str, Any]]
    compliance_checks: Dict[str, bool]
    limits_applied: Dict[str, Any]
    requires_manual_review: bool
    assessment_id: str
    created_at: datetime


@dataclass
class RiskRule:
    """قاعدة المخاطر"""
    rule_id: str
    name: str
    risk_type: RiskType
    condition: str
    risk_score: float
    action: RiskAction
    is_active: bool
    priority: int


class RiskManagementService:
    """خدمة إدارة المخاطر المالية"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Risk scoring weights
        self.risk_weights = {
            RiskType.FRAUD: 0.25,
            RiskType.VELOCITY: 0.20,
            RiskType.AMOUNT: 0.15,
            RiskType.GEOGRAPHIC: 0.15,
            RiskType.BEHAVIORAL: 0.15,
            RiskType.COMPLIANCE: 0.10
        }
        
        # Risk thresholds
        self.risk_thresholds = {
            RiskLevel.LOW: (0.0, 30.0),
            RiskLevel.MEDIUM: (30.0, 60.0),
            RiskLevel.HIGH: (60.0, 80.0),
            RiskLevel.CRITICAL: (80.0, 100.0)
        }
        
        # Transaction limits by risk level
        self.transaction_limits = {
            RiskLevel.LOW: {
                "daily_limit": Decimal('50000.00'),
                "monthly_limit": Decimal('500000.00'),
                "single_transaction": Decimal('10000.00')
            },
            RiskLevel.MEDIUM: {
                "daily_limit": Decimal('25000.00'),
                "monthly_limit": Decimal('250000.00'),
                "single_transaction": Decimal('5000.00')
            },
            RiskLevel.HIGH: {
                "daily_limit": Decimal('10000.00'),
                "monthly_limit": Decimal('100000.00'),
                "single_transaction": Decimal('2000.00')
            },
            RiskLevel.CRITICAL: {
                "daily_limit": Decimal('1000.00'),
                "monthly_limit": Decimal('10000.00'),
                "single_transaction": Decimal('500.00')
            }
        }
        
        # Compliance rules
        self.compliance_rules = {
            "aml_check": True,
            "sanctions_check": True,
            "kyc_verification": True,
            "pep_screening": True,
            "transaction_monitoring": True
        }
        
        # Statistics
        self.assessments_performed = 0
        self.high_risk_transactions = 0
        self.blocked_transactions = 0
        
        # Initialize risk rules
        self.risk_rules = []
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """تهيئة القواعد الافتراضية"""
        default_rules = [
            # Fraud detection rules
            RiskRule(
                rule_id="fraud_001",
                name="Multiple failed attempts",
                risk_type=RiskType.FRAUD,
                condition="failed_attempts > 3 in last 1 hour",
                risk_score=40.0,
                action=RiskAction.REQUIRE_VERIFICATION,
                is_active=True,
                priority=1
            ),
            RiskRule(
                rule_id="fraud_002",
                name="Unusual device",
                risk_type=RiskType.FRAUD,
                condition="new_device and high_amount",
                risk_score=30.0,
                action=RiskAction.REVIEW,
                is_active=True,
                priority=2
            ),
            
            # Velocity rules
            RiskRule(
                rule_id="velocity_001",
                name="High transaction frequency",
                risk_type=RiskType.VELOCITY,
                condition="transactions > 10 in last 1 hour",
                risk_score=35.0,
                action=RiskAction.DELAY_PROCESSING,
                is_active=True,
                priority=1
            ),
            RiskRule(
                rule_id="velocity_002",
                name="Rapid successive transactions",
                risk_type=RiskType.VELOCITY,
                condition="transactions > 3 in last 5 minutes",
                risk_score=25.0,
                action=RiskAction.REVIEW,
                is_active=True,
                priority=2
            ),
            
            # Amount-based rules
            RiskRule(
                rule_id="amount_001",
                name="Large transaction",
                risk_type=RiskType.AMOUNT,
                condition="amount > 50000",
                risk_score=30.0,
                action=RiskAction.REQUIRE_VERIFICATION,
                is_active=True,
                priority=1
            ),
            RiskRule(
                rule_id="amount_002",
                name="Unusual amount pattern",
                risk_type=RiskType.AMOUNT,
                condition="amount > avg_amount * 5",
                risk_score=20.0,
                action=RiskAction.REVIEW,
                is_active=True,
                priority=3
            ),
            
            # Geographic rules
            RiskRule(
                rule_id="geo_001",
                name="High-risk country",
                risk_type=RiskType.GEOGRAPHIC,
                condition="country in high_risk_list",
                risk_score=50.0,
                action=RiskAction.BLOCK,
                is_active=True,
                priority=1
            ),
            RiskRule(
                rule_id="geo_002",
                name="Location change",
                risk_type=RiskType.GEOGRAPHIC,
                condition="location_change > 1000km in 1 hour",
                risk_score=35.0,
                action=RiskAction.REQUIRE_VERIFICATION,
                is_active=True,
                priority=2
            ),
            
            # Behavioral rules
            RiskRule(
                rule_id="behavior_001",
                name="Off-hours transaction",
                risk_type=RiskType.BEHAVIORAL,
                condition="time between 02:00 and 06:00",
                risk_score=15.0,
                action=RiskAction.REVIEW,
                is_active=True,
                priority=4
            ),
            RiskRule(
                rule_id="behavior_002",
                name="Unusual payment method",
                risk_type=RiskType.BEHAVIORAL,
                condition="new_payment_method and high_amount",
                risk_score=25.0,
                action=RiskAction.REQUIRE_VERIFICATION,
                is_active=True,
                priority=3
            )
        ]
        
        self.risk_rules = default_rules
    
    async def assess_risk(self, request: RiskAssessmentRequest) -> RiskAssessmentResult:
        """تقييم المخاطر"""
        try:
            logger.info(f"🔍 Assessing risk for user {request.user_id}: {request.transaction_amount} {request.currency}")
            
            # Generate assessment ID
            assessment_id = f"risk_{uuid.uuid4().hex[:12]}"
            
            # Get user risk profile
            user_profile = await self._get_user_risk_profile(request.user_id)
            
            # Get transaction history
            transaction_history = await self._get_transaction_history(request.user_id)
            
            # Perform risk checks
            risk_factors = []
            total_risk_score = 0.0
            
            # Fraud detection
            fraud_score = await self._check_fraud_indicators(request, user_profile, transaction_history)
            if fraud_score > 0:
                risk_factors.append({
                    "type": RiskType.FRAUD.value,
                    "score": fraud_score,
                    "description": "Fraud indicators detected",
                    "details": await self._get_fraud_details(request, user_profile)
                })
                total_risk_score += fraud_score * self.risk_weights[RiskType.FRAUD]
            
            # Velocity checks
            velocity_score = await self._check_velocity_limits(request, transaction_history)
            if velocity_score > 0:
                risk_factors.append({
                    "type": RiskType.VELOCITY.value,
                    "score": velocity_score,
                    "description": "High transaction velocity detected",
                    "details": await self._get_velocity_details(request, transaction_history)
                })
                total_risk_score += velocity_score * self.risk_weights[RiskType.VELOCITY]
            
            # Amount analysis
            amount_score = await self._check_amount_patterns(request, transaction_history)
            if amount_score > 0:
                risk_factors.append({
                    "type": RiskType.AMOUNT.value,
                    "score": amount_score,
                    "description": "Unusual transaction amount",
                    "details": await self._get_amount_details(request, transaction_history)
                })
                total_risk_score += amount_score * self.risk_weights[RiskType.AMOUNT]
            
            # Geographic analysis
            geo_score = await self._check_geographic_risk(request, user_profile)
            if geo_score > 0:
                risk_factors.append({
                    "type": RiskType.GEOGRAPHIC.value,
                    "score": geo_score,
                    "description": "Geographic risk detected",
                    "details": await self._get_geographic_details(request, user_profile)
                })
                total_risk_score += geo_score * self.risk_weights[RiskType.GEOGRAPHIC]
            
            # Behavioral analysis
            behavior_score = await self._check_behavioral_patterns(request, user_profile, transaction_history)
            if behavior_score > 0:
                risk_factors.append({
                    "type": RiskType.BEHAVIORAL.value,
                    "score": behavior_score,
                    "description": "Unusual behavioral pattern",
                    "details": await self._get_behavioral_details(request, user_profile)
                })
                total_risk_score += behavior_score * self.risk_weights[RiskType.BEHAVIORAL]
            
            # Compliance checks
            compliance_checks = await self._perform_compliance_checks(request, user_profile)
            compliance_score = await self._calculate_compliance_score(compliance_checks)
            if compliance_score > 0:
                risk_factors.append({
                    "type": RiskType.COMPLIANCE.value,
                    "score": compliance_score,
                    "description": "Compliance issues detected",
                    "details": compliance_checks
                })
                total_risk_score += compliance_score * self.risk_weights[RiskType.COMPLIANCE]
            
            # Determine risk level
            risk_level = self._determine_risk_level(total_risk_score)
            
            # Determine recommended action
            recommended_action = await self._determine_recommended_action(
                risk_level, risk_factors, request
            )
            
            # Check if manual review is required
            requires_manual_review = await self._requires_manual_review(
                risk_level, risk_factors, request
            )
            
            # Apply limits based on risk level
            limits_applied = await self._apply_risk_limits(request, risk_level)
            
            # Store risk assessment
            result = RiskAssessmentResult(
                risk_score=round(total_risk_score, 2),
                risk_level=risk_level,
                recommended_action=recommended_action,
                risk_factors=risk_factors,
                compliance_checks=compliance_checks,
                limits_applied=limits_applied,
                requires_manual_review=requires_manual_review,
                assessment_id=assessment_id,
                created_at=datetime.now()
            )
            
            await self._store_risk_assessment(assessment_id, request, result)
            
            # Update statistics
            self.assessments_performed += 1
            if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                self.high_risk_transactions += 1
            if recommended_action == RiskAction.BLOCK:
                self.blocked_transactions += 1
            
            logger.info(f"✅ Risk assessment completed: {assessment_id} - Score: {total_risk_score:.2f}, Level: {risk_level.value}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Risk assessment failed: {e}")
            
            # Return safe default for errors
            return RiskAssessmentResult(
                risk_score=100.0,
                risk_level=RiskLevel.CRITICAL,
                recommended_action=RiskAction.BLOCK,
                risk_factors=[{
                    "type": "system_error",
                    "score": 100.0,
                    "description": "Risk assessment system error",
                    "details": {"error": str(e)}
                }],
                compliance_checks={},
                limits_applied={},
                requires_manual_review=True,
                assessment_id=f"error_{uuid.uuid4().hex[:8]}",
                created_at=datetime.now()
            )
    
    async def get_user_risk_profile(self, user_id: str) -> Dict[str, Any]:
        """الحصول على ملف المخاطر للمستخدم"""
        return await self._get_user_risk_profile(user_id)
    
    async def update_risk_rules(self, rules: List[RiskRule]) -> bool:
        """تحديث قواعد المخاطر"""
        try:
            # Validate rules
            for rule in rules:
                if not self._validate_risk_rule(rule):
                    raise ValueError(f"Invalid risk rule: {rule.rule_id}")
            
            # Store rules in database
            await self._store_risk_rules(rules)
            
            # Update in-memory rules
            self.risk_rules = rules
            
            logger.info(f"✅ Updated {len(rules)} risk rules")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to update risk rules: {e}")
            return False
    
    async def get_risk_statistics(
        self,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> Dict[str, Any]:
        """الحصول على إحصائيات المخاطر"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = []
                params = []
                param_count = 0
                
                if start_date:
                    param_count += 1
                    where_conditions.append(f"created_at >= ${param_count}")
                    params.append(start_date)
                
                if end_date:
                    param_count += 1
                    where_conditions.append(f"created_at <= ${param_count}")
                    params.append(end_date)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Overall statistics
                stats_query = f"""
                    SELECT 
                        COUNT(*) as total_assessments,
                        AVG(risk_score) as avg_risk_score,
                        COUNT(CASE WHEN risk_level = 'low' THEN 1 END) as low_risk_count,
                        COUNT(CASE WHEN risk_level = 'medium' THEN 1 END) as medium_risk_count,
                        COUNT(CASE WHEN risk_level = 'high' THEN 1 END) as high_risk_count,
                        COUNT(CASE WHEN risk_level = 'critical' THEN 1 END) as critical_risk_count,
                        COUNT(CASE WHEN recommended_action = 'block' THEN 1 END) as blocked_count,
                        COUNT(CASE WHEN requires_manual_review = true THEN 1 END) as manual_review_count
                    FROM risk_assessments 
                    WHERE {where_clause}
                """
                
                stats = await conn.fetchrow(stats_query, *params)
                
                # Risk type breakdown
                risk_type_query = f"""
                    SELECT 
                        risk_type,
                        COUNT(*) as count,
                        AVG(risk_score) as avg_score
                    FROM risk_factors rf
                    JOIN risk_assessments ra ON rf.assessment_id = ra.id
                    WHERE {where_clause.replace('created_at', 'ra.created_at')}
                    GROUP BY risk_type
                    ORDER BY avg_score DESC
                """
                
                risk_type_stats = await conn.fetch(risk_type_query, *params)
                
                # Daily trend
                daily_query = f"""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as assessments_count,
                        AVG(risk_score) as avg_risk_score,
                        COUNT(CASE WHEN risk_level IN ('high', 'critical') THEN 1 END) as high_risk_count
                    FROM risk_assessments 
                    WHERE {where_clause}
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                    LIMIT 30
                """
                
                daily_stats = await conn.fetch(daily_query, *params)
                
                total_assessments = stats['total_assessments']
                
                return {
                    "overview": {
                        "total_assessments": total_assessments,
                        "avg_risk_score": round(float(stats['avg_risk_score'] or 0), 2),
                        "risk_distribution": {
                            "low": stats['low_risk_count'],
                            "medium": stats['medium_risk_count'],
                            "high": stats['high_risk_count'],
                            "critical": stats['critical_risk_count']
                        },
                        "blocked_transactions": stats['blocked_count'],
                        "manual_reviews": stats['manual_review_count'],
                        "block_rate": round((stats['blocked_count'] / max(total_assessments, 1)) * 100, 2),
                        "manual_review_rate": round((stats['manual_review_count'] / max(total_assessments, 1)) * 100, 2)
                    },
                    "by_risk_type": [
                        {
                            "risk_type": row['risk_type'],
                            "count": row['count'],
                            "avg_score": round(float(row['avg_score']), 2)
                        }
                        for row in risk_type_stats
                    ],
                    "daily_trend": [
                        {
                            "date": row['date'].isoformat(),
                            "assessments_count": row['assessments_count'],
                            "avg_risk_score": round(float(row['avg_risk_score']), 2),
                            "high_risk_count": row['high_risk_count'],
                            "high_risk_rate": round((row['high_risk_count'] / max(row['assessments_count'], 1)) * 100, 2)
                        }
                        for row in daily_stats
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get risk statistics: {e}")
            return {}
    
    # Helper methods
    async def _get_user_risk_profile(self, user_id: str) -> Dict[str, Any]:
        """الحصول على ملف المخاطر للمستخدم"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Get user basic info
                user_query = """
                    SELECT 
                        id, created_at, kyc_status, kyc_level, 
                        is_verified, account_status, country, risk_level
                    FROM users 
                    WHERE id = $1
                """
                
                user = await conn.fetchrow(user_query, user_id)
                
                if not user:
                    return {"user_id": user_id, "risk_level": "high", "is_new_user": True}
                
                # Get recent risk assessments
                recent_assessments_query = """
                    SELECT risk_score, risk_level, created_at
                    FROM risk_assessments 
                    WHERE user_id = $1 
                    ORDER BY created_at DESC 
                    LIMIT 10
                """
                
                recent_assessments = await conn.fetch(recent_assessments_query, user_id)
                
                # Calculate average risk score
                if recent_assessments:
                    avg_risk_score = statistics.mean([float(a['risk_score']) for a in recent_assessments])
                    risk_trend = self._calculate_risk_trend(recent_assessments)
                else:
                    avg_risk_score = 50.0  # Default for new users
                    risk_trend = "stable"
                
                # Get transaction patterns
                transaction_patterns = await self._get_transaction_patterns(conn, user_id)
                
                return {
                    "user_id": user_id,
                    "account_age_days": (datetime.now() - user['created_at']).days,
                    "kyc_status": user['kyc_status'],
                    "kyc_level": user['kyc_level'],
                    "is_verified": user['is_verified'],
                    "account_status": user['account_status'],
                    "country": user['country'],
                    "current_risk_level": user['risk_level'] or "medium",
                    "avg_risk_score": round(avg_risk_score, 2),
                    "risk_trend": risk_trend,
                    "transaction_patterns": transaction_patterns,
                    "is_new_user": (datetime.now() - user['created_at']).days < 30
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get user risk profile: {e}")
            return {"user_id": user_id, "risk_level": "high", "error": str(e)}
    
    async def _get_transaction_history(self, user_id: str) -> List[Dict[str, Any]]:
        """الحصول على تاريخ المعاملات"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        amount, currency, transaction_type, status, 
                        created_at, ip_address, device_fingerprint
                    FROM transactions 
                    WHERE user_id = $1 
                    AND created_at >= $2
                    ORDER BY created_at DESC
                    LIMIT 100
                """
                
                # Get transactions from last 30 days
                since_date = datetime.now() - timedelta(days=30)
                rows = await conn.fetch(query, user_id, since_date)
                
                return [
                    {
                        "amount": float(row['amount']),
                        "currency": row['currency'],
                        "transaction_type": row['transaction_type'],
                        "status": row['status'],
                        "created_at": row['created_at'],
                        "ip_address": row['ip_address'],
                        "device_fingerprint": row['device_fingerprint']
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get transaction history: {e}")
            return []
    
    async def _check_fraud_indicators(
        self, 
        request: RiskAssessmentRequest, 
        user_profile: Dict[str, Any], 
        transaction_history: List[Dict[str, Any]]
    ) -> float:
        """فحص مؤشرات الاحتيال"""
        fraud_score = 0.0
        
        # Check for multiple failed attempts
        recent_failures = [
            t for t in transaction_history 
            if t['status'] == 'failed' and 
            (datetime.now() - t['created_at']).total_seconds() < 3600  # Last hour
        ]
        
        if len(recent_failures) > 3:
            fraud_score += 40.0
        elif len(recent_failures) > 1:
            fraud_score += 20.0
        
        # Check for new device with high amount
        if (request.device_fingerprint and 
            request.device_fingerprint not in [t.get('device_fingerprint') for t in transaction_history] and
            request.transaction_amount > Decimal('5000.00')):
            fraud_score += 30.0
        
        # Check for unusual IP address
        if (request.ip_address and 
            request.ip_address not in [t.get('ip_address') for t in transaction_history]):
            fraud_score += 15.0
        
        # Check account age vs transaction amount
        if (user_profile.get('is_new_user', False) and 
            request.transaction_amount > Decimal('1000.00')):
            fraud_score += 25.0
        
        return min(fraud_score, 100.0)
    
    async def _check_velocity_limits(
        self, 
        request: RiskAssessmentRequest, 
        transaction_history: List[Dict[str, Any]]
    ) -> float:
        """فحص حدود السرعة"""
        velocity_score = 0.0
        now = datetime.now()
        
        # Check transactions in last hour
        last_hour_transactions = [
            t for t in transaction_history 
            if (now - t['created_at']).total_seconds() < 3600
        ]
        
        if len(last_hour_transactions) > 10:
            velocity_score += 35.0
        elif len(last_hour_transactions) > 5:
            velocity_score += 20.0
        
        # Check transactions in last 5 minutes
        last_5min_transactions = [
            t for t in transaction_history 
            if (now - t['created_at']).total_seconds() < 300
        ]
        
        if len(last_5min_transactions) > 3:
            velocity_score += 25.0
        elif len(last_5min_transactions) > 1:
            velocity_score += 10.0
        
        # Check daily transaction count
        today_transactions = [
            t for t in transaction_history 
            if t['created_at'].date() == now.date()
        ]
        
        if len(today_transactions) > 20:
            velocity_score += 20.0
        elif len(today_transactions) > 10:
            velocity_score += 10.0
        
        return min(velocity_score, 100.0)
    
    async def _check_amount_patterns(
        self, 
        request: RiskAssessmentRequest, 
        transaction_history: List[Dict[str, Any]]
    ) -> float:
        """فحص أنماط المبالغ"""
        amount_score = 0.0
        
        # Check for large transaction
        if request.transaction_amount > Decimal('50000.00'):
            amount_score += 30.0
        elif request.transaction_amount > Decimal('20000.00'):
            amount_score += 15.0
        
        # Check against user's average transaction amount
        if transaction_history:
            amounts = [t['amount'] for t in transaction_history if t['status'] == 'completed']
            if amounts:
                avg_amount = statistics.mean(amounts)
                if float(request.transaction_amount) > avg_amount * 5:
                    amount_score += 20.0
                elif float(request.transaction_amount) > avg_amount * 3:
                    amount_score += 10.0
        
        # Check for round numbers (potential structuring)
        amount_str = str(request.transaction_amount)
        if amount_str.endswith('000.00') or amount_str.endswith('500.00'):
            amount_score += 5.0
        
        return min(amount_score, 100.0)
    
    async def _check_geographic_risk(
        self, 
        request: RiskAssessmentRequest, 
        user_profile: Dict[str, Any]
    ) -> float:
        """فحص المخاطر الجغرافية"""
        geo_score = 0.0
        
        # High-risk countries (simplified list)
        high_risk_countries = ['XX', 'YY', 'ZZ']  # Replace with actual list
        
        if request.location:
            country = request.location.get('country')
            if country in high_risk_countries:
                geo_score += 50.0
            
            # Check for location change
            user_country = user_profile.get('country')
            if user_country and country != user_country:
                geo_score += 20.0
        
        return min(geo_score, 100.0)
    
    async def _check_behavioral_patterns(
        self, 
        request: RiskAssessmentRequest, 
        user_profile: Dict[str, Any], 
        transaction_history: List[Dict[str, Any]]
    ) -> float:
        """فحص الأنماط السلوكية"""
        behavior_score = 0.0
        
        # Check transaction time
        current_hour = datetime.now().hour
        if 2 <= current_hour <= 6:  # Off-hours
            behavior_score += 15.0
        
        # Check payment method consistency
        if transaction_history:
            used_methods = set(t.get('payment_method', 'unknown') for t in transaction_history[-10:])
            if request.payment_method not in used_methods and request.transaction_amount > Decimal('1000.00'):
                behavior_score += 25.0
        
        # Check transaction type patterns
        if request.transaction_type not in ['transfer', 'payment']:  # Unusual types
            behavior_score += 10.0
        
        return min(behavior_score, 100.0)
    
    async def _perform_compliance_checks(
        self, 
        request: RiskAssessmentRequest, 
        user_profile: Dict[str, Any]
    ) -> Dict[str, bool]:
        """إجراء فحوصات الامتثال"""
        checks = {}
        
        # AML check
        checks['aml_check'] = user_profile.get('kyc_status') == 'approved'
        
        # Sanctions check (simplified)
        checks['sanctions_check'] = True  # Would integrate with sanctions database
        
        # KYC verification
        checks['kyc_verification'] = user_profile.get('is_verified', False)
        
        # PEP screening
        checks['pep_screening'] = True  # Would integrate with PEP database
        
        # Transaction monitoring
        checks['transaction_monitoring'] = request.transaction_amount <= Decimal('10000.00')
        
        return checks
    
    async def _calculate_compliance_score(self, compliance_checks: Dict[str, bool]) -> float:
        """حساب نقاط الامتثال"""
        failed_checks = sum(1 for passed in compliance_checks.values() if not passed)
        return failed_checks * 20.0  # 20 points per failed check
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """تحديد مستوى المخاطر"""
        for level, (min_score, max_score) in self.risk_thresholds.items():
            if min_score <= risk_score < max_score:
                return level
        return RiskLevel.CRITICAL
    
    async def _determine_recommended_action(
        self, 
        risk_level: RiskLevel, 
        risk_factors: List[Dict[str, Any]], 
        request: RiskAssessmentRequest
    ) -> RiskAction:
        """تحديد الإجراء الموصى به"""
        # Check for critical risk factors
        for factor in risk_factors:
            if factor['type'] == RiskType.FRAUD.value and factor['score'] > 40:
                return RiskAction.BLOCK
            if factor['type'] == RiskType.COMPLIANCE.value and factor['score'] > 60:
                return RiskAction.BLOCK
        
        # Based on risk level
        if risk_level == RiskLevel.CRITICAL:
            return RiskAction.BLOCK
        elif risk_level == RiskLevel.HIGH:
            return RiskAction.REQUIRE_VERIFICATION
        elif risk_level == RiskLevel.MEDIUM:
            return RiskAction.REVIEW
        else:
            return RiskAction.ALLOW
    
    async def _requires_manual_review(
        self, 
        risk_level: RiskLevel, 
        risk_factors: List[Dict[str, Any]], 
        request: RiskAssessmentRequest
    ) -> bool:
        """تحديد ما إذا كانت المراجعة اليدوية مطلوبة"""
        # Always require manual review for critical risk
        if risk_level == RiskLevel.CRITICAL:
            return True
        
        # High amounts require review
        if request.transaction_amount > Decimal('25000.00'):
            return True
        
        # Multiple high-score risk factors
        high_score_factors = [f for f in risk_factors if f['score'] > 30]
        if len(high_score_factors) >= 2:
            return True
        
        return False
    
    async def _apply_risk_limits(
        self, 
        request: RiskAssessmentRequest, 
        risk_level: RiskLevel
    ) -> Dict[str, Any]:
        """تطبيق حدود المخاطر"""
        limits = self.transaction_limits.get(risk_level, {})
        
        applied_limits = {}
        
        # Check single transaction limit
        single_limit = limits.get('single_transaction', Decimal('999999.00'))
        if request.transaction_amount > single_limit:
            applied_limits['single_transaction_limit'] = float(single_limit)
            applied_limits['amount_reduced'] = True
        
        # Add other applicable limits
        applied_limits['daily_limit'] = float(limits.get('daily_limit', Decimal('999999.00')))
        applied_limits['monthly_limit'] = float(limits.get('monthly_limit', Decimal('999999.00')))
        
        return applied_limits
    
    async def _store_risk_assessment(
        self, 
        assessment_id: str, 
        request: RiskAssessmentRequest, 
        result: RiskAssessmentResult
    ):
        """حفظ تقييم المخاطر"""
        try:
            async with self.db_connection.get_connection() as conn:
                async with conn.transaction():
                    # Store main assessment
                    assessment_query = """
                        INSERT INTO risk_assessments (
                            id, user_id, transaction_amount, currency, transaction_type,
                            payment_method, risk_score, risk_level, recommended_action,
                            requires_manual_review, ip_address, device_fingerprint, metadata
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                    """
                    
                    await conn.execute(
                        assessment_query,
                        assessment_id,
                        request.user_id,
                        request.transaction_amount,
                        request.currency,
                        request.transaction_type,
                        request.payment_method,
                        result.risk_score,
                        result.risk_level.value,
                        result.recommended_action.value,
                        result.requires_manual_review,
                        request.ip_address,
                        request.device_fingerprint,
                        json.dumps(request.metadata or {})
                    )
                    
                    # Store risk factors
                    for factor in result.risk_factors:
                        factor_query = """
                            INSERT INTO risk_factors (
                                assessment_id, risk_type, risk_score, description, details
                            ) VALUES ($1, $2, $3, $4, $5)
                        """
                        
                        await conn.execute(
                            factor_query,
                            assessment_id,
                            factor['type'],
                            factor['score'],
                            factor['description'],
                            json.dumps(factor['details'])
                        )
                
        except Exception as e:
            logger.error(f"❌ Failed to store risk assessment: {e}")
    
    def _validate_risk_rule(self, rule: RiskRule) -> bool:
        """التحقق من صحة قاعدة المخاطر"""
        if not rule.rule_id or not rule.name:
            return False
        
        if rule.risk_score < 0 or rule.risk_score > 100:
            return False
        
        if rule.priority < 1 or rule.priority > 10:
            return False
        
        return True
    
    async def _store_risk_rules(self, rules: List[RiskRule]):
        """حفظ قواعد المخاطر"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Clear existing rules
                await conn.execute("DELETE FROM risk_rules")
                
                # Insert new rules
                for rule in rules:
                    query = """
                        INSERT INTO risk_rules (
                            id, name, risk_type, condition_text, risk_score,
                            action, is_active, priority
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    """
                    
                    await conn.execute(
                        query,
                        rule.rule_id,
                        rule.name,
                        rule.risk_type.value,
                        rule.condition,
                        rule.risk_score,
                        rule.action.value,
                        rule.is_active,
                        rule.priority
                    )
                
        except Exception as e:
            logger.error(f"❌ Failed to store risk rules: {e}")
            raise
    
    def _calculate_risk_trend(self, assessments: List[Dict[str, Any]]) -> str:
        """حساب اتجاه المخاطر"""
        if len(assessments) < 3:
            return "stable"
        
        recent_scores = [float(a['risk_score']) for a in assessments[:3]]
        older_scores = [float(a['risk_score']) for a in assessments[3:6]] if len(assessments) > 3 else recent_scores
        
        recent_avg = statistics.mean(recent_scores)
        older_avg = statistics.mean(older_scores)
        
        if recent_avg > older_avg + 10:
            return "increasing"
        elif recent_avg < older_avg - 10:
            return "decreasing"
        else:
            return "stable"
    
    async def _get_transaction_patterns(self, conn, user_id: str) -> Dict[str, Any]:
        """الحصول على أنماط المعاملات"""
        try:
            query = """
                SELECT 
                    COUNT(*) as total_transactions,
                    AVG(amount) as avg_amount,
                    MAX(amount) as max_amount,
                    COUNT(DISTINCT DATE(created_at)) as active_days,
                    COUNT(DISTINCT payment_method) as payment_methods_used
                FROM transactions 
                WHERE user_id = $1 
                AND created_at >= $2
                AND status = 'completed'
            """
            
            since_date = datetime.now() - timedelta(days=90)
            row = await conn.fetchrow(query, user_id, since_date)
            
            return {
                "total_transactions": row['total_transactions'] or 0,
                "avg_amount": float(row['avg_amount'] or 0),
                "max_amount": float(row['max_amount'] or 0),
                "active_days": row['active_days'] or 0,
                "payment_methods_used": row['payment_methods_used'] or 0
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get transaction patterns: {e}")
            return {}
    
    async def _get_fraud_details(
        self, 
        request: RiskAssessmentRequest, 
        user_profile: Dict[str, Any]
    ) -> Dict[str, Any]:
        """الحصول على تفاصيل الاحتيال"""
        return {
            "new_device": request.device_fingerprint not in user_profile.get('known_devices', []),
            "new_ip": request.ip_address not in user_profile.get('known_ips', []),
            "account_age_days": user_profile.get('account_age_days', 0),
            "is_new_user": user_profile.get('is_new_user', False)
        }
    
    async def _get_velocity_details(
        self, 
        request: RiskAssessmentRequest, 
        transaction_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """الحصول على تفاصيل السرعة"""
        now = datetime.now()
        
        return {
            "transactions_last_hour": len([
                t for t in transaction_history 
                if (now - t['created_at']).total_seconds() < 3600
            ]),
            "transactions_last_5min": len([
                t for t in transaction_history 
                if (now - t['created_at']).total_seconds() < 300
            ]),
            "transactions_today": len([
                t for t in transaction_history 
                if t['created_at'].date() == now.date()
            ])
        }
    
    async def _get_amount_details(
        self, 
        request: RiskAssessmentRequest, 
        transaction_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """الحصول على تفاصيل المبلغ"""
        completed_transactions = [t for t in transaction_history if t['status'] == 'completed']
        
        if completed_transactions:
            amounts = [t['amount'] for t in completed_transactions]
            avg_amount = statistics.mean(amounts)
            max_amount = max(amounts)
        else:
            avg_amount = 0
            max_amount = 0
        
        return {
            "transaction_amount": float(request.transaction_amount),
            "user_avg_amount": avg_amount,
            "user_max_amount": max_amount,
            "amount_vs_avg_ratio": float(request.transaction_amount) / max(avg_amount, 1)
        }
    
    async def _get_geographic_details(
        self, 
        request: RiskAssessmentRequest, 
        user_profile: Dict[str, Any]
    ) -> Dict[str, Any]:
        """الحصول على التفاصيل الجغرافية"""
        return {
            "transaction_country": request.location.get('country') if request.location else None,
            "user_country": user_profile.get('country'),
            "ip_address": request.ip_address,
            "location_change": request.location.get('country') != user_profile.get('country')
        }
    
    async def _get_behavioral_details(
        self, 
        request: RiskAssessmentRequest, 
        user_profile: Dict[str, Any]
    ) -> Dict[str, Any]:
        """الحصول على التفاصيل السلوكية"""
        return {
            "transaction_hour": datetime.now().hour,
            "is_off_hours": 2 <= datetime.now().hour <= 6,
            "payment_method": request.payment_method,
            "transaction_type": request.transaction_type,
            "is_new_payment_method": request.payment_method not in user_profile.get('used_payment_methods', [])
        }
    
    async def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمة"""
        return {
            "assessments_performed": self.assessments_performed,
            "high_risk_transactions": self.high_risk_transactions,
            "blocked_transactions": self.blocked_transactions,
            "block_rate": (self.blocked_transactions / max(self.assessments_performed, 1)) * 100,
            "high_risk_rate": (self.high_risk_transactions / max(self.assessments_performed, 1)) * 100,
            "active_risk_rules": len([r for r in self.risk_rules if r.is_active]),
            "total_risk_rules": len(self.risk_rules)
        }
