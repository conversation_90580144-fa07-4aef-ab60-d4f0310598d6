{"version": 3, "file": "repl.js", "sourceRoot": "", "sources": ["../src/repl.ts"], "names": [], "mappings": ";;;AACA,2BAA6B;AAC7B,+BAA4B;AAC5B,+BAKc;AACd,2BAAoD;AACpD,mCAA+D;AAC/D,2BAA4C;AAC5C,qCAAkC;AAClC,iCAAiC;AAGjC,mCAAwC;AAExC,eAAe;AACf,IAAI,qBAAqD,CAAC;AAC1D,SAAS,uBAAuB;IAC9B,IAAI,qBAAqB,KAAK,SAAS,EAAE;QACvC,CAAC;YACC,oBAAoB,EAAE,qBAAqB;SAC5C,GAAG,OAAO,CAAC,sCAAsC,CAAC,CAAC,CAAC;KACtD;IACD,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AACD,IAAI,IAAkB,CAAC;AACvB,SAAS,YAAY;IACnB,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;KACxB;IACD,OAAO,IAAI,CAAC,SAAS,CAAC;AACxB,CAAC;AAED,gBAAgB;AACH,QAAA,aAAa,GAAG,WAAW,CAAC;AACzC,gBAAgB;AACH,QAAA,SAAS,GAAG,QAAQ,CAAC;AAClC,gBAAgB;AACH,QAAA,cAAc,GAAG,YAAY,CAAC;AAC3C,gBAAgB;AACH,QAAA,UAAU,GAAG,SAAS,CAAC;AACpC,gBAAgB;AACH,QAAA,aAAa,GAAG,WAAW,CAAC;AACzC,gBAAgB;AACH,QAAA,SAAS,GAAG,QAAQ,CAAC;AAsFlC;;;;;;;;;;;;;;;GAeG;AACH,SAAgB,UAAU,CAAC,UAA6B,EAAE;;IACxD,MAAM,EAAE,iDAAiD,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IAC7E,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAC9B,IAAI,cAA0B,CAAC;IAC/B,wEAAwE;IACxE,2FAA2F;IAC3F,IAAI,OAA4B,CAAC;IACjC,MAAM,KAAK,GACT,MAAA,OAAO,CAAC,KAAK,mCAAI,IAAI,SAAS,CAAC,IAAA,WAAI,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,qBAAa,CAAC,CAAC,CAAC;IACrE,MAAM,oBAAoB,GAAG,0BAA0B,CACrD,KAAK,EACL,OAAO,CAAC,+BAA+B,CACxC,CAAC;IACF,MAAM,KAAK,GAAG,MAAA,OAAO,CAAC,KAAK,mCAAI,OAAO,CAAC,KAAK,CAAC;IAC7C,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,OAAO,CAAC,MAAM,CAAC;IAChD,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,OAAO,CAAC,MAAM,CAAC;IAChD,MAAM,QAAQ,GACZ,MAAM,KAAK,OAAO,CAAC,MAAM,IAAI,MAAM,KAAK,OAAO,CAAC,MAAM;QACpD,CAAC,CAAC,OAAO;QACT,CAAC,CAAC,IAAI,iBAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAElC,MAAM,WAAW,GAAgB;QAC/B,KAAK,EAAE,MAAA,OAAO,CAAC,KAAK,mCAAI,IAAI,SAAS,CAAC,IAAA,WAAI,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,qBAAa,CAAC,CAAC;QACzE,UAAU;QACV,QAAQ;QACR,gBAAgB;QAChB,QAAQ;QACR,oBAAoB;QACpB,KAAK;QACL,aAAa;QACb,KAAK;QACL,MAAM;QACN,MAAM;QACN,OAAO,EAAE,QAAQ;KAClB,CAAC;IAEF,OAAO,WAAW,CAAC;IAEnB,SAAS,UAAU,CAAC,QAAiB;QACnC,OAAO,GAAG,QAAQ,CAAC;QACnB,IAAI,iDAAiD,EAAE;YACrD,OAAO,CAAC,mBAAmB,CAAC;gBAC1B,iBAAiB,EAAE,KAAK;gBACxB,iBAAiB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC/B,kBAAkB,EAAE;oBAClB,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,EAAE,CAAC;iBACjE;aACF,CAAC,CAAC;SACJ;IACH,CAAC;IAED,SAAS,QAAQ,CAAC,IAAY;QAC5B,MAAM,MAAM,GAAG,yBAAyB,CAAC;YACvC,OAAO,EAAE,OAAQ;YACjB,KAAK;YACL,KAAK,EAAE,IAAI;YACX,OAAO;YACP,oBAAoB,EAAE,KAAK;SAC5B,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,qBAAqB,KAAK,KAAK,CAAC,CAAC;QAC/C,OAAO,MAAM,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,SAAS,gBAAgB,CAAC,OAIzB;QACC,MAAM,EAAE,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QACvD,OAAO,yBAAyB,CAAC;YAC/B,OAAO,EAAE,OAAQ;YACjB,KAAK;YACL,KAAK,EAAE,IAAI;YACX,mBAAmB;YACnB,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED,SAAS,QAAQ,CACf,IAAY,EACZ,OAAY,EACZ,SAAiB,EACjB,QAAkD;QAElD,kDAAkD;QAClD,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,QAAQ,CAAC,IAAI,CAAC,CAAC;YACf,OAAO;SACR;QAED,IAAI;YACF,MAAM,UAAU,GAAG,gBAAgB,CAAC;gBAClC,IAAI;gBACJ,mBAAmB,EAAE,IAAI;gBACzB,OAAO;aACR,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,qBAAqB,EAAE;gBACpC,CAAC,KAAK,IAAI,EAAE;oBACV,IAAI;wBACF,QAAQ,CAAC,IAAI,EAAE,MAAM,UAAU,CAAC,YAAY,CAAC,CAAC;qBAC/C;oBAAC,OAAO,YAAY,EAAE;wBACrB,WAAW,CAAC,YAAY,CAAC,CAAC;qBAC3B;gBACH,CAAC,CAAC,EAAE,CAAC;aACN;iBAAM;gBACL,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;aAClC;SACF;QAAC,OAAO,KAAK,EAAE;YACd,WAAW,CAAC,KAAK,CAAC,CAAC;SACpB;QAED,4EAA4E;QAC5E,6CAA6C;QAC7C,iEAAiE;QACjE,SAAS,WAAW,CAAC,KAAc;;YACjC,2EAA2E;YAC3E,MAAM,uBAAuB,GAC3B,OAAQ,CAAC,OAAO,CAAC,qBAAqB,KAAK,KAAK;gBAChD,CAAC,OAAQ,CAAC,eAAe,CAAC;YAC5B,IAAI,KAAK,YAAY,eAAO,EAAE;gBAC5B,oDAAoD;gBACpD,IAAI,kBAAW,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;oBACvC,QAAQ,CAAC,IAAI,kBAAW,CAAC,KAAK,CAAC,CAAC,CAAC;oBACjC,OAAO;iBACR;qBAAM;oBACL,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAEtB,IACE,uBAAuB;wBACvB,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAChC,4BAA4B,CAAC,QAAQ,CAAC,EAAE,CAAC,CAC1C,EACD;wBACA,QAAQ,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC;qBACxC;oBACD,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAChB;aACF;iBAAM;gBACL,IAAI,MAAM,GAAG,KAA0B,CAAC;gBACxC,IACE,uBAAuB;oBACvB,MAAM,YAAY,WAAW;qBAC7B,MAAA,MAAM,CAAC,OAAO,0CAAE,QAAQ,CAAC,qBAAqB,CAAC,CAAA,EAC/C;oBACA,IAAI;wBACF,0DAA0D;wBAC1D,MAAM,CAAC,OAAO,IAAI,OAAO,oBAAoB,EAAE,EAAE,CAAC;wBAClD,MAAM,CAAC,KAAK,GAAG,MAAA,MAAM,CAAC,KAAK,0CAAE,OAAO,CAClC,kBAAkB,EAClB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,OAAO,oBAAoB,EAAE,EAAE,CAChD,CAAC;qBACH;oBAAC,MAAM,GAAE;iBACX;gBACD,QAAQ,CAAC,MAAe,CAAC,CAAC;aAC3B;QACH,CAAC;QACD,SAAS,oBAAoB;YAC3B,OAAO,8HACL,OAAQ,CAAC,EAAE,CAAC,OACd,eACE,OAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,OAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAO,CAC1D,GAAG,CAAC;QACN,CAAC;IACH,CAAC;IAED,sCAAsC;IACtC,SAAS,KAAK,CAAC,IAAa;QAC1B,aAAa,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,sCAAsC;IACtC,SAAS,aAAa,CACpB,OAAoE;QAEpE,MAAM,EAAE,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;QAC3E,+EAA+E;QAE/E,6CAA6C;QAC7C,mBAAmB;QACnB,IAAI,IAAI,EAAE;YACR,IAAI;gBACF,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;aACvB;YAAC,OAAO,GAAG,EAAE;gBACZ,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpB,uFAAuF;gBACvF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB;SACF;QAED,gEAAgE;QAChE,0DAA0D;QAC1D,6DAA6D;QAC7D,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAEjC,MAAM,IAAI,GAAG,IAAA,YAAa,EAAC;YACzB,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,kJAAkJ;YAClJ,QAAQ,EACL,MAA0B,CAAC,KAAK;gBACjC,CAAC,QAAQ,CAAC,WAAG,CAAC,gBAAiB,EAAE,EAAE,CAAC;YACtC,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,IAAI;YACf,GAAG,eAAe;SACnB,CAAC,CAAC;QAEH,cAAc,GAAG,IAAI,CAAC;QACtB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAEvB,2DAA2D;QAC3D,MAAM,SAAS,GAAG,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE/C,SAAS,KAAK;YACZ,SAAS,EAAE,CAAC;YAEZ,yEAAyE;YACzE,YAAY,CAAC,0BAA0B,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9D,IAAI,eAAe,EAAE;gBACnB,KAAK,CAAC,KAAK,IAAI,qBAAqB,CAAC;aACtC;YAED,yBAAyB;YACzB,sDAAsD;YACtD,0BAA0B;YAC1B,uBAAuB;YACvB,wCAAwC;YACxC,mGAAmG;YACnG,kDAAkD;YAClD,qGAAqG;YACrG,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,CAAA,EAAE;gBAC3B,KAAK,CAAC,KAAK,IAAI,kBAAkB,uBAAc;qBAC5C,MAAM,CACL,CAAC,IAAI,EAAE,EAAE,CACP,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;oBACrB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;oBACnB,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CACnD;qBACA,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,kBAAkB,IAAI,eAAe,IAAI,IAAI,CAAC;qBAC5D,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;aAClB;QACH,CAAC;QAED,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAExB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACzB,IAAI,EAAE,2CAA2C;YACjD,MAAM,EAAE,UAAU,UAAkB;gBAClC,IAAI,CAAC,UAAU,EAAE;oBACf,IAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,OAAO;iBACR;gBAED,MAAM,IAAI,GAAG,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAClD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAQ,CAAC,WAAW,CAC5C,KAAK,CAAC,KAAK,EACX,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,KAAK,CAAC,MAAM,CACnB,CAAC;gBAEF,IAAI,EAAE,CAAC;gBAEP,IAAI,IAAI;oBAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;gBAC/C,IAAI,OAAO;oBAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC;SACF,CAAC,CAAC;QAEH,iEAAiE;QACjE,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,MAAM,WAAW,GACf,WAAG,CAAC,eAAe,IAAI,IAAA,WAAI,EAAC,IAAA,YAAO,GAAE,EAAE,uBAAuB,CAAC,CAAC;YAElE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE;gBACrC,IAAI,CAAC,GAAG;oBAAE,OAAO;gBAEjB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAhSD,gCAgSC;AAED;;GAEG;AACH,MAAa,SAAS;IAYpB,YAAmB,IAAY;QAAZ,SAAI,GAAJ,IAAI,CAAQ;QAX/B,gBAAgB;QAChB,UAAK,GAAG,EAAE,CAAC;QACX,gBAAgB;QAChB,WAAM,GAAG,EAAE,CAAC;QACZ,gBAAgB;QAChB,YAAO,GAAG,CAAC,CAAC;QACZ,gBAAgB;QAChB,UAAK,GAAG,CAAC,CAAC;IAIwB,CAAC;CACpC;AAbD,8BAaC;AAWD,SAAgB,0BAA0B,CACxC,KAAgB,EAChB,WAAkC;IAElC,SAAS,QAAQ,CAAC,IAAY;QAC5B,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC,KAAK,CAAC;QAE5C,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ;YAAE,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE7D,IAAI;YACF,OAAO,IAAA,iBAAY,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACnC;QAAC,OAAO,GAAG,EAAE;YACZ,aAAa;SACd;IACH,CAAC;IACD,SAAS,UAAU,CAAC,IAAY;QAC9B,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAErC,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,UAAU;YAAE,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjE,IAAI;YACF,MAAM,KAAK,GAAG,IAAA,aAAQ,EAAC,IAAI,CAAC,CAAC;YAC7B,OAAO,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;SACzC;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IACD,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AAClC,CAAC;AA5BD,gEA4BC;AAED,MAAM,kBAAkB,GAAG,uCAAuC,CAAC;AAKnE;;;;;GAKG;AACH,SAAS,yBAAyB,CAAC,OAclC;IACC,MAAM,EACJ,OAAO,EACP,KAAK,EACL,UAAU,EACV,mBAAmB,GAAG,KAAK,EAC3B,OAAO,EACP,oBAAoB,GACrB,GAAG,OAAO,CAAC;IACZ,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IAExB,uEAAuE;IACvE,gEAAgE;IAChE,gEAAgE;IAChE,8GAA8G;IAC9G,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC9D,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;QAC9B,UAAU,GAAG,IAAI,CAAC;KACnB;IAED,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC1B,MAAM,YAAY,GAAG,oBAAoB,aAApB,oBAAoB,cAApB,oBAAoB,GAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE,MAAM,IAAI,GAAG,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC7C,IAAI,MAAc,CAAC;IAEnB,8GAA8G;IAC9G,SAAS,eAAe,CAAC,IAAY;QACnC,oEAAoE;QACpE,mEAAmE;QACnE,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;IACjE,CAAC;IAED,IAAI;QACF,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;KAC3D;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,EAAE,CAAC;QAEP,IAAI,UAAU,EAAE;YACd,IAAI,GAAG,YAAY,eAAO,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBAC7D,qFAAqF;gBACrF,MAAM,GAAG,CAAC;aACX;YACD,uBAAuB;YACvB,OAAO,yBAAyB,CAAC;gBAC/B,GAAG,OAAO;gBACV,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC;SACJ;QAED,IAAI,UAAU;YAAE,MAAM,UAAU,CAAC;QACjC,MAAM,GAAG,CAAC;KACX;IAED,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IAEjC,0CAA0C;IAC1C,2EAA2E;IAC3E,6EAA6E;IAC7E,6EAA6E;IAC7E,oCAAoC;IACpC,iFAAiF;IACjF,MAAM,6BAA6B,GAAG,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;IAC7E,MAAM,gCAAgC,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAC3D,kBAAkB,EAClB,EAAE,CACH,CAAC;IAEF,qDAAqD;IACrD,MAAM,OAAO,GAAG,YAAY,EAAE,CAC5B,gCAAgC,EAChC,6BAA6B,CAC9B,CAAC;IAEF,IAAI,YAAY,EAAE;QAChB,IAAI,EAAE,CAAC;KACR;SAAM;QACL,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QAEtB,qFAAqF;QACrF,oDAAoD;QACpD,mGAAmG;QACnG,wFAAwF;QACxF,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAC/B,qBAAqB,EACrB,CAAC,GAAG,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE;YAC5B,IAAI,QAAQ,KAAK,GAAG;gBAAE,OAAO,GAAG,QAAQ,IAAI,UAAU,EAAE,CAAC;YACzD,OAAO,GAAG,CAAC;QACb,CAAC,CACF,CAAC;KACH;IAED,IAAI,QAAQ,GAAwD,EAAE,CAAC;IACvE,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAElC,iFAAiF;IACjF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,IACE,mBAAmB;gBACnB,OAAO,CAAC,eAAe;gBACvB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAClC;gBACA,MAAM,oBAAoB,GAAG,uBAAuB,EAAE,CAAC;gBAEvD,iDAAiD;gBACjD,MAAM,aAAa,GAAG,oBAAoB,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;gBAChE,IAAI,aAAa,KAAK,IAAI,EAAE;oBAC1B,qBAAqB,GAAG,IAAI,CAAC;oBAC7B,QAAQ,CAAC,IAAI,CAAC;wBACZ,SAAS,EAAE,IAAI;wBACf,WAAW,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC;qBACpE,CAAC,CAAC;oBACH,SAAS;iBACV;aACF;YACD,QAAQ,CAAC,IAAI,CAAC;gBACZ,WAAW,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC;aACnE,CAAC,CAAC;SACJ;KACF;IAED,8EAA8E;IAC9E,yBAAyB;IACzB,IAAI,qBAAqB,EAAE;QACzB,OAAO;YACL,qBAAqB;YACrB,YAAY,EAAE,CAAC,KAAK,IAAI,EAAE;gBACxB,IAAI,KAAK,CAAC;gBACV,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;oBAC9B,MAAM,CAAC,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;oBAChC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACzC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,EAAE;SACL,CAAC;KACH;SAAM;QACL,OAAO;YACL,qBAAqB,EAAE,KAAK;YAC5B,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC;SAClE,CAAC;KACH;AACH,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,IAAY,EAAE,QAAgB,EAAE,OAAiB;IACrE,MAAM,MAAM,GAAG,IAAI,WAAM,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;IAE9C,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,MAAM,EAAE;QAC/C,OAAO,MAAM,CAAC,gBAAgB,EAAE,CAAC;KAClC;SAAM;QACL,OAAO,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;KACrC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,KAAgB,EAAE,KAAa;IACxD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;IAC9B,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC;IAClC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;IAChC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;IAE9B,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC;IACrB,KAAK,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;IAChC,KAAK,CAAC,OAAO,EAAE,CAAC;IAEhB,OAAO;QACL,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;QACxB,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC;QAC1B,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC;QAC5B,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAC,KAAa;IAC9B,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,KAAK,EAAE,CAAC;SACT;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,MAAM,cAAc,GAAoC,IAAI,GAAG,CAAC;IAC9D,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ;QACE,IAAI;QACJ,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,gGAAgG;KAClH;IACD,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ,CAAC,IAAI,EAAE,IAAI,CAAC;IACZ;QACE,IAAI;QACJ,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,qFAAqF;KACvG;CACF,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,4BAA4B,GAAG;IACnC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,EAAE,8JAA8J;CACrK,CAAC;AAEF;;GAEG;AACH,SAAS,aAAa,CAAC,KAAc;IACnC,OAAO,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE;QAC1C,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACtC,OAAO,CACL,IAAI,KAAK,IAAI;YACb,CAAC,IAAI,IAAI,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAC/D,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAgB,YAAY,CAC1B,OAAY,EACZ,MAAc,EACd,kBAA2C;IAE3C,IAAI,kBAAkB,EAAE;QACtB,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC;QACxB,OAAO,CAAC,UAAU,GAAG,IAAI,kBAAkB,GAAG,CAAC;KAChD;IACD,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IACjC,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChD,CAAC;AAZD,oCAYC", "sourcesContent": ["import type * as _diff from 'diff';\nimport { homedir } from 'os';\nimport { join } from 'path';\nimport {\n  Recoverable,\n  ReplOptions,\n  REPLServer,\n  start as nodeReplStart,\n} from 'repl';\nimport { Context, createContext, Script } from 'vm';\nimport { Service, CreateOptions, TSError, env } from './index';\nimport { readFileSync, statSync } from 'fs';\nimport { Console } from 'console';\nimport * as assert from 'assert';\nimport type * as tty from 'tty';\nimport type * as Module from 'module';\nimport { builtinModules } from 'module';\n\n// Lazy-loaded.\nlet _processTopLevelAwait: (src: string) => string | null;\nfunction getProcessTopLevelAwait() {\n  if (_processTopLevelAwait === undefined) {\n    ({\n      processTopLevelAwait: _processTopLevelAwait,\n    } = require('../dist-raw/node-internal-repl-await'));\n  }\n  return _processTopLevelAwait;\n}\nlet diff: typeof _diff;\nfunction getDiffLines() {\n  if (diff === undefined) {\n    diff = require('diff');\n  }\n  return diff.diffLines;\n}\n\n/** @internal */\nexport const EVAL_FILENAME = `[eval].ts`;\n/** @internal */\nexport const EVAL_NAME = `[eval]`;\n/** @internal */\nexport const STDIN_FILENAME = `[stdin].ts`;\n/** @internal */\nexport const STDIN_NAME = `[stdin]`;\n/** @internal */\nexport const REPL_FILENAME = '<repl>.ts';\n/** @internal */\nexport const REPL_NAME = '<repl>';\n\nexport interface ReplService {\n  readonly state: EvalState;\n  /**\n   * Bind this REPL to a ts-node compiler service.  A compiler service must be bound before `eval`-ing code or starting the REPL\n   */\n  setService(service: Service): void;\n  /**\n   * Append code to the virtual <repl> source file, compile it to JavaScript, throw semantic errors if the typechecker is enabled,\n   * and execute it.\n   *\n   * Note: typically, you will want to call `start()` instead of using this method.\n   *\n   * @param code string of TypeScript.\n   */\n  evalCode(code: string): any;\n  /** @internal */\n  evalCodeInternal(opts: {\n    code: string;\n    enableTopLevelAwait?: boolean;\n    context?: Context;\n  }):\n    | {\n        containsTopLevelAwait: true;\n        valuePromise: Promise<any>;\n      }\n    | {\n        containsTopLevelAwait: false;\n        value: any;\n      };\n  /**\n   * `eval` implementation compatible with node's REPL API\n   *\n   * Can be used in advanced scenarios if you want to manually create your own\n   * node REPL instance and delegate eval to this `ReplService`.\n   *\n   * Example:\n   *\n   *     import {start} from 'repl';\n   *     const replService: tsNode.ReplService = ...; // assuming you have already created a ts-node ReplService\n   *     const nodeRepl = start({eval: replService.eval});\n   */\n  nodeEval(\n    code: string,\n    // TODO change to `Context` in a future release?  Technically a breaking change\n    context: any,\n    _filename: string,\n    callback: (err: Error | null, result?: any) => any\n  ): void;\n  evalAwarePartialHost: EvalAwarePartialHost;\n  /** Start a node REPL */\n  start(): void;\n  /**\n   * Start a node REPL, evaling a string of TypeScript before it starts.\n   * @deprecated\n   */\n  start(code: string): void;\n  /** @internal */\n  startInternal(opts?: ReplOptions): REPLServer;\n  /** @internal */\n  readonly stdin: NodeJS.ReadableStream;\n  /** @internal */\n  readonly stdout: NodeJS.WritableStream;\n  /** @internal */\n  readonly stderr: NodeJS.WritableStream;\n  /** @internal */\n  readonly console: Console;\n}\n\n/** @category REPL */\nexport interface CreateReplOptions {\n  service?: Service;\n  state?: EvalState;\n  stdin?: NodeJS.ReadableStream;\n  stdout?: NodeJS.WritableStream;\n  stderr?: NodeJS.WritableStream;\n  /** @internal */\n  composeWithEvalAwarePartialHost?: EvalAwarePartialHost;\n  /**\n   * @internal\n   * Ignore diagnostics that are annoying when interactively entering input line-by-line.\n   */\n  ignoreDiagnosticsThatAreAnnoyingInInteractiveRepl?: boolean;\n}\n\n/**\n * Create a ts-node REPL instance.\n *\n * Pay close attention to the example below.  Today, the API requires a few lines\n * of boilerplate to correctly bind the `ReplService` to the ts-node `Service` and\n * vice-versa.\n *\n * Usage example:\n *\n *     const repl = tsNode.createRepl();\n *     const service = tsNode.create({...repl.evalAwarePartialHost});\n *     repl.setService(service);\n *     repl.start();\n *\n * @category REPL\n */\nexport function createRepl(options: CreateReplOptions = {}) {\n  const { ignoreDiagnosticsThatAreAnnoyingInInteractiveRepl = true } = options;\n  let service = options.service;\n  let nodeReplServer: REPLServer;\n  // If `useGlobal` is not true, then REPL creates a context when started.\n  // This stores a reference to it or to `global`, whichever is used, after REPL has started.\n  let context: Context | undefined;\n  const state =\n    options.state ?? new EvalState(join(process.cwd(), REPL_FILENAME));\n  const evalAwarePartialHost = createEvalAwarePartialHost(\n    state,\n    options.composeWithEvalAwarePartialHost\n  );\n  const stdin = options.stdin ?? process.stdin;\n  const stdout = options.stdout ?? process.stdout;\n  const stderr = options.stderr ?? process.stderr;\n  const _console =\n    stdout === process.stdout && stderr === process.stderr\n      ? console\n      : new Console(stdout, stderr);\n\n  const replService: ReplService = {\n    state: options.state ?? new EvalState(join(process.cwd(), EVAL_FILENAME)),\n    setService,\n    evalCode,\n    evalCodeInternal,\n    nodeEval,\n    evalAwarePartialHost,\n    start,\n    startInternal,\n    stdin,\n    stdout,\n    stderr,\n    console: _console,\n  };\n\n  return replService;\n\n  function setService(_service: Service) {\n    service = _service;\n    if (ignoreDiagnosticsThatAreAnnoyingInInteractiveRepl) {\n      service.addDiagnosticFilter({\n        appliesToAllFiles: false,\n        filenamesAbsolute: [state.path],\n        diagnosticsIgnored: [\n          2393, // Duplicate function implementation: https://github.com/TypeStrong/ts-node/issues/729\n          6133, // <identifier> is declared but its value is never read. https://github.com/TypeStrong/ts-node/issues/850\n          7027, // Unreachable code detected. https://github.com/TypeStrong/ts-node/issues/469\n          ...(service.shouldReplAwait ? topLevelAwaitDiagnosticCodes : []),\n        ],\n      });\n    }\n  }\n\n  function evalCode(code: string) {\n    const result = appendCompileAndEvalInput({\n      service: service!,\n      state,\n      input: code,\n      context,\n      overrideIsCompletion: false,\n    });\n    assert(result.containsTopLevelAwait === false);\n    return result.value;\n  }\n\n  function evalCodeInternal(options: {\n    code: string;\n    enableTopLevelAwait?: boolean;\n    context: Context;\n  }) {\n    const { code, enableTopLevelAwait, context } = options;\n    return appendCompileAndEvalInput({\n      service: service!,\n      state,\n      input: code,\n      enableTopLevelAwait,\n      context,\n    });\n  }\n\n  function nodeEval(\n    code: string,\n    context: any,\n    _filename: string,\n    callback: (err: Error | null, result?: any) => any\n  ) {\n    // TODO: Figure out how to handle completion here.\n    if (code === '.scope') {\n      callback(null);\n      return;\n    }\n\n    try {\n      const evalResult = evalCodeInternal({\n        code,\n        enableTopLevelAwait: true,\n        context,\n      });\n\n      if (evalResult.containsTopLevelAwait) {\n        (async () => {\n          try {\n            callback(null, await evalResult.valuePromise);\n          } catch (promiseError) {\n            handleError(promiseError);\n          }\n        })();\n      } else {\n        callback(null, evalResult.value);\n      }\n    } catch (error) {\n      handleError(error);\n    }\n\n    // Log TSErrors, check if they're recoverable, log helpful hints for certain\n    // well-known errors, and invoke `callback()`\n    // TODO should evalCode API get the same error-handling benefits?\n    function handleError(error: unknown) {\n      // Don't show TLA hint if the user explicitly disabled repl top level await\n      const canLogTopLevelAwaitHint =\n        service!.options.experimentalReplAwait !== false &&\n        !service!.shouldReplAwait;\n      if (error instanceof TSError) {\n        // Support recoverable compilations using >= node 6.\n        if (Recoverable && isRecoverable(error)) {\n          callback(new Recoverable(error));\n          return;\n        } else {\n          _console.error(error);\n\n          if (\n            canLogTopLevelAwaitHint &&\n            error.diagnosticCodes.some((dC) =>\n              topLevelAwaitDiagnosticCodes.includes(dC)\n            )\n          ) {\n            _console.error(getTopLevelAwaitHint());\n          }\n          callback(null);\n        }\n      } else {\n        let _error = error as Error | undefined;\n        if (\n          canLogTopLevelAwaitHint &&\n          _error instanceof SyntaxError &&\n          _error.message?.includes('await is only valid')\n        ) {\n          try {\n            // Only way I know to make our hint appear after the error\n            _error.message += `\\n\\n${getTopLevelAwaitHint()}`;\n            _error.stack = _error.stack?.replace(\n              /(SyntaxError:.*)/,\n              (_, $1) => `${$1}\\n\\n${getTopLevelAwaitHint()}`\n            );\n          } catch {}\n        }\n        callback(_error as Error);\n      }\n    }\n    function getTopLevelAwaitHint() {\n      return `Hint: REPL top-level await requires TypeScript version 3.8 or higher and target ES2018 or higher. You are using TypeScript ${\n        service!.ts.version\n      } and target ${\n        service!.ts.ScriptTarget[service!.config.options.target!]\n      }.`;\n    }\n  }\n\n  // Note: `code` argument is deprecated\n  function start(code?: string) {\n    startInternal({ code });\n  }\n\n  // Note: `code` argument is deprecated\n  function startInternal(\n    options?: ReplOptions & { code?: string; forceToBeModule?: boolean }\n  ) {\n    const { code, forceToBeModule = true, ...optionsOverride } = options ?? {};\n    // TODO assert that `service` is set; remove all `service!` non-null assertions\n\n    // Eval incoming code before the REPL starts.\n    // Note: deprecated\n    if (code) {\n      try {\n        evalCode(`${code}\\n`);\n      } catch (err) {\n        _console.error(err);\n        // Note: should not be killing the process here, but this codepath is deprecated anyway\n        process.exit(1);\n      }\n    }\n\n    // In case the typescript compiler hasn't compiled anything yet,\n    // make it run though compilation at least one time before\n    // the REPL starts for a snappier user experience on startup.\n    service?.compile('', state.path);\n\n    const repl = nodeReplStart({\n      prompt: '> ',\n      input: replService.stdin,\n      output: replService.stdout,\n      // Mimicking node's REPL implementation: https://github.com/nodejs/node/blob/168b22ba073ee1cbf8d0bcb4ded7ff3099335d04/lib/internal/repl.js#L28-L30\n      terminal:\n        (stdout as tty.WriteStream).isTTY &&\n        !parseInt(env.NODE_NO_READLINE!, 10),\n      eval: nodeEval,\n      useGlobal: true,\n      ...optionsOverride,\n    });\n\n    nodeReplServer = repl;\n    context = repl.context;\n\n    // Bookmark the point where we should reset the REPL state.\n    const resetEval = appendToEvalState(state, '');\n\n    function reset() {\n      resetEval();\n\n      // Hard fix for TypeScript forcing `Object.defineProperty(exports, ...)`.\n      runInContext('exports = module.exports', state.path, context);\n      if (forceToBeModule) {\n        state.input += 'export {};void 0;\\n';\n      }\n\n      // Declare node builtins.\n      // Skip the same builtins as `addBuiltinLibsToObject`:\n      //   those starting with _\n      //   those containing /\n      //   those that already exist as globals\n      // Intentionally suppress type errors in case @types/node does not declare any of them, and because\n      // `declare import` is technically invalid syntax.\n      // Avoid this when in transpileOnly, because third-party transpilers may not handle `declare import`.\n      if (!service?.transpileOnly) {\n        state.input += `// @ts-ignore\\n${builtinModules\n          .filter(\n            (name) =>\n              !name.startsWith('_') &&\n              !name.includes('/') &&\n              !['console', 'module', 'process'].includes(name)\n          )\n          .map((name) => `declare import ${name} = require('${name}')`)\n          .join(';')}\\n`;\n      }\n    }\n\n    reset();\n    repl.on('reset', reset);\n\n    repl.defineCommand('type', {\n      help: 'Check the type of a TypeScript identifier',\n      action: function (identifier: string) {\n        if (!identifier) {\n          repl.displayPrompt();\n          return;\n        }\n\n        const undo = appendToEvalState(state, identifier);\n        const { name, comment } = service!.getTypeInfo(\n          state.input,\n          state.path,\n          state.input.length\n        );\n\n        undo();\n\n        if (name) repl.outputStream.write(`${name}\\n`);\n        if (comment) repl.outputStream.write(`${comment}\\n`);\n        repl.displayPrompt();\n      },\n    });\n\n    // Set up REPL history when available natively via node.js >= 11.\n    if (repl.setupHistory) {\n      const historyPath =\n        env.TS_NODE_HISTORY || join(homedir(), '.ts_node_repl_history');\n\n      repl.setupHistory(historyPath, (err) => {\n        if (!err) return;\n\n        _console.error(err);\n        process.exit(1);\n      });\n    }\n\n    return repl;\n  }\n}\n\n/**\n * Eval state management. Stores virtual `[eval].ts` file\n */\nexport class EvalState {\n  /** @internal */\n  input = '';\n  /** @internal */\n  output = '';\n  /** @internal */\n  version = 0;\n  /** @internal */\n  lines = 0;\n\n  __tsNodeEvalStateBrand: unknown;\n\n  constructor(public path: string) {}\n}\n\n/**\n * Filesystem host functions which are aware of the \"virtual\" `[eval].ts`, `<repl>`, or `[stdin].ts` file used to compile REPL inputs.\n * Must be passed to `create()` to create a ts-node compiler service which can compile REPL inputs.\n */\nexport type EvalAwarePartialHost = Pick<\n  CreateOptions,\n  'readFile' | 'fileExists'\n>;\n\nexport function createEvalAwarePartialHost(\n  state: EvalState,\n  composeWith?: EvalAwarePartialHost\n): EvalAwarePartialHost {\n  function readFile(path: string) {\n    if (path === state.path) return state.input;\n\n    if (composeWith?.readFile) return composeWith.readFile(path);\n\n    try {\n      return readFileSync(path, 'utf8');\n    } catch (err) {\n      /* Ignore. */\n    }\n  }\n  function fileExists(path: string) {\n    if (path === state.path) return true;\n\n    if (composeWith?.fileExists) return composeWith.fileExists(path);\n\n    try {\n      const stats = statSync(path);\n      return stats.isFile() || stats.isFIFO();\n    } catch (err) {\n      return false;\n    }\n  }\n  return { readFile, fileExists };\n}\n\nconst sourcemapCommentRe = /\\/\\/# ?sourceMappingURL=\\S+[\\s\\r\\n]*$/;\n\ntype AppendCompileAndEvalInputResult =\n  | { containsTopLevelAwait: true; valuePromise: Promise<any> }\n  | { containsTopLevelAwait: false; value: any };\n/**\n * Evaluate the code snippet.\n *\n * Append it to virtual .ts file, compile, handle compiler errors, compute a diff of the JS, and eval any code that\n * appears as \"added\" in the diff.\n */\nfunction appendCompileAndEvalInput(options: {\n  service: Service;\n  state: EvalState;\n  input: string;\n  wrappedErr?: unknown;\n  /** Enable top-level await but only if the TSNode service allows it. */\n  enableTopLevelAwait?: boolean;\n  context: Context | undefined;\n  /**\n   * Added so that `evalCode` can be guaranteed *not* to trigger the `isCompletion`\n   * codepath.  However, the `isCompletion` logic is ancient and maybe should be removed entirely.\n   * Nobody's looked at it in a long time.\n   */\n  overrideIsCompletion?: boolean;\n}): AppendCompileAndEvalInputResult {\n  const {\n    service,\n    state,\n    wrappedErr,\n    enableTopLevelAwait = false,\n    context,\n    overrideIsCompletion,\n  } = options;\n  let { input } = options;\n\n  // It's confusing for `{ a: 1 }` to be interpreted as a block statement\n  // rather than an object literal. So, we first try to wrap it in\n  // parentheses, so that it will be interpreted as an expression.\n  // Based on https://github.com/nodejs/node/blob/c2e6822153bad023ab7ebd30a6117dcc049e475c/lib/repl.js#L413-L422\n  let wrappedCmd = false;\n  if (!wrappedErr && /^\\s*{/.test(input) && !/;\\s*$/.test(input)) {\n    input = `(${input.trim()})\\n`;\n    wrappedCmd = true;\n  }\n\n  const lines = state.lines;\n  const isCompletion = overrideIsCompletion ?? !/\\n$/.test(input);\n  const undo = appendToEvalState(state, input);\n  let output: string;\n\n  // Based on https://github.com/nodejs/node/blob/92573721c7cff104ccb82b6ed3e8aa69c4b27510/lib/repl.js#L457-L461\n  function adjustUseStrict(code: string) {\n    // \"void 0\" keeps the repl from returning \"use strict\" as the result\n    // value for statements and declarations that don't return a value.\n    return code.replace(/^\"use strict\";/, '\"use strict\"; void 0;');\n  }\n\n  try {\n    output = service.compile(state.input, state.path, -lines);\n  } catch (err) {\n    undo();\n\n    if (wrappedCmd) {\n      if (err instanceof TSError && err.diagnosticCodes[0] === 2339) {\n        // Ensure consistent and more sane behavior between { a: 1 }['b'] and ({ a: 1 }['b'])\n        throw err;\n      }\n      // Unwrap and try again\n      return appendCompileAndEvalInput({\n        ...options,\n        wrappedErr: err,\n      });\n    }\n\n    if (wrappedErr) throw wrappedErr;\n    throw err;\n  }\n\n  output = adjustUseStrict(output);\n\n  // Note: REPL does not respect sourcemaps!\n  // To properly do that, we'd need to prefix the code we eval -- which comes\n  // from `diffLines` -- with newlines so that it's at the proper line numbers.\n  // Then we'd need to ensure each bit of eval-ed code, if there are multiples,\n  // has the sourcemap appended to it.\n  // We might also need to integrate with our sourcemap hooks' cache; I'm not sure.\n  const outputWithoutSourcemapComment = output.replace(sourcemapCommentRe, '');\n  const oldOutputWithoutSourcemapComment = state.output.replace(\n    sourcemapCommentRe,\n    ''\n  );\n\n  // Use `diff` to check for new JavaScript to execute.\n  const changes = getDiffLines()(\n    oldOutputWithoutSourcemapComment,\n    outputWithoutSourcemapComment\n  );\n\n  if (isCompletion) {\n    undo();\n  } else {\n    state.output = output;\n\n    // Insert a semicolon to make sure that the code doesn't interact with the next line,\n    // for example to prevent `2\\n+ 2` from producing 4.\n    // This is safe since the output will not change since we can only get here with successful inputs,\n    // and adding a semicolon to the end of a successful input won't ever change the output.\n    state.input = state.input.replace(\n      /([^\\n\\s])([\\n\\s]*)$/,\n      (all, lastChar, whitespace) => {\n        if (lastChar !== ';') return `${lastChar};${whitespace}`;\n        return all;\n      }\n    );\n  }\n\n  let commands: Array<{ mustAwait?: true; execCommand: () => any }> = [];\n  let containsTopLevelAwait = false;\n\n  // Build a list of \"commands\": bits of JS code in the diff that must be executed.\n  for (const change of changes) {\n    if (change.added) {\n      if (\n        enableTopLevelAwait &&\n        service.shouldReplAwait &&\n        change.value.indexOf('await') > -1\n      ) {\n        const processTopLevelAwait = getProcessTopLevelAwait();\n\n        // Newline prevents comments to mess with wrapper\n        const wrappedResult = processTopLevelAwait(change.value + '\\n');\n        if (wrappedResult !== null) {\n          containsTopLevelAwait = true;\n          commands.push({\n            mustAwait: true,\n            execCommand: () => runInContext(wrappedResult, state.path, context),\n          });\n          continue;\n        }\n      }\n      commands.push({\n        execCommand: () => runInContext(change.value, state.path, context),\n      });\n    }\n  }\n\n  // Execute all commands asynchronously if necessary, returning the result or a\n  // promise of the result.\n  if (containsTopLevelAwait) {\n    return {\n      containsTopLevelAwait,\n      valuePromise: (async () => {\n        let value;\n        for (const command of commands) {\n          const r = command.execCommand();\n          value = command.mustAwait ? await r : r;\n        }\n        return value;\n      })(),\n    };\n  } else {\n    return {\n      containsTopLevelAwait: false,\n      value: commands.reduce<any>((_, c) => c.execCommand(), undefined),\n    };\n  }\n}\n\n/**\n * Low-level execution of JS code in context\n */\nfunction runInContext(code: string, filename: string, context?: Context) {\n  const script = new Script(code, { filename });\n\n  if (context === undefined || context === global) {\n    return script.runInThisContext();\n  } else {\n    return script.runInContext(context);\n  }\n}\n\n/**\n * Append to the eval instance and return an undo function.\n */\nfunction appendToEvalState(state: EvalState, input: string) {\n  const undoInput = state.input;\n  const undoVersion = state.version;\n  const undoOutput = state.output;\n  const undoLines = state.lines;\n\n  state.input += input;\n  state.lines += lineCount(input);\n  state.version++;\n\n  return function () {\n    state.input = undoInput;\n    state.output = undoOutput;\n    state.version = undoVersion;\n    state.lines = undoLines;\n  };\n}\n\n/**\n * Count the number of lines.\n */\nfunction lineCount(value: string) {\n  let count = 0;\n\n  for (const char of value) {\n    if (char === '\\n') {\n      count++;\n    }\n  }\n\n  return count;\n}\n\n/**\n * TS diagnostic codes which are recoverable, meaning that the user likely entered an incomplete line of code\n * and should be prompted for the next.  For example, starting a multi-line for() loop and not finishing it.\n * null value means code is always recoverable.  `Set` means code is only recoverable when occurring alongside at least one\n * of the other codes.\n */\nconst RECOVERY_CODES: Map<number, Set<number> | null> = new Map([\n  [1003, null], // \"Identifier expected.\"\n  [1005, null], // \"')' expected.\", \"'}' expected.\"\n  [1109, null], // \"Expression expected.\"\n  [1126, null], // \"Unexpected end of text.\"\n  [\n    1136, // \"Property assignment expected.\"\n    new Set([1005]), // happens when typing out an object literal or block scope across multiple lines: '{ foo: 123,'\n  ],\n  [1160, null], // \"Unterminated template literal.\"\n  [1161, null], // \"Unterminated regular expression literal.\"\n  [2355, null], // \"A function whose declared type is neither 'void' nor 'any' must return a value.\"\n  [2391, null], // \"Function implementation is missing or not immediately following the declaration.\"\n  [\n    7010, // \"Function, which lacks return-type annotation, implicitly has an 'any' return type.\"\n    new Set([1005]), // happens when fn signature spread across multiple lines: 'function a(\\nb: any\\n) {'\n  ],\n]);\n\n/**\n * Diagnostic codes raised when using top-level await.\n * These are suppressed when top-level await is enabled.\n * When it is *not* enabled, these trigger a helpful hint about enabling top-level await.\n */\nconst topLevelAwaitDiagnosticCodes = [\n  1375, // 'await' expressions are only allowed at the top level of a file when that file is a module, but this file has no imports or exports. Consider adding an empty 'export {}' to make this file a module.\n  1378, // Top-level 'await' expressions are only allowed when the 'module' option is set to 'esnext' or 'system', and the 'target' option is set to 'es2017' or higher.\n  1431, // 'for await' loops are only allowed at the top level of a file when that file is a module, but this file has no imports or exports. Consider adding an empty 'export {}' to make this file a module.\n  1432, // Top-level 'for await' loops are only allowed when the 'module' option is set to 'esnext' or 'system', and the 'target' option is set to 'es2017' or higher.\n];\n\n/**\n * Check if a function can recover gracefully.\n */\nfunction isRecoverable(error: TSError) {\n  return error.diagnosticCodes.every((code) => {\n    const deps = RECOVERY_CODES.get(code);\n    return (\n      deps === null ||\n      (deps && error.diagnosticCodes.some((code) => deps.has(code)))\n    );\n  });\n}\n\n/**\n * @internal\n * Set properties on `context` before eval-ing [stdin] or [eval] input.\n */\nexport function setupContext(\n  context: any,\n  module: Module,\n  filenameAndDirname: 'eval' | 'stdin' | null\n) {\n  if (filenameAndDirname) {\n    context.__dirname = '.';\n    context.__filename = `[${filenameAndDirname}]`;\n  }\n  context.module = module;\n  context.exports = module.exports;\n  context.require = module.require.bind(module);\n}\n"]}