@echo off
chcp 65001 >nul
title WS Transfir - Complete Online System

:: WS Transfir Complete Online System Launcher
:: مشغل نظام WS Transfir الأونلاين الشامل

color 0A
cls

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🚀 WS TRANSFIR - COMPLETE ONLINE SYSTEM 🚀             ██
echo ██                                                            ██
echo ██    النظام الشامل للتحويلات المالية الأونلاين              ██
echo ██    Complete Online Money Transfer System                   ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:: Get current time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%DD%/%MM%/%YYYY% %HH%:%Min%:%Sec%"

echo 📅 التاريخ والوقت: %timestamp%
echo 🌍 الوضع: Complete Online Production
echo 📡 الوصول: Full External Access
echo 🔧 الخدمات: Backend + Frontend + API
echo.

:: Phase 1: System Preparation
echo ═══════════════════════════════════════════════════════════════
echo 🔧 المرحلة 1: تحضير النظام الشامل
echo ═══════════════════════════════════════════════════════════════
echo.

:: Kill any existing processes
echo 🛑 إيقاف العمليات السابقة...
taskkill /F /IM node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

:: Check Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL: Node.js غير مثبت
    echo 📥 تحميل من: https://nodejs.org
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js: %NODE_VERSION%

:: Check required files
echo 🔍 فحص الملفات المطلوبة...
set "missing_files="

if not exist "online-system-server.js" (
    set "missing_files=%missing_files% online-system-server.js"
)
if not exist "online-frontend-server.js" (
    set "missing_files=%missing_files% online-frontend-server.js"
)
if not exist "online-frontend.html" (
    set "missing_files=%missing_files% online-frontend.html"
)
if not exist "package.json" (
    set "missing_files=%missing_files% package.json"
)

if not "%missing_files%"=="" (
    echo ❌ CRITICAL: ملفات مفقودة:%missing_files%
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة

:: Install dependencies if needed
echo 📦 فحص المكتبات...
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    npm install express cors helmet compression express-rate-limit morgan --silent
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات
) else (
    echo ✅ المكتبات مثبتة مسبقاً
)

echo.

:: Phase 2: Port Management
echo ═══════════════════════════════════════════════════════════════
echo 🌐 المرحلة 2: إدارة المنافذ
echo ═══════════════════════════════════════════════════════════════
echo.

:: Check and free ports
echo 🔍 فحص المنفذ 3000 (Backend API)...
netstat -an | findstr :3000 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ المنفذ 3000 مستخدم - تحرير المنفذ...
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do taskkill /F /PID %%a >nul 2>&1
    timeout /t 1 /nobreak >nul
)

echo 🔍 فحص المنفذ 3100 (Frontend)...
netstat -an | findstr :3100 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ المنفذ 3100 مستخدم - تحرير المنفذ...
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3100') do taskkill /F /PID %%a >nul 2>&1
    timeout /t 1 /nobreak >nul
)

echo ✅ المنافذ متاحة

:: Get local IP
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4" ^| findstr /v "127.0.0.1"') do (
    for /f "tokens=1" %%j in ("%%i") do set LOCAL_IP=%%j
)

if not defined LOCAL_IP set LOCAL_IP=localhost

echo 🌐 عنوان IP المحلي: %LOCAL_IP%
echo.

:: Phase 3: Launch Backend Server
echo ═══════════════════════════════════════════════════════════════
echo 🔧 المرحلة 3: تشغيل خادم Backend
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🚀 تشغيل Backend Server (Port 3000)...
start "WS Transfir Backend" cmd /k "echo 🔧 WS Transfir Backend Server && echo ========================== && node online-system-server.js"

:: Wait for backend to start
echo ⏳ انتظار تشغيل Backend...
timeout /t 5 /nobreak >nul

:: Test backend
echo 🧪 اختبار Backend...
curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Backend قد يحتاج وقت إضافي
) else (
    echo ✅ Backend يعمل بنجاح
)

echo.

:: Phase 4: Launch Frontend Server
echo ═══════════════════════════════════════════════════════════════
echo 🎨 المرحلة 4: تشغيل خادم Frontend
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🚀 تشغيل Frontend Server (Port 3100)...
start "WS Transfir Frontend" cmd /k "echo 🎨 WS Transfir Frontend Server && echo =========================== && node online-frontend-server.js"

:: Wait for frontend to start
echo ⏳ انتظار تشغيل Frontend...
timeout /t 5 /nobreak >nul

:: Test frontend
echo 🧪 اختبار Frontend...
curl -s http://localhost:3100/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Frontend قد يحتاج وقت إضافي
) else (
    echo ✅ Frontend يعمل بنجاح
)

echo.

:: Phase 5: System Verification
echo ═══════════════════════════════════════════════════════════════
echo ✅ المرحلة 5: التحقق من النظام
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🔍 فحص شامل للنظام...

:: Check backend health
echo 🔧 فحص Backend Health...
curl -s http://localhost:3000/api/health | findstr "OK" >nul 2>&1
if errorlevel 1 (
    echo ❌ Backend غير متاح
    set BACKEND_STATUS=❌ غير متاح
) else (
    echo ✅ Backend متاح
    set BACKEND_STATUS=✅ متاح
)

:: Check frontend health
echo 🎨 فحص Frontend Health...
curl -s http://localhost:3100/health | findstr "OK" >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend غير متاح
    set FRONTEND_STATUS=❌ غير متاح
) else (
    echo ✅ Frontend متاح
    set FRONTEND_STATUS=✅ متاح
)

:: Test API endpoints
echo 🧪 اختبار API Endpoints...
curl -s http://localhost:3000/api/status >nul 2>&1
if errorlevel 1 (
    set API_STATUS=❌ غير متاح
) else (
    set API_STATUS=✅ متاح
)

echo.

:: Phase 6: Launch Browser
echo ═══════════════════════════════════════════════════════════════
echo 🌐 المرحلة 6: فتح المتصفح
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🌐 فتح النظام في المتصفح...
start "" "http://localhost:3100"
timeout /t 2 /nobreak >nul

:: Also open API health check
start "" "http://localhost:3000/api/health"
timeout /t 1 /nobreak >nul

echo.

:: Final Status Display
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🎉 WS TRANSFIR ONLINE SYSTEM IS FULLY OPERATIONAL!     ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 📊 حالة النظام الشامل:
echo ========================
echo 🔧 Backend Server:     %BACKEND_STATUS%
echo 🎨 Frontend Server:    %FRONTEND_STATUS%
echo 🌐 API Endpoints:      %API_STATUS%
echo 📡 External Access:    ✅ مفعل
echo 🛡️ Security:          ✅ مفعل
echo 📈 Monitoring:        ✅ مفعل
echo.

echo 🌍 روابط الوصول:
echo ==================
echo 🎨 الواجهة الرئيسية:    http://localhost:3100
echo 🔧 Backend API:         http://localhost:3000
echo 📊 فحص الصحة:          http://localhost:3000/api/health
echo 📈 حالة النظام:         http://localhost:3000/api/status
echo.

if not "%LOCAL_IP%"=="localhost" (
    echo 🌐 الوصول الخارجي:
    echo ===================
    echo 🎨 الواجهة الرئيسية:    http://%LOCAL_IP%:3100
    echo 🔧 Backend API:         http://%LOCAL_IP%:3000
    echo 📊 فحص الصحة:          http://%LOCAL_IP%:3000/api/health
    echo.
)

echo 🔐 بيانات الدخول التجريبية:
echo ============================
echo 👨‍💼 مدير النظام:        <EMAIL> / admin123
echo 👤 مستخدم عادي:        <EMAIL> / password123
echo.

echo 📋 الميزات المتاحة:
echo ====================
echo ✅ نظام مصادقة متقدم
echo ✅ إدارة التحويلات المالية
echo ✅ لوحة تحكم تفاعلية
echo ✅ إحصائيات وتقارير
echo ✅ واجهة مستخدم حديثة
echo ✅ API متكامل
echo ✅ أمان متعدد الطبقات
echo ✅ مراقبة في الوقت الفعلي
echo.

echo 💡 نصائح الاستخدام:
echo ==================
echo 🔹 النظام يعمل في وضع الإنتاج الكامل
echo 🔹 يمكن الوصول من أجهزة أخرى في الشبكة
echo 🔹 جميع الميزات الأمنية مفعلة
echo 🔹 المراقبة والتسجيل مفعلان
echo 🔹 لإيقاف النظام: أغلق نوافذ الخوادم
echo.

echo 🔧 إدارة النظام:
echo =================
echo 🔹 لإعادة التشغيل: شغل هذا الملف مرة أخرى
echo 🔹 لفحص الحالة: زر http://localhost:3000/api/health
echo 🔹 لمراقبة الأداء: راقب نوافذ الخوادم
echo 🔹 للدعم الفني: <EMAIL>
echo.

echo 📞 معلومات الاتصال:
echo ====================
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📱 الهاتف: +966 11 123 4567
echo 🌐 الموقع: https://wstransfir.com
echo 📚 التوثيق: https://docs.wstransfir.com
echo.

echo 🎉 النظام الأونلاين الشامل جاهز للاستخدام!
echo.
echo اضغط أي مفتاح للاستمرار أو أغلق النافذة...
pause >nul
