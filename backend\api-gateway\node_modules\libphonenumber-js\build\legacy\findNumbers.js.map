{"version": 3, "file": "findNumbers.js", "names": ["findNumbers", "normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "PhoneNumberMatcher", "results", "hasNext", "push", "next"], "sources": ["../../source/legacy/findNumbers.js"], "sourcesContent": ["import PhoneNumberMatcher from '../PhoneNumberMatcher.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findNumbers() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, options, metadata)\r\n\tconst results = []\r\n\twhile (matcher.hasNext()) {\r\n\t\tresults.push(matcher.next())\r\n\t}\r\n\treturn results\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEe,SAASA,WAAT,GAAuB;EACrC,0BAAoC,IAAAC,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,IAAMC,OAAO,GAAG,IAAIC,8BAAJ,CAAuBJ,IAAvB,EAA6BC,OAA7B,EAAsCC,QAAtC,CAAhB;EACA,IAAMG,OAAO,GAAG,EAAhB;;EACA,OAAOF,OAAO,CAACG,OAAR,EAAP,EAA0B;IACzBD,OAAO,CAACE,IAAR,CAAaJ,OAAO,CAACK,IAAR,EAAb;EACA;;EACD,OAAOH,OAAP;AACA"}