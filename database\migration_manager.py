"""
Database Migration Manager
=========================
مدير مايجريشن قاعدة البيانات
"""

import os
import re
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pathlib import Path

import asyncpg
from asyncpg import Connection

logger = logging.getLogger(__name__)


class Migration:
    """فئة المايجريشن"""
    
    def __init__(self, version: str, name: str, file_path: str, content: str):
        self.version = version
        self.name = name
        self.file_path = file_path
        self.content = content
        self.executed_at: Optional[datetime] = None
        self.execution_time_ms: Optional[int] = None
    
    def __str__(self):
        return f"Migration {self.version}: {self.name}"
    
    def __repr__(self):
        return f"Migration(version='{self.version}', name='{self.name}')"


class MigrationManager:
    """مدير المايجريشن"""
    
    def __init__(self, database_url: str, migrations_dir: str = "database/migrations"):
        self.database_url = database_url
        self.migrations_dir = Path(migrations_dir)
        self.connection: Optional[Connection] = None
        
        # Statistics
        self.total_migrations = 0
        self.executed_migrations = 0
        self.failed_migrations = 0
    
    async def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = await asyncpg.connect(self.database_url)
            logger.info("✅ Connected to database for migrations")
            
            # Create migrations table if it doesn't exist
            await self._create_migrations_table()
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            raise
    
    async def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            await self.connection.close()
            logger.info("✅ Disconnected from database")
    
    async def _create_migrations_table(self):
        """إنشاء جدول المايجريشن"""
        try:
            await self.connection.execute("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    version VARCHAR(10) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    file_path VARCHAR(500) NOT NULL,
                    checksum VARCHAR(64) NOT NULL,
                    executed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    execution_time_ms INTEGER NOT NULL,
                    success BOOLEAN NOT NULL DEFAULT true,
                    error_message TEXT,
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_schema_migrations_executed_at 
                ON schema_migrations(executed_at);
                
                CREATE INDEX IF NOT EXISTS idx_schema_migrations_success 
                ON schema_migrations(success);
            """)
            
            logger.info("✅ Schema migrations table ready")
            
        except Exception as e:
            logger.error(f"❌ Failed to create migrations table: {e}")
            raise
    
    def _get_migration_files(self) -> List[Path]:
        """الحصول على ملفات المايجريشن"""
        try:
            if not self.migrations_dir.exists():
                logger.warning(f"⚠️ Migrations directory not found: {self.migrations_dir}")
                return []
            
            # Get all .sql files
            sql_files = list(self.migrations_dir.glob("*.sql"))
            
            # Sort by filename (which should start with version number)
            sql_files.sort(key=lambda x: x.name)
            
            logger.info(f"📁 Found {len(sql_files)} migration files")
            return sql_files
            
        except Exception as e:
            logger.error(f"❌ Failed to get migration files: {e}")
            return []
    
    def _parse_migration_file(self, file_path: Path) -> Optional[Migration]:
        """تحليل ملف المايجريشن"""
        try:
            # Extract version from filename (e.g., "001_create_users_table.sql")
            filename = file_path.name
            version_match = re.match(r'^(\d+)_(.+)\.sql$', filename)
            
            if not version_match:
                logger.warning(f"⚠️ Invalid migration filename format: {filename}")
                return None
            
            version = version_match.group(1)
            name = version_match.group(2).replace('_', ' ').title()
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if not content.strip():
                logger.warning(f"⚠️ Empty migration file: {filename}")
                return None
            
            migration = Migration(
                version=version,
                name=name,
                file_path=str(file_path),
                content=content
            )
            
            logger.debug(f"📄 Parsed migration: {migration}")
            return migration
            
        except Exception as e:
            logger.error(f"❌ Failed to parse migration file {file_path}: {e}")
            return None
    
    def _calculate_checksum(self, content: str) -> str:
        """حساب checksum للمحتوى"""
        import hashlib
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    async def _get_executed_migrations(self) -> Dict[str, Dict]:
        """الحصول على المايجريشن المنفذة"""
        try:
            rows = await self.connection.fetch("""
                SELECT version, name, file_path, checksum, executed_at, 
                       execution_time_ms, success, error_message
                FROM schema_migrations
                ORDER BY version
            """)
            
            executed = {}
            for row in rows:
                executed[row['version']] = {
                    'name': row['name'],
                    'file_path': row['file_path'],
                    'checksum': row['checksum'],
                    'executed_at': row['executed_at'],
                    'execution_time_ms': row['execution_time_ms'],
                    'success': row['success'],
                    'error_message': row['error_message']
                }
            
            logger.info(f"📊 Found {len(executed)} executed migrations")
            return executed
            
        except Exception as e:
            logger.error(f"❌ Failed to get executed migrations: {e}")
            return {}
    
    async def _execute_migration(self, migration: Migration) -> Tuple[bool, Optional[str], int]:
        """تنفيذ مايجريشن واحد"""
        try:
            logger.info(f"🔄 Executing migration: {migration}")
            
            start_time = datetime.now()
            
            # Start transaction
            async with self.connection.transaction():
                # Execute migration SQL
                await self.connection.execute(migration.content)
                
                # Calculate execution time
                execution_time = (datetime.now() - start_time).total_seconds() * 1000
                execution_time_ms = int(execution_time)
                
                # Record migration in schema_migrations table
                checksum = self._calculate_checksum(migration.content)
                
                await self.connection.execute("""
                    INSERT INTO schema_migrations 
                    (version, name, file_path, checksum, execution_time_ms, success)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, migration.version, migration.name, migration.file_path, 
                    checksum, execution_time_ms, True)
                
                logger.info(f"✅ Migration executed successfully: {migration} ({execution_time_ms}ms)")
                return True, None, execution_time_ms
                
        except Exception as e:
            error_message = str(e)
            logger.error(f"❌ Migration failed: {migration} - {error_message}")
            
            # Record failed migration
            try:
                execution_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
                checksum = self._calculate_checksum(migration.content)
                
                await self.connection.execute("""
                    INSERT INTO schema_migrations 
                    (version, name, file_path, checksum, execution_time_ms, success, error_message)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                """, migration.version, migration.name, migration.file_path, 
                    checksum, execution_time_ms, False, error_message)
                    
            except Exception as record_error:
                logger.error(f"❌ Failed to record migration failure: {record_error}")
            
            return False, error_message, execution_time_ms
    
    async def migrate(self, target_version: Optional[str] = None) -> Dict[str, any]:
        """تنفيذ المايجريشن"""
        try:
            logger.info("🚀 Starting database migration...")
            
            if not self.connection:
                await self.connect()
            
            # Get migration files
            migration_files = self._get_migration_files()
            if not migration_files:
                logger.info("📭 No migration files found")
                return {
                    "success": True,
                    "message": "No migrations to execute",
                    "executed_count": 0,
                    "failed_count": 0,
                    "total_time_ms": 0
                }
            
            # Parse migrations
            migrations = []
            for file_path in migration_files:
                migration = self._parse_migration_file(file_path)
                if migration:
                    migrations.append(migration)
            
            self.total_migrations = len(migrations)
            
            # Get executed migrations
            executed_migrations = await self._get_executed_migrations()
            
            # Filter migrations to execute
            migrations_to_execute = []
            for migration in migrations:
                # Skip if already executed successfully
                if migration.version in executed_migrations:
                    executed_info = executed_migrations[migration.version]
                    if executed_info['success']:
                        logger.debug(f"⏭️ Skipping already executed migration: {migration}")
                        continue
                    else:
                        logger.warning(f"⚠️ Re-executing failed migration: {migration}")
                
                # Check target version
                if target_version and migration.version > target_version:
                    logger.info(f"🎯 Stopping at target version: {target_version}")
                    break
                
                migrations_to_execute.append(migration)
            
            if not migrations_to_execute:
                logger.info("✅ All migrations are up to date")
                return {
                    "success": True,
                    "message": "Database is up to date",
                    "executed_count": 0,
                    "failed_count": 0,
                    "total_time_ms": 0
                }
            
            # Execute migrations
            executed_count = 0
            failed_count = 0
            total_time_ms = 0
            failed_migrations = []
            
            for migration in migrations_to_execute:
                success, error_message, execution_time_ms = await self._execute_migration(migration)
                
                total_time_ms += execution_time_ms
                
                if success:
                    executed_count += 1
                    self.executed_migrations += 1
                else:
                    failed_count += 1
                    self.failed_migrations += 1
                    failed_migrations.append({
                        "migration": str(migration),
                        "error": error_message
                    })
                    
                    # Stop on first failure
                    logger.error(f"🛑 Stopping migration due to failure: {migration}")
                    break
            
            # Summary
            if failed_count == 0:
                logger.info(f"🎉 Migration completed successfully! Executed {executed_count} migrations in {total_time_ms}ms")
                return {
                    "success": True,
                    "message": f"Successfully executed {executed_count} migrations",
                    "executed_count": executed_count,
                    "failed_count": failed_count,
                    "total_time_ms": total_time_ms
                }
            else:
                logger.error(f"💥 Migration failed! {executed_count} succeeded, {failed_count} failed")
                return {
                    "success": False,
                    "message": f"Migration failed: {executed_count} succeeded, {failed_count} failed",
                    "executed_count": executed_count,
                    "failed_count": failed_count,
                    "total_time_ms": total_time_ms,
                    "failed_migrations": failed_migrations
                }
                
        except Exception as e:
            logger.error(f"❌ Migration process failed: {e}")
            return {
                "success": False,
                "message": f"Migration process failed: {str(e)}",
                "executed_count": 0,
                "failed_count": 0,
                "total_time_ms": 0
            }
    
    async def rollback(self, target_version: str) -> Dict[str, any]:
        """التراجع عن المايجريشن"""
        logger.warning("⚠️ Rollback functionality not implemented yet")
        return {
            "success": False,
            "message": "Rollback functionality not implemented",
            "error": "Feature not available"
        }
    
    async def status(self) -> Dict[str, any]:
        """حالة المايجريشن"""
        try:
            if not self.connection:
                await self.connect()
            
            # Get migration files
            migration_files = self._get_migration_files()
            migrations = []
            for file_path in migration_files:
                migration = self._parse_migration_file(file_path)
                if migration:
                    migrations.append(migration)
            
            # Get executed migrations
            executed_migrations = await self._get_executed_migrations()
            
            # Build status
            migration_status = []
            for migration in migrations:
                if migration.version in executed_migrations:
                    executed_info = executed_migrations[migration.version]
                    status = "SUCCESS" if executed_info['success'] else "FAILED"
                    executed_at = executed_info['executed_at'].isoformat()
                    execution_time = executed_info['execution_time_ms']
                    error = executed_info['error_message']
                else:
                    status = "PENDING"
                    executed_at = None
                    execution_time = None
                    error = None
                
                migration_status.append({
                    "version": migration.version,
                    "name": migration.name,
                    "status": status,
                    "executed_at": executed_at,
                    "execution_time_ms": execution_time,
                    "error": error
                })
            
            # Summary
            total_migrations = len(migrations)
            executed_count = len([m for m in migration_status if m['status'] in ['SUCCESS', 'FAILED']])
            success_count = len([m for m in migration_status if m['status'] == 'SUCCESS'])
            failed_count = len([m for m in migration_status if m['status'] == 'FAILED'])
            pending_count = len([m for m in migration_status if m['status'] == 'PENDING'])
            
            return {
                "total_migrations": total_migrations,
                "executed_migrations": executed_count,
                "successful_migrations": success_count,
                "failed_migrations": failed_count,
                "pending_migrations": pending_count,
                "migrations": migration_status
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get migration status: {e}")
            return {
                "error": str(e),
                "total_migrations": 0,
                "executed_migrations": 0,
                "successful_migrations": 0,
                "failed_migrations": 0,
                "pending_migrations": 0,
                "migrations": []
            }
    
    async def create_migration(self, name: str, content: str = None) -> str:
        """إنشاء ملف مايجريشن جديد"""
        try:
            # Get next version number
            migration_files = self._get_migration_files()
            if migration_files:
                last_file = migration_files[-1]
                last_version = int(re.match(r'^(\d+)_', last_file.name).group(1))
                next_version = f"{last_version + 1:03d}"
            else:
                next_version = "001"
            
            # Create filename
            safe_name = re.sub(r'[^a-zA-Z0-9_]', '_', name.lower())
            filename = f"{next_version}_{safe_name}.sql"
            file_path = self.migrations_dir / filename
            
            # Create migrations directory if it doesn't exist
            self.migrations_dir.mkdir(parents=True, exist_ok=True)
            
            # Default content if not provided
            if not content:
                content = f"""-- Migration: {name.title()}
-- Description: {name.replace('_', ' ').title()}
-- Version: {next_version}
-- Created: {datetime.now().strftime('%Y-%m-%d')}

-- Add your SQL statements here
-- Example:
-- CREATE TABLE example (
--     id SERIAL PRIMARY KEY,
--     name VARCHAR(255) NOT NULL,
--     created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
-- );
"""
            
            # Write file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"✅ Created migration file: {filename}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"❌ Failed to create migration: {e}")
            raise
    
    async def get_statistics(self) -> Dict[str, any]:
        """الحصول على إحصائيات المايجريشن"""
        return {
            "total_migrations": self.total_migrations,
            "executed_migrations": self.executed_migrations,
            "failed_migrations": self.failed_migrations,
            "success_rate": self.executed_migrations / max(self.total_migrations, 1) * 100
        }


# CLI Interface
async def main():
    """واجهة سطر الأوامر"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Database Migration Manager")
    parser.add_argument("command", choices=["migrate", "status", "create", "rollback"], help="Command to execute")
    parser.add_argument("--database-url", required=True, help="Database connection URL")
    parser.add_argument("--migrations-dir", default="database/migrations", help="Migrations directory")
    parser.add_argument("--target-version", help="Target migration version")
    parser.add_argument("--name", help="Migration name (for create command)")
    parser.add_argument("--content", help="Migration content (for create command)")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create migration manager
    manager = MigrationManager(args.database_url, args.migrations_dir)
    
    try:
        if args.command == "migrate":
            result = await manager.migrate(args.target_version)
            print(f"Migration result: {result}")
            
        elif args.command == "status":
            status = await manager.status()
            print(f"Migration status: {status}")
            
        elif args.command == "create":
            if not args.name:
                print("Error: --name is required for create command")
                return
            
            file_path = await manager.create_migration(args.name, args.content)
            print(f"Created migration: {file_path}")
            
        elif args.command == "rollback":
            if not args.target_version:
                print("Error: --target-version is required for rollback command")
                return
            
            result = await manager.rollback(args.target_version)
            print(f"Rollback result: {result}")
            
    finally:
        await manager.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
