'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { clsx } from 'clsx';

// أنواع أحجام المؤشر
export type SpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// أنواع المؤشرات
export type SpinnerVariant = 'default' | 'dots' | 'pulse' | 'bars' | 'ring';

// واجهة خصائص المؤشر
export interface LoadingSpinnerProps {
  size?: SpinnerSize;
  variant?: SpinnerVariant;
  color?: string;
  className?: string;
  text?: string;
  fullScreen?: boolean;
}

// أحجام المؤشرات
const spinnerSizes = {
  xs: 'w-4 h-4',
  sm: 'w-6 h-6',
  md: 'w-8 h-8',
  lg: 'w-12 h-12',
  xl: 'w-16 h-16',
};

// أحجام النصوص
const textSizes = {
  xs: 'text-xs',
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl',
};

// مؤشر افتراضي (دائري)
const DefaultSpinner = ({ size, color, className }: { size: SpinnerSize; color: string; className?: string }) => (
  <motion.div
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    className={clsx(spinnerSizes[size], className)}
  >
    <svg className="w-full h-full" viewBox="0 0 24 24" fill="none">
      <circle
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="2"
        className="opacity-25"
      />
      <path
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        className={color}
      />
    </svg>
  </motion.div>
);

// مؤشر النقاط
const DotsSpinner = ({ size, color }: { size: SpinnerSize; color: string }) => {
  const dotSize = {
    xs: 'w-1 h-1',
    sm: 'w-1.5 h-1.5',
    md: 'w-2 h-2',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4',
  };

  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: index * 0.2,
          }}
          className={clsx(dotSize[size], 'rounded-full', color)}
        />
      ))}
    </div>
  );
};

// مؤشر النبض
const PulseSpinner = ({ size, color, className }: { size: SpinnerSize; color: string; className?: string }) => (
  <motion.div
    animate={{
      scale: [1, 1.1, 1],
      opacity: [0.7, 1, 0.7],
    }}
    transition={{
      duration: 1.5,
      repeat: Infinity,
      ease: 'easeInOut',
    }}
    className={clsx(spinnerSizes[size], 'rounded-full', color, className)}
  />
);

// مؤشر الأشرطة
const BarsSpinner = ({ size, color }: { size: SpinnerSize; color: string }) => {
  const barHeight = {
    xs: 'h-3',
    sm: 'h-4',
    md: 'h-6',
    lg: 'h-8',
    xl: 'h-12',
  };

  const barWidth = {
    xs: 'w-0.5',
    sm: 'w-1',
    md: 'w-1',
    lg: 'w-1.5',
    xl: 'w-2',
  };

  return (
    <div className="flex items-end space-x-1">
      {[0, 1, 2, 3].map((index) => (
        <motion.div
          key={index}
          animate={{
            scaleY: [1, 0.4, 1],
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            delay: index * 0.1,
          }}
          className={clsx(barWidth[size], barHeight[size], color)}
        />
      ))}
    </div>
  );
};

// مؤشر الحلقة
const RingSpinner = ({ size, color, className }: { size: SpinnerSize; color: string; className?: string }) => (
  <motion.div
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    className={clsx(spinnerSizes[size], className)}
  >
    <div className={clsx('w-full h-full rounded-full border-2 border-transparent border-t-current', color)} />
  </motion.div>
);

// المكون الرئيسي
export default function LoadingSpinner({
  size = 'md',
  variant = 'default',
  color = 'text-primary-600',
  className,
  text,
  fullScreen = false,
}: LoadingSpinnerProps) {
  // اختيار نوع المؤشر
  const renderSpinner = () => {
    const props = { size, color, className };
    
    switch (variant) {
      case 'dots':
        return <DotsSpinner size={size} color={color} />;
      case 'pulse':
        return <PulseSpinner {...props} />;
      case 'bars':
        return <BarsSpinner size={size} color={color} />;
      case 'ring':
        return <RingSpinner {...props} />;
      default:
        return <DefaultSpinner {...props} />;
    }
  };

  // المحتوى
  const content = (
    <div className="flex flex-col items-center justify-center space-y-3">
      {renderSpinner()}
      {text && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className={clsx(
            textSizes[size],
            'text-gray-600 dark:text-gray-400 font-medium'
          )}
        >
          {text}
        </motion.p>
      )}
    </div>
  );

  // إذا كان ملء الشاشة
  if (fullScreen) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm"
      >
        {content}
      </motion.div>
    );
  }

  // عادي
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={clsx('flex items-center justify-center', className)}
    >
      {content}
    </motion.div>
  );
}

// مكونات مخصصة للاستخدامات الشائعة
export const PageLoader = ({ text = 'جاري التحميل...' }: { text?: string }) => (
  <LoadingSpinner size="lg" text={text} fullScreen />
);

export const ButtonLoader = ({ size = 'sm' }: { size?: SpinnerSize }) => (
  <LoadingSpinner size={size} variant="ring" color="text-current" />
);

export const InlineLoader = ({ text }: { text?: string }) => (
  <LoadingSpinner size="sm" variant="dots" text={text} />
);

export const CardLoader = () => (
  <div className="flex items-center justify-center py-12">
    <LoadingSpinner size="lg" variant="pulse" text="جاري التحميل..." />
  </div>
);
