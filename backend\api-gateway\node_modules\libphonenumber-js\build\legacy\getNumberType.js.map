{"version": 3, "file": "getNumberType.js", "names": ["getNumberType", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "_getNumberType", "args", "Array", "prototype", "slice", "call", "arg_1", "arg_2", "arg_3", "arg_4", "isObject", "isViablePhoneNumber", "parse", "defaultCountry", "undefined", "TypeError"], "sources": ["../../source/legacy/getNumberType.js"], "sourcesContent": ["import isViablePhoneNumber from '../helpers/isViablePhoneNumber.js'\r\nimport _getNumberType from '../helpers/getNumberType.js'\r\nimport isObject from '../helpers/isObject.js'\r\nimport parse from '../parse.js'\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function getNumberType() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone) {\r\n\t\treturn\r\n\t}\r\n\treturn _getNumberType(input, options, metadata)\r\n}\r\n\r\n// Sort out arguments\r\nexport function normalizeArguments(args)\r\n{\r\n\tconst [arg_1, arg_2, arg_3, arg_4] = Array.prototype.slice.call(args)\r\n\r\n\tlet input\r\n\tlet options = {}\r\n\tlet metadata\r\n\r\n\t// If the phone number is passed as a string.\r\n\t// `getNumberType('88005553535', ...)`.\r\n\tif (typeof arg_1 === 'string')\r\n\t{\r\n\t\t// If \"default country\" argument is being passed\r\n\t\t// then convert it to an `options` object.\r\n\t\t// `getNumberType('88005553535', 'RU', metadata)`.\r\n\t\tif (!isObject(arg_2))\r\n\t\t{\r\n\t\t\tif (arg_4)\r\n\t\t\t{\r\n\t\t\t\toptions = arg_3\r\n\t\t\t\tmetadata = arg_4\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tmetadata = arg_3\r\n\t\t\t}\r\n\r\n\t\t\t// `parse` extracts phone numbers from raw text,\r\n\t\t\t// therefore it will cut off all \"garbage\" characters,\r\n\t\t\t// while this `validate` function needs to verify\r\n\t\t\t// that the phone number contains no \"garbage\"\r\n\t\t\t// therefore the explicit `isViablePhoneNumber` check.\r\n\t\t\tif (isViablePhoneNumber(arg_1))\r\n\t\t\t{\r\n\t\t\t\tinput = parse(arg_1, { defaultCountry: arg_2 }, metadata)\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tinput = {}\r\n\t\t\t}\r\n\t\t}\r\n\t\t// No \"resrict country\" argument is being passed.\r\n\t\t// International phone number is passed.\r\n\t\t// `getNumberType('+78005553535', metadata)`.\r\n\t\telse\r\n\t\t{\r\n\t\t\tif (arg_3)\r\n\t\t\t{\r\n\t\t\t\toptions = arg_2\r\n\t\t\t\tmetadata = arg_3\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tmetadata = arg_2\r\n\t\t\t}\r\n\r\n\t\t\t// `parse` extracts phone numbers from raw text,\r\n\t\t\t// therefore it will cut off all \"garbage\" characters,\r\n\t\t\t// while this `validate` function needs to verify\r\n\t\t\t// that the phone number contains no \"garbage\"\r\n\t\t\t// therefore the explicit `isViablePhoneNumber` check.\r\n\t\t\tif (isViablePhoneNumber(arg_1))\r\n\t\t\t{\r\n\t\t\t\tinput = parse(arg_1, undefined, metadata)\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\tinput = {}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// If the phone number is passed as a parsed phone number.\r\n\t// `getNumberType({ phone: '88005553535', country: 'RU' }, ...)`.\r\n\telse if (isObject(arg_1))\r\n\t{\r\n\t\tinput = arg_1\r\n\r\n\t\tif (arg_3)\r\n\t\t{\r\n\t\t\toptions = arg_2\r\n\t\t\tmetadata = arg_3\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tmetadata = arg_2\r\n\t\t}\r\n\t}\r\n\telse throw new TypeError('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\treturn {\r\n\t\tinput,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t}\r\n}"], "mappings": ";;;;;;;;AAAA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;AAEA;AACe,SAASA,aAAT,GAAyB;EACvC,0BAAqCC,kBAAkB,CAACC,SAAD,CAAvD;EAAA,IAAQC,KAAR,uBAAQA,KAAR;EAAA,IAAeC,OAAf,uBAAeA,OAAf;EAAA,IAAwBC,QAAxB,uBAAwBA,QAAxB,CADuC,CAEvC;;;EACA,IAAI,CAACF,KAAK,CAACG,KAAX,EAAkB;IACjB;EACA;;EACD,OAAO,IAAAC,0BAAA,EAAeJ,KAAf,EAAsBC,OAAtB,EAA+BC,QAA/B,CAAP;AACA,C,CAED;;;AACO,SAASJ,kBAAT,CAA4BO,IAA5B,EACP;EACC,4BAAqCC,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBC,IAAtB,CAA2BJ,IAA3B,CAArC;EAAA;EAAA,IAAOK,KAAP;EAAA,IAAcC,KAAd;EAAA,IAAqBC,KAArB;EAAA,IAA4BC,KAA5B;;EAEA,IAAIb,KAAJ;EACA,IAAIC,OAAO,GAAG,EAAd;EACA,IAAIC,QAAJ,CALD,CAOC;EACA;;EACA,IAAI,OAAOQ,KAAP,KAAiB,QAArB,EACA;IACC;IACA;IACA;IACA,IAAI,CAAC,IAAAI,oBAAA,EAASH,KAAT,CAAL,EACA;MACC,IAAIE,KAAJ,EACA;QACCZ,OAAO,GAAGW,KAAV;QACAV,QAAQ,GAAGW,KAAX;MACA,CAJD,MAMA;QACCX,QAAQ,GAAGU,KAAX;MACA,CATF,CAWC;MACA;MACA;MACA;MACA;;;MACA,IAAI,IAAAG,+BAAA,EAAoBL,KAApB,CAAJ,EACA;QACCV,KAAK,GAAG,IAAAgB,iBAAA,EAAMN,KAAN,EAAa;UAAEO,cAAc,EAAEN;QAAlB,CAAb,EAAwCT,QAAxC,CAAR;MACA,CAHD,MAKA;QACCF,KAAK,GAAG,EAAR;MACA;IACD,CAzBD,CA0BA;IACA;IACA;IA5BA,KA8BA;MACC,IAAIY,KAAJ,EACA;QACCX,OAAO,GAAGU,KAAV;QACAT,QAAQ,GAAGU,KAAX;MACA,CAJD,MAMA;QACCV,QAAQ,GAAGS,KAAX;MACA,CATF,CAWC;MACA;MACA;MACA;MACA;;;MACA,IAAI,IAAAI,+BAAA,EAAoBL,KAApB,CAAJ,EACA;QACCV,KAAK,GAAG,IAAAgB,iBAAA,EAAMN,KAAN,EAAaQ,SAAb,EAAwBhB,QAAxB,CAAR;MACA,CAHD,MAKA;QACCF,KAAK,GAAG,EAAR;MACA;IACD;EACD,CA5DD,CA6DA;EACA;EA9DA,KA+DK,IAAI,IAAAc,oBAAA,EAASJ,KAAT,CAAJ,EACL;IACCV,KAAK,GAAGU,KAAR;;IAEA,IAAIE,KAAJ,EACA;MACCX,OAAO,GAAGU,KAAV;MACAT,QAAQ,GAAGU,KAAX;IACA,CAJD,MAMA;MACCV,QAAQ,GAAGS,KAAX;IACA;EACD,CAbI,MAcA,MAAM,IAAIQ,SAAJ,CAAc,oFAAd,CAAN;;EAEL,OAAO;IACNnB,KAAK,EAALA,KADM;IAENC,OAAO,EAAPA,OAFM;IAGNC,QAAQ,EAARA;EAHM,CAAP;AAKA"}