import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  <PERSON>ert,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Divider,
} from '@mui/material';
import {
  Send,
  Person,
  Payment,
  CheckCircle,
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Head from 'next/head';
import Layout from '../../components/Layout';

const steps = ['معلومات المستقبل', 'تفاصيل التحويل', 'طريقة الدفع', 'التأكيد'];

// Validation schemas for each step
const step1Schema = Yup.object({
  receiverName: Yup.string().required('اسم المستقبل مطلوب'),
  receiverPhone: Yup.string().required('رقم هاتف المستقبل مطلوب'),
  receiverEmail: Yup.string().email('البريد الإلكتروني غير صالح'),
});

const step2Schema = Yup.object({
  sendAmount: Yup.number()
    .min(1, 'المبلغ يجب أن يكون أكبر من 1')
    .max(50000, 'المبلغ يجب أن يكون أقل من 50000')
    .required('مبلغ الإرسال مطلوب'),
  sendCurrency: Yup.string().required('عملة الإرسال مطلوبة'),
  receiveCurrency: Yup.string().required('عملة الاستقبال مطلوبة'),
  transferType: Yup.string().required('نوع التحويل مطلوب'),
  purpose: Yup.string().required('الغرض من التحويل مطلوب'),
});

const NewTransferPage: React.FC = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [quote, setQuote] = useState<any>(null);

  const formik = useFormik({
    initialValues: {
      // Step 1: Receiver Info
      receiverName: '',
      receiverPhone: '',
      receiverEmail: '',
      receiverAddress: {
        street: '',
        city: '',
        country: 'SA',
      },
      
      // Step 2: Transfer Details
      sendAmount: '',
      sendCurrency: 'SAR',
      receiveCurrency: 'USD',
      transferType: 'international',
      purpose: 'family_support',
      description: '',
      
      // Step 3: Payment Details
      paymentMethod: 'wallet',
      sourceWalletId: '',
      
      // Step 4: Delivery Details
      deliveryMethod: 'bank_transfer',
      bankName: '',
      accountNumber: '',
    },
    validationSchema: activeStep === 0 ? step1Schema : activeStep === 1 ? step2Schema : undefined,
    onSubmit: async (values) => {
      if (activeStep < steps.length - 1) {
        handleNext();
      } else {
        await handleSubmitTransfer(values);
      }
    },
  });

  const handleNext = async () => {
    if (activeStep === 1) {
      // Get quote when moving from step 2 to step 3
      await getQuote();
    }
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const getQuote = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/transfers/quote?' + new URLSearchParams({
        sendAmount: formik.values.sendAmount,
        sendCurrency: formik.values.sendCurrency,
        receiveCurrency: formik.values.receiveCurrency,
        transferType: formik.values.transferType,
      }), {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const quoteData = await response.json();
        setQuote(quoteData);
      } else {
        setError('فشل في الحصول على عرض السعر');
        // Mock quote for development
        setQuote({
          sendAmount: formik.values.sendAmount,
          sendCurrency: formik.values.sendCurrency,
          receiveAmount: (parseFloat(formik.values.sendAmount) * 0.27).toFixed(2),
          receiveCurrency: formik.values.receiveCurrency,
          exchangeRate: '0.27',
          transferFee: '15.00',
          totalAmount: (parseFloat(formik.values.sendAmount) + 15).toFixed(2),
          estimatedDelivery: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        });
      }
    } catch (err) {
      setError('حدث خطأ في الحصول على عرض السعر');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitTransfer = async (values: any) => {
    try {
      setLoading(true);
      setError(null);

      const transferData = {
        ...values,
        ...quote,
      };

      const response = await fetch('/api/transfers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(transferData),
      });

      if (response.ok) {
        const transfer = await response.json();
        router.push(`/transfers/${transfer.id}`);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'فشل في إنشاء التحويل');
      }
    } catch (err) {
      setError('حدث خطأ في إنشاء التحويل');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="receiverName"
                label="اسم المستقبل"
                value={formik.values.receiverName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.receiverName && Boolean(formik.errors.receiverName)}
                helperText={formik.touched.receiverName && formik.errors.receiverName}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="receiverPhone"
                label="رقم الهاتف"
                value={formik.values.receiverPhone}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.receiverPhone && Boolean(formik.errors.receiverPhone)}
                helperText={formik.touched.receiverPhone && formik.errors.receiverPhone}
                dir="ltr"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="receiverEmail"
                label="البريد الإلكتروني (اختياري)"
                type="email"
                value={formik.values.receiverEmail}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.receiverEmail && Boolean(formik.errors.receiverEmail)}
                helperText={formik.touched.receiverEmail && formik.errors.receiverEmail}
                dir="ltr"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>الدولة</InputLabel>
                <Select
                  name="receiverAddress.country"
                  value={formik.values.receiverAddress.country}
                  onChange={formik.handleChange}
                  label="الدولة"
                >
                  <MenuItem value="SA">السعودية</MenuItem>
                  <MenuItem value="US">الولايات المتحدة</MenuItem>
                  <MenuItem value="GB">المملكة المتحدة</MenuItem>
                  <MenuItem value="AE">الإمارات</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                name="sendAmount"
                label="مبلغ الإرسال"
                type="number"
                value={formik.values.sendAmount}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.sendAmount && Boolean(formik.errors.sendAmount)}
                helperText={formik.touched.sendAmount && formik.errors.sendAmount}
                InputProps={{
                  endAdornment: formik.values.sendCurrency,
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>عملة الإرسال</InputLabel>
                <Select
                  name="sendCurrency"
                  value={formik.values.sendCurrency}
                  onChange={formik.handleChange}
                  label="عملة الإرسال"
                >
                  <MenuItem value="SAR">ريال سعودي (SAR)</MenuItem>
                  <MenuItem value="USD">دولار أمريكي (USD)</MenuItem>
                  <MenuItem value="EUR">يورو (EUR)</MenuItem>
                  <MenuItem value="GBP">جنيه إسترليني (GBP)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>عملة الاستقبال</InputLabel>
                <Select
                  name="receiveCurrency"
                  value={formik.values.receiveCurrency}
                  onChange={formik.handleChange}
                  label="عملة الاستقبال"
                >
                  <MenuItem value="SAR">ريال سعودي (SAR)</MenuItem>
                  <MenuItem value="USD">دولار أمريكي (USD)</MenuItem>
                  <MenuItem value="EUR">يورو (EUR)</MenuItem>
                  <MenuItem value="GBP">جنيه إسترليني (GBP)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>الغرض من التحويل</InputLabel>
                <Select
                  name="purpose"
                  value={formik.values.purpose}
                  onChange={formik.handleChange}
                  label="الغرض من التحويل"
                >
                  <MenuItem value="family_support">دعم عائلي</MenuItem>
                  <MenuItem value="education">تعليم</MenuItem>
                  <MenuItem value="medical">طبي</MenuItem>
                  <MenuItem value="business">تجاري</MenuItem>
                  <MenuItem value="investment">استثمار</MenuItem>
                  <MenuItem value="other">أخرى</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                name="description"
                label="وصف التحويل (اختياري)"
                multiline
                rows={3}
                value={formik.values.description}
                onChange={formik.handleChange}
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Box>
            {quote && (
              <Paper sx={{ p: 3, mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                <Typography variant="h6" gutterBottom>
                  عرض السعر
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">مبلغ الإرسال:</Typography>
                    <Typography variant="h6">{quote.sendAmount} {quote.sendCurrency}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">مبلغ الاستقبال:</Typography>
                    <Typography variant="h6">{quote.receiveAmount} {quote.receiveCurrency}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">سعر الصرف:</Typography>
                    <Typography variant="body1">{quote.exchangeRate}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">رسوم التحويل:</Typography>
                    <Typography variant="body1">{quote.transferFee} {quote.sendCurrency}</Typography>
                  </Grid>
                </Grid>
                <Divider sx={{ my: 2, borderColor: 'rgba(255,255,255,0.3)' }} />
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="h6">المبلغ الإجمالي:</Typography>
                  <Typography variant="h5" fontWeight="bold">{quote.totalAmount} {quote.sendCurrency}</Typography>
                </Box>
              </Paper>
            )}

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>طريقة الدفع</InputLabel>
                  <Select
                    name="paymentMethod"
                    value={formik.values.paymentMethod}
                    onChange={formik.handleChange}
                    label="طريقة الدفع"
                  >
                    <MenuItem value="wallet">المحفظة الرقمية</MenuItem>
                    <MenuItem value="credit_card">بطاقة ائتمان</MenuItem>
                    <MenuItem value="bank_transfer">تحويل بنكي</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );

      case 3:
        return (
          <Box>
            <Alert severity="info" sx={{ mb: 3 }}>
              يرجى مراجعة تفاصيل التحويل قبل التأكيد
            </Alert>
            
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                ملخص التحويل
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">المستقبل:</Typography>
                  <Typography variant="body1">{formik.values.receiverName}</Typography>
                  <Typography variant="body2" color="text.secondary">{formik.values.receiverPhone}</Typography>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">المبلغ:</Typography>
                  <Typography variant="h6">
                    {formik.values.sendAmount} {formik.values.sendCurrency} → {quote?.receiveAmount} {formik.values.receiveCurrency}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">طريقة الدفع:</Typography>
                  <Typography variant="body1">{formik.values.paymentMethod}</Typography>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">المبلغ الإجمالي:</Typography>
                  <Typography variant="h6" color="primary">{quote?.totalAmount} {formik.values.sendCurrency}</Typography>
                </Grid>
              </Grid>
            </Paper>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Head>
        <title>تحويل جديد - WS Transfir</title>
        <meta name="description" content="إنشاء تحويل مالي جديد" />
      </Head>

      <Layout>
        <Box sx={{ p: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            تحويل جديد
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Card>
            <CardContent sx={{ p: 4 }}>
              <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              <form onSubmit={formik.handleSubmit}>
                {renderStepContent(activeStep)}

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                  <Button
                    disabled={activeStep === 0}
                    onClick={handleBack}
                    variant="outlined"
                  >
                    السابق
                  </Button>

                  <Button
                    type="submit"
                    variant="contained"
                    disabled={loading}
                    startIcon={activeStep === steps.length - 1 ? <Send /> : undefined}
                    sx={{
                      background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    }}
                  >
                    {loading
                      ? 'جاري المعالجة...'
                      : activeStep === steps.length - 1
                      ? 'تأكيد التحويل'
                      : 'التالي'}
                  </Button>
                </Box>
              </form>
            </CardContent>
          </Card>
        </Box>
      </Layout>
    </>
  );
};

export default NewTransferPage;
