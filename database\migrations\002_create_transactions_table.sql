-- Migration: Create Transactions Table
-- Description: إنشاء جدول المعاملات المالية
-- Version: 002
-- Created: 2024-01-15

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Transaction Basic Info
    transaction_number VARCHAR(20) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('transfer', 'deposit', 'withdrawal', 'payment', 'refund', 'fee', 'commission')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
    
    -- Amount Information
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    amount_in_base_currency DECIMAL(15,2) NOT NULL,
    
    -- Fee Information
    fee_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    fee_currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    fee_type VARCHAR(20) DEFAULT 'fixed' CHECK (fee_type IN ('fixed', 'percentage', 'tiered')),
    
    -- Parties Involved
    sender_id VARCHAR(50) NOT NULL,
    sender_type VARCHAR(20) NOT NULL DEFAULT 'user' CHECK (sender_type IN ('user', 'agent', 'system', 'external')),
    receiver_id VARCHAR(50) NOT NULL,
    receiver_type VARCHAR(20) NOT NULL DEFAULT 'user' CHECK (receiver_type IN ('user', 'agent', 'system', 'external')),
    
    -- Agent Information (if applicable)
    agent_id VARCHAR(50),
    agent_commission DECIMAL(15,2) DEFAULT 0,
    agent_commission_rate DECIMAL(5,4) DEFAULT 0,
    
    -- External References
    external_reference VARCHAR(100),
    bank_reference VARCHAR(100),
    payment_method VARCHAR(50),
    
    -- Transaction Details
    description TEXT,
    purpose VARCHAR(100),
    category VARCHAR(50),
    subcategory VARCHAR(50),
    
    -- Location Information
    location_country VARCHAR(3),
    location_city VARCHAR(100),
    location_coordinates POINT,
    
    -- Device and Security
    device_id VARCHAR(100),
    device_type VARCHAR(20),
    ip_address INET,
    user_agent TEXT,
    
    -- Risk and Compliance
    risk_score DECIMAL(3,2) DEFAULT 0 CHECK (risk_score BETWEEN 0 AND 1),
    risk_level VARCHAR(10) DEFAULT 'low' CHECK (risk_level IN ('very_low', 'low', 'medium', 'high', 'very_high')),
    aml_status VARCHAR(20) DEFAULT 'clear' CHECK (aml_status IN ('clear', 'flagged', 'under_review', 'blocked')),
    compliance_checked BOOLEAN DEFAULT false,
    compliance_checked_at TIMESTAMP WITH TIME ZONE,
    
    -- Fraud Detection
    fraud_score DECIMAL(3,2) DEFAULT 0 CHECK (fraud_score BETWEEN 0 AND 1),
    fraud_flags TEXT[],
    fraud_checked BOOLEAN DEFAULT false,
    fraud_checked_at TIMESTAMP WITH TIME ZONE,
    
    -- Processing Information
    processing_started_at TIMESTAMP WITH TIME ZONE,
    processing_completed_at TIMESTAMP WITH TIME ZONE,
    processing_duration_ms INTEGER,
    retry_count INTEGER DEFAULT 0,
    
    -- Settlement Information
    settlement_date DATE,
    settlement_batch_id VARCHAR(50),
    settlement_status VARCHAR(20) DEFAULT 'pending' CHECK (settlement_status IN ('pending', 'settled', 'failed')),
    
    -- Notification Status
    sender_notified BOOLEAN DEFAULT false,
    receiver_notified BOOLEAN DEFAULT false,
    notification_sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Reconciliation
    reconciled BOOLEAN DEFAULT false,
    reconciled_at TIMESTAMP WITH TIME ZONE,
    reconciliation_batch_id VARCHAR(50),
    
    -- Parent/Child Relationships
    parent_transaction_id VARCHAR(50),
    root_transaction_id VARCHAR(50),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    tags TEXT[],
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Soft Delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by VARCHAR(50),
    
    -- Foreign Key Constraints
    CONSTRAINT fk_transactions_sender FOREIGN KEY (sender_id) REFERENCES users(id),
    CONSTRAINT fk_transactions_receiver FOREIGN KEY (receiver_id) REFERENCES users(id),
    CONSTRAINT fk_transactions_agent FOREIGN KEY (agent_id) REFERENCES users(id),
    CONSTRAINT fk_transactions_parent FOREIGN KEY (parent_transaction_id) REFERENCES transactions(id),
    CONSTRAINT fk_transactions_root FOREIGN KEY (root_transaction_id) REFERENCES transactions(id)
);

-- Create indexes for better performance
CREATE INDEX idx_transactions_number ON transactions(transaction_number) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_type ON transactions(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_status ON transactions(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_sender ON transactions(sender_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_receiver ON transactions(receiver_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_agent ON transactions(agent_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_created_at ON transactions(created_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_amount ON transactions(amount) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_currency ON transactions(currency) WHERE deleted_at IS NULL;

-- Create composite indexes for common queries
CREATE INDEX idx_transactions_sender_status ON transactions(sender_id, status) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_receiver_status ON transactions(receiver_id, status) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_date_status ON transactions(created_at, status) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_type_status ON transactions(type, status) WHERE deleted_at IS NULL;

-- Create indexes for risk and compliance
CREATE INDEX idx_transactions_risk_level ON transactions(risk_level) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_aml_status ON transactions(aml_status) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_fraud_score ON transactions(fraud_score) WHERE fraud_score > 0.5 AND deleted_at IS NULL;

-- Create GIN indexes for JSONB and array fields
CREATE INDEX idx_transactions_metadata ON transactions USING GIN(metadata) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_tags ON transactions USING GIN(tags) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_fraud_flags ON transactions USING GIN(fraud_flags) WHERE deleted_at IS NULL;

-- Create function to generate transaction number
CREATE OR REPLACE FUNCTION generate_transaction_number()
RETURNS TRIGGER AS $$
DECLARE
    prefix VARCHAR(3);
    date_part VARCHAR(6);
    sequence_part VARCHAR(6);
    new_number VARCHAR(20);
BEGIN
    -- Set prefix based on transaction type
    CASE NEW.type
        WHEN 'transfer' THEN prefix := 'TRF';
        WHEN 'deposit' THEN prefix := 'DEP';
        WHEN 'withdrawal' THEN prefix := 'WTH';
        WHEN 'payment' THEN prefix := 'PAY';
        WHEN 'refund' THEN prefix := 'REF';
        WHEN 'fee' THEN prefix := 'FEE';
        WHEN 'commission' THEN prefix := 'COM';
        ELSE prefix := 'TXN';
    END CASE;
    
    -- Get date part (YYMMDD)
    date_part := TO_CHAR(CURRENT_DATE, 'YYMMDD');
    
    -- Get sequence part (6 digits)
    SELECT LPAD(NEXTVAL('transaction_number_seq')::TEXT, 6, '0') INTO sequence_part;
    
    -- Combine parts
    new_number := prefix || date_part || sequence_part;
    
    NEW.transaction_number := new_number;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for transaction numbers
CREATE SEQUENCE IF NOT EXISTS transaction_number_seq START 1;

-- Create trigger to auto-generate transaction number
CREATE TRIGGER generate_transaction_number_trigger
    BEFORE INSERT ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION generate_transaction_number();

-- Create function to generate transaction ID
CREATE OR REPLACE FUNCTION generate_transaction_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'txn_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to auto-generate transaction ID
CREATE TRIGGER generate_transaction_id_trigger
    BEFORE INSERT ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION generate_transaction_id();

-- Create trigger to update updated_at
CREATE TRIGGER update_transactions_updated_at
    BEFORE UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to calculate amount in base currency
CREATE OR REPLACE FUNCTION calculate_base_amount()
RETURNS TRIGGER AS $$
BEGIN
    NEW.amount_in_base_currency := NEW.amount * COALESCE(NEW.exchange_rate, 1.0);
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to auto-calculate base amount
CREATE TRIGGER calculate_base_amount_trigger
    BEFORE INSERT OR UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION calculate_base_amount();

-- Add comments for documentation
COMMENT ON TABLE transactions IS 'جدول المعاملات المالية - يحتوي على جميع التحويلات والمدفوعات';
COMMENT ON COLUMN transactions.id IS 'معرف المعاملة الفريد';
COMMENT ON COLUMN transactions.transaction_number IS 'رقم المعاملة المرئي للمستخدم';
COMMENT ON COLUMN transactions.type IS 'نوع المعاملة';
COMMENT ON COLUMN transactions.status IS 'حالة المعاملة';
COMMENT ON COLUMN transactions.amount IS 'مبلغ المعاملة';
COMMENT ON COLUMN transactions.risk_score IS 'درجة المخاطر (0-1)';
COMMENT ON COLUMN transactions.fraud_score IS 'درجة الاحتيال (0-1)';

-- Create views for different transaction types
CREATE VIEW transfer_transactions AS
SELECT * FROM transactions 
WHERE type = 'transfer' AND deleted_at IS NULL;

CREATE VIEW pending_transactions AS
SELECT * FROM transactions 
WHERE status = 'pending' AND deleted_at IS NULL;

CREATE VIEW high_risk_transactions AS
SELECT * FROM transactions 
WHERE risk_level IN ('high', 'very_high') AND deleted_at IS NULL;

-- Create view for transaction statistics
CREATE VIEW transaction_statistics AS
SELECT 
    type,
    status,
    currency,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as average_amount,
    MIN(amount) as min_amount,
    MAX(amount) as max_amount,
    AVG(risk_score) as average_risk_score,
    COUNT(CASE WHEN fraud_score > 0.5 THEN 1 END) as high_fraud_count
FROM transactions 
WHERE deleted_at IS NULL
GROUP BY type, status, currency;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON transactions TO ws_app_user;
GRANT SELECT ON transfer_transactions TO ws_app_user;
GRANT SELECT ON pending_transactions TO ws_app_user;
GRANT SELECT ON high_risk_transactions TO ws_app_user;
GRANT SELECT ON transaction_statistics TO ws_app_user;
GRANT USAGE ON SEQUENCE transaction_number_seq TO ws_app_user;
