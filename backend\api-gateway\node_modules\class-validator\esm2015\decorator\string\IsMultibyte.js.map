{"version": 3, "file": "IsMultibyte.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMultibyte.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,oBAAoB,MAAM,2BAA2B,CAAC;AAE7D,MAAM,CAAC,MAAM,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,iBAAqC;IAC/D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YACtD,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,oDAAoD,EAC/E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isMultibyteValidator from 'validator/lib/isMultibyte';\n\nexport const IS_MULTIBYTE = 'isMultibyte';\n\n/**\n * Checks if the string contains one or more multibyte chars.\n * If given value is not a string, then it returns false.\n */\nexport function isMultibyte(value: unknown): boolean {\n  return typeof value === 'string' && isMultibyteValidator(value);\n}\n\n/**\n * Checks if the string contains one or more multibyte chars.\n * If given value is not a string, then it returns false.\n */\nexport function IsMultibyte(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_MULTIBYTE,\n      validator: {\n        validate: (value, args): boolean => isMultibyte(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain one or more multibyte chars',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}