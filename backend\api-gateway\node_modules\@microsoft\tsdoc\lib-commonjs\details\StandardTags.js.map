{"version": 3, "file": "StandardTags.js", "sourceRoot": "", "sources": ["../../src/details/StandardTags.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,0EAI6C;AAC7C,qDAAoD;AAEpD;;GAEG;AACH;IAAA;IA8fA,CAAC;IAHgB,uBAAU,GAAzB,UAA0B,UAAiD;QACzE,OAAO,IAAI,uCAAkB,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC;IA5fD;;;;;;;;;OASG;IACoB,kBAAK,GAAuB,YAAY,CAAC,UAAU,CAAC;QACzE,OAAO,EAAE,QAAQ;QACjB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,aAAa;KAC/C,CAAC,CAAC;IAEH;;;;;;;;;;;;;OAaG;IACoB,iBAAI,GAAuB,YAAY,CAAC,UAAU,CAAC;QACxE,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,aAAa;KAC/C,CAAC,CAAC;IAEH;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACoB,sBAAS,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC7E,OAAO,EAAE,YAAY;QACrB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;;;OAQG;IACoB,yBAAY,GAAuB,YAAY,CAAC,UAAU,CAAC;QAChF,OAAO,EAAE,eAAe;QACxB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;;OAOG;IACoB,uBAAU,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC9E,OAAO,EAAE,aAAa;QACtB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;;;;;OAQG;IACoB,0BAAa,GAAuB,YAAY,CAAC,UAAU,CAAC;QACjF,OAAO,EAAE,gBAAgB;QACzB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;OAKG;IACoB,oBAAO,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC3E,OAAO,EAAE,UAAU;QACnB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;;;;;OAUG;IACoB,yBAAY,GAAuB,YAAY,CAAC,UAAU,CAAC;QAChF,OAAO,EAAE,eAAe;QACxB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,aAAa;KAC/C,CAAC,CAAC;IAEH;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACoB,uBAAU,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC9E,OAAO,EAAE,aAAa;QACtB,UAAU,EAAE,uCAAkB,CAAC,SAAS;QACxC,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;;;;;OAUG;IACoB,qBAAQ,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC5E,OAAO,EAAE,WAAW;QACpB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,aAAa;KAC/C,CAAC,CAAC;IAEH;;;;;;;;;OASG;IACoB,kBAAK,GAAuB,YAAY,CAAC,UAAU,CAAC;QACzE,OAAO,EAAE,QAAQ;QACjB,UAAU,EAAE,uCAAkB,CAAC,SAAS;QACxC,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;;;;;;;OAUG;IACoB,iBAAI,GAAuB,YAAY,CAAC,UAAU,CAAC;QACxE,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,uCAAkB,CAAC,SAAS;QACxC,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;;;;;;;;OAWG;IACoB,qBAAQ,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC5E,OAAO,EAAE,WAAW;QACpB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;;;OAQG;IACoB,iCAAoB,GAAuB,YAAY,CAAC,UAAU,CAAC;QACxF,OAAO,EAAE,uBAAuB;QAChC,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;;;OAMG;IACoB,kBAAK,GAAuB,YAAY,CAAC,UAAU,CAAC;QACzE,OAAO,EAAE,QAAQ;QACjB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;;;OAMG;IACoB,2BAAc,GAAuB,YAAY,CAAC,UAAU,CAAC;QAClF,OAAO,EAAE,iBAAiB;QAC1B,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;;;;;;OASG;IACoB,mBAAM,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC1E,OAAO,EAAE,SAAS;QAClB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,aAAa;KAC/C,CAAC,CAAC;IAEH;;;;;;;;;;;OAWG;IACoB,qBAAQ,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC5E,OAAO,EAAE,WAAW;QACpB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;;;;OASG;IACoB,oBAAO,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC3E,OAAO,EAAE,UAAU;QACnB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;OAIG;IACoB,oBAAO,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC3E,OAAO,EAAE,UAAU;QACnB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;;;;;;;;OAWG;IACoB,mBAAM,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC1E,OAAO,EAAE,SAAS;QAClB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACoB,gBAAG,GAAuB,YAAY,CAAC,UAAU,CAAC;QACvE,OAAO,EAAE,MAAM;QACf,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACoB,mBAAM,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC1E,OAAO,EAAE,SAAS;QAClB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;;;;;OAMG;IACoB,sBAAS,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC7E,OAAO,EAAE,YAAY;QACrB,UAAU,EAAE,uCAAkB,CAAC,QAAQ;QACvC,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,iCAAe,CAAC,IAAI;KACtC,CAAC,CAAC;IAEH;;;;;;;;;;OAUG;IACoB,oBAAO,GAAuB,YAAY,CAAC,UAAU,CAAC;QAC3E,OAAO,EAAE,UAAU;QACnB,UAAU,EAAE,uCAAkB,CAAC,WAAW;QAC1C,eAAe,EAAE,iCAAe,CAAC,QAAQ;KAC1C,CAAC,CAAC;IAEH;;OAEG;IACW,2BAAc,GAAsC;QAChE,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,IAAI;QACjB,YAAY,CAAC,YAAY;QACzB,YAAY,CAAC,SAAS;QACtB,YAAY,CAAC,UAAU;QACvB,YAAY,CAAC,aAAa;QAC1B,YAAY,CAAC,OAAO;QACpB,YAAY,CAAC,YAAY;QACzB,YAAY,CAAC,UAAU;QACvB,YAAY,CAAC,QAAQ;QACrB,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,IAAI;QACjB,YAAY,CAAC,QAAQ;QACrB,YAAY,CAAC,oBAAoB;QACjC,YAAY,CAAC,KAAK;QAClB,YAAY,CAAC,cAAc;QAC3B,YAAY,CAAC,MAAM;QACnB,YAAY,CAAC,QAAQ;QACrB,YAAY,CAAC,OAAO;QACpB,YAAY,CAAC,OAAO;QACpB,YAAY,CAAC,MAAM;QACnB,YAAY,CAAC,GAAG;QAChB,YAAY,CAAC,MAAM;QACnB,YAAY,CAAC,SAAS;QACtB,YAAY,CAAC,OAAO;KACrB,CAAC;IAKJ,mBAAC;CAAA,AA9fD,IA8fC;AA9fY,oCAAY", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport {\r\n  TSDocTagDefinition,\r\n  TSDocTagSyntaxKind,\r\n  type ITSDocTagDefinitionInternalParameters\r\n} from '../configuration/TSDocTagDefinition';\r\nimport { Standardization } from './Standardization';\r\n\r\n/**\r\n * Tags whose meaning is defined by the TSDoc standard.\r\n */\r\nexport class StandardTags {\r\n  /**\r\n   * (Discretionary)\r\n   *\r\n   * Suggested meaning: Designates that an API item's release stage is \"alpha\".\r\n   * It is intended to be used by third-party developers eventually, but has not\r\n   * yet been released.  The tooling may trim the declaration from a public release.\r\n   *\r\n   * @remarks\r\n   * Example implementations: API Extractor\r\n   */\r\n  public static readonly alpha: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@alpha',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Discretionary\r\n  });\r\n\r\n  /**\r\n   * (Discretionary)\r\n   *\r\n   * Suggested meaning: Designates that an API item's release stage is \"beta\".\r\n   * It has been released to third-party developers experimentally for the purpose of\r\n   * collecting feedback.  The API should not be used in production, because its contract may\r\n   * change without notice.  The tooling may trim the declaration from a public release,\r\n   * but may include it in a developer preview release.\r\n   *\r\n   * @remarks\r\n   * Example implementations: API Extractor\r\n   *\r\n   * Synonyms: `@experimental`\r\n   */\r\n  public static readonly beta: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@beta',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Discretionary\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * ECMAScript decorators are sometimes an important part of an API contract.\r\n   * However, today the TypeScript compiler does not represent decorators in the\r\n   * .d.ts output files used by API consumers.  The `@decorator` tag provides a workaround,\r\n   * enabling a decorator expressions to be quoted in a doc comment.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * class Book {\r\n   *   /**\r\n   *    * The title of the book.\r\n   *    * @decorator `@jsonSerialized`\r\n   *    * @decorator `@jsonFormat(JsonFormats.Url)`\r\n   *    *\r\n   *+/\r\n   *   @jsonSerialized\r\n   *   @jsonFormat(JsonFormats.Url)\r\n   *   public website: string;\r\n   * }\r\n   * ```\r\n   */\r\n  public static readonly decorator: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@decorator',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    allowMultiple: true,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * This block tag is used to document the default value for a field or property,\r\n   * if a value is not assigned explicitly.\r\n   *\r\n   * @remarks\r\n   * This tag should only be used with fields or properties that are members of a class or interface.\r\n   */\r\n  public static readonly defaultValue: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@defaultValue',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * This block tag communicates that an API item is no longer supported and may be removed\r\n   * in a future release.  The `@deprecated` tag is followed by a sentence describing\r\n   * the recommended alternative.  It recursively applies to members of the container.\r\n   * For example, if a class is deprecated, then so are all of its members.\r\n   */\r\n  public static readonly deprecated: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@deprecated',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * When applied to a class or interface property, this indicates that the property\r\n   * returns an event object that event handlers can be attached to.  The event-handling\r\n   * API is implementation-defined, but typically the property return type would be a class\r\n   * with members such as `addHandler()` and `removeHandler()`.  A documentation tool can\r\n   * display such properties under an \"Events\" heading instead of the usual \"Properties\" heading.\r\n   */\r\n  public static readonly eventProperty: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@eventProperty',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * Indicates a documentation section that should be presented as an example\r\n   * illustrating how to use the API.  It may include a code sample.\r\n   */\r\n  public static readonly example: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@example',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    allowMultiple: true,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Discretionary)\r\n   *\r\n   * Suggested meaning:  Same semantics as `@beta`, but used by tools that don't support\r\n   * an `@alpha` release stage.\r\n   *\r\n   * @remarks\r\n   * Example implementations: Angular API documenter\r\n   *\r\n   * Synonyms: `@beta`\r\n   */\r\n  public static readonly experimental: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@experimental',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Discretionary\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * This inline tag is used to automatically generate an API item's documentation by\r\n   * copying it from another API item.  The inline tag parameter contains a reference\r\n   * to the other item, which may be an unrelated class, or even an import from a\r\n   * separate NPM package.\r\n   *\r\n   * @remarks\r\n   * What gets copied\r\n   *\r\n   * The `@inheritDoc` tag does not copy the entire comment body. Only the following\r\n   * components are copied:\r\n   * - summary section\r\n   * - `@remarks` block\r\n   * - `@params` blocks\r\n   * - `@typeParam` blocks\r\n   * - `@returns` block\r\n   * Other tags such as `@defaultValue` or `@example` are not copied, and need to be\r\n   * explicitly included after the `@inheritDoc` tag.\r\n   *\r\n   * TODO: The notation for API item references is still being standardized.  See this issue:\r\n   * https://github.com/microsoft/tsdoc/issues/9\r\n   */\r\n  public static readonly inheritDoc: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@inheritDoc',\r\n    syntaxKind: TSDocTagSyntaxKind.InlineTag,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Discretionary)\r\n   *\r\n   * Suggested meaning:  Designates that an API item is not planned to be used by\r\n   * third-party developers.  The tooling may trim the declaration from a public release.\r\n   * In some implementations, certain designated packages may be allowed to consume\r\n   * internal API items, e.g. because the packages are components of the same product.\r\n   *\r\n   * @remarks\r\n   * Example implementations: API Extractor\r\n   */\r\n  public static readonly internal: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@internal',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Discretionary\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * The `{@label}` inline tag is used to label a declaration, so that it can be referenced\r\n   * using a selector in the TSDoc declaration reference notation.\r\n   *\r\n   * @remarks\r\n   * TODO: The `{@label}` notation is still being standardized.  See this issue:\r\n   * https://github.com/microsoft/tsdoc/issues/9\r\n   */\r\n  public static readonly label: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@label',\r\n    syntaxKind: TSDocTagSyntaxKind.InlineTag,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * The `{@link}` inline tag is used to create hyperlinks to other pages in a\r\n   * documentation system or general internet URLs.  In particular, it supports\r\n   * expressions for referencing API items.\r\n   *\r\n   * @remarks\r\n   * TODO: The `{@link}` notation is still being standardized.  See this issue:\r\n   * https://github.com/microsoft/tsdoc/issues/9\r\n   */\r\n  public static readonly link: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@link',\r\n    syntaxKind: TSDocTagSyntaxKind.InlineTag,\r\n    allowMultiple: true,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * This modifier has similar semantics to the `override` keyword in C# or Java.\r\n   * For a member function or property, explicitly indicates that this definition\r\n   * is overriding (i.e. redefining) the definition inherited from the base class.\r\n   * The base class definition would normally be marked as `virtual`.\r\n   *\r\n   * @remarks\r\n   * A documentation tool may enforce that the `@virtual`, `@override`, and/or `@sealed`\r\n   * modifiers are consistently applied, but this is not required by the TSDoc standard.\r\n   */\r\n  public static readonly override: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@override',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * Used to indicate a doc comment that describes an entire NPM package (as opposed\r\n   * to an individual API item belonging to that package).  The `@packageDocumentation` comment\r\n   * is found in the *.d.ts file that acts as the entry point for the package, and it\r\n   * should be the first `/**` comment encountered in that file.  A comment containing a\r\n   * `@packageDocumentation` tag should never be used to describe an individual API item.\r\n   */\r\n  public static readonly packageDocumentation: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@packageDocumentation',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * Used to document a function parameter.  The `@param` tag is followed by a parameter\r\n   * name, followed by a hyphen, followed by a description.  The TSDoc parser recognizes\r\n   * this syntax and will extract it into a DocParamBlock node.\r\n   */\r\n  public static readonly param: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@param',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    allowMultiple: true,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * Starts a section of additional documentation content that is not intended for a\r\n   * public audience.  A tool must omit this entire section from the API reference web site,\r\n   * generated *.d.ts file, and any other outputs incorporating the content.\r\n   */\r\n  public static readonly privateRemarks: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@privateRemarks',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Discretionary)\r\n   *\r\n   * Suggested meaning: Designates that an API item's release stage is \"public\".\r\n   * It has been officially released to third-party developers, and its signature is\r\n   * guaranteed to be stable (e.g. following Semantic Versioning rules).\r\n   *\r\n   * @remarks\r\n   * Example implementations: API Extractor\r\n   */\r\n  public static readonly public: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@public',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Discretionary\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * This modifier tag indicates that an API item should be documented as being read-only,\r\n   * even if the TypeScript type system may indicate otherwise.  For example, suppose a\r\n   * class property has a setter function that always throws an exception explaining that\r\n   * the property cannot be assigned; in this situation, the `@readonly` modifier can be\r\n   * added so that the property is shown as read-only in the documentation.\r\n   *\r\n   * @remarks\r\n   * Example implementations: API Extractor\r\n   */\r\n  public static readonly readonly: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@readonly',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * The main documentation for an API item is separated into a brief \"summary\" section,\r\n   * optionally followed by a more detailed \"remarks\" section.  On a documentation web site,\r\n   * index pages (e.g. showing members of a class) will show only the brief summaries,\r\n   * whereas a detail pages (e.g. describing a single member) will show the summary followed\r\n   * by the remarks.  The `@remarks` block tag ends the summary section, and begins the\r\n   * remarks section for a doc comment.\r\n   */\r\n  public static readonly remarks: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@remarks',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * Used to document the return value for a function.\r\n   */\r\n  public static readonly returns: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@returns',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * This modifier has similar semantics to the `sealed` keyword in C# or Java.\r\n   * For a class, indicates that subclasses must not inherit from the class.\r\n   * For a member function or property, indicates that subclasses must not override\r\n   * (i.e. redefine) the member.\r\n   *\r\n   * @remarks\r\n   * A documentation tool may enforce that the `@virtual`, `@override`, and/or `@sealed`\r\n   * modifiers are consistently applied, but this is not required by the TSDoc standard.\r\n   */\r\n  public static readonly sealed: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@sealed',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * Used to build a list of references to an API item or other resource that may be related to the\r\n   * current item.\r\n   *\r\n   * @remarks\r\n   *\r\n   * For example:\r\n   *\r\n   * ```ts\r\n   * /**\r\n   *  * Parses a string containing a Uniform Resource Locator (URL).\r\n   *  * @see {@link ParsedUrl} for the returned data structure\r\n   *  * @see {@link https://tools.ietf.org/html/rfc1738|RFC 1738}\r\n   *  * for syntax\r\n   *  * @see your developer SDK for code samples\r\n   *  * @param url - the string to be parsed\r\n   *  * @returns the parsed result\r\n   *  &#42;/\r\n   * function parseURL(url: string): ParsedUrl;\r\n   * ```\r\n   *\r\n   * `@see` is a block tag.  Each block becomes an item in the list of references.  For example, a documentation\r\n   * system might render the above blocks as follows:\r\n   *\r\n   * ```markdown\r\n   * `function parseURL(url: string): ParsedUrl;`\r\n   *\r\n   * Parses a string containing a Uniform Resource Locator (URL).\r\n   *\r\n   * ## See Also\r\n   * - ParsedUrl for the returned data structure\r\n   * - RFC 1738 for syntax\r\n   * - your developer SDK for code samples\r\n   * ```\r\n   *\r\n   * NOTE: JSDoc attempts to automatically hyperlink the text immediately after `@see`.  Because this is ambiguous\r\n   * with plain text, TSDoc instead requires an explicit `{@link}` tag to make hyperlinks.\r\n   */\r\n  public static readonly see: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@see',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * Used to document an exception type that may be thrown by a function or property.\r\n   *\r\n   * @remarks\r\n   *\r\n   * A separate `@throws` block should be used to document each exception type.  This tag is for informational\r\n   * purposes only, and does not restrict other types from being thrown.  It is suggested, but not required,\r\n   * for the `@throws` block to start with a line containing only the name of the exception.\r\n   *\r\n   * For example:\r\n   *\r\n   * ```ts\r\n   * /**\r\n   *  * Retrieves metadata about a book from the catalog.\r\n   *  *\r\n   *  * @param isbnCode - the ISBN number for the book\r\n   *  * @returns the retrieved book object\r\n   *  *\r\n   *  * @throws {@link IsbnSyntaxError}\r\n   *  * This exception is thrown if the input is not a valid ISBN number.\r\n   *  *\r\n   *  * @throws {@link book-lib#BookNotFoundError}\r\n   *  * Thrown if the ISBN number is valid, but no such book exists in the catalog.\r\n   *  *\r\n   *  * @public\r\n   *  &#42;/\r\n   * function fetchBookByIsbn(isbnCode: string): Book;\r\n   * ```\r\n   */\r\n  public static readonly throws: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@throws',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    allowMultiple: true,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * (Core)\r\n   *\r\n   * Used to document a generic parameter.  The `@typeParam` tag is followed by a parameter\r\n   * name, followed by a hyphen, followed by a description.  The TSDoc parser recognizes\r\n   * this syntax and will extract it into a DocParamBlock node.\r\n   */\r\n  public static readonly typeParam: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@typeParam',\r\n    syntaxKind: TSDocTagSyntaxKind.BlockTag,\r\n    allowMultiple: true,\r\n    standardization: Standardization.Core\r\n  });\r\n\r\n  /**\r\n   * (Extended)\r\n   *\r\n   * This modifier has similar semantics to the `virtual` keyword in C# or Java.\r\n   * For a member function or property, explicitly indicates that subclasses may override\r\n   * (i.e. redefine) the member.\r\n   *\r\n   * @remarks\r\n   * A documentation tool may enforce that the `@virtual`, `@override`, and/or `@sealed`\r\n   * modifiers are consistently applied, but this is not required by the TSDoc standard.\r\n   */\r\n  public static readonly virtual: TSDocTagDefinition = StandardTags._defineTag({\r\n    tagName: '@virtual',\r\n    syntaxKind: TSDocTagSyntaxKind.ModifierTag,\r\n    standardization: Standardization.Extended\r\n  });\r\n\r\n  /**\r\n   * Returns the full list of all core tags.\r\n   */\r\n  public static allDefinitions: ReadonlyArray<TSDocTagDefinition> = [\r\n    StandardTags.alpha,\r\n    StandardTags.beta,\r\n    StandardTags.defaultValue,\r\n    StandardTags.decorator,\r\n    StandardTags.deprecated,\r\n    StandardTags.eventProperty,\r\n    StandardTags.example,\r\n    StandardTags.experimental,\r\n    StandardTags.inheritDoc,\r\n    StandardTags.internal,\r\n    StandardTags.label,\r\n    StandardTags.link,\r\n    StandardTags.override,\r\n    StandardTags.packageDocumentation,\r\n    StandardTags.param,\r\n    StandardTags.privateRemarks,\r\n    StandardTags.public,\r\n    StandardTags.readonly,\r\n    StandardTags.remarks,\r\n    StandardTags.returns,\r\n    StandardTags.sealed,\r\n    StandardTags.see,\r\n    StandardTags.throws,\r\n    StandardTags.typeParam,\r\n    StandardTags.virtual\r\n  ];\r\n\r\n  private static _defineTag(parameters: ITSDocTagDefinitionInternalParameters): TSDocTagDefinition {\r\n    return new TSDocTagDefinition(parameters);\r\n  }\r\n}\r\n"]}