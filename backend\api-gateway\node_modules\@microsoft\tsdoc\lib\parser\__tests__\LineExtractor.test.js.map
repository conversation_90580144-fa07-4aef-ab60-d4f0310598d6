{"version": 3, "file": "LineExtractor.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/LineExtractor.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,SAAS,qBAAqB,CAAC,MAAc;IAC3C,IAAM,WAAW,GAAgB,IAAI,WAAW,EAAE,CAAC;IACnD,IAAM,aAAa,GAAkB,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IACrE,MAAM,CAAC;QACL,MAAM,EAAE,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC;QACtC,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QACtE,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAvC,CAAuC,CAAC;QACjF,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAC,OAAO,IAAK,OAAA,OAAO,CAAC,IAAI,EAAZ,CAAY,CAAC;KACvE,CAAC,CAAC,eAAe,EAAE,CAAC;AACvB,CAAC;AAED,IAAI,CAAC,0BAA0B,EAAE;IAC/B,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI;IACpC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;IACtC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI;IACvC,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;IAC1C,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI;IACxC,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;IAC1C,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;IAC1C,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI;IAC3C,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;IAC1C,qBAAqB,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK;IAC7C,qBAAqB,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK;IAC7C,qBAAqB,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK;IACjD,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK;IAC9C,qBAAqB,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK;IAChD,qBAAqB,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK;IACjD,qBAAqB,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK;IACnD,qBAAqB,CAAC,wBAAwB,CAAC,CAAC,CAAC,KAAK;IACtD,qBAAqB,CAAC,2BAA2B,CAAC,CAAC,CAAC,KAAK;IACzD,qBAAqB,CACnB;QACE,KAAK;QACL,WAAW;QACX,UAAU;QACV,QAAQ;QACR,SAAS;KACV,CAAC,IAAI,CAAC,MAAM,CAAC,CACf,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,kEAAkE;AAClE,IAAI,CAAC,gBAAgB,EAAE;IACrB,qBAAqB,CAAC,UAAU,CAAC,CAAC;IAClC,qBAAqB,CAAC,YAAY,CAAC,CAAC;IACpC,qBAAqB,CAAC,YAAY,CAAC,CAAC;IACpC,qBAAqB,CAAC,4BAGf,CAAC,CAAC;AACX,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kBAAkB,EAAE;IACvB,qBAAqB,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAExF,qBAAqB,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9F,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,mBAAmB,EAAE;IACxB,qBAAqB,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACpE,qBAAqB,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAE9D,qEAAqE;IACrE,uBAAuB;IACvB,qBAAqB,CAAC,eAAe,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kBAAkB,EAAE;IACvB,qBAAqB,CAAC,EAAE,CAAC,CAAC;IAC1B,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC5B,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAC5B,qBAAqB,CAAC,aAAa,CAAC,CAAC;IACrC,qBAAqB,CAAC,UAAU,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { TSDocParser } from '../TSDocParser';\r\nimport { TestHelpers } from './TestHelpers';\r\nimport type { ParserContext } from '../ParserContext';\r\n\r\nfunction parseAndMatchSnapshot(buffer: string): void {\r\n  const tsdocParser: TSDocParser = new TSDocParser();\r\n  const parserContext: ParserContext = tsdocParser.parseString(buffer);\r\n  expect({\r\n    buffer: TestHelpers.getEscaped(buffer),\r\n    comment: TestHelpers.getEscaped(parserContext.commentRange.toString()),\r\n    lines: parserContext.lines.map((line) => TestHelpers.getEscaped(line.toString())),\r\n    logMessages: parserContext.log.messages.map((message) => message.text)\r\n  }).toMatchSnapshot();\r\n}\r\n\r\ntest('A. Whitespace variations', () => {\r\n  parseAndMatchSnapshot(`/***/`); // 1\r\n  parseAndMatchSnapshot(` /***/ `); // 2\r\n  parseAndMatchSnapshot(` /** */ `); // 3\r\n  parseAndMatchSnapshot(` /**\\n\\n*/ `); // 4\r\n  parseAndMatchSnapshot(` /**L1*/ `); // 5\r\n  parseAndMatchSnapshot(` /** L1 */ `); // 6\r\n  parseAndMatchSnapshot(` /**L1\\n*/ `); // 7\r\n  parseAndMatchSnapshot(` /**L1*\\n*/ `); // 8\r\n  parseAndMatchSnapshot(` /**\\nL1*/ `); // 9\r\n  parseAndMatchSnapshot(` /**\\n L1 */ `); // 10\r\n  parseAndMatchSnapshot(` /**\\nL1\\n*/ `); // 11\r\n  parseAndMatchSnapshot(` /**\\nL1\\n\\nL2*/ `); // 12\r\n  parseAndMatchSnapshot(` /**\\n*L1\\n*/ `); // 13\r\n  parseAndMatchSnapshot(` /**\\n * L1\\n*/ `); // 14\r\n  parseAndMatchSnapshot(` /**\\n * L1\\n */ `); // 15\r\n  parseAndMatchSnapshot(` /**L1\\n *L2\\nL3*/ `); // 16\r\n  parseAndMatchSnapshot(` /** L1\\n * L2\\n L3*/ `); // 17\r\n  parseAndMatchSnapshot(` /** L1 \\n * L2 \\n L3 */ `); // 18\r\n  parseAndMatchSnapshot(\r\n    [\r\n      // 19\r\n      '/**  L1  ',\r\n      ' *  L2  ',\r\n      '  L3  ',\r\n      '  L4 */'\r\n    ].join('\\r\\n')\r\n  );\r\n});\r\n\r\n// TODO: Special handling for these somewhat common ornamentations\r\ntest('B. Extra stars', () => {\r\n  parseAndMatchSnapshot(` /****/ `);\r\n  parseAndMatchSnapshot(` /**L1**/ `);\r\n  parseAndMatchSnapshot(` /***L1*/ `);\r\n  parseAndMatchSnapshot(`\r\n/*****\r\n **X**\r\n *****/ `);\r\n});\r\n\r\ntest('C. Missing stars', () => {\r\n  parseAndMatchSnapshot(['/**', '```', 'a', ' b', ' c ', '  d', '```', ' */'].join('\\n'));\r\n\r\n  parseAndMatchSnapshot(['/**', '```', 'ee', ' ff', ' gg ', '  hh', '```', ' */'].join('\\n'));\r\n});\r\n\r\ntest('D. Newline styles', () => {\r\n  parseAndMatchSnapshot(['', '/**', ' * L1', ' */', ''].join('\\r\\n'));\r\n  parseAndMatchSnapshot(['/**', 'L1', 'L2', '*/'].join('\\r\\n'));\r\n\r\n  // We currently don't support CR or LFCR, so a single \"\\r\" is treated\r\n  // as part of the line.\r\n  parseAndMatchSnapshot(`/** L \\r 1 */`);\r\n});\r\n\r\ntest('E. Parser errors', () => {\r\n  parseAndMatchSnapshot('');\r\n  parseAndMatchSnapshot('/*');\r\n  parseAndMatchSnapshot('//');\r\n  parseAndMatchSnapshot('/** L1\\n L2');\r\n  parseAndMatchSnapshot('/** L1 *');\r\n});\r\n"]}