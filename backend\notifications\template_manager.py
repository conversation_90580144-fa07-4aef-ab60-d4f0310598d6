"""
Template Manager
===============
مدير قوالب الإشعارات المتقدم
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import json
import re

import asyncpg
from ..shared.database.connection import DatabaseConnection
from .notification_service import NotificationChannel, NotificationType

logger = logging.getLogger(__name__)


class TemplateLanguage(Enum):
    """لغات القوالب"""
    ARABIC = "ar"
    ENGLISH = "en"
    FRENCH = "fr"


@dataclass
class TemplateVariable:
    """متغير القالب"""
    name: str
    type: str  # string, number, date, boolean
    description: str
    required: bool = True
    default_value: Optional[str] = None


@dataclass
class TemplateValidationResult:
    """نتيجة التحقق من القالب"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    variables_found: List[str]
    missing_variables: List[str]


class TemplateManager:
    """مدير قوالب الإشعارات"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Template validation patterns
        self.variable_pattern = re.compile(r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}')
        
        # Default templates
        self.default_templates = {
            # Transaction Success Templates
            (NotificationType.TRANSACTION_SUCCESS, NotificationChannel.EMAIL, "ar"): {
                "name": "نجح التحويل - بريد إلكتروني",
                "subject": "تم تنفيذ عملية التحويل بنجاح",
                "body": """
                مرحباً {user_name},
                
                تم تنفيذ عملية التحويل الخاصة بك بنجاح.
                
                تفاصيل العملية:
                - رقم العملية: {transaction_id}
                - نوع العملية: {transaction_type}
                - المبلغ: {amount} {currency}
                - التاريخ: {timestamp}
                
                شكراً لاستخدامك خدماتنا.
                
                فريق WS Transfir
                """,
                "variables": ["user_name", "transaction_id", "transaction_type", "amount", "currency", "timestamp"]
            },
            
            (NotificationType.TRANSACTION_SUCCESS, NotificationChannel.SMS, "ar"): {
                "name": "نجح التحويل - رسالة نصية",
                "subject": "",
                "body": "تم تنفيذ التحويل {transaction_id} بمبلغ {amount} {currency} بنجاح. WS Transfir",
                "variables": ["transaction_id", "amount", "currency"]
            },
            
            (NotificationType.TRANSACTION_SUCCESS, NotificationChannel.IN_APP, "ar"): {
                "name": "نجح التحويل - داخل التطبيق",
                "subject": "تم التحويل بنجاح",
                "body": "تم تنفيذ عملية التحويل {transaction_id} بمبلغ {amount} {currency} بنجاح.",
                "variables": ["transaction_id", "amount", "currency"]
            },
            
            # Transaction Failed Templates
            (NotificationType.TRANSACTION_FAILED, NotificationChannel.EMAIL, "ar"): {
                "name": "فشل التحويل - بريد إلكتروني",
                "subject": "فشل في تنفيذ عملية التحويل",
                "body": """
                مرحباً {user_name},
                
                نأسف لإبلاغك بأن عملية التحويل الخاصة بك لم تكتمل.
                
                تفاصيل العملية:
                - رقم العملية: {transaction_id}
                - نوع العملية: {transaction_type}
                - المبلغ: {amount} {currency}
                - التاريخ: {timestamp}
                
                يرجى المحاولة مرة أخرى أو التواصل مع خدمة العملاء.
                
                فريق WS Transfir
                """,
                "variables": ["user_name", "transaction_id", "transaction_type", "amount", "currency", "timestamp"]
            },
            
            # Security Alert Templates
            (NotificationType.SECURITY_ALERT, NotificationChannel.EMAIL, "ar"): {
                "name": "تنبيه أمني - بريد إلكتروني",
                "subject": "تنبيه أمني مهم",
                "body": """
                مرحباً {user_name},
                
                تم اكتشاف نشاط مشبوه في حسابك.
                
                تفاصيل التنبيه:
                - نوع التنبيه: {alert_type}
                - التاريخ: {timestamp}
                
                إذا لم تكن أنت من قام بهذا النشاط، يرجى تغيير كلمة المرور فوراً والتواصل معنا.
                
                فريق الأمان - WS Transfir
                """,
                "variables": ["user_name", "alert_type", "timestamp"]
            },
            
            (NotificationType.SECURITY_ALERT, NotificationChannel.SMS, "ar"): {
                "name": "تنبيه أمني - رسالة نصية",
                "subject": "",
                "body": "تنبيه أمني: تم اكتشاف {alert_type} في حسابك. إذا لم تكن أنت، غير كلمة المرور فوراً. WS Transfir",
                "variables": ["alert_type"]
            },
            
            # Account Verification Templates
            (NotificationType.ACCOUNT_VERIFICATION, NotificationChannel.EMAIL, "ar"): {
                "name": "تحقق من الحساب - بريد إلكتروني",
                "subject": "تحقق من حسابك في WS Transfir",
                "body": """
                مرحباً {user_name},
                
                مرحباً بك في WS Transfir!
                
                لإكمال تسجيلك، يرجى تأكيد بريدك الإلكتروني بالنقر على الرابط أدناه:
                
                {verification_link}
                
                هذا الرابط صالح لمدة 24 ساعة.
                
                إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذه الرسالة.
                
                فريق WS Transfir
                """,
                "variables": ["user_name", "verification_link"]
            },
            
            # KYC Update Templates
            (NotificationType.KYC_UPDATE, NotificationChannel.EMAIL, "ar"): {
                "name": "تحديث KYC - بريد إلكتروني",
                "subject": "تحديث حالة التحقق من الهوية",
                "body": """
                مرحباً {user_name},
                
                تم تحديث حالة التحقق من هويتك.
                
                الحالة الجديدة: {kyc_status}
                المستوى: {kyc_level}
                
                {additional_message}
                
                فريق WS Transfir
                """,
                "variables": ["user_name", "kyc_status", "kyc_level", "additional_message"]
            },
            
            # Agent Commission Templates
            (NotificationType.AGENT_COMMISSION, NotificationChannel.EMAIL, "ar"): {
                "name": "عمولة الوكيل - بريد إلكتروني",
                "subject": "تم إضافة عمولة جديدة",
                "body": """
                مرحباً {agent_name},
                
                تم إضافة عمولة جديدة إلى حسابك.
                
                تفاصيل العمولة:
                - المبلغ: {commission_amount} {currency}
                - نوع العمولة: {commission_type}
                - من المعاملة: {transaction_id}
                - التاريخ: {timestamp}
                
                إجمالي عمولاتك هذا الشهر: {monthly_total} {currency}
                
                فريق WS Transfir
                """,
                "variables": ["agent_name", "commission_amount", "currency", "commission_type", "transaction_id", "timestamp", "monthly_total"]
            },
            
            # System Maintenance Templates
            (NotificationType.SYSTEM_MAINTENANCE, NotificationChannel.EMAIL, "ar"): {
                "name": "صيانة النظام - بريد إلكتروني",
                "subject": "{title}",
                "body": """
                مرحباً,
                
                {message}
                
                نعتذر عن أي إزعاج قد يسببه هذا.
                
                فريق WS Transfir
                """,
                "variables": ["title", "message"]
            },
            
            # Wallet Balance Templates
            (NotificationType.WALLET_BALANCE, NotificationChannel.SMS, "ar"): {
                "name": "رصيد المحفظة - رسالة نصية",
                "subject": "",
                "body": "رصيدك الحالي: {balance} {currency}. آخر معاملة: {last_transaction_amount} {currency}. WS Transfir",
                "variables": ["balance", "currency", "last_transaction_amount"]
            }
        }
    
    async def create_template(
        self,
        name: str,
        notification_type: NotificationType,
        channel: NotificationChannel,
        language: TemplateLanguage,
        subject_template: str,
        body_template: str,
        variables: List[TemplateVariable],
        created_by: str,
        description: str = None
    ) -> str:
        """إنشاء قالب جديد"""
        try:
            logger.info(f"📝 Creating template: {name}")
            
            # Validate template
            validation_result = self.validate_template(subject_template, body_template, variables)
            if not validation_result.is_valid:
                raise ValueError(f"Template validation failed: {', '.join(validation_result.errors)}")
            
            # Generate template ID
            template_id = f"tpl_{notification_type.value}_{channel.value}_{language.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            async with self.db_connection.get_connection() as conn:
                # Insert template
                template_query = """
                    INSERT INTO notification_templates (
                        id, name, notification_type, channel, language,
                        subject_template, body_template, variables,
                        description, is_active, created_by
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    RETURNING id
                """
                
                variable_names = [var.name for var in variables]
                
                result = await conn.fetchval(
                    template_query,
                    template_id,
                    name,
                    notification_type.value,
                    channel.value,
                    language.value,
                    subject_template,
                    body_template,
                    variable_names,
                    description,
                    True,
                    created_by
                )
                
                # Insert template variables
                for variable in variables:
                    var_query = """
                        INSERT INTO template_variables (
                            template_id, variable_name, variable_type,
                            description, is_required, default_value
                        ) VALUES ($1, $2, $3, $4, $5, $6)
                    """
                    
                    await conn.execute(
                        var_query,
                        template_id,
                        variable.name,
                        variable.type,
                        variable.description,
                        variable.required,
                        variable.default_value
                    )
                
                logger.info(f"✅ Template created successfully: {template_id}")
                return template_id
                
        except Exception as e:
            logger.error(f"❌ Failed to create template: {e}")
            raise
    
    async def update_template(
        self,
        template_id: str,
        name: str = None,
        subject_template: str = None,
        body_template: str = None,
        variables: List[TemplateVariable] = None,
        updated_by: str = None,
        description: str = None,
        is_active: bool = None
    ) -> bool:
        """تحديث قالب موجود"""
        try:
            logger.info(f"📝 Updating template: {template_id}")
            
            async with self.db_connection.get_connection() as conn:
                # Get current template
                current_template = await conn.fetchrow(
                    "SELECT * FROM notification_templates WHERE id = $1",
                    template_id
                )
                
                if not current_template:
                    raise ValueError(f"Template not found: {template_id}")
                
                # Prepare update fields
                update_fields = []
                params = []
                param_count = 0
                
                if name is not None:
                    param_count += 1
                    update_fields.append(f"name = ${param_count}")
                    params.append(name)
                
                if subject_template is not None:
                    param_count += 1
                    update_fields.append(f"subject_template = ${param_count}")
                    params.append(subject_template)
                
                if body_template is not None:
                    param_count += 1
                    update_fields.append(f"body_template = ${param_count}")
                    params.append(body_template)
                
                if variables is not None:
                    # Validate template with new variables
                    subject = subject_template or current_template['subject_template']
                    body = body_template or current_template['body_template']
                    validation_result = self.validate_template(subject, body, variables)
                    
                    if not validation_result.is_valid:
                        raise ValueError(f"Template validation failed: {', '.join(validation_result.errors)}")
                    
                    param_count += 1
                    update_fields.append(f"variables = ${param_count}")
                    params.append([var.name for var in variables])
                
                if description is not None:
                    param_count += 1
                    update_fields.append(f"description = ${param_count}")
                    params.append(description)
                
                if is_active is not None:
                    param_count += 1
                    update_fields.append(f"is_active = ${param_count}")
                    params.append(is_active)
                
                if updated_by is not None:
                    param_count += 1
                    update_fields.append(f"updated_by = ${param_count}")
                    params.append(updated_by)
                
                # Always update timestamp
                param_count += 1
                update_fields.append(f"updated_at = ${param_count}")
                params.append(datetime.now())
                
                # Add template_id for WHERE clause
                param_count += 1
                params.append(template_id)
                
                if update_fields:
                    update_query = f"""
                        UPDATE notification_templates 
                        SET {', '.join(update_fields)}
                        WHERE id = ${param_count}
                    """
                    
                    await conn.execute(update_query, *params)
                
                # Update variables if provided
                if variables is not None:
                    # Delete existing variables
                    await conn.execute(
                        "DELETE FROM template_variables WHERE template_id = $1",
                        template_id
                    )
                    
                    # Insert new variables
                    for variable in variables:
                        var_query = """
                            INSERT INTO template_variables (
                                template_id, variable_name, variable_type,
                                description, is_required, default_value
                            ) VALUES ($1, $2, $3, $4, $5, $6)
                        """
                        
                        await conn.execute(
                            var_query,
                            template_id,
                            variable.name,
                            variable.type,
                            variable.description,
                            variable.required,
                            variable.default_value
                        )
                
                logger.info(f"✅ Template updated successfully: {template_id}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to update template: {e}")
            return False
    
    async def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على قالب بالمعرف"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Get template
                template_query = """
                    SELECT * FROM notification_templates WHERE id = $1
                """
                
                template = await conn.fetchrow(template_query, template_id)
                
                if not template:
                    return None
                
                # Get template variables
                variables_query = """
                    SELECT * FROM template_variables WHERE template_id = $1
                    ORDER BY variable_name
                """
                
                variables = await conn.fetch(variables_query, template_id)
                
                return {
                    "id": template["id"],
                    "name": template["name"],
                    "notification_type": template["notification_type"],
                    "channel": template["channel"],
                    "language": template["language"],
                    "subject_template": template["subject_template"],
                    "body_template": template["body_template"],
                    "description": template["description"],
                    "is_active": template["is_active"],
                    "created_at": template["created_at"].isoformat(),
                    "updated_at": template["updated_at"].isoformat(),
                    "created_by": template["created_by"],
                    "updated_by": template["updated_by"],
                    "variables": [
                        {
                            "name": var["variable_name"],
                            "type": var["variable_type"],
                            "description": var["description"],
                            "required": var["is_required"],
                            "default_value": var["default_value"]
                        }
                        for var in variables
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get template: {e}")
            return None
    
    async def list_templates(
        self,
        notification_type: NotificationType = None,
        channel: NotificationChannel = None,
        language: TemplateLanguage = None,
        is_active: bool = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """قائمة القوالب مع الفلترة"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = []
                params = []
                param_count = 0
                
                if notification_type:
                    param_count += 1
                    where_conditions.append(f"notification_type = ${param_count}")
                    params.append(notification_type.value)
                
                if channel:
                    param_count += 1
                    where_conditions.append(f"channel = ${param_count}")
                    params.append(channel.value)
                
                if language:
                    param_count += 1
                    where_conditions.append(f"language = ${param_count}")
                    params.append(language.value)
                
                if is_active is not None:
                    param_count += 1
                    where_conditions.append(f"is_active = ${param_count}")
                    params.append(is_active)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Add limit
                param_count += 1
                params.append(limit)
                
                query = f"""
                    SELECT 
                        id, name, notification_type, channel, language,
                        subject_template, body_template, description,
                        is_active, created_at, updated_at, created_by
                    FROM notification_templates 
                    WHERE {where_clause}
                    ORDER BY created_at DESC
                    LIMIT ${param_count}
                """
                
                templates = await conn.fetch(query, *params)
                
                return [
                    {
                        "id": template["id"],
                        "name": template["name"],
                        "notification_type": template["notification_type"],
                        "channel": template["channel"],
                        "language": template["language"],
                        "subject_template": template["subject_template"][:100] + "..." if len(template["subject_template"]) > 100 else template["subject_template"],
                        "body_template": template["body_template"][:200] + "..." if len(template["body_template"]) > 200 else template["body_template"],
                        "description": template["description"],
                        "is_active": template["is_active"],
                        "created_at": template["created_at"].isoformat(),
                        "updated_at": template["updated_at"].isoformat(),
                        "created_by": template["created_by"]
                    }
                    for template in templates
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to list templates: {e}")
            return []
    
    async def delete_template(self, template_id: str, deleted_by: str) -> bool:
        """حذف قالب"""
        try:
            logger.info(f"🗑️ Deleting template: {template_id}")
            
            async with self.db_connection.get_connection() as conn:
                async with conn.transaction():
                    # Soft delete template
                    template_result = await conn.execute(
                        """
                        UPDATE notification_templates 
                        SET is_active = false, 
                            updated_at = CURRENT_TIMESTAMP,
                            updated_by = $2
                        WHERE id = $1
                        """,
                        template_id,
                        deleted_by
                    )
                    
                    # Delete template variables
                    await conn.execute(
                        "DELETE FROM template_variables WHERE template_id = $1",
                        template_id
                    )
                    
                    if template_result == "UPDATE 1":
                        logger.info(f"✅ Template deleted successfully: {template_id}")
                        return True
                    else:
                        logger.warning(f"⚠️ Template not found: {template_id}")
                        return False
                
        except Exception as e:
            logger.error(f"❌ Failed to delete template: {e}")
            return False
    
    def validate_template(
        self,
        subject_template: str,
        body_template: str,
        variables: List[TemplateVariable]
    ) -> TemplateValidationResult:
        """التحقق من صحة القالب"""
        try:
            errors = []
            warnings = []
            
            # Find all variables in templates
            subject_vars = set(self.variable_pattern.findall(subject_template))
            body_vars = set(self.variable_pattern.findall(body_template))
            template_vars = subject_vars.union(body_vars)
            
            # Get defined variable names
            defined_vars = set(var.name for var in variables)
            
            # Check for undefined variables in template
            undefined_vars = template_vars - defined_vars
            if undefined_vars:
                errors.append(f"Undefined variables in template: {', '.join(undefined_vars)}")
            
            # Check for unused defined variables
            unused_vars = defined_vars - template_vars
            if unused_vars:
                warnings.append(f"Defined but unused variables: {', '.join(unused_vars)}")
            
            # Check for required variables
            required_vars = set(var.name for var in variables if var.required)
            missing_required = required_vars - template_vars
            if missing_required:
                errors.append(f"Required variables not used in template: {', '.join(missing_required)}")
            
            # Basic template validation
            if not subject_template.strip() and not body_template.strip():
                errors.append("Both subject and body templates cannot be empty")
            
            # Check for malformed variable syntax
            malformed_vars = []
            for template in [subject_template, body_template]:
                # Find potential malformed variables (e.g., {var with space}, {123var})
                potential_vars = re.findall(r'\{([^}]*)\}', template)
                for var in potential_vars:
                    if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', var):
                        malformed_vars.append(var)
            
            if malformed_vars:
                errors.append(f"Malformed variable names: {', '.join(malformed_vars)}")
            
            return TemplateValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                variables_found=list(template_vars),
                missing_variables=list(undefined_vars)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to validate template: {e}")
            return TemplateValidationResult(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                variables_found=[],
                missing_variables=[]
            )
    
    async def preview_template(
        self,
        template_id: str,
        sample_data: Dict[str, Any]
    ) -> Dict[str, str]:
        """معاينة القالب مع بيانات تجريبية"""
        try:
            template = await self.get_template(template_id)
            if not template:
                raise ValueError(f"Template not found: {template_id}")
            
            # Render template with sample data
            subject = template["subject_template"]
            body = template["body_template"]
            
            # Replace variables
            for key, value in sample_data.items():
                placeholder = f"{{{key}}}"
                subject = subject.replace(placeholder, str(value))
                body = body.replace(placeholder, str(value))
            
            return {
                "subject": subject,
                "body": body,
                "template_id": template_id,
                "rendered_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to preview template: {e}")
            return {}
    
    async def initialize_default_templates(self, created_by: str = "system") -> int:
        """تهيئة القوالب الافتراضية"""
        try:
            logger.info("🔧 Initializing default templates")
            
            created_count = 0
            
            for (notification_type, channel, language), template_data in self.default_templates.items():
                # Check if template already exists
                existing = await self.list_templates(
                    notification_type=notification_type,
                    channel=channel,
                    language=TemplateLanguage(language)
                )
                
                if not existing:
                    # Create template variables
                    variables = [
                        TemplateVariable(
                            name=var_name,
                            type="string",  # Default type
                            description=f"Variable: {var_name}",
                            required=True
                        )
                        for var_name in template_data["variables"]
                    ]
                    
                    # Create template
                    template_id = await self.create_template(
                        name=template_data["name"],
                        notification_type=notification_type,
                        channel=channel,
                        language=TemplateLanguage(language),
                        subject_template=template_data["subject"],
                        body_template=template_data["body"],
                        variables=variables,
                        created_by=created_by,
                        description=f"Default template for {notification_type.value} via {channel.value}"
                    )
                    
                    if template_id:
                        created_count += 1
            
            logger.info(f"✅ Initialized {created_count} default templates")
            return created_count
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize default templates: {e}")
            return 0
    
    async def get_template_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات القوالب"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Overall statistics
                stats_query = """
                    SELECT 
                        COUNT(*) as total_templates,
                        COUNT(CASE WHEN is_active THEN 1 END) as active_templates,
                        COUNT(DISTINCT notification_type) as notification_types,
                        COUNT(DISTINCT channel) as channels,
                        COUNT(DISTINCT language) as languages
                    FROM notification_templates
                """
                
                stats = await conn.fetchrow(stats_query)
                
                # Templates by type
                type_query = """
                    SELECT 
                        notification_type,
                        COUNT(*) as count,
                        COUNT(CASE WHEN is_active THEN 1 END) as active_count
                    FROM notification_templates
                    GROUP BY notification_type
                    ORDER BY count DESC
                """
                
                type_stats = await conn.fetch(type_query)
                
                # Templates by channel
                channel_query = """
                    SELECT 
                        channel,
                        COUNT(*) as count,
                        COUNT(CASE WHEN is_active THEN 1 END) as active_count
                    FROM notification_templates
                    GROUP BY channel
                    ORDER BY count DESC
                """
                
                channel_stats = await conn.fetch(channel_query)
                
                return {
                    "overview": {
                        "total_templates": stats["total_templates"],
                        "active_templates": stats["active_templates"],
                        "notification_types": stats["notification_types"],
                        "channels": stats["channels"],
                        "languages": stats["languages"]
                    },
                    "by_type": [
                        {
                            "type": row["notification_type"],
                            "total": row["count"],
                            "active": row["active_count"]
                        }
                        for row in type_stats
                    ],
                    "by_channel": [
                        {
                            "channel": row["channel"],
                            "total": row["count"],
                            "active": row["active_count"]
                        }
                        for row in channel_stats
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get template statistics: {e}")
            return {}
