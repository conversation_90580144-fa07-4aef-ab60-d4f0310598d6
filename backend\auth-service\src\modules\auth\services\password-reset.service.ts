import {
  Injectable,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as bcrypt from 'bcrypt';
import { PasswordReset } from '../entities/password-reset.entity';
import { User } from '../../users/entities/user.entity';
import { EmailService } from '../../../shared/services/email.service';

export interface PasswordResetRequest {
  email: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

@Injectable()
export class PasswordResetService {
  private readonly logger = new Logger(PasswordResetService.name);
  private readonly resetTokenExpiry = 60 * 60 * 1000; // 1 hour
  private readonly maxResetAttempts = 5; // Max attempts per hour

  constructor(
    @InjectRepository(PasswordReset)
    private readonly passwordResetRepository: Repository<PasswordReset>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
  ) {}

  async requestPasswordReset(request: PasswordResetRequest): Promise<{ success: boolean; message: string }> {
    const { email, ipAddress, userAgent } = request;

    // Find user by email
    const user = await this.userRepository.findOne({
      where: { email: email.toLowerCase() },
    });

    // Always return success to prevent email enumeration
    const successMessage = 'إذا كان البريد الإلكتروني موجود في نظامنا، ستتلقى رسالة لإعادة تعيين كلمة المرور';

    if (!user) {
      this.logger.warn(`Password reset requested for non-existent email: ${email}`);
      return { success: true, message: successMessage };
    }

    // Check if user account is active
    if (!user.isActive) {
      this.logger.warn(`Password reset requested for inactive user: ${user.id}`);
      return { success: true, message: successMessage };
    }

    // Check rate limiting
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentAttempts = await this.passwordResetRepository.count({
      where: {
        userId: user.id,
        createdAt: MoreThan(oneHourAgo),
      },
    });

    if (recentAttempts >= this.maxResetAttempts) {
      this.logger.warn(`Too many password reset attempts for user: ${user.id}`);
      return { 
        success: false, 
        message: 'تم تجاوز الحد الأقصى لمحاولات إعادة تعيين كلمة المرور. يرجى المحاولة لاحقاً' 
      };
    }

    // Invalidate any existing active reset tokens
    await this.passwordResetRepository.update(
      {
        userId: user.id,
        isUsed: false,
        expiresAt: MoreThan(new Date()),
      },
      {
        isUsed: true,
        usedAt: new Date(),
      }
    );

    // Generate reset token
    const token = this.generateResetToken();
    const hashedToken = await this.hashToken(token);

    // Create password reset record
    const passwordReset = this.passwordResetRepository.create({
      userId: user.id,
      token: hashedToken,
      expiresAt: new Date(Date.now() + this.resetTokenExpiry),
      ipAddress,
      userAgent,
    });

    await this.passwordResetRepository.save(passwordReset);

    // Send reset email
    try {
      await this.sendPasswordResetEmail(user, token);
      this.logger.log(`Password reset email sent to user: ${user.id}`);
    } catch (error) {
      this.logger.error(`Failed to send password reset email to user: ${user.id}`, error.stack);
      // Don't reveal email sending failure to user
    }

    return { success: true, message: successMessage };
  }

  async confirmPasswordReset(confirmation: PasswordResetConfirm): Promise<{ success: boolean; message: string }> {
    const { token, newPassword, confirmPassword } = confirmation;

    // Validate passwords match
    if (newPassword !== confirmPassword) {
      throw new BadRequestException('كلمات المرور غير متطابقة');
    }

    // Validate password strength
    this.validatePasswordStrength(newPassword);

    // Find valid reset token
    const hashedToken = await this.hashToken(token);
    const passwordReset = await this.passwordResetRepository.findOne({
      where: {
        token: hashedToken,
        isUsed: false,
        expiresAt: MoreThan(new Date()),
      },
      relations: ['user'],
    });

    if (!passwordReset) {
      throw new BadRequestException('رمز إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية');
    }

    const user = passwordReset.user;

    // Check if user is still active
    if (!user.isActive) {
      throw new BadRequestException('الحساب غير نشط');
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update user password
    await this.userRepository.update(user.id, {
      password: hashedPassword,
      passwordChangedAt: new Date(),
    });

    // Mark reset token as used
    passwordReset.isUsed = true;
    passwordReset.usedAt = new Date();
    await this.passwordResetRepository.save(passwordReset);

    // Send confirmation email
    try {
      await this.sendPasswordChangeConfirmationEmail(user);
    } catch (error) {
      this.logger.error(`Failed to send password change confirmation email to user: ${user.id}`, error.stack);
    }

    this.logger.log(`Password reset completed for user: ${user.id}`);

    return {
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة',
    };
  }

  async validateResetToken(token: string): Promise<{ valid: boolean; userId?: string }> {
    const hashedToken = await this.hashToken(token);
    const passwordReset = await this.passwordResetRepository.findOne({
      where: {
        token: hashedToken,
        isUsed: false,
        expiresAt: MoreThan(new Date()),
      },
    });

    if (!passwordReset) {
      return { valid: false };
    }

    return {
      valid: true,
      userId: passwordReset.userId,
    };
  }

  async getPasswordResetHistory(userId: string, limit: number = 10): Promise<PasswordReset[]> {
    return this.passwordResetRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit,
      select: ['id', 'createdAt', 'expiresAt', 'isUsed', 'usedAt', 'ipAddress'],
    });
  }

  async cleanupExpiredTokens(): Promise<void> {
    const result = await this.passwordResetRepository.delete({
      expiresAt: { $lt: new Date() } as any,
    });

    if (result.affected && result.affected > 0) {
      this.logger.log(`Cleaned up ${result.affected} expired password reset tokens`);
    }
  }

  async getPasswordResetStats(): Promise<any> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const [
      totalRequests,
      dailyRequests,
      weeklyRequests,
      successfulResets,
      expiredTokens,
    ] = await Promise.all([
      this.passwordResetRepository.count(),
      this.passwordResetRepository.count({
        where: { createdAt: MoreThan(oneDayAgo) },
      }),
      this.passwordResetRepository.count({
        where: { createdAt: MoreThan(oneWeekAgo) },
      }),
      this.passwordResetRepository.count({
        where: { isUsed: true },
      }),
      this.passwordResetRepository.count({
        where: {
          isUsed: false,
          expiresAt: { $lt: new Date() } as any,
        },
      }),
    ]);

    const successRate = totalRequests > 0 ? (successfulResets / totalRequests) * 100 : 0;

    return {
      totalRequests,
      dailyRequests,
      weeklyRequests,
      successfulResets,
      expiredTokens,
      successRate: Math.round(successRate * 100) / 100,
    };
  }

  private generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private async hashToken(token: string): Promise<string> {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  private validatePasswordStrength(password: string): void {
    if (password.length < 8) {
      throw new BadRequestException('كلمة المرور يجب أن تكون على الأقل 8 أحرف');
    }

    if (!/(?=.*[a-z])/.test(password)) {
      throw new BadRequestException('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
    }

    if (!/(?=.*[A-Z])/.test(password)) {
      throw new BadRequestException('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
    }

    if (!/(?=.*\d)/.test(password)) {
      throw new BadRequestException('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
    }

    if (!/(?=.*[@$!%*?&])/.test(password)) {
      throw new BadRequestException('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
    }
  }

  private async sendPasswordResetEmail(user: User, token: string): Promise<void> {
    const resetUrl = `${this.configService.get('FRONTEND_URL')}/reset-password?token=${token}`;
    
    const emailData = {
      to: user.email,
      subject: 'إعادة تعيين كلمة المرور - WS Transfir',
      template: 'password-reset',
      context: {
        firstName: user.firstName,
        resetUrl,
        expiryTime: '60 دقيقة',
      },
    };

    await this.emailService.sendEmail(emailData);
  }

  private async sendPasswordChangeConfirmationEmail(user: User): Promise<void> {
    const emailData = {
      to: user.email,
      subject: 'تم تغيير كلمة المرور - WS Transfir',
      template: 'password-changed',
      context: {
        firstName: user.firstName,
        changeTime: new Date().toLocaleString('ar-SA'),
      },
    };

    await this.emailService.sendEmail(emailData);
  }
}
