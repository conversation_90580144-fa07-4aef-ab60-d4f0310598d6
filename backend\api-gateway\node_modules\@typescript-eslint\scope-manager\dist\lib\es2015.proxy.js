"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2015_proxy = void 0;
const base_config_1 = require("./base-config");
exports.es2015_proxy = {
    ProxyHandler: base_config_1.TYPE,
    ProxyConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=es2015.proxy.js.map