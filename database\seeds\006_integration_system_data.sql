-- =====================================================
-- Integration System Seed Data
-- Description: بذور بيانات نظام التكامل والواجهات الخارجية
-- =====================================================

-- =====================================================
-- Bank Accounts Sample Data
-- =====================================================

INSERT INTO bank_accounts (
    account_number, iban, bank_code, bank_name, account_holder, 
    currency, account_type, is_active
) VALUES 
-- Saudi Banks
('**********', 'SA0310000001**********23', 'RIBLSARI', 'Riyad Bank', 'WalletSystem Main Account', 'SAR', 'business', true),
('**********', 'SA030500000**********234', 'NCBKSAJE', 'National Commercial Bank', 'WalletSystem Operations', 'SAR', 'business', true),
('**********', 'SA031500000**********345', 'ALBISARI', 'Albilad Bank', 'WalletSystem Islamic Account', 'SAR', 'business', true),
('**********', 'SA032000000**********456', 'SMBCSAJE', 'SAMBA Financial Group', 'WalletSystem Treasury', 'SAR', 'business', true),
('**********', 'SA038000000**********567', 'ARABSARI', 'Arab National Bank', 'WalletSystem Reserve', 'SAR', 'business', true),

-- International Accounts
('USD**********', 'US64SVBKUS6S**********', 'SVBKUS6S', 'Silicon Valley Bank', 'WalletSystem USD Account', 'USD', 'business', true),
('EUR**********', '**********************', 'COBADEFF', 'Commerzbank', 'WalletSystem EUR Account', 'EUR', 'business', true),
('GBP**********', '**********************', 'NWBKGB2L', 'NatWest Bank', 'WalletSystem GBP Account', 'GBP', 'business', true),

-- Customer Test Accounts
('CUST001SAR', '************************', 'RIBLSARI', 'Riyad Bank', 'Ahmed Al-Rashid', 'SAR', 'personal', true),
('CUST002SAR', '************************', 'NCBKSAJE', 'National Commercial Bank', 'Fatima Al-Zahra', 'SAR', 'personal', true),
('CUST003SAR', '************************', 'ALBISARI', 'Albilad Bank', 'Mohammed Al-Otaibi', 'SAR', 'personal', true);

-- =====================================================
-- Partner Configurations Sample Data
-- =====================================================

INSERT INTO partner_configs (
    partner_id, partner_name, partner_type, api_version, auth_method, 
    rate_limit, api_key, secret_key, webhook_url, allowed_ips, metadata
) VALUES 
-- E-commerce Partners
('partner_ecom_001', 'Saudi E-Commerce Platform', 'merchant', 'v2', 'jwt', 'premium', 
 'pk_ecom_001_' || encode(gen_random_bytes(16), 'hex'), 
 'sk_ecom_001_' || encode(gen_random_bytes(32), 'hex'),
 'https://api.saudiecommerce.com/webhooks/walletsystem',
 ARRAY['************', '************'],
 '{"business_type": "retail", "monthly_volume": 5000000, "integration_date": "2024-01-15"}'::jsonb),

('partner_ecom_002', 'Gulf Online Marketplace', 'merchant', 'v2', 'api_key', 'standard',
 'pk_ecom_002_' || encode(gen_random_bytes(16), 'hex'),
 'sk_ecom_002_' || encode(gen_random_bytes(32), 'hex'),
 'https://webhooks.gulfonline.com/payments',
 ARRAY['*************', '*************'],
 '{"business_type": "marketplace", "monthly_volume": 2000000, "integration_date": "2024-02-01"}'::jsonb),

-- Fintech Partners
('partner_fintech_001', 'MENA Digital Wallet', 'fintech', 'v3', 'oauth2', 'enterprise',
 'pk_fintech_001_' || encode(gen_random_bytes(16), 'hex'),
 'sk_fintech_001_' || encode(gen_random_bytes(32), 'hex'),
 'https://api.menadigital.com/hooks/payments',
 ARRAY['**********', '192.0.2.31', '192.0.2.32'],
 '{"license_type": "full_banking", "monthly_volume": ********, "compliance_level": "tier1"}'::jsonb),

('partner_fintech_002', 'Islamic Finance Solutions', 'fintech', 'v2', 'jwt', 'premium',
 'pk_fintech_002_' || encode(gen_random_bytes(16), 'hex'),
 'sk_fintech_002_' || encode(gen_random_bytes(32), 'hex'),
 'https://api.islamicfinance.sa/webhooks',
 ARRAY['************', '************'],
 '{"sharia_compliant": true, "monthly_volume": 3000000, "certification": "AAOIFI"}'::jsonb),

-- Payment Processors
('partner_processor_001', 'Saudi Payment Gateway', 'payment_processor', 'v2', 'mutual_tls', 'enterprise',
 'pk_processor_001_' || encode(gen_random_bytes(16), 'hex'),
 'sk_processor_001_' || encode(gen_random_bytes(32), 'hex'),
 'https://secure.saudipayments.com/notifications',
 ARRAY['*************', '*************', '*************'],
 '{"processor_type": "acquiring", "monthly_volume": ********, "pci_level": "level1"}'::jsonb),

-- Government Partners
('partner_gov_001', 'Ministry of Finance', 'government', 'v1', 'mutual_tls', 'standard',
 'pk_gov_001_' || encode(gen_random_bytes(16), 'hex'),
 'sk_gov_001_' || encode(gen_random_bytes(32), 'hex'),
 'https://api.mof.gov.sa/webhooks/payments',
 ARRAY['**********', '**********'],
 '{"department": "digital_payments", "clearance_level": "confidential"}'::jsonb),

('partner_gov_002', 'ZATCA Integration', 'government', 'v2', 'api_key', 'basic',
 'pk_zatca_001_' || encode(gen_random_bytes(16), 'hex'),
 'sk_zatca_001_' || encode(gen_random_bytes(32), 'hex'),
 'https://api.zatca.gov.sa/notifications',
 ARRAY['************', '************'],
 '{"service_type": "tax_reporting", "compliance_required": true}'::jsonb),

-- Enterprise Partners
('partner_enterprise_001', 'Saudi Aramco', 'enterprise', 'v3', 'oauth2', 'enterprise',
 'pk_enterprise_001_' || encode(gen_random_bytes(16), 'hex'),
 'sk_enterprise_001_' || encode(gen_random_bytes(32), 'hex'),
 'https://payments.aramco.com/webhooks',
 ARRAY['198.51.100.80', '198.51.100.81', '198.51.100.82', '198.51.100.83'],
 '{"company_size": "large", "monthly_volume": *********, "industry": "energy"}'::jsonb);

-- =====================================================
-- Webhook Endpoints Sample Data
-- =====================================================

INSERT INTO webhook_endpoints (
    endpoint_id, partner_id, url, events, secret_key, signature_method,
    max_retries, retry_delay, timeout, metadata
) VALUES 
-- E-commerce webhooks
('wh_ecom_001_payments', 'partner_ecom_001', 'https://api.saudiecommerce.com/webhooks/payments',
 ARRAY['payment.created', 'payment.completed', 'payment.failed', 'refund.completed'],
 'wh_secret_' || encode(gen_random_bytes(32), 'hex'), 'hmac_sha256', 5, 120, 45,
 '{"priority": "high", "notification_email": "<EMAIL>"}'::jsonb),

('wh_ecom_001_transfers', 'partner_ecom_001', 'https://api.saudiecommerce.com/webhooks/transfers',
 ARRAY['transfer.created', 'transfer.completed', 'transfer.failed'],
 'wh_secret_' || encode(gen_random_bytes(32), 'hex'), 'hmac_sha256', 3, 60, 30,
 '{"priority": "medium", "notification_email": "<EMAIL>"}'::jsonb),

-- Fintech webhooks
('wh_fintech_001_all', 'partner_fintech_001', 'https://api.menadigital.com/hooks/all-events',
 ARRAY['payment.created', 'payment.completed', 'payment.failed', 'transfer.created', 'transfer.completed', 'balance.updated'],
 'wh_secret_' || encode(gen_random_bytes(32), 'hex'), 'hmac_sha512', 5, 180, 60,
 '{"priority": "critical", "backup_url": "https://backup.menadigital.com/hooks"}'::jsonb),

-- Payment processor webhooks
('wh_processor_001_status', 'partner_processor_001', 'https://secure.saudipayments.com/status-updates',
 ARRAY['payment.completed', 'payment.failed', 'settlement.completed'],
 'wh_secret_' || encode(gen_random_bytes(32), 'hex'), 'hmac_sha256', 10, 300, 90,
 '{"priority": "critical", "encryption": "required", "compliance": "pci_dss"}'::jsonb),

-- Government webhooks
('wh_gov_001_reports', 'partner_gov_001', 'https://api.mof.gov.sa/webhooks/financial-reports',
 ARRAY['settlement.completed', 'compliance.alert'],
 'wh_secret_' || encode(gen_random_bytes(32), 'hex'), 'hmac_sha256', 3, 600, 120,
 '{"priority": "high", "classification": "official", "retention_days": 2555}'::jsonb);

-- =====================================================
-- Sample Bank Transfers
-- =====================================================

INSERT INTO bank_transfers (
    id, from_account, to_account, amount, currency, fees, reference,
    description, beneficiary_name, beneficiary_bank, status, bank_reference
) VALUES 
-- Successful transfers
('bt_001_' || to_char(CURRENT_DATE, 'YYYYMMDD'), '**********', 'CUST001SAR', 5000.00, 'SAR', 15.00,
 'TXF-001-' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'Customer payout', 'Ahmed Al-Rashid', 'RIBLSARI',
 'completed', 'BNK' || to_char(CURRENT_DATE, 'YYYYMMDD') || '001'),

('bt_002_' || to_char(CURRENT_DATE, 'YYYYMMDD'), '**********', 'CUST002SAR', 2500.00, 'SAR', 10.00,
 'TXF-002-' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'Refund processing', 'Fatima Al-Zahra', 'NCBKSAJE',
 'completed', 'BNK' || to_char(CURRENT_DATE, 'YYYYMMDD') || '002'),

-- Pending transfers
('bt_003_' || to_char(CURRENT_DATE, 'YYYYMMDD'), '**********', 'CUST003SAR', 10000.00, 'SAR', 25.00,
 'TXF-003-' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'Large amount transfer', 'Mohammed Al-Otaibi', 'ALBISARI',
 'pending', 'BNK' || to_char(CURRENT_DATE, 'YYYYMMDD') || '003'),

-- International transfers
('bt_004_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'USD**********', '**********', 1000.00, 'USD', 50.00,
 'TXF-004-' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'USD to SAR conversion', 'WalletSystem Main Account', 'RIBLSARI',
 'processing', 'BNK' || to_char(CURRENT_DATE, 'YYYYMMDD') || '004');

-- =====================================================
-- Sample Accounting Entries
-- =====================================================

INSERT INTO accounting_entries (
    entry_id, transaction_id, account_code, account_name, debit_amount, credit_amount,
    description, reference, transaction_date, currency, tax_amount, tax_type
) VALUES 
-- Revenue entries
('entry_001_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'txn_rev_001', '1200', 'العملاء', 1150.00, 0.00,
 'Customer payment received', 'INV-000001', CURRENT_DATE, 'SAR', 0.00, null),
('entry_002_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'txn_rev_001', '4100', 'إيرادات الخدمات', 0.00, 1000.00,
 'Service revenue', 'INV-000001', CURRENT_DATE, 'SAR', 0.00, null),
('entry_003_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'txn_rev_001', '2300', 'ضريبة القيمة المضافة مستحقة', 0.00, 150.00,
 'VAT on service revenue', 'INV-000001', CURRENT_DATE, 'SAR', 150.00, 'vat'),

-- Fee income entries
('entry_004_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'txn_fee_001', '1100', 'النقد وما في حكمه', 575.00, 0.00,
 'Transaction fees collected', 'FEE-001', CURRENT_DATE, 'SAR', 0.00, null),
('entry_005_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'txn_fee_001', '4200', 'إيرادات الرسوم', 0.00, 500.00,
 'Transaction fee income', 'FEE-001', CURRENT_DATE, 'SAR', 0.00, null),
('entry_006_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'txn_fee_001', '2300', 'ضريبة القيمة المضافة مستحقة', 0.00, 75.00,
 'VAT on fee income', 'FEE-001', CURRENT_DATE, 'SAR', 75.00, 'vat'),

-- Operating expense entries
('entry_007_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'txn_exp_001', '5300', 'مصروفات تقنية', 2300.00, 0.00,
 'Cloud infrastructure costs', 'EXP-001', CURRENT_DATE, 'SAR', 0.00, null),
('entry_008_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'txn_exp_001', '1100', 'النقد وما في حكمه', 0.00, 2300.00,
 'Payment for cloud services', 'EXP-001', CURRENT_DATE, 'SAR', 0.00, null);

-- =====================================================
-- Sample Tax Calculations
-- =====================================================

INSERT INTO tax_calculations (
    tax_id, base_amount, tax_rate, tax_amount, tax_type, currency,
    calculation_date, is_inclusive, exemption_reason
) VALUES 
('tax_001_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 1000.00, 0.15, 150.00, 'vat', 'SAR', CURRENT_DATE, false, null),
('tax_002_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 500.00, 0.15, 75.00, 'vat', 'SAR', CURRENT_DATE, false, null),
('tax_003_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 10000.00, 0.05, 500.00, 'withholding', 'SAR', CURRENT_DATE, false, null),
('tax_004_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 5000.00, 0.00, 0.00, 'vat', 'SAR', CURRENT_DATE, false, 'Export services - zero rated');

-- =====================================================
-- Sample Invoices
-- =====================================================

INSERT INTO invoices (
    invoice_id, invoice_number, customer_id, customer_name, customer_vat_number,
    issue_date, due_date, subtotal, tax_amount, total_amount, currency,
    line_items, payment_terms, notes
) VALUES 
('inv_001_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'INV-000001', 'user_test_001', 'Ahmed Al-Rashid', '300000000000003',
 CURRENT_DATE, CURRENT_DATE + INTERVAL '30 days', 1000.00, 150.00, 1150.00, 'SAR',
 '[{"description": "Digital wallet services", "quantity": 1, "unit_price": 1000.00, "amount": 1000.00, "taxable": true, "tax_amount": 150.00}]'::jsonb,
 'Net 30', 'Monthly service fee for digital wallet platform'),

('inv_002_' || to_char(CURRENT_DATE, 'YYYYMMDD'), 'INV-000002', 'user_test_002', 'Fatima Al-Zahra', '300000000000004',
 CURRENT_DATE, CURRENT_DATE + INTERVAL '15 days', 500.00, 75.00, 575.00, 'SAR',
 '[{"description": "Transaction processing fees", "quantity": 100, "unit_price": 5.00, "amount": 500.00, "taxable": true, "tax_amount": 75.00}]'::jsonb,
 'Net 15', 'Transaction processing fees for Q1 2024');

-- =====================================================
-- Sample Standards Configurations
-- =====================================================

INSERT INTO standards_configs (
    standard, version, compliance_level, enabled_features, configuration
) VALUES 
('iso_20022', '2019', 'full', 
 ARRAY['pain.001.001.03', 'pacs.008.001.02', 'camt.053.001.02'],
 '{"validation_strict": true, "auto_correction": false, "supported_currencies": ["SAR", "USD", "EUR"]}'::jsonb),

('swift_mt', '2023', 'standard',
 ARRAY['MT103', 'MT202', 'MT900', 'MT910'],
 '{"field_validation": true, "format_checking": "strict", "supported_networks": ["SWIFT", "RTGS"]}'::jsonb),

('pci_dss', '4.0', 'advanced',
 ARRAY['requirement_1', 'requirement_3', 'requirement_4', 'requirement_8', 'requirement_11'],
 '{"scan_frequency": "weekly", "vulnerability_assessment": true, "penetration_testing": "quarterly"}'::jsonb),

('sama_regulations', '2024', 'full',
 ARRAY['cybersecurity', 'operational_risk', 'consumer_protection', 'aml_cft'],
 '{"reporting_frequency": "monthly", "audit_trail": true, "real_time_monitoring": true}'::jsonb);

-- =====================================================
-- Sample Message Validations
-- =====================================================

INSERT INTO message_validations (
    validation_id, message_type, format, is_valid, errors, warnings, validated_at
) VALUES 
('val_001_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'pain.001.001.03', 'xml', true, '[]'::jsonb, '[]'::jsonb, CURRENT_TIMESTAMP),
('val_002_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'MT103', 'mt', true, '[]'::jsonb, '["Field 71A is optional but recommended"]'::jsonb, CURRENT_TIMESTAMP),
('val_003_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'pacs.008.001.02', 'xml', false, 
 '["Missing required field: GrpHdr.MsgId", "Invalid amount format in CdtTrfTxInf.Amt.InstdAmt"]'::jsonb, 
 '[]'::jsonb, CURRENT_TIMESTAMP);

-- =====================================================
-- Sample Compliance Checks
-- =====================================================

INSERT INTO compliance_checks (
    check_id, standard, rule_name, description, severity, status, details, checked_at
) VALUES 
('chk_001_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'pci_dss', 'Requirement 1', 'Install and maintain a firewall configuration', 'critical', 'passed', 'Firewall rules reviewed and updated', CURRENT_TIMESTAMP),
('chk_002_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'pci_dss', 'Requirement 3', 'Protect stored cardholder data', 'critical', 'passed', 'Data encryption verified and compliant', CURRENT_TIMESTAMP),
('chk_003_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'pci_dss', 'Requirement 11', 'Regularly test security systems and processes', 'high', 'warning', 'Penetration testing scheduled for next month', CURRENT_TIMESTAMP),
('chk_004_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'sama_regulations', 'Cybersecurity Framework', 'Implement multi-factor authentication', 'high', 'passed', 'MFA implemented across all systems', CURRENT_TIMESTAMP),
('chk_005_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'sama_regulations', 'Consumer Protection', 'Provide clear terms and conditions', 'medium', 'passed', 'Terms and conditions reviewed and approved by legal team', CURRENT_TIMESTAMP);

-- =====================================================
-- Sample API Request Logs
-- =====================================================

INSERT INTO api_request_logs (
    request_id, partner_id, endpoint, method, headers, body, ip_address,
    user_agent, status_code, response_data, processing_time, completed_at
) VALUES 
('req_001_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'partner_ecom_001', '/api/v2/payments', 'POST',
 '{"Authorization": "Bearer jwt_token", "Content-Type": "application/json"}'::jsonb,
 '{"amount": 1000.00, "currency": "SAR", "customer_id": "cust_001"}'::jsonb,
 '************', 'SaudiEcommerce/1.0', 200,
 '{"success": true, "payment_id": "pay_001", "status": "completed"}'::jsonb,
 0.245, CURRENT_TIMESTAMP),

('req_002_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'partner_fintech_001', '/api/v3/transfers', 'POST',
 '{"Authorization": "Bearer oauth_token", "Content-Type": "application/json"}'::jsonb,
 '{"from_account": "acc_001", "to_account": "acc_002", "amount": 5000.00}'::jsonb,
 '**********', 'MENADigital/2.1', 201,
 '{"success": true, "transfer_id": "txf_001", "status": "processing"}'::jsonb,
 0.189, CURRENT_TIMESTAMP),

('req_003_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'partner_processor_001', '/api/v2/balances', 'GET',
 '{"Authorization": "Bearer api_key", "Accept": "application/json"}'::jsonb,
 '{}'::jsonb, '*************', 'SaudiPayments/3.0', 200,
 '{"balances": [{"account_id": "acc_main", "available": 50000.00, "currency": "SAR"}]}'::jsonb,
 0.067, CURRENT_TIMESTAMP);

-- =====================================================
-- Sample Webhook Deliveries
-- =====================================================

INSERT INTO webhook_deliveries (
    delivery_id, webhook_id, endpoint_id, partner_id, event, payload, status,
    attempts, response_status, delivered_at
) VALUES 
('del_001_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'wh_001', 'wh_ecom_001_payments', 'partner_ecom_001', 'payment.completed',
 '{"event": "payment.completed", "data": {"payment_id": "pay_001", "amount": 1000.00, "currency": "SAR"}, "timestamp": "' || CURRENT_TIMESTAMP::text || '"}'::jsonb,
 'delivered', 1, 200, CURRENT_TIMESTAMP),

('del_002_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'wh_002', 'wh_fintech_001_all', 'partner_fintech_001', 'transfer.created',
 '{"event": "transfer.created", "data": {"transfer_id": "txf_001", "amount": 5000.00, "status": "processing"}, "timestamp": "' || CURRENT_TIMESTAMP::text || '"}'::jsonb,
 'delivered', 1, 201, CURRENT_TIMESTAMP),

('del_003_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS'), 'wh_003', 'wh_processor_001_status', 'partner_processor_001', 'settlement.completed',
 '{"event": "settlement.completed", "data": {"settlement_id": "set_001", "amount": 25000.00, "currency": "SAR"}, "timestamp": "' || CURRENT_TIMESTAMP::text || '"}'::jsonb,
 'failed', 3, 500, null);

-- =====================================================
-- Update Statistics
-- =====================================================

-- Analyze tables for better query performance
ANALYZE bank_accounts;
ANALYZE bank_transfers;
ANALYZE partner_configs;
ANALYZE api_request_logs;
ANALYZE webhook_endpoints;
ANALYZE webhook_deliveries;
ANALYZE accounting_entries;
ANALYZE tax_calculations;
ANALYZE invoices;
ANALYZE standards_configs;
ANALYZE message_validations;
ANALYZE compliance_checks;

-- =====================================================
-- Seed Data Complete
-- =====================================================

-- Log seed completion
DO $$
BEGIN
    RAISE NOTICE 'Integration system seed data inserted successfully';
    RAISE NOTICE 'Bank accounts: %', (SELECT COUNT(*) FROM bank_accounts);
    RAISE NOTICE 'Partner configurations: %', (SELECT COUNT(*) FROM partner_configs);
    RAISE NOTICE 'Webhook endpoints: %', (SELECT COUNT(*) FROM webhook_endpoints);
    RAISE NOTICE 'Accounting entries: %', (SELECT COUNT(*) FROM accounting_entries);
    RAISE NOTICE 'Standards configurations: %', (SELECT COUNT(*) FROM standards_configs);
END $$;
