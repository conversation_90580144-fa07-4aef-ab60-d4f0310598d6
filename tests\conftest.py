"""
Pytest Configuration and Fixtures
=================================
إعدادات pytest والتركيبات المشتركة
"""

import os
import sys
import asyncio
import pytest
import logging
from datetime import datetime
from typing import Dict, Any, AsyncGenerator
from unittest.mock import Mock, AsyncMock

# Add backend to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend', 'shared'))

# Configure logging for tests
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Disable some noisy loggers during tests
logging.getLogger('asyncio').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_settings():
    """Mock settings for testing"""
    return {
        'DATABASE_URL': 'postgresql://test:test@localhost:5432/test_db',
        'REDIS_URL': 'redis://localhost:6379/1',
        'MONGODB_URL': 'mongodb://localhost:27017/test_db',
        'ELASTICSEARCH_URL': 'http://localhost:9200',
        'SECRET_KEY': 'test_secret_key_12345',
        'REFRESH_SECRET_KEY': 'test_refresh_secret_key_12345',
        'JWT_ALGORITHM': 'HS256',
        'ACCESS_TOKEN_EXPIRE_MINUTES': 15,
        'REFRESH_TOKEN_EXPIRE_DAYS': 30,
        'ENVIRONMENT': 'test',
        'DEBUG': True,
        'LOG_LEVEL': 'DEBUG'
    }


@pytest.fixture
def mock_database():
    """Mock database connection"""
    db = AsyncMock()
    
    # Mock common database operations
    db.fetch = AsyncMock(return_value=[])
    db.fetchrow = AsyncMock(return_value=None)
    db.fetchval = AsyncMock(return_value=None)
    db.execute = AsyncMock(return_value="SELECT 1")
    db.executemany = AsyncMock(return_value=None)
    
    # Mock transaction context
    transaction = AsyncMock()
    transaction.__aenter__ = AsyncMock(return_value=transaction)
    transaction.__aexit__ = AsyncMock(return_value=None)
    db.transaction = Mock(return_value=transaction)
    
    return db


@pytest.fixture
def mock_redis():
    """Mock Redis client"""
    redis = AsyncMock()
    
    # Mock Redis operations
    redis.get = AsyncMock(return_value=None)
    redis.set = AsyncMock(return_value=True)
    redis.setex = AsyncMock(return_value=True)
    redis.delete = AsyncMock(return_value=1)
    redis.exists = AsyncMock(return_value=0)
    redis.expire = AsyncMock(return_value=True)
    redis.ttl = AsyncMock(return_value=-1)
    redis.ping = AsyncMock(return_value=True)
    redis.close = AsyncMock()
    
    # Mock hash operations
    redis.hget = AsyncMock(return_value=None)
    redis.hset = AsyncMock(return_value=True)
    redis.hgetall = AsyncMock(return_value={})
    redis.hmset = AsyncMock(return_value=True)
    
    # Mock list operations
    redis.lpush = AsyncMock(return_value=1)
    redis.rpush = AsyncMock(return_value=1)
    redis.lrange = AsyncMock(return_value=[])
    redis.llen = AsyncMock(return_value=0)
    
    # Mock set operations
    redis.sadd = AsyncMock(return_value=1)
    redis.smembers = AsyncMock(return_value=set())
    redis.sismember = AsyncMock(return_value=False)
    
    return redis


@pytest.fixture
def mock_mongodb():
    """Mock MongoDB client"""
    mongodb = AsyncMock()
    
    # Mock database and collection
    db = AsyncMock()
    collection = AsyncMock()
    
    # Mock collection operations
    collection.insert_one = AsyncMock(return_value=Mock(inserted_id="test_id"))
    collection.insert_many = AsyncMock(return_value=Mock(inserted_ids=["id1", "id2"]))
    collection.find_one = AsyncMock(return_value=None)
    collection.find = AsyncMock(return_value=AsyncMock())
    collection.update_one = AsyncMock(return_value=Mock(modified_count=1))
    collection.update_many = AsyncMock(return_value=Mock(modified_count=1))
    collection.delete_one = AsyncMock(return_value=Mock(deleted_count=1))
    collection.delete_many = AsyncMock(return_value=Mock(deleted_count=1))
    collection.count_documents = AsyncMock(return_value=0)
    
    # Mock database operations
    db.__getitem__ = Mock(return_value=collection)
    db.__getattr__ = Mock(return_value=collection)
    
    # Mock client operations
    mongodb.__getitem__ = Mock(return_value=db)
    mongodb.__getattr__ = Mock(return_value=db)
    mongodb.admin = Mock()
    mongodb.admin.command = AsyncMock(return_value={"ok": 1})
    
    return mongodb


@pytest.fixture
def mock_elasticsearch():
    """Mock Elasticsearch client"""
    es = AsyncMock()
    
    # Mock search operations
    es.search = AsyncMock(return_value={
        "hits": {
            "total": {"value": 0},
            "hits": []
        }
    })
    es.index = AsyncMock(return_value={"_id": "test_id", "result": "created"})
    es.get = AsyncMock(return_value={"_source": {}})
    es.update = AsyncMock(return_value={"result": "updated"})
    es.delete = AsyncMock(return_value={"result": "deleted"})
    es.ping = AsyncMock(return_value=True)
    es.info = AsyncMock(return_value={"version": {"number": "8.0.0"}})
    es.close = AsyncMock()
    
    return es


@pytest.fixture
def mock_email_service():
    """Mock email service"""
    email_service = AsyncMock()
    
    email_service.send_email = AsyncMock(return_value=True)
    email_service.send_verification_email = AsyncMock(return_value=True)
    email_service.send_password_reset_email = AsyncMock(return_value=True)
    email_service.send_notification_email = AsyncMock(return_value=True)
    
    return email_service


@pytest.fixture
def mock_sms_service():
    """Mock SMS service"""
    sms_service = AsyncMock()
    
    sms_service.send_sms = AsyncMock(return_value=True)
    sms_service.send_verification_code = AsyncMock(return_value=True)
    sms_service.send_notification = AsyncMock(return_value=True)
    
    return sms_service


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        'id': 'test_user_123',
        'email': '<EMAIL>',
        'phone': '+966501234567',
        'first_name': 'Test',
        'last_name': 'User',
        'full_name': 'Test User',
        'date_of_birth': '1990-01-01',
        'nationality': 'SAU',
        'gender': 'male',
        'role': 'user',
        'is_active': True,
        'is_verified': True,
        'kyc_level': 1,
        'kyc_status': 'approved',
        'country': 'SAU',
        'preferred_currency': 'SAR',
        'language': 'ar',
        'created_at': datetime.now(),
        'updated_at': datetime.now()
    }


@pytest.fixture
def sample_transaction_data():
    """Sample transaction data for testing"""
    return {
        'id': 'test_txn_123',
        'transaction_number': 'TRF20240115000001',
        'type': 'transfer',
        'status': 'completed',
        'amount': 1000.00,
        'currency': 'SAR',
        'sender_id': 'test_user_123',
        'receiver_id': 'test_user_456',
        'agent_id': 'test_agent_789',
        'fee_amount': 10.00,
        'description': 'Test transfer',
        'risk_score': 0.2,
        'risk_level': 'low',
        'fraud_score': 0.1,
        'created_at': datetime.now(),
        'updated_at': datetime.now()
    }


@pytest.fixture
def sample_wallet_data():
    """Sample wallet data for testing"""
    return {
        'id': 'test_wallet_123',
        'wallet_number': 'WP123456SAR000001',
        'user_id': 'test_user_123',
        'currency': 'SAR',
        'type': 'primary',
        'balance': 10000.00,
        'available_balance': 9500.00,
        'pending_balance': 500.00,
        'reserved_balance': 0.00,
        'status': 'active',
        'is_default': True,
        'created_at': datetime.now(),
        'updated_at': datetime.now()
    }


@pytest.fixture
def jwt_test_keys():
    """JWT test keys"""
    return {
        'secret_key': 'test_secret_key_for_jwt_testing_12345',
        'refresh_secret_key': 'test_refresh_secret_key_for_jwt_testing_12345',
        'algorithm': 'HS256'
    }


@pytest.fixture
def mock_ai_models():
    """Mock AI models for testing"""
    models = {}
    
    # Mock fraud detection model
    fraud_model = Mock()
    fraud_model.predict = Mock(return_value=[[0.1, 0.9]])  # Low fraud probability
    fraud_model.predict_proba = Mock(return_value=[[0.9, 0.1]])
    models['fraud_detection'] = fraud_model
    
    # Mock risk assessment model
    risk_model = Mock()
    risk_model.predict = Mock(return_value=[0.3])  # Medium risk
    risk_model.predict_proba = Mock(return_value=[[0.7, 0.3]])
    models['risk_assessment'] = risk_model
    
    # Mock recommendation model
    recommendation_model = Mock()
    recommendation_model.predict = Mock(return_value=[[0.8, 0.6, 0.4, 0.2, 0.1]])
    models['recommendations'] = recommendation_model
    
    return models


@pytest.fixture
def test_client():
    """Test client for API testing"""
    from fastapi.testclient import TestClient
    
    # This would import your main FastAPI app
    # For now, we'll create a mock
    from fastapi import FastAPI
    
    app = FastAPI(title="Test API")
    
    @app.get("/health")
    async def health_check():
        return {"status": "ok"}
    
    return TestClient(app)


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Setup test environment variables"""
    test_env = {
        'ENVIRONMENT': 'test',
        'DEBUG': 'true',
        'LOG_LEVEL': 'DEBUG',
        'DATABASE_URL': 'postgresql://test:test@localhost:5432/test_db',
        'REDIS_URL': 'redis://localhost:6379/1',
        'SECRET_KEY': 'test_secret_key_12345',
        'REFRESH_SECRET_KEY': 'test_refresh_secret_key_12345'
    }
    
    for key, value in test_env.items():
        monkeypatch.setenv(key, value)


@pytest.fixture
def cleanup_test_data():
    """Cleanup test data after tests"""
    # This fixture can be used to clean up test data
    # It runs after the test completes
    yield
    
    # Cleanup code would go here
    # For example, clearing test databases, removing test files, etc.
    pass


# Pytest hooks
def pytest_configure(config):
    """Configure pytest"""
    # Add custom markers
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    # Add markers to tests based on their location
    for item in items:
        # Mark integration tests
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Mark unit tests
        if "unit" in item.nodeid:
            item.add_marker(pytest.mark.unit)
        
        # Mark slow tests
        if "slow" in item.nodeid or "performance" in item.nodeid:
            item.add_marker(pytest.mark.slow)


def pytest_runtest_setup(item):
    """Setup for each test"""
    # Skip integration tests if no database is available
    if "integration" in item.keywords:
        # Check if test database is available
        # Skip if not available
        pass


def pytest_runtest_teardown(item, nextitem):
    """Teardown after each test"""
    # Cleanup after each test
    pass


def pytest_sessionstart(session):
    """Called after the Session object has been created"""
    print("\n🧪 Starting WS Transfir test session...")


def pytest_sessionfinish(session, exitstatus):
    """Called after whole test run finished"""
    if exitstatus == 0:
        print("✅ All tests passed!")
    else:
        print(f"❌ Tests failed with exit status: {exitstatus}")


# Custom pytest markers
pytestmark = [
    pytest.mark.asyncio,
]
