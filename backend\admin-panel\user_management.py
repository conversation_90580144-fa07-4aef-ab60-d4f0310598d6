"""
User Management Service
======================
خدمة إدارة المستخدمين للوحة الإدارة
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

import asyncpg
from ..shared.database.connection import DatabaseConnection
from ..shared.auth.jwt_manager import UserRole

logger = logging.getLogger(__name__)


class UserStatus(Enum):
    """حالات المستخدم"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    BANNED = "banned"


class KYCStatus(Enum):
    """حالات التحقق من الهوية"""
    PENDING = "pending"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXPIRED = "expired"


@dataclass
class UserProfile:
    """ملف المستخدم"""
    id: str
    email: str
    phone: str
    first_name: str
    last_name: str
    full_name: str
    date_of_birth: date
    nationality: str
    gender: str
    role: UserRole
    is_active: bool
    is_verified: bool
    kyc_level: int
    kyc_status: KYCStatus
    country: str
    preferred_currency: str
    language: str
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime]
    login_count: int
    failed_login_attempts: int


@dataclass
class UserActivity:
    """نشاط المستخدم"""
    user_id: str
    activity_type: str
    description: str
    ip_address: str
    user_agent: str
    created_at: datetime
    metadata: Dict[str, Any]


class UserManagement:
    """خدمة إدارة المستخدمين"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Statistics
        self.total_users = 0
        self.active_users = 0
        self.verified_users = 0
        self.kyc_approved_users = 0
    
    async def get_users_list(
        self,
        page: int = 1,
        limit: int = 50,
        search: str = None,
        role: str = None,
        status: str = None,
        kyc_status: str = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """الحصول على قائمة المستخدمين مع الفلترة والبحث"""
        try:
            logger.info(f"👥 Getting users list - page: {page}, limit: {limit}")
            
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = ["deleted_at IS NULL"]
                params = []
                param_count = 0
                
                if search:
                    param_count += 1
                    where_conditions.append(f"""
                        (LOWER(first_name) LIKE LOWER(${param_count}) 
                         OR LOWER(last_name) LIKE LOWER(${param_count})
                         OR LOWER(email) LIKE LOWER(${param_count})
                         OR phone LIKE ${param_count})
                    """)
                    params.append(f"%{search}%")
                
                if role:
                    param_count += 1
                    where_conditions.append(f"role = ${param_count}")
                    params.append(role)
                
                if status:
                    if status == "active":
                        where_conditions.append("is_active = true")
                    elif status == "inactive":
                        where_conditions.append("is_active = false")
                
                if kyc_status:
                    param_count += 1
                    where_conditions.append(f"kyc_status = ${param_count}")
                    params.append(kyc_status)
                
                where_clause = " AND ".join(where_conditions)
                
                # Validate sort column
                valid_sort_columns = [
                    "created_at", "updated_at", "last_login", "email", 
                    "first_name", "last_name", "role", "kyc_status"
                ]
                if sort_by not in valid_sort_columns:
                    sort_by = "created_at"
                
                if sort_order.lower() not in ["asc", "desc"]:
                    sort_order = "desc"
                
                # Get total count
                count_query = f"""
                    SELECT COUNT(*) FROM users WHERE {where_clause}
                """
                total_count = await conn.fetchval(count_query, *params)
                
                # Get users with pagination
                offset = (page - 1) * limit
                param_count += 1
                limit_param = param_count
                param_count += 1
                offset_param = param_count
                
                users_query = f"""
                    SELECT 
                        id, email, phone, first_name, last_name,
                        date_of_birth, nationality, gender, role,
                        is_active, is_verified, kyc_level, kyc_status,
                        country, preferred_currency, language,
                        created_at, updated_at, last_login,
                        login_count, failed_login_attempts
                    FROM users 
                    WHERE {where_clause}
                    ORDER BY {sort_by} {sort_order.upper()}
                    LIMIT ${limit_param} OFFSET ${offset_param}
                """
                
                params.extend([limit, offset])
                users_data = await conn.fetch(users_query, *params)
                
                # Convert to UserProfile objects
                users = []
                for row in users_data:
                    user = UserProfile(
                        id=row['id'],
                        email=row['email'],
                        phone=row['phone'],
                        first_name=row['first_name'],
                        last_name=row['last_name'],
                        full_name=f"{row['first_name']} {row['last_name']}",
                        date_of_birth=row['date_of_birth'],
                        nationality=row['nationality'],
                        gender=row['gender'],
                        role=UserRole(row['role']),
                        is_active=row['is_active'],
                        is_verified=row['is_verified'],
                        kyc_level=row['kyc_level'],
                        kyc_status=KYCStatus(row['kyc_status']),
                        country=row['country'],
                        preferred_currency=row['preferred_currency'],
                        language=row['language'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at'],
                        last_login=row['last_login'],
                        login_count=row['login_count'],
                        failed_login_attempts=row['failed_login_attempts']
                    )
                    users.append(user)
                
                # Calculate pagination info
                total_pages = (total_count + limit - 1) // limit
                has_next = page < total_pages
                has_prev = page > 1
                
                return {
                    'users': [
                        {
                            'id': user.id,
                            'email': user.email,
                            'phone': user.phone,
                            'full_name': user.full_name,
                            'role': user.role.value,
                            'is_active': user.is_active,
                            'is_verified': user.is_verified,
                            'kyc_status': user.kyc_status.value,
                            'kyc_level': user.kyc_level,
                            'country': user.country,
                            'created_at': user.created_at.isoformat(),
                            'last_login': user.last_login.isoformat() if user.last_login else None,
                            'login_count': user.login_count
                        }
                        for user in users
                    ],
                    'pagination': {
                        'current_page': page,
                        'total_pages': total_pages,
                        'total_count': total_count,
                        'limit': limit,
                        'has_next': has_next,
                        'has_prev': has_prev
                    },
                    'filters': {
                        'search': search,
                        'role': role,
                        'status': status,
                        'kyc_status': kyc_status,
                        'sort_by': sort_by,
                        'sort_order': sort_order
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get users list: {e}")
            return {'error': str(e)}
    
    async def get_user_details(self, user_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على تفاصيل المستخدم الكاملة"""
        try:
            logger.info(f"👤 Getting user details for: {user_id}")
            
            async with self.db_connection.get_connection() as conn:
                # Get user basic info
                user_query = """
                    SELECT * FROM users WHERE id = $1 AND deleted_at IS NULL
                """
                user_data = await conn.fetchrow(user_query, user_id)
                
                if not user_data:
                    return None
                
                # Get user wallets
                wallets_query = """
                    SELECT 
                        id, wallet_number, currency, type, balance,
                        available_balance, pending_balance, reserved_balance,
                        status, is_default, created_at
                    FROM wallets 
                    WHERE user_id = $1 AND deleted_at IS NULL
                    ORDER BY is_default DESC, created_at DESC
                """
                wallets_data = await conn.fetch(wallets_query, user_id)
                
                # Get recent transactions
                transactions_query = """
                    SELECT 
                        id, transaction_number, type, amount, currency,
                        status, description, created_at
                    FROM transactions 
                    WHERE (sender_id = $1 OR receiver_id = $1)
                    ORDER BY created_at DESC 
                    LIMIT 10
                """
                transactions_data = await conn.fetch(transactions_query, user_id)
                
                # Get user activity log
                activity_query = """
                    SELECT 
                        activity_type, description, ip_address,
                        created_at, metadata
                    FROM user_activity_log 
                    WHERE user_id = $1
                    ORDER BY created_at DESC 
                    LIMIT 20
                """
                activity_data = await conn.fetch(activity_query, user_id)
                
                # Get agent profile if user is an agent
                agent_profile = None
                if user_data['role'] in ['agent', 'agent_manager']:
                    agent_query = """
                        SELECT 
                            id, agent_code, agent_type, status, business_name,
                            region, city, total_transactions, total_volume,
                            total_commission_earned, customer_count, rating
                        FROM agent_profiles 
                        WHERE user_id = $1 AND deleted_at IS NULL
                    """
                    agent_data = await conn.fetchrow(agent_query, user_id)
                    if agent_data:
                        agent_profile = {
                            'id': agent_data['id'],
                            'agent_code': agent_data['agent_code'],
                            'agent_type': agent_data['agent_type'],
                            'status': agent_data['status'],
                            'business_name': agent_data['business_name'],
                            'region': agent_data['region'],
                            'city': agent_data['city'],
                            'total_transactions': agent_data['total_transactions'],
                            'total_volume': float(agent_data['total_volume']),
                            'total_commission_earned': float(agent_data['total_commission_earned']),
                            'customer_count': agent_data['customer_count'],
                            'rating': float(agent_data['rating'])
                        }
                
                return {
                    'user': {
                        'id': user_data['id'],
                        'email': user_data['email'],
                        'phone': user_data['phone'],
                        'first_name': user_data['first_name'],
                        'last_name': user_data['last_name'],
                        'full_name': f"{user_data['first_name']} {user_data['last_name']}",
                        'date_of_birth': user_data['date_of_birth'].isoformat(),
                        'nationality': user_data['nationality'],
                        'gender': user_data['gender'],
                        'role': user_data['role'],
                        'is_active': user_data['is_active'],
                        'is_verified': user_data['is_verified'],
                        'kyc_level': user_data['kyc_level'],
                        'kyc_status': user_data['kyc_status'],
                        'country': user_data['country'],
                        'preferred_currency': user_data['preferred_currency'],
                        'language': user_data['language'],
                        'created_at': user_data['created_at'].isoformat(),
                        'updated_at': user_data['updated_at'].isoformat(),
                        'last_login': user_data['last_login'].isoformat() if user_data['last_login'] else None,
                        'login_count': user_data['login_count'],
                        'failed_login_attempts': user_data['failed_login_attempts'],
                        'two_factor_enabled': user_data['two_factor_enabled']
                    },
                    'wallets': [
                        {
                            'id': wallet['id'],
                            'wallet_number': wallet['wallet_number'],
                            'currency': wallet['currency'],
                            'type': wallet['type'],
                            'balance': float(wallet['balance']),
                            'available_balance': float(wallet['available_balance']),
                            'pending_balance': float(wallet['pending_balance']),
                            'reserved_balance': float(wallet['reserved_balance']),
                            'status': wallet['status'],
                            'is_default': wallet['is_default'],
                            'created_at': wallet['created_at'].isoformat()
                        }
                        for wallet in wallets_data
                    ],
                    'recent_transactions': [
                        {
                            'id': txn['id'],
                            'transaction_number': txn['transaction_number'],
                            'type': txn['type'],
                            'amount': float(txn['amount']),
                            'currency': txn['currency'],
                            'status': txn['status'],
                            'description': txn['description'],
                            'created_at': txn['created_at'].isoformat()
                        }
                        for txn in transactions_data
                    ],
                    'activity_log': [
                        {
                            'activity_type': activity['activity_type'],
                            'description': activity['description'],
                            'ip_address': activity['ip_address'],
                            'created_at': activity['created_at'].isoformat(),
                            'metadata': activity['metadata']
                        }
                        for activity in activity_data
                    ],
                    'agent_profile': agent_profile
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get user details: {e}")
            return None
    
    async def update_user_status(
        self,
        user_id: str,
        is_active: bool,
        updated_by: str,
        reason: str = None
    ) -> bool:
        """تحديث حالة المستخدم"""
        try:
            logger.info(f"🔄 Updating user status - user: {user_id}, active: {is_active}")
            
            async with self.db_connection.get_connection() as conn:
                # Update user status
                update_query = """
                    UPDATE users 
                    SET is_active = $2,
                        updated_at = CURRENT_TIMESTAMP,
                        updated_by = $3
                    WHERE id = $1 AND deleted_at IS NULL
                    RETURNING email
                """
                
                result = await conn.fetchval(update_query, user_id, is_active, updated_by)
                
                if result:
                    # Log the activity
                    await self._log_user_activity(
                        conn,
                        user_id,
                        "status_change",
                        f"User status changed to {'active' if is_active else 'inactive'}",
                        updated_by,
                        {"reason": reason, "new_status": is_active}
                    )
                    
                    logger.info(f"✅ User status updated successfully: {result}")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to update user status: {e}")
            return False
    
    async def update_kyc_status(
        self,
        user_id: str,
        kyc_status: str,
        kyc_level: int,
        updated_by: str,
        notes: str = None
    ) -> bool:
        """تحديث حالة التحقق من الهوية"""
        try:
            logger.info(f"📋 Updating KYC status - user: {user_id}, status: {kyc_status}")
            
            async with self.db_connection.get_connection() as conn:
                # Update KYC status
                update_query = """
                    UPDATE users 
                    SET kyc_status = $2,
                        kyc_level = $3,
                        kyc_approved_at = CASE WHEN $2 = 'approved' THEN CURRENT_TIMESTAMP ELSE kyc_approved_at END,
                        updated_at = CURRENT_TIMESTAMP,
                        updated_by = $4
                    WHERE id = $1 AND deleted_at IS NULL
                    RETURNING email
                """
                
                result = await conn.fetchval(update_query, user_id, kyc_status, kyc_level, updated_by)
                
                if result:
                    # Log the activity
                    await self._log_user_activity(
                        conn,
                        user_id,
                        "kyc_update",
                        f"KYC status updated to {kyc_status} (Level {kyc_level})",
                        updated_by,
                        {"kyc_status": kyc_status, "kyc_level": kyc_level, "notes": notes}
                    )
                    
                    logger.info(f"✅ KYC status updated successfully: {result}")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to update KYC status: {e}")
            return False
    
    async def reset_user_password(
        self,
        user_id: str,
        new_password_hash: str,
        updated_by: str
    ) -> bool:
        """إعادة تعيين كلمة مرور المستخدم"""
        try:
            logger.info(f"🔑 Resetting password for user: {user_id}")
            
            async with self.db_connection.get_connection() as conn:
                # Update password
                update_query = """
                    UPDATE users 
                    SET password_hash = $2,
                        password_changed_at = CURRENT_TIMESTAMP,
                        failed_login_attempts = 0,
                        locked_until = NULL,
                        updated_at = CURRENT_TIMESTAMP,
                        updated_by = $3
                    WHERE id = $1 AND deleted_at IS NULL
                    RETURNING email
                """
                
                result = await conn.fetchval(update_query, user_id, new_password_hash, updated_by)
                
                if result:
                    # Log the activity
                    await self._log_user_activity(
                        conn,
                        user_id,
                        "password_reset",
                        "Password reset by administrator",
                        updated_by,
                        {"reset_by": updated_by}
                    )
                    
                    logger.info(f"✅ Password reset successfully: {result}")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to reset password: {e}")
            return False
    
    async def get_user_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المستخدمين"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Basic statistics
                stats_query = """
                    SELECT 
                        COUNT(*) as total_users,
                        COUNT(CASE WHEN is_active THEN 1 END) as active_users,
                        COUNT(CASE WHEN is_verified THEN 1 END) as verified_users,
                        COUNT(CASE WHEN kyc_status = 'approved' THEN 1 END) as kyc_approved_users,
                        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_users_30d,
                        COUNT(CASE WHEN last_login >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as active_users_7d
                    FROM users 
                    WHERE deleted_at IS NULL
                """
                
                stats = await conn.fetchrow(stats_query)
                
                # Role distribution
                role_query = """
                    SELECT role, COUNT(*) as count
                    FROM users 
                    WHERE deleted_at IS NULL
                    GROUP BY role
                    ORDER BY count DESC
                """
                
                role_distribution = await conn.fetch(role_query)
                
                # Geographic distribution
                geo_query = """
                    SELECT country, COUNT(*) as count
                    FROM users 
                    WHERE deleted_at IS NULL AND country IS NOT NULL
                    GROUP BY country
                    ORDER BY count DESC
                    LIMIT 10
                """
                
                geo_distribution = await conn.fetch(geo_query)
                
                return {
                    'total_users': stats['total_users'],
                    'active_users': stats['active_users'],
                    'verified_users': stats['verified_users'],
                    'kyc_approved_users': stats['kyc_approved_users'],
                    'new_users_30d': stats['new_users_30d'],
                    'active_users_7d': stats['active_users_7d'],
                    'activity_rate': (stats['active_users_7d'] / max(stats['total_users'], 1)) * 100,
                    'verification_rate': (stats['verified_users'] / max(stats['total_users'], 1)) * 100,
                    'kyc_approval_rate': (stats['kyc_approved_users'] / max(stats['total_users'], 1)) * 100,
                    'role_distribution': [
                        {'role': row['role'], 'count': row['count']}
                        for row in role_distribution
                    ],
                    'geographic_distribution': [
                        {'country': row['country'], 'count': row['count']}
                        for row in geo_distribution
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get user statistics: {e}")
            return {'error': str(e)}
    
    # Helper methods
    async def _log_user_activity(
        self,
        conn,
        user_id: str,
        activity_type: str,
        description: str,
        performed_by: str,
        metadata: Dict[str, Any] = None
    ):
        """تسجيل نشاط المستخدم"""
        try:
            activity_query = """
                INSERT INTO user_activity_log (
                    user_id, activity_type, description, 
                    performed_by, metadata
                ) VALUES ($1, $2, $3, $4, $5)
            """
            
            await conn.execute(
                activity_query,
                user_id,
                activity_type,
                description,
                performed_by,
                metadata or {}
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to log user activity: {e}")
