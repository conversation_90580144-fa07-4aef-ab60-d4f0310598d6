{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestFilter", "title": "Nest Filter Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the filter.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the filter?"}, "path": {"type": "string", "format": "path", "description": "The path to create the filter."}, "language": {"type": "string", "description": "Nest filter language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest filter source root directory."}, "flat": {"type": "boolean", "default": true, "description": "Flag to indicate if a directory is created."}, "spec": {"type": "boolean", "default": true, "description": "Specifies if a spec file is generated."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}}, "required": ["name"]}