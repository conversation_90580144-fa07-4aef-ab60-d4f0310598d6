import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Between } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Cron, CronExpression } from '@nestjs/schedule';
import { 
  Notification, 
  NotificationStatus, 
  NotificationType,
  NotificationPriority 
} from '../entities/notification.entity';
import { CreateNotificationDto } from '../dto/create-notification.dto';

export interface FindNotificationsOptions {
  page?: number;
  limit?: number;
  status?: NotificationStatus;
  type?: NotificationType;
  unreadOnly?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface PaginatedNotifications {
  data: Notification[];
  total: number;
  unreadCount: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    @InjectQueue('notifications')
    private readonly notificationQueue: Queue,
  ) {}

  async create(createNotificationDto: CreateNotificationDto): Promise<Notification> {
    const notification = this.notificationRepository.create({
      ...createNotificationDto,
      scheduledAt: createNotificationDto.scheduledAt 
        ? new Date(createNotificationDto.scheduledAt) 
        : undefined,
      expiresAt: createNotificationDto.expiresAt 
        ? new Date(createNotificationDto.expiresAt) 
        : undefined,
    });

    const savedNotification = await this.notificationRepository.save(notification);

    // Queue notification for sending
    if (createNotificationDto.sendImmediately || savedNotification.shouldSendNow()) {
      await this.queueNotification(savedNotification);
    } else if (savedNotification.scheduledAt) {
      await this.scheduleNotification(savedNotification);
    }

    this.logger.log(`Notification created: ${savedNotification.id}`);
    return savedNotification;
  }

  async getUserNotifications(
    userId: string, 
    options: FindNotificationsOptions = {}
  ): Promise<PaginatedNotifications> {
    const {
      page = 1,
      limit = 20,
      status,
      type,
      unreadOnly = false,
      dateFrom,
      dateTo,
    } = options;

    const skip = (page - 1) * limit;
    const queryBuilder = this.notificationRepository.createQueryBuilder('notification');

    queryBuilder.where('notification.userId = :userId', { userId });

    if (status) {
      queryBuilder.andWhere('notification.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('notification.type = :type', { type });
    }

    if (unreadOnly) {
      queryBuilder.andWhere('notification.isRead = :isRead', { isRead: false });
    }

    if (dateFrom && dateTo) {
      queryBuilder.andWhere('notification.createdAt BETWEEN :dateFrom AND :dateTo', {
        dateFrom,
        dateTo,
      });
    }

    queryBuilder
      .andWhere('notification.isArchived = :isArchived', { isArchived: false })
      .orderBy('notification.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    // Get unread count
    const unreadCount = await this.notificationRepository.count({
      where: {
        userId,
        isRead: false,
        isArchived: false,
      },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      unreadCount,
      page,
      limit,
      totalPages,
    };
  }

  async findById(id: string): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({
      where: { id },
    });

    if (!notification) {
      throw new NotFoundException('الإشعار غير موجود');
    }

    return notification;
  }

  async markAsRead(id: string, userId: string): Promise<Notification> {
    const notification = await this.findById(id);

    if (notification.userId !== userId) {
      throw new BadRequestException('غير مصرح لك بالوصول لهذا الإشعار');
    }

    if (!notification.isRead) {
      notification.markAsRead();
      await this.notificationRepository.save(notification);
      this.logger.log(`Notification marked as read: ${id}`);
    }

    return notification;
  }

  async markAllAsRead(userId: string): Promise<{ updatedCount: number }> {
    const result = await this.notificationRepository.update(
      {
        userId,
        isRead: false,
        isArchived: false,
      },
      {
        isRead: true,
        readAt: new Date(),
      }
    );

    this.logger.log(`Marked ${result.affected} notifications as read for user: ${userId}`);
    return { updatedCount: result.affected || 0 };
  }

  async archiveNotification(id: string, userId: string): Promise<void> {
    const notification = await this.findById(id);

    if (notification.userId !== userId) {
      throw new BadRequestException('غير مصرح لك بالوصول لهذا الإشعار');
    }

    notification.isArchived = true;
    await this.notificationRepository.save(notification);
    this.logger.log(`Notification archived: ${id}`);
  }

  async getUnreadCount(userId: string): Promise<any> {
    const unreadCount = await this.notificationRepository.count({
      where: {
        userId,
        isRead: false,
        isArchived: false,
      },
    });

    // Get count by type
    const byType = await this.notificationRepository
      .createQueryBuilder('notification')
      .select('notification.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('notification.userId = :userId', { userId })
      .andWhere('notification.isRead = :isRead', { isRead: false })
      .andWhere('notification.isArchived = :isArchived', { isArchived: false })
      .groupBy('notification.type')
      .getRawMany();

    const typeCount = {};
    byType.forEach(item => {
      typeCount[item.type] = parseInt(item.count);
    });

    return {
      unreadCount,
      byType: typeCount,
    };
  }

  async retryNotification(id: string): Promise<Notification> {
    const notification = await this.findById(id);

    if (!notification.canRetry()) {
      throw new BadRequestException('لا يمكن إعادة محاولة هذا الإشعار');
    }

    notification.status = NotificationStatus.PENDING;
    notification.errorMessage = null;
    notification.incrementAttempts();

    const updatedNotification = await this.notificationRepository.save(notification);
    await this.queueNotification(updatedNotification);

    this.logger.log(`Notification retry queued: ${id}`);
    return updatedNotification;
  }

  async getNotificationStats(): Promise<any> {
    const [
      totalNotifications,
      sentNotifications,
      failedNotifications,
      deliveredNotifications,
    ] = await Promise.all([
      this.notificationRepository.count(),
      this.notificationRepository.count({ where: { status: NotificationStatus.SENT } }),
      this.notificationRepository.count({ where: { status: NotificationStatus.FAILED } }),
      this.notificationRepository.count({ where: { status: NotificationStatus.DELIVERED } }),
    ]);

    // Get stats by type
    const byType = await this.notificationRepository
      .createQueryBuilder('notification')
      .select('notification.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('notification.type')
      .getRawMany();

    // Get stats by status
    const byStatus = await this.notificationRepository
      .createQueryBuilder('notification')
      .select('notification.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('notification.status')
      .getRawMany();

    const deliveryRate = totalNotifications > 0 
      ? ((sentNotifications + deliveredNotifications) / totalNotifications) * 100 
      : 0;

    return {
      totalNotifications,
      sentNotifications,
      failedNotifications,
      deliveredNotifications,
      deliveryRate: Math.round(deliveryRate * 100) / 100,
      byType: byType.reduce((acc, item) => {
        acc[item.type] = parseInt(item.count);
        return acc;
      }, {}),
      byStatus: byStatus.reduce((acc, item) => {
        acc[item.status] = parseInt(item.count);
        return acc;
      }, {}),
    };
  }

  async getFailedNotifications(limit: number = 50): Promise<Notification[]> {
    return this.notificationRepository.find({
      where: { status: NotificationStatus.FAILED },
      order: { updatedAt: 'DESC' },
      take: limit,
    });
  }

  async createBulkNotifications(
    userIds: string[],
    notificationData: Omit<CreateNotificationDto, 'userId'>
  ): Promise<{ createdCount: number; notifications: Notification[] }> {
    const notifications = userIds.map(userId => 
      this.notificationRepository.create({
        ...notificationData,
        userId,
        scheduledAt: notificationData.scheduledAt 
          ? new Date(notificationData.scheduledAt) 
          : undefined,
        expiresAt: notificationData.expiresAt 
          ? new Date(notificationData.expiresAt) 
          : undefined,
      })
    );

    const savedNotifications = await this.notificationRepository.save(notifications);

    // Queue all notifications for sending
    for (const notification of savedNotifications) {
      if (notificationData.sendImmediately || notification.shouldSendNow()) {
        await this.queueNotification(notification);
      } else if (notification.scheduledAt) {
        await this.scheduleNotification(notification);
      }
    }

    this.logger.log(`Bulk notifications created: ${savedNotifications.length}`);
    return {
      createdCount: savedNotifications.length,
      notifications: savedNotifications,
    };
  }

  private async queueNotification(notification: Notification): Promise<void> {
    const delay = notification.scheduledAt 
      ? Math.max(0, notification.scheduledAt.getTime() - Date.now())
      : 0;

    await this.notificationQueue.add(
      'send-notification',
      { notificationId: notification.id },
      {
        delay,
        attempts: notification.maxAttempts,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      }
    );
  }

  private async scheduleNotification(notification: Notification): Promise<void> {
    const delay = notification.scheduledAt.getTime() - Date.now();
    
    if (delay > 0) {
      await this.notificationQueue.add(
        'send-notification',
        { notificationId: notification.id },
        {
          delay,
          attempts: notification.maxAttempts,
          removeOnComplete: 100,
          removeOnFail: 50,
        }
      );
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async processScheduledNotifications(): Promise<void> {
    const now = new Date();
    const scheduledNotifications = await this.notificationRepository.find({
      where: {
        status: NotificationStatus.PENDING,
        scheduledAt: Between(new Date(now.getTime() - 60000), now),
      },
      take: 100,
    });

    for (const notification of scheduledNotifications) {
      if (notification.shouldSendNow()) {
        await this.queueNotification(notification);
      }
    }

    if (scheduledNotifications.length > 0) {
      this.logger.log(`Processed ${scheduledNotifications.length} scheduled notifications`);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async cleanupExpiredNotifications(): Promise<void> {
    const result = await this.notificationRepository.update(
      {
        status: NotificationStatus.PENDING,
        expiresAt: Between(new Date(0), new Date()),
      },
      {
        status: NotificationStatus.CANCELLED,
        errorMessage: 'انتهت صلاحية الإشعار',
      }
    );

    if (result.affected && result.affected > 0) {
      this.logger.log(`Cancelled ${result.affected} expired notifications`);
    }
  }
}
