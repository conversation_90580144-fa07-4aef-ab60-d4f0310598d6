import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  But<PERSON>,
  Grid,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepL<PERSON>l,
  StepContent,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  ArrowBack,
  Download,
  Share,
  Cancel,
  Refresh,
  CheckCircle,
  Schedule,
  Error,
  Info,
  Receipt,
  Print,
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '../../components/Layout';

interface TransferDetails {
  id: string;
  referenceNumber: string;
  amount: string;
  currency: string;
  receiveCurrency: string;
  receiveAmount: string;
  exchangeRate: string;
  fees: string;
  totalAmount: string;
  receiverName: string;
  receiverPhone: string;
  receiverCountry: string;
  receiverCity: string;
  status: string;
  createdAt: string;
  estimatedDelivery: string;
  actualDelivery?: string;
  purpose: string;
  paymentMethod: string;
  deliveryMethod: string;
  statusHistory: Array<{
    status: string;
    timestamp: string;
    description: string;
  }>;
}

const steps = [
  {
    label: 'تم إنشاء التحويل',
    description: 'تم إنشاء طلب التحويل بنجاح',
  },
  {
    label: 'تم تأكيد الدفع',
    description: 'تم تأكيد استلام المبلغ',
  },
  {
    label: 'قيد المعالجة',
    description: 'جاري معالجة التحويل',
  },
  {
    label: 'تم الإرسال',
    description: 'تم إرسال المبلغ للمستقبل',
  },
  {
    label: 'تم التسليم',
    description: 'تم تسليم المبلغ بنجاح',
  },
];

const TransferDetailsPage: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  
  const [transfer, setTransfer] = useState<TransferDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [cancelling, setCancelling] = useState(false);

  useEffect(() => {
    if (id) {
      fetchTransferDetails(id as string);
    }
  }, [id]);

  const fetchTransferDetails = async (transferId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/transfers/${transferId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTransfer(data);
      } else {
        setError('فشل في تحميل تفاصيل التحويل');
        // Mock data for development
        setTransfer({
          id: transferId,
          referenceNumber: 'WS20241225001',
          amount: '1,500.00',
          currency: 'SAR',
          receiveCurrency: 'EGP',
          receiveAmount: '6,000.00',
          exchangeRate: '4.00',
          fees: '15.00',
          totalAmount: '1,515.00',
          receiverName: 'أحمد محمد علي',
          receiverPhone: '+***********',
          receiverCountry: 'مصر',
          receiverCity: 'القاهرة',
          status: 'processing',
          createdAt: '2024-12-25T10:30:00Z',
          estimatedDelivery: '2024-12-25T14:30:00Z',
          purpose: 'family_support',
          paymentMethod: 'wallet',
          deliveryMethod: 'bank_transfer',
          statusHistory: [
            {
              status: 'created',
              timestamp: '2024-12-25T10:30:00Z',
              description: 'تم إنشاء طلب التحويل',
            },
            {
              status: 'payment_confirmed',
              timestamp: '2024-12-25T10:32:00Z',
              description: 'تم تأكيد الدفع من المحفظة الرقمية',
            },
            {
              status: 'processing',
              timestamp: '2024-12-25T10:35:00Z',
              description: 'جاري معالجة التحويل',
            },
          ],
        });
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelTransfer = async () => {
    if (!transfer) return;

    try {
      setCancelling(true);
      const response = await fetch(`/api/transfers/${transfer.id}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        await fetchTransferDetails(transfer.id);
        setCancelDialogOpen(false);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'فشل في إلغاء التحويل');
      }
    } catch (err) {
      setError('حدث خطأ في إلغاء التحويل');
    } finally {
      setCancelling(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'processing':
        return 'info';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'معلق';
      case 'processing':
        return 'قيد المعالجة';
      case 'failed':
        return 'فاشل';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  };

  const getPurposeText = (purpose: string) => {
    switch (purpose) {
      case 'family_support':
        return 'دعم عائلي';
      case 'education':
        return 'تعليم';
      case 'medical':
        return 'طبي';
      case 'business':
        return 'تجاري';
      case 'investment':
        return 'استثمار';
      default:
        return 'أخرى';
    }
  };

  const getActiveStep = (status: string) => {
    switch (status) {
      case 'pending':
        return 0;
      case 'payment_confirmed':
        return 1;
      case 'processing':
        return 2;
      case 'sent':
        return 3;
      case 'completed':
        return 4;
      default:
        return 0;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const canCancel = (status: string) => {
    return ['pending', 'processing'].includes(status);
  };

  if (loading) {
    return (
      <Layout>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Typography>جاري التحميل...</Typography>
        </Box>
      </Layout>
    );
  }

  if (!transfer) {
    return (
      <Layout>
        <Box p={3}>
          <Alert severity="error">التحويل غير موجود</Alert>
        </Box>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>تفاصيل التحويل {transfer.referenceNumber} - WS Transfir</title>
        <meta name="description" content={`تفاصيل التحويل ${transfer.referenceNumber}`} />
      </Head>

      <Layout>
        <Box sx={{ p: 3 }}>
          {/* Header */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Box display="flex" alignItems="center" gap={2}>
              <IconButton onClick={() => router.back()}>
                <ArrowBack />
              </IconButton>
              <Box>
                <Typography variant="h4" component="h1">
                  تفاصيل التحويل
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {transfer.referenceNumber}
                </Typography>
              </Box>
            </Box>
            <Box display="flex" gap={1}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={() => fetchTransferDetails(transfer.id)}
                size="small"
              >
                تحديث
              </Button>
              <Button
                variant="outlined"
                startIcon={<Download />}
                size="small"
              >
                تحميل الإيصال
              </Button>
              <Button
                variant="outlined"
                startIcon={<Share />}
                size="small"
              >
                مشاركة
              </Button>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Status and Progress */}
            <Grid item xs={12} lg={8}>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                    <Typography variant="h6">حالة التحويل</Typography>
                    <Chip
                      label={getStatusText(transfer.status)}
                      color={getStatusColor(transfer.status) as any}
                      size="large"
                    />
                  </Box>

                  <Stepper activeStep={getActiveStep(transfer.status)} orientation="vertical">
                    {steps.map((step, index) => (
                      <Step key={step.label}>
                        <StepLabel>
                          <Typography variant="subtitle2">{step.label}</Typography>
                        </StepLabel>
                        <StepContent>
                          <Typography variant="body2" color="text.secondary">
                            {step.description}
                          </Typography>
                        </StepContent>
                      </Step>
                    ))}
                  </Stepper>
                </CardContent>
              </Card>

              {/* Transfer Details */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    تفاصيل التحويل
                  </Typography>
                  
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        مبلغ الإرسال
                      </Typography>
                      <Typography variant="h6" gutterBottom>
                        {transfer.amount} {transfer.currency}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        مبلغ الاستقبال
                      </Typography>
                      <Typography variant="h6" gutterBottom>
                        {transfer.receiveAmount} {transfer.receiveCurrency}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        سعر الصرف
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        1 {transfer.currency} = {transfer.exchangeRate} {transfer.receiveCurrency}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        رسوم التحويل
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {transfer.fees} {transfer.currency}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12}>
                      <Divider sx={{ my: 2 }} />
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="h6">
                          المبلغ الإجمالي
                        </Typography>
                        <Typography variant="h6" color="primary">
                          {transfer.totalAmount} {transfer.currency}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Receiver Information */}
            <Grid item xs={12} lg={4}>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    معلومات المستقبل
                  </Typography>
                  
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="text.secondary">
                      الاسم
                    </Typography>
                    <Typography variant="body1">
                      {transfer.receiverName}
                    </Typography>
                  </Box>
                  
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="text.secondary">
                      رقم الهاتف
                    </Typography>
                    <Typography variant="body1" dir="ltr">
                      {transfer.receiverPhone}
                    </Typography>
                  </Box>
                  
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="text.secondary">
                      الموقع
                    </Typography>
                    <Typography variant="body1">
                      {transfer.receiverCity}, {transfer.receiverCountry}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>

              {/* Additional Information */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    معلومات إضافية
                  </Typography>
                  
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="text.secondary">
                      الغرض من التحويل
                    </Typography>
                    <Typography variant="body1">
                      {getPurposeText(transfer.purpose)}
                    </Typography>
                  </Box>
                  
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="text.secondary">
                      طريقة الدفع
                    </Typography>
                    <Typography variant="body1">
                      {transfer.paymentMethod === 'wallet' ? 'المحفظة الرقمية' : transfer.paymentMethod}
                    </Typography>
                  </Box>
                  
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="text.secondary">
                      طريقة التسليم
                    </Typography>
                    <Typography variant="body1">
                      {transfer.deliveryMethod === 'bank_transfer' ? 'تحويل بنكي' : transfer.deliveryMethod}
                    </Typography>
                  </Box>
                  
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="text.secondary">
                      تاريخ الإنشاء
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(transfer.createdAt)}
                    </Typography>
                  </Box>
                  
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="text.secondary">
                      التسليم المتوقع
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(transfer.estimatedDelivery)}
                    </Typography>
                  </Box>

                  {canCancel(transfer.status) && (
                    <Box mt={3}>
                      <Button
                        variant="outlined"
                        color="error"
                        startIcon={<Cancel />}
                        fullWidth
                        onClick={() => setCancelDialogOpen(true)}
                      >
                        إلغاء التحويل
                      </Button>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Cancel Confirmation Dialog */}
          <Dialog
            open={cancelDialogOpen}
            onClose={() => setCancelDialogOpen(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>تأكيد إلغاء التحويل</DialogTitle>
            <DialogContent>
              <Typography>
                هل أنت متأكد من رغبتك في إلغاء هذا التحويل؟ 
                سيتم إرجاع المبلغ إلى محفظتك خلال 24 ساعة.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setCancelDialogOpen(false)}>
                تراجع
              </Button>
              <Button
                onClick={handleCancelTransfer}
                color="error"
                variant="contained"
                disabled={cancelling}
              >
                {cancelling ? 'جاري الإلغاء...' : 'تأكيد الإلغاء'}
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </Layout>
    </>
  );
};

export default TransferDetailsPage;
