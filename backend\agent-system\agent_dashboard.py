"""
Agent Dashboard Service
======================
خدمة لوحة تحكم الوكلاء
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

import asyncpg # type: ignore
from ..shared.database.connection import DatabaseConnection # type: ignore

logger = logging.getLogger(__name__)


@dataclass
class DashboardMetrics:
    """مقاييس لوحة التحكم"""
    total_transactions: int
    total_volume: Decimal
    total_commission: Decimal
    active_customers: int
    success_rate: Decimal
    average_transaction_amount: Decimal
    monthly_growth: Decimal
    ranking_position: int
    performance_score: Decimal


@dataclass
class TransactionSummary:
    """ملخص المعاملات"""
    today: Dict[str, Any]
    this_week: Dict[str, Any]
    this_month: Dict[str, Any]
    last_month: Dict[str, Any]
    year_to_date: Dict[str, Any]


@dataclass
class CommissionSummary:
    """ملخص العمولات"""
    pending: Decimal
    approved: Decimal
    paid: Decimal
    total_earned: Decimal
    this_month: Decimal
    last_month: Decimal
    growth_rate: Decimal


class AgentDashboard:
    """خدمة لوحة تحكم الوكلاء"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Cache settings
        self.cache_duration = 300  # 5 minutes
        self.dashboard_cache = {}
    
    async def get_agent_dashboard(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على بيانات لوحة تحكم الوكيل"""
        try:
            logger.info(f"📊 Getting dashboard data for agent: {agent_id}")
            
            # Check cache first
            cache_key = f"dashboard_{agent_id}"
            if cache_key in self.dashboard_cache:
                cached_data = self.dashboard_cache[cache_key]
                if (datetime.now() - cached_data['timestamp']).seconds < self.cache_duration:
                    logger.info("📋 Returning cached dashboard data")
                    return cached_data['data']
            
            # Get all dashboard components
            dashboard_data = {
                'agent_info': await self._get_agent_basic_info(agent_id),
                'metrics': await self._get_dashboard_metrics(agent_id),
                'transactions': await self._get_transaction_summary(agent_id),
                'commissions': await self._get_commission_summary(agent_id),
                'customers': await self._get_customer_summary(agent_id),
                'performance': await self._get_performance_summary(agent_id),
                'hierarchy': await self._get_hierarchy_summary(agent_id),
                'recent_activities': await self._get_recent_activities(agent_id),
                'charts_data': await self._get_charts_data(agent_id),
                'notifications': await self._get_agent_notifications(agent_id),
                'targets': await self._get_targets_progress(agent_id),
                'last_updated': datetime.now().isoformat()
            }
            
            # Cache the data
            self.dashboard_cache[cache_key] = {
                'data': dashboard_data,
                'timestamp': datetime.now()
            }
            
            logger.info("✅ Dashboard data retrieved successfully")
            return dashboard_data
            
        except Exception as e:
            logger.error(f"❌ Failed to get dashboard data: {e}")
            return {'error': str(e)}
    
    async def get_real_time_metrics(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على المقاييس في الوقت الفعلي"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Today's metrics
                today_query = """
                    SELECT 
                        COUNT(*) as today_transactions,
                        COALESCE(SUM(amount), 0) as today_volume,
                        COALESCE(AVG(amount), 0) as today_avg_amount
                    FROM transactions 
                    WHERE agent_id = $1 
                    AND DATE(created_at) = CURRENT_DATE
                    AND status = 'completed'
                """
                
                today_metrics = await conn.fetchrow(today_query, agent_id)
                
                # This month's commission
                current_month = datetime.now().strftime('%Y-%m')
                commission_query = """
                    SELECT COALESCE(SUM(commission_amount), 0) as month_commission
                    FROM agent_commissions 
                    WHERE agent_id = $1 AND commission_period = $2
                """
                
                month_commission = await conn.fetchval(commission_query, agent_id, current_month)
                
                # Active customers count
                customers_query = """
                    SELECT COUNT(DISTINCT customer_id) as active_customers
                    FROM agent_customers 
                    WHERE agent_id = $1 AND status = 'active'
                """
                
                active_customers = await conn.fetchval(customers_query, agent_id)
                
                return {
                    'today_transactions': today_metrics['today_transactions'],
                    'today_volume': float(today_metrics['today_volume']),
                    'today_avg_amount': float(today_metrics['today_avg_amount']),
                    'month_commission': float(month_commission or 0),
                    'active_customers': active_customers or 0,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get real-time metrics: {e}")
            return {'error': str(e)}
    
    async def get_performance_analytics(
        self, 
        agent_id: str, 
        period: str = 'monthly',
        months: int = 12
    ) -> Dict[str, Any]:
        """الحصول على تحليلات الأداء"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Get performance data for the specified period
                query = """
                    SELECT 
                        period_start,
                        period_end,
                        transaction_count,
                        transaction_volume,
                        total_commission,
                        new_customers,
                        customer_retention_rate,
                        success_rate,
                        customer_satisfaction,
                        regional_rank,
                        national_rank,
                        volume_achievement_rate,
                        transaction_achievement_rate
                    FROM agent_performance 
                    WHERE agent_id = $1 AND period_type = $2
                    ORDER BY period_start DESC 
                    LIMIT $3
                """
                
                performance_data = await conn.fetch(query, agent_id, period, months)
                
                # Calculate trends and analytics
                analytics = {
                    'performance_history': [],
                    'trends': {},
                    'averages': {},
                    'best_month': {},
                    'growth_analysis': {}
                }
                
                if performance_data:
                    # Process performance history
                    for row in performance_data:
                        analytics['performance_history'].append({
                            'period': row['period_start'].strftime('%Y-%m'),
                            'transactions': row['transaction_count'],
                            'volume': float(row['transaction_volume']),
                            'commission': float(row['total_commission']),
                            'new_customers': row['new_customers'],
                            'retention_rate': float(row['customer_retention_rate']),
                            'success_rate': float(row['success_rate']),
                            'satisfaction': float(row['customer_satisfaction']),
                            'regional_rank': row['regional_rank'],
                            'national_rank': row['national_rank'],
                            'volume_achievement': float(row['volume_achievement_rate']),
                            'transaction_achievement': float(row['transaction_achievement_rate'])
                        })
                    
                    # Calculate trends (comparing last 3 months with previous 3)
                    if len(performance_data) >= 6:
                        recent_avg = sum(row['transaction_volume'] for row in performance_data[:3]) / 3
                        previous_avg = sum(row['transaction_volume'] for row in performance_data[3:6]) / 3
                        
                        analytics['trends'] = {
                            'volume_trend': float((recent_avg - previous_avg) / previous_avg * 100) if previous_avg > 0 else 0,
                            'commission_trend': 0,  # Calculate similarly
                            'customer_trend': 0     # Calculate similarly
                        }
                    
                    # Calculate averages
                    analytics['averages'] = {
                        'avg_volume': float(sum(row['transaction_volume'] for row in performance_data) / len(performance_data)),
                        'avg_commission': float(sum(row['total_commission'] for row in performance_data) / len(performance_data)),
                        'avg_transactions': sum(row['transaction_count'] for row in performance_data) // len(performance_data),
                        'avg_success_rate': float(sum(row['success_rate'] for row in performance_data) / len(performance_data))
                    }
                    
                    # Find best performing month
                    best_month_data = max(performance_data, key=lambda x: x['transaction_volume'])
                    analytics['best_month'] = {
                        'period': best_month_data['period_start'].strftime('%Y-%m'),
                        'volume': float(best_month_data['transaction_volume']),
                        'commission': float(best_month_data['total_commission']),
                        'transactions': best_month_data['transaction_count']
                    }
                
                return analytics
                
        except Exception as e:
            logger.error(f"❌ Failed to get performance analytics: {e}")
            return {'error': str(e)}
    
    async def get_commission_breakdown(self, agent_id: str, month: str = None) -> Dict[str, Any]:
        """الحصول على تفصيل العمولات"""
        try:
            if not month:
                month = datetime.now().strftime('%Y-%m')
            
            async with self.db_connection.get_connection() as conn:
                # Get commission breakdown by type
                breakdown_query = """
                    SELECT 
                        commission_type,
                        COUNT(*) as count,
                        SUM(commission_amount) as total_amount,
                        AVG(commission_amount) as avg_amount,
                        MIN(commission_amount) as min_amount,
                        MAX(commission_amount) as max_amount
                    FROM agent_commissions 
                    WHERE agent_id = $1 AND commission_period = $2
                    GROUP BY commission_type
                """
                
                breakdown_data = await conn.fetch(breakdown_query, agent_id, month)
                
                # Get daily commission data for the month
                daily_query = """
                    SELECT 
                        DATE(created_at) as commission_date,
                        COUNT(*) as daily_count,
                        SUM(commission_amount) as daily_amount
                    FROM agent_commissions 
                    WHERE agent_id = $1 AND commission_period = $2
                    GROUP BY DATE(created_at)
                    ORDER BY commission_date
                """
                
                daily_data = await conn.fetch(daily_query, agent_id, month)
                
                # Get status breakdown
                status_query = """
                    SELECT 
                        status,
                        COUNT(*) as count,
                        SUM(commission_amount) as amount
                    FROM agent_commissions 
                    WHERE agent_id = $1 AND commission_period = $2
                    GROUP BY status
                """
                
                status_data = await conn.fetch(status_query, agent_id, month)
                
                return {
                    'month': month,
                    'breakdown_by_type': [
                        {
                            'type': row['commission_type'],
                            'count': row['count'],
                            'total_amount': float(row['total_amount']),
                            'avg_amount': float(row['avg_amount']),
                            'min_amount': float(row['min_amount']),
                            'max_amount': float(row['max_amount'])
                        }
                        for row in breakdown_data
                    ],
                    'daily_commissions': [
                        {
                            'date': row['commission_date'].strftime('%Y-%m-%d'),
                            'count': row['daily_count'],
                            'amount': float(row['daily_amount'])
                        }
                        for row in daily_data
                    ],
                    'status_breakdown': [
                        {
                            'status': row['status'],
                            'count': row['count'],
                            'amount': float(row['amount'])
                        }
                        for row in status_data
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get commission breakdown: {e}")
            return {'error': str(e)}
    
    # Helper methods
    async def _get_agent_basic_info(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على المعلومات الأساسية للوكيل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        ap.*,
                        u.first_name,
                        u.last_name,
                        u.email,
                        u.phone
                    FROM agent_profiles ap
                    JOIN users u ON ap.user_id = u.id
                    WHERE ap.id = $1 AND ap.deleted_at IS NULL
                """
                
                row = await conn.fetchrow(query, agent_id)
                
                if row:
                    return {
                        'agent_id': row['id'],
                        'agent_code': row['agent_code'],
                        'name': f"{row['first_name']} {row['last_name']}",
                        'email': row['email'],
                        'phone': row['phone'],
                        'business_name': row['business_name'],
                        'region': row['region'],
                        'city': row['city'],
                        'status': row['status'],
                        'level': row['level'],
                        'rating': float(row['rating']),
                        'certification_level': row['certification_level'],
                        'joined_date': row['created_at'].strftime('%Y-%m-%d')
                    }
                
                return {}
                
        except Exception as e:
            logger.error(f"❌ Failed to get agent basic info: {e}")
            return {}
    
    async def _get_dashboard_metrics(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على مقاييس لوحة التحكم"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Get overall metrics
                metrics_query = """
                    SELECT 
                        total_transactions,
                        total_volume,
                        total_commission_earned,
                        customer_count,
                        rating
                    FROM agent_profiles 
                    WHERE id = $1
                """
                
                metrics = await conn.fetchrow(metrics_query, agent_id)
                
                if metrics:
                    return {
                        'total_transactions': metrics['total_transactions'],
                        'total_volume': float(metrics['total_volume']),
                        'total_commission': float(metrics['total_commission_earned']),
                        'active_customers': metrics['customer_count'],
                        'rating': float(metrics['rating']),
                        'success_rate': 98.5,  # This would be calculated from actual data
                        'monthly_growth': 15.2  # This would be calculated from performance data
                    }
                
                return {}
                
        except Exception as e:
            logger.error(f"❌ Failed to get dashboard metrics: {e}")
            return {}
    
    async def _get_transaction_summary(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على ملخص المعاملات"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Today's transactions
                today_query = """
                    SELECT 
                        COUNT(*) as count,
                        COALESCE(SUM(amount), 0) as volume
                    FROM transactions 
                    WHERE agent_id = $1 
                    AND DATE(created_at) = CURRENT_DATE
                    AND status = 'completed'
                """
                
                today = await conn.fetchrow(today_query, agent_id)
                
                # This month's transactions
                month_query = """
                    SELECT 
                        COUNT(*) as count,
                        COALESCE(SUM(amount), 0) as volume
                    FROM transactions 
                    WHERE agent_id = $1 
                    AND DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE)
                    AND status = 'completed'
                """
                
                month = await conn.fetchrow(month_query, agent_id)
                
                return {
                    'today': {
                        'count': today['count'],
                        'volume': float(today['volume'])
                    },
                    'this_month': {
                        'count': month['count'],
                        'volume': float(month['volume'])
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get transaction summary: {e}")
            return {}
    
    async def _get_commission_summary(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على ملخص العمولات"""
        try:
            current_month = datetime.now().strftime('%Y-%m')
            
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        status,
                        SUM(commission_amount) as total
                    FROM agent_commissions 
                    WHERE agent_id = $1 AND commission_period = $2
                    GROUP BY status
                """
                
                rows = await conn.fetch(query, agent_id, current_month)
                
                summary = {
                    'pending': 0,
                    'approved': 0,
                    'paid': 0,
                    'total_earned': 0
                }
                
                for row in rows:
                    summary[row['status']] = float(row['total'])
                    summary['total_earned'] += float(row['total'])
                
                return summary
                
        except Exception as e:
            logger.error(f"❌ Failed to get commission summary: {e}")
            return {}
    
    async def _get_customer_summary(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على ملخص العملاء"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        COUNT(*) as total_customers,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_customers,
                        COUNT(CASE WHEN acquisition_date >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_customers_30d
                    FROM agent_customers 
                    WHERE agent_id = $1
                """
                
                row = await conn.fetchrow(query, agent_id)
                
                if row:
                    return {
                        'total_customers': row['total_customers'],
                        'active_customers': row['active_customers'],
                        'new_customers_30d': row['new_customers_30d']
                    }
                
                return {}
                
        except Exception as e:
            logger.error(f"❌ Failed to get customer summary: {e}")
            return {}
    
    async def _get_performance_summary(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على ملخص الأداء"""
        # This would get the latest performance metrics
        return {
            'current_rank': 15,
            'performance_score': 87.5,
            'target_achievement': 92.3
        }
    
    async def _get_hierarchy_summary(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على ملخص الهيكل الهرمي"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Count direct children
                children_query = """
                    SELECT COUNT(*) as direct_children
                    FROM agent_profiles 
                    WHERE parent_agent_id = $1 AND deleted_at IS NULL
                """
                
                direct_children = await conn.fetchval(children_query, agent_id)
                
                # Count total downline
                downline_query = """
                    WITH RECURSIVE agent_tree AS (
                        SELECT id FROM agent_profiles WHERE id = $1
                        UNION ALL
                        SELECT ap.id 
                        FROM agent_profiles ap
                        INNER JOIN agent_tree at ON ap.parent_agent_id = at.id
                        WHERE ap.deleted_at IS NULL
                    )
                    SELECT COUNT(*) - 1 as total_downline FROM agent_tree
                """
                
                total_downline = await conn.fetchval(downline_query, agent_id)
                
                return {
                    'direct_children': direct_children or 0,
                    'total_downline': total_downline or 0
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get hierarchy summary: {e}")
            return {}
    
    async def _get_recent_activities(self, agent_id: str) -> List[Dict[str, Any]]:
        """الحصول على الأنشطة الحديثة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        'transaction' as type,
                        t.id,
                        t.transaction_number,
                        t.amount,
                        t.status,
                        t.created_at,
                        u.first_name || ' ' || u.last_name as customer_name
                    FROM transactions t
                    JOIN users u ON t.sender_id = u.id
                    WHERE t.agent_id = $1
                    ORDER BY t.created_at DESC
                    LIMIT 10
                """
                
                rows = await conn.fetch(query, agent_id)
                
                activities = []
                for row in rows:
                    activities.append({
                        'type': row['type'],
                        'id': row['id'],
                        'description': f"Transaction {row['transaction_number']} - {row['customer_name']}",
                        'amount': float(row['amount']),
                        'status': row['status'],
                        'timestamp': row['created_at'].isoformat()
                    })
                
                return activities
                
        except Exception as e:
            logger.error(f"❌ Failed to get recent activities: {e}")
            return []
    
    async def _get_charts_data(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على بيانات الرسوم البيانية"""
        # This would return data for various charts
        return {
            'monthly_volume': [],
            'commission_trend': [],
            'customer_growth': [],
            'performance_radar': {}
        }
    
    async def _get_agent_notifications(self, agent_id: str) -> List[Dict[str, Any]]:
        """الحصول على إشعارات الوكيل"""
        # This would get notifications specific to the agent
        return [
            {
                'id': 'notif_1',
                'type': 'commission',
                'title': 'عمولة جديدة',
                'message': 'تم إضافة عمولة بقيمة 150 ريال',
                'timestamp': datetime.now().isoformat(),
                'read': False
            }
        ]
    
    async def _get_targets_progress(self, agent_id: str) -> Dict[str, Any]:
        """الحصول على تقدم الأهداف"""
        # This would get the agent's targets and progress
        return {
            'monthly_volume_target': 500000,
            'monthly_volume_achieved': 425000,
            'monthly_transaction_target': 200,
            'monthly_transaction_achieved': 175,
            'customer_acquisition_target': 10,
            'customer_acquisition_achieved': 8
        }
