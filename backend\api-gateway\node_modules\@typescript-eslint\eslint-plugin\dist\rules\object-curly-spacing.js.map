{"version": 3, "file": "object-curly-spacing.js", "sourceRoot": "", "sources": ["../../src/rules/object-curly-spacing.ts"], "names": [], "mappings": ";;AACA,oDAA2E;AAC3E,wEAAsE;AAMtE,kCAKiB;AACjB,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,sBAAsB,CAAC,CAAC;AAK3D,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,sBAAsB;IAC5B,iQAAiQ;IACjQ,IAAI,EAAE;QACJ,GAAG,QAAQ,CAAC,IAAI;QAChB,UAAU,EAAE,IAAI;QAChB,IAAI,EAAE;YACJ,WAAW,EAAE,0CAA0C;YACvD,eAAe,EAAE,IAAI;SACtB;QACD,UAAU,EAAE,CAAC,oCAAoC,CAAC;KACnD;IACD,cAAc,EAAE,CAAC,OAAO,CAAC;IACzB,MAAM,CAAC,OAAO;QACZ,uFAAuF;QACvF,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;QACpD,MAAM,MAAM,GAAG,WAAW,KAAK,QAAQ,CAAC;QACxC,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C;;;;;;WAMG;QACH,SAAS,WAAW,CAClB,MAA8C;YAE9C,OAAO,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;QACjE,CAAC;QAED,MAAM,OAAO,GAAG;YACd,MAAM;YACN,wBAAwB,EAAE,WAAW,CAAC,iBAAiB,CAAC;YACxD,yBAAyB,EAAE,WAAW,CAAC,kBAAkB,CAAC;SAC3D,CAAC;QAEF,4EAA4E;QAC5E,UAAU;QACV,4EAA4E;QAE5E;;;;WAIG;QACH,SAAS,sBAAsB,CAC7B,IAAoD,EACpD,KAAqB;YAErB,MAAM,SAAS,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE;gBAC5D,eAAe,EAAE,IAAI;aACtB,CAAE,CAAC;YAEJ,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,GAAG,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE;gBACvD,SAAS,EAAE,sBAAsB;gBACjC,IAAI,EAAE;oBACJ,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB;gBACD,GAAG,CAAC,KAAK;oBACP,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjE,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED;;;;WAIG;QACH,SAAS,mBAAmB,CAC1B,IAAoD,EACpD,KAAqB;YAErB,MAAM,aAAa,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC,cAAc,CAAC,KAAK,EAAE;gBACjE,eAAe,EAAE,IAAI;aACtB,CAAE,CAAC;YAEJ,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,GAAG,EAAE,EAAE,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE;gBAC3D,SAAS,EAAE,uBAAuB;gBAClC,IAAI,EAAE;oBACJ,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB;gBACD,GAAG,CAAC,KAAK;oBACP,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED;;;;WAIG;QACH,SAAS,4BAA4B,CACnC,IAAoD,EACpD,KAAqB;YAErB,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,SAAS,EAAE,mBAAmB;gBAC9B,IAAI,EAAE;oBACJ,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB;gBACD,GAAG,CAAC,KAAK;oBACP,OAAO,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC3C,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED;;;;WAIG;QACH,SAAS,yBAAyB,CAChC,IAAoD,EACpD,KAAqB;YAErB,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,SAAS,EAAE,oBAAoB;gBAC/B,IAAI,EAAE;oBACJ,KAAK,EAAE,KAAK,CAAC,KAAK;iBACnB;gBACD,GAAG,CAAC,KAAK;oBACP,OAAO,KAAK,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC5C,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED;;;;;;;WAOG;QACH,SAAS,oBAAoB,CAC3B,IAAoD,EACpD,KAAqB,EACrB,MAAsB,EACtB,WAA2B,EAC3B,IAAoB;YAEpB,IAAI,IAAA,wBAAiB,EAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC;gBACrC,MAAM,WAAW,GAAG,UAAU,CAAC,cAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC9D,MAAM,UAAU,GAAG,UAAU,CAAC,mBAAmB,CAC/C,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CACf,CAAC,IAAI,CAAC;gBAER,MAAM,6BAA6B,GACjC,OAAO,CAAC,wBAAwB;oBAChC;wBACE,sBAAc,CAAC,YAAY;wBAC3B,sBAAc,CAAC,gBAAgB;qBAChC,CAAC,QAAQ,CAAC,UAAU,CAAC;oBACpB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;oBACjB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;gBAErB,IAAI,6BAA6B,IAAI,CAAC,WAAW,EAAE,CAAC;oBAClD,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC5C,CAAC;gBACD,IACE,CAAC,6BAA6B;oBAC9B,WAAW;oBACX,MAAM,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI,EACpC,CAAC;oBACD,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;YAED,IAAI,IAAA,wBAAiB,EAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC;gBACzC,MAAM,sBAAsB,GAC1B,CAAC,OAAO,CAAC,wBAAwB;oBAC/B,IAAA,4BAAqB,EAAC,WAAW,CAAC,CAAC;oBACrC,CAAC,OAAO,CAAC,yBAAyB;wBAChC,IAAA,0BAAmB,EAAC,WAAW,CAAC,CAAC,CAAC;gBACtC,MAAM,eAAe,GAAG,sBAAsB;oBAC5C,CAAC,CAAC,UAAU,CAAC,mBAAmB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC,IAAI;oBAC5D,CAAC,CAAC,SAAS,CAAC;gBAEd,MAAM,6BAA6B,GACjC,CAAC,OAAO,CAAC,wBAAwB;oBAC/B,eAAe,KAAK,sBAAc,CAAC,WAAW,CAAC;oBACjD,CAAC,OAAO,CAAC,yBAAyB;wBAChC,eAAe,KAAK,SAAS;wBAC7B;4BACE,sBAAc,CAAC,YAAY;4BAC3B,sBAAc,CAAC,aAAa;yBAC7B,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;oBACjB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;gBAErB,MAAM,UAAU,GAAG,UAAU,CAAC,cAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBAEjE,IAAI,6BAA6B,IAAI,CAAC,UAAU,EAAE,CAAC;oBACjD,yBAAyB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACxC,CAAC;gBACD,IAAI,CAAC,6BAA6B,IAAI,UAAU,EAAE,CAAC;oBACjD,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;QAED;;;;;;;;;;WAUG;QACH,SAAS,uBAAuB,CAC9B,IAA4B;YAE5B,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAE3D,OAAO,UAAU,CAAC,aAAa,CAAC,YAAY,EAAE,0BAAmB,CAAC,CAAC;QACrE,CAAC;QAED,4EAA4E;QAC5E,SAAS;QACT,4EAA4E;QAE5E,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,OAAO;YACL,GAAG,KAAK;YACR,YAAY,CAAC,IAA2B;gBACtC,MAAM,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC;gBAC9C,MAAM,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAE,CAAC;gBAC5C,MAAM,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE;oBAC7C,eAAe,EAAE,IAAI;iBACtB,CAAE,CAAC;gBACJ,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE;oBAClD,eAAe,EAAE,IAAI;iBACtB,CAAE,CAAC;gBAEJ,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;YAC/D,CAAC;YACD,aAAa,CAAC,IAA4B;gBACxC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC9B,OAAO;gBACT,CAAC;gBAED,MAAM,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC;gBAC9C,MAAM,IAAI,GAAG,uBAAuB,CAAC,IAAI,CAAE,CAAC;gBAC5C,MAAM,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE;oBAC7C,eAAe,EAAE,IAAI;iBACtB,CAAE,CAAC;gBACJ,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE;oBAClD,eAAe,EAAE,IAAI;iBACtB,CAAE,CAAC;gBAEJ,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;YAC/D,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}