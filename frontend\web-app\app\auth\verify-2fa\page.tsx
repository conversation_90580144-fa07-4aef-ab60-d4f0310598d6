'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  ArrowRightIcon,
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

// مخطط التحقق
const verify2FASchema = z.object({
  code: z
    .string()
    .min(1, 'رمز التحقق مطلوب')
    .length(6, 'رمز التحقق يجب أن يكون 6 أرقام')
    .regex(/^\d+$/, 'رمز التحقق يجب أن يحتوي على أرقام فقط'),
});

type Verify2FAFormData = z.infer<typeof verify2FASchema>;

export default function Verify2FAPage() {
  const [timeLeft, setTimeLeft] = useState(300); // 5 دقائق
  const [canResend, setCanResend] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  
  const router = useRouter();
  const { verify2FA, state: authState } = useAuth();
  const { t, isRTL } = useLanguage();

  // إعداد النموذج
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
    setValue,
    watch,
  } = useForm<Verify2FAFormData>({
    resolver: zodResolver(verify2FASchema),
    defaultValues: {
      code: '',
    },
  });

  const watchedCode = watch('code');

  // إعادة توجيه إذا لم يكن هناك حاجة للمصادقة الثنائية
  useEffect(() => {
    if (!authState.requires2FA || !authState.tempToken) {
      router.push('/auth/login');
    }
  }, [authState.requires2FA, authState.tempToken, router]);

  // مؤقت العد التنازلي
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  // تنسيق الوقت المتبقي
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // معالجة إدخال الرمز
  const handleCodeInput = (index: number, value: string) => {
    if (value.length > 1) {
      // إذا تم لصق رمز كامل
      const code = value.slice(0, 6);
      setValue('code', code);
      
      // توزيع الأرقام على الحقول
      for (let i = 0; i < 6; i++) {
        if (inputRefs.current[i]) {
          inputRefs.current[i]!.value = code[i] || '';
        }
      }
      
      // التركيز على آخر حقل مملوء
      const lastFilledIndex = Math.min(code.length - 1, 5);
      inputRefs.current[lastFilledIndex]?.focus();
    } else {
      // إدخال رقم واحد
      const currentCode = watchedCode.split('');
      currentCode[index] = value;
      const newCode = currentCode.join('');
      setValue('code', newCode);
      
      // الانتقال للحقل التالي
      if (value && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  // معالجة حذف الرمز
  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !e.currentTarget.value && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // معالجة التحقق
  const onSubmit = async (data: Verify2FAFormData) => {
    try {
      await verify2FA(data.code);
    } catch (error: any) {
      if (error.response?.status === 400) {
        setError('code', {
          message: 'رمز التحقق غير صحيح',
        });
      } else if (error.response?.status === 410) {
        setError('root', {
          message: 'انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد',
        });
        setCanResend(true);
      } else {
        setError('root', {
          message: error.message || 'حدث خطأ أثناء التحقق',
        });
      }
    }
  };

  // إعادة إرسال الرمز
  const handleResend = async () => {
    if (!canResend || isResending) return;
    
    setIsResending(true);
    try {
      // TODO: استدعاء API لإعادة إرسال الرمز
      await new Promise(resolve => setTimeout(resolve, 1000)); // محاكاة
      
      setTimeLeft(300);
      setCanResend(false);
      setValue('code', '');
      
      // مسح جميع الحقول
      inputRefs.current.forEach(input => {
        if (input) input.value = '';
      });
      
      // التركيز على الحقل الأول
      inputRefs.current[0]?.focus();
    } catch (error) {
      setError('root', {
        message: 'فشل في إعادة إرسال الرمز',
      });
    } finally {
      setIsResending(false);
    }
  };

  if (!authState.requires2FA) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* الشعار */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-700 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold">WS</span>
            </div>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              WS Transfir
            </span>
          </div>
        </motion.div>

        {/* بطاقة التحقق */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="p-8">
            {/* الأيقونة والعنوان */}
            <div className="text-center mb-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: 'spring' }}
                className="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <ShieldCheckIcon className="w-8 h-8 text-primary-600 dark:text-primary-400" />
              </motion.div>
              
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                المصادقة الثنائية
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                تم إرسال رمز التحقق إلى هاتفك المسجل
              </p>
            </div>

            {/* رسالة الخطأ العامة */}
            {errors.root && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mb-4 p-4 bg-error-50 dark:bg-error-900/20 border border-error-200 dark:border-error-800 rounded-lg flex items-center space-x-3 rtl:space-x-reverse"
              >
                <ExclamationTriangleIcon className="w-5 h-5 text-error-600 dark:text-error-400 flex-shrink-0" />
                <span className="text-error-700 dark:text-error-300 text-sm">
                  {errors.root.message}
                </span>
              </motion.div>
            )}

            {/* النموذج */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* حقول إدخال الرمز */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4 text-center">
                  أدخل رمز التحقق المكون من 6 أرقام
                </label>
                
                <div className="flex justify-center space-x-3 rtl:space-x-reverse mb-4">
                  {[0, 1, 2, 3, 4, 5].map((index) => (
                    <input
                      key={index}
                      ref={(el) => (inputRefs.current[index] = el)}
                      type="text"
                      inputMode="numeric"
                      maxLength={6}
                      className={`w-12 h-12 text-center text-lg font-semibold border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                        errors.code
                          ? 'border-error-500 bg-error-50 dark:bg-error-900/20'
                          : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800'
                      } text-gray-900 dark:text-white`}
                      onChange={(e) => handleCodeInput(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      onPaste={(e) => {
                        e.preventDefault();
                        const pastedData = e.clipboardData.getData('text');
                        handleCodeInput(index, pastedData);
                      }}
                    />
                  ))}
                </div>
                
                {errors.code && (
                  <p className="text-center text-sm text-error-600 dark:text-error-400">
                    {errors.code.message}
                  </p>
                )}
              </div>

              {/* مؤقت العد التنازلي */}
              <div className="text-center">
                <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse text-gray-600 dark:text-gray-400">
                  <ClockIcon className="w-4 h-4" />
                  <span className="text-sm">
                    {timeLeft > 0 ? (
                      <>انتهاء الصلاحية خلال {formatTime(timeLeft)}</>
                    ) : (
                      'انتهت صلاحية الرمز'
                    )}
                  </span>
                </div>
              </div>

              {/* زر التحقق */}
              <Button
                type="submit"
                fullWidth
                size="lg"
                isLoading={isSubmitting}
                loadingText="جاري التحقق..."
                disabled={watchedCode.length !== 6}
                rightIcon={<ArrowRightIcon className="w-5 h-5" />}
              >
                تحقق من الرمز
              </Button>

              {/* زر إعادة الإرسال */}
              <div className="text-center">
                <button
                  type="button"
                  onClick={handleResend}
                  disabled={!canResend || isResending}
                  className={`text-sm font-medium transition-colors duration-200 ${
                    canResend && !isResending
                      ? 'text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300'
                      : 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  }`}
                >
                  {isResending ? 'جاري الإرسال...' : 'إعادة إرسال الرمز'}
                </button>
              </div>
            </form>

            {/* رابط العودة */}
            <div className="mt-6 text-center">
              <button
                onClick={() => router.push('/auth/login')}
                className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
              >
                العودة لتسجيل الدخول
              </button>
            </div>
          </Card>
        </motion.div>

        {/* معلومات الأمان */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mt-8 text-center"
        >
          <p className="text-sm text-gray-500 dark:text-gray-400">
            المصادقة الثنائية تحمي حسابك من الوصول غير المصرح به
          </p>
        </motion.div>
      </div>
    </div>
  );
}
