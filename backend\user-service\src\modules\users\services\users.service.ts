import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindManyOptions } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User, UserStatus, UserRole } from '../entities/user.entity';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';

export interface FindUsersOptions {
  page?: number;
  limit?: number;
  search?: string;
  status?: UserStatus;
  role?: UserRole;
}

export interface PaginatedUsers {
  data: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const { email, phone, password, ...userData } = createUserDto;

    // التحقق من وجود المستخدم
    const existingUser = await this.userRepository.findOne({
      where: [{ email }, { phone }],
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw new ConflictException('البريد الإلكتروني مستخدم بالفعل');
      }
      if (existingUser.phone === phone) {
        throw new ConflictException('رقم الهاتف مستخدم بالفعل');
      }
    }

    // تشفير كلمة المرور
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // إنشاء المستخدم
    const user = this.userRepository.create({
      ...userData,
      email,
      phone,
      password: hashedPassword,
    });

    const savedUser = await this.userRepository.save(user);
    this.logger.log(`User created: ${savedUser.email}`);

    return savedUser;
  }

  async findAll(options: FindUsersOptions = {}): Promise<PaginatedUsers> {
    const { page = 1, limit = 10, search, status, role } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.userRepository.createQueryBuilder('user');

    // البحث
    if (search) {
      queryBuilder.where(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // تصفية حسب الحالة
    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    // تصفية حسب الدور
    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    // ترتيب وتصفح
    queryBuilder
      .orderBy('user.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile', 'wallets'],
    });

    if (!user) {
      throw new NotFoundException('المستخدم غير موجود');
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } });
  }

  async findByPhone(phone: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { phone } });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(id);

    // التحقق من تفرد البريد الإلكتروني ورقم الهاتف
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.findByEmail(updateUserDto.email);
      if (existingUser) {
        throw new ConflictException('البريد الإلكتروني مستخدم بالفعل');
      }
    }

    if (updateUserDto.phone && updateUserDto.phone !== user.phone) {
      const existingUser = await this.findByPhone(updateUserDto.phone);
      if (existingUser) {
        throw new ConflictException('رقم الهاتف مستخدم بالفعل');
      }
    }

    // تحديث البيانات
    Object.assign(user, updateUserDto);
    const updatedUser = await this.userRepository.save(user);

    this.logger.log(`User updated: ${updatedUser.email}`);
    return updatedUser;
  }

  async updateStatus(id: string, status: UserStatus): Promise<User> {
    const user = await this.findById(id);
    user.status = status;
    
    const updatedUser = await this.userRepository.save(user);
    this.logger.log(`User status updated: ${updatedUser.email} -> ${status}`);
    
    return updatedUser;
  }

  async remove(id: string): Promise<void> {
    const user = await this.findById(id);
    
    // حذف ناعم
    user.deletedAt = new Date();
    user.status = UserStatus.DELETED;
    
    await this.userRepository.save(user);
    this.logger.log(`User deleted: ${user.email}`);
  }

  async getUserActivity(id: string): Promise<any> {
    const user = await this.findById(id);
    
    // هنا يمكن إضافة منطق لاسترجاع نشاط المستخدم
    // من جداول أخرى مثل التحويلات، تسجيل الدخول، إلخ
    
    return {
      userId: user.id,
      lastLoginAt: user.lastLoginAt,
      lastLoginIp: user.lastLoginIp,
      createdAt: user.createdAt,
      status: user.status,
      // يمكن إضافة المزيد من البيانات هنا
    };
  }

  async validatePassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password);
  }

  async updatePassword(id: string, newPassword: string): Promise<void> {
    const user = await this.findById(id);
    
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    user.password = hashedPassword;
    user.passwordChangedAt = new Date();
    
    await this.userRepository.save(user);
    this.logger.log(`Password updated for user: ${user.email}`);
  }
}
