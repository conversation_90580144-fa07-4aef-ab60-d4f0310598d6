"""
Webhook Service
==============
خدمة Webhook المتقدمة للإشعارات الخارجية
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from decimal import Decimal
import json
import uuid
import hashlib
import hmac
import base64
from urllib.parse import urlencode

try:
    import asyncpg
except ImportError:
    asyncpg = None

try:
    import aiohttp
except ImportError:
    aiohttp = None

try:
    from ..shared.database.connection import DatabaseConnection
except ImportError:
    # Fallback for testing or standalone usage
    class DatabaseConnection:
        def __init__(self):
            pass

        async def get_connection(self):
            return None

logger = logging.getLogger(__name__)


class WebhookEvent(Enum):
    """أحداث Webhook"""
    PAYMENT_CREATED = "payment.created"
    PAYMENT_COMPLETED = "payment.completed"
    PAYMENT_FAILED = "payment.failed"
    PAYMENT_CANCELLED = "payment.cancelled"
    REFUND_CREATED = "refund.created"
    REFUND_COMPLETED = "refund.completed"
    TRANSFER_CREATED = "transfer.created"
    TRANSFER_COMPLETED = "transfer.completed"
    TRANSFER_FAILED = "transfer.failed"
    SETTLEMENT_CREATED = "settlement.created"
    SETTLEMENT_COMPLETED = "settlement.completed"
    BALANCE_UPDATED = "balance.updated"
    ACCOUNT_CREATED = "account.created"
    ACCOUNT_UPDATED = "account.updated"
    RISK_ALERT = "risk.alert"
    COMPLIANCE_ALERT = "compliance.alert"


class WebhookStatus(Enum):
    """حالات Webhook"""
    PENDING = "pending"
    SENDING = "sending"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRYING = "retrying"
    EXPIRED = "expired"


class SignatureMethod(Enum):
    """طرق التوقيع"""
    HMAC_SHA256 = "hmac_sha256"
    HMAC_SHA512 = "hmac_sha512"
    RSA_SHA256 = "rsa_sha256"


@dataclass
class WebhookEndpoint:
    """نقطة نهاية Webhook"""
    endpoint_id: str
    partner_id: str
    url: str
    events: List[WebhookEvent]
    secret_key: str
    signature_method: SignatureMethod
    is_active: bool = True
    max_retries: int = 3
    retry_delay: int = 60  # seconds
    timeout: int = 30  # seconds
    metadata: Dict[str, Any] = None


@dataclass
class WebhookPayload:
    """حمولة Webhook"""
    event: WebhookEvent
    data: Dict[str, Any]
    timestamp: datetime
    webhook_id: str
    partner_id: str
    signature: str = None


@dataclass
class WebhookDelivery:
    """تسليم Webhook"""
    delivery_id: str
    webhook_id: str
    endpoint_id: str
    partner_id: str
    event: WebhookEvent
    payload: Dict[str, Any]
    status: WebhookStatus
    attempts: int = 0
    max_retries: int = 3
    next_retry_at: datetime = None
    response_status: int = None
    response_body: str = None
    error_message: str = None
    created_at: datetime = None
    delivered_at: datetime = None


class WebhookService:
    """خدمة Webhook المتقدمة"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection

        # Check dependencies
        if asyncpg is None:
            logger.warning("asyncpg not available - database operations will be mocked")
        if aiohttp is None:
            logger.warning("aiohttp not available - HTTP operations will be mocked")
        
        # Webhook configurations
        self.webhook_config = {
            "max_payload_size": 1024 * 1024,  # 1MB
            "default_timeout": 30,
            "default_retries": 3,
            "retry_delays": [60, 300, 900],  # 1min, 5min, 15min
            "max_retry_delay": 3600,  # 1 hour
            "signature_header": "X-Webhook-Signature",
            "timestamp_header": "X-Webhook-Timestamp",
            "event_header": "X-Webhook-Event",
            "id_header": "X-Webhook-ID"
        }
        
        # HTTP client session
        self.http_session = None
        
        # Statistics
        self.webhooks_sent = 0
        self.webhooks_delivered = 0
        self.webhooks_failed = 0
        
        # Background tasks
        self.retry_task = None
        self.cleanup_task = None
    
    async def initialize(self):
        """تهيئة الخدمة"""
        # Create HTTP session if aiohttp is available
        if aiohttp is not None:
            timeout = aiohttp.ClientTimeout(total=self.webhook_config["default_timeout"])
            self.http_session = aiohttp.ClientSession(timeout=timeout)
        else:
            logger.warning("aiohttp not available - webhook HTTP operations will be mocked")
            self.http_session = None
        
        # Start background tasks
        self.retry_task = asyncio.create_task(self._retry_failed_webhooks())
        self.cleanup_task = asyncio.create_task(self._cleanup_old_deliveries())
        
        logger.info("🔗 Webhook service initialized")
    
    async def shutdown(self):
        """إغلاق الخدمة"""
        # Cancel background tasks
        if self.retry_task:
            self.retry_task.cancel()
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Close HTTP session
        if self.http_session:
            await self.http_session.close()
        
        logger.info("🔗 Webhook service shutdown")
    
    async def register_endpoint(self, endpoint: WebhookEndpoint) -> bool:
        """تسجيل نقطة نهاية webhook"""
        try:
            logger.info(f"🔗 Registering webhook endpoint: {endpoint.url}")
            
            # Validate endpoint
            await self._validate_endpoint(endpoint)
            
            # Store endpoint configuration
            await self._store_endpoint_config(endpoint)
            
            # Test endpoint connectivity
            test_result = await self._test_endpoint_connectivity(endpoint)
            
            if not test_result:
                logger.warning(f"⚠️ Webhook endpoint test failed: {endpoint.url}")
            
            logger.info(f"✅ Webhook endpoint registered: {endpoint.endpoint_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to register webhook endpoint: {e}")
            return False
    
    async def send_webhook(
        self, 
        partner_id: str, 
        event: WebhookEvent, 
        data: Dict[str, Any]
    ) -> List[str]:
        """إرسال webhook"""
        try:
            logger.info(f"🔗 Sending webhook: {event.value} to partner {partner_id}")
            
            # Get partner's webhook endpoints for this event
            endpoints = await self._get_partner_endpoints(partner_id, event)
            
            if not endpoints:
                logger.info(f"📭 No webhook endpoints found for {partner_id} and event {event.value}")
                return []
            
            delivery_ids = []
            
            for endpoint in endpoints:
                try:
                    # Create webhook payload
                    webhook_id = f"wh_{uuid.uuid4().hex[:12]}"
                    payload = WebhookPayload(
                        event=event,
                        data=data,
                        timestamp=datetime.now(),
                        webhook_id=webhook_id,
                        partner_id=partner_id
                    )
                    
                    # Generate signature
                    payload.signature = await self._generate_signature(payload, endpoint)
                    
                    # Create delivery record
                    delivery = WebhookDelivery(
                        delivery_id=f"del_{uuid.uuid4().hex[:12]}",
                        webhook_id=webhook_id,
                        endpoint_id=endpoint.endpoint_id,
                        partner_id=partner_id,
                        event=event,
                        payload=asdict(payload),
                        status=WebhookStatus.PENDING,
                        max_retries=endpoint.max_retries,
                        created_at=datetime.now()
                    )
                    
                    # Store delivery record
                    await self._store_delivery_record(delivery)
                    
                    # Send webhook
                    await self._send_webhook_delivery(delivery, endpoint)
                    
                    delivery_ids.append(delivery.delivery_id)
                    
                except Exception as e:
                    logger.error(f"❌ Failed to send webhook to endpoint {endpoint.endpoint_id}: {e}")
                    continue
            
            self.webhooks_sent += len(delivery_ids)
            logger.info(f"✅ Webhook sent to {len(delivery_ids)} endpoints")
            return delivery_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to send webhook: {e}")
            return []
    
    async def get_delivery_status(self, delivery_id: str) -> Optional[WebhookDelivery]:
        """الحصول على حالة التسليم"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM webhook_deliveries WHERE delivery_id = $1
                """
                
                row = await conn.fetchrow(query, delivery_id)
                
                if row:
                    return WebhookDelivery(
                        delivery_id=row['delivery_id'],
                        webhook_id=row['webhook_id'],
                        endpoint_id=row['endpoint_id'],
                        partner_id=row['partner_id'],
                        event=WebhookEvent(row['event']),
                        payload=json.loads(row['payload']),
                        status=WebhookStatus(row['status']),
                        attempts=row['attempts'],
                        max_retries=row['max_retries'],
                        next_retry_at=row['next_retry_at'],
                        response_status=row['response_status'],
                        response_body=row['response_body'],
                        error_message=row['error_message'],
                        created_at=row['created_at'],
                        delivered_at=row['delivered_at']
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get delivery status: {e}")
            return None
    
    async def get_partner_deliveries(
        self, 
        partner_id: str, 
        limit: int = 100,
        status: WebhookStatus = None
    ) -> List[Dict[str, Any]]:
        """الحصول على تسليمات الشريك"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build query
                where_conditions = ["partner_id = $1"]
                params = [partner_id]
                param_count = 1
                
                if status:
                    param_count += 1
                    where_conditions.append(f"status = ${param_count}")
                    params.append(status.value)
                
                where_clause = " AND ".join(where_conditions)
                
                query = f"""
                    SELECT 
                        delivery_id, webhook_id, endpoint_id, event, status,
                        attempts, max_retries, response_status, created_at, delivered_at
                    FROM webhook_deliveries 
                    WHERE {where_clause}
                    ORDER BY created_at DESC
                    LIMIT {limit}
                """
                
                rows = await conn.fetch(query, *params)
                
                return [
                    {
                        "delivery_id": row['delivery_id'],
                        "webhook_id": row['webhook_id'],
                        "endpoint_id": row['endpoint_id'],
                        "event": row['event'],
                        "status": row['status'],
                        "attempts": row['attempts'],
                        "max_retries": row['max_retries'],
                        "response_status": row['response_status'],
                        "created_at": row['created_at'].isoformat(),
                        "delivered_at": row['delivered_at'].isoformat() if row['delivered_at'] else None
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get partner deliveries: {e}")
            return []
    
    async def retry_delivery(self, delivery_id: str) -> bool:
        """إعادة محاولة التسليم"""
        try:
            # Get delivery record
            delivery = await self.get_delivery_status(delivery_id)
            
            if not delivery:
                logger.error(f"❌ Delivery not found: {delivery_id}")
                return False
            
            if delivery.status == WebhookStatus.DELIVERED:
                logger.info(f"📦 Delivery already delivered: {delivery_id}")
                return True
            
            if delivery.attempts >= delivery.max_retries:
                logger.error(f"❌ Max retries exceeded for delivery: {delivery_id}")
                return False
            
            # Get endpoint configuration
            endpoint = await self._get_endpoint_config(delivery.endpoint_id)
            
            if not endpoint:
                logger.error(f"❌ Endpoint not found: {delivery.endpoint_id}")
                return False
            
            # Retry delivery
            await self._send_webhook_delivery(delivery, endpoint)
            
            logger.info(f"🔄 Delivery retry initiated: {delivery_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to retry delivery: {e}")
            return False
    
    async def get_webhook_statistics(
        self, 
        partner_id: str = None,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> Dict[str, Any]:
        """الحصول على إحصائيات webhook"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = []
                params = []
                param_count = 0
                
                if partner_id:
                    param_count += 1
                    where_conditions.append(f"partner_id = ${param_count}")
                    params.append(partner_id)
                
                if start_date:
                    param_count += 1
                    where_conditions.append(f"created_at >= ${param_count}")
                    params.append(start_date)
                
                if end_date:
                    param_count += 1
                    where_conditions.append(f"created_at <= ${param_count}")
                    params.append(end_date)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Overall statistics
                stats_query = f"""
                    SELECT 
                        COUNT(*) as total_deliveries,
                        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                        COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_count,
                        AVG(attempts) as avg_attempts
                    FROM webhook_deliveries 
                    WHERE {where_clause}
                """
                
                stats = await conn.fetchrow(stats_query, *params)
                
                # Event breakdown
                event_query = f"""
                    SELECT 
                        event,
                        COUNT(*) as count,
                        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered
                    FROM webhook_deliveries 
                    WHERE {where_clause}
                    GROUP BY event
                    ORDER BY count DESC
                """
                
                event_stats = await conn.fetch(event_query, *params)
                
                # Daily trend
                daily_query = f"""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as deliveries,
                        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered
                    FROM webhook_deliveries 
                    WHERE {where_clause}
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                    LIMIT 30
                """
                
                daily_stats = await conn.fetch(daily_query, *params)
                
                # Calculate delivery rate
                total_deliveries = stats['total_deliveries']
                delivery_rate = (stats['delivered_count'] / max(total_deliveries, 1)) * 100
                
                return {
                    "overview": {
                        "total_deliveries": total_deliveries,
                        "delivered_count": stats['delivered_count'],
                        "failed_count": stats['failed_count'],
                        "expired_count": stats['expired_count'],
                        "delivery_rate": round(delivery_rate, 2),
                        "avg_attempts": round(float(stats['avg_attempts'] or 0), 2)
                    },
                    "by_event": [
                        {
                            "event": row['event'],
                            "total": row['count'],
                            "delivered": row['delivered'],
                            "delivery_rate": round((row['delivered'] / max(row['count'], 1)) * 100, 2)
                        }
                        for row in event_stats
                    ],
                    "daily_trend": [
                        {
                            "date": row['date'].isoformat(),
                            "total_deliveries": row['deliveries'],
                            "delivered_count": row['delivered'],
                            "delivery_rate": round((row['delivered'] / max(row['deliveries'], 1)) * 100, 2)
                        }
                        for row in daily_stats
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get webhook statistics: {e}")
            return {}
    
    # Helper methods
    async def _validate_endpoint(self, endpoint: WebhookEndpoint):
        """التحقق من صحة نقطة النهاية"""
        if not endpoint.url.startswith(('http://', 'https://')):
            raise ValueError("Webhook URL must use HTTP or HTTPS")
        
        if not endpoint.events:
            raise ValueError("At least one event must be specified")
        
        if not endpoint.secret_key:
            raise ValueError("Secret key is required")
        
        if endpoint.max_retries < 0 or endpoint.max_retries > 10:
            raise ValueError("Max retries must be between 0 and 10")
        
        if endpoint.timeout < 5 or endpoint.timeout > 300:
            raise ValueError("Timeout must be between 5 and 300 seconds")
    
    async def _store_endpoint_config(self, endpoint: WebhookEndpoint):
        """حفظ إعدادات نقطة النهاية"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO webhook_endpoints (
                        endpoint_id, partner_id, url, events, secret_key,
                        signature_method, is_active, max_retries, retry_delay,
                        timeout, metadata
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    ON CONFLICT (endpoint_id) DO UPDATE SET
                        url = EXCLUDED.url,
                        events = EXCLUDED.events,
                        secret_key = EXCLUDED.secret_key,
                        signature_method = EXCLUDED.signature_method,
                        is_active = EXCLUDED.is_active,
                        max_retries = EXCLUDED.max_retries,
                        retry_delay = EXCLUDED.retry_delay,
                        timeout = EXCLUDED.timeout,
                        metadata = EXCLUDED.metadata,
                        updated_at = CURRENT_TIMESTAMP
                """
                
                await conn.execute(
                    query,
                    endpoint.endpoint_id,
                    endpoint.partner_id,
                    endpoint.url,
                    [event.value for event in endpoint.events],
                    endpoint.secret_key,
                    endpoint.signature_method.value,
                    endpoint.is_active,
                    endpoint.max_retries,
                    endpoint.retry_delay,
                    endpoint.timeout,
                    json.dumps(endpoint.metadata or {})
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store endpoint config: {e}")
            raise
    
    async def _test_endpoint_connectivity(self, endpoint: WebhookEndpoint) -> bool:
        """اختبار اتصال نقطة النهاية"""
        try:
            # Check if HTTP session is available
            if self.http_session is None:
                logger.warning("HTTP session not available - mocking endpoint connectivity test")
                return True  # Mock successful connectivity

            # Create test payload
            test_payload = {
                "event": "webhook.test",
                "data": {"test": True},
                "timestamp": datetime.now().isoformat(),
                "webhook_id": f"test_{uuid.uuid4().hex[:8]}"
            }

            # Generate test signature
            signature = self._generate_test_signature(test_payload, endpoint.secret_key)

            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "WalletSystem-Webhook/1.0",
                self.webhook_config["signature_header"]: signature,
                self.webhook_config["event_header"]: "webhook.test"
            }

            # Send test request
            if aiohttp is not None:
                async with self.http_session.post(
                    endpoint.url,
                    json=test_payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status < 400
            else:
                # Mock successful test if aiohttp not available
                return True

        except Exception as e:
            logger.error(f"❌ Endpoint connectivity test failed: {e}")
            return False
    
    def _generate_test_signature(self, payload: Dict[str, Any], secret_key: str) -> str:
        """إنشاء توقيع اختبار"""
        payload_str = json.dumps(payload, sort_keys=True, separators=(',', ':'))
        signature = hmac.new(
            secret_key.encode('utf-8'),
            payload_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return f"sha256={signature}"
    
    async def _get_partner_endpoints(
        self, 
        partner_id: str, 
        event: WebhookEvent
    ) -> List[WebhookEndpoint]:
        """الحصول على نقاط نهاية الشريك"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM webhook_endpoints 
                    WHERE partner_id = $1 AND is_active = true AND $2 = ANY(events)
                """
                
                rows = await conn.fetch(query, partner_id, event.value)
                
                endpoints = []
                for row in rows:
                    endpoint = WebhookEndpoint(
                        endpoint_id=row['endpoint_id'],
                        partner_id=row['partner_id'],
                        url=row['url'],
                        events=[WebhookEvent(e) for e in row['events']],
                        secret_key=row['secret_key'],
                        signature_method=SignatureMethod(row['signature_method']),
                        is_active=row['is_active'],
                        max_retries=row['max_retries'],
                        retry_delay=row['retry_delay'],
                        timeout=row['timeout'],
                        metadata=json.loads(row['metadata'] or '{}')
                    )
                    endpoints.append(endpoint)
                
                return endpoints
                
        except Exception as e:
            logger.error(f"❌ Failed to get partner endpoints: {e}")
            return []
    
    async def _generate_signature(
        self, 
        payload: WebhookPayload, 
        endpoint: WebhookEndpoint
    ) -> str:
        """إنشاء التوقيع"""
        payload_dict = asdict(payload)
        payload_dict.pop('signature', None)  # Remove signature field
        
        payload_str = json.dumps(payload_dict, sort_keys=True, separators=(',', ':'))
        
        if endpoint.signature_method == SignatureMethod.HMAC_SHA256:
            signature = hmac.new(
                endpoint.secret_key.encode('utf-8'),
                payload_str.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            return f"sha256={signature}"
        
        elif endpoint.signature_method == SignatureMethod.HMAC_SHA512:
            signature = hmac.new(
                endpoint.secret_key.encode('utf-8'),
                payload_str.encode('utf-8'),
                hashlib.sha512
            ).hexdigest()
            return f"sha512={signature}"
        
        else:
            # Default to HMAC-SHA256
            signature = hmac.new(
                endpoint.secret_key.encode('utf-8'),
                payload_str.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            return f"sha256={signature}"
    
    async def _store_delivery_record(self, delivery: WebhookDelivery):
        """حفظ سجل التسليم"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO webhook_deliveries (
                        delivery_id, webhook_id, endpoint_id, partner_id, event,
                        payload, status, attempts, max_retries, next_retry_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                """
                
                await conn.execute(
                    query,
                    delivery.delivery_id,
                    delivery.webhook_id,
                    delivery.endpoint_id,
                    delivery.partner_id,
                    delivery.event.value,
                    json.dumps(delivery.payload),
                    delivery.status.value,
                    delivery.attempts,
                    delivery.max_retries,
                    delivery.next_retry_at
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store delivery record: {e}")
            raise
    
    async def _send_webhook_delivery(self, delivery: WebhookDelivery, endpoint: WebhookEndpoint):
        """إرسال تسليم webhook"""
        try:
            # Update status to sending
            await self._update_delivery_status(delivery.delivery_id, WebhookStatus.SENDING)
            
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "WalletSystem-Webhook/1.0",
                self.webhook_config["signature_header"]: delivery.payload.get('signature', ''),
                self.webhook_config["timestamp_header"]: delivery.payload.get('timestamp', ''),
                self.webhook_config["event_header"]: delivery.event.value,
                self.webhook_config["id_header"]: delivery.webhook_id
            }
            
            # Send webhook
            async with self.http_session.post(
                endpoint.url,
                json=delivery.payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=endpoint.timeout)
            ) as response:
                response_body = await response.text()
                
                if response.status < 400:
                    # Success
                    await self._update_delivery_success(
                        delivery.delivery_id,
                        response.status,
                        response_body
                    )
                    self.webhooks_delivered += 1
                    logger.info(f"✅ Webhook delivered: {delivery.delivery_id}")
                else:
                    # HTTP error
                    await self._update_delivery_failure(
                        delivery.delivery_id,
                        delivery.attempts + 1,
                        response.status,
                        response_body,
                        f"HTTP {response.status}"
                    )
                    logger.warning(f"⚠️ Webhook delivery failed: {delivery.delivery_id} - HTTP {response.status}")
                
        except asyncio.TimeoutError:
            # Timeout
            await self._update_delivery_failure(
                delivery.delivery_id,
                delivery.attempts + 1,
                None,
                None,
                "Request timeout"
            )
            logger.warning(f"⚠️ Webhook delivery timeout: {delivery.delivery_id}")
            
        except Exception as e:
            # Other errors
            await self._update_delivery_failure(
                delivery.delivery_id,
                delivery.attempts + 1,
                None,
                None,
                str(e)
            )
            logger.error(f"❌ Webhook delivery error: {delivery.delivery_id} - {e}")
    
    async def _update_delivery_status(self, delivery_id: str, status: WebhookStatus):
        """تحديث حالة التسليم"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE webhook_deliveries 
                    SET status = $1, updated_at = CURRENT_TIMESTAMP
                    WHERE delivery_id = $2
                """
                
                await conn.execute(query, status.value, delivery_id)
                
        except Exception as e:
            logger.error(f"❌ Failed to update delivery status: {e}")
    
    async def _update_delivery_success(
        self, 
        delivery_id: str, 
        response_status: int, 
        response_body: str
    ):
        """تحديث نجاح التسليم"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE webhook_deliveries 
                    SET status = $1, 
                        response_status = $2,
                        response_body = $3,
                        delivered_at = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE delivery_id = $4
                """
                
                await conn.execute(
                    query,
                    WebhookStatus.DELIVERED.value,
                    response_status,
                    response_body[:1000],  # Limit response body size
                    delivery_id
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to update delivery success: {e}")
    
    async def _update_delivery_failure(
        self, 
        delivery_id: str, 
        attempts: int,
        response_status: int = None,
        response_body: str = None,
        error_message: str = None
    ):
        """تحديث فشل التسليم"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Get current delivery info
                delivery = await self.get_delivery_status(delivery_id)
                
                if not delivery:
                    return
                
                # Determine next status and retry time
                if attempts >= delivery.max_retries:
                    status = WebhookStatus.FAILED
                    next_retry_at = None
                    self.webhooks_failed += 1
                else:
                    status = WebhookStatus.RETRYING
                    # Calculate next retry time with exponential backoff
                    delay_index = min(attempts - 1, len(self.webhook_config["retry_delays"]) - 1)
                    delay = self.webhook_config["retry_delays"][delay_index]
                    next_retry_at = datetime.now() + timedelta(seconds=delay)
                
                query = """
                    UPDATE webhook_deliveries 
                    SET status = $1,
                        attempts = $2,
                        next_retry_at = $3,
                        response_status = $4,
                        response_body = $5,
                        error_message = $6,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE delivery_id = $7
                """
                
                await conn.execute(
                    query,
                    status.value,
                    attempts,
                    next_retry_at,
                    response_status,
                    response_body[:1000] if response_body else None,
                    error_message,
                    delivery_id
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to update delivery failure: {e}")
    
    async def _get_endpoint_config(self, endpoint_id: str) -> Optional[WebhookEndpoint]:
        """الحصول على إعدادات نقطة النهاية"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM webhook_endpoints WHERE endpoint_id = $1
                """
                
                row = await conn.fetchrow(query, endpoint_id)
                
                if row:
                    return WebhookEndpoint(
                        endpoint_id=row['endpoint_id'],
                        partner_id=row['partner_id'],
                        url=row['url'],
                        events=[WebhookEvent(e) for e in row['events']],
                        secret_key=row['secret_key'],
                        signature_method=SignatureMethod(row['signature_method']),
                        is_active=row['is_active'],
                        max_retries=row['max_retries'],
                        retry_delay=row['retry_delay'],
                        timeout=row['timeout'],
                        metadata=json.loads(row['metadata'] or '{}')
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get endpoint config: {e}")
            return None
    
    async def _retry_failed_webhooks(self):
        """إعادة محاولة webhook الفاشلة"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                # Get deliveries ready for retry
                async with self.db_connection.get_connection() as conn:
                    query = """
                        SELECT delivery_id FROM webhook_deliveries 
                        WHERE status = 'retrying' 
                        AND next_retry_at <= CURRENT_TIMESTAMP
                        LIMIT 100
                    """
                    
                    rows = await conn.fetch(query)
                    
                    for row in rows:
                        try:
                            await self.retry_delivery(row['delivery_id'])
                        except Exception as e:
                            logger.error(f"❌ Failed to retry delivery {row['delivery_id']}: {e}")
                            continue
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in retry task: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_old_deliveries(self):
        """تنظيف التسليمات القديمة"""
        while True:
            try:
                await asyncio.sleep(3600)  # Check every hour
                
                # Delete deliveries older than 30 days
                cutoff_date = datetime.now() - timedelta(days=30)
                
                async with self.db_connection.get_connection() as conn:
                    query = """
                        DELETE FROM webhook_deliveries 
                        WHERE created_at < $1 
                        AND status IN ('delivered', 'failed', 'expired')
                    """
                    
                    result = await conn.execute(query, cutoff_date)
                    
                    if result != "DELETE 0":
                        logger.info(f"🧹 Cleaned up old webhook deliveries: {result}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in cleanup task: {e}")
                await asyncio.sleep(3600)
    
    async def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمة"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Count active endpoints
                endpoints_query = "SELECT COUNT(*) FROM webhook_endpoints WHERE is_active = true"
                endpoints_count = await conn.fetchval(endpoints_query)
                
                return {
                    "webhooks_sent": self.webhooks_sent,
                    "webhooks_delivered": self.webhooks_delivered,
                    "webhooks_failed": self.webhooks_failed,
                    "delivery_rate": (self.webhooks_delivered / max(self.webhooks_sent, 1)) * 100,
                    "active_endpoints": endpoints_count,
                    "supported_events": len(WebhookEvent),
                    "http_session_active": self.http_session is not None
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get service statistics: {e}")
            return {
                "webhooks_sent": self.webhooks_sent,
                "webhooks_delivered": self.webhooks_delivered,
                "webhooks_failed": self.webhooks_failed,
                "delivery_rate": 0,
                "active_endpoints": 0,
                "supported_events": len(WebhookEvent),
                "http_session_active": self.http_session is not None
            }
