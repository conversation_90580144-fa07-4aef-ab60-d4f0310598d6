{"version": 3, "file": "ClassTransformer.js", "sourceRoot": "", "sources": ["../../src/ClassTransformer.ts"], "names": [], "mappings": ";;;;;;;;;;;AACA,OAAO,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAC1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAE7C,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AAEtE;IAAA;IAoJA,CAAC;IA1IC,0CAAe,GAAf,UACE,MAAe,EACf,OAA+B;QAE/B,IAAM,QAAQ,GAAG,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,cAAc,wBAC5E,cAAc,GACd,OAAO,EACV,CAAC;QACH,OAAO,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAC3F,CAAC;IAiBD,gDAAqB,GAArB,UACE,MAAS,EACT,WAAoB,EACpB,OAA+B;QAE/B,IAAM,QAAQ,GAAG,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,cAAc,wBAC5E,cAAc,GACd,OAAO,EACV,CAAC;QACH,OAAO,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAC7F,CAAC;IAeD,0CAAe,GAAf,UACE,GAAwB,EACxB,KAAc,EACd,OAA+B;QAE/B,IAAM,QAAQ,GAAG,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,cAAc,wBAC5E,cAAc,GACd,OAAO,EACV,CAAC;QACH,OAAO,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IACpF,CAAC;IAaD,gDAAqB,GAArB,UACE,SAAY,EACZ,KAAc,EACd,OAA+B;QAE/B,IAAM,QAAQ,GAAG,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,cAAc,wBAC5E,cAAc,GACd,OAAO,EACV,CAAC;QACH,OAAO,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAC1F,CAAC;IAOD,6CAAkB,GAAlB,UAAsB,MAAe,EAAE,OAA+B;QACpE,IAAM,QAAQ,GAAG,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,cAAc,wBAC5E,cAAc,GACd,OAAO,EACV,CAAC;QACH,OAAO,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAC3F,CAAC;IASD,gDAAqB,GAArB,UAAyB,MAAS,EAAE,UAAmB,EAAE,OAA+B;QACtF,IAAM,QAAQ,GAAG,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,cAAc,wBAC5E,cAAc,GACd,OAAO,EACV,CAAC;QACH,OAAO,QAAQ,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAC5F,CAAC;IAOD,oCAAS,GAAT,UAAa,MAAe,EAAE,OAA+B;QAC3D,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,sCAAW,GAAX,UAAe,GAAwB,EAAE,IAAY,EAAE,OAA+B;QACpF,IAAM,UAAU,GAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,2CAAgB,GAAhB,UAAoB,GAAwB,EAAE,IAAY,EAAE,OAA+B;QACzF,IAAM,UAAU,GAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IACH,uBAAC;AAAD,CAAC,AApJD,IAoJC", "sourcesContent": ["import { ClassTransformOptions } from './interfaces';\nimport { TransformOperationExecutor } from './TransformOperationExecutor';\nimport { TransformationType } from './enums';\nimport { ClassConstructor } from './interfaces';\nimport { defaultOptions } from './constants/default-options.constant';\n\nexport class ClassTransformer {\n  // -------------------------------------------------------------------------\n  // Public Methods\n  // -------------------------------------------------------------------------\n\n  /**\n   * Converts class (constructor) object to plain (literal) object. Also works with arrays.\n   */\n  instanceTo<PERSON>lain<T extends Record<string, any>>(object: T, options?: ClassTransformOptions): Record<string, any>;\n  instanceTo<PERSON>lain<T extends Record<string, any>>(object: T[], options?: ClassTransformOptions): Record<string, any>[];\n  instanceTo<PERSON>lain<T extends Record<string, any>>(\n    object: T | T[],\n    options?: ClassTransformOptions\n  ): Record<string, any> | Record<string, any>[] {\n    const executor = new TransformOperationExecutor(TransformationType.CLASS_TO_PLAIN, {\n      ...defaultOptions,\n      ...options,\n    });\n    return executor.transform(undefined, object, undefined, undefined, undefined, undefined);\n  }\n\n  /**\n   * Converts class (constructor) object to plain (literal) object.\n   * Uses given plain object as source object (it means fills given plain object with data from class object).\n   * Also works with arrays.\n   */\n  classToPlainFromExist<T extends Record<string, any>, P>(\n    object: T,\n    plainObject: P,\n    options?: ClassTransformOptions\n  ): T;\n  classToPlainFromExist<T extends Record<string, any>, P>(\n    object: T,\n    plainObjects: P[],\n    options?: ClassTransformOptions\n  ): T[];\n  classToPlainFromExist<T extends Record<string, any>, P>(\n    object: T,\n    plainObject: P | P[],\n    options?: ClassTransformOptions\n  ): T | T[] {\n    const executor = new TransformOperationExecutor(TransformationType.CLASS_TO_PLAIN, {\n      ...defaultOptions,\n      ...options,\n    });\n    return executor.transform(plainObject, object, undefined, undefined, undefined, undefined);\n  }\n\n  /**\n   * Converts plain (literal) object to class (constructor) object. Also works with arrays.\n   */\n  plainToInstance<T extends Record<string, any>, V extends Array<any>>(\n    cls: ClassConstructor<T>,\n    plain: V,\n    options?: ClassTransformOptions\n  ): T[];\n  plainToInstance<T extends Record<string, any>, V>(\n    cls: ClassConstructor<T>,\n    plain: V,\n    options?: ClassTransformOptions\n  ): T;\n  plainToInstance<T extends Record<string, any>, V>(\n    cls: ClassConstructor<T>,\n    plain: V | V[],\n    options?: ClassTransformOptions\n  ): T | T[] {\n    const executor = new TransformOperationExecutor(TransformationType.PLAIN_TO_CLASS, {\n      ...defaultOptions,\n      ...options,\n    });\n    return executor.transform(undefined, plain, cls, undefined, undefined, undefined);\n  }\n\n  /**\n   * Converts plain (literal) object to class (constructor) object.\n   * Uses given object as source object (it means fills given object with data from plain object).\n   * Also works with arrays.\n   */\n  plainToClassFromExist<T extends Record<string, any>, V extends Array<any>>(\n    clsObject: T,\n    plain: V,\n    options?: ClassTransformOptions\n  ): T;\n  plainToClassFromExist<T extends Record<string, any>, V>(clsObject: T, plain: V, options?: ClassTransformOptions): T[];\n  plainToClassFromExist<T extends Record<string, any>, V>(\n    clsObject: T,\n    plain: V | V[],\n    options?: ClassTransformOptions\n  ): T | T[] {\n    const executor = new TransformOperationExecutor(TransformationType.PLAIN_TO_CLASS, {\n      ...defaultOptions,\n      ...options,\n    });\n    return executor.transform(clsObject, plain, undefined, undefined, undefined, undefined);\n  }\n\n  /**\n   * Converts class (constructor) object to new class (constructor) object. Also works with arrays.\n   */\n  instanceToInstance<T>(object: T, options?: ClassTransformOptions): T;\n  instanceToInstance<T>(object: T[], options?: ClassTransformOptions): T[];\n  instanceToInstance<T>(object: T | T[], options?: ClassTransformOptions): T | T[] {\n    const executor = new TransformOperationExecutor(TransformationType.CLASS_TO_CLASS, {\n      ...defaultOptions,\n      ...options,\n    });\n    return executor.transform(undefined, object, undefined, undefined, undefined, undefined);\n  }\n\n  /**\n   * Converts class (constructor) object to plain (literal) object.\n   * Uses given plain object as source object (it means fills given plain object with data from class object).\n   * Also works with arrays.\n   */\n  classToClassFromExist<T>(object: T, fromObject: T, options?: ClassTransformOptions): T;\n  classToClassFromExist<T>(object: T, fromObjects: T[], options?: ClassTransformOptions): T[];\n  classToClassFromExist<T>(object: T, fromObject: T | T[], options?: ClassTransformOptions): T | T[] {\n    const executor = new TransformOperationExecutor(TransformationType.CLASS_TO_CLASS, {\n      ...defaultOptions,\n      ...options,\n    });\n    return executor.transform(fromObject, object, undefined, undefined, undefined, undefined);\n  }\n\n  /**\n   * Serializes given object to a JSON string.\n   */\n  serialize<T>(object: T, options?: ClassTransformOptions): string;\n  serialize<T>(object: T[], options?: ClassTransformOptions): string;\n  serialize<T>(object: T | T[], options?: ClassTransformOptions): string {\n    return JSON.stringify(this.instanceToPlain(object, options));\n  }\n\n  /**\n   * Deserializes given JSON string to a object of the given class.\n   */\n  deserialize<T>(cls: ClassConstructor<T>, json: string, options?: ClassTransformOptions): T {\n    const jsonObject: T = JSON.parse(json);\n    return this.plainToInstance(cls, jsonObject, options);\n  }\n\n  /**\n   * Deserializes given JSON string to an array of objects of the given class.\n   */\n  deserializeArray<T>(cls: ClassConstructor<T>, json: string, options?: ClassTransformOptions): T[] {\n    const jsonObject: any[] = JSON.parse(json);\n    return this.plainToInstance(cls, jsonObject, options);\n  }\n}\n"]}