import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Decimal } from 'decimal.js';

export enum TransferStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  ON_HOLD = 'on_hold',
}

export enum TransferType {
  DOMESTIC = 'domestic',
  INTERNATIONAL = 'international',
  WALLET_TO_WALLET = 'wallet_to_wallet',
  BANK_TRANSFER = 'bank_transfer',
  CASH_PICKUP = 'cash_pickup',
  MOBILE_MONEY = 'mobile_money',
}

export enum TransferPurpose {
  FAMILY_SUPPORT = 'family_support',
  EDUCATION = 'education',
  MEDICAL = 'medical',
  BUSINESS = 'business',
  INVESTMENT = 'investment',
  GIFT = 'gift',
  OTHER = 'other',
}

@Entity('transfers')
@Index(['senderId'])
@Index(['receiverId'])
@Index(['status'])
@Index(['transferType'])
@Index(['createdAt'])
export class Transfer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Transfer Reference
  @Column({ unique: true, length: 50 })
  referenceNumber: string;

  // Parties
  @Column({ type: 'uuid' })
  senderId: string;

  @Column({ type: 'uuid', nullable: true })
  receiverId?: string;

  // Receiver Information (for non-registered recipients)
  @Column({ nullable: true, length: 200 })
  receiverName?: string;

  @Column({ nullable: true, length: 20 })
  receiverPhone?: string;

  @Column({ nullable: true, length: 255 })
  receiverEmail?: string;

  @Column({ type: 'json', nullable: true })
  receiverAddress?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };

  // Transfer Details
  @Column({
    type: 'enum',
    enum: TransferType,
  })
  transferType: TransferType;

  @Column({
    type: 'enum',
    enum: TransferPurpose,
  })
  purpose: TransferPurpose;

  @Column({ nullable: true, length: 500 })
  description?: string;

  // Amount Information
  @Column({ type: 'decimal', precision: 15, scale: 2 })
  sendAmount: string;

  @Column({ length: 3 })
  sendCurrency: string;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  receiveAmount: string;

  @Column({ length: 3 })
  receiveCurrency: string;

  @Column({ type: 'decimal', precision: 10, scale: 6 })
  exchangeRate: string;

  // Fees
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  transferFee: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  exchangeFee: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalFees: string;

  // Status and Processing
  @Column({
    type: 'enum',
    enum: TransferStatus,
    default: TransferStatus.PENDING,
  })
  status: TransferStatus;

  @Column({ nullable: true, length: 500 })
  statusReason?: string;

  @Column({ type: 'timestamp', nullable: true })
  processedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  completedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  expectedDelivery?: Date;

  // Payment Information
  @Column({ nullable: true, length: 100 })
  paymentMethod?: string;

  @Column({ type: 'json', nullable: true })
  paymentDetails?: {
    cardLast4?: string;
    bankName?: string;
    accountNumber?: string;
    walletId?: string;
  };

  // Delivery Information
  @Column({ nullable: true, length: 100 })
  deliveryMethod?: string;

  @Column({ type: 'json', nullable: true })
  deliveryDetails?: {
    bankName?: string;
    accountNumber?: string;
    branchCode?: string;
    pickupLocation?: string;
    agentCode?: string;
  };

  // Security and Compliance
  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  riskScore: string;

  @Column({ default: false })
  requiresManualReview: boolean;

  @Column({ nullable: true, type: 'uuid' })
  reviewedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  reviewedAt?: Date;

  // Tracking
  @Column({ nullable: true, length: 45 })
  senderIp?: string;

  @Column({ nullable: true, length: 500 })
  userAgent?: string;

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;

  // External References
  @Column({ nullable: true, length: 100 })
  externalTransactionId?: string;

  @Column({ nullable: true, length: 100 })
  partnerTransactionId?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Computed properties
  get totalAmount(): Decimal {
    return new Decimal(this.sendAmount).plus(this.totalFees);
  }

  get isCompleted(): boolean {
    return this.status === TransferStatus.COMPLETED;
  }

  get isPending(): boolean {
    return this.status === TransferStatus.PENDING;
  }

  get isProcessing(): boolean {
    return this.status === TransferStatus.PROCESSING;
  }

  get isFailed(): boolean {
    return this.status === TransferStatus.FAILED;
  }

  get canCancel(): boolean {
    return [TransferStatus.PENDING, TransferStatus.ON_HOLD].includes(this.status);
  }

  get canRefund(): boolean {
    return this.status === TransferStatus.COMPLETED;
  }

  get processingTime(): number | null {
    if (!this.processedAt) return null;
    return this.processedAt.getTime() - this.createdAt.getTime();
  }

  get completionTime(): number | null {
    if (!this.completedAt) return null;
    return this.completedAt.getTime() - this.createdAt.getTime();
  }

  // Methods
  updateStatus(status: TransferStatus, reason?: string): void {
    this.status = status;
    this.statusReason = reason;
    
    if (status === TransferStatus.PROCESSING && !this.processedAt) {
      this.processedAt = new Date();
    }
    
    if (status === TransferStatus.COMPLETED && !this.completedAt) {
      this.completedAt = new Date();
    }
  }

  calculateTotalFees(): void {
    const transferFee = new Decimal(this.transferFee || 0);
    const exchangeFee = new Decimal(this.exchangeFee || 0);
    this.totalFees = transferFee.plus(exchangeFee).toString();
  }

  generateReference(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `WS${timestamp}${random}`.toUpperCase();
  }
}
