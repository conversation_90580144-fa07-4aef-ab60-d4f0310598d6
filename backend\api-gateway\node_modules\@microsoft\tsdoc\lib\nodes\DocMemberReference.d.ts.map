{"version": 3, "file": "DocMemberReference.d.ts", "sourceRoot": "", "sources": ["../../src/nodes/DocMemberReference.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,kBAAkB,EAAE,KAAK,wBAAwB,EAAE,MAAM,WAAW,CAAC;AACzG,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AACjE,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAC7D,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAG7D;;GAEG;AACH,MAAM,WAAW,6BAA8B,SAAQ,kBAAkB;IACvE,MAAM,EAAE,OAAO,CAAC;IAEhB,gBAAgB,CAAC,EAAE,mBAAmB,CAAC;IACvC,YAAY,CAAC,EAAE,eAAe,CAAC;IAE/B,QAAQ,CAAC,EAAE,iBAAiB,CAAC;CAC9B;AAED;;GAEG;AACH,MAAM,WAAW,mCAAoC,SAAQ,wBAAwB;IACnF,UAAU,CAAC,EAAE,aAAa,CAAC;IAC3B,sBAAsB,CAAC,EAAE,aAAa,CAAC;IAEvC,sBAAsB,CAAC,EAAE,aAAa,CAAC;IACvC,kCAAkC,CAAC,EAAE,aAAa,CAAC;IAEnD,gBAAgB,CAAC,EAAE,mBAAmB,CAAC;IACvC,YAAY,CAAC,EAAE,eAAe,CAAC;IAE/B,yBAAyB,CAAC,EAAE,aAAa,CAAC;IAE1C,YAAY,CAAC,EAAE,aAAa,CAAC;IAC7B,wBAAwB,CAAC,EAAE,aAAa,CAAC;IAEzC,QAAQ,CAAC,EAAE,iBAAiB,CAAC;IAC7B,2BAA2B,CAAC,EAAE,aAAa,CAAC;IAE5C,uBAAuB,CAAC,EAAE,aAAa,CAAC;IACxC,mCAAmC,CAAC,EAAE,aAAa,CAAC;CACrD;AAED;;;;;;;;GAQG;AACH,qBAAa,kBAAmB,SAAQ,OAAO;IAE7C,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAU;IAClC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAyB;IACrD,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAyB;IAEjE,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAyB;IACjE,OAAO,CAAC,QAAQ,CAAC,mCAAmC,CAAyB;IAE7E,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAkC;IAEpE,OAAO,CAAC,QAAQ,CAAC,aAAa,CAA8B;IAE5D,OAAO,CAAC,QAAQ,CAAC,0BAA0B,CAAyB;IAGpE,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAyB;IACvD,OAAO,CAAC,QAAQ,CAAC,yBAAyB,CAAyB;IAEnE,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAgC;IAC1D,OAAO,CAAC,QAAQ,CAAC,4BAA4B,CAAyB;IAEtE,OAAO,CAAC,QAAQ,CAAC,wBAAwB,CAAyB;IAClE,OAAO,CAAC,QAAQ,CAAC,oCAAoC,CAAyB;IAE9E;;;OAGG;gBACgB,UAAU,EAAE,6BAA6B,GAAG,mCAAmC;IAyFlG,gBAAgB;IAChB,IAAW,IAAI,IAAI,WAAW,GAAG,MAAM,CAEtC;IAED;;;OAGG;IACH,IAAW,MAAM,IAAI,OAAO,CAE3B;IAED;;;;OAIG;IACH,IAAW,gBAAgB,IAAI,mBAAmB,GAAG,SAAS,CAE7D;IAED;;;;;OAKG;IACH,IAAW,YAAY,IAAI,eAAe,GAAG,SAAS,CAErD;IAED;;;OAGG;IACH,IAAW,QAAQ,IAAI,iBAAiB,GAAG,SAAS,CAEnD;IAED,gBAAgB;IAChB,SAAS,CAAC,eAAe,IAAI,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC;CAsBhE"}