{
  "compilerOptions": {
    // Target and Module
    "target": "ES2022",
    "module": "commonjs",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "moduleResolution": "node",
    
    // Output
    "outDir": "./dist",
    "rootDir": "./",
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    
    // Type Checking
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,
    
    // Module Resolution
    "baseUrl": "./",
    "paths": {
      "@/*": ["./src/*"],
      "@backend/*": ["./backend/*"],
      "@frontend/*": ["./frontend/*"],
      "@shared/*": ["./shared/*"],
      "@types/*": ["./types/*"],
      "@utils/*": ["./utils/*"],
      "@config/*": ["./config/*"],
      "@database/*": ["./database/*"],
      "@middleware/*": ["./middleware/*"],
      "@services/*": ["./services/*"],
      "@controllers/*": ["./controllers/*"],
      "@models/*": ["./models/*"],
      "@validators/*": ["./validators/*"],
      "@interfaces/*": ["./interfaces/*"],
      "@enums/*": ["./enums/*"],
      "@constants/*": ["./constants/*"]
    },
    "typeRoots": ["./node_modules/@types", "./types"],
    "types": ["node"],
    
    // Source Maps
    "sourceMap": true,
    "inlineSourceMap": false,
    "inlineSources": false,
    
    // Emit
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": false,
    "importHelpers": true,
    "importsNotUsedAsValues": "remove",
    "downlevelIteration": true,
    "preserveConstEnums": true,

    // Interop Constraints
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "preserveSymlinks": true,
    "forceConsistentCasingInFileNames": true,
    
    // Language and Environment
    "useDefineForClassFields": true,
    "allowJs": true,
    "checkJs": false,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "keyofStringsOnly": false,
    "noStrictGenericChecks": false,
    "suppressExcessPropertyErrors": false,
    "suppressImplicitAnyIndexErrors": false,
    
    // Completeness
    "skipDefaultLibCheck": false,
    "skipLibCheck": true,
    
    // Advanced
    "resolveJsonModule": true,
    "incremental": true,
    "tsBuildInfoFile": "./dist/.tsbuildinfo"
  },
  "include": [
    "src/**/*",
    "backend/**/*",
    "frontend/**/*",
    "shared/**/*",
    "types/**/*",
    "utils/**/*",
    "config/**/*",
    "database/**/*",
    "middleware/**/*",
    "services/**/*",
    "controllers/**/*",
    "models/**/*",
    "validators/**/*",
    "interfaces/**/*",
    "enums/**/*",
    "constants/**/*",
    "*.ts",
    "*.js"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "coverage",
    "**/*.test.ts",
    "**/*.spec.ts",
    "**/*.test.js",
    "**/*.spec.js",
    "temp",
    "tmp",
    "logs",
    "uploads",
    "public",
    ".next",
    "out",
    "docker",
    "kubernetes",
    "terraform",
    "scripts",
    "docs",
    "examples",
    "samples"
  ],
  "ts-node": {
    "esm": true,
    "experimentalSpecifierResolution": "node"
  },
  "compileOnSave": false
}
