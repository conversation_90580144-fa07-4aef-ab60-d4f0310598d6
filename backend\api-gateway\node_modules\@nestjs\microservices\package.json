{"name": "@nestjs/microservices", "version": "10.4.20", "description": "Nest - modern, fast, powerful node.js web framework (@microservices)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://nestjs.com", "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "repository": {"type": "git", "url": "https://github.com/nestjs/nest.git", "directory": "packages/microservices"}, "publishConfig": {"access": "public"}, "dependencies": {"iterare": "1.2.1", "tslib": "2.8.1"}, "devDependencies": {"@nestjs/common": "10.4.20", "@nestjs/core": "10.4.20"}, "peerDependencies": {"@grpc/grpc-js": "*", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/websockets": "^10.0.0", "amqp-connection-manager": "*", "amqplib": "*", "cache-manager": "*", "ioredis": "*", "kafkajs": "*", "mqtt": "*", "nats": "*", "reflect-metadata": "^0.1.12 || ^0.2.0", "rxjs": "^7.1.0"}, "peerDependenciesMeta": {"@grpc/grpc-js": {"optional": true}, "@nestjs/websockets": {"optional": true}, "cache-manager": {"optional": true}, "kafkajs": {"optional": true}, "mqtt": {"optional": true}, "nats": {"optional": true}, "ioredis": {"optional": true}, "amqplib": {"optional": true}, "amqp-connection-manager": {"optional": true}}}