{"version": 3, "file": "IsLatLong.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsLatLong.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AACxD,OAAO,kBAAkB,MAAM,yBAAyB,CAAC;AAEzD,MAAM,CAAC,IAAM,UAAU,GAAG,WAAW,CAAC;AAEtC;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,KAAa;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,iBAAqC;IAC7D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,SAAS,CAAC,KAAK,CAAC,EAAhB,CAAgB;YACpD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,+CAA+C,EAA5D,CAA4D,EAC1E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from './ValidateBy';\nimport isLatLongValidator from 'validator/lib/isLatLong';\n\nexport const IS_LATLONG = 'isLatLong';\n\n/**\n * Checks if a value is string in format a \"latitude,longitude\".\n */\nexport function isLatLong(value: string): boolean {\n  return typeof value === 'string' && isLatLongValidator(value);\n}\n\n/**\n * Checks if a value is string in format a \"latitude,longitude\".\n */\nexport function IsLatLong(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_LATLONG,\n      validator: {\n        validate: (value, args): boolean => isLatLong(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a latitude,longitude string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}