{"version": 3, "file": "plugin.js", "sourceRoot": "", "sources": ["../src/plugin.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,6BAA+B;AAC/B,8CAAgD;AAChD,2BAA6B;AAC7B,mCAAqC;AACrC,iCAAmC;AAoHnC,wCAAwC;AACxC,IAAM,eAAe,GAAoB,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAEzF;IAcE,6BAAY,UAAyC;QAAzC,2BAAA,EAAA,eAAyC;QAArD,iBA6CC;QAxDD,WAAM,GAAW,mBAAmB,CAAC;QACrC,WAAM,GAAW,SAAS,CAAC;QAWzB,IAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAE/C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAE5B,qEAAqE;QAErE,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,CAC1B,OAAO,EACP,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAC9D,CAAC;QAEF,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QACjD,IAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC;QAE/C,IAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,EAAE;YACvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC;YACrD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,OAAO;gBACpC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC/B,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC;YAC/B,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,oBAAoB,CACjD,IAAI,CAAC,eAAe,EACpB,UAAU,CAAC,KAAK,EAChB,OAAO,CAAC,UAAU,CACnB,CAAC;YAEF,IAAI,OAAO,CAAC,UAAU,EAAE;gBACtB,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,OAAO,EAAE,SAAS;oBAC3C,IAAI,SAAS,EAAE;wBACb,IAAM,eAAe,GAAG,UAAU,CAAC,SAAS,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC;wBACxD,IAAI,eAAe,CAAC,UAAU,KAAK,SAAS,EAAE;4BACpC,IAAA,KAAK,GAAsB,eAAe,MAArC,EAAE,eAAe,GAAK,eAAe,gBAApB,CAAqB;4BACnD,OAAO,CAAC,eAAe,CAAC,GAAG,aAAa,CAAC,oBAAoB,CAC3D,eAAe,EACf,KAAK,EACL,OAAO,CAAC,UAAU,CACnB,CAAC;yBACH;qBACF;oBACD,OAAO,OAAO,CAAC;gBACjB,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAC5B;SACF;IACH,CAAC;IAED,mCAAK,GAAL,UAAM,QAAkB;QACtB,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,GAAG,CAAC,UAAU,CACjB,8FAA8F,CAC/F,CAAC;YACF,OAAO;SACR;QAED,4IAA4I;QAC5I,iJAAiJ;QACjJ,+GAA+G;QAC/G,IAAI,CAAC,CAAC,YAAY,IAAI,QAAQ,CAAC,EAAE;YAC/B,IAAI,CAAC,GAAG,CAAC,UAAU,CACjB,kEAAkE;gBAChE,sFAAsF;gBACtF,0GAA0G,CAC7G,CAAC;YACF,OAAO;SACR;QAED,mGAAmG;QACnG,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,UAAU,EAAE;YACnE,QAAQ;iBACL,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;iBACpB,QAAQ,CACP,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAC/B,oBAAoB,CAClB,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,SAAS,EACd,QAAQ,EACR,IAAI,CAAC,eAAe,EACpB,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAC7B,IAAI,CAAC,UAAU,CAChB,CACF,CAAC;SACL;aAAM,IAAI,QAAQ,IAAI,QAAQ,EAAE;YAC/B,uEAAuE;YACvE,IAAM,cAAc,GAAG,QAAqC,CAAC;YAC7D,cAAc,CAAC,MAAM,CACnB,IAAI,CAAC,MAAM,EACX,kBAAkB,CAChB,IAAI,CAAC,SAAS,EACd,QAAqC,EACrC,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,UAAU,CAChB,CACF,CAAC;SACH;IACH,CAAC;IACH,0BAAC;AAAD,CAAC,AA/GD,IA+GC;AA/GY,kDAAmB;AAiHhC,SAAS,UAAU,CACjB,UAAkB,EAClB,MAAqB;IAErB,IAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IACxD,IAAI,UAAU,CAAC,UAAU,KAAK,QAAQ,EAAE;QACtC,MAAM,CAAC,QAAQ,CAAC,yBAAkB,UAAU,eAAK,UAAU,CAAC,OAAO,CAAE,CAAC,CAAC;KACxE;SAAM;QACL,MAAM,CAAC,OAAO,CACZ,8DAAuD,UAAU,CAAC,sBAAsB,CAAE,CAC3F,CAAC;KACH;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,oBAAoB,CAC3B,iBAA+D,EAC/D,aAA2C,EAC3C,QAAkB,EAClB,mBAA2B,EAC3B,IAGC,EACD,UAAiC;IAEjC,IAAM,cAAc,GAAG,oBAAoB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACjE,IAAM,aAAa,GAAG,mBAAmB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC/D,OAAO,UACL,OAAuB,EACvB,cAA8B,EAC9B,QAA+B;;QAE/B,IAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAExD,IACE,CAAC,YAAY;aACb,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,0CAAE,UAAU,CAAC,GAAG,CAAC,CAAA;aACjC,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,0CAAE,UAAU,CAAC,IAAI,CAAC,CAAA,EAClC;YACA,OAAO,QAAQ,EAAE,CAAC;SACnB;QAED,2CAA2C;QAC3C,uDAAuD;QACvD,2DAA2D;QAC3D,IAAI,eAAe,GAAG,mBAAmB,CAAC;QAC1C,IACE,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ;YAChC,OAAO,CAAC,IAAI,KAAK,mBAAmB,EACpC;YACA,IAAI,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnC,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC;aAChC;iBAAM;gBACL,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CACtD,UAAC,UAAU;oBACT,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;oBAC/D,OAAO,CACL,QAAQ;wBACR,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC;wBAC1B,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAC3B,CAAC;gBACJ,CAAC,CACF,CAAC;gBACF,IAAI,YAAY,EAAE;oBAChB,eAAe,GAAG,YAAY,CAAC;iBAChC;aACF;SACF;QAED,IAAM,SAAS,GAAG,iBAAiB,CAAC,eAAe,CAAC,IAAI,aAAa,CAAC;QAEtE,SAAS,CACP,YAAY,EACZ,aAAa,EACb,cAAc,EACd,UAAU,EACV,UAAC,GAAG,EAAE,UAAU;YACd,IAAI,GAAG,EAAE;gBACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;aACtB;YAED,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,QAAQ,EAAE,CAAC;aACnB;YAED,IAAM,UAAU,yBACX,OAAO,KACV,OAAO,EAAE,UAAU,EACnB,IAAI,EAAE,eAAe,GACtB,CAAC;YAEF,2FAA2F;YAC3F,8DAA8D;YAC9D,wCAAwC;YACxC,IAAM,kBAAkB,GAAuB,OAAO,CAAC,yCAAyC,CAAC,CAAC;YAElG,OAAO,QAAQ,CAAC,SAAS,CACvB,IAAI,EACJ,UAAmB,EACnB,4BAAqB,YAAY,mBAAS,UAAU,wCAAqC;YACzF,8DAA8D;YAC9D,kBAAkB,cAAO,cAAsB,EAAG,EAClD,UAAC,IAAW,EAAE,OAAuB;gBACnC,sBAAsB;gBACtB,mHAAmH;gBACnH,IAAI,IAAI,EAAE;oBACR,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;iBACvB;gBAED,4CAA4C;gBAC5C,IAAI,OAAO,KAAK,SAAS,EAAE;oBACzB,OAAO,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;iBACvC;gBAED,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC,CACF,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,SAAuC,EACvC,QAAwB,EACxB,eAAuB,EACvB,MAAc,EACd,UAAiC;IAEjC,IAAM,cAAc,GAAG,oBAAoB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACjE,IAAM,aAAa,GAAG,mBAAmB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC/D,OAAO,UAAC,OAAO,EAAE,QAAQ;QACvB,IAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAExD,IACE,CAAC,YAAY;YACb,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC;YAC5B,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAC7B;YACA,OAAO,QAAQ,EAAE,CAAC;SACnB;QAED,SAAS,CACP,YAAY,EACZ,aAAa,EACb,cAAc,EACd,UAAU,EACV,UAAC,GAAG,EAAE,UAAU;YACd,IAAI,GAAG,EAAE;gBACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;aACtB;YAED,IAAI,CAAC,UAAU,EAAE;gBACf,OAAO,QAAQ,EAAE,CAAC;aACnB;YAED,IAAM,UAAU,yBACX,OAAO,KACV,OAAO,EAAE,UAAU,EACnB,IAAI,EAAE,eAAe,GACtB,CAAC;YAEF,wFAAwF;YACxF,+DAA+D;YAC/D,gEAAgE;YAChE,IAAM,mBAAmB,GAAwB,OAAO,CAAC,0CAA0C,CAAC,CAAC;YAErG,OAAQ,QAAQ,CAAC,SAA6B,CAC5C,MAAM,EACN,UAAU,EACV,4BAAqB,YAAY,mBAAS,UAAU,wCAAqC,EACzF,mBAAmB,CAAC,UAAU,IAAW,EAAE,OAAe;gBACxD,QAAQ;gBACR,sFAAsF;gBACtF,uFAAuF;gBACvF,uBAAuB;gBACvB,kIAAkI;gBAClI,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;oBACxB,OAAO,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBAChC;gBAED,4CAA4C;gBAC5C,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACjC,CAAC,EAAE,QAAQ,CAAC,CACb,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,QAAQ,CACf,UAAiD,EACjD,KAAa,EACb,QAA0B;IAE1B,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE;QACnD,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;KAC7C;IAED,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,GAAG;QAClC,IAAI,GAAG,EAAE;YACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;SACtB;QAED,IAAI,IAAI,CAAC;QAET,IAAI;YACF,8EAA8E;YAC9E,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;SAC1C;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;SACpB;QAED,OAAO,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAC1B,UAAiD;IAEjD,8DAA8D;IAC9D,OAAO,UAAC,KAAa,EAAE,SAA+C;QACpE,QAAQ,CAAC,UAAU,EAAE,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;YACpC,sCAAsC;YACtC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;gBAChB,SAAS,EAAE,CAAC;gBACZ,OAAO;aACR;YACD,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAiD;IAEjD,OAAO,UACL,KAAa,EACb,SAAkD;QAElD,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAU,EAAE,KAAe;YACjD,sCAAsC;YACtC,IAAI,GAAG,EAAE;gBACP,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBAC5B,OAAO;aACR;YACD,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}