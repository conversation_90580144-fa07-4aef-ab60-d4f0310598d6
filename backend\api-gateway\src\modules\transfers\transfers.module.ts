import { Module } from '@nestjs/common';
import { TransfersController } from './transfers.controller';
import { TransfersService } from './transfers.service';
import { ExchangeRateService } from './services/exchange-rate.service';
import { FeeCalculationService } from './services/fee-calculation.service';
import { TransferValidationService } from './services/transfer-validation.service';

@Module({
  controllers: [TransfersController],
  providers: [
    TransfersService,
    ExchangeRateService,
    FeeCalculationService,
    TransferValidationService,
  ],
  exports: [TransfersService],
})
export class TransfersModule {}
