# Core ML/AI Libraries
tensorflow==2.15.0
scikit-learn==1.3.2
pandas==2.1.4
numpy==1.24.3
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Deep Learning
keras==2.15.0
torch==2.1.2
torchvision==0.16.2
transformers==4.36.2

# Data Processing
scipy==1.11.4
joblib==1.3.2
imbalanced-learn==0.11.0
feature-engine==1.6.2

# Time Series Analysis
statsmodels==0.14.1
prophet==1.1.5
pmdarima==2.0.4

# Natural Language Processing
nltk==3.8.1
spacy==3.7.2
textblob==0.17.1

# Computer Vision
opencv-python==********
Pillow==10.1.0

# Web Framework
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.2
python-multipart==0.0.6

# Database Connections
psycopg2-binary==2.9.9
pymongo==4.6.1
redis==5.0.1
elasticsearch==8.11.0

# Async Support
asyncio==3.4.3
aiofiles==23.2.1
httpx==0.25.2

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk==1.39.2

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1
click==8.1.7

# Security
cryptography==41.0.8
passlib==1.7.4
python-jose==3.3.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3
requests==2.31.0
tqdm==4.66.1

# Model Serving
mlflow==2.8.1
bentoml==1.1.11

# Explainability
shap==0.44.0
lime==*******

# Hyperparameter Tuning
optuna==3.5.0
hyperopt==0.2.7

# Model Validation
evidently==0.4.12

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Jupyter (for development)
jupyter==1.0.0
ipykernel==6.27.1
