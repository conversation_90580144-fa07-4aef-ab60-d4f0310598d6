{"version": 3, "file": "DocErrorText.js", "sourceRoot": "", "sources": ["../../src/nodes/DocErrorText.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAE,OAAO,EAAiC,MAAM,WAAW,CAAC;AAEhF,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAcvD;;;GAGG;AACH;IAAkC,gCAAO;IAQvC;;;OAGG;IACH,sBAAmB,UAAyC;QAC1D,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,KAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC;YACjC,aAAa,EAAE,KAAI,CAAC,aAAa;YACjC,WAAW,EAAE,WAAW,CAAC,SAAS;YAClC,OAAO,EAAE,UAAU,CAAC,WAAW;SAChC,CAAC,CAAC;QAEH,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC;QACvC,KAAI,CAAC,aAAa,GAAG,UAAU,CAAC,YAAY,CAAC;QAC7C,KAAI,CAAC,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC;;IACjD,CAAC;IAGD,sBAAW,8BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,SAAS,CAAC;QAC/B,CAAC;;;OAAA;IAMD,sBAAW,8BAAI;QAJf;;;WAGG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACpD,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAED,sBAAW,qCAAW;aAAtB;YACE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;;;OAAA;IAKD,sBAAW,mCAAS;QAHpB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;;;OAAA;IAKD,sBAAW,sCAAY;QAHvB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;;;OAAA;IAWD,sBAAW,uCAAa;QATxB;;;;;;;;WAQG;aACH;YACE,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;;;OAAA;IAED,gBAAgB;IACN,sCAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7B,CAAC;IACH,mBAAC;AAAD,CAAC,AAjFD,CAAkC,OAAO,GAiFxC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\nimport type { TSDocMessageId } from '../parser/TSDocMessageId';\r\n\r\n/**\r\n * Constructor parameters for {@link DocErrorText}.\r\n */\r\nexport interface IDocErrorTextParsedParameters extends IDocNodeParsedParameters {\r\n  textExcerpt: TokenSequence;\r\n\r\n  messageId: TSDocMessageId;\r\n  errorMessage: string;\r\n  errorLocation: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents a span of text that contained invalid markup.\r\n * The characters should be rendered as plain text.\r\n */\r\nexport class DocErrorText extends DocNode {\r\n  private _text: string | undefined;\r\n  private readonly _textExcerpt: DocExcerpt;\r\n\r\n  private readonly _messageId: TSDocMessageId;\r\n  private readonly _errorMessage: string;\r\n  private readonly _errorLocation: TokenSequence;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocErrorTextParsedParameters) {\r\n    super(parameters);\r\n\r\n    this._textExcerpt = new DocExcerpt({\r\n      configuration: this.configuration,\r\n      excerptKind: ExcerptKind.ErrorText,\r\n      content: parameters.textExcerpt\r\n    });\r\n\r\n    this._messageId = parameters.messageId;\r\n    this._errorMessage = parameters.errorMessage;\r\n    this._errorLocation = parameters.errorLocation;\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.ErrorText;\r\n  }\r\n\r\n  /**\r\n   * The characters that should be rendered as plain text because they\r\n   * could not be parsed successfully.\r\n   */\r\n  public get text(): string {\r\n    if (this._text === undefined) {\r\n      this._text = this._textExcerpt.content.toString();\r\n    }\r\n    return this._text;\r\n  }\r\n\r\n  public get textExcerpt(): TokenSequence | undefined {\r\n    if (this._textExcerpt) {\r\n      return this._textExcerpt.content;\r\n    } else {\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * The TSDoc error message identifier.\r\n   */\r\n  public get messageId(): TSDocMessageId {\r\n    return this._messageId;\r\n  }\r\n\r\n  /**\r\n   * A description of why the character could not be parsed.\r\n   */\r\n  public get errorMessage(): string {\r\n    return this._errorMessage;\r\n  }\r\n\r\n  /**\r\n   * The range of characters that caused the error.  In general these may be\r\n   * somewhat farther ahead in the input stream from the DocErrorText node itself.\r\n   *\r\n   * @remarks\r\n   * For example, for the malformed HTML tag `<a href=\"123\" @ /a>`, the DocErrorText node\r\n   * will correspond to the `<` character that looked like an HTML tag, whereas the\r\n   * error location might be the `@` character that caused the trouble.\r\n   */\r\n  public get errorLocation(): TokenSequence {\r\n    return this._errorLocation;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._textExcerpt];\r\n  }\r\n}\r\n"]}