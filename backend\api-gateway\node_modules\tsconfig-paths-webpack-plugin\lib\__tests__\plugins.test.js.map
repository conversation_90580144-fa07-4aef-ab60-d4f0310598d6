{"version": 3, "file": "plugins.test.js", "sourceRoot": "", "sources": ["../../src/__tests__/plugins.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,iCAAmC;AAEnC,IAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7B,oCAAgD;AAEhD,QAAQ,CAAC,qBAAqB,EAAE;IAC9B,IAAM,QAAQ,GAAkB;QAC9B,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC;QACvC,KAAK,EAAE,UAAG,SAAS,yCAAsC;QACzD,MAAM,EAAE;YACN,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;YACxC,QAAQ,EAAE,WAAW;SACtB;QACD,MAAM,EAAE;YACN,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,eAAe;oBACxB,MAAM,EAAE,WAAW;oBACnB,OAAO,EAAE;wBACP,UAAU,EAAE,yBAAyB;qBACtC;iBACF;aACF;SACF;QACD,OAAO,EAAE;YACP,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;SACnC;KACF,CAAC;IAEF,EAAE,CAAC,2BAA2B,EAAE,UAAC,IAAI;QACnC,IAAM,UAAU,GAAG,IAAI,4BAAmB,CAAC;YACzC,UAAU,EAAE,UAAG,SAAS,0CAAuC;YAC/D,QAAQ,EAAE,MAAM;YAChB,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;YAC3B,UAAU,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;SAChC,CAAC,CAAC;QACH,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,4BAAmB,CAAC,CAAC;QAEvD,IAAM,YAAY,yBACb,QAAQ,KACX,OAAO,EAAE;gBACP,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;gBAClC,OAAO,EAAE,CAAC,UAAU,CAAC;aACtB,GACF,CAAC;QAEF,IAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;QAEvC,QAAQ,CAAC,GAAG,CAAC,UAAC,GAAG,EAAE,KAAK;YACtB,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,OAAO;aACR;YACD,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAM,OAAO,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE,CAAC;YAChC,MAAM,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,sFAAsF;YACtF,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE,UAAC,IAAI;;QACpD,IAAM,eAAe,GAAkB;YACrC,KAAK,EAAE,UAAG,SAAS,yCAAsC;YACzD,MAAM,EAAE,KAAK;YACb,MAAM,EAAE;gBACN,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;gBACxC,QAAQ,EAAE,WAAW;aACtB;YACD,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE;gBACP,UAAU,EAAE;oBACV,KAAK;oBACL,MAAM;oBACN,KAAK;oBACL,MAAM;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,MAAM;oBACN,OAAO;iBACR;gBACD,OAAO,EAAE;oBACP,IAAI,4BAAmB,CAAC;wBACtB,UAAU,EAAE,UAAG,SAAS,0CAAuC;qBAChE,CAAC;iBACH;aACF;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,EAAE;aACV;SACF,CAAC;QACF,iBAAiB;QACjB,IAAM,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,IAAM,cAAc,GAAG,MAAA,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,0CAAE,OAAO,0CAAE,OAAO,0CAAE,IAAI,CAC9D,UAAC,MAAM,IAAK,OAAA,MAAM,YAAY,4BAAmB,EAArC,CAAqC,CAClD,CAAC;QACF,IAAI,CAAC,cAAc,EAAE;YACnB,OAAO,IAAI,CAAC,oDAAoD,CAAC,CAAC;SACnE;QACD,MAAM,CAAC,cAAc,YAAY,4BAAmB,CAAC,CAAC,UAAU,EAAE,CAAC;QACnE,MAAM,CAAE,cAAsC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAEpE,eAAe;QACf,QAAQ,CAAC,GAAG,CAAC,UAAC,GAAG,EAAE,KAAK;YACtB,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,OAAO;aACR;YACD,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAM,OAAO,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE,CAAC;YAChC,MAAM,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE,UAAC,IAAI;QACrC,IAAM,UAAU,GAAG,IAAI,4BAAmB,CAAC;YACzC,UAAU,EAAE,UAAG,SAAS,mDAAgD;YACxE,QAAQ,EAAE,MAAM;YAChB,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;YAC3B,UAAU,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;YAC/B,UAAU,EAAE,CAAC,UAAG,SAAS,0CAAuC,CAAC;SAClE,CAAC,CAAC;QACH,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,4BAAmB,CAAC,CAAC;QAEvD,IAAM,YAAY,yBACb,QAAQ,KACX,OAAO,EAAE;gBACP,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;gBAClC,OAAO,EAAE,CAAC,UAAU,CAAC;aACtB,GACF,CAAC;QAEF,IAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;QAEvC,QAAQ,CAAC,GAAG,CAAC,UAAC,GAAG,EAAE,KAAK;YACtB,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,OAAO;aACR;YACD,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAE5B,IAAM,OAAO,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE,CAAC;YAChC,MAAM,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,sFAAsF;YACtF,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}