{"version": 3, "file": "IsSemVer.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsSemVer.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAE1C,QAAA,UAAU,GAAG,UAAU,CAAC;AAErC;;;GAGG;AACH,SAAgB,QAAQ,CAAC,KAAc;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,kBAAiB,EAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAFD,4BAEC;AAED;;;GAGG;AACH,SAAgB,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,kBAAU;QAChB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YACnD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,uDAAuD,EAClF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,4BAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isSemVerValidator from 'validator/lib/isSemVer';\n\nexport const IS_SEM_VER = 'isSemVer';\n\n/**\n * Check if the string is a Semantic Versioning Specification (SemVer).\n * If given value is not a string, then it returns false.\n */\nexport function isSemVer(value: unknown): boolean {\n  return typeof value === 'string' && isSemVerValidator(value);\n}\n\n/**\n * Check if the string is a Semantic Versioning Specification (SemVer).\n * If given value is not a string, then it returns false.\n */\nexport function IsSemVer(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_SEM_VER,\n      validator: {\n        validate: (value, args): boolean => isSemVer(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a Semantic Versioning Specification',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}