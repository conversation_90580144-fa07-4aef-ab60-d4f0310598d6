{"version": 3, "file": "TSDocParser.js", "sourceRoot": "", "sources": ["../../src/parser/TSDocParser.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,yCAAwC;AACxC,iDAAgD;AAChD,iDAAgD;AAChD,yCAAwC;AACxC,2CAA0C;AAC1C,0EAAyE;AACzE,yDAAwD;AAExD;;GAEG;AACH;IAME,qBAAmB,aAAkC;QACnD,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAChD,CAAC;IACH,CAAC;IAEM,iCAAW,GAAlB,UAAmB,IAAY;QAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACrD,CAAC;IAEM,gCAAU,GAAjB,UAAkB,KAAgB;QAChC,IAAM,aAAa,GAAkB,IAAI,6BAAa,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAElF,IAAI,6BAAa,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YACzC,aAAa,CAAC,MAAM,GAAG,qBAAS,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAEjE,IAAM,UAAU,GAAe,IAAI,uBAAU,CAAC,aAAa,CAAC,CAAC;YAC7D,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,qCAAiB,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IACH,kBAAC;AAAD,CAAC,AAhCD,IAgCC;AAhCY,kCAAW", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { TextRange } from './TextRange';\r\nimport { ParserContext } from './ParserContext';\r\nimport { LineExtractor } from './LineExtractor';\r\nimport { Tokenizer } from './Tokenizer';\r\nimport { NodeParser } from './NodeParser';\r\nimport { TSDocConfiguration } from '../configuration/TSDocConfiguration';\r\nimport { ParagraphSplitter } from './ParagraphSplitter';\r\n\r\n/**\r\n * The main API for parsing TSDoc comments.\r\n */\r\nexport class TSDocParser {\r\n  /**\r\n   * The configuration that was provided for the TSDocParser.\r\n   */\r\n  public readonly configuration: TSDocConfiguration;\r\n\r\n  public constructor(configuration?: TSDocConfiguration) {\r\n    if (configuration) {\r\n      this.configuration = configuration;\r\n    } else {\r\n      this.configuration = new TSDocConfiguration();\r\n    }\r\n  }\r\n\r\n  public parseString(text: string): ParserContext {\r\n    return this.parseRange(TextRange.fromString(text));\r\n  }\r\n\r\n  public parseRange(range: TextRange): ParserContext {\r\n    const parserContext: ParserContext = new ParserContext(this.configuration, range);\r\n\r\n    if (LineExtractor.extract(parserContext)) {\r\n      parserContext.tokens = Tokenizer.readTokens(parserContext.lines);\r\n\r\n      const nodeParser: NodeParser = new NodeParser(parserContext);\r\n      nodeParser.parse();\r\n\r\n      ParagraphSplitter.splitParagraphs(parserContext.docComment);\r\n    }\r\n\r\n    return parserContext;\r\n  }\r\n}\r\n"]}