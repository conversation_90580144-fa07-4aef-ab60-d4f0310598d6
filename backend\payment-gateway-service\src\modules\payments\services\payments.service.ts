import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { 
  Payment, 
  PaymentStatus, 
  PaymentMethod,
  PaymentGatewayType 
} from '../entities/payment.entity';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { StripeService } from '../providers/stripe.service';
import { PaypalService } from '../providers/paypal.service';
import { SadadService } from '../providers/sadad.service';

export interface FindPaymentsOptions {
  page?: number;
  limit?: number;
  status?: PaymentStatus;
  method?: PaymentMethod;
  gatewayType?: PaymentGatewayType;
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface PaginatedPayments {
  data: Payment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaymentResult {
  success: boolean;
  payment: Payment;
  externalTransactionId?: string;
  redirectUrl?: string;
  errorMessage?: string;
}

@Injectable()
export class PaymentsService {
  private readonly logger = new Logger(PaymentsService.name);

  constructor(
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
    private readonly configService: ConfigService,
    private readonly stripeService: StripeService,
    private readonly paypalService: PaypalService,
    private readonly sadadService: SadadService,
  ) {}

  async create(createPaymentDto: CreatePaymentDto): Promise<Payment> {
    // Generate reference number
    const referenceNumber = this.generateReferenceNumber();

    // Calculate fees and net amount
    const fees = this.calculateFees(
      parseFloat(createPaymentDto.amount),
      createPaymentDto.method,
      createPaymentDto.gatewayType
    );

    const payment = this.paymentRepository.create({
      ...createPaymentDto,
      referenceNumber,
      fees: fees.toString(),
      netAmount: (parseFloat(createPaymentDto.amount) - fees).toFixed(2),
      expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
    });

    const savedPayment = await this.paymentRepository.save(payment);
    this.logger.log(`Payment created: ${savedPayment.referenceNumber}`);

    return savedPayment;
  }

  async processPayment(paymentId: string): Promise<PaymentResult> {
    const payment = await this.findById(paymentId);

    if (payment.status !== PaymentStatus.PENDING) {
      throw new BadRequestException('الدفعة تم معالجتها مسبقاً أو ملغاة');
    }

    if (payment.isExpired()) {
      payment.markAsCancelled();
      await this.paymentRepository.save(payment);
      throw new BadRequestException('انتهت صلاحية الدفعة');
    }

    payment.markAsProcessing();
    payment.incrementAttempts();
    await this.paymentRepository.save(payment);

    try {
      let result: PaymentResult;

      switch (payment.gatewayType) {
        case PaymentGatewayType.STRIPE:
          result = await this.processStripePayment(payment);
          break;
        case PaymentGatewayType.PAYPAL:
          result = await this.processPaypalPayment(payment);
          break;
        case PaymentGatewayType.SADAD:
          result = await this.processSadadPayment(payment);
          break;
        case PaymentGatewayType.MADA:
          result = await this.processMadaPayment(payment);
          break;
        default:
          throw new BadRequestException('نوع بوابة الدفع غير مدعوم');
      }

      if (result.success) {
        payment.markAsCompleted(result.externalTransactionId);
        if (result.externalTransactionId) {
          payment.externalTransactionId = result.externalTransactionId;
        }
      } else {
        payment.markAsFailed(result.errorMessage || 'فشل في معالجة الدفعة');
      }

      await this.paymentRepository.save(payment);
      this.logger.log(`Payment processed: ${payment.referenceNumber} - ${result.success ? 'Success' : 'Failed'}`);

      return { ...result, payment };
    } catch (error) {
      payment.markAsFailed(error.message);
      await this.paymentRepository.save(payment);
      
      this.logger.error(`Payment processing failed: ${payment.referenceNumber}`, error.stack);
      return {
        success: false,
        payment,
        errorMessage: error.message,
      };
    }
  }

  async findAll(options: FindPaymentsOptions = {}): Promise<PaginatedPayments> {
    const {
      page = 1,
      limit = 20,
      status,
      method,
      gatewayType,
      userId,
      dateFrom,
      dateTo,
    } = options;

    const skip = (page - 1) * limit;
    const queryBuilder = this.paymentRepository.createQueryBuilder('payment');

    if (userId) {
      queryBuilder.where('payment.userId = :userId', { userId });
    }

    if (status) {
      queryBuilder.andWhere('payment.status = :status', { status });
    }

    if (method) {
      queryBuilder.andWhere('payment.method = :method', { method });
    }

    if (gatewayType) {
      queryBuilder.andWhere('payment.gatewayType = :gatewayType', { gatewayType });
    }

    if (dateFrom && dateTo) {
      queryBuilder.andWhere('payment.createdAt BETWEEN :dateFrom AND :dateTo', {
        dateFrom,
        dateTo,
      });
    }

    queryBuilder
      .orderBy('payment.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findById(id: string): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({
      where: { id },
    });

    if (!payment) {
      throw new NotFoundException('الدفعة غير موجودة');
    }

    return payment;
  }

  async findByReferenceNumber(referenceNumber: string): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({
      where: { referenceNumber },
    });

    if (!payment) {
      throw new NotFoundException('الدفعة غير موجودة');
    }

    return payment;
  }

  async cancelPayment(paymentId: string, userId?: string): Promise<Payment> {
    const payment = await this.findById(paymentId);

    if (userId && payment.userId !== userId) {
      throw new BadRequestException('غير مصرح لك بإلغاء هذه الدفعة');
    }

    if (!payment.canCancel()) {
      throw new BadRequestException('لا يمكن إلغاء هذه الدفعة');
    }

    payment.markAsCancelled();
    const updatedPayment = await this.paymentRepository.save(payment);

    this.logger.log(`Payment cancelled: ${payment.referenceNumber}`);
    return updatedPayment;
  }

  async refundPayment(paymentId: string, amount?: string): Promise<Payment> {
    const payment = await this.findById(paymentId);

    if (!payment.canRefund()) {
      throw new BadRequestException('لا يمكن استرداد هذه الدفعة');
    }

    const refundAmount = amount ? parseFloat(amount) : parseFloat(payment.amount);
    
    if (refundAmount > parseFloat(payment.amount)) {
      throw new BadRequestException('مبلغ الاسترداد أكبر من مبلغ الدفعة');
    }

    try {
      let refundResult: boolean;

      switch (payment.gatewayType) {
        case PaymentGatewayType.STRIPE:
          refundResult = await this.stripeService.refund(payment.externalTransactionId, refundAmount);
          break;
        case PaymentGatewayType.PAYPAL:
          refundResult = await this.paypalService.refund(payment.externalTransactionId, refundAmount);
          break;
        default:
          throw new BadRequestException('الاسترداد غير مدعوم لهذه البوابة');
      }

      if (refundResult) {
        payment.markAsRefunded();
        const updatedPayment = await this.paymentRepository.save(payment);
        
        this.logger.log(`Payment refunded: ${payment.referenceNumber} - Amount: ${refundAmount}`);
        return updatedPayment;
      } else {
        throw new BadRequestException('فشل في عملية الاسترداد');
      }
    } catch (error) {
      this.logger.error(`Refund failed: ${payment.referenceNumber}`, error.stack);
      throw new BadRequestException(`فشل في الاسترداد: ${error.message}`);
    }
  }

  async getPaymentStats(userId?: string): Promise<any> {
    const queryBuilder = this.paymentRepository.createQueryBuilder('payment');

    if (userId) {
      queryBuilder.where('payment.userId = :userId', { userId });
    }

    const [
      totalPayments,
      completedPayments,
      failedPayments,
      totalVolume,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('payment.status = :status', { status: PaymentStatus.COMPLETED }).getCount(),
      queryBuilder.clone().andWhere('payment.status = :status', { status: PaymentStatus.FAILED }).getCount(),
      queryBuilder.clone()
        .andWhere('payment.status = :status', { status: PaymentStatus.COMPLETED })
        .select('SUM(CAST(payment.amount AS DECIMAL))', 'total')
        .getRawOne(),
    ]);

    const successRate = totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0;

    return {
      totalPayments,
      completedPayments,
      failedPayments,
      totalVolume: totalVolume?.total || '0',
      successRate: Math.round(successRate * 100) / 100,
    };
  }

  private async processStripePayment(payment: Payment): Promise<PaymentResult> {
    return this.stripeService.processPayment(payment);
  }

  private async processPaypalPayment(payment: Payment): Promise<PaymentResult> {
    return this.paypalService.processPayment(payment);
  }

  private async processSadadPayment(payment: Payment): Promise<PaymentResult> {
    return this.sadadService.processPayment(payment);
  }

  private async processMadaPayment(payment: Payment): Promise<PaymentResult> {
    // Mada processing logic
    return {
      success: true,
      payment,
      externalTransactionId: `mada_${Date.now()}`,
    };
  }

  private calculateFees(amount: number, method: PaymentMethod, gatewayType: PaymentGatewayType): number {
    // Fee calculation logic based on method and gateway
    const feeRates = {
      [PaymentGatewayType.STRIPE]: 0.029, // 2.9%
      [PaymentGatewayType.PAYPAL]: 0.034, // 3.4%
      [PaymentGatewayType.SADAD]: 0.015, // 1.5%
      [PaymentGatewayType.MADA]: 0.02, // 2%
    };

    const rate = feeRates[gatewayType] || 0.025;
    const fee = amount * rate;
    const fixedFee = 2; // Fixed fee

    return Math.round((fee + fixedFee) * 100) / 100;
  }

  private generateReferenceNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `PAY${timestamp.slice(-8)}${random}`;
  }
}
