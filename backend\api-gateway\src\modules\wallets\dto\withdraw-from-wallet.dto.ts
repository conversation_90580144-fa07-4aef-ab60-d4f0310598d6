import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  <PERSON>N<PERSON><PERSON>,
  IsEnum,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum WithdrawalMethod {
  BANK_TRANSFER = 'bank_transfer',
  CASH_PICKUP = 'cash_pickup',
  MOBILE_WALLET = 'mobile_wallet',
  PREPAID_CARD = 'prepaid_card',
  CHECK = 'check',
}

export class BankAccountDto {
  @ApiProperty({
    description: 'اسم البنك',
    example: 'البنك الأهلي السعودي',
  })
  @IsString({ message: 'اسم البنك يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم البنك مطلوب' })
  bankName: string;

  @ApiProperty({
    description: 'رقم الحساب البنكي',
    example: '**********123456',
  })
  @IsString({ message: 'رقم الحساب يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم الحساب مطلوب' })
  accountNumber: string;

  @ApiProperty({
    description: 'اسم صاحب الحساب',
    example: 'أحمد محمد',
  })
  @IsString({ message: 'اسم صاحب الحساب يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم صاحب الحساب مطلوب' })
  accountHolderName: string;

  @ApiProperty({
    description: 'رمز البنك (SWIFT/IBAN)',
    example: 'NCBKSARI',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رمز البنك يجب أن يكون نص' })
  bankCode?: string;

  @ApiProperty({
    description: 'فرع البنك',
    example: 'فرع الرياض الرئيسي',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'فرع البنك يجب أن يكون نص' })
  branchName?: string;
}

export class CashPickupDto {
  @ApiProperty({
    description: 'موقع الاستلام',
    example: 'فرع الرياض - شارع الملك فهد',
  })
  @IsString({ message: 'موقع الاستلام يجب أن يكون نص' })
  @IsNotEmpty({ message: 'موقع الاستلام مطلوب' })
  pickupLocation: string;

  @ApiProperty({
    description: 'اسم المستلم',
    example: 'أحمد محمد',
  })
  @IsString({ message: 'اسم المستلم يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم المستلم مطلوب' })
  recipientName: string;

  @ApiProperty({
    description: 'رقم هاتف المستلم',
    example: '+************',
  })
  @IsString({ message: 'رقم هاتف المستلم يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم هاتف المستلم مطلوب' })
  recipientPhone: string;

  @ApiProperty({
    description: 'رقم الهوية الوطنية للمستلم',
    example: '**********',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الهوية يجب أن يكون نص' })
  recipientNationalId?: string;
}

export class MobileWalletDto {
  @ApiProperty({
    description: 'مقدم خدمة المحفظة الإلكترونية',
    example: 'STC Pay',
  })
  @IsString({ message: 'مقدم الخدمة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'مقدم الخدمة مطلوب' })
  provider: string;

  @ApiProperty({
    description: 'رقم الهاتف المرتبط بالمحفظة',
    example: '+************',
  })
  @IsString({ message: 'رقم الهاتف يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم الهاتف مطلوب' })
  phoneNumber: string;

  @ApiProperty({
    description: 'اسم صاحب المحفظة',
    example: 'أحمد محمد',
  })
  @IsString({ message: 'اسم صاحب المحفظة يجب أن يكون نص' })
  @IsNotEmpty({ message: 'اسم صاحب المحفظة مطلوب' })
  walletHolderName: string;
}

export class WithdrawFromWalletDto {
  @ApiProperty({
    description: 'المبلغ المراد سحبه',
    example: 500,
    minimum: 10,
    maximum: 5000,
  })
  @IsNumber({}, { message: 'المبلغ يجب أن يكون رقم' })
  @IsNotEmpty({ message: 'المبلغ مطلوب' })
  @Min(10, { message: 'الحد الأدنى للسحب 10' })
  @Max(5000, { message: 'الحد الأقصى للسحب 5,000' })
  amount: number;

  @ApiProperty({
    description: 'العملة',
    example: 'SAR',
  })
  @IsString({ message: 'العملة يجب أن تكون نص' })
  @IsNotEmpty({ message: 'العملة مطلوبة' })
  currency: string;

  @ApiProperty({
    description: 'طريقة السحب',
    enum: WithdrawalMethod,
    example: WithdrawalMethod.BANK_TRANSFER,
  })
  @IsEnum(WithdrawalMethod, { message: 'طريقة السحب غير صحيحة' })
  @IsNotEmpty({ message: 'طريقة السحب مطلوبة' })
  method: WithdrawalMethod;

  @ApiProperty({
    description: 'بيانات الحساب البنكي (مطلوب للتحويل البنكي)',
    type: BankAccountDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BankAccountDto)
  bankAccountDetails?: BankAccountDto;

  @ApiProperty({
    description: 'بيانات الاستلام النقدي (مطلوب للاستلام النقدي)',
    type: CashPickupDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CashPickupDto)
  cashPickupDetails?: CashPickupDto;

  @ApiProperty({
    description: 'بيانات المحفظة الإلكترونية (مطلوب للمحفظة الإلكترونية)',
    type: MobileWalletDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MobileWalletDto)
  mobileWalletDetails?: MobileWalletDto;

  @ApiProperty({
    description: 'سبب السحب',
    example: 'سحب للاستخدام الشخصي',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'سبب السحب يجب أن يكون نص' })
  reason?: string;

  @ApiProperty({
    description: 'ملاحظات إضافية',
    example: 'يرجى المعالجة السريعة',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الملاحظات يجب أن تكون نص' })
  notes?: string;

  @ApiProperty({
    description: 'حفظ طريقة السحب للاستخدام المستقبلي',
    example: true,
    required: false,
  })
  @IsOptional()
  saveWithdrawalMethod?: boolean;
}
