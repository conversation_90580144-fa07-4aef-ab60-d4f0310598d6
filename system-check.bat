@echo off
chcp 65001 >nul

echo 🔍 فحص حالة نظام WS Transfir
echo =============================
echo.

:: Check if processes are running
echo 📊 فحص العمليات الجارية...
echo.

:: Check Node.js processes
tasklist /FI "IMAGENAME eq node.exe" /FO TABLE >nul 2>&1
if errorlevel 1 (
    echo ❌ لا توجد عمليات Node.js تعمل
) else (
    echo ✅ عمليات Node.js تعمل
    tasklist /FI "IMAGENAME eq node.exe" /FO TABLE | findstr node.exe
)

echo.

:: Check if ports are in use
echo 🌐 فحص المنافذ...
echo.

:: Check port 3000 (API)
netstat -an | findstr :3000 >nul 2>&1
if errorlevel 1 (
    echo ❌ المنفذ 3000 (API) غير مستخدم
) else (
    echo ✅ المنفذ 3000 (API) مستخدم
)

:: Check port 3100 (Frontend)
netstat -an | findstr :3100 >nul 2>&1
if errorlevel 1 (
    echo ❌ المنفذ 3100 (Frontend) غير مستخدم
) else (
    echo ✅ المنفذ 3100 (Frontend) مستخدم
)

echo.

:: Try to ping the services
echo 🏥 فحص صحة الخدمات...
echo.

:: Test API health (simple method)
curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo ❌ API Server غير متاح على http://localhost:3000
) else (
    echo ✅ API Server متاح على http://localhost:3000
)

:: Test Frontend (simple method)
curl -s http://localhost:3100 >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend غير متاح على http://localhost:3100
) else (
    echo ✅ Frontend متاح على http://localhost:3100
)

echo.

:: Check if files exist
echo 📁 فحص الملفات...
echo.

if exist "simple-api-server.js" (
    echo ✅ ملف API Server موجود
) else (
    echo ❌ ملف API Server غير موجود
)

if exist "frontend\web-app\package.json" (
    echo ✅ ملف Frontend package.json موجود
) else (
    echo ❌ ملف Frontend package.json غير موجود
)

if exist "frontend\web-app\node_modules" (
    echo ✅ مجلد node_modules موجود
) else (
    echo ❌ مجلد node_modules غير موجود
)

echo.

:: Summary
echo 📋 الملخص:
echo ==========
echo.

:: Count running services
set /a running_count=0

netstat -an | findstr :3000 >nul 2>&1
if not errorlevel 1 set /a running_count+=1

netstat -an | findstr :3100 >nul 2>&1
if not errorlevel 1 set /a running_count+=1

if %running_count%==2 (
    echo 🟢 النظام يعمل بشكل كامل (%running_count%/2)
    echo.
    echo 🌐 روابط الوصول:
    echo ================
    echo 📱 التطبيق: http://localhost:3100
    echo 🔧 API: http://localhost:3000
    echo 📊 Health: http://localhost:3000/api/health
    echo.
    echo 🔐 بيانات الدخول:
    echo =================
    echo 👨‍💼 مدير: <EMAIL> / admin123
    echo 👤 مستخدم: <EMAIL> / password123
    echo.
    echo ✅ النظام جاهز للاستخدام!
) else if %running_count%==1 (
    echo 🟡 النظام يعمل جزئياً (%running_count%/2)
    echo ⚠️ بعض الخدمات متوقفة
) else (
    echo 🔴 النظام متوقف (%running_count%/2)
    echo.
    echo 🔧 لتشغيل النظام:
    echo ================
    echo run-system.bat
)

echo.

:: Show available commands
echo 🔧 الأوامر المتاحة:
echo ==================
echo system-check.bat          - فحص حالة النظام (هذا الأمر)
echo run-system.bat            - تشغيل النظام
echo quick-start.bat           - تشغيل سريع
echo node system-status.js     - تقرير مفصل
echo.

pause
