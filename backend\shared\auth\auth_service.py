"""
Authentication Service
=====================
خدمة المصادقة المتقدمة
"""

import asyncio
import logging
import secrets
import pyotp
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from dataclasses import dataclass

import asyncpg
from email_validator import validate_email, EmailNotValidError

from .jwt_manager import JWTManager, UserRole, Permission, TokenType, AuthResult, TokenPayload
from ..database.connection import DatabaseConnection
from ..utils.email_service import EmailService
from ..utils.sms_service import SMSService
from ..security.encryption import EncryptionService

logger = logging.getLogger(__name__)


@dataclass
class UserData:
    """بيانات المستخدم"""
    id: str
    email: str
    phone: str
    password_hash: str
    role: UserRole
    is_active: bool
    is_verified: bool
    two_factor_enabled: bool
    two_factor_secret: Optional[str]
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime]
    failed_login_attempts: int
    locked_until: Optional[datetime]


@dataclass
class RegistrationData:
    """بيانات التسجيل"""
    email: str
    phone: str
    password: str
    first_name: str
    last_name: str
    date_of_birth: str
    nationality: str
    role: UserRole = UserRole.USER


class AuthService:
    """خدمة المصادقة المتقدمة"""
    
    def __init__(
        self,
        jwt_manager: JWTManager,
        db_connection: DatabaseConnection,
        email_service: EmailService,
        sms_service: SMSService,
        encryption_service: EncryptionService
    ):
        self.jwt_manager = jwt_manager
        self.db_connection = db_connection
        self.email_service = email_service
        self.sms_service = sms_service
        self.encryption_service = encryption_service
        
        # Configuration
        self.max_login_attempts = 5
        self.lockout_duration_minutes = 30
        self.password_reset_expire_minutes = 15
        self.email_verification_expire_hours = 24
        
        # Statistics
        self.total_logins = 0
        self.successful_logins = 0
        self.failed_logins = 0
        self.registrations = 0
    
    async def register_user(
        self,
        registration_data: RegistrationData,
        ip_address: str = None,
        user_agent: str = None
    ) -> AuthResult:
        """تسجيل مستخدم جديد"""
        try:
            logger.info(f"🔐 User registration attempt: {registration_data.email}")
            
            # Validate email
            try:
                valid_email = validate_email(registration_data.email)
                registration_data.email = valid_email.email
            except EmailNotValidError as e:
                return AuthResult(
                    success=False,
                    error=f"عنوان البريد الإلكتروني غير صحيح: {str(e)}"
                )
            
            # Check if user already exists
            existing_user = await self._get_user_by_email(registration_data.email)
            if existing_user:
                return AuthResult(
                    success=False,
                    error="المستخدم موجود بالفعل"
                )
            
            # Check phone number
            existing_phone = await self._get_user_by_phone(registration_data.phone)
            if existing_phone:
                return AuthResult(
                    success=False,
                    error="رقم الهاتف مستخدم بالفعل"
                )
            
            # Validate password strength
            password_validation = self._validate_password_strength(registration_data.password)
            if not password_validation["valid"]:
                return AuthResult(
                    success=False,
                    error=f"كلمة المرور ضعيفة: {password_validation['message']}"
                )
            
            # Hash password
            password_hash = self.jwt_manager.hash_password(registration_data.password)
            
            # Generate user ID
            user_id = f"user_{secrets.token_urlsafe(16)}"
            
            # Create user in database
            user_created = await self._create_user_in_db(
                user_id=user_id,
                registration_data=registration_data,
                password_hash=password_hash,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            if not user_created:
                return AuthResult(
                    success=False,
                    error="فشل في إنشاء المستخدم"
                )
            
            # Send email verification
            await self._send_email_verification(user_id, registration_data.email)
            
            # Update statistics
            self.registrations += 1
            
            logger.info(f"✅ User registered successfully: {registration_data.email}")
            
            return AuthResult(
                success=True,
                user_id=user_id,
                error="تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني."
            )
            
        except Exception as e:
            logger.error(f"❌ User registration failed: {e}")
            return AuthResult(
                success=False,
                error="حدث خطأ أثناء التسجيل"
            )
    
    async def login_user(
        self,
        email: str,
        password: str,
        device_id: str = None,
        ip_address: str = None,
        user_agent: str = None
    ) -> AuthResult:
        """تسجيل دخول المستخدم"""
        try:
            self.total_logins += 1
            logger.info(f"🔐 Login attempt: {email}")
            
            # Check if account is locked
            if self.jwt_manager.is_account_locked(email, ip_address):
                self.failed_logins += 1
                return AuthResult(
                    success=False,
                    error="الحساب مقفل مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة"
                )
            
            # Get user from database
            user = await self._get_user_by_email(email)
            if not user:
                self.jwt_manager.track_failed_login(email, ip_address)
                self.failed_logins += 1
                return AuthResult(
                    success=False,
                    error="بيانات تسجيل الدخول غير صحيحة"
                )
            
            # Check if user is active
            if not user.is_active:
                return AuthResult(
                    success=False,
                    error="الحساب غير نشط"
                )
            
            # Verify password
            if not self.jwt_manager.verify_password(password, user.password_hash):
                self.jwt_manager.track_failed_login(email, ip_address)
                await self._update_failed_login_attempts(user.id)
                self.failed_logins += 1
                return AuthResult(
                    success=False,
                    error="بيانات تسجيل الدخول غير صحيحة"
                )
            
            # Check if 2FA is required
            if user.two_factor_enabled:
                # Generate temporary session for 2FA
                temp_session_id = self.jwt_manager.generate_session_id()
                
                return AuthResult(
                    success=False,
                    requires_2fa=True,
                    session_id=temp_session_id,
                    error="مطلوب التحقق بخطوتين"
                )
            
            # Generate session and tokens
            session_id = self.jwt_manager.generate_session_id()
            
            access_token = self.jwt_manager.create_access_token(
                user_id=user.id,
                email=user.email,
                role=user.role,
                session_id=session_id,
                device_id=device_id,
                ip_address=ip_address,
                two_factor_verified=False
            )
            
            refresh_token = self.jwt_manager.create_refresh_token(
                user_id=user.id,
                session_id=session_id
            )
            
            # Clear failed login attempts
            self.jwt_manager.clear_failed_attempts(email, ip_address)
            await self._clear_failed_login_attempts(user.id)
            
            # Update last login
            await self._update_last_login(user.id, ip_address, user_agent)
            
            # Update statistics
            self.successful_logins += 1
            
            logger.info(f"✅ User logged in successfully: {email}")
            
            return AuthResult(
                success=True,
                user_id=user.id,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_in=self.jwt_manager.access_token_expire_minutes * 60,
                session_id=session_id
            )
            
        except Exception as e:
            logger.error(f"❌ User login failed: {e}")
            self.failed_logins += 1
            return AuthResult(
                success=False,
                error="حدث خطأ أثناء تسجيل الدخول"
            )
    
    async def verify_two_factor(
        self,
        user_id: str,
        totp_code: str,
        session_id: str,
        device_id: str = None,
        ip_address: str = None
    ) -> AuthResult:
        """التحقق من الرمز ثنائي العامل"""
        try:
            logger.info(f"🔐 2FA verification attempt: {user_id}")
            
            # Get user
            user = await self._get_user_by_id(user_id)
            if not user or not user.two_factor_enabled:
                return AuthResult(
                    success=False,
                    error="المستخدم غير موجود أو التحقق بخطوتين غير مفعل"
                )
            
            # Verify TOTP code
            totp = pyotp.TOTP(user.two_factor_secret)
            if not totp.verify(totp_code, valid_window=1):
                return AuthResult(
                    success=False,
                    error="رمز التحقق غير صحيح"
                )
            
            # Generate new tokens with 2FA verified
            new_session_id = self.jwt_manager.generate_session_id()
            
            access_token = self.jwt_manager.create_access_token(
                user_id=user.id,
                email=user.email,
                role=user.role,
                session_id=new_session_id,
                device_id=device_id,
                ip_address=ip_address,
                two_factor_verified=True
            )
            
            refresh_token = self.jwt_manager.create_refresh_token(
                user_id=user.id,
                session_id=new_session_id
            )
            
            logger.info(f"✅ 2FA verified successfully: {user_id}")
            
            return AuthResult(
                success=True,
                user_id=user.id,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_in=self.jwt_manager.access_token_expire_minutes * 60,
                session_id=new_session_id
            )
            
        except Exception as e:
            logger.error(f"❌ 2FA verification failed: {e}")
            return AuthResult(
                success=False,
                error="حدث خطأ أثناء التحقق"
            )
    
    async def logout_user(self, token: str) -> bool:
        """تسجيل خروج المستخدم"""
        try:
            # Verify token
            token_payload = self.jwt_manager.verify_token(token)
            if not token_payload:
                return False
            
            # Blacklist token
            self.jwt_manager.blacklist_token(token)
            
            # Logout session
            self.jwt_manager.logout_session(token_payload.session_id)
            
            logger.info(f"✅ User logged out: {token_payload.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ User logout failed: {e}")
            return False
    
    async def refresh_token(self, refresh_token: str) -> Optional[str]:
        """تحديث رمز الوصول"""
        try:
            new_access_token = self.jwt_manager.refresh_access_token(refresh_token)
            
            if new_access_token:
                logger.info("✅ Token refreshed successfully")
                return new_access_token
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Token refresh failed: {e}")
            return None
    
    async def enable_two_factor(self, user_id: str) -> Optional[str]:
        """تفعيل التحقق بخطوتين"""
        try:
            # Generate secret
            secret = pyotp.random_base32()
            
            # Update user in database
            await self._update_two_factor_secret(user_id, secret)
            
            # Generate QR code URL
            user = await self._get_user_by_id(user_id)
            if user:
                totp = pyotp.TOTP(secret)
                qr_url = totp.provisioning_uri(
                    name=user.email,
                    issuer_name="WS Transfir"
                )
                
                logger.info(f"✅ 2FA enabled for user: {user_id}")
                return qr_url
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Enable 2FA failed: {e}")
            return None
    
    def _validate_password_strength(self, password: str) -> Dict[str, Any]:
        """التحقق من قوة كلمة المرور"""
        if len(password) < 8:
            return {"valid": False, "message": "كلمة المرور يجب أن تكون 8 أحرف على الأقل"}
        
        if not any(c.isupper() for c in password):
            return {"valid": False, "message": "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل"}
        
        if not any(c.islower() for c in password):
            return {"valid": False, "message": "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل"}
        
        if not any(c.isdigit() for c in password):
            return {"valid": False, "message": "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"}
        
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            return {"valid": False, "message": "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل"}
        
        return {"valid": True, "message": "كلمة المرور قوية"}
    
    # Database helper methods (simplified implementations)
    async def _get_user_by_email(self, email: str) -> Optional[UserData]:
        """الحصول على المستخدم بالبريد الإلكتروني"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT id, email, phone, password_hash, role, is_active, 
                           is_verified, two_factor_enabled, two_factor_secret,
                           created_at, updated_at, last_login, failed_login_attempts, locked_until
                    FROM users WHERE email = $1
                """
                row = await conn.fetchrow(query, email)
                
                if row:
                    return UserData(
                        id=row['id'],
                        email=row['email'],
                        phone=row['phone'],
                        password_hash=row['password_hash'],
                        role=UserRole(row['role']),
                        is_active=row['is_active'],
                        is_verified=row['is_verified'],
                        two_factor_enabled=row['two_factor_enabled'],
                        two_factor_secret=row['two_factor_secret'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at'],
                        last_login=row['last_login'],
                        failed_login_attempts=row['failed_login_attempts'],
                        locked_until=row['locked_until']
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Get user by email failed: {e}")
            return None
    
    async def _get_user_by_id(self, user_id: str) -> Optional[UserData]:
        """الحصول على المستخدم بالمعرف"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT id, email, phone, password_hash, role, is_active, 
                           is_verified, two_factor_enabled, two_factor_secret,
                           created_at, updated_at, last_login, failed_login_attempts, locked_until
                    FROM users WHERE id = $1
                """
                row = await conn.fetchrow(query, user_id)
                
                if row:
                    return UserData(
                        id=row['id'],
                        email=row['email'],
                        phone=row['phone'],
                        password_hash=row['password_hash'],
                        role=UserRole(row['role']),
                        is_active=row['is_active'],
                        is_verified=row['is_verified'],
                        two_factor_enabled=row['two_factor_enabled'],
                        two_factor_secret=row['two_factor_secret'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at'],
                        last_login=row['last_login'],
                        failed_login_attempts=row['failed_login_attempts'],
                        locked_until=row['locked_until']
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Get user by ID failed: {e}")
            return None
    
    async def _get_user_by_phone(self, phone: str) -> Optional[UserData]:
        """الحصول على المستخدم برقم الهاتف"""
        # Similar implementation to _get_user_by_email
        return None
    
    async def _create_user_in_db(
        self,
        user_id: str,
        registration_data: RegistrationData,
        password_hash: str,
        ip_address: str = None,
        user_agent: str = None
    ) -> bool:
        """إنشاء المستخدم في قاعدة البيانات"""
        # Implementation would insert user into database
        return True
    
    async def _send_email_verification(self, user_id: str, email: str):
        """إرسال رسالة التحقق من البريد الإلكتروني"""
        # Implementation would send verification email
        pass
    
    async def _update_failed_login_attempts(self, user_id: str):
        """تحديث محاولات تسجيل الدخول الفاشلة"""
        # Implementation would update failed attempts in database
        pass
    
    async def _clear_failed_login_attempts(self, user_id: str):
        """مسح محاولات تسجيل الدخول الفاشلة"""
        # Implementation would clear failed attempts in database
        pass
    
    async def _update_last_login(self, user_id: str, ip_address: str = None, user_agent: str = None):
        """تحديث آخر تسجيل دخول"""
        # Implementation would update last login in database
        pass
    
    async def _update_two_factor_secret(self, user_id: str, secret: str):
        """تحديث سر التحقق بخطوتين"""
        # Implementation would update 2FA secret in database
        pass
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المصادقة"""
        return {
            "total_logins": self.total_logins,
            "successful_logins": self.successful_logins,
            "failed_logins": self.failed_logins,
            "success_rate": self.successful_logins / max(self.total_logins, 1),
            "registrations": self.registrations,
            "jwt_statistics": self.jwt_manager.get_statistics()
        }
