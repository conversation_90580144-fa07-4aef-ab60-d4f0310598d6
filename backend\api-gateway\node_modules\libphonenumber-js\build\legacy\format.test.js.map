{"version": 3, "file": "format.test.js", "names": ["formatNumber", "parameters", "push", "metadata", "_formatNumber", "apply", "describe", "it", "should", "equal", "phone", "countryCallingCode", "options", "formatExtension", "number", "extension", "country", "ext", "nationalPrefix", "deep", "thrower", "expect", "to", "be", "undefined", "fromCountry", "humanReadable"], "sources": ["../../source/legacy/format.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' assert { type: 'json' }\r\nimport _formatNumber from './format.js'\r\n\r\nfunction formatNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _formatNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('format', () => {\r\n\tit('should work with the first argument being a E.164 number', () => {\r\n\t\tformatNumber('+12133734253', 'NATIONAL').should.equal('(*************')\r\n\t\tformatNumber('+12133734253', 'INTERNATIONAL').should.equal('****** 373 4253')\r\n\r\n\t\t// Invalid number.\r\n\t\tformatNumber('+12111111111', 'NATIONAL').should.equal('(*************')\r\n\r\n\t\t// Formatting invalid E.164 numbers.\r\n\t\tformatNumber('+11111', 'INTERNATIONAL').should.equal('+1 1111')\r\n\t\tformatNumber('+11111', 'NATIONAL').should.equal('1111')\r\n\t})\r\n\r\n\tit('should work with the first object argument expanded', () => {\r\n\t\tformatNumber('2133734253', 'US', 'NATIONAL').should.equal('(*************')\r\n\t\tformatNumber('2133734253', 'US', 'INTERNATIONAL').should.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should support legacy \"National\" / \"International\" formats', () => {\r\n\t\tformatNumber('2133734253', 'US', 'National').should.equal('(*************')\r\n\t\tformatNumber('2133734253', 'US', 'International').should.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should format using formats with no leading digits (`format.leadingDigitsPatterns().length === 0`)', () => {\r\n\t\tformatNumber({ phone: '12345678901', countryCallingCode: 888 }, 'INTERNATIONAL').should.equal('+888 123 456 78901')\r\n\t})\r\n\r\n\tit('should sort out the arguments', () => {\r\n\t\tconst options = {\r\n\t\t\tformatExtension: (number, extension) => `${number} доб. ${extension}`\r\n\t\t}\r\n\r\n\t\tformatNumber({\r\n\t\t\tphone   : '8005553535',\r\n\t\t\tcountry : 'RU',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'NATIONAL', options).should.equal('8 (800) 555-35-35 доб. 123')\r\n\r\n\t\t// Parse number from string.\r\n\t\tformatNumber('+78005553535', 'NATIONAL', options).should.equal('8 (800) 555-35-35')\r\n\t\tformatNumber('8005553535', 'RU', 'NATIONAL', options).should.equal('8 (800) 555-35-35')\r\n\t})\r\n\r\n\tit('should format with national prefix when specifically instructed', () => {\r\n\t\t// With national prefix.\r\n\t\tformatNumber('88005553535', 'RU', 'NATIONAL').should.equal('8 (800) 555-35-35')\r\n\t\t// Without national prefix via an explicitly set option.\r\n\t\tformatNumber('88005553535', 'RU', 'NATIONAL', { nationalPrefix: false }).should.equal('800 555-35-35')\r\n\t})\r\n\r\n\tit('should format valid phone numbers', () => {\r\n\t\t// Switzerland\r\n\t\tformatNumber({ country: 'CH', phone: '446681800' }, 'INTERNATIONAL').should.equal('+41 44 668 18 00')\r\n\t\tformatNumber({ country: 'CH', phone: '446681800' }, 'E.164').should.equal('+41446681800')\r\n\t\tformatNumber({ country: 'CH', phone: '446681800' }, 'RFC3966').should.equal('tel:+41446681800')\r\n\t\tformatNumber({ country: 'CH', phone: '446681800' }, 'NATIONAL').should.equal('044 668 18 00')\r\n\r\n\t\t// France\r\n\t\tformatNumber({ country: 'FR', phone: '169454850' }, 'NATIONAL').should.equal('01 69 45 48 50')\r\n\r\n\t\t// Kazakhstan\r\n\t\tformatNumber('****** 211 1111', 'NATIONAL').should.deep.equal('8 (*************')\r\n\t})\r\n\r\n\tit('should format national numbers with national prefix even if it\\'s optional', () => {\r\n\t\t// Russia\r\n\t\tformatNumber({ country: 'RU', phone: '9991234567' }, 'NATIONAL').should.equal('8 (999) 123-45-67')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No phone number\r\n\t\tformatNumber('', 'RU', 'INTERNATIONAL').should.equal('')\r\n\t\tformatNumber('', 'RU', 'NATIONAL').should.equal('')\r\n\r\n\t\tformatNumber({ country: 'RU', phone: '' }, 'INTERNATIONAL').should.equal('+7')\r\n\t\tformatNumber({ country: 'RU', phone: '' }, 'NATIONAL').should.equal('')\r\n\r\n\t\t// No suitable format\r\n\t\tformatNumber('+121337342530', 'US', 'NATIONAL').should.equal('21337342530')\r\n\t\t// No suitable format (leading digits mismatch)\r\n\t\tformatNumber('28199999', 'AD', 'NATIONAL').should.equal('28199999')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => formatNumber(89150000000, 'RU', 'NATIONAL')\r\n\t\tthrower.should.throw('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\t\t// No metadata for country\r\n\t\texpect(() => formatNumber('+121337342530', 'USA', 'NATIONAL')).to.throw('Unknown country')\r\n\t\texpect(() => formatNumber('21337342530', 'USA', 'NATIONAL')).to.throw('Unknown country')\r\n\r\n\t\t// No format type\r\n\t\tthrower = () => formatNumber('+123')\r\n\t\tthrower.should.throw('`format` argument not passed')\r\n\r\n\t\t// Unknown format type\r\n\t\tthrower = () => formatNumber('123', 'US', 'Gay')\r\n\t\tthrower.should.throw('Unknown \"format\" argument')\r\n\r\n\t\t// No metadata\r\n\t\tthrower = () => _formatNumber('123', 'US', 'E.164')\r\n\t\tthrower.should.throw('`metadata`')\r\n\r\n\t\t// No formats\r\n\t\tformatNumber('012345', 'AC', 'NATIONAL').should.equal('012345')\r\n\r\n\t\t// No `fromCountry` for `IDD` format.\r\n\t\texpect(formatNumber('+78005553535', 'IDD')).to.be.undefined\r\n\r\n\t\t// `fromCountry` has no default IDD prefix.\r\n\t\texpect(formatNumber('+78005553535', 'IDD', { fromCountry: 'BO' })).to.be.undefined\r\n\r\n\t\t// No such country.\r\n\t\texpect(() => formatNumber({ phone: '123', country: 'USA' }, 'NATIONAL')).to.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should format phone number extensions', () => {\r\n\t\t// National\r\n\t\tformatNumber({\r\n\t\t\tcountry: 'US',\r\n\t\t\tphone: '2133734253',\r\n\t\t\text: '123'\r\n\t\t},\r\n\t\t'NATIONAL').should.equal('(************* ext. 123')\r\n\r\n\t\t// International\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL').should.equal('****** 373 4253 ext. 123')\r\n\r\n\t\t// International\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL').should.equal('****** 373 4253 ext. 123')\r\n\r\n\t\t// E.164\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'E.164').should.equal('+12133734253')\r\n\r\n\t\t// RFC3966\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'RFC3966').should.equal('tel:+12133734253;ext=123')\r\n\r\n\t\t// Custom ext prefix.\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'GB',\r\n\t\t\tphone   : '7912345678',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL').should.equal('+44 7912 345678 x123')\r\n\t})\r\n\r\n\tit('should work with Argentina numbers', () => {\r\n\t\t// The same mobile number is written differently\r\n\t\t// in different formats in Argentina:\r\n\t\t// `9` gets prepended in international format.\r\n\t\tformatNumber({ country: 'AR', phone: '3435551212' }, 'INTERNATIONAL')\r\n\t\t\t.should.equal('+54 3435 55 1212')\r\n\t\tformatNumber({ country: 'AR', phone: '3435551212' }, 'NATIONAL')\r\n\t\t\t.should.equal('03435 55-1212')\r\n\t})\r\n\r\n\tit('should work with Mexico numbers', () => {\r\n\t\t// Fixed line.\r\n\t\tformatNumber({ country: 'MX', phone: '4499780001' }, 'INTERNATIONAL')\r\n\t\t\t.should.equal('+52 ************')\r\n\t\tformatNumber({ country: 'MX', phone: '4499780001' }, 'NATIONAL')\r\n\t\t\t.should.equal('************')\r\n\t\t\t// or '(449)978-0001'.\r\n\t\t// Mobile.\r\n\t\t// `1` is prepended before area code to mobile numbers in international format.\r\n\t\tformatNumber({ country: 'MX', phone: '3312345678' }, 'INTERNATIONAL')\r\n\t\t\t.should.equal('+52 33 1234 5678')\r\n\t\tformatNumber({ country: 'MX', phone: '3312345678' }, 'NATIONAL')\r\n\t\t\t.should.equal('33 1234 5678')\r\n\t\t\t// or '045 33 1234-5678'.\r\n\t})\r\n\r\n\tit('should format possible numbers', () => {\r\n\t\tformatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'E.164')\r\n\t\t\t.should.equal('+71111111111')\r\n\r\n\t\tformatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'NATIONAL')\r\n\t\t\t.should.equal('1111111111')\r\n\r\n\t\tformatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'INTERNATIONAL')\r\n\t\t\t.should.equal('*************')\r\n\t})\r\n\r\n\tit('should format IDD-prefixed number', () => {\r\n\t\t// No `fromCountry`.\r\n\t\texpect(formatNumber('+78005553535', 'IDD')).to.be.undefined\r\n\r\n\t\t// No default IDD prefix.\r\n\t\texpect(formatNumber('+78005553535', 'IDD', { fromCountry: 'BO' })).to.be.undefined\r\n\r\n\t\t// Same country calling code.\r\n\t\tformatNumber('+12133734253', 'IDD', { fromCountry: 'CA', humanReadable: true }).should.equal('1 (*************')\r\n\t\tformatNumber('+78005553535', 'IDD', { fromCountry: 'KZ', humanReadable: true }).should.equal('8 (800) 555-35-35')\r\n\r\n\t\t// formatNumber('+78005553535', 'IDD', { fromCountry: 'US' }).should.equal('01178005553535')\r\n\t\tformatNumber('+78005553535', 'IDD', { fromCountry: 'US', humanReadable: true }).should.equal('011 7 800 555 35 35')\r\n\t})\r\n\r\n\tit('should format non-geographic numbering plan phone numbers', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/323\r\n\t\tformatNumber('+870773111632', 'INTERNATIONAL').should.equal('+870 773 111 632')\r\n\t\tformatNumber('+870773111632', 'NATIONAL').should.equal('773 111 632')\r\n\t})\r\n\r\n\tit('should use the default IDD prefix when formatting a phone number', () => {\r\n\t\t// Testing preferred international prefixes with ~ are supported.\r\n\t\t// (\"~\" designates waiting on a line until proceeding with the input).\r\n\t\tformatNumber('+390236618300', 'IDD', { fromCountry: 'BY' }).should.equal('8~10 39 02 3661 8300')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;;;AAEA,SAASA,YAAT,GAAqC;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACpCA,UAAU,CAACC,IAAX,CAAgBC,uBAAhB;EACA,OAAOC,kBAAA,CAAcC,KAAd,CAAoB,IAApB,EAA0BJ,UAA1B,CAAP;AACA;;AAEDK,QAAQ,CAAC,QAAD,EAAW,YAAM;EACxBC,EAAE,CAAC,0DAAD,EAA6D,YAAM;IACpEP,YAAY,CAAC,cAAD,EAAiB,UAAjB,CAAZ,CAAyCQ,MAAzC,CAAgDC,KAAhD,CAAsD,gBAAtD;IACAT,YAAY,CAAC,cAAD,EAAiB,eAAjB,CAAZ,CAA8CQ,MAA9C,CAAqDC,KAArD,CAA2D,iBAA3D,EAFoE,CAIpE;;IACAT,YAAY,CAAC,cAAD,EAAiB,UAAjB,CAAZ,CAAyCQ,MAAzC,CAAgDC,KAAhD,CAAsD,gBAAtD,EALoE,CAOpE;;IACAT,YAAY,CAAC,QAAD,EAAW,eAAX,CAAZ,CAAwCQ,MAAxC,CAA+CC,KAA/C,CAAqD,SAArD;IACAT,YAAY,CAAC,QAAD,EAAW,UAAX,CAAZ,CAAmCQ,MAAnC,CAA0CC,KAA1C,CAAgD,MAAhD;EACA,CAVC,CAAF;EAYAF,EAAE,CAAC,qDAAD,EAAwD,YAAM;IAC/DP,YAAY,CAAC,YAAD,EAAe,IAAf,EAAqB,UAArB,CAAZ,CAA6CQ,MAA7C,CAAoDC,KAApD,CAA0D,gBAA1D;IACAT,YAAY,CAAC,YAAD,EAAe,IAAf,EAAqB,eAArB,CAAZ,CAAkDQ,MAAlD,CAAyDC,KAAzD,CAA+D,iBAA/D;EACA,CAHC,CAAF;EAKAF,EAAE,CAAC,4DAAD,EAA+D,YAAM;IACtEP,YAAY,CAAC,YAAD,EAAe,IAAf,EAAqB,UAArB,CAAZ,CAA6CQ,MAA7C,CAAoDC,KAApD,CAA0D,gBAA1D;IACAT,YAAY,CAAC,YAAD,EAAe,IAAf,EAAqB,eAArB,CAAZ,CAAkDQ,MAAlD,CAAyDC,KAAzD,CAA+D,iBAA/D;EACA,CAHC,CAAF;EAKAF,EAAE,CAAC,oGAAD,EAAuG,YAAM;IAC9GP,YAAY,CAAC;MAAEU,KAAK,EAAE,aAAT;MAAwBC,kBAAkB,EAAE;IAA5C,CAAD,EAAoD,eAApD,CAAZ,CAAiFH,MAAjF,CAAwFC,KAAxF,CAA8F,oBAA9F;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,+BAAD,EAAkC,YAAM;IACzC,IAAMK,OAAO,GAAG;MACfC,eAAe,EAAE,yBAACC,MAAD,EAASC,SAAT;QAAA,iBAA0BD,MAA1B,kCAAyCC,SAAzC;MAAA;IADF,CAAhB;IAIAf,YAAY,CAAC;MACZU,KAAK,EAAK,YADE;MAEZM,OAAO,EAAG,IAFE;MAGZC,GAAG,EAAO;IAHE,CAAD,EAKZ,UALY,EAKAL,OALA,CAAZ,CAKqBJ,MALrB,CAK4BC,KAL5B,CAKkC,4BALlC,EALyC,CAYzC;;IACAT,YAAY,CAAC,cAAD,EAAiB,UAAjB,EAA6BY,OAA7B,CAAZ,CAAkDJ,MAAlD,CAAyDC,KAAzD,CAA+D,mBAA/D;IACAT,YAAY,CAAC,YAAD,EAAe,IAAf,EAAqB,UAArB,EAAiCY,OAAjC,CAAZ,CAAsDJ,MAAtD,CAA6DC,KAA7D,CAAmE,mBAAnE;EACA,CAfC,CAAF;EAiBAF,EAAE,CAAC,iEAAD,EAAoE,YAAM;IAC3E;IACAP,YAAY,CAAC,aAAD,EAAgB,IAAhB,EAAsB,UAAtB,CAAZ,CAA8CQ,MAA9C,CAAqDC,KAArD,CAA2D,mBAA3D,EAF2E,CAG3E;;IACAT,YAAY,CAAC,aAAD,EAAgB,IAAhB,EAAsB,UAAtB,EAAkC;MAAEkB,cAAc,EAAE;IAAlB,CAAlC,CAAZ,CAAyEV,MAAzE,CAAgFC,KAAhF,CAAsF,eAAtF;EACA,CALC,CAAF;EAOAF,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C;IACAP,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,eAAxC,CAAZ,CAAqEF,MAArE,CAA4EC,KAA5E,CAAkF,kBAAlF;IACAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,OAAxC,CAAZ,CAA6DF,MAA7D,CAAoEC,KAApE,CAA0E,cAA1E;IACAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,SAAxC,CAAZ,CAA+DF,MAA/D,CAAsEC,KAAtE,CAA4E,kBAA5E;IACAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,UAAxC,CAAZ,CAAgEF,MAAhE,CAAuEC,KAAvE,CAA6E,eAA7E,EAL6C,CAO7C;;IACAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,UAAxC,CAAZ,CAAgEF,MAAhE,CAAuEC,KAAvE,CAA6E,gBAA7E,EAR6C,CAU7C;;IACAT,YAAY,CAAC,iBAAD,EAAoB,UAApB,CAAZ,CAA4CQ,MAA5C,CAAmDW,IAAnD,CAAwDV,KAAxD,CAA8D,kBAA9D;EACA,CAZC,CAAF;EAcAF,EAAE,CAAC,4EAAD,EAA+E,YAAM;IACtF;IACAP,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,UAAzC,CAAZ,CAAiEF,MAAjE,CAAwEC,KAAxE,CAA8E,mBAA9E;EACA,CAHC,CAAF;EAKAF,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAIa,OAAJ,CADqC,CAGrC;;IACApB,YAAY,CAAC,EAAD,EAAK,IAAL,EAAW,eAAX,CAAZ,CAAwCQ,MAAxC,CAA+CC,KAA/C,CAAqD,EAArD;IACAT,YAAY,CAAC,EAAD,EAAK,IAAL,EAAW,UAAX,CAAZ,CAAmCQ,MAAnC,CAA0CC,KAA1C,CAAgD,EAAhD;IAEAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAA+B,eAA/B,CAAZ,CAA4DF,MAA5D,CAAmEC,KAAnE,CAAyE,IAAzE;IACAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAA+B,UAA/B,CAAZ,CAAuDF,MAAvD,CAA8DC,KAA9D,CAAoE,EAApE,EARqC,CAUrC;;IACAT,YAAY,CAAC,eAAD,EAAkB,IAAlB,EAAwB,UAAxB,CAAZ,CAAgDQ,MAAhD,CAAuDC,KAAvD,CAA6D,aAA7D,EAXqC,CAYrC;;IACAT,YAAY,CAAC,UAAD,EAAa,IAAb,EAAmB,UAAnB,CAAZ,CAA2CQ,MAA3C,CAAkDC,KAAlD,CAAwD,UAAxD,EAbqC,CAerC;;IACAW,OAAO,GAAG;MAAA,OAAMpB,YAAY,CAAC,WAAD,EAAc,IAAd,EAAoB,UAApB,CAAlB;IAAA,CAAV;;IACAoB,OAAO,CAACZ,MAAR,UAAqB,oFAArB,EAjBqC,CAmBrC;;IACAa,MAAM,CAAC;MAAA,OAAMrB,YAAY,CAAC,eAAD,EAAkB,KAAlB,EAAyB,UAAzB,CAAlB;IAAA,CAAD,CAAN,CAA+DsB,EAA/D,UAAwE,iBAAxE;IACAD,MAAM,CAAC;MAAA,OAAMrB,YAAY,CAAC,aAAD,EAAgB,KAAhB,EAAuB,UAAvB,CAAlB;IAAA,CAAD,CAAN,CAA6DsB,EAA7D,UAAsE,iBAAtE,EArBqC,CAuBrC;;IACAF,OAAO,GAAG;MAAA,OAAMpB,YAAY,CAAC,MAAD,CAAlB;IAAA,CAAV;;IACAoB,OAAO,CAACZ,MAAR,UAAqB,8BAArB,EAzBqC,CA2BrC;;IACAY,OAAO,GAAG;MAAA,OAAMpB,YAAY,CAAC,KAAD,EAAQ,IAAR,EAAc,KAAd,CAAlB;IAAA,CAAV;;IACAoB,OAAO,CAACZ,MAAR,UAAqB,2BAArB,EA7BqC,CA+BrC;;IACAY,OAAO,GAAG;MAAA,OAAM,IAAAhB,kBAAA,EAAc,KAAd,EAAqB,IAArB,EAA2B,OAA3B,CAAN;IAAA,CAAV;;IACAgB,OAAO,CAACZ,MAAR,UAAqB,YAArB,EAjCqC,CAmCrC;;IACAR,YAAY,CAAC,QAAD,EAAW,IAAX,EAAiB,UAAjB,CAAZ,CAAyCQ,MAAzC,CAAgDC,KAAhD,CAAsD,QAAtD,EApCqC,CAsCrC;;IACAY,MAAM,CAACrB,YAAY,CAAC,cAAD,EAAiB,KAAjB,CAAb,CAAN,CAA4CsB,EAA5C,CAA+CC,EAA/C,CAAkDC,SAAlD,CAvCqC,CAyCrC;;IACAH,MAAM,CAACrB,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAEyB,WAAW,EAAE;IAAf,CAAxB,CAAb,CAAN,CAAmEH,EAAnE,CAAsEC,EAAtE,CAAyEC,SAAzE,CA1CqC,CA4CrC;;IACAH,MAAM,CAAC;MAAA,OAAMrB,YAAY,CAAC;QAAEU,KAAK,EAAE,KAAT;QAAgBM,OAAO,EAAE;MAAzB,CAAD,EAAmC,UAAnC,CAAlB;IAAA,CAAD,CAAN,CAAyEM,EAAzE,UAAkF,iBAAlF;EACA,CA9CC,CAAF;EAgDAf,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjD;IACAP,YAAY,CAAC;MACZgB,OAAO,EAAE,IADG;MAEZN,KAAK,EAAE,YAFK;MAGZO,GAAG,EAAE;IAHO,CAAD,EAKZ,UALY,CAAZ,CAKYT,MALZ,CAKmBC,KALnB,CAKyB,yBALzB,EAFiD,CASjD;;IACAT,YAAY,CAAC;MACZgB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,eALY,CAAZ,CAKiBT,MALjB,CAKwBC,KALxB,CAK8B,0BAL9B,EAViD,CAiBjD;;IACAT,YAAY,CAAC;MACZgB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,eALY,CAAZ,CAKiBT,MALjB,CAKwBC,KALxB,CAK8B,0BAL9B,EAlBiD,CAyBjD;;IACAT,YAAY,CAAC;MACZgB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,OALY,CAAZ,CAKST,MALT,CAKgBC,KALhB,CAKsB,cALtB,EA1BiD,CAiCjD;;IACAT,YAAY,CAAC;MACZgB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,SALY,CAAZ,CAKWT,MALX,CAKkBC,KALlB,CAKwB,0BALxB,EAlCiD,CAyCjD;;IACAT,YAAY,CAAC;MACZgB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,eALY,CAAZ,CAKiBT,MALjB,CAKwBC,KALxB,CAK8B,sBAL9B;EAMA,CAhDC,CAAF;EAkDAF,EAAE,CAAC,oCAAD,EAAuC,YAAM;IAC9C;IACA;IACA;IACAP,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,eAAzC,CAAZ,CACEF,MADF,CACSC,KADT,CACe,kBADf;IAEAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,UAAzC,CAAZ,CACEF,MADF,CACSC,KADT,CACe,eADf;EAEA,CARC,CAAF;EAUAF,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C;IACAP,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,eAAzC,CAAZ,CACEF,MADF,CACSC,KADT,CACe,kBADf;IAEAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,UAAzC,CAAZ,CACEF,MADF,CACSC,KADT,CACe,cADf,EAJ2C,CAM1C;IACD;IACA;;IACAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,eAAzC,CAAZ,CACEF,MADF,CACSC,KADT,CACe,kBADf;IAEAT,YAAY,CAAC;MAAEgB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,UAAzC,CAAZ,CACEF,MADF,CACSC,KADT,CACe,cADf,EAX2C,CAa1C;EACD,CAdC,CAAF;EAgBAF,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1CP,YAAY,CAAC;MAAEW,kBAAkB,EAAE,GAAtB;MAA2BD,KAAK,EAAE;IAAlC,CAAD,EAAmD,OAAnD,CAAZ,CACEF,MADF,CACSC,KADT,CACe,cADf;IAGAT,YAAY,CAAC;MAAEW,kBAAkB,EAAE,GAAtB;MAA2BD,KAAK,EAAE;IAAlC,CAAD,EAAmD,UAAnD,CAAZ,CACEF,MADF,CACSC,KADT,CACe,YADf;IAGAT,YAAY,CAAC;MAAEW,kBAAkB,EAAE,GAAtB;MAA2BD,KAAK,EAAE;IAAlC,CAAD,EAAmD,eAAnD,CAAZ,CACEF,MADF,CACSC,KADT,CACe,eADf;EAEA,CATC,CAAF;EAWAF,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C;IACAc,MAAM,CAACrB,YAAY,CAAC,cAAD,EAAiB,KAAjB,CAAb,CAAN,CAA4CsB,EAA5C,CAA+CC,EAA/C,CAAkDC,SAAlD,CAF6C,CAI7C;;IACAH,MAAM,CAACrB,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAEyB,WAAW,EAAE;IAAf,CAAxB,CAAb,CAAN,CAAmEH,EAAnE,CAAsEC,EAAtE,CAAyEC,SAAzE,CAL6C,CAO7C;;IACAxB,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAEyB,WAAW,EAAE,IAAf;MAAqBC,aAAa,EAAE;IAApC,CAAxB,CAAZ,CAAgFlB,MAAhF,CAAuFC,KAAvF,CAA6F,kBAA7F;IACAT,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAEyB,WAAW,EAAE,IAAf;MAAqBC,aAAa,EAAE;IAApC,CAAxB,CAAZ,CAAgFlB,MAAhF,CAAuFC,KAAvF,CAA6F,mBAA7F,EAT6C,CAW7C;;IACAT,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAEyB,WAAW,EAAE,IAAf;MAAqBC,aAAa,EAAE;IAApC,CAAxB,CAAZ,CAAgFlB,MAAhF,CAAuFC,KAAvF,CAA6F,qBAA7F;EACA,CAbC,CAAF;EAeAF,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE;IACAP,YAAY,CAAC,eAAD,EAAkB,eAAlB,CAAZ,CAA+CQ,MAA/C,CAAsDC,KAAtD,CAA4D,kBAA5D;IACAT,YAAY,CAAC,eAAD,EAAkB,UAAlB,CAAZ,CAA0CQ,MAA1C,CAAiDC,KAAjD,CAAuD,aAAvD;EACA,CAJC,CAAF;EAMAF,EAAE,CAAC,kEAAD,EAAqE,YAAM;IAC5E;IACA;IACAP,YAAY,CAAC,eAAD,EAAkB,KAAlB,EAAyB;MAAEyB,WAAW,EAAE;IAAf,CAAzB,CAAZ,CAA4DjB,MAA5D,CAAmEC,KAAnE,CAAyE,sBAAzE;EACA,CAJC,CAAF;AAKA,CAvOO,CAAR"}