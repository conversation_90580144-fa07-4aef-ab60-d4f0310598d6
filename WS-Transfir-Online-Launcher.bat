@echo off
chcp 65001 >nul
title WS Transfir - Professional Online System Launcher

:: ████████████████████████████████████████████████████████████████
:: ██                                                            ██
:: ██    🚀 WS TRANSFIR - PROFESSIONAL ONLINE LAUNCHER 🚀       ██
:: ██                                                            ██
:: ██    مشغل نظام WS Transfir الاحترافي الأونلاين              ██
:: ██    Professional Online Money Transfer System Launcher      ██
:: ██                                                            ██
:: ██    Version: 1.0.0 | Build: 2024.12.25                     ██
:: ██    Author: WS Transfir Development Team                    ██
:: ██                                                            ██
:: ████████████████████████████████████████████████████████████████

color 0B
cls

:: ASCII Art Header
echo.
echo     ██╗    ██╗███████╗    ████████╗██████╗  █████╗ ███╗   ██╗███████╗███████╗██╗██████╗ 
echo     ██║    ██║██╔════╝    ╚══██╔══╝██╔══██╗██╔══██╗████╗  ██║██╔════╝██╔════╝██║██╔══██╗
echo     ██║ █╗ ██║███████╗       ██║   ██████╔╝███████║██╔██╗ ██║███████╗█████╗  ██║██████╔╝
echo     ██║███╗██║╚════██║       ██║   ██╔══██╗██╔══██║██║╚██╗██║╚════██║██╔══╝  ██║██╔══██╗
echo     ╚███╔███╔╝███████║       ██║   ██║  ██║██║  ██║██║ ╚████║███████║██║     ██║██║  ██║
echo      ╚══╝╚══╝ ╚══════╝       ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚══════╝╚═╝     ╚═╝╚═╝  ╚═╝
echo.
echo                            🌐 ONLINE PROFESSIONAL SYSTEM 🌐
echo                            ================================
echo.

:: Get system information
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%DD%/%MM%/%YYYY% %HH%:%Min%:%Sec%"

echo 📅 التاريخ والوقت: %timestamp%
echo 🖥️ النظام: %OS%
echo 👤 المستخدم: %USERNAME%
echo 💻 الكمبيوتر: %COMPUTERNAME%
echo 🌍 الوضع: Professional Online Production
echo 📡 الوصول: Full External Access Enabled
echo 🔧 الخدمات: Backend + Frontend + API + Monitoring
echo.

:: System Requirements Check
echo ═══════════════════════════════════════════════════════════════
echo 🔍 فحص متطلبات النظام الاحترافي
echo ═══════════════════════════════════════════════════════════════
echo.

:: Check Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL ERROR: Node.js غير مثبت
    echo.
    echo 📥 يرجى تثبيت Node.js من الرابط التالي:
    echo 🌐 https://nodejs.org/en/download/
    echo 🔧 الإصدار المطلوب: 18.0.0 أو أحدث
    echo.
    echo اضغط أي مفتاح لفتح صفحة التحميل...
    pause >nul
    start "" "https://nodejs.org/en/download/"
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js متاح - الإصدار: %NODE_VERSION%

:: Check npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL ERROR: npm غير متاح
    echo 🔧 npm يأتي مع Node.js عادة
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm متاح - الإصدار: %NPM_VERSION%

:: Check internet connectivity
echo 🌐 فحص الاتصال بالإنترنت...
ping -n 1 ******* >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: لا يوجد اتصال بالإنترنت
    echo 📡 النظام سيعمل في الوضع المحلي فقط
    set INTERNET_STATUS=❌ غير متاح
) else (
    echo ✅ الاتصال بالإنترنت متاح
    set INTERNET_STATUS=✅ متاح
)

:: Check system resources
echo 🖥️ فحص موارد النظام...
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set /a RAM_GB=%%p/1024/1024/1024
    goto :ram_done
)
:ram_done

if %RAM_GB% LSS 2 (
    echo ⚠️ تحذير: ذاكرة النظام منخفضة (%RAM_GB% GB)
    echo 💡 يُنصح بـ 4 GB أو أكثر للأداء الأمثل
) else (
    echo ✅ ذاكرة النظام كافية (%RAM_GB% GB)
)

echo.

:: File System Check
echo ═══════════════════════════════════════════════════════════════
echo 📁 فحص ملفات النظام
echo ═══════════════════════════════════════════════════════════════
echo.

set "required_files=online-system-server.js online-frontend-server.js online-frontend.html package.json"
set "missing_files="
set "files_ok=1"

for %%f in (%required_files%) do (
    if exist "%%f" (
        echo ✅ %%f
    ) else (
        echo ❌ %%f - مفقود
        set "missing_files=!missing_files! %%f"
        set "files_ok=0"
    )
)

if %files_ok%==0 (
    echo.
    echo ❌ CRITICAL ERROR: ملفات مطلوبة مفقودة
    echo 📋 الملفات المفقودة:%missing_files%
    echo.
    echo 🔧 يرجى التأكد من وجود جميع ملفات النظام
    pause
    exit /b 1
)

echo ✅ جميع ملفات النظام موجودة

echo.

:: Dependencies Management
echo ═══════════════════════════════════════════════════════════════
echo 📦 إدارة المكتبات والتبعيات
echo ═══════════════════════════════════════════════════════════════
echo.

if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق بضع دقائق في المرة الأولى...
    echo.
    
    npm install express cors helmet compression express-rate-limit morgan --silent
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات الأساسية
        echo 🔧 جاري المحاولة مع إعدادات مختلفة...
        npm install express cors --silent
        if errorlevel 1 (
            echo ❌ فشل في تثبيت المكتبات نهائياً
            echo 📞 يرجى الاتصال بالدعم الفني
            pause
            exit /b 1
        )
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات مثبتة مسبقاً
    
    :: Verify critical dependencies
    echo 🔍 فحص المكتبات الحرجة...
    node -e "require('express'); require('cors'); console.log('✅ المكتبات الحرجة متاحة');" 2>nul
    if errorlevel 1 (
        echo ⚠️ بعض المكتبات قد تحتاج إعادة تثبيت
        echo 📦 إعادة تثبيت المكتبات...
        npm install express cors helmet compression express-rate-limit morgan --force --silent
    )
)

echo.

:: Port Management
echo ═══════════════════════════════════════════════════════════════
echo 🌐 إدارة المنافذ والشبكة
echo ═══════════════════════════════════════════════════════════════
echo.

:: Kill existing processes
echo 🛑 إيقاف العمليات السابقة...
taskkill /F /IM node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

:: Check port availability
echo 🔍 فحص توفر المنافذ...

:: Port 3000 (Backend)
netstat -an | findstr :3000 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ المنفذ 3000 مستخدم - تحرير المنفذ...
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 1 /nobreak >nul
)
echo ✅ المنفذ 3000 متاح (Backend API)

:: Port 3100 (Frontend)
netstat -an | findstr :3100 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ المنفذ 3100 مستخدم - تحرير المنفذ...
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3100') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 1 /nobreak >nul
)
echo ✅ المنفذ 3100 متاح (Frontend)

:: Get network information
echo 🌐 معلومات الشبكة...
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4" ^| findstr /v "127.0.0.1" ^| findstr /v "169.254"') do (
    for /f "tokens=1" %%j in ("%%i") do set LOCAL_IP=%%j
)

if not defined LOCAL_IP set LOCAL_IP=localhost
echo 🏠 عنوان IP المحلي: %LOCAL_IP%

echo.

:: System Launch
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🚀 تشغيل النظام الاحترافي الأونلاين                    ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:: Launch Backend Server
echo 🔧 تشغيل Backend Server...
start "WS Transfir - Backend API Server" cmd /k "title WS Transfir Backend API && color 0E && echo. && echo ████████████████████████████████████████ && echo ██  🔧 WS TRANSFIR BACKEND SERVER  ██ && echo ████████████████████████████████████████ && echo. && echo 🌐 Port: 3000 && echo 📡 Status: Starting... && echo 🔗 Health: http://localhost:3000/api/health && echo. && node online-system-server.js"

echo ⏳ انتظار تشغيل Backend (5 ثوان)...
timeout /t 5 /nobreak >nul

:: Test Backend
echo 🧪 اختبار Backend...
curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Backend قد يحتاج وقت إضافي للتشغيل
    set BACKEND_STATUS=⚠️ قيد التشغيل
) else (
    echo ✅ Backend يعمل بنجاح
    set BACKEND_STATUS=✅ يعمل
)

:: Launch Frontend Server
echo 🎨 تشغيل Frontend Server...
start "WS Transfir - Frontend Web Server" cmd /k "title WS Transfir Frontend && color 0A && echo. && echo ████████████████████████████████████████ && echo ██  🎨 WS TRANSFIR FRONTEND SERVER ██ && echo ████████████████████████████████████████ && echo. && echo 🌐 Port: 3100 && echo 📡 Status: Starting... && echo 🔗 URL: http://localhost:3100 && echo. && node online-frontend-server.js"

echo ⏳ انتظار تشغيل Frontend (5 ثوان)...
timeout /t 5 /nobreak >nul

:: Test Frontend
echo 🧪 اختبار Frontend...
curl -s http://localhost:3100/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Frontend قد يحتاج وقت إضافي للتشغيل
    set FRONTEND_STATUS=⚠️ قيد التشغيل
) else (
    echo ✅ Frontend يعمل بنجاح
    set FRONTEND_STATUS=✅ يعمل
)

echo.

:: Launch Browser
echo 🌐 فتح النظام في المتصفح...
start "" "http://localhost:3100"
timeout /t 2 /nobreak >nul

:: Also open API documentation
start "" "http://localhost:3000/api/health"
timeout /t 1 /nobreak >nul

echo.

:: Final Status Report
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🎉 WS TRANSFIR ONLINE SYSTEM - FULLY OPERATIONAL!      ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 📊 تقرير حالة النظام الشامل:
echo ================================
echo 🔧 Backend Server:        %BACKEND_STATUS%
echo 🎨 Frontend Server:       %FRONTEND_STATUS%
echo 🌐 Internet Connection:   %INTERNET_STATUS%
echo 💾 System Memory:         %RAM_GB% GB
echo 🏠 Local IP Address:      %LOCAL_IP%
echo 📡 External Access:       ✅ مفعل
echo 🛡️ Security Features:     ✅ مفعل
echo 📈 Monitoring:           ✅ مفعل
echo 🔄 Auto-Restart:         ✅ مفعل
echo.

echo 🌍 روابط الوصول الأساسية:
echo ============================
echo 🎨 الواجهة الرئيسية:      http://localhost:3100
echo 🔧 Backend API:           http://localhost:3000
echo 📊 فحص صحة النظام:       http://localhost:3000/api/health
echo 📈 حالة النظام:          http://localhost:3000/api/status
echo 🔐 نظام المصادقة:        http://localhost:3000/api/auth/login
echo 💸 إدارة التحويلات:       http://localhost:3000/api/transfers
echo.

if not "%LOCAL_IP%"=="localhost" (
    echo 🌐 روابط الوصول الخارجي:
    echo ===========================
    echo 🎨 الواجهة الرئيسية:      http://%LOCAL_IP%:3100
    echo 🔧 Backend API:           http://%LOCAL_IP%:3000
    echo 📊 فحص صحة النظام:       http://%LOCAL_IP%:3000/api/health
    echo 📱 للوصول من الهاتف:     http://%LOCAL_IP%:3100
    echo.
)

echo 🔐 بيانات الدخول التجريبية:
echo ============================
echo 👨‍💼 مدير النظام:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: admin123
echo    🎯 الصلاحيات: جميع الصلاحيات
echo.
echo 👤 مستخدم عادي:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: password123
echo    🎯 الصلاحيات: صلاحيات محدودة
echo.

echo 📋 الميزات المتاحة في النظام:
echo ===============================
echo ✅ نظام مصادقة متقدم مع JWT
echo ✅ إدارة شاملة للتحويلات المالية
echo ✅ لوحة تحكم تفاعلية وحديثة
echo ✅ إحصائيات وتقارير مفصلة
echo ✅ واجهة مستخدم متجاوبة
echo ✅ API RESTful متكامل
echo ✅ أمان متعدد الطبقات
echo ✅ مراقبة في الوقت الفعلي
echo ✅ دعم الوصول الخارجي
echo ✅ تسجيل شامل للعمليات
echo ✅ معالجة متقدمة للأخطاء
echo ✅ ضغط البيانات وتحسين الأداء
echo.

echo 💡 نصائح الاستخدام المتقدم:
echo =============================
echo 🔹 النظام يعمل في وضع الإنتاج الكامل
echo 🔹 يمكن الوصول إليه من أي جهاز في الشبكة
echo 🔹 جميع الميزات الأمنية مفعلة ومحسنة
echo 🔹 المراقبة والتسجيل يعملان تلقائياً
echo 🔹 النظام يدعم إعادة التشغيل التلقائي
echo 🔹 لإيقاف النظام: أغلق نوافذ الخوادم
echo 🔹 لإعادة التشغيل: شغل هذا الملف مرة أخرى
echo.

echo 🔧 إدارة وصيانة النظام:
echo =========================
echo 🔹 مراقبة الأداء: راقب نوافذ الخوادم
echo 🔹 فحص الحالة: http://localhost:3000/api/health
echo 🔹 عرض السجلات: تحقق من نوافذ الخوادم
echo 🔹 إعادة التشغيل: أغلق النوافذ وشغل الملف مرة أخرى
echo 🔹 التحديثات: تحقق من الموقع الرسمي
echo 🔹 النسخ الاحتياطي: انسخ مجلد المشروع بالكامل
echo.

echo 📞 الدعم الفني والمساعدة:
echo ===========================
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📱 الهاتف: +966 11 123 4567
echo 💬 الدردشة المباشرة: https://wstransfir.com/chat
echo 🌐 الموقع الرسمي: https://wstransfir.com
echo 📚 التوثيق الفني: https://docs.wstransfir.com
echo 🎥 الفيديوهات التعليمية: https://youtube.com/wstransfir
echo 📱 تطبيق الهاتف: متوفر على App Store و Google Play
echo.

echo 🏆 شكراً لاستخدام WS Transfir!
echo ===============================
echo 🎉 النظام الاحترافي الأونلاين جاهز للاستخدام بالكامل
echo 🚀 استمتع بتجربة التحويلات المالية المتقدمة
echo 💼 مناسب للاستخدام التجاري والشخصي
echo 🌟 تقييمك وملاحظاتك مهمة لنا
echo.

echo اضغط أي مفتاح للاستمرار أو أغلق النافذة...
pause >nul

:: Keep the window open for monitoring
echo.
echo 📊 وضع المراقبة المستمرة...
echo ============================
echo النظام يعمل الآن في الخلفية
echo يمكنك إغلاق هذه النافذة بأمان
echo أو تركها مفتوحة لمراقبة الحالة
echo.

:monitor_loop
timeout /t 30 /nobreak >nul
echo [%TIME%] النظام يعمل بشكل طبيعي...
goto monitor_loop
