{"name": "@nestjs/swagger", "version": "7.4.2", "description": "Nest - modern, fast, powerful node.js web framework (@swagger)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": "https://github.com/nestjs/swagger", "scripts": {"build": "tsc -p tsconfig.build.json", "format": "prettier \"lib/**/*.ts\" --write", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "test": "jest", "test:dev": "jest --watch", "test:e2e": "jest --config e2e/jest-e2e.json", "prerelease": "npm run build", "release": "release-it", "---manual-tests---": "", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --watch --debug"}, "dependencies": {"@microsoft/tsdoc": "^0.15.0", "@nestjs/mapped-types": "2.0.5", "js-yaml": "4.1.0", "lodash": "4.17.21", "path-to-regexp": "3.3.0", "swagger-ui-dist": "5.17.14"}, "devDependencies": {"@commitlint/cli": "19.5.0", "@commitlint/config-angular": "19.5.0", "@fastify/static": "7.0.4", "@nestjs/common": "10.4.2", "@nestjs/core": "10.4.2", "@nestjs/platform-express": "10.4.2", "@nestjs/platform-fastify": "10.4.2", "@types/jest": "29.5.13", "@types/js-yaml": "4.0.9", "@types/lodash": "4.17.7", "@types/node": "20.16.5", "@typescript-eslint/eslint-plugin": "8.6.0", "@typescript-eslint/parser": "8.6.0", "class-transformer": "0.5.1", "class-validator": "0.14.1", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.30.0", "express": "4.21.0", "husky": "9.1.6", "jest": "29.7.0", "lint-staged": "15.2.10", "openapi-types": "12.1.3", "prettier": "2.8.8", "reflect-metadata": "0.2.2", "release-it": "17.6.0", "supertest": "7.0.0", "swagger-parser": "10.0.3", "ts-jest": "29.2.5", "typescript": "5.1.6"}, "peerDependencies": {"@fastify/static": "^6.0.0 || ^7.0.0", "@nestjs/common": "^9.0.0 || ^10.0.0", "@nestjs/core": "^9.0.0 || ^10.0.0", "class-transformer": "*", "class-validator": "*", "reflect-metadata": "^0.1.12 || ^0.2.0"}, "peerDependenciesMeta": {"@fastify/static": {"optional": true}, "class-transformer": {"optional": true}, "class-validator": {"optional": true}}, "lint-staged": {"*.ts": ["prettier --write", "git add -f"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -c .commitlintrc.json -E HUSKY_GIT_PARAMS"}}}