import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Pagination,
  Alert,
  Fab,
} from '@mui/material';
import {
  Add,
  Search,
  FilterList,
  MoreVert,
  Visibility,
  Receipt,
  Refresh,
  Download,
  Send,
  TrendingUp,
  AccountBalance,
  Schedule,
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '../../components/Layout';

interface Transfer {
  id: string;
  referenceNumber: string;
  amount: string;
  currency: string;
  receiverName: string;
  receiverCountry: string;
  status: string;
  createdAt: string;
  estimatedDelivery: string;
  fees: string;
}

interface TransferStats {
  totalTransfers: number;
  totalAmount: string;
  pendingTransfers: number;
  completedTransfers: number;
}

const TransfersPage: React.FC = () => {
  const router = useRouter();
  const [transfers, setTransfers] = useState<Transfer[]>([]);
  const [stats, setStats] = useState<TransferStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTransfer, setSelectedTransfer] = useState<Transfer | null>(null);

  useEffect(() => {
    fetchTransfers();
    fetchStats();
  }, [page, searchTerm, statusFilter]);

  const fetchTransfers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
      });

      const response = await fetch(`/api/transfers?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTransfers(data.data || []);
        setTotalPages(data.totalPages || 1);
      } else {
        setError('فشل في تحميل التحويلات');
        // Mock data for development
        setTransfers([
          {
            id: '1',
            referenceNumber: 'WS20241225001',
            amount: '1,500.00',
            currency: 'SAR',
            receiverName: 'أحمد محمد',
            receiverCountry: 'مصر',
            status: 'completed',
            createdAt: '2024-12-25T10:30:00Z',
            estimatedDelivery: '2024-12-25T14:30:00Z',
            fees: '15.00',
          },
          {
            id: '2',
            referenceNumber: 'WS20241224002',
            amount: '750.00',
            currency: 'USD',
            receiverName: 'فاطمة علي',
            receiverCountry: 'الأردن',
            status: 'pending',
            createdAt: '2024-12-24T15:45:00Z',
            estimatedDelivery: '2024-12-26T15:45:00Z',
            fees: '12.50',
          },
          {
            id: '3',
            referenceNumber: 'WS20241223003',
            amount: '2,200.00',
            currency: 'SAR',
            receiverName: 'محمد حسن',
            receiverCountry: 'لبنان',
            status: 'processing',
            createdAt: '2024-12-23T09:15:00Z',
            estimatedDelivery: '2024-12-25T09:15:00Z',
            fees: '22.00',
          },
        ]);
        setTotalPages(5);
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/transfers/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        // Mock stats for development
        setStats({
          totalTransfers: 24,
          totalAmount: '45,230.50',
          pendingTransfers: 3,
          completedTransfers: 21,
        });
      }
    } catch (err) {
      console.error('Failed to fetch stats:', err);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, transfer: Transfer) => {
    setAnchorEl(event.currentTarget);
    setSelectedTransfer(transfer);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTransfer(null);
  };

  const handleViewTransfer = (transferId: string) => {
    router.push(`/transfers/${transferId}`);
    handleMenuClose();
  };

  const handleDownloadReceipt = (transferId: string) => {
    // Implement receipt download
    console.log('Download receipt for:', transferId);
    handleMenuClose();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'processing':
        return 'info';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'معلق';
      case 'processing':
        return 'قيد المعالجة';
      case 'failed':
        return 'فاشل';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const handleRefresh = () => {
    fetchTransfers();
    fetchStats();
  };

  return (
    <>
      <Head>
        <title>التحويلات - WS Transfir</title>
        <meta name="description" content="إدارة التحويلات المالية في نظام WS Transfir" />
      </Head>

      <Layout>
        <Box sx={{ p: 3 }}>
          {/* Header */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                التحويلات المالية
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                إدارة ومتابعة جميع تحويلاتك المالية
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => router.push('/transfers/new')}
              sx={{
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                px: 3,
              }}
            >
              تحويل جديد
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Stats Cards */}
          {stats && (
            <Grid container spacing={3} mb={4}>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h4" component="div" fontWeight="bold">
                          {stats.totalTransfers}
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.8 }}>
                          إجمالي التحويلات
                        </Typography>
                      </Box>
                      <Send sx={{ fontSize: 40, opacity: 0.8 }} />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h4" component="div" fontWeight="bold">
                          {stats.totalAmount} ر.س
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.8 }}>
                          إجمالي المبلغ
                        </Typography>
                      </Box>
                      <TrendingUp sx={{ fontSize: 40, opacity: 0.8 }} />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h4" component="div" fontWeight="bold">
                          {stats.pendingTransfers}
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.8 }}>
                          تحويلات معلقة
                        </Typography>
                      </Box>
                      <Schedule sx={{ fontSize: 40, opacity: 0.8 }} />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', color: 'white' }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h4" component="div" fontWeight="bold">
                          {stats.completedTransfers}
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.8 }}>
                          تحويلات مكتملة
                        </Typography>
                      </Box>
                      <AccountBalance sx={{ fontSize: 40, opacity: 0.8 }} />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Filters */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    placeholder="البحث برقم التحويل أو اسم المستقبل"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search />
                        </InputAdornment>
                      ),
                    }}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>حالة التحويل</InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      label="حالة التحويل"
                    >
                      <MenuItem value="">جميع الحالات</MenuItem>
                      <MenuItem value="completed">مكتمل</MenuItem>
                      <MenuItem value="pending">معلق</MenuItem>
                      <MenuItem value="processing">قيد المعالجة</MenuItem>
                      <MenuItem value="failed">فاشل</MenuItem>
                      <MenuItem value="cancelled">ملغي</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={5}>
                  <Box display="flex" gap={1} justifyContent="flex-end">
                    <Button
                      variant="outlined"
                      startIcon={<Refresh />}
                      onClick={handleRefresh}
                      size="small"
                    >
                      تحديث
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Download />}
                      size="small"
                    >
                      تصدير
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<FilterList />}
                      size="small"
                    >
                      فلاتر متقدمة
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Transfers Table */}
          <Card>
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>الرقم المرجعي</TableCell>
                      <TableCell>المستقبل</TableCell>
                      <TableCell>المبلغ</TableCell>
                      <TableCell>الحالة</TableCell>
                      <TableCell>تاريخ الإنشاء</TableCell>
                      <TableCell>التسليم المتوقع</TableCell>
                      <TableCell align="center">الإجراءات</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={7} align="center">
                          <Typography>جاري التحميل...</Typography>
                        </TableCell>
                      </TableRow>
                    ) : transfers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} align="center">
                          <Box py={4}>
                            <Typography variant="h6" color="text.secondary" gutterBottom>
                              لا توجد تحويلات
                            </Typography>
                            <Typography variant="body2" color="text.secondary" mb={2}>
                              لم تقم بأي تحويلات مالية حتى الآن
                            </Typography>
                            <Button
                              variant="contained"
                              startIcon={<Add />}
                              onClick={() => router.push('/transfers/new')}
                            >
                              إنشاء أول تحويل
                            </Button>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ) : (
                      transfers.map((transfer) => (
                        <TableRow key={transfer.id} hover>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {transfer.referenceNumber}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {transfer.receiverName}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {transfer.receiverCountry}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {transfer.amount} {transfer.currency}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                رسوم: {transfer.fees} {transfer.currency}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getStatusText(transfer.status)}
                              color={getStatusColor(transfer.status) as any}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDate(transfer.createdAt)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary">
                              {formatDate(transfer.estimatedDelivery)}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuClick(e, transfer)}
                            >
                              <MoreVert />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={3}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Action Menu */}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => selectedTransfer && handleViewTransfer(selectedTransfer.id)}>
              <Visibility sx={{ mr: 1 }} />
              عرض التفاصيل
            </MenuItem>
            <MenuItem onClick={() => selectedTransfer && handleDownloadReceipt(selectedTransfer.id)}>
              <Receipt sx={{ mr: 1 }} />
              تحميل الإيصال
            </MenuItem>
          </Menu>

          {/* Floating Action Button */}
          <Fab
            color="primary"
            aria-label="add transfer"
            onClick={() => router.push('/transfers/new')}
            sx={{
              position: 'fixed',
              bottom: 16,
              right: 16,
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            }}
          >
            <Add />
          </Fab>
        </Box>
      </Layout>
    </>
  );
};

export default TransfersPage;
