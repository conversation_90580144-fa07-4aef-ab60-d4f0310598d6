import { Module } from '@nestjs/common';
import { WalletsController } from './wallets.controller';
import { WalletsService } from './wallets.service';
import { BalanceService } from './services/balance.service';
import { TransactionService } from './services/transaction.service';
import { TopUpService } from './services/top-up.service';
import { WithdrawalService } from './services/withdrawal.service';

@Module({
  controllers: [WalletsController],
  providers: [
    WalletsService,
    BalanceService,
    TransactionService,
    TopUpService,
    WithdrawalService,
  ],
  exports: [WalletsService, BalanceService],
})
export class WalletsModule {}
