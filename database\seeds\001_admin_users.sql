-- Seed: Admin Users
-- Description: إنشاء المستخدمين الإداريين الأساسيين
-- Version: 001
-- Created: 2024-01-15

-- Insert Super Admin User
INSERT INTO users (
    id, email, phone, password_hash, first_name, last_name, 
    date_of_birth, nationality, gender, role, is_active, is_verified,
    kyc_level, kyc_status, kyc_approved_at, country, preferred_currency,
    language, created_by
) VALUES (
    'user_super_admin_001',
    '<EMAIL>',
    '+966501234567',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', -- password: SuperAdmin@123
    'Super',
    'Administrator',
    '1985-01-01',
    'SAU',
    'male',
    'super_admin',
    true,
    true,
    3,
    'approved',
    CURRENT_TIMESTAMP,
    'SAU',
    'SAR',
    'ar',
    'system'
) ON CONFLICT (id) DO NOTHING;

-- Insert Admin User
INSERT INTO users (
    id, email, phone, password_hash, first_name, last_name,
    date_of_birth, nationality, gender, role, is_active, is_verified,
    kyc_level, kyc_status, kyc_approved_at, country, preferred_currency,
    language, created_by
) VALUES (
    'user_admin_001',
    '<EMAIL>',
    '+966501234568',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', -- password: Admin@123
    'System',
    'Admin',
    '1988-05-15',
    'SAU',
    'male',
    'admin',
    true,
    true,
    3,
    'approved',
    CURRENT_TIMESTAMP,
    'SAU',
    'SAR',
    'ar',
    'user_super_admin_001'
) ON CONFLICT (id) DO NOTHING;

-- Insert Agent Manager
INSERT INTO users (
    id, email, phone, password_hash, first_name, last_name,
    date_of_birth, nationality, gender, role, is_active, is_verified,
    kyc_level, kyc_status, kyc_approved_at, country, preferred_currency,
    language, created_by
) VALUES (
    'user_agent_manager_001',
    '<EMAIL>',
    '+966501234569',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', -- password: AgentManager@123
    'Agent',
    'Manager',
    '1990-03-20',
    'SAU',
    'female',
    'agent_manager',
    true,
    true,
    2,
    'approved',
    CURRENT_TIMESTAMP,
    'SAU',
    'SAR',
    'ar',
    'user_admin_001'
) ON CONFLICT (id) DO NOTHING;

-- Insert Sample Agent
INSERT INTO users (
    id, email, phone, password_hash, first_name, last_name,
    date_of_birth, nationality, gender, role, is_active, is_verified,
    kyc_level, kyc_status, kyc_approved_at, country, preferred_currency,
    language, occupation, created_by
) VALUES (
    'user_agent_001',
    '<EMAIL>',
    '+966501234570',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', -- password: Agent@123
    'Ahmed',
    'Al-Rashid',
    '1992-07-10',
    'SAU',
    'male',
    'agent',
    true,
    true,
    2,
    'approved',
    CURRENT_TIMESTAMP,
    'SAU',
    'SAR',
    'ar',
    'Financial Agent',
    'user_agent_manager_001'
) ON CONFLICT (id) DO NOTHING;

-- Insert Sample Regular Users
INSERT INTO users (
    id, email, phone, password_hash, first_name, last_name,
    date_of_birth, nationality, gender, role, is_active, is_verified,
    kyc_level, kyc_status, country, preferred_currency, language, created_by
) VALUES 
(
    'user_customer_001',
    '<EMAIL>',
    '+966501234571',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', -- password: Customer@123
    'Mohammed',
    'Al-Fahad',
    '1995-12-25',
    'SAU',
    'male',
    'user',
    true,
    true,
    1,
    'approved',
    'SAU',
    'SAR',
    'ar',
    'user_agent_001'
),
(
    'user_customer_002',
    '<EMAIL>',
    '+966501234572',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', -- password: Customer@123
    'Fatima',
    'Al-Zahra',
    '1993-08-14',
    'SAU',
    'female',
    'user',
    true,
    true,
    1,
    'approved',
    'SAU',
    'SAR',
    'ar',
    'user_agent_001'
),
(
    'user_customer_003',
    '<EMAIL>',
    '+966501234573',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', -- password: Customer@123
    'Omar',
    'Al-Mansouri',
    '1988-04-30',
    'AE',
    'male',
    'user',
    true,
    true,
    2,
    'approved',
    'AE',
    'AED',
    'ar',
    'user_agent_001'
) ON CONFLICT (id) DO NOTHING;

-- Create default wallets for users
INSERT INTO wallets (
    user_id, currency, type, balance, available_balance, 
    is_default, status, name, created_by
) VALUES 
-- Super Admin Wallet
(
    'user_super_admin_001',
    'SAR',
    'primary',
    1000000.00,
    1000000.00,
    true,
    'active',
    'Primary SAR Wallet',
    'system'
),
-- Admin Wallet
(
    'user_admin_001',
    'SAR',
    'primary',
    500000.00,
    500000.00,
    true,
    'active',
    'Primary SAR Wallet',
    'user_super_admin_001'
),
-- Agent Manager Wallet
(
    'user_agent_manager_001',
    'SAR',
    'primary',
    100000.00,
    100000.00,
    true,
    'active',
    'Primary SAR Wallet',
    'user_admin_001'
),
-- Agent Wallet
(
    'user_agent_001',
    'SAR',
    'primary',
    50000.00,
    50000.00,
    true,
    'active',
    'Primary SAR Wallet',
    'user_agent_manager_001'
),
-- Customer Wallets
(
    'user_customer_001',
    'SAR',
    'primary',
    10000.00,
    10000.00,
    true,
    'active',
    'Primary SAR Wallet',
    'user_agent_001'
),
(
    'user_customer_002',
    'SAR',
    'primary',
    15000.00,
    15000.00,
    true,
    'active',
    'Primary SAR Wallet',
    'user_agent_001'
),
(
    'user_customer_003',
    'AED',
    'primary',
    25000.00,
    25000.00,
    true,
    'active',
    'Primary AED Wallet',
    'user_agent_001'
) ON CONFLICT (user_id, currency, type) DO NOTHING;

-- Add some sample transactions
INSERT INTO transactions (
    sender_id, receiver_id, agent_id, type, amount, currency,
    amount_in_base_currency, fee_amount, status, description,
    risk_score, risk_level, fraud_score, created_by
) VALUES 
(
    'user_customer_001',
    'user_customer_002',
    'user_agent_001',
    'transfer',
    1000.00,
    'SAR',
    1000.00,
    10.00,
    'completed',
    'Transfer from Mohammed to Fatima',
    0.2,
    'low',
    0.1,
    'user_customer_001'
),
(
    'user_customer_002',
    'user_customer_003',
    'user_agent_001',
    'transfer',
    500.00,
    'SAR',
    500.00,
    5.00,
    'completed',
    'Transfer from Fatima to Omar',
    0.3,
    'low',
    0.15,
    'user_customer_002'
),
(
    'user_customer_003',
    'user_customer_001',
    'user_agent_001',
    'transfer',
    750.00,
    'SAR',
    750.00,
    7.50,
    'pending',
    'Transfer from Omar to Mohammed',
    0.4,
    'medium',
    0.25,
    'user_customer_003'
) ON CONFLICT (id) DO NOTHING;

-- Update wallet balances based on transactions (simplified)
UPDATE wallets SET 
    balance = balance - 1010.00,
    available_balance = available_balance - 1010.00
WHERE user_id = 'user_customer_001' AND currency = 'SAR';

UPDATE wallets SET 
    balance = balance + 1000.00 - 505.00,
    available_balance = available_balance + 1000.00 - 505.00
WHERE user_id = 'user_customer_002' AND currency = 'SAR';

-- Add some metadata to users
UPDATE users SET metadata = jsonb_build_object(
    'registration_source', 'admin_seed',
    'initial_setup', true,
    'test_account', true
) WHERE id LIKE 'user_%';

-- Add notification preferences
UPDATE users SET notification_preferences = jsonb_build_object(
    'email', true,
    'sms', true,
    'push', true,
    'marketing', false,
    'security_alerts', true
) WHERE id LIKE 'user_%';

-- Add some tags
UPDATE users SET tags = ARRAY['seed_data', 'initial_setup'] WHERE id LIKE 'user_%';
UPDATE users SET tags = array_append(tags, 'admin') WHERE role IN ('super_admin', 'admin');
UPDATE users SET tags = array_append(tags, 'agent') WHERE role IN ('agent', 'agent_manager');
UPDATE users SET tags = array_append(tags, 'customer') WHERE role = 'user';

-- Log the seeding
INSERT INTO schema_migrations (version, name, file_path, checksum, execution_time_ms, success)
VALUES (
    'seed_001',
    'Admin Users Seed',
    'database/seeds/001_admin_users.sql',
    'admin_users_seed_checksum',
    0,
    true
) ON CONFLICT (version) DO NOTHING;
