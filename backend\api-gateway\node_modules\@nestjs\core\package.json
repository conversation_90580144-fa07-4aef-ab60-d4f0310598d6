{"name": "@nestjs/core", "version": "10.4.20", "description": "Nest - modern, fast, powerful node.js web framework (@core)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "homepage": "https://nestjs.com", "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "repository": {"type": "git", "url": "https://github.com/nestjs/nest.git", "directory": "packages/core"}, "publishConfig": {"access": "public"}, "scripts": {"postinstall": "opencollective || exit 0"}, "collective": {"type": "opencollective", "url": "https://opencollective.com/nest", "donation": {"text": "Become a partner:"}}, "dependencies": {"@nuxtjs/opencollective": "0.3.2", "fast-safe-stringify": "2.1.1", "iterare": "1.2.1", "path-to-regexp": "3.3.0", "tslib": "2.8.1", "uid": "2.0.2"}, "devDependencies": {"@nestjs/common": "10.4.20"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/websockets": "^10.0.0", "reflect-metadata": "^0.1.12 || ^0.2.0", "rxjs": "^7.1.0"}, "peerDependenciesMeta": {"@nestjs/websockets": {"optional": true}, "@nestjs/microservices": {"optional": true}, "@nestjs/platform-express": {"optional": true}}}