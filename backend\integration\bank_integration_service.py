"""
Bank Integration Service
=======================
خدمة التكامل البنكي المتقدمة
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from decimal import Decimal
import json
import uuid
import xml.etree.ElementTree as ET
from xml.dom import minidom
import hashlib
import hmac
import base64

try:
    import asyncpg
except ImportError:
    asyncpg = None

try:
    import aiohttp
except ImportError:
    aiohttp = None

try:
    from ..shared.database.connection import DatabaseConnection
except ImportError:
    # Fallback for testing or standalone usage
    class DatabaseConnection:
        def __init__(self):
            pass

        async def get_connection(self):
            return None

logger = logging.getLogger(__name__)


class BankType(Enum):
    """أنواع البنوك"""
    COMMERCIAL = "commercial"
    ISLAMIC = "islamic"
    INVESTMENT = "investment"
    CENTRAL = "central"


class MessageType(Enum):
    """أنواع الرسائل البنكية"""
    PAYMENT_INITIATION = "pacs.008"
    PAYMENT_STATUS = "pacs.002"
    ACCOUNT_STATEMENT = "camt.053"
    BALANCE_REPORT = "camt.052"
    CUSTOMER_CREDIT_TRANSFER = "pain.001"
    CUSTOMER_PAYMENT_STATUS = "pain.002"


class TransactionStatus(Enum):
    """حالات المعاملات البنكية"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


@dataclass
class BankAccount:
    """حساب بنكي"""
    account_number: str
    iban: str
    bank_code: str
    bank_name: str
    account_holder: str
    currency: str
    account_type: str
    is_active: bool = True


@dataclass
class BankTransferRequest:
    """طلب تحويل بنكي"""
    from_account: str
    to_account: str
    amount: Decimal
    currency: str
    reference: str
    description: str
    beneficiary_name: str
    beneficiary_bank: str
    purpose_code: str = None
    metadata: Dict[str, Any] = None


@dataclass
class BankTransferResponse:
    """استجابة التحويل البنكي"""
    transfer_id: str
    status: TransactionStatus
    bank_reference: str
    amount: Decimal
    currency: str
    fees: Decimal = Decimal('0.00')
    exchange_rate: Decimal = None
    estimated_completion: datetime = None
    error_code: str = None
    error_message: str = None
    created_at: datetime = None


@dataclass
class AccountBalance:
    """رصيد الحساب"""
    account_number: str
    available_balance: Decimal
    current_balance: Decimal
    pending_balance: Decimal
    currency: str
    last_updated: datetime
    bank_reference: str = None


class BankIntegrationService:
    """خدمة التكامل البنكي المتقدمة"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection

        # Check dependencies
        if asyncpg is None:
            logger.warning("asyncpg not available - database operations will be mocked")
        if aiohttp is None:
            logger.warning("aiohttp not available - HTTP operations will be mocked")
        
        # Bank configurations
        self.bank_configs = {
            "RIBLSARI": {  # الرياض
                "name": "Riyad Bank",
                "type": BankType.COMMERCIAL,
                "api_endpoint": "https://api.riyadbank.com/v1/",
                "api_key": "your_riyad_api_key",
                "certificate_path": "/certs/riyad_cert.pem",
                "supported_currencies": ["SAR", "USD", "EUR"],
                "supported_messages": [MessageType.PAYMENT_INITIATION, MessageType.ACCOUNT_STATEMENT],
                "fees": {"domestic": Decimal('5.00'), "international": Decimal('25.00')},
                "limits": {"daily": Decimal('1000000.00'), "single": Decimal('100000.00')}
            },
            "NCBKSAJE": {  # الأهلي
                "name": "National Commercial Bank",
                "type": BankType.COMMERCIAL,
                "api_endpoint": "https://api.alahli.com/v1/",
                "api_key": "your_ncb_api_key",
                "certificate_path": "/certs/ncb_cert.pem",
                "supported_currencies": ["SAR", "USD", "EUR", "GBP"],
                "supported_messages": [MessageType.PAYMENT_INITIATION, MessageType.BALANCE_REPORT],
                "fees": {"domestic": Decimal('3.00'), "international": Decimal('20.00')},
                "limits": {"daily": Decimal('2000000.00'), "single": Decimal('200000.00')}
            },
            "ALBISARI": {  # البلاد
                "name": "Albilad Bank",
                "type": BankType.ISLAMIC,
                "api_endpoint": "https://api.albilad.com/v1/",
                "api_key": "your_albilad_api_key",
                "certificate_path": "/certs/albilad_cert.pem",
                "supported_currencies": ["SAR", "USD"],
                "supported_messages": [MessageType.CUSTOMER_CREDIT_TRANSFER],
                "fees": {"domestic": Decimal('2.00'), "international": Decimal('15.00')},
                "limits": {"daily": Decimal('500000.00'), "single": Decimal('50000.00')}
            },
            "SWIFT_GENERIC": {  # Generic SWIFT
                "name": "SWIFT Network",
                "type": BankType.CENTRAL,
                "api_endpoint": "https://api.swift.com/v1/",
                "api_key": "your_swift_api_key",
                "certificate_path": "/certs/swift_cert.pem",
                "supported_currencies": ["USD", "EUR", "GBP", "JPY", "CHF"],
                "supported_messages": list(MessageType),
                "fees": {"international": Decimal('50.00')},
                "limits": {"daily": Decimal('********.00'), "single": Decimal('1000000.00')}
            }
        }
        
        # ISO 20022 message templates
        self.iso20022_templates = {}
        self._initialize_iso20022_templates()
        
        # Statistics
        self.transfers_processed = 0
        self.transfers_failed = 0
        self.total_volume = Decimal('0.00')
        
        # Session management
        self.bank_sessions = {}
    
    def _initialize_iso20022_templates(self):
        """تهيئة قوالب ISO 20022"""
        # Customer Credit Transfer Initiation (pain.001)
        self.iso20022_templates[MessageType.CUSTOMER_CREDIT_TRANSFER] = """
        <Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03">
            <CstmrCdtTrfInitn>
                <GrpHdr>
                    <MsgId>{message_id}</MsgId>
                    <CreDtTm>{creation_datetime}</CreDtTm>
                    <NbOfTxs>{number_of_transactions}</NbOfTxs>
                    <CtrlSum>{control_sum}</CtrlSum>
                    <InitgPty>
                        <Nm>{initiating_party_name}</Nm>
                        <Id>
                            <OrgId>
                                <Othr>
                                    <Id>{initiating_party_id}</Id>
                                </Othr>
                            </OrgId>
                        </Id>
                    </InitgPty>
                </GrpHdr>
                <PmtInf>
                    <PmtInfId>{payment_info_id}</PmtInfId>
                    <PmtMtd>TRF</PmtMtd>
                    <ReqdExctnDt>{requested_execution_date}</ReqdExctnDt>
                    <Dbtr>
                        <Nm>{debtor_name}</Nm>
                    </Dbtr>
                    <DbtrAcct>
                        <Id>
                            <IBAN>{debtor_iban}</IBAN>
                        </Id>
                    </DbtrAcct>
                    <DbtrAgt>
                        <FinInstnId>
                            <BIC>{debtor_agent_bic}</BIC>
                        </FinInstnId>
                    </DbtrAgt>
                    <CdtTrfTxInf>
                        <PmtId>
                            <EndToEndId>{end_to_end_id}</EndToEndId>
                        </PmtId>
                        <Amt>
                            <InstdAmt Ccy="{currency}">{amount}</InstdAmt>
                        </Amt>
                        <Cdtr>
                            <Nm>{creditor_name}</Nm>
                        </Cdtr>
                        <CdtrAcct>
                            <Id>
                                <IBAN>{creditor_iban}</IBAN>
                            </Id>
                        </CdtrAcct>
                        <CdtrAgt>
                            <FinInstnId>
                                <BIC>{creditor_agent_bic}</BIC>
                            </FinInstnId>
                        </CdtrAgt>
                        <RmtInf>
                            <Ustrd>{remittance_information}</Ustrd>
                        </RmtInf>
                    </CdtTrfTxInf>
                </PmtInf>
            </CstmrCdtTrfInitn>
        </Document>
        """
        
        # Payment Status Report (pacs.002)
        self.iso20022_templates[MessageType.PAYMENT_STATUS] = """
        <Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.03">
            <FIToFIPmtStsRpt>
                <GrpHdr>
                    <MsgId>{message_id}</MsgId>
                    <CreDtTm>{creation_datetime}</CreDtTm>
                </GrpHdr>
                <TxInfAndSts>
                    <OrgnlInstrId>{original_instruction_id}</OrgnlInstrId>
                    <OrgnlEndToEndId>{original_end_to_end_id}</OrgnlEndToEndId>
                    <TxSts>{transaction_status}</TxSts>
                    <StsRsnInf>
                        <Rsn>
                            <Cd>{reason_code}</Cd>
                        </Rsn>
                        <AddtlInf>{additional_information}</AddtlInf>
                    </StsRsnInf>
                </TxInfAndSts>
            </FIToFIPmtStsRpt>
        </Document>
        """
    
    async def initiate_bank_transfer(self, request: BankTransferRequest) -> BankTransferResponse:
        """بدء تحويل بنكي"""
        try:
            logger.info(f"🏦 Initiating bank transfer: {request.amount} {request.currency}")
            
            # Validate transfer request
            await self._validate_transfer_request(request)
            
            # Generate transfer ID
            transfer_id = f"bt_{uuid.uuid4().hex[:12]}"
            
            # Get bank configuration
            from_bank_code = await self._get_account_bank_code(request.from_account)
            bank_config = self.bank_configs.get(from_bank_code)
            
            if not bank_config:
                raise ValueError(f"Bank configuration not found for {from_bank_code}")
            
            # Check transfer limits
            await self._check_transfer_limits(request, bank_config)
            
            # Calculate fees
            fees = await self._calculate_transfer_fees(request, bank_config)
            
            # Store transfer record
            await self._store_transfer_record(transfer_id, request, fees)
            
            # Create ISO 20022 message
            iso_message = await self._create_iso20022_message(transfer_id, request)
            
            # Send to bank
            bank_response = await self._send_to_bank(bank_config, iso_message, transfer_id)
            
            # Process bank response
            result = await self._process_bank_response(transfer_id, bank_response, fees)
            
            # Update statistics
            if result.status in [TransactionStatus.COMPLETED, TransactionStatus.PROCESSING]:
                self.transfers_processed += 1
                if result.status == TransactionStatus.COMPLETED:
                    self.total_volume += request.amount
            else:
                self.transfers_failed += 1
            
            logger.info(f"✅ Bank transfer initiated: {transfer_id} - Status: {result.status.value}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Bank transfer failed: {e}")
            self.transfers_failed += 1
            
            return BankTransferResponse(
                transfer_id=transfer_id if 'transfer_id' in locals() else None,
                status=TransactionStatus.FAILED,
                bank_reference="",
                amount=request.amount,
                currency=request.currency,
                error_message=str(e),
                created_at=datetime.now()
            )
    
    async def get_account_balance(self, account_number: str, bank_code: str = None) -> AccountBalance:
        """الحصول على رصيد الحساب"""
        try:
            logger.info(f"💰 Getting account balance for: {account_number}")
            
            # Get bank configuration
            if not bank_code:
                bank_code = await self._get_account_bank_code(account_number)
            
            bank_config = self.bank_configs.get(bank_code)
            if not bank_config:
                raise ValueError(f"Bank configuration not found for {bank_code}")
            
            # Create balance inquiry message
            message_id = f"bal_{uuid.uuid4().hex[:8]}"
            
            # Send balance inquiry to bank
            balance_response = await self._send_balance_inquiry(bank_config, account_number, message_id)
            
            # Parse balance response
            balance = await self._parse_balance_response(balance_response, account_number)
            
            # Store balance record
            await self._store_balance_record(balance)
            
            logger.info(f"✅ Account balance retrieved: {balance.available_balance} {balance.currency}")
            return balance
            
        except Exception as e:
            logger.error(f"❌ Failed to get account balance: {e}")
            raise
    
    async def get_account_statement(
        self, 
        account_number: str, 
        start_date: datetime, 
        end_date: datetime,
        bank_code: str = None
    ) -> List[Dict[str, Any]]:
        """الحصول على كشف الحساب"""
        try:
            logger.info(f"📄 Getting account statement for: {account_number}")
            
            # Get bank configuration
            if not bank_code:
                bank_code = await self._get_account_bank_code(account_number)
            
            bank_config = self.bank_configs.get(bank_code)
            if not bank_config:
                raise ValueError(f"Bank configuration not found for {bank_code}")
            
            # Create statement request message
            message_id = f"stmt_{uuid.uuid4().hex[:8]}"
            
            # Send statement request to bank
            statement_response = await self._send_statement_request(
                bank_config, account_number, start_date, end_date, message_id
            )
            
            # Parse statement response
            transactions = await self._parse_statement_response(statement_response)
            
            # Store statement records
            await self._store_statement_records(account_number, transactions)
            
            logger.info(f"✅ Account statement retrieved: {len(transactions)} transactions")
            return transactions
            
        except Exception as e:
            logger.error(f"❌ Failed to get account statement: {e}")
            raise
    
    async def get_transfer_status(self, transfer_id: str) -> Optional[BankTransferResponse]:
        """الحصول على حالة التحويل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM bank_transfers WHERE id = $1
                """
                
                row = await conn.fetchrow(query, transfer_id)
                
                if row:
                    return BankTransferResponse(
                        transfer_id=row['id'],
                        status=TransactionStatus(row['status']),
                        bank_reference=row['bank_reference'],
                        amount=row['amount'],
                        currency=row['currency'],
                        fees=row['fees'],
                        exchange_rate=row['exchange_rate'],
                        estimated_completion=row['estimated_completion'],
                        error_code=row['error_code'],
                        error_message=row['error_message'],
                        created_at=row['created_at']
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get transfer status: {e}")
            return None
    
    async def register_bank_account(self, account: BankAccount) -> bool:
        """تسجيل حساب بنكي"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO bank_accounts (
                        account_number, iban, bank_code, bank_name, account_holder,
                        currency, account_type, is_active
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ON CONFLICT (account_number, bank_code) DO UPDATE SET
                        iban = EXCLUDED.iban,
                        bank_name = EXCLUDED.bank_name,
                        account_holder = EXCLUDED.account_holder,
                        currency = EXCLUDED.currency,
                        account_type = EXCLUDED.account_type,
                        is_active = EXCLUDED.is_active,
                        updated_at = CURRENT_TIMESTAMP
                """
                
                await conn.execute(
                    query,
                    account.account_number,
                    account.iban,
                    account.bank_code,
                    account.bank_name,
                    account.account_holder,
                    account.currency,
                    account.account_type,
                    account.is_active
                )
                
                logger.info(f"✅ Bank account registered: {account.account_number}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to register bank account: {e}")
            return False
    
    async def get_supported_banks(self) -> List[Dict[str, Any]]:
        """الحصول على البنوك المدعومة"""
        return [
            {
                "bank_code": code,
                "bank_name": config["name"],
                "bank_type": config["type"].value,
                "supported_currencies": config["supported_currencies"],
                "supported_messages": [msg.value for msg in config["supported_messages"]],
                "fees": {
                    "domestic": float(config["fees"].get("domestic", 0)),
                    "international": float(config["fees"].get("international", 0))
                },
                "limits": {
                    "daily": float(config["limits"].get("daily", 0)),
                    "single": float(config["limits"].get("single", 0))
                }
            }
            for code, config in self.bank_configs.items()
        ]
    
    async def validate_iban(self, iban: str) -> Dict[str, Any]:
        """التحقق من صحة IBAN"""
        try:
            # Remove spaces and convert to uppercase
            iban = iban.replace(" ", "").upper()
            
            # Check length (Saudi IBAN is 24 characters)
            if len(iban) != 24:
                return {"valid": False, "error": "Invalid IBAN length"}
            
            # Check country code
            if not iban.startswith("SA"):
                return {"valid": False, "error": "Not a Saudi IBAN"}
            
            # Check format (SA + 2 digits + 18 alphanumeric)
            if not iban[2:4].isdigit():
                return {"valid": False, "error": "Invalid check digits"}
            
            # Calculate check digits
            rearranged = iban[4:] + iban[:4]
            numeric_string = ""
            
            for char in rearranged:
                if char.isdigit():
                    numeric_string += char
                else:
                    numeric_string += str(ord(char) - ord('A') + 10)
            
            remainder = int(numeric_string) % 97
            
            if remainder != 1:
                return {"valid": False, "error": "Invalid check digits"}
            
            # Extract bank code
            bank_code = iban[4:6]
            
            return {
                "valid": True,
                "iban": iban,
                "country_code": "SA",
                "check_digits": iban[2:4],
                "bank_code": bank_code,
                "account_number": iban[6:]
            }
            
        except Exception as e:
            return {"valid": False, "error": str(e)}
    
    async def get_exchange_rate(self, from_currency: str, to_currency: str) -> Decimal:
        """الحصول على سعر الصرف"""
        try:
            # In production, integrate with real exchange rate API
            # For now, return mock rates
            rates = {
                ("SAR", "USD"): Decimal('0.2667'),
                ("USD", "SAR"): Decimal('3.75'),
                ("SAR", "EUR"): Decimal('0.2400'),
                ("EUR", "SAR"): Decimal('4.1667'),
                ("USD", "EUR"): Decimal('0.90'),
                ("EUR", "USD"): Decimal('1.11')
            }
            
            rate = rates.get((from_currency, to_currency))
            if rate:
                return rate
            
            # If direct rate not found, try reverse
            reverse_rate = rates.get((to_currency, from_currency))
            if reverse_rate:
                return Decimal('1.0') / reverse_rate
            
            # Default to 1.0 for same currency
            if from_currency == to_currency:
                return Decimal('1.0')
            
            raise ValueError(f"Exchange rate not available for {from_currency} to {to_currency}")
            
        except Exception as e:
            logger.error(f"❌ Failed to get exchange rate: {e}")
            raise
    
    # Helper methods
    async def _validate_transfer_request(self, request: BankTransferRequest):
        """التحقق من صحة طلب التحويل"""
        if request.amount <= 0:
            raise ValueError("Transfer amount must be positive")
        
        if not request.from_account or not request.to_account:
            raise ValueError("From and to accounts are required")
        
        if request.from_account == request.to_account:
            raise ValueError("Cannot transfer to the same account")
        
        if not request.currency:
            raise ValueError("Currency is required")
        
        # Validate IBAN format if provided
        if request.to_account.startswith("SA"):
            iban_validation = await self.validate_iban(request.to_account)
            if not iban_validation["valid"]:
                raise ValueError(f"Invalid IBAN: {iban_validation['error']}")
    
    async def _get_account_bank_code(self, account_number: str) -> str:
        """الحصول على رمز البنك للحساب"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT bank_code FROM bank_accounts 
                    WHERE account_number = $1 OR iban = $1
                    LIMIT 1
                """
                
                row = await conn.fetchrow(query, account_number)
                
                if row:
                    return row['bank_code']
                
                # If not found in database, try to extract from IBAN
                if account_number.startswith("SA") and len(account_number) == 24:
                    bank_code = account_number[4:6]
                    # Map bank code to our internal codes
                    bank_code_mapping = {
                        "10": "RIBLSARI",
                        "05": "NCBKSAJE",
                        "15": "ALBISARI"
                    }
                    return bank_code_mapping.get(bank_code, "RIBLSARI")
                
                return "RIBLSARI"  # Default bank
                
        except Exception as e:
            logger.error(f"❌ Failed to get bank code: {e}")
            return "RIBLSARI"  # Default bank
    
    async def _check_transfer_limits(self, request: BankTransferRequest, bank_config: Dict[str, Any]):
        """فحص حدود التحويل"""
        limits = bank_config.get("limits", {})
        
        # Check single transaction limit
        single_limit = limits.get("single", Decimal('*********.00'))
        if request.amount > single_limit:
            raise ValueError(f"Amount exceeds single transaction limit: {single_limit}")
        
        # Check daily limit
        daily_limit = limits.get("daily", Decimal('*********.00'))
        today_total = await self._get_daily_transfer_total(request.from_account)
        
        if today_total + request.amount > daily_limit:
            raise ValueError(f"Amount exceeds daily limit: {daily_limit}")
    
    async def _calculate_transfer_fees(
        self, 
        request: BankTransferRequest, 
        bank_config: Dict[str, Any]
    ) -> Decimal:
        """حساب رسوم التحويل"""
        fees_config = bank_config.get("fees", {})
        
        # Determine if domestic or international
        is_domestic = await self._is_domestic_transfer(request.from_account, request.to_account)
        
        if is_domestic:
            return fees_config.get("domestic", Decimal('5.00'))
        else:
            return fees_config.get("international", Decimal('25.00'))
    
    async def _is_domestic_transfer(self, from_account: str, to_account: str) -> bool:
        """تحديد ما إذا كان التحويل محلي"""
        # Check if both accounts are Saudi (IBAN starts with SA)
        from_is_saudi = from_account.startswith("SA") if len(from_account) == 24 else True
        to_is_saudi = to_account.startswith("SA") if len(to_account) == 24 else False
        
        return from_is_saudi and to_is_saudi
    
    async def _create_iso20022_message(self, transfer_id: str, request: BankTransferRequest) -> str:
        """إنشاء رسالة ISO 20022"""
        template = self.iso20022_templates[MessageType.CUSTOMER_CREDIT_TRANSFER]
        
        # Get account details
        from_account_details = await self._get_account_details(request.from_account)
        
        message_data = {
            "message_id": f"MSG{transfer_id}",
            "creation_datetime": datetime.now().isoformat(),
            "number_of_transactions": "1",
            "control_sum": str(request.amount),
            "initiating_party_name": "WalletSystem",
            "initiating_party_id": "WS001",
            "payment_info_id": f"PMT{transfer_id}",
            "requested_execution_date": datetime.now().date().isoformat(),
            "debtor_name": from_account_details.get("account_holder", "Unknown"),
            "debtor_iban": from_account_details.get("iban", request.from_account),
            "debtor_agent_bic": from_account_details.get("bank_code", "RIBLSARI"),
            "end_to_end_id": transfer_id,
            "currency": request.currency,
            "amount": str(request.amount),
            "creditor_name": request.beneficiary_name,
            "creditor_iban": request.to_account,
            "creditor_agent_bic": request.beneficiary_bank,
            "remittance_information": request.description
        }
        
        return template.format(**message_data)
    
    async def _send_to_bank(
        self,
        bank_config: Dict[str, Any],
        iso_message: str,
        transfer_id: str
    ) -> Dict[str, Any]:
        """إرسال الرسالة للبنك"""
        try:
            # Check if aiohttp is available for real HTTP requests
            if aiohttp is None:
                logger.warning("aiohttp not available - using mock bank response")
                await asyncio.sleep(0.1)  # Simulate network delay
                return {
                    "status": "COMPLETED",
                    "bank_reference": f"MOCK{datetime.now().strftime('%Y%m%d')}{transfer_id[-6:]}",
                    "message": "Transfer completed successfully (mocked)",
                    "completion_time": datetime.now().isoformat()
                }

            # In production, this would use actual bank APIs
            # For now, simulate bank response
            await asyncio.sleep(0.2)  # Simulate network delay

            # Safely extract amount from ISO message
            try:
                # Look for amount in ISO message
                if '<InstdAmt Ccy="' in iso_message and '</InstdAmt>' in iso_message:
                    amount_section = iso_message.split('<InstdAmt Ccy="')[1].split('</InstdAmt>')[0]
                    amount_value = amount_section.split('>')[1] if '>' in amount_section else "1000.00"
                    amount = Decimal(amount_value)
                else:
                    # Default amount if parsing fails
                    amount = Decimal('1000.00')
            except (IndexError, ValueError, Exception) as e:
                logger.warning(f"Failed to parse amount from ISO message: {e}")
                amount = Decimal('1000.00')

            if amount > Decimal('100000.00'):
                # Large amounts require manual approval
                return {
                    "status": "PENDING",
                    "bank_reference": f"BNK{datetime.now().strftime('%Y%m%d')}{transfer_id[-6:]}",
                    "message": "Transfer pending manual approval",
                    "estimated_completion": (datetime.now() + timedelta(hours=24)).isoformat()
                }
            elif amount > Decimal('50000.00'):
                # Medium amounts processed with delay
                return {
                    "status": "PROCESSING",
                    "bank_reference": f"BNK{datetime.now().strftime('%Y%m%d')}{transfer_id[-6:]}",
                    "message": "Transfer being processed",
                    "estimated_completion": (datetime.now() + timedelta(hours=2)).isoformat()
                }
            else:
                # Small amounts processed immediately
                return {
                    "status": "COMPLETED",
                    "bank_reference": f"BNK{datetime.now().strftime('%Y%m%d')}{transfer_id[-6:]}",
                    "message": "Transfer completed successfully",
                    "completion_time": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"❌ Failed to send to bank: {e}")
            return {
                "status": "FAILED",
                "error_code": "BANK_ERROR",
                "error_message": str(e)
            }
    
    async def _process_bank_response(
        self, 
        transfer_id: str, 
        bank_response: Dict[str, Any], 
        fees: Decimal
    ) -> BankTransferResponse:
        """معالجة استجابة البنك"""
        status_mapping = {
            "COMPLETED": TransactionStatus.COMPLETED,
            "PROCESSING": TransactionStatus.PROCESSING,
            "PENDING": TransactionStatus.PENDING,
            "FAILED": TransactionStatus.FAILED
        }
        
        status = status_mapping.get(bank_response.get("status"), TransactionStatus.FAILED)
        
        # Parse estimated completion
        estimated_completion = None
        if "estimated_completion" in bank_response:
            try:
                estimated_completion = datetime.fromisoformat(bank_response["estimated_completion"])
            except:
                pass
        
        # Get transfer details from database
        transfer_details = await self._get_transfer_details(transfer_id)
        
        return BankTransferResponse(
            transfer_id=transfer_id,
            status=status,
            bank_reference=bank_response.get("bank_reference", ""),
            amount=transfer_details.get("amount", Decimal('0.00')),
            currency=transfer_details.get("currency", "SAR"),
            fees=fees,
            estimated_completion=estimated_completion,
            error_code=bank_response.get("error_code"),
            error_message=bank_response.get("error_message"),
            created_at=datetime.now()
        )
    
    async def _store_transfer_record(self, transfer_id: str, request: BankTransferRequest, fees: Decimal):
        """حفظ سجل التحويل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO bank_transfers (
                        id, from_account, to_account, amount, currency, fees,
                        reference, description, beneficiary_name, beneficiary_bank,
                        purpose_code, metadata, status
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                """
                
                await conn.execute(
                    query,
                    transfer_id,
                    request.from_account,
                    request.to_account,
                    request.amount,
                    request.currency,
                    fees,
                    request.reference,
                    request.description,
                    request.beneficiary_name,
                    request.beneficiary_bank,
                    request.purpose_code,
                    json.dumps(request.metadata or {}),
                    TransactionStatus.PENDING.value
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store transfer record: {e}")
            raise
    
    async def _get_transfer_details(self, transfer_id: str) -> Dict[str, Any]:
        """الحصول على تفاصيل التحويل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM bank_transfers WHERE id = $1
                """
                
                row = await conn.fetchrow(query, transfer_id)
                return dict(row) if row else {}
                
        except Exception as e:
            logger.error(f"❌ Failed to get transfer details: {e}")
            return {}
    
    async def _get_account_details(self, account_number: str) -> Dict[str, Any]:
        """الحصول على تفاصيل الحساب"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM bank_accounts 
                    WHERE account_number = $1 OR iban = $1
                    LIMIT 1
                """
                
                row = await conn.fetchrow(query, account_number)
                return dict(row) if row else {}
                
        except Exception as e:
            logger.error(f"❌ Failed to get account details: {e}")
            return {}
    
    async def _get_daily_transfer_total(self, account_number: str) -> Decimal:
        """الحصول على إجمالي التحويلات اليومية"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT COALESCE(SUM(amount), 0) as total
                    FROM bank_transfers 
                    WHERE from_account = $1 
                    AND DATE(created_at) = CURRENT_DATE
                    AND status IN ('completed', 'processing', 'pending')
                """
                
                row = await conn.fetchrow(query, account_number)
                return row['total'] if row else Decimal('0.00')
                
        except Exception as e:
            logger.error(f"❌ Failed to get daily transfer total: {e}")
            return Decimal('0.00')
    
    async def _send_balance_inquiry(
        self, 
        bank_config: Dict[str, Any], 
        account_number: str, 
        message_id: str
    ) -> Dict[str, Any]:
        """إرسال استعلام الرصيد"""
        # Simulate balance inquiry
        await asyncio.sleep(0.1)
        
        # Return mock balance data
        return {
            "account_number": account_number,
            "available_balance": "15000.00",
            "current_balance": "15500.00",
            "pending_balance": "500.00",
            "currency": "SAR",
            "last_updated": datetime.now().isoformat(),
            "bank_reference": f"BAL{message_id}"
        }
    
    async def _parse_balance_response(
        self, 
        response: Dict[str, Any], 
        account_number: str
    ) -> AccountBalance:
        """تحليل استجابة الرصيد"""
        return AccountBalance(
            account_number=account_number,
            available_balance=Decimal(response["available_balance"]),
            current_balance=Decimal(response["current_balance"]),
            pending_balance=Decimal(response["pending_balance"]),
            currency=response["currency"],
            last_updated=datetime.fromisoformat(response["last_updated"]),
            bank_reference=response["bank_reference"]
        )
    
    async def _store_balance_record(self, balance: AccountBalance):
        """حفظ سجل الرصيد"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO account_balance_history (
                        account_number, available_balance, current_balance, 
                        pending_balance, currency, bank_reference
                    ) VALUES ($1, $2, $3, $4, $5, $6)
                """
                
                await conn.execute(
                    query,
                    balance.account_number,
                    balance.available_balance,
                    balance.current_balance,
                    balance.pending_balance,
                    balance.currency,
                    balance.bank_reference
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store balance record: {e}")
    
    async def _send_statement_request(
        self, 
        bank_config: Dict[str, Any], 
        account_number: str, 
        start_date: datetime, 
        end_date: datetime,
        message_id: str
    ) -> Dict[str, Any]:
        """إرسال طلب كشف الحساب"""
        # Simulate statement request
        await asyncio.sleep(0.3)
        
        # Return mock statement data
        return {
            "account_number": account_number,
            "statement_period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "transactions": [
                {
                    "transaction_id": "TXN001",
                    "date": (datetime.now() - timedelta(days=1)).isoformat(),
                    "description": "Transfer from customer",
                    "amount": "1500.00",
                    "type": "credit",
                    "balance": "16500.00",
                    "reference": "REF001"
                },
                {
                    "transaction_id": "TXN002",
                    "date": (datetime.now() - timedelta(days=2)).isoformat(),
                    "description": "Payment to merchant",
                    "amount": "750.00",
                    "type": "debit",
                    "balance": "15000.00",
                    "reference": "REF002"
                }
            ]
        }
    
    async def _parse_statement_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """تحليل استجابة كشف الحساب"""
        transactions = []
        
        for txn in response.get("transactions", []):
            transactions.append({
                "transaction_id": txn["transaction_id"],
                "date": datetime.fromisoformat(txn["date"]),
                "description": txn["description"],
                "amount": Decimal(txn["amount"]),
                "type": txn["type"],
                "balance": Decimal(txn["balance"]),
                "reference": txn["reference"]
            })
        
        return transactions
    
    async def _store_statement_records(self, account_number: str, transactions: List[Dict[str, Any]]):
        """حفظ سجلات كشف الحساب"""
        try:
            async with self.db_connection.get_connection() as conn:
                for txn in transactions:
                    query = """
                        INSERT INTO account_statement_history (
                            account_number, transaction_id, transaction_date, 
                            description, amount, transaction_type, balance, reference
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                        ON CONFLICT (account_number, transaction_id) DO NOTHING
                    """
                    
                    await conn.execute(
                        query,
                        account_number,
                        txn["transaction_id"],
                        txn["date"],
                        txn["description"],
                        txn["amount"],
                        txn["type"],
                        txn["balance"],
                        txn["reference"]
                    )
                    
        except Exception as e:
            logger.error(f"❌ Failed to store statement records: {e}")
    
    async def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمة"""
        return {
            "transfers_processed": self.transfers_processed,
            "transfers_failed": self.transfers_failed,
            "total_volume": float(self.total_volume),
            "success_rate": (self.transfers_processed / max(self.transfers_processed + self.transfers_failed, 1)) * 100,
            "supported_banks": len(self.bank_configs),
            "active_sessions": len(self.bank_sessions)
        }
