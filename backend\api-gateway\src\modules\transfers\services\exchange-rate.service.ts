import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../../../shared/redis/redis.service';
import { LoggerService } from '../../../shared/logger/logger.service';

export interface ExchangeRate {
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  lastUpdated: Date;
  source: string;
}

export interface CurrencyPair {
  base: string;
  target: string;
  rate: number;
  spread: number;
  lastUpdated: Date;
}

@Injectable()
export class ExchangeRateService {
  private readonly CACHE_TTL = 300; // 5 دقائق
  private readonly API_KEY: string;
  private readonly supportedCurrencies = [
    'SAR', 'USD', 'EUR', 'GBP', 'AED', 'KWD', 'QAR', 'BHD', 'OMR',
    'EGP', 'JOD', 'LBP', 'SYP', 'IQD', 'YER', 'MAD', 'TND', 'DZD',
    'LYD', 'SDG', 'SOS', 'DJF', 'KMF', 'MRU', 'INR', 'PKR', 'BDT',
    'LKR', 'NPR', 'AFN', 'IRR', 'TRY', 'RUB', 'CNY', 'JPY', 'KRW',
    'THB', 'VND', 'IDR', 'MYR', 'SGD', 'PHP', 'AUD', 'NZD', 'CAD'
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly logger: LoggerService,
  ) {
    this.API_KEY = this.configService.get<string>('CURRENCY_EXCHANGE_API_KEY');
    this.logger.setContext('ExchangeRateService');
  }

  /**
   * الحصول على سعر الصرف بين عملتين
   */
  async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<ExchangeRate> {
    try {
      // التحقق من دعم العملات
      this.validateCurrencies(fromCurrency, toCurrency);

      // إذا كانت العملتان متشابهتان
      if (fromCurrency === toCurrency) {
        return {
          fromCurrency,
          toCurrency,
          rate: 1,
          lastUpdated: new Date(),
          source: 'internal',
        };
      }

      // البحث في الكاش أولاً
      const cachedRate = await this.getCachedRate(fromCurrency, toCurrency);
      if (cachedRate) {
        return cachedRate;
      }

      // جلب السعر من المصدر الخارجي
      const rate = await this.fetchExchangeRateFromAPI(fromCurrency, toCurrency);

      // حفظ في الكاش
      await this.cacheExchangeRate(rate);

      this.logger.log(`تم جلب سعر الصرف: ${fromCurrency}/${toCurrency} = ${rate.rate}`);

      return rate;
    } catch (error) {
      this.logger.error('خطأ في جلب سعر الصرف', error.stack, {
        fromCurrency,
        toCurrency,
      });
      throw new BadRequestException('فشل في جلب سعر الصرف');
    }
  }

  /**
   * الحصول على جميع أسعار الصرف
   */
  async getAllExchangeRates(): Promise<CurrencyPair[]> {
    try {
      const baseCurrency = 'USD';
      const rates: CurrencyPair[] = [];

      for (const currency of this.supportedCurrencies) {
        if (currency !== baseCurrency) {
          try {
            const rate = await this.getExchangeRate(baseCurrency, currency);
            rates.push({
              base: baseCurrency,
              target: currency,
              rate: rate.rate,
              spread: this.calculateSpread(rate.rate),
              lastUpdated: rate.lastUpdated,
            });
          } catch (error) {
            this.logger.warn(`فشل في جلب سعر ${baseCurrency}/${currency}`, { error: error.message });
          }
        }
      }

      return rates;
    } catch (error) {
      this.logger.error('خطأ في جلب جميع أسعار الصرف', error.stack);
      throw error;
    }
  }

  /**
   * تحديث أسعار الصرف (مهمة مجدولة)
   */
  async updateExchangeRates(): Promise<void> {
    try {
      this.logger.log('بدء تحديث أسعار الصرف');

      const baseCurrencies = ['USD', 'EUR', 'SAR'];
      let updatedCount = 0;

      for (const baseCurrency of baseCurrencies) {
        for (const targetCurrency of this.supportedCurrencies) {
          if (baseCurrency !== targetCurrency) {
            try {
              await this.fetchAndCacheRate(baseCurrency, targetCurrency);
              updatedCount++;
            } catch (error) {
              this.logger.warn(`فشل في تحديث ${baseCurrency}/${targetCurrency}`, {
                error: error.message,
              });
            }
          }
        }
      }

      this.logger.log(`تم تحديث ${updatedCount} سعر صرف`);
    } catch (error) {
      this.logger.error('خطأ في تحديث أسعار الصرف', error.stack);
    }
  }

  /**
   * حساب التكلفة الإجمالية بعد تحويل العملة
   */
  async convertAmount(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
  ): Promise<{ convertedAmount: number; rate: number; fees: number }> {
    const exchangeRate = await this.getExchangeRate(fromCurrency, toCurrency);
    const convertedAmount = amount * exchangeRate.rate;
    const fees = this.calculateConversionFees(amount, fromCurrency, toCurrency);

    return {
      convertedAmount: convertedAmount - fees,
      rate: exchangeRate.rate,
      fees,
    };
  }

  /**
   * الحصول على العملات المدعومة
   */
  getSupportedCurrencies(): string[] {
    return [...this.supportedCurrencies];
  }

  /**
   * التحقق من دعم العملة
   */
  isCurrencySupported(currency: string): boolean {
    return this.supportedCurrencies.includes(currency.toUpperCase());
  }

  // Private Methods

  private validateCurrencies(fromCurrency: string, toCurrency: string): void {
    if (!this.isCurrencySupported(fromCurrency)) {
      throw new BadRequestException(`العملة ${fromCurrency} غير مدعومة`);
    }

    if (!this.isCurrencySupported(toCurrency)) {
      throw new BadRequestException(`العملة ${toCurrency} غير مدعومة`);
    }
  }

  private async getCachedRate(
    fromCurrency: string,
    toCurrency: string,
  ): Promise<ExchangeRate | null> {
    try {
      const cacheKey = `exchange_rate:${fromCurrency}:${toCurrency}`;
      const cachedData = await this.redisService.get(cacheKey);

      if (cachedData) {
        return JSON.parse(cachedData);
      }

      return null;
    } catch (error) {
      this.logger.warn('خطأ في جلب السعر من الكاش', { error: error.message });
      return null;
    }
  }

  private async cacheExchangeRate(rate: ExchangeRate): Promise<void> {
    try {
      const cacheKey = `exchange_rate:${rate.fromCurrency}:${rate.toCurrency}`;
      await this.redisService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(rate));
    } catch (error) {
      this.logger.warn('خطأ في حفظ السعر في الكاش', { error: error.message });
    }
  }

  private async fetchExchangeRateFromAPI(
    fromCurrency: string,
    toCurrency: string,
  ): Promise<ExchangeRate> {
    try {
      // محاكاة استدعاء API خارجي
      // في التطبيق الحقيقي، استخدم خدمة مثل:
      // - ExchangeRate-API
      // - Fixer.io
      // - CurrencyLayer
      // - Open Exchange Rates

      const mockRates = this.getMockExchangeRates();
      const pairKey = `${fromCurrency}${toCurrency}`;
      const rate = mockRates[pairKey] || this.calculateCrossRate(fromCurrency, toCurrency, mockRates);

      if (!rate) {
        throw new Error(`لا يمكن العثور على سعر الصرف لـ ${fromCurrency}/${toCurrency}`);
      }

      return {
        fromCurrency,
        toCurrency,
        rate,
        lastUpdated: new Date(),
        source: 'external_api',
      };
    } catch (error) {
      throw new Error(`فشل في جلب سعر الصرف من API: ${error.message}`);
    }
  }

  private getMockExchangeRates(): Record<string, number> {
    // أسعار وهمية للاختبار - في التطبيق الحقيقي، جلب من API
    return {
      USDSAR: 3.75,
      SARUSD: 0.2667,
      USDEUR: 0.85,
      EURUSD: 1.18,
      USDGBP: 0.73,
      GBPUSD: 1.37,
      USDAED: 3.67,
      AEDUSD: 0.27,
      USDKWD: 0.30,
      KWDUSD: 3.33,
      USDQAR: 3.64,
      QARUSD: 0.27,
      USDBHD: 0.38,
      BHDUSD: 2.65,
      USDOMR: 0.38,
      OMRUSD: 2.60,
      USDEGP: 31.45,
      EGPUSD: 0.032,
      USDJOD: 0.71,
      JODUSD: 1.41,
      USDLBP: 15000,
      LBPUSD: 0.000067,
      USDINR: 83.12,
      INRUSD: 0.012,
      USDPKR: 287.50,
      PKRUSD: 0.0035,
    };
  }

  private calculateCrossRate(
    fromCurrency: string,
    toCurrency: string,
    rates: Record<string, number>,
  ): number | null {
    // حساب السعر المتقاطع عبر USD
    const fromToUsd = rates[`${fromCurrency}USD`];
    const usdToTarget = rates[`USD${toCurrency}`];

    if (fromToUsd && usdToTarget) {
      return fromToUsd * usdToTarget;
    }

    const usdToFrom = rates[`USD${fromCurrency}`];
    const targetToUsd = rates[`${toCurrency}USD`];

    if (usdToFrom && targetToUsd) {
      return targetToUsd / usdToFrom;
    }

    return null;
  }

  private calculateSpread(rate: number): number {
    // حساب الفارق (عادة 1-3%)
    return rate * 0.02; // 2% spread
  }

  private calculateConversionFees(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
  ): number {
    // حساب رسوم التحويل (عادة 0.5-2%)
    const feePercentage = 0.015; // 1.5%
    return amount * feePercentage;
  }

  private async fetchAndCacheRate(fromCurrency: string, toCurrency: string): Promise<void> {
    const rate = await this.fetchExchangeRateFromAPI(fromCurrency, toCurrency);
    await this.cacheExchangeRate(rate);
  }
}
