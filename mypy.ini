[mypy]
# MyPy Configuration
# ==================
# إعدادات MyPy للتحقق من الأنواع

# Python version
python_version = 3.11

# Import discovery
mypy_path = backend:frontend:tests
packages = backend,tests
namespace_packages = True

# Platform configuration
platform = linux

# Error output
show_error_codes = True
show_error_context = True
show_column_numbers = True
show_absolute_path = True
color_output = True
error_summary = True
pretty = True

# Warnings
warn_return_any = True
warn_unused_configs = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# Error handling
strict_optional = True
strict_equality = True
strict_concatenate = True

# Untyped definitions
check_untyped_defs = True
disallow_untyped_calls = False
disallow_untyped_defs = False
disallow_incomplete_defs = True
disallow_untyped_decorators = False

# None and Optional handling
no_implicit_optional = True
no_implicit_reexport = True

# Dynamic typing
disallow_any_unimported = False
disallow_any_expr = False
disallow_any_decorated = False
disallow_any_explicit = False
disallow_any_generics = True
disallow_subclassing_any = True

# Miscellaneous
allow_redefinition = False
local_partial_types = False
implicit_reexport = False
strict_concatenate = True

# Per-module options
[mypy-tests.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = False

[mypy-migrations.*]
ignore_errors = True

[mypy-seeds.*]
ignore_errors = True

# Third-party libraries without stubs
[mypy-asyncpg.*]
ignore_missing_imports = True

[mypy-aiohttp.*]
ignore_missing_imports = True

[mypy-fastapi.*]
ignore_missing_imports = True

[mypy-uvicorn.*]
ignore_missing_imports = True

[mypy-pydantic.*]
ignore_missing_imports = True

[mypy-sqlalchemy.*]
ignore_missing_imports = True

[mypy-alembic.*]
ignore_missing_imports = True

[mypy-pytest.*]
ignore_missing_imports = True

[mypy-pytest_asyncio.*]
ignore_missing_imports = True

[mypy-pytest_mock.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-celery.*]
ignore_missing_imports = True

[mypy-boto3.*]
ignore_missing_imports = True

[mypy-botocore.*]
ignore_missing_imports = True

[mypy-azure.*]
ignore_missing_imports = True

[mypy-pandas.*]
ignore_missing_imports = True

[mypy-numpy.*]
ignore_missing_imports = True

[mypy-sklearn.*]
ignore_missing_imports = True

[mypy-tensorflow.*]
ignore_missing_imports = True

[mypy-torch.*]
ignore_missing_imports = True

[mypy-web3.*]
ignore_missing_imports = True

[mypy-eth_account.*]
ignore_missing_imports = True

[mypy-passlib.*]
ignore_missing_imports = True

[mypy-bcrypt.*]
ignore_missing_imports = True

[mypy-cryptography.*]
ignore_missing_imports = True

[mypy-jwt.*]
ignore_missing_imports = True

[mypy-structlog.*]
ignore_missing_imports = True

[mypy-prometheus_client.*]
ignore_missing_imports = True

[mypy-sentry_sdk.*]
ignore_missing_imports = True

[mypy-datadog.*]
ignore_missing_imports = True

[mypy-click.*]
ignore_missing_imports = True

[mypy-rich.*]
ignore_missing_imports = True

[mypy-jinja2.*]
ignore_missing_imports = True

[mypy-aiosmtplib.*]
ignore_missing_imports = True

[mypy-kombu.*]
ignore_missing_imports = True

[mypy-aiofiles.*]
ignore_missing_imports = True

[mypy-httpx.*]
ignore_missing_imports = True

[mypy-websockets.*]
ignore_missing_imports = True

[mypy-socketio.*]
ignore_missing_imports = True

[mypy-psycopg2.*]
ignore_missing_imports = True

[mypy-dotenv.*]
ignore_missing_imports = True

[mypy-pytz.*]
ignore_missing_imports = True

[mypy-dateutil.*]
ignore_missing_imports = True

# Project-specific modules
[mypy-backend.shared.*]
disallow_untyped_defs = False

[mypy-backend.integration.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-backend.payment.*]
disallow_untyped_defs = False

[mypy-backend.notifications.*]
disallow_untyped_defs = False

[mypy-backend.admin-panel.*]
disallow_untyped_defs = False

[mypy-backend.agent-system.*]
disallow_untyped_defs = False

[mypy-backend.ai-engine.*]
disallow_untyped_defs = False

[mypy-backend.api-gateway.*]
disallow_untyped_defs = False
