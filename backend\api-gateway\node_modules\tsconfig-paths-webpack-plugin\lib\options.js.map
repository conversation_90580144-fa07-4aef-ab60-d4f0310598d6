{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../src/options.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAgBA,IAAM,YAAY,GAAgC;IAChD,YAAY;IACZ,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,UAAU;IACV,iBAAiB;IACjB,SAAS;IACT,YAAY;IACZ,YAAY;CACb,CAAC;AAEF;;;GAGG;AACH,SAAgB,UAAU,CAAC,UAAc;IACvC,eAAe,CAAC,UAAU,CAAC,CAAC;IAE5B,IAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;IAExC,OAAO,OAAO,CAAC;AACjB,CAAC;AAND,gCAMC;AAED;;;;;GAKG;AACH,SAAS,eAAe,CAAC,UAAc;IACrC,IAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAChD,IAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACnC,IAAM,kBAAkB,GACrB,YAAsC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACjE,IAAI,kBAAkB,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,uFAAgF,MAAM,sGAE1G,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,OACzB,CAAC,CAAC;SACE;KACF;AACH,CAAC;AAED,SAAS,WAAW,CAAC,UAA4B;IAC/C,IAAM,OAAO,yBACP;QACF,UAAU,EAAE,eAAe;QAC3B,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;QAC3B,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,MAAM;QAChB,eAAe,EAAE,KAAK;QACtB,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE,CAAC,MAAM,CAAC;QACpB,UAAU,EAAE,SAAS;KACV,GACV,UAAU,CACd,CAAC;IAEF,IAAM,QAAQ,yBACT,OAAO,KACV,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAc,GACrD,CAAC;IAEF,OAAO,QAAQ,CAAC;AAClB,CAAC"}