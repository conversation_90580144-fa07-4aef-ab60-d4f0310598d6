"""
Notifications API
================
واجهة برمجة التطبيقات للإشعارات
"""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator

from ..shared.auth.middleware import get_current_user, require_role, require_permission
from ..shared.auth.jwt_manager import TokenPayload, UserRole, Permission
from ..shared.database.connection import DatabaseConnection

from .notification_service import (
    NotificationService, NotificationRequest, NotificationRecipient,
    NotificationChannel, NotificationType, NotificationPriority, NotificationStatus
)
from .template_manager import TemplateManager, TemplateLanguage, TemplateVariable
from .scheduler_service import NotificationScheduler, ScheduleType, ScheduleStatus
from .analytics_service import NotificationAnalytics, AnalyticsPeriod

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/notifications", tags=["Notifications"])


# Pydantic Models
class NotificationSendRequest(BaseModel):
    """طلب إرسال إشعار"""
    type: str = Field(..., description="نوع الإشعار")
    channel: str = Field(..., description="قناة الإرسال")
    recipient_user_id: str = Field(..., description="معرف المستقبل")
    priority: str = Field(default="medium", description="أولوية الإشعار")
    data: Dict[str, Any] = Field(..., description="بيانات الإشعار")
    template_id: Optional[str] = Field(None, description="معرف القالب")
    scheduled_at: Optional[datetime] = Field(None, description="وقت الجدولة")
    
    @validator('type')
    def validate_type(cls, v):
        valid_types = [t.value for t in NotificationType]
        if v not in valid_types:
            raise ValueError(f'نوع إشعار غير صحيح. الأنواع المتاحة: {valid_types}')
        return v
    
    @validator('channel')
    def validate_channel(cls, v):
        valid_channels = [c.value for c in NotificationChannel]
        if v not in valid_channels:
            raise ValueError(f'قناة غير صحيحة. القنوات المتاحة: {valid_channels}')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        valid_priorities = [p.value for p in NotificationPriority]
        if v not in valid_priorities:
            raise ValueError(f'أولوية غير صحيحة. الأولويات المتاحة: {valid_priorities}')
        return v


class BulkNotificationRequest(BaseModel):
    """طلب إرسال إشعارات متعددة"""
    notifications: List[NotificationSendRequest] = Field(..., description="قائمة الإشعارات")
    
    @validator('notifications')
    def validate_notifications_count(cls, v):
        if len(v) > 1000:
            raise ValueError('لا يمكن إرسال أكثر من 1000 إشعار في المرة الواحدة')
        return v


class TemplateCreateRequest(BaseModel):
    """طلب إنشاء قالب"""
    name: str = Field(..., description="اسم القالب")
    notification_type: str = Field(..., description="نوع الإشعار")
    channel: str = Field(..., description="قناة الإرسال")
    language: str = Field(default="ar", description="لغة القالب")
    subject_template: str = Field(..., description="قالب العنوان")
    body_template: str = Field(..., description="قالب المحتوى")
    variables: List[Dict[str, Any]] = Field(..., description="متغيرات القالب")
    description: Optional[str] = Field(None, description="وصف القالب")


class ScheduleNotificationRequest(BaseModel):
    """طلب جدولة إشعار"""
    name: str = Field(..., description="اسم الجدولة")
    notification: NotificationSendRequest = Field(..., description="الإشعار المراد جدولته")
    schedule_type: str = Field(..., description="نوع الجدولة")
    scheduled_at: Optional[datetime] = Field(None, description="وقت التنفيذ")
    cron_expression: Optional[str] = Field(None, description="تعبير الجدولة")
    max_executions: Optional[int] = Field(None, description="الحد الأقصى للتنفيذ")
    description: Optional[str] = Field(None, description="وصف الجدولة")
    
    @validator('schedule_type')
    def validate_schedule_type(cls, v):
        valid_types = [t.value for t in ScheduleType]
        if v not in valid_types:
            raise ValueError(f'نوع جدولة غير صحيح. الأنواع المتاحة: {valid_types}')
        return v


class SystemAnnouncementRequest(BaseModel):
    """طلب إعلان نظام"""
    title: str = Field(..., description="عنوان الإعلان")
    message: str = Field(..., description="محتوى الإعلان")
    target_users: Optional[List[str]] = Field(None, description="المستخدمون المستهدفون")
    target_roles: Optional[List[str]] = Field(None, description="الأدوار المستهدفة")
    channels: Optional[List[str]] = Field(None, description="قنوات الإرسال")
    scheduled_at: Optional[datetime] = Field(None, description="وقت الجدولة")


# Dependency injection
async def get_notification_service() -> NotificationService:
    """الحصول على خدمة الإشعارات"""
    db_connection = DatabaseConnection()
    return NotificationService(db_connection)


async def get_template_manager() -> TemplateManager:
    """الحصول على مدير القوالب"""
    db_connection = DatabaseConnection()
    return TemplateManager(db_connection)


async def get_notification_scheduler() -> NotificationScheduler:
    """الحصول على جدولة الإشعارات"""
    db_connection = DatabaseConnection()
    notification_service = await get_notification_service()
    return NotificationScheduler(db_connection, notification_service)


async def get_notification_analytics() -> NotificationAnalytics:
    """الحصول على تحليلات الإشعارات"""
    db_connection = DatabaseConnection()
    return NotificationAnalytics(db_connection)


# Notification Endpoints

@router.post("/send")
async def send_notification(
    request: NotificationSendRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.SEND_NOTIFICATIONS)),
    notification_service: NotificationService = Depends(get_notification_service)
):
    """إرسال إشعار واحد"""
    try:
        # Get recipient details
        recipient = await _get_notification_recipient(request.recipient_user_id)
        if not recipient:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="المستقبل غير موجود"
            )
        
        # Create notification request
        notification_request = NotificationRequest(
            type=NotificationType(request.type),
            channel=NotificationChannel(request.channel),
            recipient=recipient,
            priority=NotificationPriority(request.priority),
            data=request.data,
            scheduled_at=request.scheduled_at,
            template_id=request.template_id
        )
        
        # Send notification
        result = await notification_service.send_notification(notification_request)
        
        return {
            "success": True,
            "message": "تم إرسال الإشعار بنجاح",
            "data": {
                "notification_id": result.notification_id,
                "status": result.status.value,
                "sent_at": result.sent_at.isoformat() if result.sent_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to send notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء إرسال الإشعار"
        )


@router.post("/send-bulk")
async def send_bulk_notifications(
    request: BulkNotificationRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.SEND_NOTIFICATIONS)),
    notification_service: NotificationService = Depends(get_notification_service)
):
    """إرسال إشعارات متعددة"""
    try:
        # Convert requests to notification requests
        notification_requests = []
        
        for notif_request in request.notifications:
            recipient = await _get_notification_recipient(notif_request.recipient_user_id)
            if not recipient:
                continue  # Skip invalid recipients
            
            notification_request = NotificationRequest(
                type=NotificationType(notif_request.type),
                channel=NotificationChannel(notif_request.channel),
                recipient=recipient,
                priority=NotificationPriority(notif_request.priority),
                data=notif_request.data,
                scheduled_at=notif_request.scheduled_at,
                template_id=notif_request.template_id
            )
            notification_requests.append(notification_request)
        
        # Send bulk notifications
        results = await notification_service.send_bulk_notifications(notification_requests)
        
        # Count results
        successful = sum(1 for r in results if r.status.value == "sent")
        failed = len(results) - successful
        
        return {
            "success": True,
            "message": f"تم إرسال {successful} إشعار بنجاح من أصل {len(results)}",
            "data": {
                "total_sent": len(results),
                "successful": successful,
                "failed": failed,
                "results": [
                    {
                        "notification_id": r.notification_id,
                        "status": r.status.value,
                        "error": r.error_message
                    }
                    for r in results
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to send bulk notifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء إرسال الإشعارات المتعددة"
        )


@router.post("/system-announcement")
async def send_system_announcement(
    request: SystemAnnouncementRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.SEND_SYSTEM_ANNOUNCEMENTS)),
    notification_service: NotificationService = Depends(get_notification_service)
):
    """إرسال إعلان نظام"""
    try:
        # Convert channel strings to enums
        channels = None
        if request.channels:
            channels = [NotificationChannel(ch) for ch in request.channels]
        
        # Send system announcement
        results = await notification_service.send_system_announcement(
            title=request.title,
            message=request.message,
            target_users=request.target_users,
            target_roles=request.target_roles,
            channels=channels,
            scheduled_at=request.scheduled_at
        )
        
        successful = sum(1 for r in results if r.status.value == "sent")
        
        return {
            "success": True,
            "message": f"تم إرسال الإعلان إلى {successful} مستقبل",
            "data": {
                "total_recipients": len(results),
                "successful_deliveries": successful
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to send system announcement: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء إرسال الإعلان"
        )


@router.get("/history")
async def get_notification_history(
    user_id: Optional[str] = Query(None, description="معرف المستخدم"),
    notification_type: Optional[str] = Query(None, description="نوع الإشعار"),
    channel: Optional[str] = Query(None, description="قناة الإرسال"),
    status: Optional[str] = Query(None, description="حالة الإشعار"),
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    limit: int = Query(default=100, ge=1, le=1000, description="عدد النتائج"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_NOTIFICATIONS)),
    notification_service: NotificationService = Depends(get_notification_service)
):
    """الحصول على تاريخ الإشعارات"""
    try:
        # Convert string enums
        notification_type_enum = NotificationType(notification_type) if notification_type else None
        channel_enum = NotificationChannel(channel) if channel else None
        status_enum = NotificationStatus(status) if status else None
        
        # Convert dates to datetime
        start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
        end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None
        
        # Get history
        history = await notification_service.get_notification_history(
            user_id=user_id,
            notification_type=notification_type_enum,
            channel=channel_enum,
            status=status_enum,
            start_date=start_datetime,
            end_date=end_datetime,
            limit=limit
        )
        
        return {
            "success": True,
            "data": {
                "notifications": history,
                "count": len(history),
                "filters": {
                    "user_id": user_id,
                    "notification_type": notification_type,
                    "channel": channel,
                    "status": status,
                    "start_date": start_date.isoformat() if start_date else None,
                    "end_date": end_date.isoformat() if end_date else None
                }
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"معامل غير صحيح: {str(e)}"
        )
    except Exception as e:
        logger.error(f"❌ Failed to get notification history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب تاريخ الإشعارات"
        )


@router.get("/statistics")
async def get_notification_statistics(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_ANALYTICS)),
    notification_service: NotificationService = Depends(get_notification_service)
):
    """الحصول على إحصائيات الإشعارات"""
    try:
        # Convert dates to datetime
        start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
        end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None
        
        # Get statistics
        statistics = await notification_service.get_notification_statistics(
            start_date=start_datetime,
            end_date=end_datetime
        )
        
        return {
            "success": True,
            "data": statistics
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get notification statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب الإحصائيات"
        )


# Template Management Endpoints

@router.post("/templates")
async def create_template(
    request: TemplateCreateRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.MANAGE_TEMPLATES)),
    template_manager: TemplateManager = Depends(get_template_manager)
):
    """إنشاء قالب جديد"""
    try:
        # Convert variables
        variables = [
            TemplateVariable(
                name=var["name"],
                type=var.get("type", "string"),
                description=var.get("description", ""),
                required=var.get("required", True),
                default_value=var.get("default_value")
            )
            for var in request.variables
        ]
        
        # Create template
        template_id = await template_manager.create_template(
            name=request.name,
            notification_type=NotificationType(request.notification_type),
            channel=NotificationChannel(request.channel),
            language=TemplateLanguage(request.language),
            subject_template=request.subject_template,
            body_template=request.body_template,
            variables=variables,
            created_by=current_user.user_id,
            description=request.description
        )
        
        return {
            "success": True,
            "message": "تم إنشاء القالب بنجاح",
            "data": {
                "template_id": template_id
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"❌ Failed to create template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء إنشاء القالب"
        )


@router.get("/templates")
async def list_templates(
    notification_type: Optional[str] = Query(None, description="نوع الإشعار"),
    channel: Optional[str] = Query(None, description="قناة الإرسال"),
    language: Optional[str] = Query(None, description="لغة القالب"),
    is_active: Optional[bool] = Query(None, description="حالة النشاط"),
    limit: int = Query(default=100, ge=1, le=1000, description="عدد النتائج"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_TEMPLATES)),
    template_manager: TemplateManager = Depends(get_template_manager)
):
    """قائمة القوالب"""
    try:
        # Convert string enums
        notification_type_enum = NotificationType(notification_type) if notification_type else None
        channel_enum = NotificationChannel(channel) if channel else None
        language_enum = TemplateLanguage(language) if language else None
        
        # Get templates
        templates = await template_manager.list_templates(
            notification_type=notification_type_enum,
            channel=channel_enum,
            language=language_enum,
            is_active=is_active,
            limit=limit
        )
        
        return {
            "success": True,
            "data": {
                "templates": templates,
                "count": len(templates)
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"معامل غير صحيح: {str(e)}"
        )
    except Exception as e:
        logger.error(f"❌ Failed to list templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب قائمة القوالب"
        )


@router.get("/templates/{template_id}")
async def get_template(
    template_id: str = Path(..., description="معرف القالب"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_TEMPLATES)),
    template_manager: TemplateManager = Depends(get_template_manager)
):
    """الحصول على قالب بالمعرف"""
    try:
        template = await template_manager.get_template(template_id)
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="القالب غير موجود"
            )
        
        return {
            "success": True,
            "data": template
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب القالب"
        )


@router.post("/templates/{template_id}/preview")
async def preview_template(
    template_id: str = Path(..., description="معرف القالب"),
    sample_data: Dict[str, Any] = Body(..., description="بيانات تجريبية"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_TEMPLATES)),
    template_manager: TemplateManager = Depends(get_template_manager)
):
    """معاينة القالب"""
    try:
        preview = await template_manager.preview_template(template_id, sample_data)
        
        if not preview:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="القالب غير موجود"
            )
        
        return {
            "success": True,
            "data": preview
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to preview template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء معاينة القالب"
        )


# Scheduling Endpoints

@router.post("/schedule")
async def schedule_notification(
    request: ScheduleNotificationRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.SCHEDULE_NOTIFICATIONS)),
    scheduler: NotificationScheduler = Depends(get_notification_scheduler)
):
    """جدولة إشعار"""
    try:
        # Get recipient
        recipient = await _get_notification_recipient(request.notification.recipient_user_id)
        if not recipient:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="المستقبل غير موجود"
            )
        
        # Create notification request
        notification_request = NotificationRequest(
            type=NotificationType(request.notification.type),
            channel=NotificationChannel(request.notification.channel),
            recipient=recipient,
            priority=NotificationPriority(request.notification.priority),
            data=request.notification.data,
            template_id=request.notification.template_id
        )
        
        # Schedule notification
        schedule_id = await scheduler.schedule_notification(
            name=request.name,
            notification_request=notification_request,
            schedule_type=ScheduleType(request.schedule_type),
            scheduled_at=request.scheduled_at,
            cron_expression=request.cron_expression,
            max_executions=request.max_executions,
            created_by=current_user.user_id,
            description=request.description
        )
        
        return {
            "success": True,
            "message": "تم جدولة الإشعار بنجاح",
            "data": {
                "schedule_id": schedule_id
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to schedule notification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جدولة الإشعار"
        )


@router.get("/schedules")
async def get_scheduled_notifications(
    status: Optional[str] = Query(None, description="حالة الجدولة"),
    schedule_type: Optional[str] = Query(None, description="نوع الجدولة"),
    limit: int = Query(default=100, ge=1, le=1000, description="عدد النتائج"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_SCHEDULES)),
    scheduler: NotificationScheduler = Depends(get_notification_scheduler)
):
    """الحصول على الإشعارات المجدولة"""
    try:
        # Convert string enums
        status_enum = ScheduleStatus(status) if status else None
        schedule_type_enum = ScheduleType(schedule_type) if schedule_type else None
        
        # Get scheduled notifications
        schedules = await scheduler.get_scheduled_notifications(
            status=status_enum,
            schedule_type=schedule_type_enum,
            created_by=current_user.user_id,
            limit=limit
        )
        
        return {
            "success": True,
            "data": {
                "schedules": schedules,
                "count": len(schedules)
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"معامل غير صحيح: {str(e)}"
        )
    except Exception as e:
        logger.error(f"❌ Failed to get scheduled notifications: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب الإشعارات المجدولة"
        )


# Analytics Endpoints

@router.get("/analytics/delivery")
async def get_delivery_analytics(
    start_date: date = Query(..., description="تاريخ البداية"),
    end_date: date = Query(..., description="تاريخ النهاية"),
    channel: Optional[str] = Query(None, description="قناة الإرسال"),
    notification_type: Optional[str] = Query(None, description="نوع الإشعار"),
    group_by: str = Query(default="day", description="تجميع البيانات"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_ANALYTICS)),
    analytics: NotificationAnalytics = Depends(get_notification_analytics)
):
    """تحليلات التسليم"""
    try:
        # Convert string enums
        channel_enum = NotificationChannel(channel) if channel else None
        notification_type_enum = NotificationType(notification_type) if notification_type else None
        group_by_enum = AnalyticsPeriod(group_by)
        
        # Get analytics
        delivery_analytics = await analytics.get_delivery_analytics(
            start_date=start_date,
            end_date=end_date,
            channel=channel_enum,
            notification_type=notification_type_enum,
            group_by=group_by_enum
        )
        
        return {
            "success": True,
            "data": delivery_analytics
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"معامل غير صحيح: {str(e)}"
        )
    except Exception as e:
        logger.error(f"❌ Failed to get delivery analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب تحليلات التسليم"
        )


@router.get("/analytics/engagement")
async def get_user_engagement_analytics(
    start_date: date = Query(..., description="تاريخ البداية"),
    end_date: date = Query(..., description="تاريخ النهاية"),
    user_id: Optional[str] = Query(None, description="معرف المستخدم"),
    limit: int = Query(default=100, ge=1, le=1000, description="عدد النتائج"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_ANALYTICS)),
    analytics: NotificationAnalytics = Depends(get_notification_analytics)
):
    """تحليلات تفاعل المستخدمين"""
    try:
        engagement_analytics = await analytics.get_user_engagement_analytics(
            start_date=start_date,
            end_date=end_date,
            user_id=user_id,
            limit=limit
        )
        
        return {
            "success": True,
            "data": engagement_analytics
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get engagement analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب تحليلات التفاعل"
        )


# Helper functions
async def _get_notification_recipient(user_id: str) -> Optional[NotificationRecipient]:
    """الحصول على مستقبل الإشعار"""
    try:
        db_connection = DatabaseConnection()
        async with db_connection.get_connection() as conn:
            query = """
                SELECT id, email, phone, language, timezone
                FROM users 
                WHERE id = $1 AND deleted_at IS NULL
            """
            
            row = await conn.fetchrow(query, user_id)
            
            if row:
                return NotificationRecipient(
                    user_id=row["id"],
                    email=row["email"],
                    phone=row["phone"],
                    language=row["language"] or "ar",
                    timezone=row["timezone"] or "Asia/Riyadh"
                )
            
            return None
            
    except Exception as e:
        logger.error(f"❌ Failed to get notification recipient: {e}")
        return None


# Error handlers
@router.exception_handler(ValueError)
async def value_error_handler(request, exc):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "success": False,
            "error": "invalid_input",
            "message": str(exc)
        }
    )


@router.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"❌ Unhandled exception in notifications API: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "error": "internal_server_error",
            "message": "حدث خطأ داخلي في الخادم"
        }
    )
