/**
 * فحص التقدم - المرحلة الثانية
 * Progress Check - Phase 2
 */

const fs = require('fs');

console.log('🚀 فحص التقدم - المرحلة الثانية');
console.log('===============================');

// الملفات الجديدة في المرحلة الثانية
const phase2Files = [
  // Transfer Service Controllers
  'backend/transfer-service/src/modules/transfers/controllers/transfers.controller.ts',
  
  // Wallet Service Controllers & Services
  'backend/wallet-service/src/modules/wallets/controllers/wallets.controller.ts',
  'backend/wallet-service/src/modules/wallets/services/wallets.service.ts',
  
  // Transfer Service Guards & Decorators
  'backend/transfer-service/src/common/guards/auth.guard.ts',
  'backend/transfer-service/src/common/guards/roles.guard.ts',
  'backend/transfer-service/src/common/decorators/roles.decorator.ts',
  'backend/transfer-service/src/common/decorators/get-user.decorator.ts',
  
  // Wallet Service Guards & Decorators
  'backend/wallet-service/src/common/guards/auth.guard.ts',
  'backend/wallet-service/src/common/guards/roles.guard.ts',
  'backend/wallet-service/src/common/decorators/roles.decorator.ts',
  'backend/wallet-service/src/common/decorators/get-user.decorator.ts'
];

let completed = 0;
let missing = 0;

console.log('\n📁 الملفات المنشأة في المرحلة الثانية:');
phase2Files.forEach((file, index) => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${index + 1}. ${file}`);
    completed++;
  } else {
    console.log(`   ❌ ${index + 1}. ${file}`);
    missing++;
  }
});

console.log('\n📊 إحصائيات المرحلة الثانية:');
console.log(`✅ ملفات مكتملة: ${completed}/${phase2Files.length}`);
console.log(`❌ ملفات مفقودة: ${missing}/${phase2Files.length}`);
console.log(`📈 نسبة الإنجاز: ${Math.round((completed / phase2Files.length) * 100)}%`);

// فحص محتوى الملفات المهمة
console.log('\n🔍 فحص محتوى الملفات الجديدة:');

const importantFiles = [
  'backend/transfer-service/src/modules/transfers/controllers/transfers.controller.ts',
  'backend/wallet-service/src/modules/wallets/controllers/wallets.controller.ts',
  'backend/wallet-service/src/modules/wallets/services/wallets.service.ts'
];

importantFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    const lines = content.split('\n').length;
    const hasApiTags = content.includes('@ApiTags');
    const hasControllerDecorator = content.includes('@Controller');
    const hasServiceDecorator = content.includes('@Injectable');
    
    console.log(`   📄 ${file}:`);
    console.log(`      📏 عدد الأسطر: ${lines}`);
    console.log(`      🏷️  يحتوي على @ApiTags: ${hasApiTags ? '✅' : '❌'}`);
    console.log(`      🎯 يحتوي على @Controller: ${hasControllerDecorator ? '✅' : '❌'}`);
    console.log(`      💉 يحتوي على @Injectable: ${hasServiceDecorator ? '✅' : '❌'}`);
  }
});

// إجمالي التقدم من المرحلتين
console.log('\n📈 إجمالي التقدم من المرحلتين:');

const phase1Files = [
  'backend/user-service/src/modules/users/dto/create-user.dto.ts',
  'backend/user-service/src/modules/users/dto/update-user.dto.ts',
  'backend/user-service/src/modules/users/controllers/users.controller.ts',
  'backend/user-service/src/modules/users/services/users.service.ts',
  'backend/user-service/src/common/guards/auth.guard.ts',
  'backend/user-service/src/common/guards/roles.guard.ts',
  'backend/user-service/src/common/decorators/roles.decorator.ts',
  'backend/user-service/src/common/decorators/get-user.decorator.ts',
  'backend/transfer-service/src/modules/transfers/dto/create-transfer.dto.ts',
  'backend/transfer-service/src/modules/transfers/services/transfers.service.ts',
  'backend/wallet-service/src/modules/wallets/dto/create-wallet.dto.ts',
  'backend/user-service/tsconfig.json',
  'backend/transfer-service/tsconfig.json',
  'backend/wallet-service/tsconfig.json'
];

const allFiles = [...phase1Files, ...phase2Files];
let totalCompleted = 0;

allFiles.forEach(file => {
  if (fs.existsSync(file)) {
    totalCompleted++;
  }
});

console.log(`✅ إجمالي الملفات المكتملة: ${totalCompleted}/${allFiles.length}`);
console.log(`📈 نسبة الإنجاز الإجمالية: ${Math.round((totalCompleted / allFiles.length) * 100)}%`);

// الملفات المطلوبة التالية
console.log('\n📋 الملفات المطلوبة التالية (أولوية عالية):');
const nextFiles = [
  'backend/user-service/src/modules/profile/controllers/profile.controller.ts',
  'backend/user-service/src/modules/profile/services/profile.service.ts',
  'backend/notification-service/package.json',
  'backend/notification-service/src/main.ts',
  'backend/payment-gateway-service/package.json',
  'backend/payment-gateway-service/src/main.ts'
];

nextFiles.forEach((file, index) => {
  console.log(`   ${index + 1}. ${file}`);
});

// تحليل الخدمات المكتملة
console.log('\n🏗️ حالة الخدمات:');

const services = {
  'User Service': {
    files: [
      'backend/user-service/src/modules/users/dto/create-user.dto.ts',
      'backend/user-service/src/modules/users/controllers/users.controller.ts',
      'backend/user-service/src/modules/users/services/users.service.ts',
      'backend/user-service/src/common/guards/auth.guard.ts'
    ]
  },
  'Transfer Service': {
    files: [
      'backend/transfer-service/src/modules/transfers/dto/create-transfer.dto.ts',
      'backend/transfer-service/src/modules/transfers/controllers/transfers.controller.ts',
      'backend/transfer-service/src/modules/transfers/services/transfers.service.ts',
      'backend/transfer-service/src/common/guards/auth.guard.ts'
    ]
  },
  'Wallet Service': {
    files: [
      'backend/wallet-service/src/modules/wallets/dto/create-wallet.dto.ts',
      'backend/wallet-service/src/modules/wallets/controllers/wallets.controller.ts',
      'backend/wallet-service/src/modules/wallets/services/wallets.service.ts',
      'backend/wallet-service/src/common/guards/auth.guard.ts'
    ]
  }
};

Object.keys(services).forEach(serviceName => {
  const service = services[serviceName];
  let serviceCompleted = 0;
  
  service.files.forEach(file => {
    if (fs.existsSync(file)) {
      serviceCompleted++;
    }
  });
  
  const percentage = Math.round((serviceCompleted / service.files.length) * 100);
  const status = percentage === 100 ? '🟢' : percentage >= 75 ? '🟡' : '🔴';
  
  console.log(`   ${status} ${serviceName}: ${serviceCompleted}/${service.files.length} (${percentage}%)`);
});

console.log('\n🎯 التوصيات:');
if (completed === phase2Files.length) {
  console.log('🎉 ممتاز! تم إكمال المرحلة الثانية بنجاح');
  console.log('📝 الخطوة التالية: إنشاء خدمات جديدة (Notification, Payment Gateway)');
} else if (completed >= phase2Files.length * 0.8) {
  console.log('👍 جيد جداً! معظم ملفات المرحلة الثانية مكتملة');
  console.log('📝 إكمال الملفات المفقودة القليلة');
} else {
  console.log('⚠️  يحتاج المزيد من العمل في المرحلة الثانية');
  console.log('📝 التركيز على إكمال Controllers والServices');
}

console.log('\n✨ انتهى فحص التقدم - المرحلة الثانية!');
