"""
Redis Client for AI Engine
==========================
عميل Redis لمحرك الذكاء الاصطناعي
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta

import redis.asyncio as redis # type: ignore
from redis.asyncio import ConnectionPool # type: ignore

from core.config import settings

logger = logging.getLogger(__name__)


class RedisClient:
    """عميل Redis المتقدم"""
    
    def __init__(self):
        self.redis_client = None
        self.connection_pool = None
        self.connected = False
        
        # Statistics
        self.total_operations = 0
        self.cache_hits = 0
        self.cache_misses = 0
    
    async def connect(self):
        """الاتصال بـ Redis"""
        try:
            logger.info("🔌 Connecting to Redis...")
            
            # Create connection pool
            self.connection_pool = ConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=settings.REDIS_POOL_SIZE,
                socket_timeout=settings.REDIS_TIMEOUT,
                retry_on_timeout=settings.REDIS_RETRY_ON_TIMEOUT,
                decode_responses=True
            )
            
            # Create Redis client
            self.redis_client = redis.Redis(
                connection_pool=self.connection_pool
            )
            
            # Test connection
            await self.redis_client.ping()
            
            self.connected = True
            logger.info("✅ Redis connected successfully")
            
        except Exception as e:
            logger.error(f"❌ Redis connection failed: {e}")
            self.connected = False
            raise
    
    async def disconnect(self):
        """قطع الاتصال بـ Redis"""
        try:
            if self.redis_client:
                await self.redis_client.close()
            
            if self.connection_pool:
                await self.connection_pool.disconnect()
            
            self.connected = False
            logger.info("✅ Redis disconnected")
            
        except Exception as e:
            logger.error(f"❌ Redis disconnection failed: {e}")
    
    async def health_check(self) -> bool:
        """فحص صحة الاتصال"""
        try:
            if not self.redis_client:
                return False
            
            await self.redis_client.ping()
            return True
            
        except Exception as e:
            logger.error(f"❌ Redis health check failed: {e}")
            return False
    
    # Basic Operations
    async def get(self, key: str) -> Optional[str]:
        """الحصول على قيمة"""
        try:
            self.total_operations += 1
            
            value = await self.redis_client.get(key)
            
            if value is not None:
                self.cache_hits += 1
                logger.debug(f"📋 Cache hit: {key}")
                return value
            else:
                self.cache_misses += 1
                logger.debug(f"❌ Cache miss: {key}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Redis GET failed for key {key}: {e}")
            return None
    
    async def set(
        self,
        key: str,
        value: str,
        ex: Optional[int] = None,
        px: Optional[int] = None,
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """تعيين قيمة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.set(
                key, value, ex=ex, px=px, nx=nx, xx=xx
            )
            
            if result:
                logger.debug(f"✅ Cache set: {key}")
                return True
            else:
                logger.debug(f"❌ Cache set failed: {key}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Redis SET failed for key {key}: {e}")
            return False
    
    async def setex(self, key: str, time: int, value: str) -> bool:
        """تعيين قيمة مع انتهاء صلاحية"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.setex(key, time, value)
            
            if result:
                logger.debug(f"✅ Cache setex: {key} (TTL: {time}s)")
                return True
            else:
                logger.debug(f"❌ Cache setex failed: {key}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Redis SETEX failed for key {key}: {e}")
            return False
    
    async def delete(self, *keys: str) -> int:
        """حذف مفاتيح"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.delete(*keys)
            
            logger.debug(f"🗑️ Cache delete: {keys} (deleted: {result})")
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis DELETE failed for keys {keys}: {e}")
            return 0
    
    async def exists(self, *keys: str) -> int:
        """فحص وجود المفاتيح"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.exists(*keys)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis EXISTS failed for keys {keys}: {e}")
            return 0
    
    async def expire(self, key: str, time: int) -> bool:
        """تعيين انتهاء صلاحية"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.expire(key, time)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis EXPIRE failed for key {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """الحصول على وقت انتهاء الصلاحية"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.ttl(key)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis TTL failed for key {key}: {e}")
            return -1
    
    # JSON Operations
    async def get_json(self, key: str) -> Optional[Dict[str, Any]]:
        """الحصول على قيمة JSON"""
        try:
            value = await self.get(key)
            if value:
                return json.loads(value)
            return None
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON decode failed for key {key}: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Get JSON failed for key {key}: {e}")
            return None
    
    async def set_json(
        self,
        key: str,
        value: Dict[str, Any],
        ex: Optional[int] = None
    ) -> bool:
        """تعيين قيمة JSON"""
        try:
            json_value = json.dumps(value, ensure_ascii=False, default=str)
            return await self.set(key, json_value, ex=ex)
            
        except json.JSONEncodeError as e:
            logger.error(f"❌ JSON encode failed for key {key}: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Set JSON failed for key {key}: {e}")
            return False
    
    # Hash Operations
    async def hget(self, name: str, key: str) -> Optional[str]:
        """الحصول على قيمة من hash"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.hget(name, key)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis HGET failed for {name}:{key}: {e}")
            return None
    
    async def hset(self, name: str, key: str, value: str) -> bool:
        """تعيين قيمة في hash"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.hset(name, key, value)
            return result >= 0
            
        except Exception as e:
            logger.error(f"❌ Redis HSET failed for {name}:{key}: {e}")
            return False
    
    async def hgetall(self, name: str) -> Dict[str, str]:
        """الحصول على جميع قيم hash"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.hgetall(name)
            return result or {}
            
        except Exception as e:
            logger.error(f"❌ Redis HGETALL failed for {name}: {e}")
            return {}
    
    async def hmset(self, name: str, mapping: Dict[str, str]) -> bool:
        """تعيين عدة قيم في hash"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.hmset(name, mapping)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis HMSET failed for {name}: {e}")
            return False
    
    # List Operations
    async def lpush(self, name: str, *values: str) -> int:
        """إضافة قيم إلى بداية القائمة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.lpush(name, *values)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis LPUSH failed for {name}: {e}")
            return 0
    
    async def rpush(self, name: str, *values: str) -> int:
        """إضافة قيم إلى نهاية القائمة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.rpush(name, *values)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis RPUSH failed for {name}: {e}")
            return 0
    
    async def lrange(self, name: str, start: int, end: int) -> List[str]:
        """الحصول على نطاق من القائمة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.lrange(name, start, end)
            return result or []
            
        except Exception as e:
            logger.error(f"❌ Redis LRANGE failed for {name}: {e}")
            return []
    
    async def llen(self, name: str) -> int:
        """الحصول على طول القائمة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.llen(name)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis LLEN failed for {name}: {e}")
            return 0
    
    # Set Operations
    async def sadd(self, name: str, *values: str) -> int:
        """إضافة قيم إلى المجموعة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.sadd(name, *values)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis SADD failed for {name}: {e}")
            return 0
    
    async def smembers(self, name: str) -> set:
        """الحصول على جميع أعضاء المجموعة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.smembers(name)
            return result or set()
            
        except Exception as e:
            logger.error(f"❌ Redis SMEMBERS failed for {name}: {e}")
            return set()
    
    async def sismember(self, name: str, value: str) -> bool:
        """فحص عضوية في المجموعة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.sismember(name, value)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis SISMEMBER failed for {name}: {e}")
            return False
    
    # Sorted Set Operations
    async def zadd(self, name: str, mapping: Dict[str, float]) -> int:
        """إضافة قيم إلى المجموعة المرتبة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.zadd(name, mapping)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis ZADD failed for {name}: {e}")
            return 0
    
    async def zrange(
        self,
        name: str,
        start: int,
        end: int,
        withscores: bool = False
    ) -> List[Union[str, Tuple[str, float]]]:
        """الحصول على نطاق من المجموعة المرتبة"""
        try:
            self.total_operations += 1
            
            result = await self.redis_client.zrange(name, start, end, withscores=withscores)
            return result or []
            
        except Exception as e:
            logger.error(f"❌ Redis ZRANGE failed for {name}: {e}")
            return []
    
    # Cache Management
    async def clear_cache_pattern(self, pattern: str) -> int:
        """مسح الكاش حسب النمط"""
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                return await self.delete(*keys)
            return 0
            
        except Exception as e:
            logger.error(f"❌ Clear cache pattern failed for {pattern}: {e}")
            return 0
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الكاش"""
        try:
            info = await self.redis_client.info()
            
            return {
                "redis_version": info.get("redis_version"),
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": self._calculate_hit_rate(
                    info.get("keyspace_hits", 0),
                    info.get("keyspace_misses", 0)
                )
            }
            
        except Exception as e:
            logger.error(f"❌ Get cache info failed: {e}")
            return {}
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """حساب معدل الإصابة"""
        total = hits + misses
        if total == 0:
            return 0.0
        return hits / total
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات العميل"""
        cache_info = await self.get_cache_info()
        
        return {
            "connected": self.connected,
            "total_operations": self.total_operations,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "client_hit_rate": self.cache_hits / max(self.cache_hits + self.cache_misses, 1),
            "redis_info": cache_info
        }
