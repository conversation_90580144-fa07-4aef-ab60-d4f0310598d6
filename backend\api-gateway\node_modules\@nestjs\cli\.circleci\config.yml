version: 2

aliases:
  - &restore-cache
    restore_cache:
      key: dependency-cache-{{ checksum "package.json" }}
  - &install-deps
    run:
      name: Install dependencies
      command: npm ci --ignore-scripts
  - &build-packages
    run:
      name: Build
      command: npm run build
  - &run-unit-tests
    run:
      name: Test
      command: npm run test -- --runInBand --no-cache

jobs:
  build:
    working_directory: ~/nest
    docker:
      - image: cimg/node:22.3
    steps:
      - checkout
      - run:
          name: Update NPM version
          command: sudo npm install -g npm@latest
      - restore_cache:
          key: dependency-cache-{{ checksum "package.json" }}
      - run:
          name: Install dependencies
          command: npm ci --ignore-scripts
      - save_cache:
          key: dependency-cache-{{ checksum "package.json" }}
          paths:
            - ./node_modules
      - run:
          name: Build
          command: npm run build

  unit_tests:
    working_directory: ~/nest
    docker:
      - image: cimg/node:22.3
    steps:
      - checkout
      - *restore-cache
      - *install-deps
      - *build-packages
      - run:
          name: Clean build artifacts
          command: npm run clean
      - *run-unit-tests

workflows:
  version: 2
  build-and-test:
    jobs:
      - build
      - unit_tests:
          requires:
            - build
