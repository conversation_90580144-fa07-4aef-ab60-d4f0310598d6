{"version": 3, "file": "getExampleNumber.test.js", "names": ["examples", "type", "metadata", "getExampleNumber", "describe", "it", "phoneNumber", "nationalNumber", "should", "equal", "number", "countryCallingCode", "country", "expect", "to", "be", "undefined"], "sources": ["../source/getExampleNumber.test.js"], "sourcesContent": ["import examples from '../examples.mobile.json' assert { type: 'json' }\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport getExampleNumber from './getExampleNumber.js'\r\n\r\ndescribe('getExampleNumber', () => {\r\n\tit('should get an example number', () => {\r\n\t\tconst phoneNumber = getExampleNumber('RU', examples, metadata)\r\n\t\tphoneNumber.nationalNumber.should.equal('9123456789')\r\n\t\tphoneNumber.number.should.equal('+79123456789')\r\n\t\tphoneNumber.countryCallingCode.should.equal('7')\r\n\t\tphoneNumber.country.should.equal('RU')\r\n\t})\r\n\r\n\tit('should handle a non-existing country', () => {\r\n\t\texpect(getExampleNumber('XX', examples, metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AACA,OAAOC,QAAP,MAAqB,sBAArB,UAAqDD,IAAI,EAAE,MAA3D;AACA,OAAOE,gBAAP,MAA6B,uBAA7B;AAEAC,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxC,IAAMC,WAAW,GAAGH,gBAAgB,CAAC,IAAD,EAAOH,QAAP,EAAiBE,QAAjB,CAApC;IACAI,WAAW,CAACC,cAAZ,CAA2BC,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAH,WAAW,CAACI,MAAZ,CAAmBF,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;IACAH,WAAW,CAACK,kBAAZ,CAA+BH,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAH,WAAW,CAACM,OAAZ,CAAoBJ,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;EACA,CANC,CAAF;EAQAJ,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChDQ,MAAM,CAACV,gBAAgB,CAAC,IAAD,EAAOH,QAAP,EAAiBE,QAAjB,CAAjB,CAAN,CAAmDY,EAAnD,CAAsDC,EAAtD,CAAyDC,SAAzD;EACA,CAFC,CAAF;AAGA,CAZO,CAAR"}