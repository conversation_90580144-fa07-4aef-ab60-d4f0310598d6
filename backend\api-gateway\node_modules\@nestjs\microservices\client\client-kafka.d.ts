import { Logger } from '@nestjs/common/services/logger.service';
import { BrokersFunction, Consumer, ConsumerGroupJoinEvent, EachMessagePayload, Kaf<PERSON>, Producer, TopicPartitionOffsetAndMetadata } from '../external/kafka.interface';
import { KafkaParser } from '../helpers';
import { KafkaOptions, OutgoingEvent, ReadPacket, WritePacket } from '../interfaces';
import { ClientProxy } from './client-proxy';
import { Observable } from 'rxjs';
/**
 * @publicApi
 */
export declare class ClientKafka extends ClientProxy {
    protected readonly options: KafkaOptions['options'];
    protected logger: Logger;
    protected client: Kafka | null;
    protected consumer: Consumer | null;
    protected producer: Producer | null;
    protected parser: KafkaParser | null;
    protected initialized: Promise<void> | null;
    protected responsePatterns: string[];
    protected consumerAssignments: {
        [key: string]: number;
    };
    protected brokers: string[] | BrokersFunction;
    protected clientId: string;
    protected groupId: string;
    protected producerOnlyMode: boolean;
    constructor(options: KafkaOptions['options']);
    subscribeToResponseOf(pattern: any): void;
    close(): Promise<void>;
    connect(): Promise<Producer>;
    bindTopics(): Promise<void>;
    createClient<T = any>(): T;
    createResponseCallback(): (payload: EachMessagePayload) => any;
    getConsumerAssignments(): {
        [key: string]: number;
    };
    emitBatch<TResult = any, TInput = any>(pattern: any, data: {
        messages: TInput[];
    }): Observable<TResult>;
    commitOffsets(topicPartitions: TopicPartitionOffsetAndMetadata[]): Promise<void>;
    protected dispatchBatchEvent<TInput = any>(packets: ReadPacket<{
        messages: TInput[];
    }>): Promise<any>;
    protected dispatchEvent(packet: OutgoingEvent): Promise<any>;
    protected getReplyTopicPartition(topic: string): string;
    protected publish(partialPacket: ReadPacket, callback: (packet: WritePacket) => any): () => void;
    protected getResponsePatternName(pattern: string): string;
    protected setConsumerAssignments(data: ConsumerGroupJoinEvent): void;
    protected initializeSerializer(options: KafkaOptions['options']): void;
    protected initializeDeserializer(options: KafkaOptions['options']): void;
}
