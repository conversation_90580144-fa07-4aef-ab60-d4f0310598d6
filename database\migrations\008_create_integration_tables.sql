-- =====================================================
-- Migration: Create Integration Tables
-- Description: إنشاء جداول نظام التكامل والواجهات الخارجية
-- Version: 008
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- Bank Integration Tables
-- =====================================================

-- Bank accounts table
CREATE TABLE IF NOT EXISTS bank_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_number VARCHAR(50) NOT NULL,
    iban VARCHAR(34),
    bank_code VARCHAR(20) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_holder VARCHAR(200) NOT NULL,
    currency VARCHAR(3) DEFAULT 'SAR',
    account_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_account_bank UNIQUE (account_number, bank_code)
);

-- Bank transfers table
CREATE TABLE IF NOT EXISTS bank_transfers (
    id VARCHAR(50) PRIMARY KEY,
    from_account VARCHAR(50) NOT NULL,
    to_account VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) DEFAULT 'SAR',
    fees DECIMAL(10,2) DEFAULT 0.00,
    exchange_rate DECIMAL(10,6) DEFAULT 1.000000,
    reference VARCHAR(100),
    description TEXT,
    beneficiary_name VARCHAR(200),
    beneficiary_bank VARCHAR(100),
    purpose_code VARCHAR(10),
    status VARCHAR(20) DEFAULT 'pending',
    bank_reference VARCHAR(100),
    estimated_completion TIMESTAMP WITH TIME ZONE,
    error_code VARCHAR(50),
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_transfer_status CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'rejected', 'cancelled'))
);

-- Account balance history table
CREATE TABLE IF NOT EXISTS account_balance_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_number VARCHAR(50) NOT NULL,
    available_balance DECIMAL(15,2) NOT NULL,
    current_balance DECIMAL(15,2) NOT NULL,
    pending_balance DECIMAL(15,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'SAR',
    bank_reference VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Account statement history table
CREATE TABLE IF NOT EXISTS account_statement_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_number VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(100) NOT NULL,
    transaction_date TIMESTAMP WITH TIME ZONE NOT NULL,
    description TEXT,
    amount DECIMAL(15,2) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL,
    balance DECIMAL(15,2),
    reference VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_account_transaction UNIQUE (account_number, transaction_id),
    CONSTRAINT valid_transaction_type CHECK (transaction_type IN ('credit', 'debit'))
);

-- =====================================================
-- Partner API Tables
-- =====================================================

-- Partner configurations table
CREATE TABLE IF NOT EXISTS partner_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    partner_id VARCHAR(100) UNIQUE NOT NULL,
    partner_name VARCHAR(200) NOT NULL,
    partner_type VARCHAR(50) NOT NULL,
    api_version VARCHAR(10) NOT NULL,
    auth_method VARCHAR(20) NOT NULL,
    rate_limit VARCHAR(20) NOT NULL,
    api_key VARCHAR(200),
    secret_key VARCHAR(200),
    webhook_url TEXT,
    allowed_ips TEXT[],
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_partner_type CHECK (partner_type IN ('merchant', 'payment_processor', 'bank', 'fintech', 'government', 'enterprise')),
    CONSTRAINT valid_api_version CHECK (api_version IN ('v1', 'v2', 'v3')),
    CONSTRAINT valid_auth_method CHECK (auth_method IN ('api_key', 'jwt', 'oauth2', 'mutual_tls')),
    CONSTRAINT valid_rate_limit CHECK (rate_limit IN ('basic', 'standard', 'premium', 'enterprise'))
);

-- API request logs table
CREATE TABLE IF NOT EXISTS api_request_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id VARCHAR(50) NOT NULL,
    partner_id VARCHAR(100) NOT NULL,
    endpoint VARCHAR(200) NOT NULL,
    method VARCHAR(10) NOT NULL,
    headers JSONB DEFAULT '{}',
    body JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    status_code INTEGER,
    response_data JSONB DEFAULT '{}',
    processing_time DECIMAL(8,3),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT valid_http_method CHECK (method IN ('GET', 'POST', 'PUT', 'DELETE', 'PATCH'))
);

-- JWT tokens table
CREATE TABLE IF NOT EXISTS jwt_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    partner_id VARCHAR(100) NOT NULL,
    jti VARCHAR(100) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_revoked BOOLEAN DEFAULT false,
    revoked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_partner_jti UNIQUE (partner_id, jti)
);

-- Partner webhooks table
CREATE TABLE IF NOT EXISTS partner_webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    partner_id VARCHAR(100) NOT NULL,
    webhook_url TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_partner_webhook UNIQUE (partner_id)
);

-- =====================================================
-- Webhook Tables
-- =====================================================

-- Webhook endpoints table
CREATE TABLE IF NOT EXISTS webhook_endpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endpoint_id VARCHAR(100) UNIQUE NOT NULL,
    partner_id VARCHAR(100) NOT NULL,
    url TEXT NOT NULL,
    events TEXT[] NOT NULL,
    secret_key VARCHAR(200) NOT NULL,
    signature_method VARCHAR(20) DEFAULT 'hmac_sha256',
    is_active BOOLEAN DEFAULT true,
    max_retries INTEGER DEFAULT 3 CHECK (max_retries >= 0 AND max_retries <= 10),
    retry_delay INTEGER DEFAULT 60 CHECK (retry_delay >= 10 AND retry_delay <= 3600),
    timeout INTEGER DEFAULT 30 CHECK (timeout >= 5 AND timeout <= 300),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_signature_method CHECK (signature_method IN ('hmac_sha256', 'hmac_sha512', 'rsa_sha256'))
);

-- Webhook deliveries table
CREATE TABLE IF NOT EXISTS webhook_deliveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    delivery_id VARCHAR(50) UNIQUE NOT NULL,
    webhook_id VARCHAR(50) NOT NULL,
    endpoint_id VARCHAR(100) NOT NULL,
    partner_id VARCHAR(100) NOT NULL,
    event VARCHAR(50) NOT NULL,
    payload JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    attempts INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    response_status INTEGER,
    response_body TEXT,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_webhook_status CHECK (status IN ('pending', 'sending', 'delivered', 'failed', 'retrying', 'expired'))
);

-- =====================================================
-- Accounting Integration Tables
-- =====================================================

-- Accounting entries table
CREATE TABLE IF NOT EXISTS accounting_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entry_id VARCHAR(50) UNIQUE NOT NULL,
    transaction_id VARCHAR(100) NOT NULL,
    account_code VARCHAR(20) NOT NULL,
    account_name VARCHAR(200) NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0.00 CHECK (debit_amount >= 0),
    credit_amount DECIMAL(15,2) DEFAULT 0.00 CHECK (credit_amount >= 0),
    description TEXT NOT NULL,
    reference VARCHAR(100),
    transaction_date DATE NOT NULL,
    currency VARCHAR(3) DEFAULT 'SAR',
    exchange_rate DECIMAL(10,6) DEFAULT 1.000000,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    tax_type VARCHAR(20),
    metadata JSONB DEFAULT '{}',
    synced_systems JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_entry_amounts CHECK (
        (debit_amount > 0 AND credit_amount = 0) OR 
        (debit_amount = 0 AND credit_amount > 0)
    ),
    CONSTRAINT valid_tax_type CHECK (tax_type IS NULL OR tax_type IN ('vat', 'withholding', 'excise', 'zakat', 'income'))
);

-- Tax calculations table
CREATE TABLE IF NOT EXISTS tax_calculations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tax_id VARCHAR(50) UNIQUE NOT NULL,
    base_amount DECIMAL(15,2) NOT NULL CHECK (base_amount >= 0),
    tax_rate DECIMAL(5,4) NOT NULL CHECK (tax_rate >= 0 AND tax_rate <= 1),
    tax_amount DECIMAL(10,2) NOT NULL CHECK (tax_amount >= 0),
    tax_type VARCHAR(20) NOT NULL,
    currency VARCHAR(3) DEFAULT 'SAR',
    calculation_date DATE NOT NULL,
    is_inclusive BOOLEAN DEFAULT false,
    exemption_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_tax_type_calc CHECK (tax_type IN ('vat', 'withholding', 'excise', 'zakat', 'income'))
);

-- Invoices table
CREATE TABLE IF NOT EXISTS invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id VARCHAR(50) UNIQUE NOT NULL,
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    customer_id VARCHAR(100) NOT NULL,
    customer_name VARCHAR(200) NOT NULL,
    customer_vat_number VARCHAR(50),
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL CHECK (subtotal >= 0),
    tax_amount DECIMAL(10,2) NOT NULL CHECK (tax_amount >= 0),
    total_amount DECIMAL(15,2) NOT NULL CHECK (total_amount >= 0),
    currency VARCHAR(3) DEFAULT 'SAR',
    line_items JSONB NOT NULL,
    payment_terms VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ZATCA submissions table
CREATE TABLE IF NOT EXISTS zatca_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    invoice_hash VARCHAR(128),
    qr_code TEXT,
    zatca_reference VARCHAR(100),
    submission_time TIMESTAMP WITH TIME ZONE NOT NULL,
    response_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_zatca_status CHECK (status IN ('accepted', 'rejected', 'pending', 'failed'))
);

-- =====================================================
-- Industry Standards Tables
-- =====================================================

-- Standards configurations table
CREATE TABLE IF NOT EXISTS standards_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    standard VARCHAR(50) NOT NULL,
    version VARCHAR(20) NOT NULL,
    compliance_level VARCHAR(20) NOT NULL,
    enabled_features TEXT[] DEFAULT '{}',
    configuration JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_standard CHECK (standard IN ('iso_20022', 'swift_mt', 'pci_dss', 'gdpr', 'psd2', 'open_banking', 'fido2', 'oauth2', 'openid_connect', 'sama_regulations')),
    CONSTRAINT valid_compliance_level CHECK (compliance_level IN ('basic', 'standard', 'advanced', 'full'))
);

-- Message validations table
CREATE TABLE IF NOT EXISTS message_validations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    validation_id VARCHAR(50) UNIQUE NOT NULL,
    message_type VARCHAR(100) NOT NULL,
    format VARCHAR(20) NOT NULL,
    is_valid BOOLEAN NOT NULL,
    errors JSONB DEFAULT '[]',
    warnings JSONB DEFAULT '[]',
    validated_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_message_format CHECK (format IN ('xml', 'json', 'mt', 'csv', 'edi'))
);

-- Compliance checks table
CREATE TABLE IF NOT EXISTS compliance_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    check_id VARCHAR(50) UNIQUE NOT NULL,
    standard VARCHAR(50) NOT NULL,
    rule_name VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    details TEXT,
    checked_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_check_standard CHECK (standard IN ('iso_20022', 'swift_mt', 'pci_dss', 'gdpr', 'psd2', 'open_banking', 'fido2', 'oauth2', 'openid_connect', 'sama_regulations')),
    CONSTRAINT valid_severity CHECK (severity IN ('critical', 'high', 'medium', 'low')),
    CONSTRAINT valid_check_status CHECK (status IN ('passed', 'failed', 'warning'))
);

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- Bank integration indexes
CREATE INDEX IF NOT EXISTS idx_bank_accounts_bank_code ON bank_accounts(bank_code);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_iban ON bank_accounts(iban);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_active ON bank_accounts(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_bank_transfers_status ON bank_transfers(status);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_from_account ON bank_transfers(from_account);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_to_account ON bank_transfers(to_account);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_created_at ON bank_transfers(created_at);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_bank_reference ON bank_transfers(bank_reference);

CREATE INDEX IF NOT EXISTS idx_balance_history_account ON account_balance_history(account_number);
CREATE INDEX IF NOT EXISTS idx_balance_history_created_at ON account_balance_history(created_at);

CREATE INDEX IF NOT EXISTS idx_statement_history_account ON account_statement_history(account_number);
CREATE INDEX IF NOT EXISTS idx_statement_history_date ON account_statement_history(transaction_date);

-- Partner API indexes
CREATE INDEX IF NOT EXISTS idx_partner_configs_type ON partner_configs(partner_type);
CREATE INDEX IF NOT EXISTS idx_partner_configs_active ON partner_configs(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_api_logs_partner ON api_request_logs(partner_id);
CREATE INDEX IF NOT EXISTS idx_api_logs_endpoint ON api_request_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_logs_created_at ON api_request_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_api_logs_status ON api_request_logs(status_code);

CREATE INDEX IF NOT EXISTS idx_jwt_tokens_partner ON jwt_tokens(partner_id);
CREATE INDEX IF NOT EXISTS idx_jwt_tokens_jti ON jwt_tokens(jti);
CREATE INDEX IF NOT EXISTS idx_jwt_tokens_expires ON jwt_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_jwt_tokens_revoked ON jwt_tokens(is_revoked) WHERE is_revoked = false;

-- Webhook indexes
CREATE INDEX IF NOT EXISTS idx_webhook_endpoints_partner ON webhook_endpoints(partner_id);
CREATE INDEX IF NOT EXISTS idx_webhook_endpoints_active ON webhook_endpoints(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_partner ON webhook_deliveries(partner_id);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_status ON webhook_deliveries(status);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_event ON webhook_deliveries(event);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_created_at ON webhook_deliveries(created_at);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_retry ON webhook_deliveries(next_retry_at) WHERE status = 'retrying';

-- Accounting indexes
CREATE INDEX IF NOT EXISTS idx_accounting_entries_transaction ON accounting_entries(transaction_id);
CREATE INDEX IF NOT EXISTS idx_accounting_entries_account ON accounting_entries(account_code);
CREATE INDEX IF NOT EXISTS idx_accounting_entries_date ON accounting_entries(transaction_date);
CREATE INDEX IF NOT EXISTS idx_accounting_entries_tax_type ON accounting_entries(tax_type);

CREATE INDEX IF NOT EXISTS idx_tax_calculations_type ON tax_calculations(tax_type);
CREATE INDEX IF NOT EXISTS idx_tax_calculations_date ON tax_calculations(calculation_date);

CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_id);
CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_issue_date ON invoices(issue_date);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);

CREATE INDEX IF NOT EXISTS idx_zatca_submissions_invoice ON zatca_submissions(invoice_id);
CREATE INDEX IF NOT EXISTS idx_zatca_submissions_status ON zatca_submissions(status);
CREATE INDEX IF NOT EXISTS idx_zatca_submissions_time ON zatca_submissions(submission_time);

-- Standards indexes
CREATE INDEX IF NOT EXISTS idx_standards_configs_standard ON standards_configs(standard);
CREATE INDEX IF NOT EXISTS idx_standards_configs_active ON standards_configs(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_message_validations_type ON message_validations(message_type);
CREATE INDEX IF NOT EXISTS idx_message_validations_format ON message_validations(format);
CREATE INDEX IF NOT EXISTS idx_message_validations_valid ON message_validations(is_valid);
CREATE INDEX IF NOT EXISTS idx_message_validations_date ON message_validations(validated_at);

CREATE INDEX IF NOT EXISTS idx_compliance_checks_standard ON compliance_checks(standard);
CREATE INDEX IF NOT EXISTS idx_compliance_checks_status ON compliance_checks(status);
CREATE INDEX IF NOT EXISTS idx_compliance_checks_severity ON compliance_checks(severity);
CREATE INDEX IF NOT EXISTS idx_compliance_checks_date ON compliance_checks(checked_at);

-- =====================================================
-- GIN Indexes for JSONB columns
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_bank_transfers_metadata_gin ON bank_transfers USING GIN (metadata);
CREATE INDEX IF NOT EXISTS idx_partner_configs_metadata_gin ON partner_configs USING GIN (metadata);
CREATE INDEX IF NOT EXISTS idx_api_logs_headers_gin ON api_request_logs USING GIN (headers);
CREATE INDEX IF NOT EXISTS idx_api_logs_body_gin ON api_request_logs USING GIN (body);
CREATE INDEX IF NOT EXISTS idx_api_logs_response_gin ON api_request_logs USING GIN (response_data);
CREATE INDEX IF NOT EXISTS idx_webhook_endpoints_metadata_gin ON webhook_endpoints USING GIN (metadata);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_payload_gin ON webhook_deliveries USING GIN (payload);
CREATE INDEX IF NOT EXISTS idx_accounting_entries_metadata_gin ON accounting_entries USING GIN (metadata);
CREATE INDEX IF NOT EXISTS idx_accounting_entries_synced_gin ON accounting_entries USING GIN (synced_systems);
CREATE INDEX IF NOT EXISTS idx_invoices_line_items_gin ON invoices USING GIN (line_items);
CREATE INDEX IF NOT EXISTS idx_zatca_submissions_response_gin ON zatca_submissions USING GIN (response_data);
CREATE INDEX IF NOT EXISTS idx_standards_configs_configuration_gin ON standards_configs USING GIN (configuration);

-- =====================================================
-- Triggers for updated_at columns
-- =====================================================

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_bank_accounts_updated_at BEFORE UPDATE ON bank_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bank_transfers_updated_at BEFORE UPDATE ON bank_transfers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_partner_configs_updated_at BEFORE UPDATE ON partner_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_partner_webhooks_updated_at BEFORE UPDATE ON partner_webhooks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_webhook_endpoints_updated_at BEFORE UPDATE ON webhook_endpoints FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_webhook_deliveries_updated_at BEFORE UPDATE ON webhook_deliveries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON invoices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_standards_configs_updated_at BEFORE UPDATE ON standards_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Functions for Business Logic
-- =====================================================

-- Function to calculate transfer fees
CREATE OR REPLACE FUNCTION calculate_transfer_fees(
    p_amount DECIMAL,
    p_currency VARCHAR(3),
    p_is_domestic BOOLEAN DEFAULT true
)
RETURNS DECIMAL AS $$
DECLARE
    v_fee DECIMAL := 0.00;
BEGIN
    IF p_is_domestic THEN
        -- Domestic transfer fees
        IF p_amount <= 1000 THEN
            v_fee := 5.00;
        ELSIF p_amount <= 10000 THEN
            v_fee := 15.00;
        ELSE
            v_fee := 25.00;
        END IF;
    ELSE
        -- International transfer fees
        IF p_amount <= 1000 THEN
            v_fee := 25.00;
        ELSIF p_amount <= 10000 THEN
            v_fee := 50.00;
        ELSE
            v_fee := 75.00;
        END IF;
    END IF;
    
    RETURN v_fee;
END;
$$ LANGUAGE plpgsql;

-- Function to validate IBAN
CREATE OR REPLACE FUNCTION validate_iban(p_iban VARCHAR(34))
RETURNS BOOLEAN AS $$
DECLARE
    v_clean_iban VARCHAR(34);
    v_rearranged VARCHAR(34);
    v_numeric_string TEXT := '';
    v_char CHAR(1);
    v_remainder INTEGER;
BEGIN
    -- Remove spaces and convert to uppercase
    v_clean_iban := UPPER(REPLACE(p_iban, ' ', ''));
    
    -- Check length for Saudi IBAN (24 characters)
    IF LENGTH(v_clean_iban) != 24 THEN
        RETURN FALSE;
    END IF;
    
    -- Check country code
    IF LEFT(v_clean_iban, 2) != 'SA' THEN
        RETURN FALSE;
    END IF;
    
    -- Rearrange: move first 4 characters to end
    v_rearranged := SUBSTRING(v_clean_iban FROM 5) || LEFT(v_clean_iban, 4);
    
    -- Convert letters to numbers (A=10, B=11, ..., Z=35)
    FOR i IN 1..LENGTH(v_rearranged) LOOP
        v_char := SUBSTRING(v_rearranged FROM i FOR 1);
        IF v_char ~ '[0-9]' THEN
            v_numeric_string := v_numeric_string || v_char;
        ELSE
            v_numeric_string := v_numeric_string || (ASCII(v_char) - ASCII('A') + 10)::TEXT;
        END IF;
    END LOOP;
    
    -- Calculate mod 97
    v_remainder := (v_numeric_string::NUMERIC % 97)::INTEGER;
    
    RETURN v_remainder = 1;
END;
$$ LANGUAGE plpgsql;

-- Function to generate invoice number
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS VARCHAR(100) AS $$
DECLARE
    v_next_number INTEGER;
    v_invoice_number VARCHAR(100);
BEGIN
    -- Get next invoice number
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 5) AS INTEGER)), 0) + 1
    INTO v_next_number
    FROM invoices 
    WHERE invoice_number LIKE 'INV-%';
    
    -- Format invoice number
    v_invoice_number := 'INV-' || LPAD(v_next_number::TEXT, 6, '0');
    
    RETURN v_invoice_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Views for Reporting
-- =====================================================

-- Bank transfers summary view
CREATE OR REPLACE VIEW bank_transfers_summary AS
SELECT 
    DATE(created_at) as transfer_date,
    status,
    currency,
    COUNT(*) as transfer_count,
    SUM(amount) as total_amount,
    SUM(fees) as total_fees,
    AVG(amount) as avg_amount
FROM bank_transfers
GROUP BY DATE(created_at), status, currency
ORDER BY transfer_date DESC, status;

-- Partner API usage view
CREATE OR REPLACE VIEW partner_api_usage AS
SELECT 
    p.partner_id,
    p.partner_name,
    p.partner_type,
    DATE(l.created_at) as usage_date,
    COUNT(*) as request_count,
    COUNT(CASE WHEN l.status_code < 400 THEN 1 END) as successful_requests,
    COUNT(CASE WHEN l.status_code >= 400 THEN 1 END) as failed_requests,
    AVG(l.processing_time) as avg_processing_time
FROM partner_configs p
LEFT JOIN api_request_logs l ON p.partner_id = l.partner_id
GROUP BY p.partner_id, p.partner_name, p.partner_type, DATE(l.created_at)
ORDER BY usage_date DESC, request_count DESC;

-- Webhook delivery statistics view
CREATE OR REPLACE VIEW webhook_delivery_stats AS
SELECT 
    partner_id,
    event,
    DATE(created_at) as delivery_date,
    COUNT(*) as total_deliveries,
    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
    AVG(attempts) as avg_attempts
FROM webhook_deliveries
GROUP BY partner_id, event, DATE(created_at)
ORDER BY delivery_date DESC, total_deliveries DESC;

-- Financial summary view
CREATE OR REPLACE VIEW financial_summary AS
SELECT 
    DATE(transaction_date) as summary_date,
    SUM(CASE WHEN account_code LIKE '4%' THEN credit_amount ELSE 0 END) as total_revenue,
    SUM(CASE WHEN account_code LIKE '5%' THEN debit_amount ELSE 0 END) as total_expenses,
    SUM(CASE WHEN tax_type = 'vat' THEN tax_amount ELSE 0 END) as total_vat,
    COUNT(DISTINCT transaction_id) as transaction_count
FROM accounting_entries
GROUP BY DATE(transaction_date)
ORDER BY summary_date DESC;

-- Compliance summary view
CREATE OR REPLACE VIEW compliance_summary AS
SELECT 
    standard,
    DATE(checked_at) as check_date,
    COUNT(*) as total_checks,
    COUNT(CASE WHEN status = 'passed' THEN 1 END) as passed_checks,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_checks,
    COUNT(CASE WHEN status = 'warning' THEN 1 END) as warning_checks,
    ROUND((COUNT(CASE WHEN status = 'passed' THEN 1 END)::DECIMAL / COUNT(*)) * 100, 2) as compliance_rate
FROM compliance_checks
GROUP BY standard, DATE(checked_at)
ORDER BY check_date DESC, standard;

-- =====================================================
-- Comments for Documentation
-- =====================================================

COMMENT ON TABLE bank_accounts IS 'Bank accounts registered in the system';
COMMENT ON TABLE bank_transfers IS 'Bank transfer transactions';
COMMENT ON TABLE account_balance_history IS 'Historical balance information for bank accounts';
COMMENT ON TABLE account_statement_history IS 'Historical transaction statements from banks';

COMMENT ON TABLE partner_configs IS 'Partner API configurations and credentials';
COMMENT ON TABLE api_request_logs IS 'Logs of all API requests from partners';
COMMENT ON TABLE jwt_tokens IS 'JWT tokens issued to partners';
COMMENT ON TABLE partner_webhooks IS 'Webhook configurations for partners';

COMMENT ON TABLE webhook_endpoints IS 'Webhook endpoint configurations';
COMMENT ON TABLE webhook_deliveries IS 'Webhook delivery attempts and results';

COMMENT ON TABLE accounting_entries IS 'Double-entry accounting records';
COMMENT ON TABLE tax_calculations IS 'Tax calculation records';
COMMENT ON TABLE invoices IS 'Generated invoices';
COMMENT ON TABLE zatca_submissions IS 'ZATCA (Saudi tax authority) submission records';

COMMENT ON TABLE standards_configs IS 'Industry standards configurations';
COMMENT ON TABLE message_validations IS 'Message validation results';
COMMENT ON TABLE compliance_checks IS 'Compliance check results';

-- =====================================================
-- Migration Complete
-- =====================================================

-- Insert migration record
INSERT INTO schema_migrations (version, description, executed_at)
VALUES ('008', 'Create Integration Tables', CURRENT_TIMESTAMP)
ON CONFLICT (version) DO NOTHING;
