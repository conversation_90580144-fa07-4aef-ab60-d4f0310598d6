// This is a legacy function.
// Use `findNumbers()` instead.
import _findPhoneNumbers, { searchPhoneNumbers as _searchPhoneNumbers } from './findPhoneNumbersInitialImplementation.js';
import normalizeArguments from '../normalizeArguments.js';
export default function findPhoneNumbers() {
  var _normalizeArguments = normalizeArguments(arguments),
      text = _normalizeArguments.text,
      options = _normalizeArguments.options,
      metadata = _normalizeArguments.metadata;

  return _findPhoneNumbers(text, options, metadata);
}
/**
 * @return ES6 `for ... of` iterator.
 */

export function searchPhoneNumbers() {
  var _normalizeArguments2 = normalizeArguments(arguments),
      text = _normalizeArguments2.text,
      options = _normalizeArguments2.options,
      metadata = _normalizeArguments2.metadata;

  return _searchPhoneNumbers(text, options, metadata);
}
//# sourceMappingURL=findPhoneNumbers.js.map