{"version": 3, "file": "TokenReader.d.ts", "sourceRoot": "", "sources": ["../../src/parser/TokenReader.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,KAAK,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAErD;;;;;;;;;GASG;AACH,qBAAa,WAAW;IACtB,SAAgB,MAAM,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;IAE7C,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAgB;IAI/C,OAAO,CAAC,iBAAiB,CAAS;IAClC,OAAO,CAAC,eAAe,CAAS;IAGhC,OAAO,CAAC,aAAa,CAAS;IAG9B,OAAO,CAAC,sBAAsB,CAAS;gBAEpB,aAAa,EAAE,aAAa,EAAE,qBAAqB,CAAC,EAAE,aAAa;IAmBtF;;;OAGG;IACI,0BAA0B,IAAI,aAAa;IAoBlD;;;;;OAKG;IACI,0BAA0B,IAAI,OAAO;IAI5C;;;OAGG;IACI,6BAA6B,IAAI,aAAa,GAAG,SAAS;IAOjE;;;OAGG;IACI,gCAAgC,IAAI,IAAI;IAgB/C;;;OAGG;IACI,SAAS,IAAI,KAAK;IAIzB;;;OAGG;IACI,aAAa,IAAI,SAAS;IAOjC;;OAEG;IACI,kBAAkB,IAAI,SAAS;IAOtC;;OAEG;IACI,uBAAuB,IAAI,SAAS;IAO3C;;;;OAIG;IACI,SAAS,IAAI,KAAK;IAiBzB;;OAEG;IACI,qBAAqB,IAAI,SAAS;IAOzC;;OAEG;IACI,YAAY,IAAI,MAAM;IAI7B;;OAEG;IACI,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;CAW/C"}