{"version": 3, "file": "getNumberType.test.js", "names": ["describe", "it", "getNumberType", "nationalNumber", "country", "v2", "oldMetadata", "should", "equal", "expect", "to", "undefined"], "sources": ["../../source/helpers/getNumberType.test.js"], "sourcesContent": ["import getNumberType from './getNumberType.js'\r\n\r\nimport oldMetadata from '../../test/metadata/1.0.0/metadata.min.json' assert { type: 'json' }\r\n\r\nimport Metadata from '../metadata.js'\r\n\r\ndescribe('getNumberType', function() {\r\n\tit('should get number type when using old metadata', function() {\r\n\t\tgetNumberType(\r\n\t\t\t{\r\n\t\t\t\tnationalNumber: '2133734253',\r\n\t\t\t\tcountry: 'US'\r\n\t\t\t},\r\n\t\t\t{ v2: true },\r\n\t\t\toldMetadata\r\n\t\t).should.equal('FIXED_LINE_OR_MOBILE')\r\n\t})\r\n\r\n\tit('should return `undefined` when the phone number is a malformed one', function() {\r\n\t\texpect(getNumberType(\r\n\t\t\t{},\r\n\t\t\t{ v2: true },\r\n\t\t\toldMetadata\r\n\t\t)).to.equal(undefined)\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEA;;AAEA;;;;AAEAA,QAAQ,CAAC,eAAD,EAAkB,YAAW;EACpCC,EAAE,CAAC,gDAAD,EAAmD,YAAW;IAC/D,IAAAC,yBAAA,EACC;MACCC,cAAc,EAAE,YADjB;MAECC,OAAO,EAAE;IAFV,CADD,EAKC;MAAEC,EAAE,EAAE;IAAN,CALD,EAMCC,uBAND,EAOEC,MAPF,CAOSC,KAPT,CAOe,sBAPf;EAQA,CATC,CAAF;EAWAP,EAAE,CAAC,oEAAD,EAAuE,YAAW;IACnFQ,MAAM,CAAC,IAAAP,yBAAA,EACN,EADM,EAEN;MAAEG,EAAE,EAAE;IAAN,CAFM,EAGNC,uBAHM,CAAD,CAAN,CAIGI,EAJH,CAIMF,KAJN,CAIYG,SAJZ;EAKA,CANC,CAAF;AAOA,CAnBO,CAAR"}