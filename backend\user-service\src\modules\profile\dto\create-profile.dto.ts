import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsEnum,
  IsDateString,
  IsPhoneNumber,
  IsEmail,
  Length,
  Min,
  Max,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum IncomeSource {
  SALARY = 'salary',
  BUSINESS = 'business',
  INVESTMENTS = 'investments',
  FREELANCE = 'freelance',
  PENSION = 'pension',
  OTHER = 'other',
}

export enum EmploymentStatus {
  EMPLOYED = 'employed',
  SELF_EMPLOYED = 'self_employed',
  UNEMPLOYED = 'unemployed',
  STUDENT = 'student',
  RETIRED = 'retired',
}

class AddressDto {
  @ApiProperty({ description: 'العنوان الأول', example: 'شارع الملك فهد' })
  @IsString()
  @Length(1, 200)
  addressLine1: string;

  @ApiProperty({ description: 'العنوان الثاني', example: 'حي العليا', required: false })
  @IsOptional()
  @IsString()
  @Length(0, 200)
  addressLine2?: string;

  @ApiProperty({ description: 'المدينة', example: 'الرياض' })
  @IsString()
  @Length(1, 100)
  city: string;

  @ApiProperty({ description: 'المنطقة/الولاية', example: 'الرياض' })
  @IsString()
  @Length(1, 100)
  state: string;

  @ApiProperty({ description: 'الرمز البريدي', example: '12345' })
  @IsString()
  @Length(5, 10)
  postalCode: string;

  @ApiProperty({ description: 'الدولة', example: 'SA' })
  @IsString()
  @Length(2, 2)
  country: string;
}

export class CreateProfileDto {
  @ApiProperty({
    description: 'المهنة',
    example: 'مهندس برمجيات',
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  occupation?: string;

  @ApiProperty({
    description: 'جهة العمل',
    example: 'شركة التقنية المتقدمة',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(1, 200)
  employer?: string;

  @ApiProperty({
    description: 'حالة التوظيف',
    enum: EmploymentStatus,
    example: EmploymentStatus.EMPLOYED,
    required: false,
  })
  @IsOptional()
  @IsEnum(EmploymentStatus)
  employmentStatus?: EmploymentStatus;

  @ApiProperty({
    description: 'الدخل الشهري',
    example: 15000.00,
    minimum: 0,
    maximum: 1000000,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(1000000)
  monthlyIncome?: number;

  @ApiProperty({
    description: 'مصدر الدخل',
    enum: IncomeSource,
    example: IncomeSource.SALARY,
    required: false,
  })
  @IsOptional()
  @IsEnum(IncomeSource)
  sourceOfIncome?: IncomeSource;

  @ApiProperty({
    description: 'العنوان',
    type: AddressDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  address?: AddressDto;

  @ApiProperty({
    description: 'رقم هاتف الطوارئ',
    example: '+966501234568',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber(null)
  emergencyContactPhone?: string;

  @ApiProperty({
    description: 'اسم جهة الاتصال في الطوارئ',
    example: 'أحمد محمد',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  emergencyContactName?: string;

  @ApiProperty({
    description: 'علاقة جهة الاتصال في الطوارئ',
    example: 'أخ',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  emergencyContactRelation?: string;

  @ApiProperty({
    description: 'تاريخ بداية العمل',
    example: '2020-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  employmentStartDate?: string;

  @ApiProperty({
    description: 'المؤهل التعليمي',
    example: 'بكالوريوس هندسة',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(1, 200)
  education?: string;

  @ApiProperty({
    description: 'الحالة الاجتماعية',
    example: 'متزوج',
    enum: ['single', 'married', 'divorced', 'widowed'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['single', 'married', 'divorced', 'widowed'])
  maritalStatus?: string;

  @ApiProperty({
    description: 'عدد الأطفال',
    example: 2,
    minimum: 0,
    maximum: 20,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(20)
  numberOfDependents?: number;

  @ApiProperty({
    description: 'بيانات إضافية',
    example: { interests: ['technology', 'finance'], languages: ['ar', 'en'] },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
