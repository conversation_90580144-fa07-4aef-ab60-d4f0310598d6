export declare const constants: {
    O_RDONLY: number;
    O_WRONLY: number;
    O_RDWR: number;
    S_IFMT: number;
    S_IFREG: number;
    S_IFDIR: number;
    S_IFCHR: number;
    S_IFBLK: number;
    S_IFIFO: number;
    S_IFLNK: number;
    S_IFSOCK: number;
    O_CREAT: number;
    O_EXCL: number;
    O_NOCTTY: number;
    O_TRUNC: number;
    O_APPEND: number;
    O_DIRECTORY: number;
    O_NOATIME: number;
    O_NOFOLLOW: number;
    O_SYNC: number;
    O_DIRECT: number;
    O_NONBLOCK: number;
    S_IRWXU: number;
    S_IRUSR: number;
    S_IWUSR: number;
    S_IXUSR: number;
    S_IRWXG: number;
    S_IRGRP: number;
    S_IWGRP: number;
    S_IXGRP: number;
    S_IRWXO: number;
    S_IROTH: number;
    S_IWOTH: number;
    S_IXOTH: number;
    F_OK: number;
    R_OK: number;
    W_OK: number;
    X_OK: number;
    UV_FS_SYMLINK_DIR: number;
    UV_FS_SYMLINK_JUNCTION: number;
    UV_FS_COPYFILE_EXCL: number;
    UV_FS_COPYFILE_FICLONE: number;
    UV_FS_COPYFILE_FICLONE_FORCE: number;
    COPYFILE_EXCL: number;
    COPYFILE_FICLONE: number;
    COPYFILE_FICLONE_FORCE: number;
};
export declare const enum S {
    ISUID = 2048,
    ISGID = 1024,
    ISVTX = 512,
    IRUSR = 256,
    IWUSR = 128,
    IXUSR = 64,
    IRGRP = 32,
    IWGRP = 16,
    IXGRP = 8,
    IROTH = 4,
    IWOTH = 2,
    IXOTH = 1
}
