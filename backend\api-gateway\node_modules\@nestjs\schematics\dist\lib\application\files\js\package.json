{"name": "<%= name %>", "version": "<%= version %>", "description": "<%= description %>", "author": "<%= author %>", "private": true, "license": "UNLICENSED", "scripts": {"format": "prettier --write \"**/*.js\"", "start": "babel-node index.js", "start:dev": "nodemon", "test": "jest", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.2.0"}, "devDependencies": {"@nestjs/testing": "^10.0.0", "@babel/core": "7.26.0", "@babel/node": "7.26.0", "@babel/plugin-proposal-decorators": "7.25.9", "@babel/plugin-transform-runtime": "7.25.9", "@babel/preset-env": "7.26.0", "@babel/register": "7.25.9", "@babel/runtime": "7.26.0", "jest": "29.7.0", "nodemon": "3.1.7", "prettier": "3.3.3", "supertest": "7.0.0"}, "jest": {"moduleFileExtensions": ["js", "json"], "rootDir": "src", "testRegex": ".spec.js$", "coverageDirectory": "../coverage"}}