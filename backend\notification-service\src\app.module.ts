import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ScheduleModule } from '@nestjs/schedule';

// Modules
import { NotificationsModule } from './modules/notifications/notifications.module';
import { TemplatesModule } from './modules/templates/templates.module';
import { ChannelsModule } from './modules/channels/channels.module';
import { DeliveryModule } from './modules/delivery/delivery.module';

// Entities
import { Notification } from './modules/notifications/entities/notification.entity';
import { NotificationTemplate } from './modules/templates/entities/template.entity';
import { NotificationChannel } from './modules/channels/entities/channel.entity';
import { DeliveryLog } from './modules/delivery/entities/delivery-log.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('NOTIFICATION_DB_HOST', 'localhost'),
        port: configService.get('NOTIFICATION_DB_PORT', 5432),
        username: configService.get('NOTIFICATION_DB_USERNAME', 'postgres'),
        password: configService.get('NOTIFICATION_DB_PASSWORD', 'password'),
        database: configService.get('NOTIFICATION_DB_NAME', 'ws_notifications'),
        entities: [
          Notification,
          NotificationTemplate,
          NotificationChannel,
          DeliveryLog,
        ],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development',
        ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
      }),
      inject: [ConfigService],
    }),

    // Redis & Bull Queue
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD'),
          db: configService.get('NOTIFICATION_REDIS_DB', 2),
        },
      }),
      inject: [ConfigService],
    }),

    // JWT Authentication
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),

    // Passport
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // Task Scheduling
    ScheduleModule.forRoot(),

    // Feature Modules
    NotificationsModule,
    TemplatesModule,
    ChannelsModule,
    DeliveryModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    console.log('🔔 Notification Service initialized');
    console.log(`📧 Email enabled: ${this.configService.get('EMAIL_ENABLED', 'false')}`);
    console.log(`📱 SMS enabled: ${this.configService.get('SMS_ENABLED', 'false')}`);
    console.log(`🔔 Push enabled: ${this.configService.get('PUSH_ENABLED', 'false')}`);
    console.log(`📊 Database: ${this.configService.get('NOTIFICATION_DB_NAME', 'ws_notifications')}`);
  }
}
