import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum NotificationType {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
  WEBHOOK = 'webhook',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('notifications')
@Index(['userId', 'status'])
@Index(['type', 'status'])
@Index(['createdAt'])
export class Notification {
  @ApiProperty({ description: 'معرف الإشعار' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'معرف المستخدم' })
  @Column('uuid')
  @Index()
  userId: string;

  @ApiProperty({ description: 'نوع الإشعار', enum: NotificationType })
  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @ApiProperty({ description: 'حالة الإشعار', enum: NotificationStatus })
  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  status: NotificationStatus;

  @ApiProperty({ description: 'أولوية الإشعار', enum: NotificationPriority })
  @Column({
    type: 'enum',
    enum: NotificationPriority,
    default: NotificationPriority.NORMAL,
  })
  priority: NotificationPriority;

  @ApiProperty({ description: 'عنوان الإشعار' })
  @Column({ length: 500 })
  title: string;

  @ApiProperty({ description: 'محتوى الإشعار' })
  @Column('text')
  content: string;

  @ApiProperty({ description: 'معرف القالب المستخدم', required: false })
  @Column('uuid', { nullable: true })
  templateId?: string;

  @ApiProperty({ description: 'قناة الإرسال', required: false })
  @Column({ length: 100, nullable: true })
  channel?: string;

  @ApiProperty({ description: 'المستقبل (email, phone, etc.)', required: false })
  @Column({ length: 255, nullable: true })
  recipient?: string;

  @ApiProperty({ description: 'بيانات إضافية للإشعار', required: false })
  @Column('jsonb', { nullable: true })
  data?: Record<string, any>;

  @ApiProperty({ description: 'بيانات وصفية', required: false })
  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'تاريخ الإرسال المجدول', required: false })
  @Column('timestamp', { nullable: true })
  scheduledAt?: Date;

  @ApiProperty({ description: 'تاريخ الإرسال الفعلي', required: false })
  @Column('timestamp', { nullable: true })
  sentAt?: Date;

  @ApiProperty({ description: 'تاريخ التسليم', required: false })
  @Column('timestamp', { nullable: true })
  deliveredAt?: Date;

  @ApiProperty({ description: 'تاريخ القراءة', required: false })
  @Column('timestamp', { nullable: true })
  readAt?: Date;

  @ApiProperty({ description: 'رسالة الخطأ في حالة الفشل', required: false })
  @Column('text', { nullable: true })
  errorMessage?: string;

  @ApiProperty({ description: 'عدد محاولات الإرسال' })
  @Column('int', { default: 0 })
  attempts: number;

  @ApiProperty({ description: 'الحد الأقصى لمحاولات الإرسال' })
  @Column('int', { default: 3 })
  maxAttempts: number;

  @ApiProperty({ description: 'تاريخ المحاولة التالية', required: false })
  @Column('timestamp', { nullable: true })
  nextAttemptAt?: Date;

  @ApiProperty({ description: 'تاريخ انتهاء صلاحية الإشعار', required: false })
  @Column('timestamp', { nullable: true })
  expiresAt?: Date;

  @ApiProperty({ description: 'هل تم قراءة الإشعار' })
  @Column('boolean', { default: false })
  isRead: boolean;

  @ApiProperty({ description: 'هل الإشعار مؤرشف' })
  @Column('boolean', { default: false })
  isArchived: boolean;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'تاريخ آخر تحديث' })
  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  markAsSent(): void {
    this.status = NotificationStatus.SENT;
    this.sentAt = new Date();
  }

  markAsDelivered(): void {
    this.status = NotificationStatus.DELIVERED;
    this.deliveredAt = new Date();
  }

  markAsFailed(errorMessage: string): void {
    this.status = NotificationStatus.FAILED;
    this.errorMessage = errorMessage;
  }

  markAsRead(): void {
    this.isRead = true;
    this.readAt = new Date();
  }

  canRetry(): boolean {
    return this.attempts < this.maxAttempts && 
           this.status === NotificationStatus.FAILED &&
           (!this.expiresAt || this.expiresAt > new Date());
  }

  incrementAttempts(): void {
    this.attempts++;
    // Calculate next attempt time with exponential backoff
    const backoffMinutes = Math.pow(2, this.attempts) * 5; // 5, 10, 20, 40 minutes
    this.nextAttemptAt = new Date(Date.now() + backoffMinutes * 60 * 1000);
  }

  isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  shouldSendNow(): boolean {
    if (this.status !== NotificationStatus.PENDING) return false;
    if (this.isExpired()) return false;
    if (this.scheduledAt && this.scheduledAt > new Date()) return false;
    return true;
  }
}
