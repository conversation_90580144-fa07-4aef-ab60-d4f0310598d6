const express = require('express');
const cors = require('cors');
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'WS Transfir API Server is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Authentication endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  console.log('Login attempt:', { email, password });
  
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      success: true,
      token: 'admin-jwt-token-mock',
      user: {
        id: '1',
        firstName: 'مدير',
        lastName: 'النظام',
        email: email,
        role: 'admin',
        isVerified: true
      }
    });
  } else if (email && password) {
    res.json({
      success: true,
      token: 'user-jwt-token-mock',
      user: {
        id: '2',
        firstName: 'أحمد',
        lastName: 'محمد',
        email: email,
        role: 'user',
        isVerified: true
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة'
    });
  }
});

app.post('/api/auth/register', (req, res) => {
  const { firstName, lastName, email, password, phone } = req.body;
  
  console.log('Registration attempt:', { firstName, lastName, email, phone });
  
  res.json({
    success: true,
    message: 'تم إنشاء الحساب بنجاح',
    user: {
      id: Date.now().toString(),
      firstName,
      lastName,
      email,
      phone,
      isVerified: false
    }
  });
});

// Profile endpoints
app.get('/api/profile/me', (req, res) => {
  res.json({
    id: '1',
    firstName: 'أحمد',
    lastName: 'محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    dateOfBirth: '1990-01-01',
    nationality: 'SA',
    isVerified: true,
    completionPercentage: 85,
    address: {
      addressLine1: 'شارع الملك فهد',
      city: 'الرياض',
      country: 'SA',
      postalCode: '12345'
    }
  });
});

// Transfers endpoints
app.get('/api/transfers', (req, res) => {
  const mockTransfers = [
    {
      id: '1',
      referenceNumber: 'WS20241225001',
      amount: '1,500.00',
      currency: 'SAR',
      receiverName: 'أحمد محمد',
      receiverCountry: 'مصر',
      status: 'completed',
      createdAt: '2024-12-25T10:30:00Z',
      fees: '15.00'
    },
    {
      id: '2',
      referenceNumber: 'WS20241224002',
      amount: '750.00',
      currency: 'USD',
      receiverName: 'فاطمة علي',
      receiverCountry: 'الأردن',
      status: 'pending',
      createdAt: '2024-12-24T15:45:00Z',
      fees: '12.50'
    }
  ];
  
  res.json({
    data: mockTransfers,
    total: mockTransfers.length,
    page: 1,
    totalPages: 1
  });
});

app.get('/api/transfers/stats', (req, res) => {
  res.json({
    totalTransfers: 24,
    totalAmount: '45,230.50',
    pendingTransfers: 3,
    completedTransfers: 21
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'حدث خطأ في الخادم'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود'
  });
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log('');
  console.log('🚀 ═══════════════════════════════════════');
  console.log('🚀 WS TRANSFIR API SERVER RUNNING');
  console.log('🚀 ═══════════════════════════════════════');
  console.log(`🌐 Server: http://localhost:${PORT}`);
  console.log(`📊 Health: http://localhost:${PORT}/api/health`);
  console.log('🔐 Admin: <EMAIL> / admin123');
  console.log('👤 User: <EMAIL> / password123');
  console.log('🚀 ═══════════════════════════════════════');
  console.log('');
});
