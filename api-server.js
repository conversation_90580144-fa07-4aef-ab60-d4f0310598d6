const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');

const app = express();

// Security Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// Compression
app.use(compression());

// Logging
app.use(morgan('combined'));

// Rate Limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP',
    message: 'Please try again later',
    retryAfter: 15 * 60, // 15 minutes in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// CORS Configuration
app.use(cors({
  origin: [
    'http://localhost:3100',
    'http://localhost:3000',
    'http://127.0.0.1:3100',
    'http://127.0.0.1:3000',
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
  ],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const ip = req.ip || req.connection.remoteAddress;
  const userAgent = req.get('User-Agent') || 'Unknown';

  console.log(`[${timestamp}] ${method} ${url} - ${ip} - ${userAgent}`);

  // Add request ID for tracking
  req.requestId = require('uuid').v4();
  res.setHeader('X-Request-ID', req.requestId);

  next();
});

// Error handling middleware
app.use((err, req, res, next) => {
  const timestamp = new Date().toISOString();
  const requestId = req.requestId || 'unknown';

  console.error(`[${timestamp}] Error ${requestId}:`, err);

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV !== 'production';

  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal Server Error',
    requestId,
    timestamp,
    ...(isDevelopment && { stack: err.stack }),
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();

  res.json({
    status: 'OK',
    message: 'WS Transfir API Server is running successfully',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: {
      seconds: Math.floor(uptime),
      human: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
    },
    memory: {
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
    },
    system: {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      pid: process.pid,
    },
    services: {
      api: 'healthy',
      database: 'simulated',
      cache: 'simulated',
      notifications: 'simulated',
    },
    endpoints: {
      health: '/api/health',
      auth: '/api/auth/*',
      profile: '/api/profile/*',
      transfers: '/api/transfers/*',
    },
  });
});

// Input validation middleware
const validateLoginInput = (req, res, next) => {
  const { email, password } = req.body;
  const errors = [];

  if (!email) {
    errors.push('البريد الإلكتروني مطلوب');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('البريد الإلكتروني غير صحيح');
  }

  if (!password) {
    errors.push('كلمة المرور مطلوبة');
  } else if (password.length < 6) {
    errors.push('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'بيانات غير صحيحة',
      errors,
      timestamp: new Date().toISOString(),
    });
  }

  next();
};

// Authentication endpoints
app.post('/api/auth/login', validateLoginInput, (req, res) => {
  const { email, password } = req.body;
  const timestamp = new Date().toISOString();
  const requestId = req.requestId;

  console.log(`[${timestamp}] Login attempt: ${email} (Request ID: ${requestId})`);

  // Simulate authentication delay
  setTimeout(() => {
    if (email === '<EMAIL>' && password === 'admin123') {
      const token = `admin-jwt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      res.json({
        success: true,
        message: 'تم تسجيل الدخول بنجاح',
        token,
        refreshToken: `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        expiresIn: 3600, // 1 hour
        user: {
          id: '1',
          firstName: 'مدير',
          lastName: 'النظام',
          email: email,
          role: 'admin',
          isVerified: true,
          avatar: null,
          lastLogin: timestamp,
          permissions: [
            'read:all',
            'write:all',
            'delete:all',
            'admin:panel',
            'user:management',
            'system:settings',
          ],
        },
        timestamp,
        requestId,
      });
    } else if (email && password) {
      const token = `user-jwt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      res.json({
        success: true,
        message: 'تم تسجيل الدخول بنجاح',
        token,
        refreshToken: `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        expiresIn: 3600, // 1 hour
        user: {
          id: '2',
          firstName: 'أحمد',
          lastName: 'محمد',
          email: email,
          role: 'user',
          isVerified: true,
          avatar: null,
          lastLogin: timestamp,
          permissions: [
            'read:own',
            'write:own',
            'transfer:create',
            'transfer:view',
            'profile:edit',
          ],
        },
        timestamp,
        requestId,
      });
    } else {
      res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة',
        error: 'INVALID_CREDENTIALS',
        timestamp,
        requestId,
      });
    }
  }, 500); // Simulate network delay
});

app.post('/api/auth/register', (req, res) => {
  const { firstName, lastName, email, password, phone } = req.body;
  
  console.log('Registration attempt:', { firstName, lastName, email, phone });
  
  res.json({
    success: true,
    message: 'تم إنشاء الحساب بنجاح',
    user: {
      id: Date.now().toString(),
      firstName,
      lastName,
      email,
      phone,
      isVerified: false
    }
  });
});

// Profile endpoints
app.get('/api/profile/me', (req, res) => {
  res.json({
    id: '1',
    firstName: 'أحمد',
    lastName: 'محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    dateOfBirth: '1990-01-01',
    nationality: 'SA',
    isVerified: true,
    completionPercentage: 85,
    address: {
      addressLine1: 'شارع الملك فهد',
      city: 'الرياض',
      country: 'SA',
      postalCode: '12345'
    }
  });
});

// Transfers endpoints
app.get('/api/transfers', (req, res) => {
  const mockTransfers = [
    {
      id: '1',
      referenceNumber: 'WS20241225001',
      amount: '1,500.00',
      currency: 'SAR',
      receiverName: 'أحمد محمد',
      receiverCountry: 'مصر',
      status: 'completed',
      createdAt: '2024-12-25T10:30:00Z',
      fees: '15.00'
    },
    {
      id: '2',
      referenceNumber: 'WS20241224002',
      amount: '750.00',
      currency: 'USD',
      receiverName: 'فاطمة علي',
      receiverCountry: 'الأردن',
      status: 'pending',
      createdAt: '2024-12-24T15:45:00Z',
      fees: '12.50'
    }
  ];
  
  res.json({
    data: mockTransfers,
    total: mockTransfers.length,
    page: 1,
    totalPages: 1
  });
});

app.get('/api/transfers/stats', (req, res) => {
  res.json({
    totalTransfers: 24,
    totalAmount: '45,230.50',
    pendingTransfers: 3,
    completedTransfers: 21
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'حدث خطأ في الخادم'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود'
  });
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log('');
  console.log('🚀 ═══════════════════════════════════════');
  console.log('🚀 WS TRANSFIR API SERVER RUNNING');
  console.log('🚀 ═══════════════════════════════════════');
  console.log(`🌐 Server: http://localhost:${PORT}`);
  console.log(`📊 Health: http://localhost:${PORT}/api/health`);
  console.log('🔐 Admin: <EMAIL> / admin123');
  console.log('👤 User: <EMAIL> / password123');
  console.log('🚀 ═══════════════════════════════════════');
  console.log('');
});
