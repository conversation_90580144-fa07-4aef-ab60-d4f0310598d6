{"name": "fork-ts-checker-webpack-plugin", "version": "9.0.2", "description": "Runs typescript type checker and linter on separate process.", "keywords": ["webpack", "plugin", "typescript", "typecheck", "ts-loader", "webpack", "fork", "fast"], "bugs": {"url": "https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/issues"}, "repository": {"type": "git", "url": "https://github.com/TypeStrong/fork-ts-checker-webpack-plugin.git"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/piotr-oles)", "<PERSON> <<EMAIL>> (https://johnnyreilly.com)"], "files": ["lib"], "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "cross-env rimraf lib && cross-env tsc --version && cross-env tsc", "lint": "cross-env eslint ./src ./test --ext .ts", "test": "yarn build && yarn test:unit && yarn test:e2e", "test:unit": "cross-env jest --config=test/unit/jest.config.js", "test:e2e": "npm pack && cross-env jest --config=test/e2e/jest.config.js --ci -i -b", "precommit": "cross-env lint-staged && yarn build && yarn test:unit", "commit": "cross-env git-cz", "semantic-release": "semantic-release", "prepare": "husky install"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.ts": "eslint --fix"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "dependencies": {"@babel/code-frame": "^7.16.7", "chalk": "^4.1.2", "chokidar": "^3.5.3", "cosmiconfig": "^8.2.0", "deepmerge": "^4.2.2", "fs-extra": "^10.0.0", "memfs": "^3.4.1", "minimatch": "^3.0.4", "node-abort-controller": "^3.0.1", "schema-utils": "^3.1.1", "semver": "^7.3.5", "tapable": "^2.2.1"}, "peerDependencies": {"typescript": ">3.6.0", "webpack": "^5.11.0"}, "devDependencies": {"@commitlint/config-conventional": "^16.0.0", "@semantic-release/commit-analyzer": "^8.0.1", "@semantic-release/exec": "^5.0.0", "@semantic-release/github": "^7.2.3", "@semantic-release/npm": "^7.1.3", "@semantic-release/release-notes-generator": "^9.0.3", "@types/babel__code-frame": "^7.0.3", "@types/cross-spawn": "^6.0.2", "@types/fs-extra": "^9.0.13", "@types/jest": "^27.4.0", "@types/json-schema": "^7.0.9", "@types/minimatch": "^3.0.5", "@types/mock-fs": "^4.13.1", "@types/node": "^16.4.13", "@types/rimraf": "^3.0.2", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "commitlint": "^16.1.0", "cross-env": "^7.0.3", "eslint": "^8.8.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "git-cz": "^4.8.0", "husky": "^7.0.4", "jest": "^27.4.7", "jest-circus": "^27.4.6", "jest-environment-node": "^27.4.6", "json-schema": "^0.4.0", "karton": "^0.4.1", "lint-staged": "^11.1.2", "mock-fs": "^5.1.2", "prettier": "^2.5.1", "rimraf": "^3.0.2", "semantic-release": "^17.4.4", "strip-ansi": "^6.0.0", "ts-jest": "^27.1.3", "typescript": "^4.5.5", "webpack": "^5.67.0"}, "engines": {"node": ">=12.13.0", "yarn": ">=1.0.0"}}