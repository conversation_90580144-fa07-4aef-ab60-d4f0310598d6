/**
 * WS Transfir System Diagnostics
 * أداة فحص شاملة للمشاكل البرمجية
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 فحص شامل للمشاكل البرمجية في نظام WS Transfir');
console.log('================================================');
console.log('');

// Function to check if file exists and is readable
function checkFile(filePath, description) {
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`✅ ${description}: موجود (${stats.size} bytes)`);
      return true;
    } else {
      console.log(`❌ ${description}: غير موجود`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${description}: خطأ في القراءة - ${error.message}`);
    return false;
  }
}

// Function to check JSON syntax
function checkJSONSyntax(filePath, description) {
  try {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      JSON.parse(content);
      console.log(`✅ ${description}: صيغة JSON صحيحة`);
      return true;
    } else {
      console.log(`❌ ${description}: الملف غير موجود`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${description}: خطأ في صيغة JSON - ${error.message}`);
    return false;
  }
}

// Function to check JavaScript syntax
function checkJSSyntax(filePath, description) {
  try {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Basic syntax checks
      const issues = [];
      
      // Check for common syntax errors
      if (content.includes('require(') && !content.includes('const') && !content.includes('var')) {
        issues.push('استخدام require بدون تعريف متغير');
      }
      
      // Check for unmatched brackets
      const openBrackets = (content.match(/\{/g) || []).length;
      const closeBrackets = (content.match(/\}/g) || []).length;
      if (openBrackets !== closeBrackets) {
        issues.push(`عدم تطابق الأقواس المجعدة: ${openBrackets} فتح، ${closeBrackets} إغلاق`);
      }
      
      // Check for unmatched parentheses
      const openParens = (content.match(/\(/g) || []).length;
      const closeParens = (content.match(/\)/g) || []).length;
      if (openParens !== closeParens) {
        issues.push(`عدم تطابق الأقواس العادية: ${openParens} فتح، ${closeParens} إغلاق`);
      }
      
      if (issues.length === 0) {
        console.log(`✅ ${description}: لا توجد مشاكل واضحة`);
        return true;
      } else {
        console.log(`⚠️ ${description}: مشاكل محتملة:`);
        issues.forEach(issue => console.log(`   - ${issue}`));
        return false;
      }
    } else {
      console.log(`❌ ${description}: الملف غير موجود`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${description}: خطأ في الفحص - ${error.message}`);
    return false;
  }
}

// Function to check dependencies
function checkDependencies() {
  console.log('📦 فحص Dependencies:');
  console.log('===================');
  
  // Check root package.json
  if (fs.existsSync('package.json')) {
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const deps = packageJson.dependencies || {};
      const devDeps = packageJson.devDependencies || {};
      
      console.log(`✅ Root package.json: ${Object.keys(deps).length} dependencies, ${Object.keys(devDeps).length} devDependencies`);
      
      // Check if node_modules exists
      if (fs.existsSync('node_modules')) {
        console.log('✅ Root node_modules: موجود');
      } else {
        console.log('❌ Root node_modules: غير موجود - يجب تشغيل npm install');
      }
    } catch (error) {
      console.log(`❌ Root package.json: خطأ في القراءة - ${error.message}`);
    }
  }
  
  // Check frontend package.json
  const frontendPackagePath = 'frontend/web-app/package.json';
  if (fs.existsSync(frontendPackagePath)) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(frontendPackagePath, 'utf8'));
      const deps = packageJson.dependencies || {};
      const devDeps = packageJson.devDependencies || {};
      
      console.log(`✅ Frontend package.json: ${Object.keys(deps).length} dependencies, ${Object.keys(devDeps).length} devDependencies`);
      
      // Check if node_modules exists
      if (fs.existsSync('frontend/web-app/node_modules')) {
        console.log('✅ Frontend node_modules: موجود');
      } else {
        console.log('❌ Frontend node_modules: غير موجود - يجب تشغيل npm install في frontend/web-app');
      }
    } catch (error) {
      console.log(`❌ Frontend package.json: خطأ في القراءة - ${error.message}`);
    }
  }
  
  console.log('');
}

// Function to check port availability
function checkPorts() {
  console.log('🌐 فحص المنافذ:');
  console.log('===============');
  
  const { exec } = require('child_process');
  
  return new Promise((resolve) => {
    exec('netstat -an | findstr ":3000\\|:3100"', (error, stdout, stderr) => {
      if (stdout) {
        const lines = stdout.split('\n').filter(line => line.trim());
        lines.forEach(line => {
          if (line.includes(':3000')) {
            console.log('⚠️ المنفذ 3000: مستخدم (قد يكون هناك خادم يعمل)');
          }
          if (line.includes(':3100')) {
            console.log('⚠️ المنفذ 3100: مستخدم (قد يكون هناك خادم يعمل)');
          }
        });
      } else {
        console.log('✅ المنافذ 3000 و 3100: متاحة');
      }
      console.log('');
      resolve();
    });
  });
}

// Main diagnostic function
async function runDiagnostics() {
  console.log('📅 التاريخ:', new Date().toLocaleDateString('ar-SA'));
  console.log('⏰ الوقت:', new Date().toLocaleTimeString('ar-SA'));
  console.log('');

  let issueCount = 0;
  let totalChecks = 0;

  // Check critical files
  console.log('📁 فحص الملفات الأساسية:');
  console.log('========================');
  
  const criticalFiles = [
    { path: 'package.json', desc: 'ملف package.json الجذر' },
    { path: 'api-server.js', desc: 'خادم API الرئيسي' },
    { path: 'start-simple.js', desc: 'ملف التشغيل البسيط' },
    { path: 'simple-frontend.html', desc: 'الواجهة الأمامية البسيطة' },
    { path: 'frontend/web-app/package.json', desc: 'ملف package.json للواجهة الأمامية' },
    { path: 'frontend/web-app/next.config.js', desc: 'إعدادات Next.js' },
    { path: 'frontend/web-app/src/pages/index.tsx', desc: 'الصفحة الرئيسية' }
  ];

  criticalFiles.forEach(file => {
    totalChecks++;
    if (!checkFile(file.path, file.desc)) {
      issueCount++;
    }
  });

  console.log('');

  // Check JSON syntax
  console.log('🔍 فحص صيغة JSON:');
  console.log('==================');
  
  const jsonFiles = [
    { path: 'package.json', desc: 'Root package.json' },
    { path: 'frontend/web-app/package.json', desc: 'Frontend package.json' }
  ];

  jsonFiles.forEach(file => {
    totalChecks++;
    if (!checkJSONSyntax(file.path, file.desc)) {
      issueCount++;
    }
  });

  console.log('');

  // Check JavaScript syntax
  console.log('🔍 فحص صيغة JavaScript:');
  console.log('========================');
  
  const jsFiles = [
    { path: 'api-server.js', desc: 'API Server' },
    { path: 'start-simple.js', desc: 'Simple Start Script' },
    { path: 'frontend/web-app/next.config.js', desc: 'Next.js Config' }
  ];

  jsFiles.forEach(file => {
    totalChecks++;
    if (!checkJSSyntax(file.path, file.desc)) {
      issueCount++;
    }
  });

  console.log('');

  // Check dependencies
  checkDependencies();

  // Check ports
  await checkPorts();

  // Summary
  console.log('📊 ملخص التشخيص:');
  console.log('=================');
  console.log(`🔍 إجمالي الفحوصات: ${totalChecks}`);
  console.log(`❌ المشاكل المكتشفة: ${issueCount}`);
  console.log(`✅ الفحوصات الناجحة: ${totalChecks - issueCount}`);
  
  const successRate = Math.round(((totalChecks - issueCount) / totalChecks) * 100);
  console.log(`📈 معدل النجاح: ${successRate}%`);
  console.log('');

  if (issueCount === 0) {
    console.log('🎉 ممتاز! لا توجد مشاكل برمجية واضحة');
    console.log('✅ النظام جاهز للتشغيل');
  } else if (issueCount <= 2) {
    console.log('⚠️ توجد مشاكل بسيطة يمكن حلها');
    console.log('🔧 النظام قابل للتشغيل مع بعض التحسينات');
  } else {
    console.log('🔴 توجد مشاكل متعددة تحتاج إلى حل');
    console.log('🛠️ يُنصح بحل المشاكل قبل التشغيل');
  }

  console.log('');
  console.log('💡 التوصيات:');
  console.log('=============');
  
  if (issueCount === 0) {
    console.log('🚀 يمكنك تشغيل النظام باستخدام:');
    console.log('   node start-simple.js');
    console.log('   أو');
    console.log('   .\final-check.bat');
  } else {
    console.log('🔧 خطوات الإصلاح المقترحة:');
    console.log('1. تأكد من تثبيت Node.js (الإصدار 18 أو أحدث)');
    console.log('2. شغل: npm install');
    console.log('3. شغل: cd frontend/web-app && npm install');
    console.log('4. أعد تشغيل هذا الفحص');
    console.log('5. جرب تشغيل النظام: node start-simple.js');
  }

  console.log('');
  console.log('🔄 لإعادة الفحص: node system-diagnostics.js');
  console.log('');
}

// Run diagnostics
runDiagnostics().catch(console.error);
