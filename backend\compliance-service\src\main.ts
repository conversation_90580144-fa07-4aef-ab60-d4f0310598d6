import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('ComplianceService');
  
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: [
      'http://localhost:3000',
      'http://localhost:3100',
      'https://ws-transfir.com',
      'https://app.ws-transfir.com',
    ],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true,
  });

  // API prefix
  app.setGlobalPrefix('api/v1');

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('WS Transfir Compliance Service')
    .setDescription('خدمة الامتثال والقانونية لنظام WS Transfir - KYC, AML, ومراقبة المعاملات')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag('KYC', 'التحقق من هوية العملاء')
    .addTag('AML', 'مكافحة غسل الأموال')
    .addTag('Sanctions', 'فحص العقوبات')
    .addTag('Risk Assessment', 'تقييم المخاطر')
    .addTag('Compliance Reports', 'تقارير الامتثال')
    .addTag('Document Verification', 'التحقق من الوثائق')
    .addTag('Health', 'فحص صحة النظام')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  // Health check endpoint
  app.getHttpAdapter().get('/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      service: 'compliance-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: configService.get('NODE_ENV'),
      version: '1.0.0',
      features: {
        kyc: configService.get('KYC_ENABLED') === 'true',
        aml: configService.get('AML_ENABLED') === 'true',
        sanctions: configService.get('SANCTIONS_SCREENING') === 'true',
        documentVerification: configService.get('DOCUMENT_VERIFICATION') === 'true',
        riskAssessment: configService.get('RISK_ASSESSMENT') === 'true',
      },
      integrations: {
        sanctionsApi: configService.get('SANCTIONS_API_ENABLED') === 'true',
        kycProvider: configService.get('KYC_PROVIDER_ENABLED') === 'true',
        documentOcr: configService.get('OCR_ENABLED') === 'true',
      },
    });
  });

  // Start server
  const port = configService.get('COMPLIANCE_SERVICE_PORT') || 3006;
  await app.listen(port);

  logger.log(`🚀 Compliance Service is running on: http://localhost:${port}`);
  logger.log(`📚 API Documentation: http://localhost:${port}/docs`);
  logger.log(`🏥 Health Check: http://localhost:${port}/health`);
  logger.log(`🌍 Environment: ${configService.get('NODE_ENV')}`);
  logger.log(`🛡️  Compliance Features: ${getComplianceFeatures(configService)}`);
  logger.log(`⚖️  Regulatory Standards: ${getRegulatoryStandards(configService)}`);
}

function getComplianceFeatures(configService: ConfigService): string {
  const features = [];
  
  if (configService.get('KYC_ENABLED') === 'true') features.push('KYC');
  if (configService.get('AML_ENABLED') === 'true') features.push('AML');
  if (configService.get('SANCTIONS_SCREENING') === 'true') features.push('Sanctions');
  if (configService.get('RISK_ASSESSMENT') === 'true') features.push('Risk Assessment');
  
  return features.length > 0 ? features.join(', ') : 'Basic Compliance';
}

function getRegulatoryStandards(configService: ConfigService): string {
  const standards = [];
  
  if (configService.get('SAMA_COMPLIANCE') === 'true') standards.push('SAMA');
  if (configService.get('FATF_COMPLIANCE') === 'true') standards.push('FATF');
  if (configService.get('GDPR_COMPLIANCE') === 'true') standards.push('GDPR');
  if (configService.get('PCI_DSS_COMPLIANCE') === 'true') standards.push('PCI DSS');
  
  return standards.length > 0 ? standards.join(', ') : 'Basic Standards';
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start Compliance Service:', error);
  process.exit(1);
});
