import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ThrottlerModule } from '@nestjs/throttler';
import { MulterModule } from '@nestjs/platform-express';

// Modules
import { UsersModule } from './modules/users/users.module';
import { ProfileModule } from './modules/profile/profile.module';
import { KycModule } from './modules/kyc/kyc.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { WalletModule } from './modules/wallet/wallet.module';

// Shared modules
import { DatabaseModule } from './shared/database/database.module';
import { RedisModule } from './shared/redis/redis.module';
import { LoggerModule } from './shared/logger/logger.module';
import { HealthModule } from './shared/health/health.module';
import { FileUploadModule } from './shared/file-upload/file-upload.module';

// Entities
import { User } from './modules/users/entities/user.entity';
import { UserProfile } from './modules/profile/entities/user-profile.entity';
import { KycDocument } from './modules/kyc/entities/kyc-document.entity';
import { UserDocument } from './modules/documents/entities/user-document.entity';
import { UserWallet } from './modules/wallet/entities/user-wallet.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env', '.env.local'],
      cache: true,
    }),
    
    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('POSTGRES_HOST'),
        port: configService.get('POSTGRES_PORT'),
        username: configService.get('POSTGRES_USER'),
        password: configService.get('POSTGRES_PASSWORD'),
        database: configService.get('POSTGRES_DB'),
        entities: [User, UserProfile, KycDocument, UserDocument, UserWallet],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development',
        ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
      }),
      inject: [ConfigService],
    }),
    
    // JWT
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
      global: true,
    }),
    
    // Passport
    PassportModule.register({ defaultStrategy: 'jwt' }),
    
    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ttl: parseInt(configService.get('RATE_LIMIT_WINDOW_MS')) / 1000,
        limit: parseInt(configService.get('RATE_LIMIT_MAX_REQUESTS')),
      }),
      inject: [ConfigService],
    }),
    
    // File upload
    MulterModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        dest: configService.get('UPLOAD_PATH'),
        limits: {
          fileSize: parseInt(configService.get('MAX_FILE_SIZE')),
        },
      }),
      inject: [ConfigService],
    }),
    
    // Shared modules
    DatabaseModule,
    RedisModule,
    LoggerModule,
    HealthModule,
    FileUploadModule,
    
    // Feature modules
    UsersModule,
    ProfileModule,
    KycModule,
    DocumentsModule,
    WalletModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
