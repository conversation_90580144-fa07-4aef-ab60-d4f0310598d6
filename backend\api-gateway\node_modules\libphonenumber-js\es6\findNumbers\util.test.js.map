{"version": 3, "file": "util.test.js", "names": ["limit", "trimAfterFirstMatch", "startsWith", "endsWith", "describe", "it", "thrower", "should", "equal"], "sources": ["../../source/findNumbers/util.test.js"], "sourcesContent": ["import {\r\n\tlimit,\r\n\ttrimAfterFirstMatch,\r\n\tstartsWith,\r\n\tendsWith\r\n} from './util.js'\r\n\r\ndescribe('findNumbers/util', () =>\r\n{\r\n\tit('should generate regexp limit', () =>\r\n\t{\r\n\t\tlet thrower = () => limit(1, 0)\r\n\t\tthrower.should.throw()\r\n\r\n\t\tthrower = () => limit(-1, 1)\r\n\t\tthrower.should.throw()\r\n\r\n\t\tthrower = () => limit(0, 0)\r\n\t\tthrower.should.throw()\r\n\t})\r\n\r\n\tit('should trimAfterFirstMatch', () =>\r\n\t{\r\n\t\ttrimAfterFirstMatch(/\\d/, 'abc123').should.equal('abc')\r\n\t\ttrimAfterFirstMatch(/\\d/, 'abc').should.equal('abc')\r\n\t})\r\n\r\n\tit('should determine if a string starts with a substring', () =>\r\n\t{\r\n\t\tstartsWith('𐍈123', '𐍈').should.equal(true)\r\n\t\tstartsWith('1𐍈', '𐍈').should.equal(false)\r\n\t})\r\n\r\n\tit('should determine if a string ends with a substring', () =>\r\n\t{\r\n\t\tendsWith('123𐍈', '𐍈').should.equal(true)\r\n\t\tendsWith('𐍈1', '𐍈').should.equal(false)\r\n\t})\r\n})"], "mappings": "AAAA,SACCA,KADD,EAECC,mBAFD,EAGCC,UAHD,EAICC,QAJD,QAKO,WALP;AAOAC,QAAQ,CAAC,kBAAD,EAAqB,YAC7B;EACCC,EAAE,CAAC,8BAAD,EAAiC,YACnC;IACC,IAAIC,OAAO,GAAG;MAAA,OAAMN,KAAK,CAAC,CAAD,EAAI,CAAJ,CAAX;IAAA,CAAd;;IACAM,OAAO,CAACC,MAAR;;IAEAD,OAAO,GAAG;MAAA,OAAMN,KAAK,CAAC,CAAC,CAAF,EAAK,CAAL,CAAX;IAAA,CAAV;;IACAM,OAAO,CAACC,MAAR;;IAEAD,OAAO,GAAG;MAAA,OAAMN,KAAK,CAAC,CAAD,EAAI,CAAJ,CAAX;IAAA,CAAV;;IACAM,OAAO,CAACC,MAAR;EACA,CAVC,CAAF;EAYAF,EAAE,CAAC,4BAAD,EAA+B,YACjC;IACCJ,mBAAmB,CAAC,IAAD,EAAO,QAAP,CAAnB,CAAoCM,MAApC,CAA2CC,KAA3C,CAAiD,KAAjD;IACAP,mBAAmB,CAAC,IAAD,EAAO,KAAP,CAAnB,CAAiCM,MAAjC,CAAwCC,KAAxC,CAA8C,KAA9C;EACA,CAJC,CAAF;EAMAH,EAAE,CAAC,sDAAD,EAAyD,YAC3D;IACCH,UAAU,CAAC,OAAD,EAAU,IAAV,CAAV,CAA0BK,MAA1B,CAAiCC,KAAjC,CAAuC,IAAvC;IACAN,UAAU,CAAC,KAAD,EAAQ,IAAR,CAAV,CAAwBK,MAAxB,CAA+BC,KAA/B,CAAqC,KAArC;EACA,CAJC,CAAF;EAMAH,EAAE,CAAC,oDAAD,EAAuD,YACzD;IACCF,QAAQ,CAAC,OAAD,EAAU,IAAV,CAAR,CAAwBI,MAAxB,CAA+BC,KAA/B,CAAqC,IAArC;IACAL,QAAQ,CAAC,KAAD,EAAQ,IAAR,CAAR,CAAsBI,MAAtB,CAA6BC,KAA7B,CAAmC,KAAnC;EACA,CAJC,CAAF;AAKA,CA/BO,CAAR"}