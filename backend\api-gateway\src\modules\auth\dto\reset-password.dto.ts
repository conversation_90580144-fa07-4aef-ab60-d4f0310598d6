import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  MinLength,
  Matches,
  Length,
} from 'class-validator';

export class ResetPasswordDto {
  @ApiProperty({
    description: 'البريد الإلكتروني',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'البريد الإلكتروني غير صحيح' })
  @IsNotEmpty({ message: 'البريد الإلكتروني مطلوب' })
  email: string;

  @ApiProperty({
    description: 'رمز التحقق',
    example: '123456',
  })
  @IsString({ message: 'رمز التحقق يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رمز التحقق مطلوب' })
  @Length(6, 6, { message: 'رمز التحقق يجب أن يكون 6 أرقام' })
  otp: string;

  @ApiProperty({
    description: 'كلمة المرور الجديدة',
    example: 'NewSecurePassword123!',
    minLength: 8,
  })
  @IsString({ message: 'كلمة المرور يجب أن تكون نص' })
  @IsNotEmpty({ message: 'كلمة المرور مطلوبة' })
  @MinLength(8, { message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    {
      message: 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص',
    },
  )
  newPassword: string;
}
