"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TsconfigPathsPlugin = void 0;
var plugin_1 = require("./plugin");
Object.defineProperty(exports, "TsconfigPathsPlugin", { enumerable: true, get: function () { return plugin_1.TsconfigPathsPlugin; } });
var plugin_2 = require("./plugin");
exports.default = plugin_2.TsconfigPathsPlugin;
// This is to make it importable in all these ways
// const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
// import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
// import { TsconfigPathsPlugin } from "tsconfig-paths-webpack-plugin";
var theClass = require("./plugin").TsconfigPathsPlugin;
theClass.TsconfigPathsPlugin = plugin_2.TsconfigPathsPlugin;
theClass.default = plugin_2.TsconfigPathsPlugin;
module.exports = theClass;
//# sourceMappingURL=index.js.map