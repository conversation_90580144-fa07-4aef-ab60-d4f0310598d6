{"version": 3, "file": "DocDeclarationReference.js", "sourceRoot": "", "sources": ["../../src/nodes/DocDeclarationReference.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,OAAO,EAAE,WAAW,EAA0D,MAAM,WAAW,CAAC;AAGzG,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAsBxD;;;;;;GAMG;AACH;IAA6C,2CAAO;IAYlD;;;OAGG;IACH,iCACE,UAAyF;;QAEzF,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,IAAI,UAAU,CAAC,kBAAkB,EAAE,CAAC;gBAClC,KAAI,CAAC,mBAAmB,GAAG,IAAI,UAAU,CAAC;oBACxC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,gCAAgC;oBACzD,OAAO,EAAE,UAAU,CAAC,kBAAkB;iBACvC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBACjC,KAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC;oBACvC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,+BAA+B;oBACxD,OAAO,EAAE,UAAU,CAAC,iBAAiB;iBACtC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBACjC,KAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC;oBACvC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,+BAA+B;oBACxD,OAAO,EAAE,UAAU,CAAC,iBAAiB;iBACtC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,6BAA6B,EAAE,CAAC;gBAC7C,KAAI,CAAC,8BAA8B,GAAG,IAAI,UAAU,CAAC;oBACnD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,6BAA6B;iBAClD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;YAC3C,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC;QAC3C,CAAC;QAED,KAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAChC,CAAA,KAAA,KAAI,CAAC,iBAAiB,CAAA,CAAC,IAAI,WAAI,UAAU,CAAC,gBAAgB,EAAE;QAC9D,CAAC;;IACH,CAAC;IAGD,sBAAW,yCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,oBAAoB,CAAC;QAC1C,CAAC;;;OAAA;IAOD,sBAAW,gDAAW;QALtB;;;;WAIG;aACH;YACE,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAI,IAAI,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;oBAC3C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAClE,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;;;OAAA;IAWD,sBAAW,+CAAU;QATrB;;;;;;;;WAQG;aACH;YACE,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;oBAC1C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAChE,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;;;OAAA;IAOD,sBAAW,qDAAgB;QAL3B;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,CAAC;;;OAAA;IAED;;OAEG;IACI,6CAAW,GAAlB;QACE,IAAM,aAAa,GAAkB,IAAI,aAAa,EAAE,CAAC;QACzD,IAAM,OAAO,GAAiB,IAAI,YAAY,EAAE,CAAC;QACjD,OAAO,CAAC,0BAA0B,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QACxD,OAAO,aAAa,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,gBAAgB;IACN,iDAAe,GAAzB;QACE;YACE,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,8BAA8B;WAChC,IAAI,CAAC,iBAAiB,QACzB;IACJ,CAAC;IACH,8BAAC;AAAD,CAAC,AA/HD,CAA6C,OAAO,GA+HnD", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNode, DocNodeKind, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { DocMemberReference } from './DocMemberReference';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\nimport { StringBuilder } from '../emitters/StringBuilder';\r\nimport { TSDocEmitter } from '../emitters/TSDocEmitter';\r\n\r\n/**\r\n * Constructor parameters for {@link DocDeclarationReference}.\r\n */\r\nexport interface IDocDeclarationReferenceParameters extends IDocNodeParameters {\r\n  packageName?: string;\r\n  importPath?: string;\r\n  memberReferences?: DocMemberReference[];\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocDeclarationReference}.\r\n */\r\nexport interface IDocDeclarationReferenceParsedParameters extends IDocNodeParsedParameters {\r\n  packageNameExcerpt?: TokenSequence;\r\n  importPathExcerpt?: TokenSequence;\r\n  importHashExcerpt?: TokenSequence;\r\n  spacingAfterImportHashExcerpt?: TokenSequence;\r\n  memberReferences?: DocMemberReference[];\r\n}\r\n\r\n/**\r\n * Represents a declaration reference.\r\n *\r\n * @remarks\r\n * Declaration references are TSDoc expressions used by tags such as `{@link}`\r\n * or `{@inheritDoc}` that need to refer to another declaration.\r\n */\r\nexport class DocDeclarationReference extends DocNode {\r\n  private _packageName: string | undefined;\r\n  private readonly _packageNameExcerpt: DocExcerpt | undefined;\r\n\r\n  private _importPath: string | undefined;\r\n  private readonly _importPathExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _importHashExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterImportHashExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _memberReferences: DocMemberReference[];\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(\r\n    parameters: IDocDeclarationReferenceParameters | IDocDeclarationReferenceParsedParameters\r\n  ) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      if (parameters.packageNameExcerpt) {\r\n        this._packageNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.DeclarationReference_PackageName,\r\n          content: parameters.packageNameExcerpt\r\n        });\r\n      }\r\n      if (parameters.importPathExcerpt) {\r\n        this._importPathExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.DeclarationReference_ImportPath,\r\n          content: parameters.importPathExcerpt\r\n        });\r\n      }\r\n      if (parameters.importHashExcerpt) {\r\n        this._importHashExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.DeclarationReference_ImportHash,\r\n          content: parameters.importHashExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterImportHashExcerpt) {\r\n        this._spacingAfterImportHashExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterImportHashExcerpt\r\n        });\r\n      }\r\n    } else {\r\n      this._packageName = parameters.packageName;\r\n      this._importPath = parameters.importPath;\r\n    }\r\n\r\n    this._memberReferences = [];\r\n    if (parameters.memberReferences) {\r\n      this._memberReferences.push(...parameters.memberReferences);\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.DeclarationReference;\r\n  }\r\n\r\n  /**\r\n   * The optional package name, which may optionally include an NPM scope.\r\n   *\r\n   * Example: `\"@scope/my-package\"`\r\n   */\r\n  public get packageName(): string | undefined {\r\n    if (this._packageName === undefined) {\r\n      if (this._packageNameExcerpt !== undefined) {\r\n        this._packageName = this._packageNameExcerpt.content.toString();\r\n      }\r\n    }\r\n    return this._packageName;\r\n  }\r\n\r\n  /**\r\n   * The optional import path.  If a package name is provided, then if an import path is provided,\r\n   * the path must start with a \"/\" delimiter; otherwise paths are resolved relative to the source file\r\n   * containing the reference.\r\n   *\r\n   * Example: `\"/path1/path2\"`\r\n   * Example: `\"./path1/path2\"`\r\n   * Example: `\"../path2/path2\"`\r\n   */\r\n  public get importPath(): string | undefined {\r\n    if (this._importPath === undefined) {\r\n      if (this._importPathExcerpt !== undefined) {\r\n        this._importPath = this._importPathExcerpt.content.toString();\r\n      }\r\n    }\r\n    return this._importPath;\r\n  }\r\n\r\n  /**\r\n   * The chain of member references that indicate the declaration being referenced.\r\n   * If this list is empty, then either the packageName or importPath must be provided,\r\n   * because the reference refers to a module.\r\n   */\r\n  public get memberReferences(): ReadonlyArray<DocMemberReference> {\r\n    return this._memberReferences;\r\n  }\r\n\r\n  /**\r\n   * Generates the TSDoc representation of this declaration reference.\r\n   */\r\n  public emitAsTsdoc(): string {\r\n    const stringBuilder: StringBuilder = new StringBuilder();\r\n    const emitter: TSDocEmitter = new TSDocEmitter();\r\n    emitter.renderDeclarationReference(stringBuilder, this);\r\n    return stringBuilder.toString();\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this._packageNameExcerpt,\r\n      this._importPathExcerpt,\r\n      this._importHashExcerpt,\r\n      this._spacingAfterImportHashExcerpt,\r\n      ...this._memberReferences\r\n    ];\r\n  }\r\n}\r\n"]}