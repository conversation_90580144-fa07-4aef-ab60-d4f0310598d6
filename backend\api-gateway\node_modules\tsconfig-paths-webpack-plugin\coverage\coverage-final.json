{"/home/<USER>/code/github/dividab/tsconfig-paths-webpack-plugin/src/index.ts": {"path": "/home/<USER>/code/github/dividab/tsconfig-paths-webpack-plugin/src/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 47}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 47}}, "3": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 35}}, "4": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 56}}, "5": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 51}}, "6": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 39}}, "7": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 26}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 28}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 47}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/code/github/dividab/tsconfig-paths-webpack-plugin/src/logger.ts": {"path": "/home/<USER>/code/github/dividab/tsconfig-paths-webpack-plugin/src/logger.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": null}}, "2": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "3": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "4": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": null}}, "5": {"start": {"line": 23, "column": 22}, "end": {"line": 23, "column": 49}}, "6": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 49}}, "7": {"start": {"line": 26, "column": 24}, "end": {"line": 28, "column": 1}}, "8": {"start": {"line": 30, "column": 23}, "end": {"line": 31, "column": 9}}, "9": {"start": {"line": 31, "column": 2}, "end": {"line": 35, "column": 72}}, "10": {"start": {"line": 35, "column": 48}, "end": {"line": 35, "column": 72}}, "11": {"start": {"line": 37, "column": 27}, "end": {"line": 40, "column": 18}}, "12": {"start": {"line": 40, "column": 17}, "end": {"line": 44, "column": 4}}, "13": {"start": {"line": 41, "column": 2}, "end": {"line": 44, "column": 4}}, "14": {"start": {"line": 46, "column": 20}, "end": {"line": 51, "column": 10}}, "15": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 22}}, "16": {"start": {"line": 53, "column": 8}, "end": {"line": 56, "column": null}}, "17": {"start": {"line": 59, "column": 21}, "end": {"line": 64, "column": 10}}, "18": {"start": {"line": 64, "column": 2}, "end": {"line": 66, "column": 22}}, "19": {"start": {"line": 65, "column": 27}, "end": {"line": 65, "column": null}}, "20": {"start": {"line": 68, "column": 23}, "end": {"line": 73, "column": 10}}, "21": {"start": {"line": 73, "column": 2}, "end": {"line": 75, "column": 22}}, "22": {"start": {"line": 74, "column": 27}, "end": {"line": 74, "column": null}}, "23": {"start": {"line": 78, "column": 17}, "end": {"line": 78, "column": 40}}, "24": {"start": {"line": 79, "column": 2}, "end": {"line": 84, "column": 4}}, "25": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 16}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 5}}, "loc": {"start": {"line": 17, "column": 13}, "end": {"line": 21, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 26, "column": 24}, "end": {"line": 26, "column": 25}}, "loc": {"start": {"line": 26, "column": 41}, "end": {"line": 28, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 23}, "end": {"line": 30, "column": 24}}, "loc": {"start": {"line": 30, "column": 40}, "end": {"line": 31, "column": 9}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 7}}, "loc": {"start": {"line": 32, "column": 45}, "end": {"line": 34, "column": 7}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 7}}, "loc": {"start": {"line": 35, "column": 43}, "end": {"line": 35, "column": 58}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 37, "column": 27}, "end": {"line": 37, "column": null}}, "loc": {"start": {"line": 39, "column": 28}, "end": {"line": 40, "column": 18}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 40, "column": 17}, "end": {"line": 40, "column": 18}}, "loc": {"start": {"line": 40, "column": 33}, "end": {"line": 41, "column": 8}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 46, "column": 20}, "end": {"line": 46, "column": null}}, "loc": {"start": {"line": 49, "column": 14}, "end": {"line": 51, "column": 10}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 7}}, "loc": {"start": {"line": 52, "column": 22}, "end": {"line": 53, "column": 14}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 59, "column": 21}, "end": {"line": 59, "column": null}}, "loc": {"start": {"line": 62, "column": 12}, "end": {"line": 64, "column": 10}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 7}}, "loc": {"start": {"line": 65, "column": 22}, "end": {"line": 65, "column": 33}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 68, "column": 23}, "end": {"line": 68, "column": null}}, "loc": {"start": {"line": 71, "column": 15}, "end": {"line": 73, "column": 10}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 7}}, "loc": {"start": {"line": 74, "column": 22}, "end": {"line": 74, "column": 33}}}, "13": {"name": "<PERSON><PERSON>ogger", "decl": {"start": {"line": 77, "column": 16}, "end": {"line": 77, "column": 26}}, "loc": {"start": {"line": 77, "column": 58}, "end": {"line": 85, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 5}, "end": {"line": 17, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 5}, "end": {"line": 17, "column": 13}}, {"start": {"line": 17, "column": 5}, "end": {"line": 17, "column": null}}]}, "1": {"loc": {"start": {"line": 31, "column": 2}, "end": {"line": 35, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 32, "column": 6}, "end": {"line": 34, "column": 7}}, {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 58}}]}, "2": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 42, "column": 36}, "end": {"line": 42, "column": 49}}, {"start": {"line": 42, "column": 52}, "end": {"line": 42, "column": 65}}]}, "3": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 21}}, "type": "cond-expr", "locations": [{"start": {"line": 52, "column": 6}, "end": {"line": 53, "column": 14}}, {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 21}}]}, "4": {"loc": {"start": {"line": 54, "column": 10}, "end": {"line": 54, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 54, "column": 36}, "end": {"line": 54, "column": 49}}, {"start": {"line": 54, "column": 52}, "end": {"line": 54, "column": 65}}]}, "5": {"loc": {"start": {"line": 64, "column": 2}, "end": {"line": 66, "column": 21}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 33}}, {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 21}}]}, "6": {"loc": {"start": {"line": 73, "column": 2}, "end": {"line": 75, "column": 21}}, "type": "cond-expr", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 33}}, {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 21}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 5, "10": 8, "11": 2, "12": 5, "13": 2, "14": 2, "15": 5, "16": 4, "17": 2, "18": 5, "19": 1, "20": 2, "21": 5, "22": 1, "23": 5, "24": 5, "25": 2}, "f": {"0": 2, "1": 1, "2": 5, "3": 0, "4": 8, "5": 5, "6": 2, "7": 5, "8": 4, "9": 5, "10": 1, "11": 5, "12": 1, "13": 5}, "b": {"0": [2, 2], "1": [0, 5], "2": [1, 1], "3": [4, 1], "4": [1, 3], "5": [5, 0], "6": [5, 0]}}, "/home/<USER>/code/github/dividab/tsconfig-paths-webpack-plugin/src/options.ts": {"path": "/home/<USER>/code/github/dividab/tsconfig-paths-webpack-plugin/src/options.ts", "statementMap": {"0": {"start": {"line": 17, "column": 50}, "end": {"line": 27, "column": 2}}, "1": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 30}}, "2": {"start": {"line": 36, "column": 18}, "end": {"line": 36, "column": 41}}, "3": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 17}}, "4": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 16}}, "5": {"start": {"line": 48, "column": 27}, "end": {"line": 48, "column": 50}}, "6": {"start": {"line": 49, "column": 2}, "end": {"line": 59, "column": null}}, "7": {"start": {"line": 49, "column": 15}, "end": {"line": 49, "column": 16}}, "8": {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 38}}, "9": {"start": {"line": 52, "column": 7}, "end": {"line": 52, "column": 68}}, "10": {"start": {"line": 53, "column": 4}, "end": {"line": 58, "column": null}}, "11": {"start": {"line": 54, "column": 6}, "end": {"line": 57, "column": 3}}, "12": {"start": {"line": 63, "column": 15}, "end": {"line": 76, "column": null}}, "13": {"start": {"line": 79, "column": 16}, "end": {"line": 81, "column": null}}, "14": {"start": {"line": 84, "column": 2}, "end": {"line": 84, "column": 18}}}, "fnMap": {"0": {"name": "getOptions", "decl": {"start": {"line": 33, "column": 16}, "end": {"line": 33, "column": 26}}, "loc": {"start": {"line": 33, "column": 41}, "end": {"line": 39, "column": 1}}}, "1": {"name": "validateOptions", "decl": {"start": {"line": 47, "column": 9}, "end": {"line": 47, "column": 24}}, "loc": {"start": {"line": 47, "column": 39}, "end": {"line": 60, "column": 1}}}, "2": {"name": "makeOptions", "decl": {"start": {"line": 62, "column": 9}, "end": {"line": 62, "column": 20}}, "loc": {"start": {"line": 62, "column": 49}, "end": {"line": 85, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 58, "column": null}}, "type": "if", "locations": [{"start": {"line": 53, "column": 4}, "end": {"line": 58, "column": null}}]}}, "s": {"0": 1, "1": 3, "2": 3, "3": 3, "4": 1, "5": 3, "6": 3, "7": 3, "8": 10, "9": 10, "10": 10, "11": 0, "12": 3, "13": 3, "14": 3}, "f": {"0": 3, "1": 3, "2": 3}, "b": {"0": [0]}}, "/home/<USER>/code/github/dividab/tsconfig-paths-webpack-plugin/src/plugin.ts": {"path": "/home/<USER>/code/github/dividab/tsconfig-paths-webpack-plugin/src/plugin.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 31}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 29}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 37}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 35}}, "5": {"start": {"line": 122, "column": 41}, "end": {"line": 122, "column": 88}}, "6": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 13}}, "7": {"start": {"line": 138, "column": 14}, "end": {"line": 138, "column": null}}, "8": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 14}}, "9": {"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 39}}, "10": {"start": {"line": 128, "column": 2}, "end": {"line": 128, "column": 29}}, "11": {"start": {"line": 139, "column": 20}, "end": {"line": 139, "column": 50}}, "12": {"start": {"line": 141, "column": 4}, "end": {"line": 141, "column": 41}}, "13": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 32}}, "14": {"start": {"line": 146, "column": 4}, "end": {"line": 149, "column": 6}}, "15": {"start": {"line": 151, "column": 20}, "end": {"line": 151, "column": 52}}, "16": {"start": {"line": 152, "column": 21}, "end": {"line": 152, "column": 50}}, "17": {"start": {"line": 154, "column": 23}, "end": {"line": 154, "column": 53}}, "18": {"start": {"line": 155, "column": 4}, "end": {"line": 182, "column": null}}, "19": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 59}}, "20": {"start": {"line": 157, "column": 6}, "end": {"line": 159, "column": 37}}, "21": {"start": {"line": 160, "column": 6}, "end": {"line": 164, "column": 8}}, "22": {"start": {"line": 166, "column": 6}, "end": {"line": 181, "column": null}}, "23": {"start": {"line": 167, "column": 8}, "end": {"line": 180, "column": 35}}, "24": {"start": {"line": 168, "column": 10}, "end": {"line": 178, "column": null}}, "25": {"start": {"line": 169, "column": 36}, "end": {"line": 169, "column": 67}}, "26": {"start": {"line": 170, "column": 12}, "end": {"line": 177, "column": null}}, "27": {"start": {"line": 171, "column": 49}, "end": {"line": 171, "column": 65}}, "28": {"start": {"line": 172, "column": 14}, "end": {"line": 176, "column": 16}}, "29": {"start": {"line": 179, "column": 10}, "end": {"line": 179, "column": 25}}, "30": {"start": {"line": 185, "column": 2}, "end": {"line": 234, "column": null}}, "31": {"start": {"line": 186, "column": 4}, "end": {"line": 191, "column": null}}, "32": {"start": {"line": 187, "column": 6}, "end": {"line": 189, "column": 8}}, "33": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 13}}, "34": {"start": {"line": 196, "column": 4}, "end": {"line": 203, "column": null}}, "35": {"start": {"line": 197, "column": 6}, "end": {"line": 201, "column": 8}}, "36": {"start": {"line": 202, "column": 6}, "end": {"line": 202, "column": 13}}, "37": {"start": {"line": 206, "column": 4}, "end": {"line": 233, "column": null}}, "38": {"start": {"line": 207, "column": 6}, "end": {"line": 219, "column": 10}}, "39": {"start": {"line": 220, "column": 11}, "end": {"line": 233, "column": null}}, "40": {"start": {"line": 222, "column": 29}, "end": {"line": 222, "column": 66}}, "41": {"start": {"line": 223, "column": 6}, "end": {"line": 232, "column": 8}}, "42": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": null}}, "43": {"start": {"line": 124, "column": 13}, "end": {"line": 124, "column": 32}}, "44": {"start": {"line": 241, "column": 21}, "end": {"line": 241, "column": 57}}, "45": {"start": {"line": 242, "column": 2}, "end": {"line": 248, "column": null}}, "46": {"start": {"line": 243, "column": 4}, "end": {"line": 243, "column": 75}}, "47": {"start": {"line": 245, "column": 4}, "end": {"line": 247, "column": 6}}, "48": {"start": {"line": 249, "column": 2}, "end": {"line": 249, "column": 20}}, "49": {"start": {"line": 263, "column": 25}, "end": {"line": 263, "column": 66}}, "50": {"start": {"line": 264, "column": 24}, "end": {"line": 264, "column": 64}}, "51": {"start": {"line": 265, "column": 2}, "end": {"line": 357, "column": 4}}, "52": {"start": {"line": 270, "column": 25}, "end": {"line": 270, "column": 59}}, "53": {"start": {"line": 272, "column": 4}, "end": {"line": 278, "column": null}}, "54": {"start": {"line": 277, "column": 6}, "end": {"line": 277, "column": 24}}, "55": {"start": {"line": 283, "column": 26}, "end": {"line": 283, "column": 45}}, "56": {"start": {"line": 284, "column": 4}, "end": {"line": 305, "column": null}}, "57": {"start": {"line": 288, "column": 6}, "end": {"line": 304, "column": null}}, "58": {"start": {"line": 289, "column": 8}, "end": {"line": 289, "column": 39}}, "59": {"start": {"line": 291, "column": 29}, "end": {"line": 299, "column": null}}, "60": {"start": {"line": 293, "column": 29}, "end": {"line": 293, "column": 74}}, "61": {"start": {"line": 294, "column": 12}, "end": {"line": 298, "column": 14}}, "62": {"start": {"line": 301, "column": 8}, "end": {"line": 303, "column": null}}, "63": {"start": {"line": 302, "column": 10}, "end": {"line": 302, "column": 41}}, "64": {"start": {"line": 307, "column": 22}, "end": {"line": 307, "column": 73}}, "65": {"start": {"line": 309, "column": 4}, "end": {"line": 356, "column": 6}}, "66": {"start": {"line": 315, "column": 8}, "end": {"line": 317, "column": null}}, "67": {"start": {"line": 316, "column": 10}, "end": {"line": 316, "column": 31}}, "68": {"start": {"line": 319, "column": 8}, "end": {"line": 321, "column": null}}, "69": {"start": {"line": 320, "column": 10}, "end": {"line": 320, "column": 28}}, "70": {"start": {"line": 323, "column": 24}, "end": {"line": 326, "column": null}}, "71": {"start": {"line": 332, "column": 55}, "end": {"line": 332, "column": 105}}, "72": {"start": {"line": 334, "column": 8}, "end": {"line": 354, "column": 10}}, "73": {"start": {"line": 343, "column": 12}, "end": {"line": 345, "column": null}}, "74": {"start": {"line": 344, "column": 14}, "end": {"line": 344, "column": 36}}, "75": {"start": {"line": 348, "column": 12}, "end": {"line": 350, "column": null}}, "76": {"start": {"line": 349, "column": 14}, "end": {"line": 349, "column": 52}}, "77": {"start": {"line": 352, "column": 12}, "end": {"line": 352, "column": 41}}, "78": {"start": {"line": 367, "column": 25}, "end": {"line": 367, "column": 66}}, "79": {"start": {"line": 368, "column": 24}, "end": {"line": 368, "column": 64}}, "80": {"start": {"line": 369, "column": 2}, "end": {"line": 425, "column": 4}}, "81": {"start": {"line": 370, "column": 25}, "end": {"line": 370, "column": 59}}, "82": {"start": {"line": 372, "column": 4}, "end": {"line": 378, "column": null}}, "83": {"start": {"line": 377, "column": 6}, "end": {"line": 377, "column": 24}}, "84": {"start": {"line": 380, "column": 4}, "end": {"line": 424, "column": 6}}, "85": {"start": {"line": 386, "column": 8}, "end": {"line": 388, "column": null}}, "86": {"start": {"line": 387, "column": 10}, "end": {"line": 387, "column": 31}}, "87": {"start": {"line": 390, "column": 8}, "end": {"line": 392, "column": null}}, "88": {"start": {"line": 391, "column": 10}, "end": {"line": 391, "column": 28}}, "89": {"start": {"line": 394, "column": 24}, "end": {"line": 397, "column": null}}, "90": {"start": {"line": 403, "column": 57}, "end": {"line": 403, "column": 108}}, "91": {"start": {"line": 405, "column": 8}, "end": {"line": 422, "column": 10}}, "92": {"start": {"line": 415, "column": 12}, "end": {"line": 417, "column": null}}, "93": {"start": {"line": 416, "column": 14}, "end": {"line": 416, "column": 45}}, "94": {"start": {"line": 420, "column": 12}, "end": {"line": 420, "column": 43}}, "95": {"start": {"line": 433, "column": 2}, "end": {"line": 435, "column": null}}, "96": {"start": {"line": 434, "column": 4}, "end": {"line": 434, "column": 48}}, "97": {"start": {"line": 437, "column": 2}, "end": {"line": 452, "column": 5}}, "98": {"start": {"line": 438, "column": 4}, "end": {"line": 440, "column": null}}, "99": {"start": {"line": 439, "column": 6}, "end": {"line": 439, "column": 27}}, "100": {"start": {"line": 444, "column": 4}, "end": {"line": 449, "column": null}}, "101": {"start": {"line": 446, "column": 6}, "end": {"line": 446, "column": 47}}, "102": {"start": {"line": 448, "column": 6}, "end": {"line": 448, "column": 25}}, "103": {"start": {"line": 451, "column": 4}, "end": {"line": 451, "column": 37}}, "104": {"start": {"line": 459, "column": 2}, "end": {"line": 468, "column": 4}}, "105": {"start": {"line": 460, "column": 4}, "end": {"line": 467, "column": 7}}, "106": {"start": {"line": 462, "column": 6}, "end": {"line": 465, "column": null}}, "107": {"start": {"line": 463, "column": 8}, "end": {"line": 463, "column": 20}}, "108": {"start": {"line": 464, "column": 8}, "end": {"line": 464, "column": 15}}, "109": {"start": {"line": 466, "column": 6}, "end": {"line": 466, "column": 33}}, "110": {"start": {"line": 474, "column": 2}, "end": {"line": 486, "column": 4}}, "111": {"start": {"line": 478, "column": 4}, "end": {"line": 485, "column": 7}}, "112": {"start": {"line": 480, "column": 6}, "end": {"line": 483, "column": null}}, "113": {"start": {"line": 481, "column": 8}, "end": {"line": 481, "column": 36}}, "114": {"start": {"line": 482, "column": 8}, "end": {"line": 482, "column": 15}}, "115": {"start": {"line": 484, "column": 6}, "end": {"line": 484, "column": 59}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 13}}, "loc": {"start": {"line": 124, "column": 0}, "end": {"line": 235, "column": null}}}, "1": {"name": "TsconfigPathsPlugin", "decl": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 14}}, "loc": {"start": {"line": 138, "column": 55}, "end": {"line": 183, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 167, "column": 34}, "end": {"line": 167, "column": 35}}, "loc": {"start": {"line": 167, "column": 53}, "end": {"line": 180, "column": 9}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 185, "column": 2}, "end": {"line": 185, "column": 7}}, "loc": {"start": {"line": 185, "column": 26}, "end": {"line": 234, "column": 3}}}, "4": {"name": "loadConfig", "decl": {"start": {"line": 237, "column": 9}, "end": {"line": 237, "column": 19}}, "loc": {"start": {"line": 239, "column": 23}, "end": {"line": 250, "column": 1}}}, "5": {"name": "createPluginCallback", "decl": {"start": {"line": 252, "column": 9}, "end": {"line": 252, "column": 29}}, "loc": {"start": {"line": 261, "column": 35}, "end": {"line": 358, "column": 1}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 265, "column": 9}, "end": {"line": 265, "column": null}}, "loc": {"start": {"line": 268, "column": 35}, "end": {"line": 357, "column": 3}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 292, "column": 10}, "end": {"line": 292, "column": 11}}, "loc": {"start": {"line": 292, "column": 21}, "end": {"line": 299, "column": 11}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 314, "column": 6}, "end": {"line": 314, "column": 7}}, "loc": {"start": {"line": 314, "column": 22}, "end": {"line": 355, "column": 7}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 340, "column": 10}, "end": {"line": 340, "column": 11}}, "loc": {"start": {"line": 340, "column": 47}, "end": {"line": 353, "column": 11}}}, "10": {"name": "createPluginLegacy", "decl": {"start": {"line": 360, "column": 9}, "end": {"line": 360, "column": 27}}, "loc": {"start": {"line": 365, "column": 35}, "end": {"line": 426, "column": 1}}}, "11": {"name": "(anonymous_13)", "decl": {"start": {"line": 369, "column": 9}, "end": {"line": 369, "column": 10}}, "loc": {"start": {"line": 369, "column": 27}, "end": {"line": 425, "column": 3}}}, "12": {"name": "(anonymous_14)", "decl": {"start": {"line": 385, "column": 6}, "end": {"line": 385, "column": 7}}, "loc": {"start": {"line": 385, "column": 22}, "end": {"line": 423, "column": 7}}}, "13": {"name": "(anonymous_15)", "decl": {"start": {"line": 409, "column": 30}, "end": {"line": 409, "column": 40}}, "loc": {"start": {"line": 409, "column": 68}, "end": {"line": 421, "column": 11}}}, "14": {"name": "read<PERSON>son", "decl": {"start": {"line": 428, "column": 9}, "end": {"line": 428, "column": 17}}, "loc": {"start": {"line": 431, "column": 28}, "end": {"line": 453, "column": 1}}}, "15": {"name": "(anonymous_17)", "decl": {"start": {"line": 437, "column": 29}, "end": {"line": 437, "column": 30}}, "loc": {"start": {"line": 437, "column": 38}, "end": {"line": 452, "column": 3}}}, "16": {"name": "createReadJsonAsync", "decl": {"start": {"line": 455, "column": 9}, "end": {"line": 455, "column": 28}}, "loc": {"start": {"line": 456, "column": 51}, "end": {"line": 469, "column": 1}}}, "17": {"name": "(anonymous_19)", "decl": {"start": {"line": 459, "column": 9}, "end": {"line": 459, "column": 10}}, "loc": {"start": {"line": 459, "column": 72}, "end": {"line": 468, "column": 3}}}, "18": {"name": "(anonymous_20)", "decl": {"start": {"line": 460, "column": 32}, "end": {"line": 460, "column": 33}}, "loc": {"start": {"line": 460, "column": 42}, "end": {"line": 467, "column": 5}}}, "19": {"name": "createFileExistAsync", "decl": {"start": {"line": 471, "column": 9}, "end": {"line": 471, "column": 29}}, "loc": {"start": {"line": 472, "column": 51}, "end": {"line": 487, "column": 1}}}, "20": {"name": "(anonymous_22)", "decl": {"start": {"line": 474, "column": 9}, "end": {"line": 474, "column": null}}, "loc": {"start": {"line": 476, "column": 54}, "end": {"line": 486, "column": 3}}}, "21": {"name": "(anonymous_23)", "decl": {"start": {"line": 478, "column": 27}, "end": {"line": 478, "column": 28}}, "loc": {"start": {"line": 478, "column": 55}, "end": {"line": 485, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 138, "column": 14}, "end": {"line": 138, "column": null}}, "type": "if", "locations": [{"start": {"line": 138, "column": 14}, "end": {"line": 138, "column": null}}]}, "1": {"loc": {"start": {"line": 148, "column": 34}, "end": {"line": 148, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 148, "column": 51}, "end": {"line": 148, "column": 60}}, {"start": {"line": 148, "column": 63}, "end": {"line": 148, "column": 64}}]}, "2": {"loc": {"start": {"line": 151, "column": 20}, "end": {"line": 151, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 151, "column": 20}, "end": {"line": 151, "column": 35}}, {"start": {"line": 151, "column": 39}, "end": {"line": 151, "column": 52}}]}, "3": {"loc": {"start": {"line": 152, "column": 21}, "end": {"line": 152, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 21}, "end": {"line": 152, "column": 39}}, {"start": {"line": 152, "column": 43}, "end": {"line": 152, "column": 50}}]}, "4": {"loc": {"start": {"line": 155, "column": 4}, "end": {"line": 182, "column": null}}, "type": "if", "locations": [{"start": {"line": 155, "column": 4}, "end": {"line": 182, "column": null}}]}, "5": {"loc": {"start": {"line": 156, "column": 21}, "end": {"line": 156, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 156, "column": 21}, "end": {"line": 156, "column": 36}}, {"start": {"line": 156, "column": 40}, "end": {"line": 156, "column": 58}}]}, "6": {"loc": {"start": {"line": 157, "column": 29}, "end": {"line": 159, "column": 36}}, "type": "cond-expr", "locations": [{"start": {"line": 158, "column": 10}, "end": {"line": 158, "column": 39}}, {"start": {"line": 159, "column": 10}, "end": {"line": 159, "column": 36}}]}, "7": {"loc": {"start": {"line": 166, "column": 6}, "end": {"line": 181, "column": null}}, "type": "if", "locations": [{"start": {"line": 166, "column": 6}, "end": {"line": 181, "column": null}}]}, "8": {"loc": {"start": {"line": 168, "column": 10}, "end": {"line": 178, "column": null}}, "type": "if", "locations": [{"start": {"line": 168, "column": 10}, "end": {"line": 178, "column": null}}]}, "9": {"loc": {"start": {"line": 170, "column": 12}, "end": {"line": 177, "column": null}}, "type": "if", "locations": [{"start": {"line": 170, "column": 12}, "end": {"line": 177, "column": null}}]}, "10": {"loc": {"start": {"line": 186, "column": 4}, "end": {"line": 191, "column": null}}, "type": "if", "locations": [{"start": {"line": 186, "column": 4}, "end": {"line": 191, "column": null}}]}, "11": {"loc": {"start": {"line": 196, "column": 4}, "end": {"line": 203, "column": null}}, "type": "if", "locations": [{"start": {"line": 196, "column": 4}, "end": {"line": 203, "column": null}}]}, "12": {"loc": {"start": {"line": 206, "column": 4}, "end": {"line": 233, "column": null}}, "type": "if", "locations": [{"start": {"line": 206, "column": 4}, "end": {"line": 233, "column": null}}, {"start": {"line": 220, "column": 11}, "end": {"line": 233, "column": null}}]}, "13": {"loc": {"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 29}}, {"start": {"line": 206, "column": 33}, "end": {"line": 206, "column": 71}}]}, "14": {"loc": {"start": {"line": 220, "column": 11}, "end": {"line": 233, "column": null}}, "type": "if", "locations": [{"start": {"line": 220, "column": 11}, "end": {"line": 233, "column": null}}]}, "15": {"loc": {"start": {"line": 242, "column": 2}, "end": {"line": 248, "column": null}}, "type": "if", "locations": [{"start": {"line": 242, "column": 2}, "end": {"line": 248, "column": null}}, {"start": {"line": 244, "column": 9}, "end": {"line": 248, "column": null}}]}, "16": {"loc": {"start": {"line": 272, "column": 4}, "end": {"line": 278, "column": null}}, "type": "if", "locations": [{"start": {"line": 272, "column": 4}, "end": {"line": 278, "column": null}}]}, "17": {"loc": {"start": {"line": 273, "column": 6}, "end": {"line": 275, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 273, "column": 6}, "end": {"line": 273, "column": 19}}, {"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 39}}, {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 40}}]}, "18": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 39}}, "type": "cond-expr", "locations": [{"start": {"line": 274, "column": 22}, "end": {"line": 274, "column": 24}}, {"start": {"line": 274, "column": 22}, "end": {"line": 274, "column": 39}}]}, "19": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 24}}, "type": "binary-expr", "locations": [{"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 24}}, {"start": {"line": 274, "column": 22}, "end": {"line": 274, "column": 24}}]}, "20": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 22}}, "type": "cond-expr", "locations": [{"start": {"line": 274, "column": 13}, "end": {"line": 274, "column": 15}}, {"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 22}}]}, "21": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 15}}, "type": "binary-expr", "locations": [{"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 15}}, {"start": {"line": 274, "column": 6}, "end": {"line": 274, "column": 15}}]}, "22": {"loc": {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 275, "column": 22}, "end": {"line": 275, "column": 24}}, {"start": {"line": 275, "column": 22}, "end": {"line": 275, "column": 40}}]}, "23": {"loc": {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 24}}, "type": "binary-expr", "locations": [{"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 24}}, {"start": {"line": 275, "column": 22}, "end": {"line": 275, "column": 24}}]}, "24": {"loc": {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 22}}, "type": "cond-expr", "locations": [{"start": {"line": 275, "column": 13}, "end": {"line": 275, "column": 15}}, {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 22}}]}, "25": {"loc": {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 15}}, "type": "binary-expr", "locations": [{"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 15}}, {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 15}}]}, "26": {"loc": {"start": {"line": 284, "column": 4}, "end": {"line": 305, "column": null}}, "type": "if", "locations": [{"start": {"line": 284, "column": 4}, "end": {"line": 305, "column": null}}]}, "27": {"loc": {"start": {"line": 285, "column": 6}, "end": {"line": 286, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 285, "column": 6}, "end": {"line": 285, "column": 38}}, {"start": {"line": 286, "column": 6}, "end": {"line": 286, "column": 42}}]}, "28": {"loc": {"start": {"line": 288, "column": 6}, "end": {"line": 304, "column": null}}, "type": "if", "locations": [{"start": {"line": 288, "column": 6}, "end": {"line": 304, "column": null}}, {"start": {"line": 290, "column": 13}, "end": {"line": 304, "column": null}}]}, "29": {"loc": {"start": {"line": 293, "column": 55}, "end": {"line": 293, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 293, "column": 55}, "end": {"line": 293, "column": 67}}, {"start": {"line": 293, "column": 71}, "end": {"line": 293, "column": 73}}]}, "30": {"loc": {"start": {"line": 295, "column": 14}, "end": {"line": 297, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 295, "column": 14}, "end": {"line": 295, "column": 22}}, {"start": {"line": 296, "column": 14}, "end": {"line": 296, "column": 40}}, {"start": {"line": 297, "column": 14}, "end": {"line": 297, "column": 40}}]}, "31": {"loc": {"start": {"line": 301, "column": 8}, "end": {"line": 303, "column": null}}, "type": "if", "locations": [{"start": {"line": 301, "column": 8}, "end": {"line": 303, "column": null}}]}, "32": {"loc": {"start": {"line": 307, "column": 22}, "end": {"line": 307, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 307, "column": 22}, "end": {"line": 307, "column": 56}}, {"start": {"line": 307, "column": 60}, "end": {"line": 307, "column": 73}}]}, "33": {"loc": {"start": {"line": 315, "column": 8}, "end": {"line": 317, "column": null}}, "type": "if", "locations": [{"start": {"line": 315, "column": 8}, "end": {"line": 317, "column": null}}]}, "34": {"loc": {"start": {"line": 319, "column": 8}, "end": {"line": 321, "column": null}}, "type": "if", "locations": [{"start": {"line": 319, "column": 8}, "end": {"line": 321, "column": null}}]}, "35": {"loc": {"start": {"line": 343, "column": 12}, "end": {"line": 345, "column": null}}, "type": "if", "locations": [{"start": {"line": 343, "column": 12}, "end": {"line": 345, "column": null}}]}, "36": {"loc": {"start": {"line": 348, "column": 12}, "end": {"line": 350, "column": null}}, "type": "if", "locations": [{"start": {"line": 348, "column": 12}, "end": {"line": 350, "column": null}}]}, "37": {"loc": {"start": {"line": 372, "column": 4}, "end": {"line": 378, "column": null}}, "type": "if", "locations": [{"start": {"line": 372, "column": 4}, "end": {"line": 378, "column": null}}]}, "38": {"loc": {"start": {"line": 373, "column": 6}, "end": {"line": 375, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 373, "column": 6}, "end": {"line": 373, "column": 19}}, {"start": {"line": 374, "column": 6}, "end": {"line": 374, "column": 34}}, {"start": {"line": 375, "column": 6}, "end": {"line": 375, "column": 35}}]}, "39": {"loc": {"start": {"line": 386, "column": 8}, "end": {"line": 388, "column": null}}, "type": "if", "locations": [{"start": {"line": 386, "column": 8}, "end": {"line": 388, "column": null}}]}, "40": {"loc": {"start": {"line": 390, "column": 8}, "end": {"line": 392, "column": null}}, "type": "if", "locations": [{"start": {"line": 390, "column": 8}, "end": {"line": 392, "column": null}}]}, "41": {"loc": {"start": {"line": 415, "column": 12}, "end": {"line": 417, "column": null}}, "type": "if", "locations": [{"start": {"line": 415, "column": 12}, "end": {"line": 417, "column": null}}]}, "42": {"loc": {"start": {"line": 433, "column": 2}, "end": {"line": 435, "column": null}}, "type": "if", "locations": [{"start": {"line": 433, "column": 2}, "end": {"line": 435, "column": null}}]}, "43": {"loc": {"start": {"line": 433, "column": 6}, "end": {"line": 433, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 433, "column": 6}, "end": {"line": 433, "column": 30}}, {"start": {"line": 433, "column": 34}, "end": {"line": 433, "column": 53}}]}, "44": {"loc": {"start": {"line": 438, "column": 4}, "end": {"line": 440, "column": null}}, "type": "if", "locations": [{"start": {"line": 438, "column": 4}, "end": {"line": 440, "column": null}}]}, "45": {"loc": {"start": {"line": 462, "column": 6}, "end": {"line": 465, "column": null}}, "type": "if", "locations": [{"start": {"line": 462, "column": 6}, "end": {"line": 465, "column": null}}]}, "46": {"loc": {"start": {"line": 462, "column": 10}, "end": {"line": 462, "column": 22}}, "type": "binary-expr", "locations": [{"start": {"line": 462, "column": 10}, "end": {"line": 462, "column": 13}}, {"start": {"line": 462, "column": 17}, "end": {"line": 462, "column": 22}}]}, "47": {"loc": {"start": {"line": 480, "column": 6}, "end": {"line": 483, "column": null}}, "type": "if", "locations": [{"start": {"line": 480, "column": 6}, "end": {"line": 483, "column": null}}]}, "48": {"loc": {"start": {"line": 484, "column": 27}, "end": {"line": 484, "column": 57}}, "type": "cond-expr", "locations": [{"start": {"line": 484, "column": 35}, "end": {"line": 484, "column": 49}}, {"start": {"line": 484, "column": 52}, "end": {"line": 484, "column": 57}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 3, "8": 3, "9": 3, "10": 3, "11": 3, "12": 3, "13": 3, "14": 3, "15": 3, "16": 3, "17": 3, "18": 3, "19": 3, "20": 3, "21": 3, "22": 3, "23": 1, "24": 1, "25": 1, "26": 1, "27": 2, "28": 1, "29": 1, "30": 1, "31": 6, "32": 0, "33": 0, "34": 6, "35": 0, "36": 0, "37": 6, "38": 6, "39": 0, "40": 0, "41": 0, "42": 1, "43": 1, "44": 4, "45": 4, "46": 0, "47": 4, "48": 4, "49": 6, "50": 6, "51": 6, "52": 39, "53": 39, "54": 0, "55": 39, "56": 39, "57": 27, "58": 6, "59": 21, "60": 7, "61": 7, "62": 21, "63": 6, "64": 39, "65": 39, "66": 39, "67": 0, "68": 39, "69": 21, "70": 18, "71": 18, "72": 18, "73": 18, "74": 0, "75": 18, "76": 0, "77": 18, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 36, "96": 36, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 6, "105": 36, "106": 36, "107": 27, "108": 27, "109": 9, "110": 6, "111": 171, "112": 171, "113": 138, "114": 138, "115": 33}, "f": {"0": 1, "1": 3, "2": 1, "3": 6, "4": 4, "5": 6, "6": 39, "7": 7, "8": 39, "9": 18, "10": 0, "11": 0, "12": 0, "13": 0, "14": 36, "15": 0, "16": 6, "17": 36, "18": 36, "19": 6, "20": 171, "21": 171}, "b": {"0": [0], "1": [3, 0], "2": [3, 3], "3": [3, 0], "4": [3], "5": [3, 3], "6": [0, 3], "7": [1], "8": [1], "9": [1], "10": [0], "11": [0], "12": [6, 0], "13": [6, 6], "14": [0], "15": [0, 4], "16": [0], "17": [39, 39, 39], "18": [0, 39], "19": [39, 39], "20": [0, 39], "21": [39, 39], "22": [0, 39], "23": [39, 39], "24": [0, 39], "25": [39, 39], "26": [27], "27": [39, 39], "28": [6, 21], "29": [7, 0], "30": [7, 7, 6], "31": [6], "32": [39, 27], "33": [0], "34": [21], "35": [0], "36": [0], "37": [0], "38": [0, 0, 0], "39": [0], "40": [0], "41": [0], "42": [36], "43": [36, 36], "44": [0], "45": [27], "46": [36, 9], "47": [138], "48": [33, 0]}}}