import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { Decimal } from 'decimal.js';
import { Wallet, WalletStatus, WalletType } from '../entities/wallet.entity';
import { CreateWalletDto } from '../dto/create-wallet.dto';

export interface FindWalletsOptions {
  page?: number;
  limit?: number;
  currency?: string;
  status?: WalletStatus;
  type?: WalletType;
  userId?: string;
}

export interface PaginatedWallets {
  data: Wallet[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TransactionRequest {
  amount: string;
  description?: string;
  reference?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class WalletsService {
  private readonly logger = new Logger(WalletsService.name);

  constructor(
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
  ) {}

  async create(userId: string, createWalletDto: CreateWalletDto): Promise<Wallet> {
    const { currency, type, name, ...walletData } = createWalletDto;

    // التحقق من وجود محفظة بنفس العملة والنوع للمستخدم
    const existingWallet = await this.walletRepository.findOne({
      where: { userId, currency, type },
    });

    if (existingWallet) {
      throw new ConflictException(`محفظة ${type} بعملة ${currency} موجودة بالفعل`);
    }

    // إنشاء رقم المحفظة
    const walletNumber = this.generateWalletNumber(currency);

    // إنشاء المحفظة
    const wallet = this.walletRepository.create({
      ...walletData,
      userId,
      currency,
      type,
      name: name || `محفظة ${currency}`,
      walletNumber,
    });

    const savedWallet = await this.walletRepository.save(wallet);
    this.logger.log(`Wallet created: ${savedWallet.walletNumber} for user ${userId}`);

    return savedWallet;
  }

  async findAll(options: FindWalletsOptions = {}): Promise<PaginatedWallets> {
    const { page = 1, limit = 10, currency, status, type, userId } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.walletRepository.createQueryBuilder('wallet');

    // تصفية حسب المستخدم
    if (userId) {
      queryBuilder.where('wallet.userId = :userId', { userId });
    }

    // تصفية حسب العملة
    if (currency) {
      queryBuilder.andWhere('wallet.currency = :currency', { currency });
    }

    // تصفية حسب الحالة
    if (status) {
      queryBuilder.andWhere('wallet.status = :status', { status });
    }

    // تصفية حسب النوع
    if (type) {
      queryBuilder.andWhere('wallet.type = :type', { type });
    }

    // ترتيب وتصفح
    queryBuilder
      .orderBy('wallet.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findById(id: string): Promise<Wallet> {
    const wallet = await this.walletRepository.findOne({
      where: { id },
    });

    if (!wallet) {
      throw new NotFoundException('المحفظة غير موجودة');
    }

    return wallet;
  }

  async findByWalletNumber(walletNumber: string): Promise<Wallet> {
    const wallet = await this.walletRepository.findOne({
      where: { walletNumber },
    });

    if (!wallet) {
      throw new NotFoundException('المحفظة غير موجودة');
    }

    return wallet;
  }

  async getUserWallets(userId: string): Promise<Wallet[]> {
    return this.walletRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async credit(
    walletId: string,
    transaction: TransactionRequest,
    userId?: string,
  ): Promise<Wallet> {
    const wallet = await this.findById(walletId);

    // التحقق من الصلاحية
    if (userId && wallet.userId !== userId) {
      throw new ForbiddenException('غير مصرح لك بالوصول لهذه المحفظة');
    }

    // التحقق من إمكانية الإيداع
    if (!wallet.canReceive) {
      throw new BadRequestException('المحفظة لا تقبل التحويلات الواردة');
    }

    // التحقق من صحة المبلغ
    const amount = new Decimal(transaction.amount);
    if (amount.lessThanOrEqualTo(0)) {
      throw new BadRequestException('المبلغ يجب أن يكون أكبر من صفر');
    }

    // إضافة المبلغ
    wallet.credit(transaction.amount);

    const updatedWallet = await this.walletRepository.save(wallet);
    this.logger.log(`Wallet credited: ${wallet.walletNumber} +${transaction.amount}`);

    return updatedWallet;
  }

  async debit(
    walletId: string,
    transaction: TransactionRequest,
    userId?: string,
  ): Promise<Wallet> {
    const wallet = await this.findById(walletId);

    // التحقق من الصلاحية
    if (userId && wallet.userId !== userId) {
      throw new ForbiddenException('غير مصرح لك بالوصول لهذه المحفظة');
    }

    // التحقق من إمكانية السحب
    if (!wallet.canSend) {
      throw new BadRequestException('المحفظة لا تسمح بالتحويلات الصادرة');
    }

    // التحقق من صحة المبلغ
    const amount = new Decimal(transaction.amount);
    if (amount.lessThanOrEqualTo(0)) {
      throw new BadRequestException('المبلغ يجب أن يكون أكبر من صفر');
    }

    // التحقق من الرصيد
    if (!wallet.canWithdraw(transaction.amount)) {
      throw new BadRequestException('الرصيد غير كافي');
    }

    // التحقق من الحدود
    if (!wallet.checkDailyLimit(transaction.amount)) {
      throw new BadRequestException('تم تجاوز الحد اليومي للإنفاق');
    }

    if (!wallet.checkMonthlyLimit(transaction.amount)) {
      throw new BadRequestException('تم تجاوز الحد الشهري للإنفاق');
    }

    if (!wallet.checkTransactionLimit(transaction.amount)) {
      throw new BadRequestException('تم تجاوز الحد الأقصى للمعاملة الواحدة');
    }

    // خصم المبلغ
    const success = wallet.debit(transaction.amount);
    if (!success) {
      throw new BadRequestException('فشل في خصم المبلغ');
    }

    // تحديث الإنفاق اليومي والشهري
    const dailySpent = new Decimal(wallet.dailySpent).plus(amount);
    const monthlySpent = new Decimal(wallet.monthlySpent).plus(amount);
    
    wallet.dailySpent = dailySpent.toString();
    wallet.monthlySpent = monthlySpent.toString();

    const updatedWallet = await this.walletRepository.save(wallet);
    this.logger.log(`Wallet debited: ${wallet.walletNumber} -${transaction.amount}`);

    return updatedWallet;
  }

  async transfer(
    fromWalletId: string,
    toWalletId: string,
    transaction: TransactionRequest,
    userId?: string,
  ): Promise<{ fromWallet: Wallet; toWallet: Wallet }> {
    const fromWallet = await this.findById(fromWalletId);
    const toWallet = await this.findById(toWalletId);

    // التحقق من الصلاحية
    if (userId && fromWallet.userId !== userId) {
      throw new ForbiddenException('غير مصرح لك بالتحويل من هذه المحفظة');
    }

    // التحقق من العملة
    if (fromWallet.currency !== toWallet.currency) {
      throw new BadRequestException('لا يمكن التحويل بين عملات مختلفة');
    }

    // خصم من المحفظة المرسلة
    const updatedFromWallet = await this.debit(fromWalletId, transaction, userId);

    try {
      // إضافة للمحفظة المستقبلة
      const updatedToWallet = await this.credit(toWalletId, transaction);

      return {
        fromWallet: updatedFromWallet,
        toWallet: updatedToWallet,
      };
    } catch (error) {
      // في حالة فشل الإضافة، إرجاع المبلغ للمحفظة المرسلة
      await this.credit(fromWalletId, transaction);
      throw error;
    }
  }

  async freeze(
    walletId: string,
    amount: string,
    reason?: string,
    userId?: string,
  ): Promise<Wallet> {
    const wallet = await this.findById(walletId);

    // التحقق من الصلاحية
    if (userId && wallet.userId !== userId) {
      throw new ForbiddenException('غير مصرح لك بالوصول لهذه المحفظة');
    }

    const success = wallet.freeze(amount);
    if (!success) {
      throw new BadRequestException('فشل في تجميد المبلغ - الرصيد غير كافي');
    }

    if (reason && wallet.metadata) {
      wallet.metadata.freezeReason = reason;
      wallet.metadata.freezeDate = new Date().toISOString();
    }

    const updatedWallet = await this.walletRepository.save(wallet);
    this.logger.log(`Amount frozen: ${wallet.walletNumber} ${amount}`);

    return updatedWallet;
  }

  async unfreeze(
    walletId: string,
    amount: string,
    userId?: string,
  ): Promise<Wallet> {
    const wallet = await this.findById(walletId);

    // التحقق من الصلاحية
    if (userId && wallet.userId !== userId) {
      throw new ForbiddenException('غير مصرح لك بالوصول لهذه المحفظة');
    }

    const success = wallet.unfreeze(amount);
    if (!success) {
      throw new BadRequestException('فشل في إلغاء تجميد المبلغ - المبلغ المجمد غير كافي');
    }

    const updatedWallet = await this.walletRepository.save(wallet);
    this.logger.log(`Amount unfrozen: ${wallet.walletNumber} ${amount}`);

    return updatedWallet;
  }

  async updateStatus(
    walletId: string,
    status: WalletStatus,
    reason?: string,
    userId?: string,
  ): Promise<Wallet> {
    const wallet = await this.findById(walletId);

    // التحقق من الصلاحية (المديرين فقط يمكنهم تغيير الحالة)
    if (userId && wallet.userId !== userId) {
      throw new ForbiddenException('غير مصرح لك بتغيير حالة هذه المحفظة');
    }

    if (status === WalletStatus.ACTIVE) {
      wallet.activate();
    } else if (status === WalletStatus.SUSPENDED) {
      wallet.suspend(reason);
    } else if (status === WalletStatus.CLOSED) {
      wallet.close(reason);
    }

    const updatedWallet = await this.walletRepository.save(wallet);
    this.logger.log(`Wallet status updated: ${wallet.walletNumber} -> ${status}`);

    return updatedWallet;
  }

  async getWalletStats(userId?: string): Promise<any> {
    const queryBuilder = this.walletRepository.createQueryBuilder('wallet');

    if (userId) {
      queryBuilder.where('wallet.userId = :userId', { userId });
    }

    const [
      totalWallets,
      activeWallets,
      totalBalance,
      walletsByCurrency,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('wallet.status = :status', { status: WalletStatus.ACTIVE }).getCount(),
      queryBuilder.clone().select('SUM(CAST(wallet.totalBalance AS DECIMAL))', 'total').getRawOne(),
      queryBuilder.clone()
        .select('wallet.currency', 'currency')
        .addSelect('COUNT(*)', 'count')
        .addSelect('SUM(CAST(wallet.totalBalance AS DECIMAL))', 'totalBalance')
        .groupBy('wallet.currency')
        .getRawMany(),
    ]);

    return {
      totalWallets,
      activeWallets,
      totalBalance: totalBalance?.total || '0',
      walletsByCurrency,
    };
  }

  private generateWalletNumber(currency: string): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${currency}${timestamp.slice(-8)}${random}`;
  }

  async remove(walletId: string, userId?: string): Promise<void> {
    const wallet = await this.findById(walletId);

    // التحقق من الصلاحية
    if (userId && wallet.userId !== userId) {
      throw new ForbiddenException('غير مصرح لك بحذف هذه المحفظة');
    }

    // التحقق من الرصيد
    const balance = new Decimal(wallet.totalBalance);
    if (balance.greaterThan(0)) {
      throw new BadRequestException('لا يمكن حذف محفظة تحتوي على رصيد');
    }

    // حذف ناعم
    wallet.close('تم حذف المحفظة بواسطة المستخدم');
    await this.walletRepository.save(wallet);

    this.logger.log(`Wallet removed: ${wallet.walletNumber}`);
  }
}
