<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WS Transfir - نظام التحويلات المالية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .status {
            background: #27ae60;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 1.1em;
            font-weight: bold;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            width: 100%;
            margin: 10px 0;
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            transform: translateY(-2px);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .btn.warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        
        .credentials {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .credentials h3 {
            margin-bottom: 15px;
        }
        
        .credentials p {
            margin: 8px 0;
            font-size: 1.1em;
        }
        
        .result {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .link {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            text-decoration: none;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        .link:hover {
            background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
            transform: translateY(-3px);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature {
            background: white;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WS Transfir</h1>
            <p>نظام التحويلات المالية المتقدم</p>
        </div>
        
        <div class="status">
            ✅ النظام يعمل بنجاح - جاهز للاستخدام
        </div>
        
        <div class="content">
            <div class="section">
                <h2>🔧 اختبار النظام</h2>
                <div class="grid">
                    <div class="card">
                        <h3>📊 فحص صحة API</h3>
                        <p>اختبار اتصال خادم API</p>
                        <button class="btn" onclick="testHealth()">فحص الصحة</button>
                    </div>
                    
                    <div class="card">
                        <h3>🔐 اختبار تسجيل الدخول</h3>
                        <p>اختبار نظام المصادقة</p>
                        <button class="btn success" onclick="testLogin()">تسجيل دخول المدير</button>
                        <button class="btn" onclick="testUserLogin()">تسجيل دخول مستخدم</button>
                    </div>
                    
                    <div class="card">
                        <h3>👤 اختبار الملف الشخصي</h3>
                        <p>عرض بيانات المستخدم</p>
                        <button class="btn" onclick="testProfile()">عرض الملف الشخصي</button>
                    </div>
                    
                    <div class="card">
                        <h3>💸 اختبار التحويلات</h3>
                        <p>عرض قائمة التحويلات</p>
                        <button class="btn warning" onclick="testTransfers()">عرض التحويلات</button>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🌐 روابط النظام</h2>
                <div class="links">
                    <a href="http://localhost:3000/api/health" target="_blank" class="link">📊 فحص صحة API</a>
                    <a href="http://localhost:3000" target="_blank" class="link">🔧 خادم API</a>
                    <a href="#" onclick="testAllEndpoints()" class="link">🧪 اختبار جميع النقاط</a>
                    <a href="#" onclick="clearResults()" class="link">🗑️ مسح النتائج</a>
                </div>
            </div>
            
            <div class="credentials">
                <h3>🔐 بيانات الدخول التجريبية</h3>
                <p><strong>👨‍💼 مدير النظام:</strong> <EMAIL> / admin123</p>
                <p><strong>👤 مستخدم عادي:</strong> <EMAIL> / password123</p>
            </div>
            
            <div class="section">
                <h2>✨ ميزات النظام</h2>
                <div class="features">
                    <div class="feature">
                        <div class="feature-icon">🔐</div>
                        <h4>أمان متقدم</h4>
                        <p>مصادقة ثنائية وتشفير</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">💳</div>
                        <h4>بوابات دفع متعددة</h4>
                        <p>دعم جميع وسائل الدفع</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">📊</div>
                        <h4>تحليلات متقدمة</h4>
                        <p>تقارير وإحصائيات شاملة</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">🔔</div>
                        <h4>إشعارات فورية</h4>
                        <p>تنبيهات متعددة القنوات</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">⚖️</div>
                        <h4>امتثال كامل</h4>
                        <p>KYC ومكافحة غسل الأموال</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">🌍</div>
                        <h4>تحويلات دولية</h4>
                        <p>دعم جميع العملات</p>
                    </div>
                </div>
            </div>
            
            <div id="result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        function showResult(data) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = JSON.stringify(data, null, 2);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function showError(error) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = `خطأ: ${error.message}`;
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showError(error);
            }
        }
        
        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showError(error);
            }
        }
        
        async function testUserLogin() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showError(error);
            }
        }
        
        async function testProfile() {
            try {
                const response = await fetch(`${API_BASE}/api/profile/me`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showError(error);
            }
        }
        
        async function testTransfers() {
            try {
                const response = await fetch(`${API_BASE}/api/transfers`);
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showError(error);
            }
        }
        
        async function testAllEndpoints() {
            const results = {};
            
            try {
                // Test health
                const healthResponse = await fetch(`${API_BASE}/api/health`);
                results.health = await healthResponse.json();
                
                // Test login
                const loginResponse = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: '<EMAIL>', password: 'admin123' })
                });
                results.login = await loginResponse.json();
                
                // Test profile
                const profileResponse = await fetch(`${API_BASE}/api/profile/me`);
                results.profile = await profileResponse.json();
                
                // Test transfers
                const transfersResponse = await fetch(`${API_BASE}/api/transfers`);
                results.transfers = await transfersResponse.json();
                
                showResult(results);
            } catch (error) {
                showError(error);
            }
        }
        
        function clearResults() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            resultDiv.textContent = '';
        }
        
        // Auto-test health on page load
        window.addEventListener('load', () => {
            setTimeout(testHealth, 1000);
        });
    </script>
</body>
</html>
