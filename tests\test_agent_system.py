"""
Agent System Tests
==================
اختبارات نظام الوكلاء المتقدم
"""

import pytest
import asyncio
from datetime import datetime, date, timedelta
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from agent_system.agent_service import AgentService, AgentType, AgentStatus, AgentProfile
from agent_system.commission_engine import CommissionEngine, CommissionTier, CommissionCalculation
from agent_system.agent_dashboard import AgentDashboard


class TestAgentService:
    """اختبارات خدمة الوكلاء"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        # Mock connection context manager
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def agent_service(self, mock_db_connection):
        """خدمة الوكلاء للاختبار"""
        db_connection, _ = mock_db_connection
        return AgentService(db_connection)
    
    @pytest.mark.asyncio
    async def test_create_agent_profile_success(self, agent_service, mock_db_connection):
        """اختبار إنشاء ملف وكيل بنجاح"""
        db_connection, conn = mock_db_connection
        
        # Setup mocks
        agent_service._validate_user_for_agent = AsyncMock(return_value=True)
        agent_service._validate_parent_agent = AsyncMock(return_value=True)
        agent_service._create_initial_performance_record = AsyncMock()
        
        # Mock database response
        mock_agent_data = {
            'id': 'agent_test_123',
            'user_id': 'user_test_123',
            'agent_code': 'AGTGEN000001',
            'agent_type': 'individual',
            'status': 'pending',
            'parent_agent_id': None,
            'level': 1,
            'hierarchy_path': 'agent_test_123',
            'business_name': 'Test Business',
            'region': 'Riyadh',
            'city': 'Riyadh',
            'base_commission_rate': Decimal('0.0100'),
            'tier_commission_rate': Decimal('0.0050'),
            'volume_bonus_rate': Decimal('0.0025'),
            'daily_transaction_limit': Decimal('100000'),
            'monthly_transaction_limit': Decimal('2000000'),
            'single_transaction_limit': Decimal('50000'),
            'total_transactions': 0,
            'total_volume': Decimal('0'),
            'total_commission_earned': Decimal('0'),
            'customer_count': 0,
            'rating': Decimal('0'),
            'review_count': 0,
            'training_completed': False,
            'certification_level': 1,
            'onboarding_completed': False,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        conn.fetchrow.return_value = mock_agent_data
        
        # Test data
        agent_data = {
            'agent_type': 'individual',
            'business_name': 'Test Business',
            'region': 'Riyadh',
            'city': 'Riyadh',
            'primary_phone': '+966501234567'
        }
        
        # Create agent profile
        result = await agent_service.create_agent_profile(
            user_id='user_test_123',
            agent_data=agent_data,
            created_by='admin_test'
        )
        
        # Assertions
        assert result is not None
        assert result.id == 'agent_test_123'
        assert result.agent_code == 'AGTGEN000001'
        assert result.agent_type == AgentType.INDIVIDUAL
        assert result.status == AgentStatus.PENDING
        assert result.region == 'Riyadh'
        
        # Verify database calls
        conn.fetchrow.assert_called_once()
        agent_service._create_initial_performance_record.assert_called_once_with('agent_test_123')
    
    @pytest.mark.asyncio
    async def test_create_agent_profile_invalid_user(self, agent_service):
        """اختبار إنشاء ملف وكيل لمستخدم غير صحيح"""
        # Setup mocks
        agent_service._validate_user_for_agent = AsyncMock(return_value=False)
        
        # Test data
        agent_data = {
            'agent_type': 'individual',
            'region': 'Riyadh',
            'city': 'Riyadh'
        }
        
        # Create agent profile
        result = await agent_service.create_agent_profile(
            user_id='invalid_user',
            agent_data=agent_data,
            created_by='admin_test'
        )
        
        # Assertions
        assert result is None
    
    @pytest.mark.asyncio
    async def test_approve_agent_success(self, agent_service, mock_db_connection):
        """اختبار الموافقة على الوكيل بنجاح"""
        db_connection, conn = mock_db_connection
        
        # Mock database response
        conn.fetchval.return_value = 'AGTGEN000001'
        
        # Approve agent
        result = await agent_service.approve_agent(
            agent_id='agent_test_123',
            approved_by='admin_test',
            approval_notes='Approved after verification'
        )
        
        # Assertions
        assert result is True
        conn.fetchval.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_agent_hierarchy(self, agent_service, mock_db_connection):
        """اختبار الحصول على الهيكل الهرمي للوكيل"""
        db_connection, conn = mock_db_connection
        
        # Mock hierarchy data
        hierarchy_data = [
            {
                'id': 'agent_parent_123',
                'user_id': 'user_parent_123',
                'agent_code': 'AGTGEN000001',
                'agent_type': 'business',
                'status': 'active',
                'parent_agent_id': None,
                'level': 1,
                'hierarchy_path': 'agent_parent_123',
                'business_name': 'Parent Business',
                'region': 'Riyadh',
                'city': 'Riyadh',
                'base_commission_rate': Decimal('0.0150'),
                'tier_commission_rate': Decimal('0.0075'),
                'volume_bonus_rate': Decimal('0.0040'),
                'daily_transaction_limit': Decimal('200000'),
                'monthly_transaction_limit': Decimal('5000000'),
                'single_transaction_limit': Decimal('100000'),
                'total_transactions': 50,
                'total_volume': Decimal('250000'),
                'total_commission_earned': Decimal('3750'),
                'customer_count': 10,
                'rating': Decimal('4.8'),
                'review_count': 25,
                'training_completed': True,
                'certification_level': 5,
                'onboarding_completed': True,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            },
            {
                'id': 'agent_child_123',
                'user_id': 'user_child_123',
                'agent_code': 'AGTGEN000002',
                'agent_type': 'individual',
                'status': 'active',
                'parent_agent_id': 'agent_parent_123',
                'level': 2,
                'hierarchy_path': 'agent_parent_123.agent_child_123',
                'business_name': 'Child Business',
                'region': 'Riyadh',
                'city': 'Riyadh',
                'base_commission_rate': Decimal('0.0100'),
                'tier_commission_rate': Decimal('0.0050'),
                'volume_bonus_rate': Decimal('0.0025'),
                'daily_transaction_limit': Decimal('100000'),
                'monthly_transaction_limit': Decimal('2000000'),
                'single_transaction_limit': Decimal('50000'),
                'total_transactions': 25,
                'total_volume': Decimal('125000'),
                'total_commission_earned': Decimal('1250'),
                'customer_count': 5,
                'rating': Decimal('4.5'),
                'review_count': 12,
                'training_completed': True,
                'certification_level': 3,
                'onboarding_completed': True,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }
        ]
        
        conn.fetch.return_value = hierarchy_data
        
        # Get hierarchy
        result = await agent_service.get_agent_hierarchy('agent_parent_123')
        
        # Assertions
        assert len(result) == 2
        assert result[0].id == 'agent_parent_123'
        assert result[0].level == 1
        assert result[1].id == 'agent_child_123'
        assert result[1].level == 2
        assert result[1].parent_agent_id == 'agent_parent_123'
    
    @pytest.mark.asyncio
    async def test_get_agent_performance(self, agent_service, mock_db_connection):
        """اختبار الحصول على أداء الوكيل"""
        db_connection, conn = mock_db_connection
        
        # Mock performance data
        performance_data = [
            {
                'id': 'perf_123',
                'agent_id': 'agent_test_123',
                'period_type': 'monthly',
                'period_start': date(2024, 1, 1),
                'period_end': date(2024, 1, 31),
                'transaction_count': 25,
                'transaction_volume': Decimal('125000'),
                'average_transaction_amount': Decimal('5000'),
                'total_commission': Decimal('1250'),
                'direct_commission': Decimal('1000'),
                'tier_commission': Decimal('200'),
                'bonus_commission': Decimal('50'),
                'new_customers': 3,
                'active_customers': 8,
                'customer_retention_rate': Decimal('0.95'),
                'success_rate': Decimal('0.98'),
                'average_processing_time': 120,
                'customer_satisfaction': Decimal('4.7'),
                'complaint_count': 1,
                'regional_rank': 5,
                'national_rank': 25,
                'volume_target': Decimal('150000'),
                'volume_achievement_rate': Decimal('0.83'),
                'transaction_target': 30,
                'transaction_achievement_rate': Decimal('0.83')
            }
        ]
        
        conn.fetch.return_value = performance_data
        
        # Get performance
        result = await agent_service.get_agent_performance(
            agent_id='agent_test_123',
            period_type='monthly'
        )
        
        # Assertions
        assert len(result) == 1
        assert result[0].agent_id == 'agent_test_123'
        assert result[0].transaction_count == 25
        assert result[0].transaction_volume == Decimal('125000')
        assert result[0].total_commission == Decimal('1250')
        assert result[0].regional_rank == 5


class TestCommissionEngine:
    """اختبارات محرك العمولات"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        conn.execute = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def commission_engine(self, mock_db_connection):
        """محرك العمولات للاختبار"""
        db_connection, _ = mock_db_connection
        return CommissionEngine(db_connection)
    
    @pytest.mark.asyncio
    async def test_calculate_commission_success(self, commission_engine, mock_db_connection):
        """اختبار حساب العمولة بنجاح"""
        db_connection, conn = mock_db_connection
        
        # Setup mocks
        commission_engine._get_agent_profile = AsyncMock(return_value={
            'id': 'agent_test_123',
            'agent_type': 'individual',
            'status': 'active',
            'base_commission_rate': Decimal('0.0100'),
            'tier_commission_rate': Decimal('0.0050'),
            'volume_bonus_rate': Decimal('0.0025')
        })
        
        commission_engine._get_agent_tier = AsyncMock(return_value=CommissionTier.SILVER)
        commission_engine._get_commission_rules = AsyncMock(return_value=[
            {'name': 'individual_silver_base', 'type': 'base_commission', 'rate': Decimal('0.0100')}
        ])
        commission_engine._calculate_base_commission = AsyncMock(return_value=Decimal('100.00'))
        commission_engine._calculate_tier_commission = AsyncMock(return_value=Decimal('0.00'))
        commission_engine._calculate_volume_bonus = AsyncMock(return_value=Decimal('25.00'))
        commission_engine._calculate_special_bonus = AsyncMock(return_value=Decimal('0.00'))
        commission_engine._store_commission_record = AsyncMock()
        
        # Calculate commission
        result = await commission_engine.calculate_commission(
            agent_id='agent_test_123',
            transaction_id='txn_test_123',
            transaction_amount=Decimal('10000.00')
        )
        
        # Assertions
        assert result is not None
        assert result.agent_id == 'agent_test_123'
        assert result.transaction_id == 'txn_test_123'
        assert result.base_amount == Decimal('100.00')
        assert result.tier_amount == Decimal('0.00')
        assert result.volume_bonus == Decimal('25.00')
        assert result.special_bonus == Decimal('0.00')
        assert result.total_amount == Decimal('125.00')
        
        # Verify method calls
        commission_engine._get_agent_profile.assert_called_once_with('agent_test_123')
        commission_engine._store_commission_record.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_calculate_hierarchical_commissions(self, commission_engine, mock_db_connection):
        """اختبار حساب العمولات الهرمية"""
        db_connection, conn = mock_db_connection
        
        # Setup mocks
        hierarchy_data = [
            {'id': 'agent_level_1', 'level': 0},
            {'id': 'agent_level_2', 'level': 1},
            {'id': 'agent_level_3', 'level': 2}
        ]
        
        commission_engine._get_agent_hierarchy_upward = AsyncMock(return_value=hierarchy_data)
        commission_engine.calculate_commission = AsyncMock(return_value=CommissionCalculation(
            agent_id='agent_level_1',
            transaction_id='txn_test_123',
            base_amount=Decimal('100.00'),
            tier_amount=Decimal('0.00'),
            volume_bonus=Decimal('25.00'),
            special_bonus=Decimal('0.00'),
            total_amount=Decimal('125.00'),
            applied_rules=['base_rule'],
            calculation_details={}
        ))
        commission_engine._get_tier_rate_for_level = AsyncMock(side_effect=[
            Decimal('0.0050'),  # Level 1
            Decimal('0.0025')   # Level 2
        ])
        commission_engine._store_commission_record = AsyncMock()
        
        # Calculate hierarchical commissions
        result = await commission_engine.calculate_hierarchical_commissions(
            transaction_id='txn_test_123',
            primary_agent_id='agent_level_1',
            transaction_amount=Decimal('10000.00')
        )
        
        # Assertions
        assert len(result) == 3  # Primary + 2 tier levels
        assert result[0].agent_id == 'agent_level_1'
        assert result[0].total_amount == Decimal('125.00')  # Full commission
        
        # Verify tier commissions
        assert result[1].tier_amount == Decimal('50.00')  # 10000 * 0.0050
        assert result[2].tier_amount == Decimal('25.00')  # 10000 * 0.0025
    
    @pytest.mark.asyncio
    async def test_get_agent_tier_bronze(self, commission_engine, mock_db_connection):
        """اختبار تحديد مستوى الوكيل - برونزي"""
        db_connection, conn = mock_db_connection
        
        # Mock low volume
        conn.fetchval.return_value = Decimal('50000.00')
        
        # Get tier
        tier = await commission_engine._get_agent_tier('agent_test_123')
        
        # Assertions
        assert tier == CommissionTier.BRONZE
    
    @pytest.mark.asyncio
    async def test_get_agent_tier_gold(self, commission_engine, mock_db_connection):
        """اختبار تحديد مستوى الوكيل - ذهبي"""
        db_connection, conn = mock_db_connection
        
        # Mock high volume
        conn.fetchval.return_value = Decimal('750000.00')
        
        # Get tier
        tier = await commission_engine._get_agent_tier('agent_test_123')
        
        # Assertions
        assert tier == CommissionTier.GOLD
    
    @pytest.mark.asyncio
    async def test_process_monthly_commissions(self, commission_engine, mock_db_connection):
        """اختبار معالجة العمولات الشهرية"""
        db_connection, conn = mock_db_connection
        
        # Setup mocks
        pending_commissions = [
            {
                'id': 'comm_1',
                'agent_id': 'agent_1',
                'commission_amount': Decimal('100.00')
            },
            {
                'id': 'comm_2',
                'agent_id': 'agent_2',
                'commission_amount': Decimal('150.00')
            }
        ]
        
        commission_engine._get_pending_commissions = AsyncMock(return_value=pending_commissions)
        commission_engine._validate_commission = AsyncMock(return_value=True)
        commission_engine._approve_commission = AsyncMock()
        commission_engine._generate_payment_batches = AsyncMock(return_value=['batch_1', 'batch_2'])
        
        # Process monthly commissions
        result = await commission_engine.process_monthly_commissions('2024-01')
        
        # Assertions
        assert result['month'] == '2024-01'
        assert result['processed_count'] == 2
        assert result['total_amount'] == 250.0
        assert result['failed_count'] == 0
        assert result['payment_batches'] == 2
        
        # Verify method calls
        commission_engine._approve_commission.assert_any_call('comm_1')
        commission_engine._approve_commission.assert_any_call('comm_2')


class TestAgentDashboard:
    """اختبارات لوحة تحكم الوكلاء"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """اتصال قاعدة البيانات المزيف"""
        db_connection = AsyncMock()
        
        conn = AsyncMock()
        conn.fetchrow = AsyncMock()
        conn.fetchval = AsyncMock()
        conn.fetch = AsyncMock()
        
        db_connection.get_connection = AsyncMock()
        db_connection.get_connection.return_value.__aenter__ = AsyncMock(return_value=conn)
        db_connection.get_connection.return_value.__aexit__ = AsyncMock(return_value=None)
        
        return db_connection, conn
    
    @pytest.fixture
    def agent_dashboard(self, mock_db_connection):
        """لوحة تحكم الوكلاء للاختبار"""
        db_connection, _ = mock_db_connection
        return AgentDashboard(db_connection)
    
    @pytest.mark.asyncio
    async def test_get_real_time_metrics(self, agent_dashboard, mock_db_connection):
        """اختبار الحصول على المقاييس في الوقت الفعلي"""
        db_connection, conn = mock_db_connection
        
        # Mock database responses
        conn.fetchrow.side_effect = [
            {
                'today_transactions': 5,
                'today_volume': Decimal('25000.00'),
                'today_avg_amount': Decimal('5000.00')
            }
        ]
        
        conn.fetchval.side_effect = [
            Decimal('1250.00'),  # month_commission
            8  # active_customers
        ]
        
        # Get real-time metrics
        result = await agent_dashboard.get_real_time_metrics('agent_test_123')
        
        # Assertions
        assert result['today_transactions'] == 5
        assert result['today_volume'] == 25000.0
        assert result['today_avg_amount'] == 5000.0
        assert result['month_commission'] == 1250.0
        assert result['active_customers'] == 8
        assert 'timestamp' in result
    
    @pytest.mark.asyncio
    async def test_get_commission_breakdown(self, agent_dashboard, mock_db_connection):
        """اختبار الحصول على تفصيل العمولات"""
        db_connection, conn = mock_db_connection
        
        # Mock database responses
        breakdown_data = [
            {
                'commission_type': 'direct',
                'count': 15,
                'total_amount': Decimal('1500.00'),
                'avg_amount': Decimal('100.00'),
                'min_amount': Decimal('50.00'),
                'max_amount': Decimal('200.00')
            },
            {
                'commission_type': 'tier',
                'count': 5,
                'total_amount': Decimal('250.00'),
                'avg_amount': Decimal('50.00'),
                'min_amount': Decimal('25.00'),
                'max_amount': Decimal('75.00')
            }
        ]
        
        daily_data = [
            {
                'commission_date': date(2024, 1, 15),
                'daily_count': 3,
                'daily_amount': Decimal('300.00')
            }
        ]
        
        status_data = [
            {
                'status': 'approved',
                'count': 18,
                'amount': Decimal('1650.00')
            },
            {
                'status': 'pending',
                'count': 2,
                'amount': Decimal('100.00')
            }
        ]
        
        conn.fetch.side_effect = [breakdown_data, daily_data, status_data]
        
        # Get commission breakdown
        result = await agent_dashboard.get_commission_breakdown('agent_test_123', '2024-01')
        
        # Assertions
        assert result['month'] == '2024-01'
        assert len(result['breakdown_by_type']) == 2
        assert result['breakdown_by_type'][0]['type'] == 'direct'
        assert result['breakdown_by_type'][0]['total_amount'] == 1500.0
        assert len(result['daily_commissions']) == 1
        assert len(result['status_breakdown']) == 2
    
    def test_dashboard_cache(self, agent_dashboard):
        """اختبار آلية التخزين المؤقت للوحة التحكم"""
        # Test cache initialization
        assert agent_dashboard.cache_duration == 300
        assert agent_dashboard.dashboard_cache == {}
        
        # Test cache key generation
        cache_key = f"dashboard_agent_test_123"
        assert cache_key not in agent_dashboard.dashboard_cache


# Integration Tests
class TestAgentSystemIntegration:
    """اختبارات التكامل لنظام الوكلاء"""
    
    @pytest.mark.asyncio
    async def test_full_agent_workflow(self):
        """اختبار تدفق العمل الكامل للوكيل"""
        # This would test the complete workflow:
        # 1. Create agent
        # 2. Approve agent
        # 3. Process transactions
        # 4. Calculate commissions
        # 5. Generate reports
        pass


# Performance Tests
class TestAgentSystemPerformance:
    """اختبارات الأداء لنظام الوكلاء"""
    
    def test_commission_calculation_performance(self):
        """اختبار أداء حساب العمولات"""
        # Test that commission calculations complete within acceptable time
        pass
    
    def test_hierarchy_query_performance(self):
        """اختبار أداء استعلامات الهيكل الهرمي"""
        # Test that hierarchy queries are optimized
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
