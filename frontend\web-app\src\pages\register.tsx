import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Link,
  Divider,
  Container,
  Paper,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Person,
  Phone,
  PersonAdd,
  DateRange,
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Head from 'next/head';

// Validation schema
const validationSchema = Yup.object({
  firstName: Yup.string()
    .min(2, 'الاسم الأول يجب أن يكون على الأقل حرفين')
    .max(50, 'الاسم الأول يجب أن يكون أقل من 50 حرف')
    .required('الاسم الأول مطلوب'),
  lastName: Yup.string()
    .min(2, 'اسم العائلة يجب أن يكون على الأقل حرفين')
    .max(50, 'اسم العائلة يجب أن يكون أقل من 50 حرف')
    .required('اسم العائلة مطلوب'),
  email: Yup.string()
    .email('البريد الإلكتروني غير صالح')
    .required('البريد الإلكتروني مطلوب'),
  phone: Yup.string()
    .matches(/^(\+966|0)?[5][0-9]{8}$/, 'رقم الهاتف غير صالح')
    .required('رقم الهاتف مطلوب'),
  password: Yup.string()
    .min(8, 'كلمة المرور يجب أن تكون على الأقل 8 أحرف')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم')
    .required('كلمة المرور مطلوبة'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'كلمات المرور غير متطابقة')
    .required('تأكيد كلمة المرور مطلوب'),
  dateOfBirth: Yup.date()
    .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'يجب أن تكون 18 سنة أو أكثر')
    .required('تاريخ الميلاد مطلوب'),
  nationality: Yup.string().required('الجنسية مطلوبة'),
  agreeToTerms: Yup.boolean()
    .oneOf([true], 'يجب الموافقة على الشروط والأحكام')
    .required('يجب الموافقة على الشروط والأحكام'),
});

const RegisterPage: React.FC = () => {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const formik = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      dateOfBirth: '',
      nationality: 'SA',
      agreeToTerms: false,
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            firstName: values.firstName,
            lastName: values.lastName,
            email: values.email,
            phone: values.phone,
            password: values.password,
            dateOfBirth: values.dateOfBirth,
            nationality: values.nationality,
          }),
        });

        if (response.ok) {
          setSuccess(true);
          setTimeout(() => {
            router.push('/login?message=registration_success');
          }, 2000);
        } else {
          const errorData = await response.json();
          setError(errorData.message || 'فشل في إنشاء الحساب');
        }
      } catch (err) {
        setError('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    },
  });

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  if (success) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 2,
        }}
      >
        <Container maxWidth="sm">
          <Paper elevation={24} sx={{ borderRadius: 4, p: 4, textAlign: 'center' }}>
            <Typography variant="h4" color="success.main" gutterBottom>
              تم إنشاء الحساب بنجاح!
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              سيتم توجيهك إلى صفحة تسجيل الدخول خلال ثوانٍ...
            </Typography>
            <Button
              variant="contained"
              onClick={() => router.push('/login')}
              sx={{
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              }}
            >
              الذهاب لتسجيل الدخول
            </Button>
          </Paper>
        </Container>
      </Box>
    );
  }

  return (
    <>
      <Head>
        <title>إنشاء حساب جديد - WS Transfir</title>
        <meta name="description" content="إنشاء حساب جديد في نظام WS Transfir للتحويلات المالية" />
      </Head>

      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 2,
        }}
      >
        <Container maxWidth="md">
          <Paper
            elevation={24}
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
            }}
          >
            {/* Header */}
            <Box
              sx={{
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                color: 'white',
                padding: 3,
                textAlign: 'center',
              }}
            >
              <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                WS Transfir
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                إنشاء حساب جديد
              </Typography>
            </Box>

            <CardContent sx={{ padding: 4 }}>
              <Typography variant="h5" component="h2" textAlign="center" gutterBottom>
                انضم إلينا اليوم
              </Typography>
              <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 3 }}>
                أدخل بياناتك لإنشاء حساب جديد
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="firstName"
                      name="firstName"
                      label="الاسم الأول"
                      value={formik.values.firstName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                      helperText={formik.touched.firstName && formik.errors.firstName}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Person color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="lastName"
                      name="lastName"
                      label="اسم العائلة"
                      value={formik.values.lastName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                      helperText={formik.touched.lastName && formik.errors.lastName}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Person color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="email"
                      name="email"
                      label="البريد الإلكتروني"
                      type="email"
                      value={formik.values.email}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.email && Boolean(formik.errors.email)}
                      helperText={formik.touched.email && formik.errors.email}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Email color="action" />
                          </InputAdornment>
                        ),
                      }}
                      dir="ltr"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="phone"
                      name="phone"
                      label="رقم الهاتف"
                      value={formik.values.phone}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.phone && Boolean(formik.errors.phone)}
                      helperText={formik.touched.phone && formik.errors.phone}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="action" />
                          </InputAdornment>
                        ),
                      }}
                      dir="ltr"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="password"
                      name="password"
                      label="كلمة المرور"
                      type={showPassword ? 'text' : 'password'}
                      value={formik.values.password}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.password && Boolean(formik.errors.password)}
                      helperText={formik.touched.password && formik.errors.password}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Lock color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              aria-label="toggle password visibility"
                              onClick={handleTogglePasswordVisibility}
                              edge="end"
                            >
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      dir="ltr"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="confirmPassword"
                      name="confirmPassword"
                      label="تأكيد كلمة المرور"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formik.values.confirmPassword}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
                      helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Lock color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              aria-label="toggle confirm password visibility"
                              onClick={handleToggleConfirmPasswordVisibility}
                              edge="end"
                            >
                              {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      dir="ltr"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      id="dateOfBirth"
                      name="dateOfBirth"
                      label="تاريخ الميلاد"
                      type="date"
                      value={formik.values.dateOfBirth}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.dateOfBirth && Boolean(formik.errors.dateOfBirth)}
                      helperText={formik.touched.dateOfBirth && formik.errors.dateOfBirth}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <DateRange color="action" />
                          </InputAdornment>
                        ),
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>الجنسية</InputLabel>
                      <Select
                        id="nationality"
                        name="nationality"
                        value={formik.values.nationality}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.nationality && Boolean(formik.errors.nationality)}
                        label="الجنسية"
                      >
                        <MenuItem value="SA">السعودية</MenuItem>
                        <MenuItem value="AE">الإمارات</MenuItem>
                        <MenuItem value="KW">الكويت</MenuItem>
                        <MenuItem value="QA">قطر</MenuItem>
                        <MenuItem value="BH">البحرين</MenuItem>
                        <MenuItem value="OM">عمان</MenuItem>
                        <MenuItem value="JO">الأردن</MenuItem>
                        <MenuItem value="LB">لبنان</MenuItem>
                        <MenuItem value="EG">مصر</MenuItem>
                        <MenuItem value="OTHER">أخرى</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          id="agreeToTerms"
                          name="agreeToTerms"
                          checked={formik.values.agreeToTerms}
                          onChange={formik.handleChange}
                          color="primary"
                        />
                      }
                      label={
                        <Typography variant="body2">
                          أوافق على{' '}
                          <Link href="/terms" target="_blank" sx={{ textDecoration: 'none' }}>
                            الشروط والأحكام
                          </Link>{' '}
                          و{' '}
                          <Link href="/privacy" target="_blank" sx={{ textDecoration: 'none' }}>
                            سياسة الخصوصية
                          </Link>
                        </Typography>
                      }
                    />
                    {formik.touched.agreeToTerms && formik.errors.agreeToTerms && (
                      <Typography variant="caption" color="error" display="block">
                        {formik.errors.agreeToTerms}
                      </Typography>
                    )}
                  </Grid>
                </Grid>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  startIcon={<PersonAdd />}
                  sx={{
                    mt: 3,
                    mb: 2,
                    py: 1.5,
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
                    },
                  }}
                >
                  {loading ? 'جاري إنشاء الحساب...' : 'إنشاء حساب'}
                </Button>

                <Divider sx={{ my: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    أو
                  </Typography>
                </Divider>

                <Box textAlign="center">
                  <Typography variant="body2" color="text.secondary">
                    لديك حساب بالفعل؟{' '}
                    <Link
                      href="/login"
                      variant="body2"
                      sx={{ textDecoration: 'none', fontWeight: 'bold' }}
                    >
                      تسجيل الدخول
                    </Link>
                  </Typography>
                </Box>
              </form>
            </CardContent>
          </Paper>

          {/* Footer */}
          <Box textAlign="center" sx={{ mt: 3 }}>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
              © 2024 WS Transfir. جميع الحقوق محفوظة.
            </Typography>
          </Box>
        </Container>
      </Box>
    </>
  );
};

export default RegisterPage;
