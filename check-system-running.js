/**
 * WS Transfir System Running Status Check
 * فحص حالة تشغيل نظام WS Transfir
 */

const http = require('http');
const https = require('https');

console.log('🔍 فحص حالة تشغيل نظام WS Transfir');
console.log('=====================================');
console.log('');

// Function to check if a service is running
function checkService(url, name) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https') ? https : http;
    const request = protocol.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          name,
          url,
          status: 'running',
          statusCode: res.statusCode,
          response: data
        });
      });
    });

    request.on('error', (err) => {
      resolve({
        name,
        url,
        status: 'not running',
        error: err.message
      });
    });

    request.setTimeout(5000, () => {
      request.destroy();
      resolve({
        name,
        url,
        status: 'timeout',
        error: 'Request timeout'
      });
    });
  });
}

// Services to check
const services = [
  { name: 'Frontend (Next.js)', url: 'http://localhost:3100' },
  { name: 'API Server', url: 'http://localhost:3000/api/health' },
  { name: 'API Health Check', url: 'http://localhost:3000/api/health' }
];

async function checkAllServices() {
  console.log('🔍 فحص الخدمات...');
  console.log('==================');
  
  const results = await Promise.all(
    services.map(service => checkService(service.url, service.name))
  );

  let runningServices = 0;
  let totalServices = services.length;

  results.forEach(result => {
    const statusIcon = result.status === 'running' ? '✅' : '❌';
    console.log(`${statusIcon} ${result.name}`);
    console.log(`   🌐 URL: ${result.url}`);
    
    if (result.status === 'running') {
      console.log(`   📊 Status Code: ${result.statusCode}`);
      runningServices++;
      
      // Try to parse JSON response for API health
      if (result.name.includes('API') && result.response) {
        try {
          const jsonResponse = JSON.parse(result.response);
          if (jsonResponse.status) {
            console.log(`   💚 Health: ${jsonResponse.status}`);
          }
          if (jsonResponse.message) {
            console.log(`   📝 Message: ${jsonResponse.message}`);
          }
        } catch (e) {
          console.log(`   📄 Response: ${result.response.substring(0, 100)}...`);
        }
      }
    } else {
      console.log(`   ❌ Error: ${result.error}`);
    }
    console.log('');
  });

  // Summary
  console.log('📊 ملخص الحالة:');
  console.log('===============');
  console.log(`🟢 خدمات تعمل: ${runningServices}/${totalServices}`);
  console.log(`🔴 خدمات متوقفة: ${totalServices - runningServices}/${totalServices}`);
  
  const percentage = Math.round((runningServices / totalServices) * 100);
  console.log(`📈 نسبة التشغيل: ${percentage}%`);
  console.log('');

  if (percentage === 100) {
    console.log('🎉 جميع الخدمات تعمل بشكل طبيعي!');
    console.log('');
    console.log('🌐 روابط الوصول:');
    console.log('================');
    console.log('📱 التطبيق الرئيسي: http://localhost:3100');
    console.log('🔧 API Server: http://localhost:3000');
    console.log('📊 Health Check: http://localhost:3000/api/health');
    console.log('');
    console.log('🔐 بيانات الدخول التجريبية:');
    console.log('============================');
    console.log('👨‍💼 مدير النظام: <EMAIL> / admin123');
    console.log('👤 مستخدم عادي: <EMAIL> / password123');
    console.log('');
    console.log('📋 الصفحات المتاحة:');
    console.log('===================');
    console.log('✅ الرئيسية: http://localhost:3100');
    console.log('✅ تسجيل الدخول: http://localhost:3100/login');
    console.log('✅ إنشاء حساب: http://localhost:3100/register');
    console.log('✅ الملف الشخصي: http://localhost:3100/profile');
    console.log('✅ التحويلات: http://localhost:3100/transfers');
    console.log('✅ استرداد كلمة المرور: http://localhost:3100/forgot-password');
    console.log('');
  } else if (percentage >= 50) {
    console.log('⚠️ بعض الخدمات تعمل، تحقق من الخدمات المتوقفة');
  } else {
    console.log('🔴 معظم الخدمات متوقفة، يرجى إعادة التشغيل');
    console.log('');
    console.log('🔧 خطوات الإصلاح:');
    console.log('=================');
    console.log('1. تأكد من تشغيل run-system.bat');
    console.log('2. تحقق من أن Node.js مثبت');
    console.log('3. تحقق من أن المنافذ 3000 و 3100 متاحة');
    console.log('4. أعد تشغيل النظام');
  }

  return { runningServices, totalServices, percentage };
}

// Test API endpoints
async function testAPIEndpoints() {
  console.log('🧪 اختبار نقاط API:');
  console.log('===================');
  
  const endpoints = [
    { name: 'Health Check', url: 'http://localhost:3000/api/health', method: 'GET' },
    { name: 'Login Endpoint', url: 'http://localhost:3000/api/auth/login', method: 'POST' },
    { name: 'Profile Endpoint', url: 'http://localhost:3000/api/profile/me', method: 'GET' },
    { name: 'Transfers Endpoint', url: 'http://localhost:3000/api/transfers', method: 'GET' }
  ];

  for (const endpoint of endpoints) {
    try {
      const result = await checkService(endpoint.url, endpoint.name);
      const statusIcon = result.status === 'running' ? '✅' : '❌';
      console.log(`${statusIcon} ${endpoint.name} (${endpoint.method})`);
      
      if (result.status === 'running') {
        console.log(`   📊 Status: ${result.statusCode}`);
      } else {
        console.log(`   ❌ Error: ${result.error}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.message}`);
    }
  }
  console.log('');
}

// Main execution
async function main() {
  console.log('📅 التاريخ:', new Date().toLocaleDateString('ar-SA'));
  console.log('⏰ الوقت:', new Date().toLocaleTimeString('ar-SA'));
  console.log('');

  const systemStatus = await checkAllServices();
  
  if (systemStatus.percentage > 0) {
    await testAPIEndpoints();
  }

  console.log('💡 نصائح:');
  console.log('=========');
  console.log('🔹 إذا كانت الخدمات لا تعمل، شغل: run-system.bat');
  console.log('🔹 للمراقبة المستمرة، شغل هذا الأمر كل دقيقة');
  console.log('🔹 تحقق من نوافذ الأوامر للأخطاء');
  console.log('🔹 استخدم Ctrl+C لإيقاف الخدمات');
  console.log('');

  console.log('🔄 لإعادة فحص النظام، شغل: node check-system-running.js');
  console.log('');
}

// Run the check
main().catch(console.error);
