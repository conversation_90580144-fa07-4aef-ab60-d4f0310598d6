'use client';

import { useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  HomeIcon,
  ArrowsRightLeftIcon,
  WalletIcon,
  UserIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon,
  ChartBarIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { clsx } from 'clsx';

// عناصر التنقل
const navigationItems = [
  {
    name: 'نظرة عامة',
    href: '/dashboard',
    icon: HomeIcon,
    exact: true,
  },
  {
    name: 'التحويلات',
    icon: ArrowsRightLeftIcon,
    children: [
      { name: 'إرسال حوالة', href: '/dashboard/transfers/send' },
      { name: 'استلام حوالة', href: '/dashboard/transfers/receive' },
      { name: 'سجل التحويلات', href: '/dashboard/transfers/history' },
      { name: 'تتبع الحوالة', href: '/dashboard/transfers/track' },
    ],
  },
  {
    name: 'المحفظة',
    icon: WalletIcon,
    children: [
      { name: 'الرصيد', href: '/dashboard/wallet/balance' },
      { name: 'شحن المحفظة', href: '/dashboard/wallet/top-up' },
      { name: 'سحب من المحفظة', href: '/dashboard/wallet/withdraw' },
      { name: 'المعاملات', href: '/dashboard/wallet/transactions' },
      { name: 'دفع الفواتير', href: '/dashboard/wallet/bills' },
    ],
  },
  {
    name: 'التقارير',
    href: '/dashboard/reports',
    icon: ChartBarIcon,
  },
  {
    name: 'الوثائق',
    href: '/dashboard/documents',
    icon: DocumentTextIcon,
  },
  {
    name: 'الملف الشخصي',
    icon: UserIcon,
    children: [
      { name: 'المعلومات الشخصية', href: '/dashboard/profile/personal' },
      { name: 'التحقق من الهوية', href: '/dashboard/profile/kyc' },
      { name: 'الأمان', href: '/dashboard/profile/security' },
      { name: 'التفضيلات', href: '/dashboard/profile/preferences' },
    ],
  },
  {
    name: 'الإعدادات',
    href: '/dashboard/settings',
    icon: Cog6ToothIcon,
  },
  {
    name: 'المساعدة',
    href: '/dashboard/help',
    icon: QuestionMarkCircleIcon,
  },
];

interface SidebarProps {
  onClose?: () => void;
}

export default function Sidebar({ onClose }: SidebarProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const pathname = usePathname();
  const router = useRouter();
  const { t, isRTL } = useLanguage();
  const { state: authState } = useAuth();

  // تبديل توسيع العنصر
  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  // التحقق من النشاط
  const isActive = (href: string, exact = false) => {
    if (exact) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  // التحقق من وجود عنصر فرعي نشط
  const hasActiveChild = (children: any[]) => {
    return children.some(child => isActive(child.href));
  };

  // معالجة النقر على الرابط
  const handleNavigation = (href: string) => {
    router.push(href);
    if (onClose) {
      onClose();
    }
  };

  return (
    <div className="flex h-full flex-col bg-white dark:bg-gray-800 shadow-xl">
      {/* الرأس */}
      <div className="flex h-16 items-center justify-between px-6 border-b border-gray-200 dark:border-gray-700">
        {/* الشعار */}
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-primary-700 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">WS</span>
          </div>
          <span className="text-lg font-bold text-gray-900 dark:text-white">
            WS Transfir
          </span>
        </div>

        {/* زر الإغلاق للشاشات الصغيرة */}
        {onClose && (
          <button
            type="button"
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
            onClick={onClose}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        )}
      </div>

      {/* معلومات المستخدم */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {authState.user?.avatar ? (
            <img
              className="h-10 w-10 rounded-full"
              src={authState.user.avatar}
              alt={`${authState.user.firstName} ${authState.user.lastName}`}
            />
          ) : (
            <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/20 flex items-center justify-center">
              <UserIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {authState.user?.firstName} {authState.user?.lastName}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {authState.user?.email}
            </p>
          </div>
          {/* مؤشر حالة التحقق */}
          {authState.user?.isVerified && (
            <ShieldCheckIcon className="h-5 w-5 text-success-500" />
          )}
        </div>
      </div>

      {/* التنقل */}
      <nav className="flex-1 overflow-y-auto py-6">
        <ul className="space-y-1 px-3">
          {navigationItems.map((item) => (
            <li key={item.name}>
              {item.children ? (
                // عنصر مع قائمة فرعية
                <div>
                  <button
                    onClick={() => toggleExpanded(item.name)}
                    className={clsx(
                      'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
                      hasActiveChild(item.children) || expandedItems.includes(item.name)
                        ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    )}
                  >
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <item.icon className="h-5 w-5 flex-shrink-0" />
                      <span>{item.name}</span>
                    </div>
                    <motion.div
                      animate={{ rotate: expandedItems.includes(item.name) ? 90 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronRightIcon className="h-4 w-4" />
                    </motion.div>
                  </button>

                  {/* القائمة الفرعية */}
                  <motion.div
                    initial={false}
                    animate={{
                      height: expandedItems.includes(item.name) ? 'auto' : 0,
                      opacity: expandedItems.includes(item.name) ? 1 : 0,
                    }}
                    transition={{ duration: 0.2 }}
                    className="overflow-hidden"
                  >
                    <ul className="mt-1 space-y-1">
                      {item.children.map((child) => (
                        <li key={child.href}>
                          <button
                            onClick={() => handleNavigation(child.href)}
                            className={clsx(
                              'w-full flex items-center px-3 py-2 text-sm rounded-lg transition-colors duration-200',
                              isActive(child.href)
                                ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 font-medium'
                                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200',
                              'mr-8 rtl:mr-0 rtl:ml-8'
                            )}
                          >
                            <span>{child.name}</span>
                          </button>
                        </li>
                      ))}
                    </ul>
                  </motion.div>
                </div>
              ) : (
                // عنصر بسيط
                <button
                  onClick={() => handleNavigation(item.href!)}
                  className={clsx(
                    'w-full flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
                    isActive(item.href!, item.exact)
                      ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  )}
                >
                  <item.icon className="h-5 w-5 flex-shrink-0" />
                  <span>{item.name}</span>
                </button>
              )}
            </li>
          ))}
        </ul>
      </nav>

      {/* التذييل */}
      <div className="p-6 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          <p>WS Transfir v1.0.0</p>
          <p className="mt-1">© 2024 جميع الحقوق محفوظة</p>
        </div>
      </div>
    </div>
  );
}
