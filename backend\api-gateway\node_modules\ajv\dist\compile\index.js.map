{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../lib/compile/index.ts"], "names": [], "mappings": ";;;AAUA,uCAAgF;AAChF,kEAAyD;AACzD,mCAAuB;AACvB,uCAAkG;AAClG,iCAA6D;AAC7D,yCAA+C;AA0D/C,MAAa,SAAS;IAkBpB,YAAY,GAAkB;;QATrB,SAAI,GAAe,EAAE,CAAA;QACrB,mBAAc,GAA6B,EAAE,CAAA;QASpD,IAAI,MAAmC,CAAA;QACvC,IAAI,OAAO,GAAG,CAAC,MAAM,IAAI,QAAQ;YAAE,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;QACtD,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;QACxB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;QAC5B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,CAAA;QAC5B,IAAI,CAAC,MAAM,GAAG,MAAA,GAAG,CAAC,MAAM,mCAAI,IAAA,qBAAW,EAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,GAAG,CAAC,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAA;QACxE,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAA;QAChC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAA;QAC9B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;QACpB,IAAI,CAAC,MAAM,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,CAAA;QAC5B,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;IAChB,CAAC;CACF;AA/BD,8BA+BC;AAED,mBAAmB;AACnB,oBAAoB;AAEpB,+BAA+B;AAC/B,SAAgB,aAAa,CAAY,GAAc;IACrD,sCAAsC;IACtC,MAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC/C,IAAI,IAAI;QAAE,OAAO,IAAI,CAAA;IACrB,MAAM,MAAM,GAAG,IAAA,qBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,CAAC,4CAA4C;IAC/G,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;IACnC,MAAM,EAAC,aAAa,EAAC,GAAG,IAAI,CAAC,IAAI,CAAA;IACjC,MAAM,GAAG,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,KAAK,EAAE,EAAC,GAAG,EAAE,KAAK,EAAE,aAAa,EAAC,CAAC,CAAA;IAChE,IAAI,gBAAgB,CAAA;IACpB,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,gBAAgB,GAAG,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE;YACzC,GAAG,EAAE,0BAAe;YACpB,IAAI,EAAE,IAAA,WAAC,EAAA,sDAAsD;SAC9D,CAAC,CAAA;KACH;IAED,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;IAC9C,GAAG,CAAC,YAAY,GAAG,YAAY,CAAA;IAE/B,MAAM,SAAS,GAAc;QAC3B,GAAG;QACH,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS;QAC9B,IAAI,EAAE,eAAC,CAAC,IAAI;QACZ,UAAU,EAAE,eAAC,CAAC,UAAU;QACxB,kBAAkB,EAAE,eAAC,CAAC,kBAAkB;QACxC,SAAS,EAAE,CAAC,eAAC,CAAC,IAAI,CAAC;QACnB,WAAW,EAAE,CAAC,aAAG,CAAC;QAClB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,EAAE;QACb,iBAAiB,EAAE,IAAI,GAAG,EAAU;QACpC,YAAY,EAAE,GAAG,CAAC,UAAU,CAC1B,QAAQ,EACR,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI;YAC5B,CAAC,CAAC,EAAC,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,IAAA,mBAAS,EAAC,GAAG,CAAC,MAAM,CAAC,EAAC;YAChD,CAAC,CAAC,EAAC,GAAG,EAAE,GAAG,CAAC,MAAM,EAAC,CACtB;QACD,YAAY;QACZ,eAAe,EAAE,gBAAgB;QACjC,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,SAAS,EAAE,GAAG;QACd,MAAM;QACN,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,MAAM;QAC5B,UAAU,EAAE,aAAG;QACf,aAAa,EAAE,GAAG,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3D,SAAS,EAAE,IAAA,WAAC,EAAA,IAAI;QAChB,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,IAAI,EAAE,IAAI;KACX,CAAA;IAED,IAAI,UAA8B,CAAA;IAClC,IAAI;QACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC3B,IAAA,+BAAoB,EAAC,SAAS,CAAC,CAAA;QAC/B,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACrC,kBAAkB;QAClB,MAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAA;QACnC,UAAU,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,eAAC,CAAC,KAAK,CAAC,UAAU,YAAY,EAAE,CAAA;QAC9D,6EAA6E;QAC7E,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;QAChF,2CAA2C;QAC3C,MAAM,YAAY,GAAG,IAAI,QAAQ,CAAC,GAAG,eAAC,CAAC,IAAI,EAAE,EAAE,GAAG,eAAC,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,CAAA;QACxE,MAAM,QAAQ,GAAwB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;QAC1E,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE,EAAC,GAAG,EAAE,QAAQ,EAAC,CAAC,CAAA;QAE/C,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAA;QACtB,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;QAC5B,QAAQ,CAAC,SAAS,GAAG,GAAG,CAAA;QACxB,IAAI,GAAG,CAAC,MAAM;YAAG,QAAkC,CAAC,MAAM,GAAG,IAAI,CAAA;QACjE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;YAClC,QAAQ,CAAC,MAAM,GAAG,EAAC,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,CAAC,OAAO,EAAC,CAAA;SACzE;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACzB,MAAM,EAAC,KAAK,EAAE,KAAK,EAAC,GAAG,SAAS,CAAA;YAChC,QAAQ,CAAC,SAAS,GAAG;gBACnB,KAAK,EAAE,KAAK,YAAY,cAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK;gBAChD,KAAK,EAAE,KAAK,YAAY,cAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK;gBAChD,YAAY,EAAE,KAAK,YAAY,cAAI;gBACnC,YAAY,EAAE,KAAK,YAAY,cAAI;aACpC,CAAA;YACD,IAAI,QAAQ,CAAC,MAAM;gBAAE,QAAQ,CAAC,MAAM,CAAC,SAAS,GAAG,IAAA,mBAAS,EAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;SAC/E;QACD,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACvB,OAAO,GAAG,CAAA;KACX;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,GAAG,CAAC,QAAQ,CAAA;QACnB,OAAO,GAAG,CAAC,YAAY,CAAA;QACvB,IAAI,UAAU;YAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,UAAU,CAAC,CAAA;QACvF,sDAAsD;QACtD,MAAM,CAAC,CAAA;KACR;YAAS;QACR,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;KAC/B;AACH,CAAC;AA5FD,sCA4FC;AAED,SAAgB,UAAU,CAExB,IAAe,EACf,MAAc,EACd,GAAW;;IAEX,GAAG,GAAG,IAAA,oBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,GAAG,CAAC,CAAA;IACpD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAChC,IAAI,SAAS;QAAE,OAAO,SAAS,CAAA;IAE/B,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA;IACxC,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,MAAM,MAAM,GAAG,MAAA,IAAI,CAAC,SAAS,0CAAG,GAAG,CAAC,CAAA,CAAC,6CAA6C;QAClF,MAAM,EAAC,QAAQ,EAAC,GAAG,IAAI,CAAC,IAAI,CAAA;QAC5B,IAAI,MAAM;YAAE,IAAI,GAAG,IAAI,SAAS,CAAC,EAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAC,CAAC,CAAA;KACnE;IAED,IAAI,IAAI,KAAK,SAAS;QAAE,OAAM;IAC9B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;AAC5D,CAAC;AAnBD,gCAmBC;AAED,SAAS,eAAe,CAAY,GAAc;IAChD,IAAI,IAAA,mBAAS,EAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QAAE,OAAO,GAAG,CAAC,MAAM,CAAA;IAClE,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;AAC3D,CAAC;AAED,6DAA6D;AAC7D,SAAgB,kBAAkB,CAAY,MAAiB;IAC7D,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE;QACpC,IAAI,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC;YAAE,OAAO,GAAG,CAAA;KAC3C;AACH,CAAC;AAJD,gDAIC;AAED,SAAS,aAAa,CAAC,EAAa,EAAE,EAAa;IACjD,OAAO,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM,CAAA;AAClF,CAAC;AAED,4CAA4C;AAC5C,qFAAqF;AACrF,SAAS,OAAO,CAEd,IAAe,EAAE,2DAA2D;AAC5E,GAAW,CAAC,uBAAuB;;IAEnC,IAAI,GAAG,CAAA;IACP,OAAO,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ;QAAE,GAAG,GAAG,GAAG,CAAA;IAC3D,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA;AACxE,CAAC;AAED,sCAAsC;AACtC,SAAgB,aAAa,CAE3B,IAAe,EAAE,kFAAkF;AACnG,GAAW,CAAC,uBAAuB;;IAEnC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC1C,MAAM,OAAO,GAAG,IAAA,sBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;IACtD,IAAI,MAAM,GAAG,IAAA,qBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;IACvE,gGAAgG;IAChG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,EAAE;QAC7D,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;KAC1C;IAED,MAAM,EAAE,GAAG,IAAA,qBAAW,EAAC,OAAO,CAAC,CAAA;IAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAClD,IAAI,OAAO,QAAQ,IAAI,QAAQ,EAAE;QAC/B,MAAM,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;QACpD,IAAI,OAAO,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,MAAM,CAAA,KAAK,QAAQ;YAAE,OAAM;QAC3C,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;KACzC;IAED,IAAI,OAAO,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,CAAA,KAAK,QAAQ;QAAE,OAAM;IAChD,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAAE,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;IAC1D,IAAI,EAAE,KAAK,IAAA,qBAAW,EAAC,GAAG,CAAC,EAAE;QAC3B,MAAM,EAAC,MAAM,EAAC,GAAG,QAAQ,CAAA;QACzB,MAAM,EAAC,QAAQ,EAAC,GAAG,IAAI,CAAC,IAAI,CAAA;QAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;QAC9B,IAAI,KAAK;YAAE,MAAM,GAAG,IAAA,oBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;QACpE,OAAO,IAAI,SAAS,CAAC,EAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAC,CAAC,CAAA;KACvD;IACD,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAA;AAC/C,CAAC;AA/BD,sCA+BC;AAED,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC;IACnC,YAAY;IACZ,mBAAmB;IACnB,MAAM;IACN,cAAc;IACd,aAAa;CACd,CAAC,CAAA;AAEF,SAAS,cAAc,CAErB,SAA4B,EAC5B,EAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAY;;IAEjC,IAAI,CAAA,MAAA,SAAS,CAAC,QAAQ,0CAAG,CAAC,CAAC,MAAK,GAAG;QAAE,OAAM;IAC3C,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QACzD,IAAI,OAAO,MAAM,KAAK,SAAS;YAAE,OAAM;QACvC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAA,uBAAgB,EAAC,IAAI,CAAC,CAAC,CAAA;QACjD,IAAI,UAAU,KAAK,SAAS;YAAE,OAAM;QACpC,MAAM,GAAG,UAAU,CAAA;QACnB,6DAA6D;QAC7D,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACtE,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE;YAC5C,MAAM,GAAG,IAAA,oBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;SAC1D;KACF;IACD,IAAI,GAA0B,CAAA;IAC9B,IAAI,OAAO,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,IAAA,2BAAoB,EAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;QAC1F,MAAM,IAAI,GAAG,IAAA,oBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;QACnE,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;KAC3C;IACD,+EAA+E;IAC/E,6CAA6C;IAC7C,MAAM,EAAC,QAAQ,EAAC,GAAG,IAAI,CAAC,IAAI,CAAA;IAC5B,GAAG,GAAG,GAAG,IAAI,IAAI,SAAS,CAAC,EAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAC,CAAC,CAAA;IAC5D,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM;QAAE,OAAO,GAAG,CAAA;IAC9C,OAAO,SAAS,CAAA;AAClB,CAAC"}