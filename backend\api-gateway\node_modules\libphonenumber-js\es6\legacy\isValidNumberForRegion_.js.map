{"version": 3, "file": "isValidNumberForRegion_.js", "names": ["isValidNumber", "isValidNumberForRegion", "input", "country", "options", "metadata"], "sources": ["../../source/legacy/isValidNumberForRegion_.js"], "sourcesContent": ["import isValidNumber from '../isValid.js'\r\n\r\n/**\r\n * Checks if a given phone number is valid within a given region.\r\n * Is just an alias for `phoneNumber.isValid() && phoneNumber.country === country`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n */\r\nexport default function isValidNumberForRegion(input, country, options, metadata) {\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\treturn input.country === country && isValidNumber(input, options, metadata)\r\n}"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,eAA1B;AAEA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,sBAAT,CAAgCC,KAAhC,EAAuCC,OAAvC,EAAgDC,OAAhD,EAAyDC,QAAzD,EAAmE;EACjF;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,EAArB;EACA,OAAOF,KAAK,CAACC,OAAN,KAAkBA,OAAlB,IAA6BH,aAAa,CAACE,KAAD,EAAQE,OAAR,EAAiBC,QAAjB,CAAjD;AACA"}