import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsUUID,
  IsEnum,
  IsNumber,
  IsOptional,
  Min,
  Max,
  IsObject,
  ValidateNested,
  IsEmail,
  IsPhoneNumber,
  Length,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TransferType, TransferPurpose } from '../entities/transfer.entity';

class ReceiverAddressDto {
  @ApiProperty({ description: 'الشارع', example: 'شارع الملك فهد' })
  @IsOptional()
  @IsString()
  street?: string;

  @ApiProperty({ description: 'المدينة', example: 'الرياض' })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({ description: 'المنطقة/الولاية', example: 'الرياض' })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({ description: 'الدولة', example: 'SA' })
  @IsOptional()
  @IsString()
  @Length(2, 2, { message: 'رمز الدولة يجب أن يكون حرفين' })
  country?: string;

  @ApiProperty({ description: 'الرمز البريدي', example: '12345' })
  @IsOptional()
  @IsString()
  postalCode?: string;
}

class PaymentDetailsDto {
  @ApiProperty({ description: 'طريقة الدفع', example: 'wallet' })
  @IsString()
  method: string;

  @ApiProperty({ description: 'معرف المحفظة أو البطاقة', example: 'wallet-id-123' })
  @IsOptional()
  @IsString()
  sourceId?: string;

  @ApiProperty({ description: 'آخر 4 أرقام من البطاقة', example: '1234' })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}$/, { message: 'يجب أن يكون 4 أرقام' })
  cardLast4?: string;
}

class DeliveryDetailsDto {
  @ApiProperty({ description: 'طريقة التسليم', example: 'bank_transfer' })
  @IsString()
  method: string;

  @ApiProperty({ description: 'اسم البنك', example: 'البنك الأهلي السعودي' })
  @IsOptional()
  @IsString()
  bankName?: string;

  @ApiProperty({ description: 'رقم الحساب', example: '**********' })
  @IsOptional()
  @IsString()
  accountNumber?: string;

  @ApiProperty({ description: 'رمز الفرع', example: '001' })
  @IsOptional()
  @IsString()
  branchCode?: string;

  @ApiProperty({ description: 'موقع الاستلام', example: 'فرع الرياض الرئيسي' })
  @IsOptional()
  @IsString()
  pickupLocation?: string;
}

export class CreateTransferDto {
  @ApiProperty({
    description: 'نوع التحويل',
    enum: TransferType,
    example: TransferType.DOMESTIC,
  })
  @IsEnum(TransferType, { message: 'نوع التحويل غير صالح' })
  transferType: TransferType;

  @ApiProperty({
    description: 'الغرض من التحويل',
    enum: TransferPurpose,
    example: TransferPurpose.FAMILY_SUPPORT,
  })
  @IsEnum(TransferPurpose, { message: 'الغرض من التحويل غير صالح' })
  purpose: TransferPurpose;

  @ApiProperty({
    description: 'معرف المستقبل (للمستخدمين المسجلين)',
    example: 'uuid-string',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'معرف المستقبل غير صالح' })
  receiverId?: string;

  @ApiProperty({
    description: 'اسم المستقبل (للمستقبلين غير المسجلين)',
    example: 'أحمد محمد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'اسم المستقبل يجب أن يكون نص' })
  @Length(2, 200, { message: 'اسم المستقبل يجب أن يكون بين 2 و 200 حرف' })
  receiverName?: string;

  @ApiProperty({
    description: 'رقم هاتف المستقبل',
    example: '+************',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber(null, { message: 'رقم هاتف المستقبل غير صالح' })
  receiverPhone?: string;

  @ApiProperty({
    description: 'بريد المستقبل الإلكتروني',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'بريد المستقبل الإلكتروني غير صالح' })
  receiverEmail?: string;

  @ApiProperty({
    description: 'عنوان المستقبل',
    type: ReceiverAddressDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ReceiverAddressDto)
  receiverAddress?: ReceiverAddressDto;

  @ApiProperty({
    description: 'مبلغ الإرسال',
    example: 1000.50,
    minimum: 1,
    maximum: 50000,
  })
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'مبلغ الإرسال يجب أن يكون رقم صالح' })
  @Min(1, { message: 'مبلغ الإرسال يجب أن يكون أكبر من 1' })
  @Max(50000, { message: 'مبلغ الإرسال يجب أن يكون أقل من 50000' })
  sendAmount: number;

  @ApiProperty({
    description: 'عملة الإرسال',
    example: 'SAR',
    enum: ['SAR', 'USD', 'EUR', 'GBP', 'AED'],
  })
  @IsString({ message: 'عملة الإرسال يجب أن تكون نص' })
  @Length(3, 3, { message: 'عملة الإرسال يجب أن تكون 3 أحرف' })
  sendCurrency: string;

  @ApiProperty({
    description: 'عملة الاستقبال',
    example: 'USD',
    enum: ['SAR', 'USD', 'EUR', 'GBP', 'AED'],
  })
  @IsString({ message: 'عملة الاستقبال يجب أن تكون نص' })
  @Length(3, 3, { message: 'عملة الاستقبال يجب أن تكون 3 أحرف' })
  receiveCurrency: string;

  @ApiProperty({
    description: 'وصف التحويل',
    example: 'مساعدة عائلية شهرية',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الوصف يجب أن يكون نص' })
  @Length(0, 500, { message: 'الوصف يجب أن يكون أقل من 500 حرف' })
  description?: string;

  @ApiProperty({
    description: 'تفاصيل الدفع',
    type: PaymentDetailsDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PaymentDetailsDto)
  paymentDetails: PaymentDetailsDto;

  @ApiProperty({
    description: 'تفاصيل التسليم',
    type: DeliveryDetailsDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DeliveryDetailsDto)
  deliveryDetails: DeliveryDetailsDto;

  @ApiProperty({
    description: 'بيانات إضافية',
    example: { notes: 'ملاحظات خاصة' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
