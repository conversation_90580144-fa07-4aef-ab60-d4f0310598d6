{"version": 3, "file": "searchPhoneNumbersInText.js", "names": ["searchPhoneNumbersInText", "normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "PhoneNumberMatcher", "v2", "Symbol", "iterator", "next", "hasNext", "done", "value"], "sources": ["../source/searchPhoneNumbersInText.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function searchPhoneNumbersInText() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, { ...options, v2: true }, metadata)\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (matcher.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: matcher.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;;;;;;;AAEe,SAASA,wBAAT,GAAoC;EAClD,0BAAoC,IAAAC,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,IAAMC,OAAO,GAAG,IAAIC,8BAAJ,CAAuBJ,IAAvB,kCAAkCC,OAAlC;IAA2CI,EAAE,EAAE;EAA/C,IAAuDH,QAAvD,CAAhB;EACA,2BACEI,MAAM,CAACC,QADT,cACqB;IACnB,OAAO;MACHC,IAAI,EAAE,gBAAM;QACX,IAAIL,OAAO,CAACM,OAAR,EAAJ,EAAuB;UACzB,OAAO;YACNC,IAAI,EAAE,KADA;YAENC,KAAK,EAAER,OAAO,CAACK,IAAR;UAFD,CAAP;QAIA;;QACD,OAAO;UACNE,IAAI,EAAE;QADA,CAAP;MAGG;IAXE,CAAP;EAaA,CAfF;AAiBA"}