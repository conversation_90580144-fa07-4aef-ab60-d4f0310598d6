"""
Payment Gateway Service
======================
خدمة بوابات الدفع المتقدمة
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
from decimal import Decimal
import json
import uuid
import hashlib
import hmac

import asyncpg
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class PaymentMethod(Enum):
    """طرق الدفع"""
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    BANK_TRANSFER = "bank_transfer"
    DIGITAL_WALLET = "digital_wallet"
    MOBILE_PAYMENT = "mobile_payment"
    CRYPTOCURRENCY = "cryptocurrency"


class PaymentProvider(Enum):
    """مقدمو خدمات الدفع"""
    STRIPE = "stripe"
    PAYPAL = "paypal"
    MADA = "mada"
    VISA = "visa"
    MASTERCARD = "mastercard"
    STCPAY = "stcpay"
    APPLEPAY = "applepay"
    GOOGLEPAY = "googlepay"
    SADAD = "sadad"
    TABBY = "tabby"


class PaymentStatus(Enum):
    """حالات الدفع"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    PARTIALLY_REFUNDED = "partially_refunded"


class TransactionType(Enum):
    """أنواع المعاملات"""
    PAYMENT = "payment"
    REFUND = "refund"
    TRANSFER = "transfer"
    WITHDRAWAL = "withdrawal"
    DEPOSIT = "deposit"
    FEE = "fee"
    COMMISSION = "commission"


@dataclass
class PaymentRequest:
    """طلب دفع"""
    amount: Decimal
    currency: str
    payment_method: PaymentMethod
    provider: PaymentProvider
    customer_id: str
    description: str
    metadata: Dict[str, Any] = None
    return_url: str = None
    webhook_url: str = None
    expires_at: datetime = None


@dataclass
class PaymentResponse:
    """استجابة الدفع"""
    payment_id: str
    status: PaymentStatus
    amount: Decimal
    currency: str
    provider_transaction_id: str = None
    provider_response: Dict[str, Any] = None
    payment_url: str = None
    error_message: str = None
    created_at: datetime = None
    updated_at: datetime = None


@dataclass
class RefundRequest:
    """طلب استرداد"""
    payment_id: str
    amount: Decimal = None  # None means full refund
    reason: str = None
    metadata: Dict[str, Any] = None


@dataclass
class RefundResponse:
    """استجابة الاسترداد"""
    refund_id: str
    payment_id: str
    status: PaymentStatus
    amount: Decimal
    currency: str
    provider_refund_id: str = None
    provider_response: Dict[str, Any] = None
    error_message: str = None
    created_at: datetime = None


class PaymentGatewayService:
    """خدمة بوابات الدفع المتقدمة"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Provider configurations
        self.provider_configs = {}
        self.provider_handlers = {}
        
        # Security settings
        self.webhook_secret = "your_webhook_secret_key"
        self.encryption_key = "your_encryption_key"
        
        # Rate limiting
        self.rate_limits = {
            PaymentProvider.STRIPE: {"requests_per_minute": 100},
            PaymentProvider.PAYPAL: {"requests_per_minute": 50},
            PaymentProvider.MADA: {"requests_per_minute": 200}
        }
        
        # Statistics
        self.payments_processed = 0
        self.payments_failed = 0
        self.total_volume = Decimal('0.00')
        
        # Initialize providers
        self._initialize_providers()
    
    def _initialize_providers(self):
        """تهيئة مقدمي الخدمة"""
        # Initialize Stripe
        self.provider_configs[PaymentProvider.STRIPE] = {
            "api_key": "sk_test_...",
            "publishable_key": "pk_test_...",
            "webhook_secret": "whsec_...",
            "supported_methods": [PaymentMethod.CREDIT_CARD, PaymentMethod.DEBIT_CARD],
            "supported_currencies": ["SAR", "USD", "EUR", "GBP"],
            "fees": {"percentage": 2.9, "fixed": Decimal('0.30')}
        }
        
        # Initialize PayPal
        self.provider_configs[PaymentProvider.PAYPAL] = {
            "client_id": "your_paypal_client_id",
            "client_secret": "your_paypal_client_secret",
            "webhook_id": "your_webhook_id",
            "supported_methods": [PaymentMethod.DIGITAL_WALLET],
            "supported_currencies": ["SAR", "USD", "EUR"],
            "fees": {"percentage": 3.4, "fixed": Decimal('0.35')}
        }
        
        # Initialize MADA (Saudi local payment)
        self.provider_configs[PaymentProvider.MADA] = {
            "merchant_id": "your_mada_merchant_id",
            "terminal_id": "your_terminal_id",
            "secret_key": "your_mada_secret",
            "supported_methods": [PaymentMethod.DEBIT_CARD],
            "supported_currencies": ["SAR"],
            "fees": {"percentage": 1.75, "fixed": Decimal('0.00')}
        }
        
        # Initialize STC Pay
        self.provider_configs[PaymentProvider.STCPAY] = {
            "merchant_id": "your_stcpay_merchant_id",
            "api_key": "your_stcpay_api_key",
            "supported_methods": [PaymentMethod.MOBILE_PAYMENT],
            "supported_currencies": ["SAR"],
            "fees": {"percentage": 2.0, "fixed": Decimal('0.00')}
        }
    
    async def process_payment(self, request: PaymentRequest) -> PaymentResponse:
        """معالجة دفعة"""
        try:
            logger.info(f"💳 Processing payment: {request.amount} {request.currency} via {request.provider.value}")
            
            # Validate request
            await self._validate_payment_request(request)
            
            # Generate payment ID
            payment_id = f"pay_{uuid.uuid4().hex[:12]}"
            
            # Get provider configuration
            provider_config = self.provider_configs.get(request.provider)
            if not provider_config:
                raise ValueError(f"Provider {request.provider.value} not configured")
            
            # Check if payment method is supported
            if request.payment_method not in provider_config["supported_methods"]:
                raise ValueError(f"Payment method {request.payment_method.value} not supported by {request.provider.value}")
            
            # Check if currency is supported
            if request.currency not in provider_config["supported_currencies"]:
                raise ValueError(f"Currency {request.currency} not supported by {request.provider.value}")
            
            # Calculate fees
            fees = await self._calculate_fees(request.amount, request.provider)
            
            # Store payment record
            await self._store_payment_record(payment_id, request, fees)
            
            # Process with specific provider
            provider_response = await self._process_with_provider(payment_id, request, provider_config)
            
            # Update payment status
            await self._update_payment_status(payment_id, provider_response)
            
            # Update statistics
            if provider_response.status == PaymentStatus.COMPLETED:
                self.payments_processed += 1
                self.total_volume += request.amount
            else:
                self.payments_failed += 1
            
            logger.info(f"✅ Payment processed: {payment_id} - Status: {provider_response.status.value}")
            return provider_response
            
        except Exception as e:
            logger.error(f"❌ Payment processing failed: {e}")
            self.payments_failed += 1
            
            return PaymentResponse(
                payment_id=payment_id if 'payment_id' in locals() else None,
                status=PaymentStatus.FAILED,
                amount=request.amount,
                currency=request.currency,
                error_message=str(e),
                created_at=datetime.now()
            )
    
    async def process_refund(self, request: RefundRequest) -> RefundResponse:
        """معالجة استرداد"""
        try:
            logger.info(f"💰 Processing refund for payment: {request.payment_id}")
            
            # Get original payment
            payment = await self._get_payment_record(request.payment_id)
            if not payment:
                raise ValueError(f"Payment {request.payment_id} not found")
            
            # Validate refund request
            await self._validate_refund_request(request, payment)
            
            # Generate refund ID
            refund_id = f"ref_{uuid.uuid4().hex[:12]}"
            
            # Determine refund amount
            refund_amount = request.amount or payment['amount']
            
            # Get provider configuration
            provider = PaymentProvider(payment['provider'])
            provider_config = self.provider_configs.get(provider)
            
            # Store refund record
            await self._store_refund_record(refund_id, request, payment, refund_amount)
            
            # Process refund with provider
            provider_response = await self._process_refund_with_provider(
                refund_id, request, payment, provider_config
            )
            
            # Update refund status
            await self._update_refund_status(refund_id, provider_response)
            
            logger.info(f"✅ Refund processed: {refund_id} - Status: {provider_response.status.value}")
            return provider_response
            
        except Exception as e:
            logger.error(f"❌ Refund processing failed: {e}")
            
            return RefundResponse(
                refund_id=refund_id if 'refund_id' in locals() else None,
                payment_id=request.payment_id,
                status=PaymentStatus.FAILED,
                amount=request.amount or Decimal('0.00'),
                currency=payment.get('currency', 'SAR') if 'payment' in locals() else 'SAR',
                error_message=str(e),
                created_at=datetime.now()
            )
    
    async def get_payment_status(self, payment_id: str) -> Optional[PaymentResponse]:
        """الحصول على حالة الدفع"""
        try:
            payment = await self._get_payment_record(payment_id)
            if not payment:
                return None
            
            return PaymentResponse(
                payment_id=payment['id'],
                status=PaymentStatus(payment['status']),
                amount=payment['amount'],
                currency=payment['currency'],
                provider_transaction_id=payment['provider_transaction_id'],
                provider_response=payment['provider_response'],
                created_at=payment['created_at'],
                updated_at=payment['updated_at']
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to get payment status: {e}")
            return None
    
    async def handle_webhook(self, provider: PaymentProvider, payload: Dict[str, Any], signature: str) -> bool:
        """معالجة webhook من مقدم الخدمة"""
        try:
            logger.info(f"🔔 Handling webhook from {provider.value}")
            
            # Verify webhook signature
            if not await self._verify_webhook_signature(provider, payload, signature):
                logger.warning(f"⚠️ Invalid webhook signature from {provider.value}")
                return False
            
            # Process webhook based on provider
            if provider == PaymentProvider.STRIPE:
                return await self._handle_stripe_webhook(payload)
            elif provider == PaymentProvider.PAYPAL:
                return await self._handle_paypal_webhook(payload)
            elif provider == PaymentProvider.MADA:
                return await self._handle_mada_webhook(payload)
            else:
                logger.warning(f"⚠️ Unsupported webhook provider: {provider.value}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Webhook handling failed: {e}")
            return False
    
    async def get_supported_methods(self, provider: PaymentProvider) -> List[PaymentMethod]:
        """الحصول على طرق الدفع المدعومة"""
        config = self.provider_configs.get(provider)
        return config.get("supported_methods", []) if config else []
    
    async def get_supported_currencies(self, provider: PaymentProvider) -> List[str]:
        """الحصول على العملات المدعومة"""
        config = self.provider_configs.get(provider)
        return config.get("supported_currencies", []) if config else []
    
    async def calculate_fees(self, amount: Decimal, provider: PaymentProvider) -> Dict[str, Decimal]:
        """حساب الرسوم"""
        return await self._calculate_fees(amount, provider)
    
    async def get_payment_statistics(
        self,
        start_date: datetime = None,
        end_date: datetime = None,
        provider: PaymentProvider = None
    ) -> Dict[str, Any]:
        """الحصول على إحصائيات الدفع"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = []
                params = []
                param_count = 0
                
                if start_date:
                    param_count += 1
                    where_conditions.append(f"created_at >= ${param_count}")
                    params.append(start_date)
                
                if end_date:
                    param_count += 1
                    where_conditions.append(f"created_at <= ${param_count}")
                    params.append(end_date)
                
                if provider:
                    param_count += 1
                    where_conditions.append(f"provider = ${param_count}")
                    params.append(provider.value)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Overall statistics
                stats_query = f"""
                    SELECT 
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_payments,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_volume,
                        SUM(CASE WHEN status = 'completed' THEN fees_amount ELSE 0 END) as total_fees,
                        AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END) as avg_payment_amount
                    FROM payments 
                    WHERE {where_clause}
                """
                
                stats = await conn.fetchrow(stats_query, *params)
                
                # Provider breakdown
                provider_query = f"""
                    SELECT 
                        provider,
                        COUNT(*) as count,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as volume
                    FROM payments 
                    WHERE {where_clause}
                    GROUP BY provider
                    ORDER BY volume DESC
                """
                
                provider_stats = await conn.fetch(provider_query, *params)
                
                # Payment method breakdown
                method_query = f"""
                    SELECT 
                        payment_method,
                        COUNT(*) as count,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful,
                        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as volume
                    FROM payments 
                    WHERE {where_clause}
                    GROUP BY payment_method
                    ORDER BY volume DESC
                """
                
                method_stats = await conn.fetch(method_query, *params)
                
                # Calculate success rate
                total_payments = stats['total_payments']
                success_rate = (stats['successful_payments'] / max(total_payments, 1)) * 100
                
                return {
                    "overview": {
                        "total_payments": total_payments,
                        "successful_payments": stats['successful_payments'],
                        "failed_payments": stats['failed_payments'],
                        "success_rate": round(success_rate, 2),
                        "total_volume": float(stats['total_volume'] or 0),
                        "total_fees": float(stats['total_fees'] or 0),
                        "avg_payment_amount": float(stats['avg_payment_amount'] or 0)
                    },
                    "by_provider": [
                        {
                            "provider": row['provider'],
                            "total_payments": row['count'],
                            "successful_payments": row['successful'],
                            "success_rate": round((row['successful'] / max(row['count'], 1)) * 100, 2),
                            "total_volume": float(row['volume'] or 0)
                        }
                        for row in provider_stats
                    ],
                    "by_method": [
                        {
                            "method": row['payment_method'],
                            "total_payments": row['count'],
                            "successful_payments": row['successful'],
                            "success_rate": round((row['successful'] / max(row['count'], 1)) * 100, 2),
                            "total_volume": float(row['volume'] or 0)
                        }
                        for row in method_stats
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get payment statistics: {e}")
            return {}
    
    # Helper methods
    async def _validate_payment_request(self, request: PaymentRequest):
        """التحقق من صحة طلب الدفع"""
        if request.amount <= 0:
            raise ValueError("Payment amount must be positive")
        
        if not request.currency:
            raise ValueError("Currency is required")
        
        if not request.customer_id:
            raise ValueError("Customer ID is required")
        
        # Check minimum amount limits
        min_amounts = {
            "SAR": Decimal('1.00'),
            "USD": Decimal('0.50'),
            "EUR": Decimal('0.50')
        }
        
        min_amount = min_amounts.get(request.currency, Decimal('1.00'))
        if request.amount < min_amount:
            raise ValueError(f"Minimum payment amount is {min_amount} {request.currency}")
    
    async def _validate_refund_request(self, request: RefundRequest, payment: Dict[str, Any]):
        """التحقق من صحة طلب الاسترداد"""
        if payment['status'] != PaymentStatus.COMPLETED.value:
            raise ValueError("Can only refund completed payments")
        
        if request.amount and request.amount <= 0:
            raise ValueError("Refund amount must be positive")
        
        if request.amount and request.amount > payment['amount']:
            raise ValueError("Refund amount cannot exceed payment amount")
        
        # Check if payment is within refund window (e.g., 90 days)
        payment_date = payment['created_at']
        refund_deadline = payment_date + timedelta(days=90)
        
        if datetime.now() > refund_deadline:
            raise ValueError("Payment is outside the refund window")
    
    async def _calculate_fees(self, amount: Decimal, provider: PaymentProvider) -> Dict[str, Decimal]:
        """حساب رسوم الدفع"""
        config = self.provider_configs.get(provider)
        if not config or 'fees' not in config:
            return {"percentage_fee": Decimal('0.00'), "fixed_fee": Decimal('0.00'), "total_fee": Decimal('0.00')}
        
        fees_config = config['fees']
        percentage_fee = amount * (Decimal(str(fees_config['percentage'])) / 100)
        fixed_fee = fees_config['fixed']
        total_fee = percentage_fee + fixed_fee
        
        return {
            "percentage_fee": percentage_fee,
            "fixed_fee": fixed_fee,
            "total_fee": total_fee
        }
    
    async def _store_payment_record(self, payment_id: str, request: PaymentRequest, fees: Dict[str, Decimal]):
        """حفظ سجل الدفع"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO payments (
                        id, customer_id, amount, currency, payment_method, provider,
                        description, status, fees_amount, metadata, return_url, webhook_url, expires_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                """
                
                await conn.execute(
                    query,
                    payment_id,
                    request.customer_id,
                    request.amount,
                    request.currency,
                    request.payment_method.value,
                    request.provider.value,
                    request.description,
                    PaymentStatus.PENDING.value,
                    fees['total_fee'],
                    json.dumps(request.metadata or {}),
                    request.return_url,
                    request.webhook_url,
                    request.expires_at
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store payment record: {e}")
            raise
    
    async def _process_with_provider(
        self, 
        payment_id: str, 
        request: PaymentRequest, 
        provider_config: Dict[str, Any]
    ) -> PaymentResponse:
        """معالجة الدفع مع مقدم الخدمة"""
        # This is a simplified implementation
        # In production, you would integrate with actual payment providers
        
        if request.provider == PaymentProvider.STRIPE:
            return await self._process_stripe_payment(payment_id, request, provider_config)
        elif request.provider == PaymentProvider.PAYPAL:
            return await self._process_paypal_payment(payment_id, request, provider_config)
        elif request.provider == PaymentProvider.MADA:
            return await self._process_mada_payment(payment_id, request, provider_config)
        else:
            # Simulate processing
            await asyncio.sleep(0.1)  # Simulate API call delay
            
            return PaymentResponse(
                payment_id=payment_id,
                status=PaymentStatus.COMPLETED,
                amount=request.amount,
                currency=request.currency,
                provider_transaction_id=f"{request.provider.value}_{uuid.uuid4().hex[:8]}",
                provider_response={"status": "success", "message": "Payment processed successfully"},
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
    
    async def _process_stripe_payment(
        self, 
        payment_id: str, 
        request: PaymentRequest, 
        config: Dict[str, Any]
    ) -> PaymentResponse:
        """معالجة دفع Stripe"""
        # Simulate Stripe API integration
        logger.info(f"🔵 Processing Stripe payment: {payment_id}")
        
        # In production, you would use the Stripe SDK
        # stripe.api_key = config['api_key']
        # intent = stripe.PaymentIntent.create(...)
        
        await asyncio.sleep(0.2)  # Simulate API call
        
        return PaymentResponse(
            payment_id=payment_id,
            status=PaymentStatus.PROCESSING,
            amount=request.amount,
            currency=request.currency,
            provider_transaction_id=f"pi_{uuid.uuid4().hex[:24]}",
            payment_url=f"https://checkout.stripe.com/pay/{payment_id}",
            provider_response={"client_secret": f"pi_{payment_id}_secret"},
            created_at=datetime.now()
        )
    
    async def _process_paypal_payment(
        self, 
        payment_id: str, 
        request: PaymentRequest, 
        config: Dict[str, Any]
    ) -> PaymentResponse:
        """معالجة دفع PayPal"""
        logger.info(f"🟡 Processing PayPal payment: {payment_id}")
        
        await asyncio.sleep(0.3)  # Simulate API call
        
        return PaymentResponse(
            payment_id=payment_id,
            status=PaymentStatus.PROCESSING,
            amount=request.amount,
            currency=request.currency,
            provider_transaction_id=f"PAYID-{uuid.uuid4().hex[:16].upper()}",
            payment_url=f"https://www.paypal.com/checkoutnow?token={payment_id}",
            provider_response={"approval_url": f"https://www.paypal.com/checkoutnow?token={payment_id}"},
            created_at=datetime.now()
        )
    
    async def _process_mada_payment(
        self, 
        payment_id: str, 
        request: PaymentRequest, 
        config: Dict[str, Any]
    ) -> PaymentResponse:
        """معالجة دفع مدى"""
        logger.info(f"🟢 Processing MADA payment: {payment_id}")
        
        await asyncio.sleep(0.1)  # Simulate API call
        
        return PaymentResponse(
            payment_id=payment_id,
            status=PaymentStatus.COMPLETED,
            amount=request.amount,
            currency=request.currency,
            provider_transaction_id=f"MADA{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:8].upper()}",
            provider_response={"reference_number": f"REF{uuid.uuid4().hex[:12].upper()}"},
            created_at=datetime.now()
        )
    
    async def _get_payment_record(self, payment_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على سجل الدفع"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM payments WHERE id = $1
                """
                
                row = await conn.fetchrow(query, payment_id)
                return dict(row) if row else None
                
        except Exception as e:
            logger.error(f"❌ Failed to get payment record: {e}")
            return None
    
    async def _update_payment_status(self, payment_id: str, response: PaymentResponse):
        """تحديث حالة الدفع"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE payments 
                    SET status = $1, 
                        provider_transaction_id = $2,
                        provider_response = $3,
                        payment_url = $4,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = $5
                """
                
                await conn.execute(
                    query,
                    response.status.value,
                    response.provider_transaction_id,
                    json.dumps(response.provider_response or {}),
                    response.payment_url,
                    payment_id
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to update payment status: {e}")
    
    async def _verify_webhook_signature(
        self, 
        provider: PaymentProvider, 
        payload: Dict[str, Any], 
        signature: str
    ) -> bool:
        """التحقق من توقيع webhook"""
        try:
            config = self.provider_configs.get(provider)
            if not config:
                return False
            
            if provider == PaymentProvider.STRIPE:
                # Verify Stripe webhook signature
                webhook_secret = config.get('webhook_secret')
                if not webhook_secret:
                    return False
                
                # In production, use stripe.Webhook.construct_event()
                return True  # Simplified for demo
            
            elif provider == PaymentProvider.PAYPAL:
                # Verify PayPal webhook signature
                return True  # Simplified for demo
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to verify webhook signature: {e}")
            return False
    
    async def _handle_stripe_webhook(self, payload: Dict[str, Any]) -> bool:
        """معالجة webhook من Stripe"""
        try:
            event_type = payload.get('type')
            
            if event_type == 'payment_intent.succeeded':
                payment_intent = payload['data']['object']
                payment_id = payment_intent.get('metadata', {}).get('payment_id')
                
                if payment_id:
                    await self._update_payment_status(
                        payment_id,
                        PaymentResponse(
                            payment_id=payment_id,
                            status=PaymentStatus.COMPLETED,
                            amount=Decimal(str(payment_intent['amount'] / 100)),
                            currency=payment_intent['currency'].upper(),
                            provider_transaction_id=payment_intent['id']
                        )
                    )
            
            elif event_type == 'payment_intent.payment_failed':
                payment_intent = payload['data']['object']
                payment_id = payment_intent.get('metadata', {}).get('payment_id')
                
                if payment_id:
                    await self._update_payment_status(
                        payment_id,
                        PaymentResponse(
                            payment_id=payment_id,
                            status=PaymentStatus.FAILED,
                            amount=Decimal(str(payment_intent['amount'] / 100)),
                            currency=payment_intent['currency'].upper(),
                            provider_transaction_id=payment_intent['id'],
                            error_message=payment_intent.get('last_payment_error', {}).get('message')
                        )
                    )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to handle Stripe webhook: {e}")
            return False
    
    async def _handle_paypal_webhook(self, payload: Dict[str, Any]) -> bool:
        """معالجة webhook من PayPal"""
        # Implementation for PayPal webhooks
        return True
    
    async def _handle_mada_webhook(self, payload: Dict[str, Any]) -> bool:
        """معالجة webhook من مدى"""
        # Implementation for MADA webhooks
        return True
    
    async def _store_refund_record(
        self, 
        refund_id: str, 
        request: RefundRequest, 
        payment: Dict[str, Any], 
        amount: Decimal
    ):
        """حفظ سجل الاسترداد"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO refunds (
                        id, payment_id, amount, currency, reason, status, metadata
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """
                
                await conn.execute(
                    query,
                    refund_id,
                    request.payment_id,
                    amount,
                    payment['currency'],
                    request.reason,
                    PaymentStatus.PENDING.value,
                    json.dumps(request.metadata or {})
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store refund record: {e}")
            raise
    
    async def _process_refund_with_provider(
        self, 
        refund_id: str, 
        request: RefundRequest, 
        payment: Dict[str, Any], 
        provider_config: Dict[str, Any]
    ) -> RefundResponse:
        """معالجة الاسترداد مع مقدم الخدمة"""
        # Simulate refund processing
        await asyncio.sleep(0.1)
        
        return RefundResponse(
            refund_id=refund_id,
            payment_id=request.payment_id,
            status=PaymentStatus.COMPLETED,
            amount=request.amount or payment['amount'],
            currency=payment['currency'],
            provider_refund_id=f"ref_{uuid.uuid4().hex[:12]}",
            provider_response={"status": "success"},
            created_at=datetime.now()
        )
    
    async def _update_refund_status(self, refund_id: str, response: RefundResponse):
        """تحديث حالة الاسترداد"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE refunds 
                    SET status = $1, 
                        provider_refund_id = $2,
                        provider_response = $3,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = $4
                """
                
                await conn.execute(
                    query,
                    response.status.value,
                    response.provider_refund_id,
                    json.dumps(response.provider_response or {}),
                    refund_id
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to update refund status: {e}")
    
    async def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمة"""
        return {
            "payments_processed": self.payments_processed,
            "payments_failed": self.payments_failed,
            "total_volume": float(self.total_volume),
            "success_rate": (self.payments_processed / max(self.payments_processed + self.payments_failed, 1)) * 100,
            "supported_providers": len(self.provider_configs),
            "active_providers": list(self.provider_configs.keys())
        }
