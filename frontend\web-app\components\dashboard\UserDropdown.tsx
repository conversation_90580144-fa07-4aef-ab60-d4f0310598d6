'use client';

import { motion } from 'framer-motion';
import { 
  UserIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  QuestionMarkCircleIcon,
  ArrowRightOnRectangleIcon,
  SunIcon,
  MoonIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from 'next-themes';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { clsx } from 'clsx';

interface UserDropdownProps {
  onClose: () => void;
  onLogout: () => void;
}

// عناصر القائمة
const menuItems = [
  {
    icon: UserIcon,
    label: 'الملف الشخصي',
    href: '/dashboard/profile/personal',
    description: 'إدارة معلوماتك الشخصية',
  },
  {
    icon: ShieldCheckIcon,
    label: 'الأمان',
    href: '/dashboard/profile/security',
    description: 'إعدادات الأمان والخصوصية',
  },
  {
    icon: Cog6ToothIcon,
    label: 'الإعدادات',
    href: '/dashboard/settings',
    description: 'تخصيص تجربة الاستخدام',
  },
  {
    icon: DocumentTextIcon,
    label: 'الوثائق',
    href: '/dashboard/documents',
    description: 'عرض وإدارة الوثائق',
  },
  {
    icon: QuestionMarkCircleIcon,
    label: 'المساعدة',
    href: '/dashboard/help',
    description: 'الحصول على المساعدة والدعم',
  },
];

export default function UserDropdown({ onClose, onLogout }: UserDropdownProps) {
  const { theme, setTheme } = useTheme();
  const { state: authState } = useAuth();
  const { isRTL } = useLanguage();

  // معالجة النقر على عنصر القائمة
  const handleMenuClick = (href: string) => {
    window.location.href = href;
    onClose();
  };

  // تبديل السمة
  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: -10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95, y: -10 }}
      className={clsx(
        'absolute top-full mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50',
        isRTL ? 'left-0' : 'right-0'
      )}
      onClick={(e) => e.stopPropagation()}
    >
      {/* معلومات المستخدم */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {authState.user?.avatar ? (
            <img
              className="h-12 w-12 rounded-full"
              src={authState.user.avatar}
              alt={`${authState.user.firstName} ${authState.user.lastName}`}
            />
          ) : (
            <div className="h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900/20 flex items-center justify-center">
              <UserIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
          )}
          
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {authState.user?.firstName} {authState.user?.lastName}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {authState.user?.email}
            </p>
            
            {/* حالة التحقق */}
            <div className="flex items-center space-x-2 rtl:space-x-reverse mt-1">
              {authState.user?.isVerified ? (
                <>
                  <ShieldCheckIcon className="h-4 w-4 text-success-500" />
                  <span className="text-xs text-success-600 dark:text-success-400">
                    محقق
                  </span>
                </>
              ) : (
                <>
                  <ShieldCheckIcon className="h-4 w-4 text-warning-500" />
                  <span className="text-xs text-warning-600 dark:text-warning-400">
                    غير محقق
                  </span>
                </>
              )}
              
              {authState.user?.kycStatus === 'verified' && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-success-100 dark:bg-success-900/20 text-success-800 dark:text-success-200">
                  KYC مكتمل
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* عناصر القائمة */}
      <div className="py-2">
        {menuItems.map((item, index) => (
          <motion.button
            key={item.href}
            initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
            onClick={() => handleMenuClick(item.href)}
            className="w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-left rtl:text-right hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200"
          >
            <item.icon className="h-5 w-5 text-gray-400 dark:text-gray-500 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {item.label}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {item.description}
              </p>
            </div>
          </motion.button>
        ))}

        {/* فاصل */}
        <div className="border-t border-gray-200 dark:border-gray-700 my-2"></div>

        {/* تبديل السمة */}
        <motion.button
          initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: menuItems.length * 0.05 }}
          onClick={toggleTheme}
          className="w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-left rtl:text-right hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200"
        >
          {theme === 'dark' ? (
            <SunIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 flex-shrink-0" />
          ) : (
            <MoonIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 flex-shrink-0" />
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {theme === 'dark' ? 'الوضع النهاري' : 'الوضع الليلي'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              تغيير مظهر التطبيق
            </p>
          </div>
        </motion.button>

        {/* فاصل */}
        <div className="border-t border-gray-200 dark:border-gray-700 my-2"></div>

        {/* تسجيل الخروج */}
        <motion.button
          initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: (menuItems.length + 1) * 0.05 }}
          onClick={onLogout}
          className="w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-left rtl:text-right hover:bg-error-50 dark:hover:bg-error-900/20 transition-colors duration-200 text-error-600 dark:text-error-400"
        >
          <ArrowRightOnRectangleIcon className="h-5 w-5 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium">
              تسجيل الخروج
            </p>
            <p className="text-xs opacity-75">
              إنهاء الجلسة الحالية
            </p>
          </div>
        </motion.button>
      </div>
    </motion.div>
  );
}
