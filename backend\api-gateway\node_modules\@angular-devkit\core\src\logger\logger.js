"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const rxjs_1 = require("rxjs");
class Logger extends rxjs_1.Observable {
    name;
    parent;
    _subject = new rxjs_1.Subject();
    _metadata;
    _obs = rxjs_1.EMPTY;
    _subscription = null;
    get _observable() {
        return this._obs;
    }
    set _observable(v) {
        if (this._subscription) {
            this._subscription.unsubscribe();
        }
        this._obs = v;
        if (this.parent) {
            this._subscription = this.subscribe((value) => {
                if (this.parent) {
                    this.parent._subject.next(value);
                }
            }, (error) => {
                if (this.parent) {
                    this.parent._subject.error(error);
                }
            }, () => {
                if (this._subscription) {
                    this._subscription.unsubscribe();
                }
                this._subscription = null;
            });
        }
    }
    constructor(name, parent = null) {
        super();
        this.name = name;
        this.parent = parent;
        const path = [];
        let p = parent;
        while (p) {
            path.push(p.name);
            p = p.parent;
        }
        this._metadata = { name, path };
        this._observable = this._subject.asObservable();
        if (this.parent && this.parent._subject) {
            // When the parent completes, complete us as well.
            this.parent._subject.subscribe(undefined, undefined, () => this.complete());
        }
    }
    asApi() {
        return {
            createChild: (name) => this.createChild(name),
            log: (level, message, metadata) => {
                return this.log(level, message, metadata);
            },
            debug: (message, metadata) => this.debug(message, metadata),
            info: (message, metadata) => this.info(message, metadata),
            warn: (message, metadata) => this.warn(message, metadata),
            error: (message, metadata) => this.error(message, metadata),
            fatal: (message, metadata) => this.fatal(message, metadata),
        };
    }
    createChild(name) {
        return new this.constructor(name, this);
    }
    complete() {
        this._subject.complete();
    }
    log(level, message, metadata = {}) {
        const entry = Object.assign({}, metadata, this._metadata, {
            level,
            message,
            timestamp: +Date.now(),
        });
        this._subject.next(entry);
    }
    next(entry) {
        this._subject.next(entry);
    }
    debug(message, metadata = {}) {
        return this.log('debug', message, metadata);
    }
    info(message, metadata = {}) {
        return this.log('info', message, metadata);
    }
    warn(message, metadata = {}) {
        return this.log('warn', message, metadata);
    }
    error(message, metadata = {}) {
        return this.log('error', message, metadata);
    }
    fatal(message, metadata = {}) {
        return this.log('fatal', message, metadata);
    }
    toString() {
        return `<Logger(${this.name})>`;
    }
    lift(operator) {
        return this._observable.lift(operator);
    }
    subscribe(_observerOrNext, _error, _complete) {
        // eslint-disable-next-line prefer-spread
        return this._observable.subscribe.apply(this._observable, 
        // eslint-disable-next-line prefer-rest-params
        arguments);
    }
    forEach(next, promiseCtor = Promise) {
        return this._observable.forEach(next, promiseCtor);
    }
}
exports.Logger = Logger;
