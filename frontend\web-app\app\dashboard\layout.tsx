'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Sidebar from '@/components/dashboard/Sidebar';
import NotificationDropdown from '@/components/dashboard/NotificationDropdown';
import UserDropdown from '@/components/dashboard/UserDropdown';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  
  const router = useRouter();
  const { state: authState, logout } = useAuth();
  const { state: notificationState } = useNotifications();
  const { t, isRTL } = useLanguage();

  // التحقق من المصادقة
  useEffect(() => {
    if (!authState.isLoading && !authState.isAuthenticated) {
      router.push('/auth/login');
    }
  }, [authState.isAuthenticated, authState.isLoading, router]);

  // إغلاق القوائم عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = () => {
      setNotificationsOpen(false);
      setUserMenuOpen(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // معالجة تسجيل الخروج
  const handleLogout = async () => {
    await logout();
  };

  // إظهار شاشة التحميل أثناء التحقق من المصادقة
  if (authState.isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // إعادة توجيه إذا لم يكن مصادق عليه
  if (!authState.isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* الشريط الجانبي للشاشات الكبيرة */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <Sidebar />
      </div>

      {/* الشريط الجانبي المحمول */}
      <AnimatePresence>
        {sidebarOpen && (
          <>
            {/* الخلفية المظلمة */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            />

            {/* الشريط الجانبي */}
            <motion.div
              initial={{ x: isRTL ? 256 : -256 }}
              animate={{ x: 0 }}
              exit={{ x: isRTL ? 256 : -256 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              className="fixed inset-y-0 left-0 z-50 w-64 lg:hidden"
            >
              <Sidebar onClose={() => setSidebarOpen(false)} />
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* المحتوى الرئيسي */}
      <div className="lg:pl-64">
        {/* شريط التنقل العلوي */}
        <div className="sticky top-0 z-30 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
            {/* الجانب الأيسر */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              {/* زر القائمة المحمولة */}
              <button
                type="button"
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
                onClick={() => setSidebarOpen(true)}
              >
                <Bars3Icon className="h-6 w-6" />
              </button>

              {/* العنوان */}
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                لوحة التحكم
              </h1>
            </div>

            {/* الجانب الأيمن */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              {/* الإشعارات */}
              <div className="relative">
                <button
                  type="button"
                  className="p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  onClick={(e) => {
                    e.stopPropagation();
                    setNotificationsOpen(!notificationsOpen);
                    setUserMenuOpen(false);
                  }}
                >
                  <BellIcon className="h-6 w-6" />
                  {notificationState.unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-error-500 flex items-center justify-center">
                      <span className="text-xs font-medium text-white">
                        {notificationState.unreadCount > 9 ? '9+' : notificationState.unreadCount}
                      </span>
                    </span>
                  )}
                </button>

                {/* قائمة الإشعارات */}
                <AnimatePresence>
                  {notificationsOpen && (
                    <NotificationDropdown
                      onClose={() => setNotificationsOpen(false)}
                    />
                  )}
                </AnimatePresence>
              </div>

              {/* قائمة المستخدم */}
              <div className="relative">
                <button
                  type="button"
                  className="flex items-center space-x-3 rtl:space-x-reverse p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  onClick={(e) => {
                    e.stopPropagation();
                    setUserMenuOpen(!userMenuOpen);
                    setNotificationsOpen(false);
                  }}
                >
                  {authState.user?.avatar ? (
                    <img
                      className="h-8 w-8 rounded-full"
                      src={authState.user.avatar}
                      alt={`${authState.user.firstName} ${authState.user.lastName}`}
                    />
                  ) : (
                    <UserCircleIcon className="h-8 w-8" />
                  )}
                  <div className="hidden sm:block text-left rtl:text-right">
                    <p className="text-sm font-medium">
                      {authState.user?.firstName} {authState.user?.lastName}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {authState.user?.email}
                    </p>
                  </div>
                </button>

                {/* قائمة المستخدم المنسدلة */}
                <AnimatePresence>
                  {userMenuOpen && (
                    <UserDropdown
                      onClose={() => setUserMenuOpen(false)}
                      onLogout={handleLogout}
                    />
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>

        {/* محتوى الصفحة */}
        <main className="flex-1">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="py-6"
          >
            {children}
          </motion.div>
        </main>
      </div>
    </div>
  );
}
