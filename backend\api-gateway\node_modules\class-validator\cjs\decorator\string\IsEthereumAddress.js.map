{"version": 3, "file": "IsEthereumAddress.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsEthereumAddress.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,wFAAyE;AAE5D,QAAA,mBAAmB,GAAG,mBAAmB,CAAC;AAEvD;;;GAGG;AACH,SAAgB,iBAAiB,CAAC,KAAc;IAC9C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,2BAA0B,EAAC,KAAK,CAAC,CAAC;AACxE,CAAC;AAFD,8CAEC;AAED;;;GAGG;AACH,SAAgB,iBAAiB,CAAC,iBAAqC;IACrE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,2BAAmB;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC;YAC5D,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,uCAAuC,EAClE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,8CAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isEthereumAddressValidator from 'validator/lib/isEthereumAddress';\n\nexport const IS_ETHEREUM_ADDRESS = 'isEthereumAddress';\n\n/**\n * Check if the string is an Ethereum address using basic regex. Does not validate address checksums.\n * If given value is not a string, then it returns false.\n */\nexport function isEthereumAddress(value: unknown): boolean {\n  return typeof value === 'string' && isEthereumAddressValidator(value);\n}\n\n/**\n * Check if the string is an Ethereum address using basic regex. Does not validate address checksums.\n * If given value is not a string, then it returns false.\n */\nexport function IsEthereumAddress(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ETHEREUM_ADDRESS,\n      validator: {\n        validate: (value, args): boolean => isEthereumAddress(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be an Ethereum address',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}