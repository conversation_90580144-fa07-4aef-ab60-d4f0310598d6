{"version": 3, "file": "NodeParserLinkTag3.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserLinkTag3.test.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,6CAA4C;AAE5C,IAAI,CAAC,yCAAyC,EAAE;IAC9C,yBAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,wDAAwD;QACxD,sFAAsF;QACtF,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yCAAyC,EAAE;IAC9C,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,uDAAuD,EAAE,sBAAsB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3G,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yBAAyB,EAAE;IAC9B,yBAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,qGAAqG;QACrG,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See L<PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('00 Symbol references: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link Class1.[WellknownSymbols.toStringPrimitive]}',\r\n      ' * {@link Class1 . ( [ WellknownSymbols . toStringPrimitive ] : static) | link text}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('01 Symbol references: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@link Class1.[WellknownSymbols.toStringPrimitive}', ' * {@link Class1.[]}', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('02 Complicated examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link ./lib/controls/Button#Button.([(WellknownSymbols:namespace).toStringPrimitive]:instance)}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n"]}