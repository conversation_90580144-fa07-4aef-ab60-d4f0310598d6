import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan, LessThan } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AnalyticsEvent } from '../entities/analytics-event.entity';
import { Report } from '../entities/report.entity';
import { CreateAnalyticsEventDto } from '../dto/create-analytics-event.dto';

export interface AnalyticsQuery {
  startDate?: Date;
  endDate?: Date;
  eventType?: string;
  userId?: string;
  groupBy?: 'day' | 'week' | 'month';
  limit?: number;
}

export interface AnalyticsResult {
  totalEvents: number;
  uniqueUsers: number;
  data: any[];
  summary: any;
}

export interface DashboardMetrics {
  totalUsers: number;
  activeUsers: number;
  totalTransfers: number;
  totalVolume: string;
  successRate: number;
  averageTransferAmount: string;
  topCountries: any[];
  recentActivity: any[];
}

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    @InjectRepository(AnalyticsEvent)
    private readonly analyticsEventRepository: Repository<AnalyticsEvent>,
    @InjectRepository(Report)
    private readonly reportRepository: Repository<Report>,
    private readonly configService: ConfigService,
  ) {}

  async trackEvent(eventData: CreateAnalyticsEventDto): Promise<AnalyticsEvent> {
    const event = this.analyticsEventRepository.create({
      ...eventData,
      timestamp: new Date(),
      sessionId: eventData.sessionId || this.generateSessionId(),
    });

    const savedEvent = await this.analyticsEventRepository.save(event);
    this.logger.debug(`Analytics event tracked: ${savedEvent.eventType}`);

    return savedEvent;
  }

  async getAnalytics(query: AnalyticsQuery): Promise<AnalyticsResult> {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate = new Date(),
      eventType,
      userId,
      groupBy = 'day',
      limit = 100,
    } = query;

    const queryBuilder = this.analyticsEventRepository.createQueryBuilder('event');

    queryBuilder.where('event.timestamp BETWEEN :startDate AND :endDate', {
      startDate,
      endDate,
    });

    if (eventType) {
      queryBuilder.andWhere('event.eventType = :eventType', { eventType });
    }

    if (userId) {
      queryBuilder.andWhere('event.userId = :userId', { userId });
    }

    // Group by time period
    let dateFormat: string;
    switch (groupBy) {
      case 'day':
        dateFormat = 'YYYY-MM-DD';
        break;
      case 'week':
        dateFormat = 'YYYY-"W"WW';
        break;
      case 'month':
        dateFormat = 'YYYY-MM';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
    }

    queryBuilder
      .select([
        `TO_CHAR(event.timestamp, '${dateFormat}') as period`,
        'COUNT(*) as count',
        'COUNT(DISTINCT event.userId) as uniqueUsers',
      ])
      .groupBy('period')
      .orderBy('period', 'ASC')
      .limit(limit);

    const data = await queryBuilder.getRawMany();

    // Get total metrics
    const totalEvents = await this.analyticsEventRepository.count({
      where: {
        timestamp: Between(startDate, endDate),
        ...(eventType && { eventType }),
        ...(userId && { userId }),
      },
    });

    const uniqueUsersResult = await this.analyticsEventRepository
      .createQueryBuilder('event')
      .select('COUNT(DISTINCT event.userId)', 'count')
      .where('event.timestamp BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .getRawOne();

    const uniqueUsers = parseInt(uniqueUsersResult?.count || '0');

    const summary = {
      totalEvents,
      uniqueUsers,
      averageEventsPerUser: uniqueUsers > 0 ? totalEvents / uniqueUsers : 0,
      dateRange: { startDate, endDate },
      groupBy,
    };

    return {
      totalEvents,
      uniqueUsers,
      data,
      summary,
    };
  }

  async getDashboardMetrics(): Promise<DashboardMetrics> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // Get user metrics
    const [totalUsersResult, activeUsersResult] = await Promise.all([
      this.analyticsEventRepository
        .createQueryBuilder('event')
        .select('COUNT(DISTINCT event.userId)', 'count')
        .where('event.eventType = :eventType', { eventType: 'user_registered' })
        .getRawOne(),
      this.analyticsEventRepository
        .createQueryBuilder('event')
        .select('COUNT(DISTINCT event.userId)', 'count')
        .where('event.timestamp > :date', { date: oneDayAgo })
        .getRawOne(),
    ]);

    const totalUsers = parseInt(totalUsersResult?.count || '0');
    const activeUsers = parseInt(activeUsersResult?.count || '0');

    // Get transfer metrics
    const [transfersResult, volumeResult, successRateResult] = await Promise.all([
      this.analyticsEventRepository.count({
        where: {
          eventType: 'transfer_completed',
          timestamp: MoreThan(thirtyDaysAgo),
        },
      }),
      this.analyticsEventRepository
        .createQueryBuilder('event')
        .select('SUM(CAST(event.properties->>\'amount\' AS DECIMAL))', 'total')
        .where('event.eventType = :eventType', { eventType: 'transfer_completed' })
        .andWhere('event.timestamp > :date', { date: thirtyDaysAgo })
        .getRawOne(),
      this.getTransferSuccessRate(thirtyDaysAgo),
    ]);

    const totalTransfers = transfersResult;
    const totalVolume = volumeResult?.total || '0';
    const averageTransferAmount = totalTransfers > 0 
      ? (parseFloat(totalVolume) / totalTransfers).toFixed(2)
      : '0';

    // Get top countries
    const topCountries = await this.getTopCountries(thirtyDaysAgo);

    // Get recent activity
    const recentActivity = await this.getRecentActivity(10);

    return {
      totalUsers,
      activeUsers,
      totalTransfers,
      totalVolume,
      successRate: successRateResult,
      averageTransferAmount,
      topCountries,
      recentActivity,
    };
  }

  async getUserAnalytics(userId: string, days: number = 30): Promise<any> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    const [
      totalEvents,
      eventsByType,
      sessionCount,
      lastActivity,
    ] = await Promise.all([
      this.analyticsEventRepository.count({
        where: {
          userId,
          timestamp: MoreThan(startDate),
        },
      }),
      this.analyticsEventRepository
        .createQueryBuilder('event')
        .select(['event.eventType', 'COUNT(*) as count'])
        .where('event.userId = :userId', { userId })
        .andWhere('event.timestamp > :startDate', { startDate })
        .groupBy('event.eventType')
        .getRawMany(),
      this.analyticsEventRepository
        .createQueryBuilder('event')
        .select('COUNT(DISTINCT event.sessionId)', 'count')
        .where('event.userId = :userId', { userId })
        .andWhere('event.timestamp > :startDate', { startDate })
        .getRawOne(),
      this.analyticsEventRepository.findOne({
        where: { userId },
        order: { timestamp: 'DESC' },
      }),
    ]);

    const sessions = parseInt(sessionCount?.count || '0');
    const averageEventsPerSession = sessions > 0 ? totalEvents / sessions : 0;

    return {
      userId,
      totalEvents,
      sessions,
      averageEventsPerSession,
      eventsByType: eventsByType.reduce((acc, item) => {
        acc[item.eventType] = parseInt(item.count);
        return acc;
      }, {}),
      lastActivity: lastActivity?.timestamp,
      analysisDate: new Date(),
    };
  }

  async generateReport(
    reportType: string,
    startDate: Date,
    endDate: Date,
    filters?: any
  ): Promise<Report> {
    const reportData = await this.getAnalytics({
      startDate,
      endDate,
      ...filters,
    });

    const report = this.reportRepository.create({
      reportType,
      startDate,
      endDate,
      filters,
      data: reportData,
      generatedAt: new Date(),
    });

    const savedReport = await this.reportRepository.save(report);
    this.logger.log(`Report generated: ${savedReport.id} - ${reportType}`);

    return savedReport;
  }

  async getReports(limit: number = 20): Promise<Report[]> {
    return this.reportRepository.find({
      order: { generatedAt: 'DESC' },
      take: limit,
    });
  }

  async getReport(reportId: string): Promise<Report> {
    const report = await this.reportRepository.findOne({
      where: { id: reportId },
    });

    if (!report) {
      throw new NotFoundException('التقرير غير موجود');
    }

    return report;
  }

  private async getTransferSuccessRate(startDate: Date): Promise<number> {
    const [totalTransfers, successfulTransfers] = await Promise.all([
      this.analyticsEventRepository.count({
        where: {
          eventType: 'transfer_initiated',
          timestamp: MoreThan(startDate),
        },
      }),
      this.analyticsEventRepository.count({
        where: {
          eventType: 'transfer_completed',
          timestamp: MoreThan(startDate),
        },
      }),
    ]);

    return totalTransfers > 0 ? (successfulTransfers / totalTransfers) * 100 : 0;
  }

  private async getTopCountries(startDate: Date): Promise<any[]> {
    return this.analyticsEventRepository
      .createQueryBuilder('event')
      .select([
        'event.properties->>\'country\' as country',
        'COUNT(*) as count',
      ])
      .where('event.timestamp > :startDate', { startDate })
      .andWhere('event.properties->>\'country\' IS NOT NULL')
      .groupBy('country')
      .orderBy('count', 'DESC')
      .limit(10)
      .getRawMany();
  }

  private async getRecentActivity(limit: number): Promise<any[]> {
    return this.analyticsEventRepository.find({
      order: { timestamp: 'DESC' },
      take: limit,
      select: ['eventType', 'userId', 'timestamp', 'properties'],
    });
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async generateDailyReport(): Promise<void> {
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const startOfDay = new Date(yesterday.setHours(0, 0, 0, 0));
    const endOfDay = new Date(yesterday.setHours(23, 59, 59, 999));

    try {
      await this.generateReport('daily_summary', startOfDay, endOfDay);
      this.logger.log('Daily report generated successfully');
    } catch (error) {
      this.logger.error('Failed to generate daily report', error.stack);
    }
  }

  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async generateMonthlyReport(): Promise<void> {
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    const startOfMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);
    const endOfMonth = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);

    try {
      await this.generateReport('monthly_summary', startOfMonth, endOfMonth);
      this.logger.log('Monthly report generated successfully');
    } catch (error) {
      this.logger.error('Failed to generate monthly report', error.stack);
    }
  }

  async cleanupOldEvents(daysToKeep: number = 365): Promise<void> {
    const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
    
    const result = await this.analyticsEventRepository.delete({
      timestamp: LessThan(cutoffDate),
    });

    if (result.affected && result.affected > 0) {
      this.logger.log(`Cleaned up ${result.affected} old analytics events`);
    }
  }
}
