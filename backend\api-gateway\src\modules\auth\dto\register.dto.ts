import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  MinLength,
  MaxLength,
  Matches,
  IsPhoneNumber,
} from 'class-validator';

export class RegisterDto {
  @ApiProperty({
    description: 'الاسم الأول',
    example: 'أحمد',
  })
  @IsString({ message: 'الاسم الأول يجب أن يكون نص' })
  @IsNotEmpty({ message: 'الاسم الأول مطلوب' })
  @MinLength(2, { message: 'الاسم الأول يجب أن يكون حرفين على الأقل' })
  @MaxLength(50, { message: 'الاسم الأول يجب أن يكون أقل من 50 حرف' })
  firstName: string;

  @ApiProperty({
    description: 'الاسم الأخير',
    example: 'محمد',
  })
  @IsString({ message: 'الاسم الأخير يجب أن يكون نص' })
  @IsNotEmpty({ message: 'الاسم الأخير مطلوب' })
  @MinLength(2, { message: 'الاسم الأخير يجب أن يكون حرفين على الأقل' })
  @MaxLength(50, { message: 'الاسم الأخير يجب أن يكون أقل من 50 حرف' })
  lastName: string;

  @ApiProperty({
    description: 'البريد الإلكتروني',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'البريد الإلكتروني غير صحيح' })
  @IsNotEmpty({ message: 'البريد الإلكتروني مطلوب' })
  email: string;

  @ApiProperty({
    description: 'رقم الهاتف',
    example: '+966501234567',
  })
  @IsPhoneNumber('SA', { message: 'رقم الهاتف غير صحيح' })
  @IsNotEmpty({ message: 'رقم الهاتف مطلوب' })
  phone: string;

  @ApiProperty({
    description: 'كلمة المرور',
    example: 'SecurePassword123!',
    minLength: 8,
  })
  @IsString({ message: 'كلمة المرور يجب أن تكون نص' })
  @IsNotEmpty({ message: 'كلمة المرور مطلوبة' })
  @MinLength(8, { message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    {
      message: 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص',
    },
  )
  password: string;

  @ApiProperty({
    description: 'رقم الهوية الوطنية',
    example: '1234567890',
  })
  @IsString({ message: 'رقم الهوية يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم الهوية مطلوب' })
  @Matches(/^\d{10}$/, { message: 'رقم الهوية يجب أن يكون 10 أرقام' })
  nationalId: string;
}
