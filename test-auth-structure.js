/**
 * اختبار بنية خدمة المصادقة
 * Test Auth Service Structure
 */

const fs = require('fs');
const path = require('path');

// قائمة الملفات المطلوبة
const requiredFiles = [
  // Main files
  'backend/auth-service/package.json',
  'backend/auth-service/tsconfig.json',
  'backend/auth-service/Dockerfile',
  'backend/auth-service/src/main.ts',
  'backend/auth-service/src/app.module.ts',
  
  // User entity and module
  'backend/auth-service/src/modules/users/entities/user.entity.ts',
  'backend/auth-service/src/modules/users/users.module.ts',
  'backend/auth-service/src/modules/users/services/users.service.ts',
  
  // Auth module
  'backend/auth-service/src/modules/auth/auth.module.ts',
  'backend/auth-service/src/modules/auth/controllers/auth.controller.ts',
  'backend/auth-service/src/modules/auth/services/auth.service.ts',
  'backend/auth-service/src/modules/auth/services/token.service.ts',
  'backend/auth-service/src/modules/auth/services/session.service.ts',
  'backend/auth-service/src/modules/auth/entities/user-session.entity.ts',
  'backend/auth-service/src/modules/auth/strategies/jwt.strategy.ts',
  'backend/auth-service/src/modules/auth/guards/jwt-auth.guard.ts',
  'backend/auth-service/src/modules/auth/decorators/get-user.decorator.ts',
  
  // DTOs
  'backend/auth-service/src/modules/auth/dto/register.dto.ts',
  'backend/auth-service/src/modules/auth/dto/login.dto.ts',
  
  // Shared modules
  'backend/auth-service/src/shared/health/health.controller.ts',
  'backend/auth-service/src/shared/health/health.service.ts',
  'backend/auth-service/src/shared/health/health.module.ts',
  'backend/auth-service/src/shared/redis/redis.service.ts',
  'backend/auth-service/src/shared/redis/redis.module.ts',
  'backend/auth-service/src/shared/logger/logger.module.ts',
  'backend/auth-service/src/shared/database/database.module.ts',
  
  // Notification module
  'backend/auth-service/src/modules/notification/notification.module.ts',
  'backend/auth-service/src/modules/notification/services/notification.service.ts',
  
  // Test files
  'backend/auth-service/test/auth.e2e-spec.ts',
  'backend/auth-service/test/jest-e2e.json',
  'backend/auth-service/test/setup.ts',
  'backend/auth-service/test/simple-test.js',
  
  // Environment file
  '.env',
];

console.log('🔍 فحص بنية خدمة المصادقة');
console.log('============================');

let totalFiles = requiredFiles.length;
let existingFiles = 0;
let missingFiles = [];

requiredFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${filePath}`);
    existingFiles++;
  } else {
    console.log(`❌ ${filePath}`);
    missingFiles.push(filePath);
  }
});

console.log('\n📊 نتائج الفحص:');
console.log(`✅ ملفات موجودة: ${existingFiles}/${totalFiles}`);
console.log(`❌ ملفات مفقودة: ${missingFiles.length}`);
console.log(`📈 نسبة الاكتمال: ${Math.round((existingFiles / totalFiles) * 100)}%`);

if (missingFiles.length > 0) {
  console.log('\n❌ الملفات المفقودة:');
  missingFiles.forEach(file => {
    console.log(`   - ${file}`);
  });
}

// فحص محتوى package.json
console.log('\n📦 فحص package.json:');
try {
  const packagePath = 'backend/auth-service/package.json';
  if (fs.existsSync(packagePath)) {
    const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    console.log(`   اسم الحزمة: ${packageContent.name}`);
    console.log(`   الإصدار: ${packageContent.version}`);
    console.log(`   الوصف: ${packageContent.description}`);
    
    // فحص التبعيات المهمة
    const importantDeps = [
      '@nestjs/common',
      '@nestjs/core',
      '@nestjs/jwt',
      '@nestjs/passport',
      '@nestjs/typeorm',
      'typeorm',
      'bcrypt',
      'passport-jwt'
    ];
    
    console.log('\n   التبعيات المهمة:');
    importantDeps.forEach(dep => {
      if (packageContent.dependencies && packageContent.dependencies[dep]) {
        console.log(`   ✅ ${dep}: ${packageContent.dependencies[dep]}`);
      } else {
        console.log(`   ❌ ${dep}: مفقود`);
      }
    });
  }
} catch (error) {
  console.log(`   ❌ خطأ في قراءة package.json: ${error.message}`);
}

// فحص ملف البيئة
console.log('\n🔧 فحص ملف البيئة:');
try {
  if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');
    const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    console.log(`   عدد المتغيرات: ${envLines.length}`);
    
    // فحص المتغيرات المهمة
    const importantVars = [
      'JWT_SECRET',
      'JWT_REFRESH_SECRET',
      'POSTGRES_URL',
      'REDIS_URL',
      'AUTH_SERVICE_PORT'
    ];
    
    importantVars.forEach(varName => {
      if (envContent.includes(varName)) {
        console.log(`   ✅ ${varName}`);
      } else {
        console.log(`   ❌ ${varName}: مفقود`);
      }
    });
  }
} catch (error) {
  console.log(`   ❌ خطأ في قراءة .env: ${error.message}`);
}

// فحص بنية المجلدات
console.log('\n📁 فحص بنية المجلدات:');
const requiredDirs = [
  'backend/auth-service/src',
  'backend/auth-service/src/modules',
  'backend/auth-service/src/modules/auth',
  'backend/auth-service/src/modules/users',
  'backend/auth-service/src/modules/notification',
  'backend/auth-service/src/shared',
  'backend/auth-service/test',
];

requiredDirs.forEach(dirPath => {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    const files = fs.readdirSync(dirPath);
    console.log(`   ✅ ${dirPath} (${files.length} ملف)`);
  } else {
    console.log(`   ❌ ${dirPath}`);
  }
});

console.log('\n🎯 التوصيات:');
if (existingFiles === totalFiles) {
  console.log('🎉 ممتاز! جميع الملفات موجودة.');
  console.log('💡 يمكنك الآن تجربة تثبيت التبعيات وتشغيل الخدمة:');
  console.log('   cd backend/auth-service');
  console.log('   npm install');
  console.log('   npm run start:dev');
} else {
  console.log('⚠️  بعض الملفات مفقودة. يرجى إنشاؤها أولاً.');
  console.log(`📈 نسبة الاكتمال: ${Math.round((existingFiles / totalFiles) * 100)}%`);
}

console.log('\n✨ انتهى الفحص!');
