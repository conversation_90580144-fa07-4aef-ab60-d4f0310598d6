@echo off
chcp 65001 >nul
title WS Transfir - XAMPP System Launcher

:: WS Transfir XAMPP System Launcher
:: مشغل نظام WS Transfir على XAMPP

color 0C
cls

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🔥 WS TRANSFIR - XAMPP SYSTEM LAUNCHER 🔥              ██
echo ██                                                            ██
echo ██    نظام التحويلات المالية على XAMPP                       ██
echo ██    Money Transfer System on XAMPP Platform                 ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

:: Get current time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%DD%/%MM%/%YYYY% %HH%:%Min%:%Sec%"

echo 📅 التاريخ والوقت: %timestamp%
echo 🔥 المنصة: XAMPP
echo 📁 المجلد الحالي: %CD%
echo 🌐 الوضع: XAMPP Production Ready
echo.

:: Phase 1: XAMPP Environment Check
echo ═══════════════════════════════════════════════════════════════
echo 🔍 المرحلة 1: فحص بيئة XAMPP
echo ═══════════════════════════════════════════════════════════════
echo.

:: Check if we're in XAMPP directory
echo 🔍 فحص مجلد XAMPP...
if exist "C:\xampp\htdocs" (
    echo ✅ تم العثور على XAMPP في C:\xampp
    set XAMPP_ROOT=C:\xampp
) else (
    echo ⚠️ لم يتم العثور على XAMPP في المسار الافتراضي
    echo 📁 استخدام المجلد الحالي: %CD%
    set XAMPP_ROOT=%CD%
)

:: Check Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL: Node.js غير مثبت
    echo.
    echo 📥 يرجى تثبيت Node.js من:
    echo 🌐 https://nodejs.org/en/download/
    echo.
    echo 💡 نصيحة: XAMPP يحتاج Node.js لتشغيل تطبيقات JavaScript
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js متاح - الإصدار: %NODE_VERSION%

:: Check npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL: npm غير متاح
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm متاح - الإصدار: %NPM_VERSION%

:: Check XAMPP Apache status
echo 🔍 فحص Apache XAMPP...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe" >NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Apache XAMPP يعمل
    set APACHE_STATUS=✅ يعمل
) else (
    echo ⚠️ Apache XAMPP غير مشغل
    echo 💡 يمكن تشغيل النظام بدون Apache
    set APACHE_STATUS=⚠️ غير مشغل
)

:: Check XAMPP MySQL status
echo 🔍 فحص MySQL XAMPP...
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe" >NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ MySQL XAMPP يعمل
    set MYSQL_STATUS=✅ يعمل
) else (
    echo ⚠️ MySQL XAMPP غير مشغل
    echo 💡 النظام يستخدم قاعدة بيانات محاكاة
    set MYSQL_STATUS=⚠️ غير مشغل
)

echo.

:: Phase 2: Dependencies Check
echo ═══════════════════════════════════════════════════════════════
echo 📦 المرحلة 2: فحص وتثبيت المكتبات
echo ═══════════════════════════════════════════════════════════════
echo.

:: Check required files
echo 🔍 فحص الملفات المطلوبة...
if not exist "xampp-server.js" (
    echo ❌ CRITICAL: ملف xampp-server.js غير موجود
    pause
    exit /b 1
)
echo ✅ xampp-server.js موجود

if not exist "package.json" (
    echo ❌ CRITICAL: ملف package.json غير موجود
    pause
    exit /b 1
)
echo ✅ package.json موجود

:: Install dependencies if needed
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة لـ XAMPP...
    npm install express cors --silent
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🔧 جاري المحاولة مرة أخرى...
        npm install express cors --force --silent
        if errorlevel 1 (
            echo ❌ فشل في تثبيت المكتبات نهائياً
            pause
            exit /b 1
        )
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات مثبتة مسبقاً
)

:: Verify critical dependencies
echo 🔍 فحص المكتبات الحرجة...
node -e "require('express'); require('cors'); console.log('✅ المكتبات الحرجة متاحة');" 2>nul
if errorlevel 1 (
    echo ❌ بعض المكتبات الحرجة مفقودة
    echo 📦 إعادة تثبيت...
    npm install express cors --force --silent
)

echo.

:: Phase 3: Port Management
echo ═══════════════════════════════════════════════════════════════
echo 🌐 المرحلة 3: إدارة المنافذ
echo ═══════════════════════════════════════════════════════════════
echo.

:: Kill existing Node.js processes
echo 🛑 إيقاف عمليات Node.js السابقة...
taskkill /F /IM node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

:: Check port 8080 (XAMPP custom apps port)
echo 🔍 فحص المنفذ 8080 (XAMPP Apps)...
netstat -an | findstr :8080 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ المنفذ 8080 مستخدم - تحرير المنفذ...
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8080') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 1 /nobreak >nul
)
echo ✅ المنفذ 8080 متاح

:: Get local IP
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4" ^| findstr /v "127.0.0.1" ^| findstr /v "169.254"') do (
    for /f "tokens=1" %%j in ("%%i") do set LOCAL_IP=%%j
)

if not defined LOCAL_IP set LOCAL_IP=localhost
echo 🌐 عنوان IP المحلي: %LOCAL_IP%

echo.

:: Phase 4: XAMPP System Launch
echo ═══════════════════════════════════════════════════════════════
echo 🚀 المرحلة 4: تشغيل نظام XAMPP
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🔥 تشغيل WS Transfir على XAMPP...
start "WS Transfir XAMPP Server" cmd /k "title WS Transfir XAMPP && color 0C && echo. && echo ████████████████████████████████████████ && echo ██  🔥 WS TRANSFIR XAMPP SERVER  ██ && echo ████████████████████████████████████████ && echo. && echo 🌐 Port: 8080 && echo 📡 Status: Starting... && echo 🔗 URL: http://localhost:8080 && echo 📁 Path: %CD% && echo. && node xampp-server.js"

:: Wait for server to start
echo ⏳ انتظار تشغيل الخادم (8 ثوان)...
timeout /t 8 /nobreak >nul

:: Test server
echo 🧪 اختبار الخادم...
curl -s http://localhost:8080/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️ الخادم قد يحتاج وقت إضافي للتشغيل
    set SERVER_STATUS=⚠️ قيد التشغيل
) else (
    echo ✅ الخادم يعمل بنجاح
    set SERVER_STATUS=✅ يعمل
)

echo.

:: Phase 5: Browser Launch
echo ═══════════════════════════════════════════════════════════════
echo 🌐 المرحلة 5: فتح المتصفح
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🌐 فتح النظام في المتصفح...
start "" "http://localhost:8080"
timeout /t 2 /nobreak >nul

:: Also open health check
start "" "http://localhost:8080/api/health"
timeout /t 1 /nobreak >nul

echo.

:: Final Status Report
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🎉 WS TRANSFIR XAMPP SYSTEM IS NOW RUNNING!            ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 📊 تقرير حالة نظام XAMPP:
echo ===========================
echo 🔥 WS Transfir Server:    %SERVER_STATUS%
echo 🌐 Apache XAMPP:          %APACHE_STATUS%
echo 🗄️ MySQL XAMPP:           %MYSQL_STATUS%
echo 💻 Node.js:               ✅ %NODE_VERSION%
echo 📦 npm:                   ✅ %NPM_VERSION%
echo 🏠 Local IP:              %LOCAL_IP%
echo 📁 XAMPP Root:            %XAMPP_ROOT%
echo.

echo 🌍 روابط الوصول:
echo ==================
echo 🎨 الواجهة الرئيسية:      http://localhost:8080
echo 🔧 API Health Check:      http://localhost:8080/api/health
echo 📈 System Status:         http://localhost:8080/api/status
echo 🔐 Authentication:        http://localhost:8080/api/auth/login
echo 💸 Transfers:             http://localhost:8080/api/transfers
echo.

if not "%LOCAL_IP%"=="localhost" (
    echo 🌐 الوصول الخارجي:
    echo ===================
    echo 🎨 الواجهة الرئيسية:      http://%LOCAL_IP%:8080
    echo 🔧 API Health Check:      http://%LOCAL_IP%:8080/api/health
    echo 📱 للهواتف الذكية:       http://%LOCAL_IP%:8080
    echo.
)

echo 🔐 بيانات الدخول التجريبية:
echo ============================
echo 👨‍💼 مدير النظام:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: admin123
echo.
echo 👤 مستخدم عادي:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: password123
echo.

echo 📋 الميزات المتاحة على XAMPP:
echo ===============================
echo ✅ نظام مصادقة متكامل
echo ✅ إدارة التحويلات المالية
echo ✅ واجهة مستخدم تفاعلية
echo ✅ API RESTful كامل
echo ✅ تكامل مع XAMPP
echo ✅ دعم قواعد البيانات المحاكاة
echo ✅ مراقبة الأداء
echo ✅ تسجيل العمليات
echo.

echo 💡 نصائح استخدام XAMPP:
echo =========================
echo 🔹 النظام يعمل على المنفذ 8080 (XAMPP standard)
echo 🔹 يمكن الوصول من أي جهاز في الشبكة
echo 🔹 متوافق مع Apache و MySQL XAMPP
echo 🔹 يدعم التطوير والإنتاج
echo 🔹 لإيقاف النظام: أغلق نافذة الخادم
echo 🔹 لإعادة التشغيل: شغل هذا الملف مرة أخرى
echo.

echo 🔧 إدارة نظام XAMPP:
echo ====================
echo 🔹 مراقبة الأداء: راقب نافذة الخادم
echo 🔹 فحص الحالة: http://localhost:8080/api/health
echo 🔹 XAMPP Control Panel: لإدارة Apache و MySQL
echo 🔹 ملفات السجل: تحقق من نافذة الخادم
echo 🔹 النسخ الاحتياطي: انسخ مجلد المشروع
echo.

echo 📞 الدعم الفني:
echo ================
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📱 الهاتف: +966 11 123 4567
echo 🌐 الموقع: https://wstransfir.com
echo 📚 توثيق XAMPP: https://docs.wstransfir.com/xampp
echo.

echo 🎉 نظام WS Transfir جاهز على XAMPP!
echo ====================================
echo 🔥 استمتع بتجربة التحويلات المالية على XAMPP
echo 🚀 النظام محسن للعمل مع بيئة XAMPP
echo 💼 مناسب للتطوير والاختبار والإنتاج
echo.

echo اضغط أي مفتاح للاستمرار أو أغلق النافذة...
pause >nul
