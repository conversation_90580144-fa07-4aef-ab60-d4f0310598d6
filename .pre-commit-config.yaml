# Pre-commit Configuration
# ========================
# إعدادات pre-commit للتحقق من جودة الكود

repos:
  # Python Code Formatting
  - repo: https://github.com/psf/black
    rev: 23.10.1
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]
        exclude: ^(migrations/|node_modules/)

  # Python Import Sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]
        exclude: ^(migrations/|node_modules/)

  # Python Linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        exclude: ^(migrations/|node_modules/)

  # Python Type Checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        exclude: ^(migrations/|node_modules/|tests/)

  # Security Checks
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, ., -f, json, -o, bandit-report.json]
        exclude: ^(tests/|migrations/)

  # General Hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # File checks
      - id: check-added-large-files
        args: [--maxkb=1000]
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-toml
      - id: check-yaml
        exclude: ^(docker-compose.*\.yml|k8s/)
      - id: check-json
        exclude: ^(\.vscode/|package.*\.json)
      
      # Python specific
      - id: check-ast
      - id: check-builtin-literals
      - id: check-docstring-first
      - id: debug-statements
      - id: name-tests-test
        args: [--pytest-test-first]
      
      # General formatting
      - id: end-of-file-fixer
        exclude: ^(\.min\.|node_modules/)
      - id: trailing-whitespace
        exclude: ^(\.min\.|node_modules/)
      - id: mixed-line-ending
        args: [--fix=lf]

  # SQL Formatting
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.3.5
    hooks:
      - id: sqlfluff-lint
        files: \.sql$
        exclude: ^(migrations/|seeds/)
      - id: sqlfluff-fix
        files: \.sql$
        exclude: ^(migrations/|seeds/)

  # JavaScript/TypeScript (for frontend)
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.54.0
    hooks:
      - id: eslint
        files: \.(js|jsx|ts|tsx)$
        exclude: ^(node_modules/|build/|dist/)
        additional_dependencies:
          - eslint@8.54.0
          - "@typescript-eslint/eslint-plugin@6.12.0"
          - "@typescript-eslint/parser@6.12.0"

  # Prettier for frontend formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.3
    hooks:
      - id: prettier
        files: \.(js|jsx|ts|tsx|json|css|scss|md|yaml|yml)$
        exclude: ^(node_modules/|build/|dist/|package-lock\.json)

  # Docker
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # Secrets Detection
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]
        exclude: ^(\.git/|node_modules/|\.pytest_cache/)

  # Documentation
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: [--convention=google]
        exclude: ^(tests/|migrations/)

# Configuration
default_language_version:
  python: python3.11

# Exclude patterns
exclude: |
  (?x)^(
    migrations/.*|
    node_modules/.*|
    \.git/.*|
    \.pytest_cache/.*|
    __pycache__/.*|
    \.venv/.*|
    venv/.*|
    build/.*|
    dist/.*|
    \.min\..*|
    package-lock\.json
  )$

# Fail fast
fail_fast: false

# Minimum pre-commit version
minimum_pre_commit_version: 3.5.0
