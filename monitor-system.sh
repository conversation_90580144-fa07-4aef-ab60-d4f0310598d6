#!/bin/bash

# WS Transfir System Monitor Script
# نص مراقبة نظام WS Transfir

echo "📊 مراقبة نظام WS Transfir"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GRE<PERSON>}$1${NC}"
}

print_service() {
    echo -e "${CYAN}$1${NC}"
}

# Check if Docker Compose is available
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    elif docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        print_error "Docker Compose غير متاح"
        exit 1
    fi
}

# Check service health
check_service_health() {
    local service_name=$1
    local port=$2
    local path=${3:-"/health"}
    
    if nc -z localhost "$port" 2>/dev/null; then
        if curl -f -s "http://localhost:$port$path" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name${NC}"
        else
            echo -e "${YELLOW}⚠️  $service_name (يعمل لكن لا يستجيب للـ health check)${NC}"
        fi
    else
        echo -e "${RED}❌ $service_name (غير متاح)${NC}"
    fi
}

# Show container status
show_container_status() {
    print_header "🐳 حالة الحاويات:"
    
    containers=$(docker ps --filter "name=ws-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}")
    
    if [ -n "$containers" ]; then
        echo "$containers"
    else
        print_warning "لا توجد حاويات تعمل"
    fi
    echo ""
}

# Show service health status
show_service_health() {
    print_header "🏥 حالة صحة الخدمات:"
    
    # Database services
    print_service "قواعد البيانات:"
    check_service_health "PostgreSQL" 5432 ""
    check_service_health "Redis" 6379 ""
    check_service_health "MongoDB" 27017 ""
    check_service_health "Elasticsearch" 9200 "/_cluster/health"
    check_service_health "RabbitMQ" 5672 ""
    echo ""
    
    # Backend services
    print_service "الخدمات الخلفية:"
    check_service_health "API Gateway" 3000
    check_service_health "Auth Service" 3001
    check_service_health "User Service" 3002
    check_service_health "Transfer Service" 3003
    check_service_health "Wallet Service" 3004
    check_service_health "Notification Service" 3005
    check_service_health "Analytics Service" 3006
    check_service_health "AI Engine" 8000
    echo ""
    
    # Frontend and monitoring
    print_service "الواجهة الأمامية والمراقبة:"
    check_service_health "Web App" 3100
    check_service_health "Nginx" 80
    check_service_health "Grafana" 3007
    check_service_health "Prometheus" 9090
    check_service_health "Kibana" 5601
    check_service_health "Jaeger" 16686
    echo ""
}

# Show resource usage
show_resource_usage() {
    print_header "💻 استخدام الموارد:"
    
    echo "استخدام الذاكرة والمعالج للحاويات:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" $(docker ps --filter "name=ws-" -q) 2>/dev/null || echo "لا توجد حاويات تعمل"
    echo ""
}

# Show logs for a specific service
show_service_logs() {
    local service=$1
    local lines=${2:-50}
    
    print_header "📋 آخر $lines سطر من سجلات $service:"
    
    if docker ps --filter "name=ws-$service" --format "{{.Names}}" | grep -q "ws-$service"; then
        docker logs --tail "$lines" "ws-$service" 2>&1
    else
        print_error "الخدمة $service غير موجودة أو لا تعمل"
    fi
}

# Show all service logs
show_all_logs() {
    print_header "📋 سجلات جميع الخدمات:"
    
    services=("api-gateway" "auth-service" "user-service" "transfer-service" "wallet-service" "notification-service" "analytics-service")
    
    for service in "${services[@]}"; do
        if docker ps --filter "name=ws-$service" --format "{{.Names}}" | grep -q "ws-$service"; then
            echo ""
            print_service "=== $service ==="
            docker logs --tail 10 "ws-$service" 2>&1 | head -10
        fi
    done
    echo ""
}

# Show network information
show_network_info() {
    print_header "🌐 معلومات الشبكة:"
    
    networks=$(docker network ls --filter "name=ws-" --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}")
    
    if [ -n "$networks" ]; then
        echo "$networks"
        echo ""
        
        # Show network details
        network_name=$(docker network ls --filter "name=ws-" --format "{{.Name}}" | head -1)
        if [ -n "$network_name" ]; then
            print_service "تفاصيل الشبكة $network_name:"
            docker network inspect "$network_name" --format "{{range .Containers}}{{.Name}}: {{.IPv4Address}}{{println}}{{end}}" 2>/dev/null || echo "لا توجد حاويات متصلة"
        fi
    else
        print_warning "لا توجد شبكات للنظام"
    fi
    echo ""
}

# Show volume information
show_volume_info() {
    print_header "💾 معلومات التخزين:"
    
    volumes=$(docker volume ls --filter "name=ws-" --format "table {{.Name}}\t{{.Driver}}")
    
    if [ -n "$volumes" ]; then
        echo "$volumes"
        echo ""
        
        # Show volume sizes
        print_service "أحجام وحدات التخزين:"
        for volume in $(docker volume ls --filter "name=ws-" --format "{{.Name}}"); do
            size=$(docker run --rm -v "$volume":/data alpine du -sh /data 2>/dev/null | cut -f1)
            echo "  $volume: $size"
        done
    else
        print_warning "لا توجد وحدات تخزين للنظام"
    fi
    echo ""
}

# Monitor in real-time
monitor_realtime() {
    print_header "📊 مراقبة مباشرة (اضغط Ctrl+C للخروج):"
    
    while true; do
        clear
        echo "📊 مراقبة نظام WS Transfir - $(date)"
        echo "=================================="
        echo ""
        
        show_service_health
        show_resource_usage
        
        sleep 5
    done
}

# Show help
show_help() {
    echo "استخدام: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "الأوامر:"
    echo "  status              عرض حالة النظام العامة"
    echo "  health              فحص صحة الخدمات"
    echo "  resources           عرض استخدام الموارد"
    echo "  logs [service]      عرض سجلات خدمة معينة"
    echo "  logs-all            عرض سجلات جميع الخدمات"
    echo "  network             عرض معلومات الشبكة"
    echo "  volumes             عرض معلومات التخزين"
    echo "  monitor             مراقبة مباشرة"
    echo "  help                عرض هذه المساعدة"
    echo ""
    echo "أمثلة:"
    echo "  $0 status           عرض حالة النظام"
    echo "  $0 logs api-gateway عرض سجلات API Gateway"
    echo "  $0 monitor          بدء المراقبة المباشرة"
}

# Main execution
main() {
    check_docker_compose
    
    case "$1" in
        status|"")
            show_container_status
            show_service_health
            ;;
        health)
            show_service_health
            ;;
        resources)
            show_resource_usage
            ;;
        logs)
            if [ -n "$2" ]; then
                show_service_logs "$2" "${3:-50}"
            else
                print_error "يرجى تحديد اسم الخدمة"
                echo "الخدمات المتاحة: api-gateway, auth-service, user-service, transfer-service, wallet-service, notification-service, analytics-service"
            fi
            ;;
        logs-all)
            show_all_logs
            ;;
        network)
            show_network_info
            ;;
        volumes)
            show_volume_info
            ;;
        monitor)
            monitor_realtime
            ;;
        help)
            show_help
            ;;
        *)
            print_error "أمر غير معروف: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
