{"version": 3, "file": "mergeArrays.test.js", "names": ["describe", "it", "mergeArrays", "should", "deep", "equal"], "sources": ["../../source/helpers/mergeArrays.test.js"], "sourcesContent": ["import mergeArrays from './mergeArrays.js'\r\n\r\ndescribe('mergeArrays', () => {\r\n\tit('should merge arrays', () => {\r\n\t\tmergeArrays([1, 2], [2, 3]).should.deep.equal([1, 2, 3])\r\n\t})\r\n})"], "mappings": ";;AAAA;;;;AAEAA,QAAQ,CAAC,aAAD,EAAgB,YAAM;EAC7BC,EAAE,CAAC,qBAAD,EAAwB,YAAM;IAC/B,IAAAC,uBAAA,EAAY,CAAC,CAAD,EAAI,CAAJ,CAAZ,EAAoB,CAAC,CAAD,EAAI,CAAJ,CAApB,EAA4BC,MAA5B,CAAmCC,IAAnC,CAAwCC,KAAxC,CAA8C,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAA9C;EACA,CAFC,CAAF;AAGA,CAJO,CAAR"}