import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsDateString,
  IsN<PERSON><PERSON>,
  Min,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum TransferStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

export enum TransferDirection {
  SENT = 'sent',
  RECEIVED = 'received',
  ALL = 'all',
}

export class TransferHistoryQueryDto {
  @ApiProperty({
    description: 'رقم الصفحة',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'رقم الصفحة يجب أن يكون رقم' })
  @Min(1, { message: 'رقم الصفحة يجب أن يكون أكبر من صفر' })
  page?: number = 1;

  @ApiProperty({
    description: 'عدد العناصر في الصفحة',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'عدد العناصر يجب أن يكون رقم' })
  @Min(1, { message: 'عدد العناصر يجب أن يكون أكبر من صفر' })
  @Max(100, { message: 'عدد العناصر يجب أن يكون أقل من 100' })
  limit?: number = 10;

  @ApiProperty({
    description: 'حالة التحويل',
    enum: TransferStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(TransferStatus, { message: 'حالة التحويل غير صحيحة' })
  status?: TransferStatus;

  @ApiProperty({
    description: 'اتجاه التحويل',
    enum: TransferDirection,
    example: TransferDirection.ALL,
    required: false,
  })
  @IsOptional()
  @IsEnum(TransferDirection, { message: 'اتجاه التحويل غير صحيح' })
  direction?: TransferDirection = TransferDirection.ALL;

  @ApiProperty({
    description: 'تاريخ البداية',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ البداية غير صحيح' })
  startDate?: string;

  @ApiProperty({
    description: 'تاريخ النهاية',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ النهاية غير صحيح' })
  endDate?: string;

  @ApiProperty({
    description: 'العملة',
    example: 'SAR',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'العملة يجب أن تكون نص' })
  currency?: string;

  @ApiProperty({
    description: 'بلد الوجهة',
    example: 'US',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'بلد الوجهة يجب أن يكون نص' })
  destinationCountry?: string;

  @ApiProperty({
    description: 'الحد الأدنى للمبلغ',
    example: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'الحد الأدنى للمبلغ يجب أن يكون رقم' })
  @Min(0, { message: 'الحد الأدنى للمبلغ يجب أن يكون أكبر من أو يساوي صفر' })
  minAmount?: number;

  @ApiProperty({
    description: 'الحد الأقصى للمبلغ',
    example: 10000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'الحد الأقصى للمبلغ يجب أن يكون رقم' })
  @Min(0, { message: 'الحد الأقصى للمبلغ يجب أن يكون أكبر من أو يساوي صفر' })
  maxAmount?: number;

  @ApiProperty({
    description: 'البحث في النص (اسم المستلم، رقم التتبع، إلخ)',
    example: 'أحمد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'نص البحث يجب أن يكون نص' })
  search?: string;

  @ApiProperty({
    description: 'ترتيب النتائج',
    example: 'createdAt:desc',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ترتيب النتائج يجب أن يكون نص' })
  sortBy?: string = 'createdAt:desc';
}
