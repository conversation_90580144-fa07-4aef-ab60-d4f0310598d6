import { TextRange } from './TextRange';
import { ParserContext } from './ParserContext';
import { TSDocConfiguration } from '../configuration/TSDocConfiguration';
/**
 * The main API for parsing TSDoc comments.
 */
export declare class TSDocParser {
    /**
     * The configuration that was provided for the TSDocParser.
     */
    readonly configuration: TSDocConfiguration;
    constructor(configuration?: TSDocConfiguration);
    parseString(text: string): ParserContext;
    parseRange(range: TextRange): ParserContext;
}
//# sourceMappingURL=TSDocParser.d.ts.map