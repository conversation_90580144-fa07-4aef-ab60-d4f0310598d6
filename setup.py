#!/usr/bin/env python3
"""
WalletSystem Setup Configuration
===============================
إعداد نظام المحفظة الرقمية المتقدم
"""

from setuptools import setup, find_packages
import os
import sys

# Ensure Python 3.8+
if sys.version_info < (3, 8):
    print("Error: WalletSystem requires Python 3.8 or higher")
    sys.exit(1)

# Read README for long description
def read_readme():
    """Read README file for long description"""
    try:
        with open("README_NEW.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return "Advanced Digital Wallet System with comprehensive financial services"

# Read requirements
def read_requirements(filename="requirements.txt"):
    """Read requirements from file"""
    try:
        with open(filename, "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        requirements = []
        for line in lines:
            line = line.strip()
            # Skip comments, empty lines, and section headers
            if line and not line.startswith("#") and not line.startswith("["):
                # Remove inline comments
                if "#" in line:
                    line = line.split("#")[0].strip()
                if line:
                    requirements.append(line)
        
        return requirements
    except FileNotFoundError:
        return []

# Package metadata
PACKAGE_NAME = "walletsystem"
VERSION = "1.0.0"
DESCRIPTION = "Advanced Digital Wallet System"
LONG_DESCRIPTION = read_readme()
AUTHOR = "WalletSystem Development Team"
AUTHOR_EMAIL = "<EMAIL>"
URL = "https://github.com/walletsystem/walletsystem"
LICENSE = "MIT"

# Requirements
INSTALL_REQUIRES = read_requirements()

# Development requirements
DEV_REQUIRES = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "black>=23.10.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
    "jupyter>=1.0.0",
    "ipython>=8.17.0",
]

# Production requirements
PROD_REQUIRES = [
    "gunicorn>=21.2.0",
    "supervisor>=4.2.0",
]

# Optional requirements
EXTRAS_REQUIRE = {
    "dev": DEV_REQUIRES,
    "prod": PROD_REQUIRES,
    "redis": ["redis>=5.0.0", "aioredis>=2.0.0"],
    "celery": ["celery>=5.3.0", "kombu>=5.3.0"],
    "email": ["aiosmtplib>=3.0.0", "jinja2>=3.1.0"],
    "aws": ["boto3>=1.34.0"],
    "azure": ["azure-storage-blob>=12.19.0"],
    "monitoring": ["sentry-sdk[fastapi]>=1.38.0", "datadog>=0.48.0"],
    "ml": ["scikit-learn>=1.3.0", "tensorflow>=2.15.0", "torch>=2.1.0"],
    "blockchain": ["web3>=6.11.0", "eth-account>=0.10.0"],
    "all": DEV_REQUIRES + PROD_REQUIRES + [
        "redis>=5.0.0", "aioredis>=2.0.0",
        "celery>=5.3.0", "kombu>=5.3.0",
        "aiosmtplib>=3.0.0", "jinja2>=3.1.0",
        "boto3>=1.34.0", "azure-storage-blob>=12.19.0",
        "sentry-sdk[fastapi]>=1.38.0",
        "scikit-learn>=1.3.0",
        "web3>=6.11.0", "eth-account>=0.10.0",
    ]
}

# Classifiers
CLASSIFIERS = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Framework :: FastAPI",
    "Framework :: AsyncIO",
]

# Keywords
KEYWORDS = [
    "wallet", "digital-wallet", "fintech", "payment", "banking",
    "financial-services", "fastapi", "asyncio", "postgresql",
    "microservices", "api", "rest", "websocket", "real-time",
    "security", "encryption", "authentication", "authorization",
    "compliance", "pci-dss", "iso20022", "swift", "sama",
    "saudi-arabia", "middle-east", "arabic", "rtl"
]

# Entry points
ENTRY_POINTS = {
    "console_scripts": [
        "walletsystem=backend.main:main",
        "walletsystem-migrate=database.migrate:main",
        "walletsystem-seed=database.seed:main",
        "walletsystem-test=tests.runner:main",
    ]
}

# Package data
PACKAGE_DATA = {
    "walletsystem": [
        "database/migrations/*.sql",
        "database/seeds/*.sql",
        "frontend/web-app/build/*",
        "frontend/mobile-app/build/*",
        "docs/*",
        "config/*.yaml",
        "config/*.json",
    ]
}

# Data files
DATA_FILES = [
    ("config", ["config/development.yaml", "config/production.yaml"]),
    ("docs", ["README_NEW.md", "LICENSE"]),
]

# Setup configuration
setup(
    name=PACKAGE_NAME,
    version=VERSION,
    description=DESCRIPTION,
    long_description=LONG_DESCRIPTION,
    long_description_content_type="text/markdown",
    author=AUTHOR,
    author_email=AUTHOR_EMAIL,
    url=URL,
    license=LICENSE,
    
    # Package discovery
    packages=find_packages(exclude=["tests*", "docs*"]),
    package_data=PACKAGE_DATA,
    data_files=DATA_FILES,
    include_package_data=True,
    
    # Dependencies
    python_requires=">=3.8",
    install_requires=INSTALL_REQUIRES,
    extras_require=EXTRAS_REQUIRE,
    
    # Metadata
    classifiers=CLASSIFIERS,
    keywords=" ".join(KEYWORDS),
    
    # Entry points
    entry_points=ENTRY_POINTS,
    
    # Options
    zip_safe=False,
    
    # Project URLs
    project_urls={
        "Documentation": "https://docs.walletsystem.com",
        "Source": "https://github.com/walletsystem/walletsystem",
        "Tracker": "https://github.com/walletsystem/walletsystem/issues",
        "Funding": "https://github.com/sponsors/walletsystem",
    },
)

# Post-installation message
print("""
🎉 WalletSystem Installation Complete!

Next Steps:
1. Set up your database: walletsystem-migrate
2. Seed initial data: walletsystem-seed
3. Run tests: walletsystem-test
4. Start the server: walletsystem

For more information, visit: https://docs.walletsystem.com
""")
