import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('user_profiles')
export class UserProfile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  // Personal Details
  @Column({ nullable: true, length: 100 })
  middleName?: string;

  @Column({ nullable: true, length: 100 })
  motherName?: string;

  @Column({ nullable: true, length: 100 })
  fatherName?: string;

  @Column({ nullable: true, length: 100 })
  placeOfBirth?: string;

  @Column({ nullable: true, length: 20 })
  maritalStatus?: string;

  @Column({ nullable: true })
  numberOfDependents?: number;

  // Contact Information
  @Column({ nullable: true, length: 20 })
  alternativePhone?: string;

  @Column({ nullable: true, length: 255 })
  alternativeEmail?: string;

  // Address Information
  @Column({ nullable: true, length: 255 })
  addressLine1?: string;

  @Column({ nullable: true, length: 255 })
  addressLine2?: string;

  @Column({ nullable: true, length: 100 })
  city?: string;

  @Column({ nullable: true, length: 100 })
  state?: string;

  @Column({ nullable: true, length: 2 })
  country?: string;

  @Column({ nullable: true, length: 20 })
  postalCode?: string;

  // Employment Information
  @Column({ nullable: true, length: 100 })
  occupation?: string;

  @Column({ nullable: true, length: 255 })
  employer?: string;

  @Column({ nullable: true, length: 255 })
  employerAddress?: string;

  @Column({ nullable: true, length: 20 })
  employmentType?: string; // full-time, part-time, self-employed, unemployed

  @Column({ nullable: true })
  yearsOfEmployment?: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  monthlyIncome?: number;

  @Column({ nullable: true, length: 100 })
  sourceOfIncome?: string;

  // Financial Information
  @Column({ nullable: true, length: 255 })
  bankName?: string;

  @Column({ nullable: true, length: 50 })
  bankAccountNumber?: string;

  @Column({ nullable: true, length: 20 })
  iban?: string;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  estimatedNetWorth?: number;

  // Profile Settings
  @Column({ nullable: true, length: 500 })
  bio?: string;

  @Column({ nullable: true, length: 255 })
  profilePicture?: string;

  @Column({ default: true })
  profilePublic: boolean;

  @Column({ default: true })
  allowMarketing: boolean;

  @Column({ default: false })
  twoFactorEnabled: boolean;

  // Emergency Contact
  @Column({ nullable: true, length: 200 })
  emergencyContactName?: string;

  @Column({ nullable: true, length: 20 })
  emergencyContactPhone?: string;

  @Column({ nullable: true, length: 100 })
  emergencyContactRelation?: string;

  // Additional Information
  @Column({ type: 'json', nullable: true })
  additionalData?: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToOne(() => User, user => user.profile)
  @JoinColumn({ name: 'userId' })
  user: User;

  // Computed properties
  get isComplete(): boolean {
    const requiredFields = [
      'addressLine1',
      'city',
      'country',
      'occupation',
      'monthlyIncome',
      'sourceOfIncome'
    ];
    
    return requiredFields.every(field => this[field] != null);
  }

  get completionPercentage(): number {
    const allFields = [
      'middleName', 'addressLine1', 'city', 'state', 'country', 'postalCode',
      'occupation', 'employer', 'monthlyIncome', 'sourceOfIncome',
      'bankName', 'bankAccountNumber', 'emergencyContactName', 'emergencyContactPhone'
    ];
    
    const filledFields = allFields.filter(field => this[field] != null).length;
    return Math.round((filledFields / allFields.length) * 100);
  }
}
