{"version": 3, "file": "extractPhoneContext.test.js", "names": ["parsePhoneNumber", "parameters", "push", "metadata", "parsePhoneNumber_", "apply", "describe", "it", "NZ_NUMBER", "PhoneNumber", "expectPhoneNumbersToBeEqual", "nzFromPhoneContext", "brFromPhoneContext", "usFromPhoneContext", "expectToThrowForInvalidPhoneContext", "string", "expect", "to", "be", "undefined", "phoneNumber1", "phoneNumber2", "number", "ext"], "sources": ["../../source/helpers/extractPhoneContext.test.js"], "sourcesContent": ["import parsePhoneNumber_ from '../parsePhoneNumber.js'\r\nimport PhoneNumber from '../PhoneNumber.js'\r\nimport metadata from '../../metadata.min.json' assert { type: 'json' }\r\n\r\nfunction parsePhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn parsePhoneNumber_.apply(this, parameters)\r\n}\r\n\r\ndescribe('extractPhoneContext', function() {\r\n\tit('should parse RFC 3966 phone number URIs', function() {\r\n\t\t// context    = \";phone-context=\" descriptor\r\n\t\t// descriptor = domainname / global-number-digits\r\n\r\n\t\tconst NZ_NUMBER = new PhoneNumber('64', '33316005', metadata)\r\n\r\n\t\t// Valid global-phone-digits\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+64'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+64;{this isn\\'t part of phone-context anymore!}'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\r\n\t\tconst nzFromPhoneContext = new PhoneNumber('64', '3033316005', metadata)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+64-3'),\r\n\t\t\tnzFromPhoneContext\r\n\t\t)\r\n\r\n\t\tconst brFromPhoneContext = new PhoneNumber('55', '5033316005', metadata)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+(555)'),\r\n\t\t\tbrFromPhoneContext\r\n\t\t)\r\n\r\n\t\tconst usFromPhoneContext = new PhoneNumber('1', '23033316005', metadata)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+-1-2.3()'),\r\n\t\t\tusFromPhoneContext\r\n\t\t)\r\n\r\n\t\t// Valid domainname.\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=abc.nz', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=www.PHONE-numb3r.com', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=a', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=3phone.J.', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=a--z', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\r\n\t\t// Should strip ISDN subaddress.\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;isub=/@;phone-context=+64', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\r\n\t\t// // Should support incorrectly-written RFC 3966 phone numbers:\r\n\t\t// // the ones written without a `tel:` prefix.\r\n\t\t// expectPhoneNumbersToBeEqual(\r\n\t\t// \tparsePhoneNumber('033316005;phone-context=+64', 'NZ'),\r\n\t\t// \tNZ_NUMBER\r\n\t\t// )\r\n\r\n\t\t// Invalid descriptor.\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=+')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=64')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=++64')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=+abc')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=.')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=3phone')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=a-.nz')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=a{b}c')\r\n\t})\r\n})\r\n\r\nfunction expectToThrowForInvalidPhoneContext(string) {\r\n\texpect(parsePhoneNumber(string)).to.be.undefined\r\n}\r\n\r\nfunction expectPhoneNumbersToBeEqual(phoneNumber1, phoneNumber2) {\r\n\tif (!phoneNumber1 || !phoneNumber2) {\r\n\t\treturn false\r\n\t}\r\n\treturn phoneNumber1.number === phoneNumber2.number &&\r\n\t\tphoneNumber1.ext === phoneNumber2.ext\r\n}"], "mappings": ";;AAAA;;AACA;;AACA;;;;AAEA,SAASA,gBAAT,GAAyC;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACxCA,UAAU,CAACC,IAAX,CAAgBC,uBAAhB;EACA,OAAOC,4BAAA,CAAkBC,KAAlB,CAAwB,IAAxB,EAA8BJ,UAA9B,CAAP;AACA;;AAEDK,QAAQ,CAAC,qBAAD,EAAwB,YAAW;EAC1CC,EAAE,CAAC,yCAAD,EAA4C,YAAW;IACxD;IACA;IAEA,IAAMC,SAAS,GAAG,IAAIC,uBAAJ,CAAgB,IAAhB,EAAsB,UAAtB,EAAkCN,uBAAlC,CAAlB,CAJwD,CAMxD;;IACAO,2BAA2B,CAC1BV,gBAAgB,CAAC,iCAAD,CADU,EAE1BQ,SAF0B,CAA3B;IAKAE,2BAA2B,CAC1BV,gBAAgB,CAAC,8EAAD,CADU,EAE1BQ,SAF0B,CAA3B;IAKA,IAAMG,kBAAkB,GAAG,IAAIF,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCN,uBAApC,CAA3B;IACAO,2BAA2B,CAC1BV,gBAAgB,CAAC,mCAAD,CADU,EAE1BW,kBAF0B,CAA3B;IAKA,IAAMC,kBAAkB,GAAG,IAAIH,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCN,uBAApC,CAA3B;IACAO,2BAA2B,CAC1BV,gBAAgB,CAAC,oCAAD,CADU,EAE1BY,kBAF0B,CAA3B;IAKA,IAAMC,kBAAkB,GAAG,IAAIJ,uBAAJ,CAAgB,GAAhB,EAAqB,aAArB,EAAoCN,uBAApC,CAA3B;IACAO,2BAA2B,CAC1BV,gBAAgB,CAAC,uCAAD,CADU,EAE1Ba,kBAF0B,CAA3B,CA9BwD,CAmCxD;;IACAH,2BAA2B,CAC1BV,gBAAgB,CAAC,oCAAD,EAAuC,IAAvC,CADU,EAE1BQ,SAF0B,CAA3B;IAIAE,2BAA2B,CAC1BV,gBAAgB,CAAC,kDAAD,EAAqD,IAArD,CADU,EAE1BQ,SAF0B,CAA3B;IAIAE,2BAA2B,CAC1BV,gBAAgB,CAAC,+BAAD,EAAkC,IAAlC,CADU,EAE1BQ,SAF0B,CAA3B;IAIAE,2BAA2B,CAC1BV,gBAAgB,CAAC,uCAAD,EAA0C,IAA1C,CADU,EAE1BQ,SAF0B,CAA3B;IAIAE,2BAA2B,CAC1BV,gBAAgB,CAAC,kCAAD,EAAqC,IAArC,CADU,EAE1BQ,SAF0B,CAA3B,CApDwD,CAyDxD;;IACAE,2BAA2B,CAC1BV,gBAAgB,CAAC,yCAAD,EAA4C,IAA5C,CADU,EAE1BQ,SAF0B,CAA3B,CA1DwD,CA+DxD;IACA;IACA;IACA;IACA;IACA;IAEA;;IACAM,mCAAmC,CAAC,8BAAD,CAAnC;IACAA,mCAAmC,CAAC,+BAAD,CAAnC;IACAA,mCAAmC,CAAC,gCAAD,CAAnC;IACAA,mCAAmC,CAAC,kCAAD,CAAnC;IACAA,mCAAmC,CAAC,kCAAD,CAAnC;IACAA,mCAAmC,CAAC,+BAAD,CAAnC;IACAA,mCAAmC,CAAC,oCAAD,CAAnC;IACAA,mCAAmC,CAAC,mCAAD,CAAnC;IACAA,mCAAmC,CAAC,mCAAD,CAAnC;EACA,CAhFC,CAAF;AAiFA,CAlFO,CAAR;;AAoFA,SAASA,mCAAT,CAA6CC,MAA7C,EAAqD;EACpDC,MAAM,CAAChB,gBAAgB,CAACe,MAAD,CAAjB,CAAN,CAAiCE,EAAjC,CAAoCC,EAApC,CAAuCC,SAAvC;AACA;;AAED,SAAST,2BAAT,CAAqCU,YAArC,EAAmDC,YAAnD,EAAiE;EAChE,IAAI,CAACD,YAAD,IAAiB,CAACC,YAAtB,EAAoC;IACnC,OAAO,KAAP;EACA;;EACD,OAAOD,YAAY,CAACE,MAAb,KAAwBD,YAAY,CAACC,MAArC,IACNF,YAAY,CAACG,GAAb,KAAqBF,YAAY,CAACE,GADnC;AAEA"}