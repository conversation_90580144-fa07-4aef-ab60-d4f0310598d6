"""
System Settings Service
======================
خدمة إعدادات النظام للوحة الإدارة
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
import json

import asyncpg
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class SettingType(Enum):
    """أنواع الإعدادات"""
    STRING = "string"
    INTEGER = "integer"
    DECIMAL = "decimal"
    BOOLEAN = "boolean"
    JSON = "json"
    LIST = "list"


class SettingCategory(Enum):
    """فئات الإعدادات"""
    GENERAL = "general"
    FINANCIAL = "financial"
    SECURITY = "security"
    NOTIFICATIONS = "notifications"
    LIMITS = "limits"
    FEES = "fees"
    COMPLIANCE = "compliance"
    INTEGRATIONS = "integrations"


@dataclass
class SystemSetting:
    """إعداد النظام"""
    key: str
    value: Any
    setting_type: SettingType
    category: SettingCategory
    description: str
    is_public: bool
    is_encrypted: bool
    validation_rules: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    updated_by: str


class SystemSettings:
    """خدمة إعدادات النظام"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Settings cache
        self.settings_cache = {}
        self.cache_duration = 600  # 10 minutes
        
        # Default settings
        self.default_settings = {
            # General Settings
            'system.name': {
                'value': 'WS Transfir',
                'type': SettingType.STRING,
                'category': SettingCategory.GENERAL,
                'description': 'اسم النظام',
                'is_public': True
            },
            'system.version': {
                'value': '1.0.0',
                'type': SettingType.STRING,
                'category': SettingCategory.GENERAL,
                'description': 'إصدار النظام',
                'is_public': True
            },
            'system.maintenance_mode': {
                'value': False,
                'type': SettingType.BOOLEAN,
                'category': SettingCategory.GENERAL,
                'description': 'وضع الصيانة',
                'is_public': True
            },
            
            # Financial Settings
            'financial.default_currency': {
                'value': 'SAR',
                'type': SettingType.STRING,
                'category': SettingCategory.FINANCIAL,
                'description': 'العملة الافتراضية',
                'is_public': True
            },
            'financial.min_transaction_amount': {
                'value': Decimal('10.00'),
                'type': SettingType.DECIMAL,
                'category': SettingCategory.FINANCIAL,
                'description': 'الحد الأدنى لمبلغ المعاملة',
                'is_public': True
            },
            'financial.max_transaction_amount': {
                'value': Decimal('100000.00'),
                'type': SettingType.DECIMAL,
                'category': SettingCategory.FINANCIAL,
                'description': 'الحد الأقصى لمبلغ المعاملة',
                'is_public': True
            },
            
            # Security Settings
            'security.password_min_length': {
                'value': 8,
                'type': SettingType.INTEGER,
                'category': SettingCategory.SECURITY,
                'description': 'الحد الأدنى لطول كلمة المرور',
                'is_public': True
            },
            'security.max_login_attempts': {
                'value': 5,
                'type': SettingType.INTEGER,
                'category': SettingCategory.SECURITY,
                'description': 'الحد الأقصى لمحاولات تسجيل الدخول',
                'is_public': False
            },
            'security.session_timeout_minutes': {
                'value': 30,
                'type': SettingType.INTEGER,
                'category': SettingCategory.SECURITY,
                'description': 'مهلة انتهاء الجلسة بالدقائق',
                'is_public': False
            },
            
            # Fee Settings
            'fees.transfer_fee_rate': {
                'value': Decimal('0.0050'),
                'type': SettingType.DECIMAL,
                'category': SettingCategory.FEES,
                'description': 'معدل رسوم التحويل',
                'is_public': True
            },
            'fees.min_transfer_fee': {
                'value': Decimal('5.00'),
                'type': SettingType.DECIMAL,
                'category': SettingCategory.FEES,
                'description': 'الحد الأدنى لرسوم التحويل',
                'is_public': True
            },
            'fees.max_transfer_fee': {
                'value': Decimal('100.00'),
                'type': SettingType.DECIMAL,
                'category': SettingCategory.FEES,
                'description': 'الحد الأقصى لرسوم التحويل',
                'is_public': True
            },
            
            # Limits Settings
            'limits.daily_transaction_limit': {
                'value': Decimal('50000.00'),
                'type': SettingType.DECIMAL,
                'category': SettingCategory.LIMITS,
                'description': 'الحد اليومي للمعاملات',
                'is_public': True
            },
            'limits.monthly_transaction_limit': {
                'value': Decimal('500000.00'),
                'type': SettingType.DECIMAL,
                'category': SettingCategory.LIMITS,
                'description': 'الحد الشهري للمعاملات',
                'is_public': True
            },
            
            # Notification Settings
            'notifications.email_enabled': {
                'value': True,
                'type': SettingType.BOOLEAN,
                'category': SettingCategory.NOTIFICATIONS,
                'description': 'تفعيل الإشعارات عبر البريد الإلكتروني',
                'is_public': False
            },
            'notifications.sms_enabled': {
                'value': True,
                'type': SettingType.BOOLEAN,
                'category': SettingCategory.NOTIFICATIONS,
                'description': 'تفعيل الإشعارات عبر الرسائل النصية',
                'is_public': False
            }
        }
    
    async def get_setting(self, key: str, default: Any = None) -> Any:
        """الحصول على إعداد واحد"""
        try:
            # Check cache first
            if self._is_cached(key):
                return self.settings_cache[key]['value']
            
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT value, setting_type, is_encrypted
                    FROM system_settings 
                    WHERE key = $1 AND is_active = true
                """
                
                row = await conn.fetchrow(query, key)
                
                if row:
                    value = self._deserialize_value(row['value'], row['setting_type'])
                    
                    # Decrypt if needed
                    if row['is_encrypted']:
                        value = self._decrypt_value(value)
                    
                    # Cache the value
                    self._cache_setting(key, value)
                    
                    return value
                
                # Return default if not found
                if key in self.default_settings:
                    return self.default_settings[key]['value']
                
                return default
                
        except Exception as e:
            logger.error(f"❌ Failed to get setting {key}: {e}")
            return default
    
    async def set_setting(
        self,
        key: str,
        value: Any,
        updated_by: str,
        description: str = None
    ) -> bool:
        """تعيين إعداد"""
        try:
            logger.info(f"⚙️ Setting {key} = {value}")
            
            # Get setting info
            setting_info = self.default_settings.get(key)
            if not setting_info:
                raise ValueError(f"Unknown setting key: {key}")
            
            # Validate value
            validated_value = self._validate_setting_value(key, value, setting_info)
            
            # Serialize value
            serialized_value = self._serialize_value(validated_value, setting_info['type'])
            
            # Encrypt if needed
            is_encrypted = setting_info.get('is_encrypted', False)
            if is_encrypted:
                serialized_value = self._encrypt_value(serialized_value)
            
            async with self.db_connection.get_connection() as conn:
                # Upsert setting
                upsert_query = """
                    INSERT INTO system_settings (
                        key, value, setting_type, category, description,
                        is_public, is_encrypted, updated_by
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ON CONFLICT (key) DO UPDATE SET
                        value = EXCLUDED.value,
                        updated_at = CURRENT_TIMESTAMP,
                        updated_by = EXCLUDED.updated_by
                    RETURNING key
                """
                
                result = await conn.fetchval(
                    upsert_query,
                    key,
                    serialized_value,
                    setting_info['type'].value,
                    setting_info['category'].value,
                    description or setting_info['description'],
                    setting_info['is_public'],
                    is_encrypted,
                    updated_by
                )
                
                if result:
                    # Update cache
                    self._cache_setting(key, validated_value)
                    
                    # Log the change
                    await self._log_setting_change(conn, key, value, updated_by)
                    
                    logger.info(f"✅ Setting {key} updated successfully")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to set setting {key}: {e}")
            return False
    
    async def get_settings_by_category(self, category: SettingCategory) -> Dict[str, Any]:
        """الحصول على الإعدادات حسب الفئة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT key, value, setting_type, description, is_public, is_encrypted
                    FROM system_settings 
                    WHERE category = $1 AND is_active = true
                    ORDER BY key
                """
                
                rows = await conn.fetch(query, category.value)
                
                settings = {}
                for row in rows:
                    value = self._deserialize_value(row['value'], row['setting_type'])
                    
                    # Decrypt if needed
                    if row['is_encrypted']:
                        value = self._decrypt_value(value)
                    
                    settings[row['key']] = {
                        'value': value,
                        'description': row['description'],
                        'is_public': row['is_public']
                    }
                
                return settings
                
        except Exception as e:
            logger.error(f"❌ Failed to get settings for category {category.value}: {e}")
            return {}
    
    async def get_public_settings(self) -> Dict[str, Any]:
        """الحصول على الإعدادات العامة فقط"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT key, value, setting_type, description
                    FROM system_settings 
                    WHERE is_public = true AND is_active = true
                    ORDER BY category, key
                """
                
                rows = await conn.fetch(query)
                
                settings = {}
                for row in rows:
                    value = self._deserialize_value(row['value'], row['setting_type'])
                    settings[row['key']] = {
                        'value': value,
                        'description': row['description']
                    }
                
                return settings
                
        except Exception as e:
            logger.error(f"❌ Failed to get public settings: {e}")
            return {}
    
    async def bulk_update_settings(
        self,
        settings: Dict[str, Any],
        updated_by: str
    ) -> Dict[str, bool]:
        """تحديث متعدد للإعدادات"""
        try:
            logger.info(f"⚙️ Bulk updating {len(settings)} settings")
            
            results = {}
            
            async with self.db_connection.get_connection() as conn:
                async with conn.transaction():
                    for key, value in settings.items():
                        try:
                            success = await self.set_setting(key, value, updated_by)
                            results[key] = success
                        except Exception as e:
                            logger.error(f"❌ Failed to update setting {key}: {e}")
                            results[key] = False
            
            successful_updates = sum(1 for success in results.values() if success)
            logger.info(f"✅ Successfully updated {successful_updates}/{len(settings)} settings")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to bulk update settings: {e}")
            return {key: False for key in settings.keys()}
    
    async def reset_setting_to_default(self, key: str, updated_by: str) -> bool:
        """إعادة تعيين إعداد للقيمة الافتراضية"""
        try:
            if key not in self.default_settings:
                raise ValueError(f"No default value for setting: {key}")
            
            default_value = self.default_settings[key]['value']
            return await self.set_setting(key, default_value, updated_by)
            
        except Exception as e:
            logger.error(f"❌ Failed to reset setting {key}: {e}")
            return False
    
    async def get_setting_history(self, key: str, limit: int = 50) -> List[Dict[str, Any]]:
        """الحصول على تاريخ تغييرات الإعداد"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT old_value, new_value, updated_by, updated_at, notes
                    FROM system_settings_history 
                    WHERE setting_key = $1
                    ORDER BY updated_at DESC
                    LIMIT $2
                """
                
                rows = await conn.fetch(query, key, limit)
                
                history = []
                for row in rows:
                    history.append({
                        'old_value': row['old_value'],
                        'new_value': row['new_value'],
                        'updated_by': row['updated_by'],
                        'updated_at': row['updated_at'].isoformat(),
                        'notes': row['notes']
                    })
                
                return history
                
        except Exception as e:
            logger.error(f"❌ Failed to get setting history for {key}: {e}")
            return []
    
    async def validate_settings(self) -> Dict[str, Any]:
        """التحقق من صحة جميع الإعدادات"""
        try:
            logger.info("🔍 Validating all system settings")
            
            validation_results = {
                'valid_settings': [],
                'invalid_settings': [],
                'missing_settings': [],
                'warnings': []
            }
            
            async with self.db_connection.get_connection() as conn:
                # Get all current settings
                query = """
                    SELECT key, value, setting_type
                    FROM system_settings 
                    WHERE is_active = true
                """
                
                rows = await conn.fetch(query)
                current_settings = {row['key']: row for row in rows}
                
                # Check each default setting
                for key, default_info in self.default_settings.items():
                    if key in current_settings:
                        # Validate existing setting
                        try:
                            current_value = self._deserialize_value(
                                current_settings[key]['value'],
                                current_settings[key]['setting_type']
                            )
                            self._validate_setting_value(key, current_value, default_info)
                            validation_results['valid_settings'].append(key)
                        except Exception as e:
                            validation_results['invalid_settings'].append({
                                'key': key,
                                'error': str(e)
                            })
                    else:
                        # Missing setting
                        validation_results['missing_settings'].append(key)
                
                # Check for orphaned settings
                for key in current_settings:
                    if key not in self.default_settings:
                        validation_results['warnings'].append({
                            'key': key,
                            'warning': 'Setting not in default configuration'
                        })
            
            logger.info(f"✅ Settings validation completed")
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ Failed to validate settings: {e}")
            return {'error': str(e)}
    
    async def initialize_default_settings(self, updated_by: str = 'system') -> bool:
        """تهيئة الإعدادات الافتراضية"""
        try:
            logger.info("🔧 Initializing default settings")
            
            success_count = 0
            
            for key, setting_info in self.default_settings.items():
                # Check if setting already exists
                current_value = await self.get_setting(key)
                
                if current_value is None:
                    # Setting doesn't exist, create it
                    success = await self.set_setting(
                        key,
                        setting_info['value'],
                        updated_by,
                        setting_info['description']
                    )
                    
                    if success:
                        success_count += 1
            
            logger.info(f"✅ Initialized {success_count} default settings")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize default settings: {e}")
            return False
    
    # Helper methods
    def _is_cached(self, key: str) -> bool:
        """فحص إذا كان الإعداد محفوظ في الذاكرة المؤقتة"""
        if key not in self.settings_cache:
            return False
        
        cached_setting = self.settings_cache[key]
        age = (datetime.now() - cached_setting['timestamp']).seconds
        
        return age < self.cache_duration
    
    def _cache_setting(self, key: str, value: Any):
        """حفظ الإعداد في الذاكرة المؤقتة"""
        self.settings_cache[key] = {
            'value': value,
            'timestamp': datetime.now()
        }
    
    def _serialize_value(self, value: Any, setting_type: SettingType) -> str:
        """تسلسل القيمة للحفظ في قاعدة البيانات"""
        if setting_type == SettingType.JSON or setting_type == SettingType.LIST:
            return json.dumps(value, default=str)
        elif setting_type == SettingType.DECIMAL:
            return str(value)
        elif setting_type == SettingType.BOOLEAN:
            return str(value).lower()
        else:
            return str(value)
    
    def _deserialize_value(self, value: str, setting_type: str) -> Any:
        """إلغاء تسلسل القيمة من قاعدة البيانات"""
        setting_type_enum = SettingType(setting_type)
        
        if setting_type_enum == SettingType.INTEGER:
            return int(value)
        elif setting_type_enum == SettingType.DECIMAL:
            return Decimal(value)
        elif setting_type_enum == SettingType.BOOLEAN:
            return value.lower() in ('true', '1', 'yes', 'on')
        elif setting_type_enum == SettingType.JSON or setting_type_enum == SettingType.LIST:
            return json.loads(value)
        else:
            return value
    
    def _validate_setting_value(self, key: str, value: Any, setting_info: Dict[str, Any]) -> Any:
        """التحقق من صحة قيمة الإعداد"""
        setting_type = setting_info['type']
        
        # Type validation
        if setting_type == SettingType.INTEGER and not isinstance(value, int):
            try:
                value = int(value)
            except (ValueError, TypeError):
                raise ValueError(f"Setting {key} must be an integer")
        
        elif setting_type == SettingType.DECIMAL and not isinstance(value, (Decimal, float, int)):
            try:
                value = Decimal(str(value))
            except (ValueError, TypeError):
                raise ValueError(f"Setting {key} must be a decimal number")
        
        elif setting_type == SettingType.BOOLEAN and not isinstance(value, bool):
            if isinstance(value, str):
                value = value.lower() in ('true', '1', 'yes', 'on')
            else:
                raise ValueError(f"Setting {key} must be a boolean")
        
        # Additional validation rules can be added here
        validation_rules = setting_info.get('validation_rules', {})
        
        if 'min_value' in validation_rules and value < validation_rules['min_value']:
            raise ValueError(f"Setting {key} must be at least {validation_rules['min_value']}")
        
        if 'max_value' in validation_rules and value > validation_rules['max_value']:
            raise ValueError(f"Setting {key} must be at most {validation_rules['max_value']}")
        
        return value
    
    def _encrypt_value(self, value: str) -> str:
        """تشفير القيمة"""
        # Implementation would use proper encryption
        return value  # Placeholder
    
    def _decrypt_value(self, value: str) -> str:
        """فك تشفير القيمة"""
        # Implementation would use proper decryption
        return value  # Placeholder
    
    async def _log_setting_change(
        self,
        conn,
        key: str,
        new_value: Any,
        updated_by: str
    ):
        """تسجيل تغيير الإعداد"""
        try:
            # Get old value
            old_value_query = """
                SELECT value FROM system_settings WHERE key = $1
            """
            old_value = await conn.fetchval(old_value_query, key)
            
            # Insert history record
            history_query = """
                INSERT INTO system_settings_history (
                    setting_key, old_value, new_value, updated_by
                ) VALUES ($1, $2, $3, $4)
            """
            
            await conn.execute(
                history_query,
                key,
                old_value,
                str(new_value),
                updated_by
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to log setting change: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الإعدادات"""
        try:
            async with self.db_connection.get_connection() as conn:
                stats_query = """
                    SELECT 
                        COUNT(*) as total_settings,
                        COUNT(CASE WHEN is_public THEN 1 END) as public_settings,
                        COUNT(CASE WHEN is_encrypted THEN 1 END) as encrypted_settings,
                        COUNT(DISTINCT category) as categories
                    FROM system_settings 
                    WHERE is_active = true
                """
                
                stats = await conn.fetchrow(stats_query)
                
                return {
                    'total_settings': stats['total_settings'],
                    'public_settings': stats['public_settings'],
                    'encrypted_settings': stats['encrypted_settings'],
                    'categories': stats['categories'],
                    'cache_size': len(self.settings_cache),
                    'default_settings': len(self.default_settings)
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get settings statistics: {e}")
            return {'error': str(e)}
