'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  ArrowsRightLeftIcon,
  WalletIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Card, { CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// تسجيل مكونات Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

// بيانات وهمية للإحصائيات
const mockStats = {
  totalBalance: 15750.50,
  totalSent: 45230.75,
  totalReceived: 32180.25,
  pendingTransfers: 3,
  completedTransfers: 127,
  monthlyChange: 12.5,
};

// بيانات وهمية للمعاملات الأخيرة
const mockRecentTransactions = [
  {
    id: '1',
    type: 'sent',
    amount: 500,
    currency: 'SAR',
    recipient: 'أحمد محمد',
    status: 'completed',
    date: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    type: 'received',
    amount: 750,
    currency: 'SAR',
    sender: 'فاطمة علي',
    status: 'completed',
    date: '2024-01-14T15:45:00Z',
  },
  {
    id: '3',
    type: 'sent',
    amount: 1200,
    currency: 'SAR',
    recipient: 'محمد خالد',
    status: 'pending',
    date: '2024-01-14T09:20:00Z',
  },
];

export default function DashboardPage() {
  const [timeRange, setTimeRange] = useState('7d');
  const { state: authState } = useAuth();
  const { t, formatCurrency, formatDate, isRTL } = useLanguage();

  // بيانات الرسم البياني للخط
  const lineChartData = {
    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
    datasets: [
      {
        label: 'المرسل',
        data: [12000, 19000, 15000, 25000, 22000, 30000],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'المستلم',
        data: [8000, 15000, 12000, 18000, 16000, 22000],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
      },
    ],
  };

  // بيانات الرسم البياني الدائري
  const doughnutChartData = {
    labels: ['مكتملة', 'قيد المعالجة', 'ملغية'],
    datasets: [
      {
        data: [85, 12, 3],
        backgroundColor: [
          'rgb(34, 197, 94)',
          'rgb(251, 191, 36)',
          'rgb(239, 68, 68)',
        ],
        borderWidth: 0,
      },
    ],
  };

  // خيارات الرسم البياني
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* ترحيب */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          مرحباً، {authState.user?.firstName}! 👋
        </h1>
        <p className="mt-1 text-gray-600 dark:text-gray-400">
          إليك نظرة عامة على نشاط حسابك
        </p>
      </motion.div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* إجمالي الرصيد */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  إجمالي الرصيد
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(mockStats.totalBalance, 'SAR')}
                </p>
              </div>
              <div className="p-3 bg-primary-100 dark:bg-primary-900/20 rounded-full">
                <WalletIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <ArrowUpIcon className="h-4 w-4 text-success-500" />
              <span className="text-sm text-success-600 dark:text-success-400 ml-1">
                +{mockStats.monthlyChange}%
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                من الشهر الماضي
              </span>
            </div>
          </Card>
        </motion.div>

        {/* إجمالي المرسل */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  إجمالي المرسل
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(mockStats.totalSent, 'SAR')}
                </p>
              </div>
              <div className="p-3 bg-error-100 dark:bg-error-900/20 rounded-full">
                <ArrowUpIcon className="h-6 w-6 text-error-600 dark:text-error-400" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {mockStats.completedTransfers} معاملة مكتملة
              </span>
            </div>
          </Card>
        </motion.div>

        {/* إجمالي المستلم */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  إجمالي المستلم
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(mockStats.totalReceived, 'SAR')}
                </p>
              </div>
              <div className="p-3 bg-success-100 dark:bg-success-900/20 rounded-full">
                <ArrowDownIcon className="h-6 w-6 text-success-600 dark:text-success-400" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                آخر تحويل منذ يومين
              </span>
            </div>
          </Card>
        </motion.div>

        {/* المعاملات المعلقة */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  معاملات معلقة
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {mockStats.pendingTransfers}
                </p>
              </div>
              <div className="p-3 bg-warning-100 dark:bg-warning-900/20 rounded-full">
                <ClockIcon className="h-6 w-6 text-warning-600 dark:text-warning-400" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                تحتاج للمراجعة
              </span>
            </div>
          </Card>
        </motion.div>
      </div>

      {/* الإجراءات السريعة */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="mb-8"
      >
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            الإجراءات السريعة
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2"
              onClick={() => window.location.href = '/dashboard/transfers/send'}
            >
              <ArrowsRightLeftIcon className="h-6 w-6" />
              <span>إرسال حوالة</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2"
              onClick={() => window.location.href = '/dashboard/wallet/top-up'}
            >
              <PlusIcon className="h-6 w-6" />
              <span>شحن المحفظة</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2"
              onClick={() => window.location.href = '/dashboard/transfers/track'}
            >
              <ChartBarIcon className="h-6 w-6" />
              <span>تتبع الحوالة</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-20 flex-col space-y-2"
              onClick={() => window.location.href = '/dashboard/wallet/bills'}
            >
              <WalletIcon className="h-6 w-6" />
              <span>دفع الفواتير</span>
            </Button>
          </div>
        </Card>
      </motion.div>

      {/* الرسوم البيانية والمعاملات */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* الرسم البياني للخط */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="lg:col-span-2"
        >
          <Card className="p-6">
            <CardHeader>
              <CardTitle>نشاط التحويلات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <Line data={lineChartData} options={chartOptions} />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* الرسم البياني الدائري */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card className="p-6">
            <CardHeader>
              <CardTitle>حالة المعاملات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <Doughnut 
                  data={doughnutChartData} 
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom',
                        labels: {
                          padding: 20,
                          usePointStyle: true,
                        },
                      },
                    },
                  }} 
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* المعاملات الأخيرة */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              المعاملات الأخيرة
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.location.href = '/dashboard/transfers/history'}
            >
              عرض الكل
            </Button>
          </div>

          <div className="space-y-4">
            {mockRecentTransactions.map((transaction, index) => (
              <motion.div
                key={transaction.id}
                initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 + index * 0.1 }}
                className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
              >
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className={`p-2 rounded-full ${
                    transaction.type === 'sent' 
                      ? 'bg-error-100 dark:bg-error-900/20' 
                      : 'bg-success-100 dark:bg-success-900/20'
                  }`}>
                    {transaction.type === 'sent' ? (
                      <ArrowUpIcon className={`h-5 w-5 ${
                        transaction.type === 'sent' 
                          ? 'text-error-600 dark:text-error-400' 
                          : 'text-success-600 dark:text-success-400'
                      }`} />
                    ) : (
                      <ArrowDownIcon className="h-5 w-5 text-success-600 dark:text-success-400" />
                    )}
                  </div>
                  
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {transaction.type === 'sent' 
                        ? `إلى ${transaction.recipient}` 
                        : `من ${transaction.sender}`}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(transaction.date)}
                    </p>
                  </div>
                </div>

                <div className="text-right rtl:text-left">
                  <p className={`font-semibold ${
                    transaction.type === 'sent' 
                      ? 'text-error-600 dark:text-error-400' 
                      : 'text-success-600 dark:text-success-400'
                  }`}>
                    {transaction.type === 'sent' ? '-' : '+'}
                    {formatCurrency(transaction.amount, transaction.currency)}
                  </p>
                  <div className="flex items-center justify-end space-x-1 rtl:space-x-reverse">
                    {transaction.status === 'completed' ? (
                      <CheckCircleIcon className="h-4 w-4 text-success-500" />
                    ) : (
                      <ExclamationTriangleIcon className="h-4 w-4 text-warning-500" />
                    )}
                    <span className={`text-xs ${
                      transaction.status === 'completed' 
                        ? 'text-success-600 dark:text-success-400' 
                        : 'text-warning-600 dark:text-warning-400'
                    }`}>
                      {transaction.status === 'completed' ? 'مكتمل' : 'معلق'}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      </motion.div>
    </div>
  );
}
