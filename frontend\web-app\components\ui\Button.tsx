'use client';

import React, { forwardRef, ButtonHTMLAttributes } from 'react';
import { motion, MotionProps } from 'framer-motion';
import { clsx } from 'clsx';

// أنواع الأزرار
export type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'outline' 
  | 'ghost' 
  | 'link';

export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// واجهة خصائص الزر
export interface ButtonProps 
  extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'size'>,
    Omit<MotionProps, 'children'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  rounded?: boolean;
  children: React.ReactNode;
}

// أنماط الأزرار
const buttonVariants = {
  primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm',
  secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600',
  success: 'bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm',
  warning: 'bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500 shadow-sm',
  error: 'bg-error-600 text-white hover:bg-error-700 focus:ring-error-500 shadow-sm',
  outline: 'border-2 border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800',
  ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800',
  link: 'text-primary-600 hover:text-primary-700 underline-offset-4 hover:underline focus:ring-primary-500',
};

const buttonSizes = {
  xs: 'px-2.5 py-1.5 text-xs',
  sm: 'px-3 py-2 text-sm',
  md: 'px-4 py-2 text-sm',
  lg: 'px-6 py-3 text-base',
  xl: 'px-8 py-4 text-lg',
};

// مكون الزر
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      isLoading = false,
      loadingText,
      leftIcon,
      rightIcon,
      fullWidth = false,
      rounded = false,
      disabled,
      className,
      children,
      ...props
    },
    ref
  ) => {
    // تحديد الأنماط
    const baseClasses = clsx(
      // الأنماط الأساسية
      'inline-flex items-center justify-center font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      
      // الحجم
      buttonSizes[size],
      
      // النوع
      variant !== 'link' && buttonVariants[variant],
      variant === 'link' && buttonVariants.link,
      
      // الشكل
      rounded ? 'rounded-full' : 'rounded-lg',
      
      // العرض الكامل
      fullWidth && 'w-full',
      
      // حالة التحميل
      isLoading && 'cursor-wait',
      
      // الفئات المخصصة
      className
    );

    // محتوى الزر
    const buttonContent = (
      <>
        {/* أيقونة اليسار أو مؤشر التحميل */}
        {isLoading ? (
          <svg
            className={clsx(
              'animate-spin',
              size === 'xs' ? 'w-3 h-3' : 
              size === 'sm' ? 'w-4 h-4' : 
              size === 'lg' ? 'w-5 h-5' : 
              size === 'xl' ? 'w-6 h-6' : 'w-4 h-4',
              (children || loadingText) && 'mr-2'
            )}
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        ) : leftIcon ? (
          <span className={clsx('flex-shrink-0', children && 'mr-2')}>
            {leftIcon}
          </span>
        ) : null}

        {/* النص */}
        <span className="flex-1">
          {isLoading && loadingText ? loadingText : children}
        </span>

        {/* أيقونة اليمين */}
        {!isLoading && rightIcon && (
          <span className={clsx('flex-shrink-0', children && 'ml-2')}>
            {rightIcon}
          </span>
        )}
      </>
    );

    return (
      <motion.button
        ref={ref}
        className={baseClasses}
        disabled={disabled || isLoading}
        whileHover={{ scale: disabled || isLoading ? 1 : 1.02 }}
        whileTap={{ scale: disabled || isLoading ? 1 : 0.98 }}
        transition={{ type: 'spring', stiffness: 400, damping: 17 }}
        {...props}
      >
        {buttonContent}
      </motion.button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
