/**
 * WS Transfir XAMPP Offline Server
 * خادم نظام WS Transfir المحلي بالكامل على XAMPP
 */

const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();

// Offline XAMPP Configuration
const config = {
  port: process.env.PORT || 8080,
  host: 'localhost', // Offline only
  environment: 'xampp-offline',
  xamppPath: process.cwd(),
  offlineMode: true,
  localDatabase: true,
};

console.log('🔥 تشغيل نظام WS Transfir Offline على XAMPP');
console.log('==========================================');
console.log(`🌐 XAMPP Path: ${config.xamppPath}`);
console.log(`🔧 Port: ${config.port}`);
console.log(`🏠 Host: ${config.host}`);
console.log(`📴 Mode: OFFLINE`);
console.log(`💾 Database: LOCAL SIMULATION`);
console.log('');

// Offline-only middleware (no external dependencies)
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Serve static files locally
app.use(express.static('.'));
app.use('/assets', express.static(path.join(__dirname, 'assets')));
app.use('/css', express.static(path.join(__dirname, 'css')));
app.use('/js', express.static(path.join(__dirname, 'js')));
app.use('/images', express.static(path.join(__dirname, 'images')));

// Local request logging
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  
  console.log(`[${timestamp}] XAMPP OFFLINE: ${method} ${url}`);
  
  req.requestId = Date.now().toString();
  res.setHeader('X-Powered-By', 'WS-Transfir-XAMPP-Offline');
  res.setHeader('X-Request-ID', req.requestId);
  res.setHeader('X-Mode', 'OFFLINE');
  
  next();
});

// Main offline route
app.get('/', (req, res) => {
  try {
    // Check for offline frontend file
    if (fs.existsSync('xampp-offline-frontend.html')) {
      const html = fs.readFileSync('xampp-offline-frontend.html', 'utf8');
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
      res.send(html);
    } else {
      // Fallback offline page
      res.send(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>WS Transfir - XAMPP Offline System</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
                    margin: 0; padding: 20px; color: #333;
                }
                .container { 
                    max-width: 900px; margin: 0 auto; 
                    background: white; padding: 40px; 
                    border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.2);
                }
                h1 { color: #ff6b35; text-align: center; margin-bottom: 30px; font-size: 2.5em; }
                .offline-badge { 
                    background: #28a745; color: white; 
                    padding: 15px; border-radius: 10px; 
                    text-align: center; margin: 20px 0; font-size: 1.2em;
                    animation: pulse 2s infinite;
                }
                @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }
                .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
                .link { 
                    background: #fff3e0; border: 2px solid #ff6b35; 
                    padding: 20px; border-radius: 15px; text-align: center; 
                    text-decoration: none; color: #333; transition: all 0.3s;
                    font-weight: 600;
                }
                .link:hover { 
                    background: #ff6b35; color: white; 
                    transform: translateY(-5px); box-shadow: 0 10px 25px rgba(255,107,53,0.3);
                }
                .info { background: #e8f5e8; padding: 20px; border-radius: 15px; margin: 20px 0; }
                .feature { background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔥 WS Transfir - XAMPP Offline</h1>
                
                <div class="offline-badge">
                    📴 النظام يعمل في الوضع المحلي (Offline) بالكامل!
                </div>
                
                <div class="info">
                    <h3>📋 معلومات النظام المحلي:</h3>
                    <p><strong>🌐 المنفذ:</strong> ${config.port}</p>
                    <p><strong>🏠 المضيف:</strong> ${config.host}</p>
                    <p><strong>📁 المسار:</strong> ${config.xamppPath}</p>
                    <p><strong>📴 الوضع:</strong> Offline Complete</p>
                    <p><strong>💾 قاعدة البيانات:</strong> محاكاة محلية</p>
                    <p><strong>⏰ الوقت:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                </div>
                
                <div class="links">
                    <a href="/api/health" class="link">
                        <h4>📊 فحص الصحة</h4>
                        <p>فحص حالة النظام المحلي</p>
                    </a>
                    <a href="/api/status" class="link">
                        <h4>📈 حالة النظام</h4>
                        <p>معلومات مفصلة محلية</p>
                    </a>
                    <a href="/api/auth/login" class="link">
                        <h4>🔐 نظام المصادقة</h4>
                        <p>تسجيل الدخول المحلي</p>
                    </a>
                    <a href="/api/transfers" class="link">
                        <h4>💸 التحويلات</h4>
                        <p>إدارة التحويلات المحلية</p>
                    </a>
                </div>
                
                <div class="feature">
                    <h3>🔥 ميزات النظام المحلي:</h3>
                    <ul>
                        <li>✅ يعمل بدون إنترنت بالكامل</li>
                        <li>✅ قاعدة بيانات محاكاة محلية</li>
                        <li>✅ جميع الملفات محفوظة محلياً</li>
                        <li>✅ أمان محلي متقدم</li>
                        <li>✅ سرعة عالية (لا توجد طلبات خارجية)</li>
                        <li>✅ خصوصية كاملة</li>
                    </ul>
                </div>
                
                <div class="info">
                    <h3>🔐 بيانات الدخول المحلية:</h3>
                    <p><strong>👨‍💼 مدير:</strong> <EMAIL> / admin123</p>
                    <p><strong>👤 مستخدم:</strong> <EMAIL> / password123</p>
                </div>
            </div>
        </body>
        </html>
      `);
    }
  } catch (error) {
    res.status(500).send(`
      <h1>خطأ في تحميل النظام المحلي</h1>
      <p>Error: ${error.message}</p>
      <p><a href="/api/health">جرب فحص الصحة المحلي</a></p>
    `);
  }
});

// Offline health check endpoint
app.get('/api/health', (req, res) => {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  res.json({
    status: 'OK',
    message: 'WS Transfir XAMPP Offline System is running successfully',
    timestamp: new Date().toISOString(),
    version: '1.0.0-xampp-offline',
    environment: config.environment,
    mode: 'OFFLINE',
    xampp: {
      path: config.xamppPath,
      port: config.port,
      host: config.host,
      offline: true,
    },
    server: {
      uptime: {
        seconds: Math.floor(uptime),
        human: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
      },
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        usage: `${Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)}%`,
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
      },
    },
    services: {
      api: 'healthy',
      xampp: 'offline-integrated',
      database: 'local-simulation',
      cache: 'local-memory',
      internet: 'not-required',
    },
    features: {
      offline: true,
      localDatabase: true,
      noExternalDependencies: true,
      privacy: 'complete',
      speed: 'maximum',
    },
    requestId: req.requestId,
  });
});

// Offline system status endpoint
app.get('/api/status', (req, res) => {
  res.json({
    online: false,
    offline: true,
    status: 'operational-offline',
    platform: 'XAMPP-Offline',
    services: {
      api: { status: 'up', responseTime: '< 10ms', location: 'local' },
      xampp: { status: 'offline-integrated', responseTime: '< 5ms', location: 'local' },
      database: { status: 'local-simulation', responseTime: '< 1ms', location: 'memory' },
      internet: { status: 'not-required', responseTime: 'N/A', location: 'N/A' },
    },
    performance: {
      responseTime: '< 10ms',
      memoryUsage: 'optimized',
      cpuUsage: 'minimal',
      diskUsage: 'local-only',
    },
    lastUpdated: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Local users database (in-memory for offline)
const localUsers = new Map([
  ['<EMAIL>', {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'مدير',
    lastName: 'النظام المحلي',
    role: 'admin',
    isVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: null,
    location: 'local',
  }],
  ['<EMAIL>', {
    id: '2',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'أحمد',
    lastName: 'محمد المحلي',
    role: 'user',
    isVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    lastLogin: null,
    location: 'local',
  }]
]);

// Offline authentication endpoint
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  const timestamp = new Date().toISOString();
  
  console.log(`[${timestamp}] XAMPP Offline Login attempt: ${email}`);
  
  const user = localUsers.get(email);
  
  if (user && user.password === password) {
    const token = `xampp-offline-jwt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Update last login locally
    user.lastLogin = timestamp;
    
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح في النظام المحلي',
      token,
      expiresIn: 3600,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified,
        lastLogin: timestamp,
        location: 'local',
      },
      platform: 'XAMPP-Offline',
      mode: 'offline',
      timestamp,
      requestId: req.requestId,
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة في النظام المحلي',
      error: 'INVALID_CREDENTIALS_OFFLINE',
      platform: 'XAMPP-Offline',
      mode: 'offline',
      timestamp,
      requestId: req.requestId,
    });
  }
});

// Offline profile endpoint
app.get('/api/profile/me', (req, res) => {
  res.json({
    id: '2',
    firstName: 'أحمد',
    lastName: 'محمد المحلي',
    email: '<EMAIL>',
    phone: '+966501234567',
    isVerified: true,
    platform: 'XAMPP-Offline',
    mode: 'offline',
    completionPercentage: 95,
    address: {
      city: 'الرياض',
      country: 'SA',
      location: 'local',
    },
    preferences: {
      language: 'ar',
      currency: 'SAR',
      theme: 'light',
      notifications: {
        email: false, // Offline mode
        sms: false,   // Offline mode
        push: true,   // Local only
      }
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Offline transfers endpoint
app.get('/api/transfers', (req, res) => {
  const mockOfflineTransfers = [
    {
      id: '1',
      referenceNumber: 'OFFLINE20241225001',
      amount: '1,500.00',
      currency: 'SAR',
      receiverName: 'أحمد محمد',
      receiverCountry: 'مصر',
      status: 'completed',
      createdAt: '2024-12-25T10:30:00Z',
      completedAt: '2024-12-25T14:30:00Z',
      platform: 'XAMPP-Offline',
      mode: 'offline',
      location: 'local',
    },
    {
      id: '2',
      referenceNumber: 'OFFLINE20241224002',
      amount: '750.00',
      currency: 'USD',
      receiverName: 'فاطمة علي',
      receiverCountry: 'الأردن',
      status: 'pending',
      createdAt: '2024-12-24T15:45:00Z',
      platform: 'XAMPP-Offline',
      mode: 'offline',
      location: 'local',
    },
    {
      id: '3',
      referenceNumber: 'OFFLINE20241223003',
      amount: '2,200.00',
      currency: 'SAR',
      receiverName: 'محمد حسن',
      receiverCountry: 'لبنان',
      status: 'processing',
      createdAt: '2024-12-23T09:15:00Z',
      platform: 'XAMPP-Offline',
      mode: 'offline',
      location: 'local',
    }
  ];
  
  res.json({
    success: true,
    data: mockOfflineTransfers,
    platform: 'XAMPP-Offline',
    mode: 'offline',
    location: 'local',
    count: mockOfflineTransfers.length,
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Offline transfer statistics
app.get('/api/transfers/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalTransfers: 18,
      totalAmount: '28,430.50',
      completedTransfers: 15,
      pendingTransfers: 2,
      processingTransfers: 1,
      platform: 'XAMPP-Offline',
      mode: 'offline',
      location: 'local',
      thisMonth: {
        transfers: 6,
        amount: '9,450.00',
        completed: 5,
        pending: 1,
      },
      topCountries: [
        { country: 'مصر', count: 6, amount: '12,230.00' },
        { country: 'الأردن', count: 4, amount: '8,450.00' },
        { country: 'لبنان', count: 3, amount: '5,750.00' },
        { country: 'المغرب', count: 3, amount: '2,000.00' },
      ]
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Offline error handling
app.use((err, req, res, next) => {
  console.error(`[${new Date().toISOString()}] XAMPP Offline Error:`, err);
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'حدث خطأ في النظام المحلي',
    error: 'XAMPP_OFFLINE_SERVER_ERROR',
    platform: 'XAMPP-Offline',
    mode: 'offline',
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Offline 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود في النظام المحلي',
    error: 'NOT_FOUND_OFFLINE',
    path: req.originalUrl,
    platform: 'XAMPP-Offline',
    mode: 'offline',
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
});

// Start offline XAMPP server
const server = app.listen(config.port, config.host, () => {
  console.log('');
  console.log('🔥 ═══════════════════════════════════════════════════════════');
  console.log('🔥 WS TRANSFIR XAMPP OFFLINE SYSTEM - FULLY OPERATIONAL');
  console.log('🔥 ═══════════════════════════════════════════════════════════');
  console.log(`📴 OFFLINE URL: http://${config.host}:${config.port}`);
  console.log(`📊 Health Check: http://${config.host}:${config.port}/api/health`);
  console.log(`🔐 Admin Login: <EMAIL> / admin123`);
  console.log(`👤 User Login: <EMAIL> / password123`);
  console.log(`📁 XAMPP Path: ${config.xamppPath}`);
  console.log(`🏠 Host: ${config.host}:${config.port}`);
  console.log(`📴 Mode: COMPLETE OFFLINE`);
  console.log(`💾 Database: LOCAL SIMULATION`);
  console.log(`🌐 Internet: NOT REQUIRED`);
  console.log('🔥 ═══════════════════════════════════════════════════════════');
  console.log('');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 XAMPP Offline Server shutting down gracefully...');
  server.close(() => {
    console.log('✅ XAMPP Offline Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 XAMPP Offline Server shutting down gracefully...');
  server.close(() => {
    console.log('✅ XAMPP Offline Server closed');
    process.exit(0);
  });
});

module.exports = app;
