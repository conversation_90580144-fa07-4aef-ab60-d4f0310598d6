import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../shared/logger/logger.service';

// DTOs
import { UpdateProfileDto } from './dto/update-profile.dto';
import { UploadDocumentDto } from './dto/upload-document.dto';
import { KycSubmissionDto } from './dto/kyc-submission.dto';
import { ChangePhoneDto } from './dto/change-phone.dto';
import { ChangeEmailDto } from './dto/change-email.dto';
import { UserPreferencesDto } from './dto/user-preferences.dto';

// Services
import { KycService } from './services/kyc.service';
import { ProfileService } from './services/profile.service';
import { DocumentService } from './services/document.service';

@Injectable()
export class UsersService {
  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    private readonly kycService: KycService,
    private readonly profileService: ProfileService,
    private readonly documentService: DocumentService,
  ) {
    this.logger.setContext('UsersService');
  }

  /**
   * الحصول على الملف الشخصي
   */
  async getProfile(userId: string) {
    try {
      const profile = await this.profileService.getProfile(userId);
      
      if (!profile) {
        throw new NotFoundException('الملف الشخصي غير موجود');
      }

      return {
        success: true,
        data: profile,
        message: 'تم جلب الملف الشخصي بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في جلب الملف الشخصي', error.stack, { userId });
      throw error;
    }
  }

  /**
   * تحديث الملف الشخصي
   */
  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto) {
    try {
      const updatedProfile = await this.profileService.updateProfile(userId, updateProfileDto);

      this.logger.logUserActivity(userId, 'profile_updated', {
        updatedFields: Object.keys(updateProfileDto),
      });

      return {
        success: true,
        data: updatedProfile,
        message: 'تم تحديث الملف الشخصي بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في تحديث الملف الشخصي', error.stack, { userId, updateProfileDto });
      throw error;
    }
  }

  /**
   * رفع وثيقة
   */
  async uploadDocument(
    userId: string,
    file: Express.Multer.File,
    uploadDocumentDto: UploadDocumentDto,
  ) {
    try {
      // التحقق من نوع وحجم الملف
      this.validateFile(file);

      const document = await this.documentService.uploadDocument(
        userId,
        file,
        uploadDocumentDto,
      );

      this.logger.logUserActivity(userId, 'document_uploaded', {
        documentType: uploadDocumentDto.documentType,
        documentId: document.id,
      });

      return {
        success: true,
        data: document,
        message: 'تم رفع الوثيقة بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في رفع الوثيقة', error.stack, { userId, uploadDocumentDto });
      throw error;
    }
  }

  /**
   * قائمة الوثائق المرفوعة
   */
  async getDocuments(userId: string) {
    try {
      const documents = await this.documentService.getUserDocuments(userId);

      return {
        success: true,
        data: documents,
        message: 'تم جلب قائمة الوثائق بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في جلب قائمة الوثائق', error.stack, { userId });
      throw error;
    }
  }

  /**
   * حذف وثيقة
   */
  async deleteDocument(userId: string, documentId: string) {
    try {
      await this.documentService.deleteDocument(userId, documentId);

      this.logger.logUserActivity(userId, 'document_deleted', { documentId });

      return {
        success: true,
        message: 'تم حذف الوثيقة بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في حذف الوثيقة', error.stack, { userId, documentId });
      throw error;
    }
  }

  /**
   * تقديم طلب التحقق من الهوية
   */
  async submitKyc(userId: string, kycSubmissionDto: KycSubmissionDto) {
    try {
      const kycApplication = await this.kycService.submitKycApplication(
        userId,
        kycSubmissionDto,
      );

      this.logger.logUserActivity(userId, 'kyc_submitted', {
        kycLevel: kycSubmissionDto.kycLevel,
        applicationId: kycApplication.id,
      });

      return {
        success: true,
        data: kycApplication,
        message: 'تم تقديم طلب التحقق من الهوية بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في تقديم طلب KYC', error.stack, { userId, kycSubmissionDto });
      throw error;
    }
  }

  /**
   * حالة التحقق من الهوية
   */
  async getKycStatus(userId: string) {
    try {
      const kycStatus = await this.kycService.getKycStatus(userId);

      return {
        success: true,
        data: kycStatus,
        message: 'تم جلب حالة التحقق من الهوية بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في جلب حالة KYC', error.stack, { userId });
      throw error;
    }
  }

  /**
   * تغيير رقم الهاتف
   */
  async changePhone(userId: string, changePhoneDto: ChangePhoneDto) {
    try {
      // التحقق من رمز OTP للهاتف الحالي
      const isValidOtp = await this.verifyCurrentPhoneOtp(
        userId,
        changePhoneDto.currentPhoneOtp,
      );

      if (!isValidOtp) {
        throw new BadRequestException('رمز التحقق غير صحيح');
      }

      // التحقق من عدم استخدام الرقم الجديد من قبل مستخدم آخر
      const isPhoneUsed = await this.isPhoneNumberUsed(changePhoneDto.newPhone);
      if (isPhoneUsed) {
        throw new ConflictException('رقم الهاتف مستخدم من قبل مستخدم آخر');
      }

      // إرسال رمز تحقق للرقم الجديد
      await this.sendPhoneVerificationOtp(userId, changePhoneDto.newPhone);

      this.logger.logUserActivity(userId, 'phone_change_requested', {
        newPhone: changePhoneDto.newPhone,
      });

      return {
        success: true,
        message: 'تم إرسال رمز التحقق للرقم الجديد',
        requiresVerification: true,
      };
    } catch (error) {
      this.logger.error('خطأ في تغيير رقم الهاتف', error.stack, { userId, changePhoneDto });
      throw error;
    }
  }

  /**
   * تغيير البريد الإلكتروني
   */
  async changeEmail(userId: string, changeEmailDto: ChangeEmailDto) {
    try {
      // التحقق من رمز OTP للبريد الحالي
      const isValidOtp = await this.verifyCurrentEmailOtp(
        userId,
        changeEmailDto.currentEmailOtp,
      );

      if (!isValidOtp) {
        throw new BadRequestException('رمز التحقق غير صحيح');
      }

      // التحقق من عدم استخدام البريد الجديد من قبل مستخدم آخر
      const isEmailUsed = await this.isEmailUsed(changeEmailDto.newEmail);
      if (isEmailUsed) {
        throw new ConflictException('البريد الإلكتروني مستخدم من قبل مستخدم آخر');
      }

      // إرسال رمز تحقق للبريد الجديد
      await this.sendEmailVerificationOtp(userId, changeEmailDto.newEmail);

      this.logger.logUserActivity(userId, 'email_change_requested', {
        newEmail: changeEmailDto.newEmail,
      });

      return {
        success: true,
        message: 'تم إرسال رمز التحقق للبريد الجديد',
        requiresVerification: true,
      };
    } catch (error) {
      this.logger.error('خطأ في تغيير البريد الإلكتروني', error.stack, { userId, changeEmailDto });
      throw error;
    }
  }

  /**
   * إعدادات المستخدم
   */
  async getPreferences(userId: string) {
    try {
      const preferences = await this.getUserPreferences(userId);

      return {
        success: true,
        data: preferences,
        message: 'تم جلب إعدادات المستخدم بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في جلب إعدادات المستخدم', error.stack, { userId });
      throw error;
    }
  }

  /**
   * تحديث إعدادات المستخدم
   */
  async updatePreferences(userId: string, preferencesDto: UserPreferencesDto) {
    try {
      const updatedPreferences = await this.updateUserPreferences(userId, preferencesDto);

      this.logger.logUserActivity(userId, 'preferences_updated', {
        updatedSections: Object.keys(preferencesDto),
      });

      return {
        success: true,
        data: updatedPreferences,
        message: 'تم تحديث إعدادات المستخدم بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في تحديث إعدادات المستخدم', error.stack, { userId, preferencesDto });
      throw error;
    }
  }

  /**
   * سجل نشاط المستخدم
   */
  async getActivity(userId: string, page: number, limit: number) {
    try {
      const activities = await this.getUserActivities(userId, page, limit);
      const totalCount = await this.getUserActivitiesCount(userId);

      return {
        success: true,
        data: {
          activities,
          pagination: {
            page,
            limit,
            total: totalCount,
            pages: Math.ceil(totalCount / limit),
          },
        },
        message: 'تم جلب سجل النشاط بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في جلب سجل النشاط', error.stack, { userId });
      throw error;
    }
  }

  /**
   * إحصائيات المستخدم
   */
  async getStatistics(userId: string) {
    try {
      const statistics = await this.calculateUserStatistics(userId);

      return {
        success: true,
        data: statistics,
        message: 'تم جلب إحصائيات المستخدم بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في جلب إحصائيات المستخدم', error.stack, { userId });
      throw error;
    }
  }

  /**
   * إلغاء تفعيل الحساب
   */
  async deactivateAccount(userId: string, reason: string) {
    try {
      await this.setAccountStatus(userId, false, reason);

      this.logger.logUserActivity(userId, 'account_deactivated', { reason });

      return {
        success: true,
        message: 'تم إلغاء تفعيل الحساب بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في إلغاء تفعيل الحساب', error.stack, { userId });
      throw error;
    }
  }

  /**
   * إعادة تفعيل الحساب
   */
  async reactivateAccount(userId: string) {
    try {
      await this.setAccountStatus(userId, true);

      this.logger.logUserActivity(userId, 'account_reactivated');

      return {
        success: true,
        message: 'تم إعادة تفعيل الحساب بنجاح',
      };
    } catch (error) {
      this.logger.error('خطأ في إعادة تفعيل الحساب', error.stack, { userId });
      throw error;
    }
  }

  // Helper Methods (سيتم تنفيذها لاحقاً)
  private validateFile(file: Express.Multer.File): void {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];

    if (!file) {
      throw new BadRequestException('الملف مطلوب');
    }

    if (file.size > maxSize) {
      throw new BadRequestException('حجم الملف كبير جداً (الحد الأقصى 10MB)');
    }

    if (!allowedTypes.includes(file.mimetype)) {
      throw new BadRequestException('نوع الملف غير مدعوم');
    }
  }

  private async verifyCurrentPhoneOtp(userId: string, otp: string): Promise<boolean> {
    // TODO: التحقق من رمز OTP
    return true;
  }

  private async verifyCurrentEmailOtp(userId: string, otp: string): Promise<boolean> {
    // TODO: التحقق من رمز OTP
    return true;
  }

  private async isPhoneNumberUsed(phone: string): Promise<boolean> {
    // TODO: التحقق من استخدام رقم الهاتف
    return false;
  }

  private async isEmailUsed(email: string): Promise<boolean> {
    // TODO: التحقق من استخدام البريد الإلكتروني
    return false;
  }

  private async sendPhoneVerificationOtp(userId: string, phone: string): Promise<void> {
    // TODO: إرسال رمز التحقق للهاتف
  }

  private async sendEmailVerificationOtp(userId: string, email: string): Promise<void> {
    // TODO: إرسال رمز التحقق للبريد
  }

  private async getUserPreferences(userId: string): Promise<any> {
    // TODO: جلب إعدادات المستخدم
    return {};
  }

  private async updateUserPreferences(userId: string, preferences: UserPreferencesDto): Promise<any> {
    // TODO: تحديث إعدادات المستخدم
    return preferences;
  }

  private async getUserActivities(userId: string, page: number, limit: number): Promise<any[]> {
    // TODO: جلب سجل النشاط
    return [];
  }

  private async getUserActivitiesCount(userId: string): Promise<number> {
    // TODO: عد سجل النشاط
    return 0;
  }

  private async calculateUserStatistics(userId: string): Promise<any> {
    // TODO: حساب إحصائيات المستخدم
    return {};
  }

  private async setAccountStatus(userId: string, isActive: boolean, reason?: string): Promise<void> {
    // TODO: تحديث حالة الحساب
  }

  // Placeholder methods for beneficiaries and limits
  async getBeneficiaries(userId: string) {
    return { success: true, data: [], message: 'قائمة المستفيدين' };
  }

  async addBeneficiary(userId: string, beneficiaryDto: any) {
    return { success: true, message: 'تم إضافة المستفيد' };
  }

  async updateBeneficiary(userId: string, beneficiaryId: string, beneficiaryDto: any) {
    return { success: true, message: 'تم تحديث المستفيد' };
  }

  async deleteBeneficiary(userId: string, beneficiaryId: string) {
    return { success: true, message: 'تم حذف المستفيد' };
  }

  async getTransferLimits(userId: string) {
    return { success: true, data: {}, message: 'حدود التحويل' };
  }

  async requestLimitUpgrade(userId: string, upgradeDto: any) {
    return { success: true, message: 'تم تقديم طلب رفع الحدود' };
  }
}
