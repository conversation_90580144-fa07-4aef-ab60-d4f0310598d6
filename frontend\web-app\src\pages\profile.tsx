import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Avatar,
  Divider,
  Alert,
  Chip,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
} from '@mui/material';
import {
  Edit,
  Save,
  Cancel,
  PhotoCamera,
  Verified,
  Warning,
  Person,
  Work,
  Home,
  Phone,
  Email,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Head from 'next/head';
import Layout from '../components/Layout';

interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  nationality: string;
  isVerified: boolean;
  profilePicture?: string;
  occupation?: string;
  employer?: string;
  monthlyIncome?: number;
  address?: {
    addressLine1: string;
    city: string;
    country: string;
    postalCode: string;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relation: string;
  };
  completionPercentage: number;
}

const validationSchema = Yup.object({
  firstName: Yup.string().required('الاسم الأول مطلوب'),
  lastName: Yup.string().required('اسم العائلة مطلوب'),
  phone: Yup.string().required('رقم الهاتف مطلوب'),
  occupation: Yup.string(),
  employer: Yup.string(),
  monthlyIncome: Yup.number().min(0, 'الدخل يجب أن يكون رقم موجب'),
});

const ProfilePage: React.FC = () => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/profile/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const profileData = await response.json();
        setProfile(profileData);
      } else {
        setError('فشل في تحميل الملف الشخصي');
        // Mock data for development
        setProfile({
          id: '1',
          firstName: 'أحمد',
          lastName: 'محمد',
          email: '<EMAIL>',
          phone: '+966501234567',
          dateOfBirth: '1990-01-01',
          nationality: 'SA',
          isVerified: false,
          occupation: 'مهندس برمجيات',
          employer: 'شركة التقنية المتقدمة',
          monthlyIncome: 15000,
          address: {
            addressLine1: 'شارع الملك فهد',
            city: 'الرياض',
            country: 'SA',
            postalCode: '12345',
          },
          emergencyContact: {
            name: 'فاطمة محمد',
            phone: '+966501234568',
            relation: 'زوجة',
          },
          completionPercentage: 75,
        });
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال');
    } finally {
      setLoading(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      firstName: profile?.firstName || '',
      lastName: profile?.lastName || '',
      phone: profile?.phone || '',
      occupation: profile?.occupation || '',
      employer: profile?.employer || '',
      monthlyIncome: profile?.monthlyIncome || '',
      addressLine1: profile?.address?.addressLine1 || '',
      city: profile?.address?.city || '',
      postalCode: profile?.address?.postalCode || '',
      emergencyContactName: profile?.emergencyContact?.name || '',
      emergencyContactPhone: profile?.emergencyContact?.phone || '',
      emergencyContactRelation: profile?.emergencyContact?.relation || '',
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      setSaving(true);
      setError(null);

      try {
        const response = await fetch('/api/profile/me', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            firstName: values.firstName,
            lastName: values.lastName,
            phone: values.phone,
            occupation: values.occupation,
            employer: values.employer,
            monthlyIncome: values.monthlyIncome ? parseFloat(values.monthlyIncome.toString()) : undefined,
            address: {
              addressLine1: values.addressLine1,
              city: values.city,
              country: 'SA',
              postalCode: values.postalCode,
            },
            emergencyContactName: values.emergencyContactName,
            emergencyContactPhone: values.emergencyContactPhone,
            emergencyContactRelation: values.emergencyContactRelation,
          }),
        });

        if (response.ok) {
          const updatedProfile = await response.json();
          setProfile(updatedProfile);
          setSuccess('تم تحديث الملف الشخصي بنجاح');
          setEditing(false);
        } else {
          const errorData = await response.json();
          setError(errorData.message || 'فشل في تحديث الملف الشخصي');
        }
      } catch (err) {
        setError('حدث خطأ في تحديث الملف الشخصي');
      } finally {
        setSaving(false);
      }
    },
  });

  const handleEdit = () => {
    setEditing(true);
    setError(null);
    setSuccess(null);
  };

  const handleCancel = () => {
    setEditing(false);
    formik.resetForm();
    setError(null);
    setSuccess(null);
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'success';
    if (percentage >= 50) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <Layout>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Typography>جاري التحميل...</Typography>
        </Box>
      </Layout>
    );
  }

  if (!profile) {
    return (
      <Layout>
        <Box p={3}>
          <Alert severity="error">فشل في تحميل الملف الشخصي</Alert>
        </Box>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>الملف الشخصي - WS Transfir</title>
        <meta name="description" content="إدارة الملف الشخصي في نظام WS Transfir" />
      </Head>

      <Layout>
        <Box sx={{ p: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            الملف الشخصي
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Profile Header */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box display="flex" alignItems="center">
                      <Box position="relative">
                        <Avatar
                          src={profile.profilePicture}
                          sx={{ width: 80, height: 80, mr: 3 }}
                        >
                          {profile.firstName[0]}{profile.lastName[0]}
                        </Avatar>
                        <IconButton
                          sx={{
                            position: 'absolute',
                            bottom: 0,
                            right: 20,
                            backgroundColor: 'primary.main',
                            color: 'white',
                            '&:hover': { backgroundColor: 'primary.dark' },
                            width: 32,
                            height: 32,
                          }}
                        >
                          <PhotoCamera fontSize="small" />
                        </IconButton>
                      </Box>
                      <Box>
                        <Typography variant="h5" component="h2">
                          {profile.firstName} {profile.lastName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {profile.email}
                        </Typography>
                        <Box display="flex" alignItems="center" mt={1}>
                          {profile.isVerified ? (
                            <Chip
                              icon={<Verified />}
                              label="محقق"
                              color="success"
                              size="small"
                            />
                          ) : (
                            <Chip
                              icon={<Warning />}
                              label="غير محقق"
                              color="warning"
                              size="small"
                            />
                          )}
                        </Box>
                      </Box>
                    </Box>

                    <Box textAlign="right">
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        اكتمال الملف الشخصي
                      </Typography>
                      <Box display="flex" alignItems="center" mb={1}>
                        <LinearProgress
                          variant="determinate"
                          value={profile.completionPercentage}
                          color={getCompletionColor(profile.completionPercentage)}
                          sx={{ width: 100, mr: 1 }}
                        />
                        <Typography variant="body2" fontWeight="bold">
                          {profile.completionPercentage}%
                        </Typography>
                      </Box>
                      {!editing && (
                        <Button
                          variant="outlined"
                          startIcon={<Edit />}
                          onClick={handleEdit}
                        >
                          تعديل
                        </Button>
                      )}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Personal Information */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                    المعلومات الشخصية
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <form onSubmit={formik.handleSubmit}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          name="firstName"
                          label="الاسم الأول"
                          value={formik.values.firstName}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                          helperText={formik.touched.firstName && formik.errors.firstName}
                          disabled={!editing}
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          name="lastName"
                          label="اسم العائلة"
                          value={formik.values.lastName}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                          helperText={formik.touched.lastName && formik.errors.lastName}
                          disabled={!editing}
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          name="phone"
                          label="رقم الهاتف"
                          value={formik.values.phone}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.phone && Boolean(formik.errors.phone)}
                          helperText={formik.touched.phone && formik.errors.phone}
                          disabled={!editing}
                          size="small"
                          dir="ltr"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="تاريخ الميلاد"
                          value={profile.dateOfBirth}
                          disabled
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="الجنسية"
                          value={profile.nationality === 'SA' ? 'السعودية' : profile.nationality}
                          disabled
                          size="small"
                        />
                      </Grid>
                    </Grid>

                    {editing && (
                      <Box mt={3} display="flex" gap={2}>
                        <Button
                          type="submit"
                          variant="contained"
                          startIcon={<Save />}
                          disabled={saving}
                        >
                          {saving ? 'جاري الحفظ...' : 'حفظ'}
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<Cancel />}
                          onClick={handleCancel}
                        >
                          إلغاء
                        </Button>
                      </Box>
                    )}
                  </form>
                </CardContent>
              </Card>
            </Grid>

            {/* Professional Information */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <Work sx={{ mr: 1, verticalAlign: 'middle' }} />
                    المعلومات المهنية
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        name="occupation"
                        label="المهنة"
                        value={formik.values.occupation}
                        onChange={formik.handleChange}
                        disabled={!editing}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        name="employer"
                        label="جهة العمل"
                        value={formik.values.employer}
                        onChange={formik.handleChange}
                        disabled={!editing}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        name="monthlyIncome"
                        label="الدخل الشهري (ريال سعودي)"
                        type="number"
                        value={formik.values.monthlyIncome}
                        onChange={formik.handleChange}
                        disabled={!editing}
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Address Information */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <Home sx={{ mr: 1, verticalAlign: 'middle' }} />
                    معلومات العنوان
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        name="addressLine1"
                        label="العنوان"
                        value={formik.values.addressLine1}
                        onChange={formik.handleChange}
                        disabled={!editing}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        name="city"
                        label="المدينة"
                        value={formik.values.city}
                        onChange={formik.handleChange}
                        disabled={!editing}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        name="postalCode"
                        label="الرمز البريدي"
                        value={formik.values.postalCode}
                        onChange={formik.handleChange}
                        disabled={!editing}
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Emergency Contact */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <Phone sx={{ mr: 1, verticalAlign: 'middle' }} />
                    جهة الاتصال في الطوارئ
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        name="emergencyContactName"
                        label="الاسم"
                        value={formik.values.emergencyContactName}
                        onChange={formik.handleChange}
                        disabled={!editing}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        name="emergencyContactPhone"
                        label="رقم الهاتف"
                        value={formik.values.emergencyContactPhone}
                        onChange={formik.handleChange}
                        disabled={!editing}
                        size="small"
                        dir="ltr"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControl fullWidth size="small">
                        <InputLabel>صلة القرابة</InputLabel>
                        <Select
                          name="emergencyContactRelation"
                          value={formik.values.emergencyContactRelation}
                          onChange={formik.handleChange}
                          disabled={!editing}
                          label="صلة القرابة"
                        >
                          <MenuItem value="زوج">زوج</MenuItem>
                          <MenuItem value="زوجة">زوجة</MenuItem>
                          <MenuItem value="أب">أب</MenuItem>
                          <MenuItem value="أم">أم</MenuItem>
                          <MenuItem value="أخ">أخ</MenuItem>
                          <MenuItem value="أخت">أخت</MenuItem>
                          <MenuItem value="ابن">ابن</MenuItem>
                          <MenuItem value="ابنة">ابنة</MenuItem>
                          <MenuItem value="صديق">صديق</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Layout>
    </>
  );
};

export default ProfilePage;
