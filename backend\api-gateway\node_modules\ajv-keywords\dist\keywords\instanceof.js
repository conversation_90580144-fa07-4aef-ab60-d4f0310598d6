"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const instanceof_1 = __importDefault(require("../definitions/instanceof"));
const instanceofPlugin = (ajv) => ajv.addKeyword((0, instanceof_1.default)());
exports.default = instanceofPlugin;
module.exports = instanceofPlugin;
//# sourceMappingURL=instanceof.js.map