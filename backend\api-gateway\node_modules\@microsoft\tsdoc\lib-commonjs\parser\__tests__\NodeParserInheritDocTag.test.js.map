{"version": 3, "file": "NodeParserInheritDocTag.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserInheritDocTag.test.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,6CAA4C;AAE5C,IAAI,CAAC,sCAAsC,EAAE;IAC3C,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3F,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,+BAA+B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACxG,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,0CAA0C,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACtE,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,sCAAsC,EAAE;IAC3C,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,8BAA8B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACvG,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,+BAA+B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACxG,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAClE,CAAC;IACF,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,iBAAiB,EAAE,aAAa,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAChF,CAAC;IAEF,2BAA2B;IAC3B,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,wDAAwD,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACpF,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See L<PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('00 InheritDoc tag: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@inheritDoc}', ' */'].join('\\n'));\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@inheritDoc Class.member}', ' */'].join('\\n'));\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@inheritDoc package# Class . member}', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('01 InheritDoc tag: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@inheritDoc | link text}', ' */'].join('\\n'));\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@inheritDoc Class % junk}', ' */'].join('\\n'));\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@inheritDoc}', ' * {@inheritDoc}', ' */'].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * summary text', ' * @remarks', ' * {@inheritDoc}', ' */'].join('\\n')\r\n  );\r\n\r\n  // Old API Extractor syntax\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@inheritdoc @scope/library:IDisposable.isDisposed}', ' */'].join('\\n')\r\n  );\r\n});\r\n"]}