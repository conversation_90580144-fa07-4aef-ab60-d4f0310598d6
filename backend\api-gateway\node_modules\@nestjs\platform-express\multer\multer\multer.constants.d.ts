export declare const multerExceptions: {
    LIMIT_PART_COUNT: string;
    LIMIT_FILE_SIZE: string;
    LIMIT_FILE_COUNT: string;
    LIMIT_FIELD_KEY: string;
    LIMIT_FIELD_VALUE: string;
    LIMIT_FIELD_COUNT: string;
    LIMIT_UNEXPECTED_FILE: string;
    MISSING_FIELD_NAME: string;
};
export declare const busboyExceptions: {
    MULTIPART_BOUNDARY_NOT_FOUND: string;
    MULTIPART_MALFORMED_PART_HEADER: string;
    MULTIPART_UNEXPECTED_END_OF_FORM: string;
    MULTIPART_UNEXPECTED_END_OF_FILE: string;
};
