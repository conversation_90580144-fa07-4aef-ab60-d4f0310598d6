import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserSession } from '../entities/user-session.entity';

@Injectable()
export class SessionService {
  constructor(
    @InjectRepository(UserSession)
    private readonly sessionRepository: Repository<UserSession>,
  ) {}

  async createSession(
    userId: string,
    refreshToken: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<UserSession> {
    // Deactivate old sessions for this user (optional - for single session per user)
    // await this.sessionRepository.update(
    //   { userId, isActive: true },
    //   { isActive: false }
    // );

    const session = this.sessionRepository.create({
      userId,
      refreshToken,
      ipAddress,
      userAgent,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    return this.sessionRepository.save(session);
  }

  async getSession(refreshToken: string): Promise<UserSession | null> {
    return this.sessionRepository.findOne({
      where: { refreshToken, isActive: true },
      relations: ['user'],
    });
  }

  async revokeSession(refreshToken: string): Promise<void> {
    await this.sessionRepository.update(
      { refreshToken },
      { isActive: false }
    );
  }

  async revokeAllUserSessions(userId: string): Promise<void> {
    await this.sessionRepository.update(
      { userId, isActive: true },
      { isActive: false }
    );
  }

  async cleanupExpiredSessions(): Promise<void> {
    await this.sessionRepository.delete({
      expiresAt: { $lt: new Date() } as any,
    });
  }
}
