import { apiRequest } from './client';
import { User } from '@/contexts/AuthContext';

// أنواع البيانات للمصادقة
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  accessToken: string;
  refreshToken: string;
  user: User;
  requires2FA?: boolean;
  tempToken?: string;
}

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  nationalId: string;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  userId: string;
}

export interface Verify2FARequest {
  tempToken: string;
  code: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}

export interface VerifyPhoneRequest {
  phone: string;
  otp: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ConfirmResetPasswordRequest {
  token: string;
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface Enable2FARequest {
  method: 'sms' | 'email' | 'authenticator';
  phone?: string;
}

export interface Verify2FASetupRequest {
  method: 'sms' | 'email' | 'authenticator';
  code: string;
  secret?: string;
}

// خدمة API للمصادقة
export const authAPI = {
  /**
   * تسجيل الدخول
   */
  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/login', data);
    return response.data;
  },

  /**
   * التسجيل
   */
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    const response = await apiRequest.post<RegisterResponse>('/auth/register', data);
    return response.data;
  },

  /**
   * تسجيل الخروج
   */
  async logout(): Promise<void> {
    await apiRequest.post('/auth/logout');
  },

  /**
   * التحقق من المصادقة الثنائية
   */
  async verify2FA(data: Verify2FARequest): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/verify-2fa', data);
    return response.data;
  },

  /**
   * تجديد الرمز المميز
   */
  async refreshToken(): Promise<RefreshTokenResponse> {
    const response = await apiRequest.post<RefreshTokenResponse>('/auth/refresh');
    return response.data;
  },

  /**
   * الحصول على الملف الشخصي
   */
  async getProfile(): Promise<{ data: User }> {
    const response = await apiRequest.get<{ data: User }>('/auth/profile');
    return response.data;
  },

  /**
   * تحديث الملف الشخصي
   */
  async updateProfile(data: Partial<User>): Promise<{ data: User }> {
    const response = await apiRequest.put<{ data: User }>('/auth/profile', data);
    return response.data;
  },

  /**
   * التحقق من رقم الهاتف
   */
  async verifyPhone(data: VerifyPhoneRequest): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/verify-phone', data);
    return response.data;
  },

  /**
   * إعادة إرسال رمز التحقق للهاتف
   */
  async resendPhoneOTP(phone: string): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/resend-phone-otp', { phone });
    return response.data;
  },

  /**
   * إعادة تعيين كلمة المرور
   */
  async resetPassword(data: ResetPasswordRequest): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/reset-password', data);
    return response.data;
  },

  /**
   * تأكيد إعادة تعيين كلمة المرور
   */
  async confirmResetPassword(data: ConfirmResetPasswordRequest): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/confirm-reset-password', data);
    return response.data;
  },

  /**
   * تغيير كلمة المرور
   */
  async changePassword(data: ChangePasswordRequest): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/change-password', data);
    return response.data;
  },

  /**
   * تفعيل المصادقة الثنائية
   */
  async enable2FA(data: Enable2FARequest): Promise<{
    success: boolean;
    message: string;
    secret?: string;
    qrCode?: string;
  }> {
    const response = await apiRequest.post('/auth/enable-2fa', data);
    return response.data;
  },

  /**
   * التحقق من إعداد المصادقة الثنائية
   */
  async verify2FASetup(data: Verify2FASetupRequest): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/verify-2fa-setup', data);
    return response.data;
  },

  /**
   * إلغاء المصادقة الثنائية
   */
  async disable2FA(): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/disable-2fa');
    return response.data;
  },

  /**
   * إرسال رمز المصادقة الثنائية
   */
  async send2FACode(): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/send-2fa-code');
    return response.data;
  },

  /**
   * التحقق من صحة الرمز المميز
   */
  async validateToken(): Promise<{ valid: boolean; user?: User }> {
    const response = await apiRequest.get('/auth/validate-token');
    return response.data;
  },

  /**
   * الحصول على جلسات المستخدم النشطة
   */
  async getActiveSessions(): Promise<{
    sessions: Array<{
      id: string;
      device: string;
      location: string;
      ipAddress: string;
      lastActivity: string;
      isCurrent: boolean;
    }>;
  }> {
    const response = await apiRequest.get('/auth/sessions');
    return response.data;
  },

  /**
   * إنهاء جلسة معينة
   */
  async terminateSession(sessionId: string): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.delete(`/auth/sessions/${sessionId}`);
    return response.data;
  },

  /**
   * إنهاء جميع الجلسات الأخرى
   */
  async terminateAllOtherSessions(): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/terminate-all-sessions');
    return response.data;
  },

  /**
   * تحديث إعدادات الأمان
   */
  async updateSecuritySettings(settings: {
    loginNotifications?: boolean;
    suspiciousActivityAlerts?: boolean;
    sessionTimeout?: number;
  }): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.put('/auth/security-settings', settings);
    return response.data;
  },

  /**
   * الحصول على سجل الأنشطة الأمنية
   */
  async getSecurityLog(params?: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    activities: Array<{
      id: string;
      type: string;
      description: string;
      ipAddress: string;
      userAgent: string;
      location: string;
      timestamp: string;
      riskLevel: 'low' | 'medium' | 'high';
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    const response = await apiRequest.get('/auth/security-log', { params });
    return response.data;
  },

  /**
   * الإبلاغ عن نشاط مشبوه
   */
  async reportSuspiciousActivity(data: {
    type: string;
    description: string;
    timestamp?: string;
  }): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest.post('/auth/report-suspicious-activity', data);
    return response.data;
  },
};
