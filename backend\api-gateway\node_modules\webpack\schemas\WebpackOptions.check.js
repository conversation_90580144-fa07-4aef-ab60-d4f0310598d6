/*
 * This file was automatically generated.
 * DO NOT MODIFY BY HAND.
 * Run `yarn fix:special` to update
 */
const e=/^(?:[A-Za-z]:[\\/]|\\\\|\/)/;module.exports=_e,module.exports.default=_e;const t={definitions:{Amd:{anyOf:[{enum:[!1]},{type:"object"}]},AmdContainer:{type:"string",minLength:1},AssetFilterItemTypes:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!1},{instanceof:"Function"}]},AssetFilterTypes:{anyOf:[{type:"array",items:{oneOf:[{$ref:"#/definitions/AssetFilterItemTypes"}]}},{$ref:"#/definitions/AssetFilterItemTypes"}]},AssetGeneratorDataUrl:{anyOf:[{$ref:"#/definitions/AssetGeneratorDataUrlOptions"},{$ref:"#/definitions/AssetGeneratorDataUrlFunction"}]},AssetGeneratorDataUrlFunction:{instanceof:"Function"},AssetGeneratorDataUrlOptions:{type:"object",additionalProperties:!1,properties:{encoding:{enum:[!1,"base64"]},mimetype:{type:"string"}}},AssetGeneratorOptions:{type:"object",additionalProperties:!1,properties:{binary:{type:"boolean"},dataUrl:{$ref:"#/definitions/AssetGeneratorDataUrl"},emit:{type:"boolean"},filename:{$ref:"#/definitions/FilenameTemplate"},outputPath:{$ref:"#/definitions/AssetModuleOutputPath"},publicPath:{$ref:"#/definitions/RawPublicPath"}}},AssetInlineGeneratorOptions:{type:"object",additionalProperties:!1,properties:{binary:{type:"boolean"},dataUrl:{$ref:"#/definitions/AssetGeneratorDataUrl"}}},AssetModuleFilename:{anyOf:[{type:"string",absolutePath:!1},{instanceof:"Function"}]},AssetModuleOutputPath:{anyOf:[{type:"string",absolutePath:!1},{instanceof:"Function"}]},AssetParserDataUrlFunction:{instanceof:"Function"},AssetParserDataUrlOptions:{type:"object",additionalProperties:!1,properties:{maxSize:{type:"number"}}},AssetParserOptions:{type:"object",additionalProperties:!1,properties:{dataUrlCondition:{anyOf:[{$ref:"#/definitions/AssetParserDataUrlOptions"},{$ref:"#/definitions/AssetParserDataUrlFunction"}]}}},AssetResourceGeneratorOptions:{type:"object",additionalProperties:!1,properties:{binary:{type:"boolean"},emit:{type:"boolean"},filename:{$ref:"#/definitions/FilenameTemplate"},outputPath:{$ref:"#/definitions/AssetModuleOutputPath"},publicPath:{$ref:"#/definitions/RawPublicPath"}}},AuxiliaryComment:{anyOf:[{type:"string"},{$ref:"#/definitions/LibraryCustomUmdCommentObject"}]},Bail:{type:"boolean"},CacheOptions:{anyOf:[{enum:[!0]},{$ref:"#/definitions/CacheOptionsNormalized"}]},CacheOptionsNormalized:{anyOf:[{enum:[!1]},{$ref:"#/definitions/MemoryCacheOptions"},{$ref:"#/definitions/FileCacheOptions"}]},Charset:{type:"boolean"},ChunkFilename:{oneOf:[{$ref:"#/definitions/FilenameTemplate"}]},ChunkFormat:{anyOf:[{enum:["array-push","commonjs","module",!1]},{type:"string"}]},ChunkLoadTimeout:{type:"number"},ChunkLoading:{anyOf:[{enum:[!1]},{$ref:"#/definitions/ChunkLoadingType"}]},ChunkLoadingGlobal:{type:"string"},ChunkLoadingType:{anyOf:[{enum:["jsonp","import-scripts","require","async-node","import"]},{type:"string"}]},Clean:{anyOf:[{type:"boolean"},{$ref:"#/definitions/CleanOptions"}]},CleanOptions:{type:"object",additionalProperties:!1,properties:{dry:{type:"boolean"},keep:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!1},{instanceof:"Function"}]}}},CompareBeforeEmit:{type:"boolean"},Context:{type:"string",absolutePath:!0},CrossOriginLoading:{enum:[!1,"anonymous","use-credentials"]},CssAutoGeneratorOptions:{type:"object",additionalProperties:!1,properties:{esModule:{$ref:"#/definitions/CssGeneratorEsModule"},exportsConvention:{$ref:"#/definitions/CssGeneratorExportsConvention"},exportsOnly:{$ref:"#/definitions/CssGeneratorExportsOnly"},localIdentName:{$ref:"#/definitions/CssGeneratorLocalIdentName"}}},CssAutoParserOptions:{type:"object",additionalProperties:!1,properties:{import:{$ref:"#/definitions/CssParserImport"},namedExports:{$ref:"#/definitions/CssParserNamedExports"},url:{$ref:"#/definitions/CssParserUrl"}}},CssChunkFilename:{oneOf:[{$ref:"#/definitions/FilenameTemplate"}]},CssFilename:{oneOf:[{$ref:"#/definitions/FilenameTemplate"}]},CssGeneratorEsModule:{type:"boolean"},CssGeneratorExportsConvention:{anyOf:[{enum:["as-is","camel-case","camel-case-only","dashes","dashes-only"]},{instanceof:"Function"}]},CssGeneratorExportsOnly:{type:"boolean"},CssGeneratorLocalIdentName:{type:"string"},CssGeneratorOptions:{type:"object",additionalProperties:!1,properties:{esModule:{$ref:"#/definitions/CssGeneratorEsModule"},exportsOnly:{$ref:"#/definitions/CssGeneratorExportsOnly"}}},CssGlobalGeneratorOptions:{type:"object",additionalProperties:!1,properties:{esModule:{$ref:"#/definitions/CssGeneratorEsModule"},exportsConvention:{$ref:"#/definitions/CssGeneratorExportsConvention"},exportsOnly:{$ref:"#/definitions/CssGeneratorExportsOnly"},localIdentName:{$ref:"#/definitions/CssGeneratorLocalIdentName"}}},CssGlobalParserOptions:{type:"object",additionalProperties:!1,properties:{import:{$ref:"#/definitions/CssParserImport"},namedExports:{$ref:"#/definitions/CssParserNamedExports"},url:{$ref:"#/definitions/CssParserUrl"}}},CssModuleGeneratorOptions:{type:"object",additionalProperties:!1,properties:{esModule:{$ref:"#/definitions/CssGeneratorEsModule"},exportsConvention:{$ref:"#/definitions/CssGeneratorExportsConvention"},exportsOnly:{$ref:"#/definitions/CssGeneratorExportsOnly"},localIdentName:{$ref:"#/definitions/CssGeneratorLocalIdentName"}}},CssModuleParserOptions:{type:"object",additionalProperties:!1,properties:{import:{$ref:"#/definitions/CssParserImport"},namedExports:{$ref:"#/definitions/CssParserNamedExports"},url:{$ref:"#/definitions/CssParserUrl"}}},CssParserImport:{type:"boolean"},CssParserNamedExports:{type:"boolean"},CssParserOptions:{type:"object",additionalProperties:!1,properties:{import:{$ref:"#/definitions/CssParserImport"},namedExports:{$ref:"#/definitions/CssParserNamedExports"},url:{$ref:"#/definitions/CssParserUrl"}}},CssParserUrl:{type:"boolean"},DeferImportExperimentOptions:{type:"boolean",required:["asyncModule"]},Dependencies:{type:"array",items:{type:"string"}},DevServer:{anyOf:[{enum:[!1]},{type:"object"}]},DevTool:{anyOf:[{enum:[!1,"eval"]},{type:"string",pattern:"^(inline-|hidden-|eval-)?(nosources-)?(cheap-(module-)?)?source-map(-debugids)?$"}]},DevtoolFallbackModuleFilenameTemplate:{anyOf:[{type:"string"},{instanceof:"Function"}]},DevtoolModuleFilenameTemplate:{anyOf:[{type:"string"},{instanceof:"Function"}]},DevtoolNamespace:{type:"string"},EmptyGeneratorOptions:{type:"object",additionalProperties:!1},EmptyParserOptions:{type:"object",additionalProperties:!1},EnabledChunkLoadingTypes:{type:"array",items:{$ref:"#/definitions/ChunkLoadingType"}},EnabledLibraryTypes:{type:"array",items:{$ref:"#/definitions/LibraryType"}},EnabledWasmLoadingTypes:{type:"array",items:{$ref:"#/definitions/WasmLoadingType"}},Entry:{anyOf:[{$ref:"#/definitions/EntryDynamic"},{$ref:"#/definitions/EntryStatic"}]},EntryDescription:{type:"object",additionalProperties:!1,properties:{asyncChunks:{type:"boolean"},baseUri:{type:"string"},chunkLoading:{$ref:"#/definitions/ChunkLoading"},dependOn:{anyOf:[{type:"array",items:{type:"string",minLength:1},minItems:1,uniqueItems:!0},{type:"string",minLength:1}]},filename:{$ref:"#/definitions/EntryFilename"},import:{$ref:"#/definitions/EntryItem"},layer:{$ref:"#/definitions/Layer"},library:{$ref:"#/definitions/LibraryOptions"},publicPath:{$ref:"#/definitions/PublicPath"},runtime:{$ref:"#/definitions/EntryRuntime"},wasmLoading:{$ref:"#/definitions/WasmLoading"}},required:["import"]},EntryDescriptionNormalized:{type:"object",additionalProperties:!1,properties:{asyncChunks:{type:"boolean"},baseUri:{type:"string"},chunkLoading:{$ref:"#/definitions/ChunkLoading"},dependOn:{type:"array",items:{type:"string",minLength:1},minItems:1,uniqueItems:!0},filename:{$ref:"#/definitions/Filename"},import:{type:"array",items:{type:"string",minLength:1},minItems:1,uniqueItems:!0},layer:{$ref:"#/definitions/Layer"},library:{$ref:"#/definitions/LibraryOptions"},publicPath:{$ref:"#/definitions/PublicPath"},runtime:{$ref:"#/definitions/EntryRuntime"},wasmLoading:{$ref:"#/definitions/WasmLoading"}}},EntryDynamic:{instanceof:"Function"},EntryDynamicNormalized:{instanceof:"Function"},EntryFilename:{oneOf:[{$ref:"#/definitions/FilenameTemplate"}]},EntryItem:{anyOf:[{type:"array",items:{type:"string",minLength:1},minItems:1,uniqueItems:!0},{type:"string",minLength:1}]},EntryNormalized:{anyOf:[{$ref:"#/definitions/EntryDynamicNormalized"},{$ref:"#/definitions/EntryStaticNormalized"}]},EntryObject:{type:"object",additionalProperties:{anyOf:[{$ref:"#/definitions/EntryItem"},{$ref:"#/definitions/EntryDescription"}]}},EntryRuntime:{anyOf:[{enum:[!1]},{type:"string",minLength:1}]},EntryStatic:{anyOf:[{$ref:"#/definitions/EntryObject"},{$ref:"#/definitions/EntryUnnamed"}]},EntryStaticNormalized:{type:"object",additionalProperties:{oneOf:[{$ref:"#/definitions/EntryDescriptionNormalized"}]}},EntryUnnamed:{oneOf:[{$ref:"#/definitions/EntryItem"}]},Environment:{type:"object",additionalProperties:!1,properties:{arrowFunction:{type:"boolean"},asyncFunction:{type:"boolean"},bigIntLiteral:{type:"boolean"},const:{type:"boolean"},destructuring:{type:"boolean"},document:{type:"boolean"},dynamicImport:{type:"boolean"},dynamicImportInWorker:{type:"boolean"},forOf:{type:"boolean"},globalThis:{type:"boolean"},module:{type:"boolean"},nodePrefixForCoreModules:{type:"boolean"},optionalChaining:{type:"boolean"},templateLiteral:{type:"boolean"}}},Experiments:{type:"object",additionalProperties:!1,properties:{asyncWebAssembly:{type:"boolean"},backCompat:{type:"boolean"},buildHttp:{anyOf:[{$ref:"#/definitions/HttpUriAllowedUris"},{$ref:"#/definitions/HttpUriOptions"}]},cacheUnaffected:{type:"boolean"},css:{type:"boolean"},deferImport:{type:"boolean"},futureDefaults:{type:"boolean"},layers:{type:"boolean"},lazyCompilation:{anyOf:[{type:"boolean"},{$ref:"#/definitions/LazyCompilationOptions"}]},outputModule:{type:"boolean"},syncWebAssembly:{type:"boolean"},topLevelAwait:{type:"boolean"}}},ExperimentsCommon:{type:"object",additionalProperties:!1,properties:{asyncWebAssembly:{type:"boolean"},backCompat:{type:"boolean"},cacheUnaffected:{type:"boolean"},futureDefaults:{type:"boolean"},layers:{type:"boolean"},outputModule:{type:"boolean"},syncWebAssembly:{type:"boolean"},topLevelAwait:{type:"boolean"}}},ExperimentsNormalized:{type:"object",additionalProperties:!1,properties:{asyncWebAssembly:{type:"boolean"},backCompat:{type:"boolean"},buildHttp:{oneOf:[{$ref:"#/definitions/HttpUriOptions"}]},cacheUnaffected:{type:"boolean"},css:{type:"boolean"},deferImport:{type:"boolean"},futureDefaults:{type:"boolean"},layers:{type:"boolean"},lazyCompilation:{anyOf:[{enum:[!1]},{$ref:"#/definitions/LazyCompilationOptions"}]},outputModule:{type:"boolean"},syncWebAssembly:{type:"boolean"},topLevelAwait:{type:"boolean"}}},Extends:{anyOf:[{type:"array",items:{$ref:"#/definitions/ExtendsItem"}},{$ref:"#/definitions/ExtendsItem"}]},ExtendsItem:{type:"string"},ExternalItem:{anyOf:[{instanceof:"RegExp"},{type:"string"},{type:"object",additionalProperties:{$ref:"#/definitions/ExternalItemValue"},properties:{byLayer:{anyOf:[{type:"object",additionalProperties:{$ref:"#/definitions/ExternalItem"}},{instanceof:"Function"}]}}},{$ref:"#/definitions/ExternalItemFunction"}]},ExternalItemFunction:{anyOf:[{$ref:"#/definitions/ExternalItemFunctionCallback"},{$ref:"#/definitions/ExternalItemFunctionPromise"}]},ExternalItemFunctionCallback:{instanceof:"Function"},ExternalItemFunctionData:{type:"object",additionalProperties:!1,properties:{context:{type:"string"},contextInfo:{type:"object"},dependencyType:{type:"string"},getResolve:{$ref:"#/definitions/ExternalItemFunctionDataGetResolve"},request:{type:"string"}}},ExternalItemFunctionDataGetResolve:{instanceof:"Function"},ExternalItemFunctionDataGetResolveCallbackResult:{instanceof:"Function"},ExternalItemFunctionDataGetResolveResult:{instanceof:"Function"},ExternalItemFunctionPromise:{instanceof:"Function"},ExternalItemValue:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"boolean"},{type:"string"},{type:"object"}]},Externals:{anyOf:[{type:"array",items:{$ref:"#/definitions/ExternalItem"}},{$ref:"#/definitions/ExternalItem"}]},ExternalsPresets:{type:"object",additionalProperties:!1,properties:{electron:{type:"boolean"},electronMain:{type:"boolean"},electronPreload:{type:"boolean"},electronRenderer:{type:"boolean"},node:{type:"boolean"},nwjs:{type:"boolean"},web:{type:"boolean"},webAsync:{type:"boolean"}}},ExternalsType:{enum:["var","module","assign","this","window","self","global","commonjs","commonjs2","commonjs-module","commonjs-static","amd","amd-require","umd","umd2","jsonp","system","promise","import","module-import","script","node-commonjs"]},Falsy:{enum:[!1,0,"",null],undefinedAsNull:!0},FileCacheOptions:{type:"object",additionalProperties:!1,properties:{allowCollectingMemory:{type:"boolean"},buildDependencies:{type:"object",additionalProperties:{type:"array",items:{type:"string",minLength:1}}},cacheDirectory:{type:"string",absolutePath:!0},cacheLocation:{type:"string",absolutePath:!0},compression:{enum:[!1,"gzip","brotli"]},hashAlgorithm:{type:"string"},idleTimeout:{type:"number",minimum:0},idleTimeoutAfterLargeChanges:{type:"number",minimum:0},idleTimeoutForInitialStore:{type:"number",minimum:0},immutablePaths:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}},managedPaths:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}},maxAge:{type:"number",minimum:0},maxMemoryGenerations:{type:"number",minimum:0},memoryCacheUnaffected:{type:"boolean"},name:{type:"string"},profile:{type:"boolean"},readonly:{type:"boolean"},store:{enum:["pack"]},type:{enum:["filesystem"]},version:{type:"string"}},required:["type"]},Filename:{oneOf:[{$ref:"#/definitions/FilenameTemplate"}]},FilenameTemplate:{anyOf:[{type:"string",absolutePath:!1,minLength:1},{instanceof:"Function"}]},FilterItemTypes:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!1},{instanceof:"Function"}]},FilterTypes:{anyOf:[{type:"array",items:{oneOf:[{$ref:"#/definitions/FilterItemTypes"}]}},{$ref:"#/definitions/FilterItemTypes"}]},GeneratorOptionsByModuleType:{type:"object",additionalProperties:{type:"object",additionalProperties:!0},properties:{asset:{$ref:"#/definitions/AssetGeneratorOptions"},"asset/inline":{$ref:"#/definitions/AssetInlineGeneratorOptions"},"asset/resource":{$ref:"#/definitions/AssetResourceGeneratorOptions"},css:{$ref:"#/definitions/CssGeneratorOptions"},"css/auto":{$ref:"#/definitions/CssAutoGeneratorOptions"},"css/global":{$ref:"#/definitions/CssGlobalGeneratorOptions"},"css/module":{$ref:"#/definitions/CssModuleGeneratorOptions"},javascript:{$ref:"#/definitions/EmptyGeneratorOptions"},"javascript/auto":{$ref:"#/definitions/EmptyGeneratorOptions"},"javascript/dynamic":{$ref:"#/definitions/EmptyGeneratorOptions"},"javascript/esm":{$ref:"#/definitions/EmptyGeneratorOptions"},json:{$ref:"#/definitions/JsonGeneratorOptions"}}},GlobalObject:{type:"string",minLength:1},HashDigest:{type:"string"},HashDigestLength:{type:"number",minimum:1},HashFunction:{anyOf:[{type:"string",minLength:1},{instanceof:"Function"}]},HashSalt:{type:"string",minLength:1},HotUpdateChunkFilename:{type:"string",absolutePath:!1},HotUpdateGlobal:{type:"string"},HotUpdateMainFilename:{type:"string",absolutePath:!1},HttpUriAllowedUris:{oneOf:[{$ref:"#/definitions/HttpUriOptionsAllowedUris"}]},HttpUriOptions:{type:"object",additionalProperties:!1,properties:{allowedUris:{$ref:"#/definitions/HttpUriOptionsAllowedUris"},cacheLocation:{anyOf:[{enum:[!1]},{type:"string",absolutePath:!0}]},frozen:{type:"boolean"},lockfileLocation:{type:"string",absolutePath:!0},proxy:{type:"string"},upgrade:{type:"boolean"}},required:["allowedUris"]},HttpUriOptionsAllowedUris:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",pattern:"^https?://"},{instanceof:"Function"}]}},IgnoreWarnings:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"object",additionalProperties:!1,properties:{file:{instanceof:"RegExp"},message:{instanceof:"RegExp"},module:{instanceof:"RegExp"}}},{instanceof:"Function"}]}},IgnoreWarningsNormalized:{type:"array",items:{instanceof:"Function"}},Iife:{type:"boolean"},ImportFunctionName:{type:"string"},ImportMetaName:{type:"string"},InfrastructureLogging:{type:"object",additionalProperties:!1,properties:{appendOnly:{type:"boolean"},colors:{type:"boolean"},console:{},debug:{anyOf:[{type:"boolean"},{$ref:"#/definitions/FilterTypes"}]},level:{enum:["none","error","warn","info","log","verbose"]},stream:{}}},JavascriptParserOptions:{type:"object",additionalProperties:!0,properties:{amd:{$ref:"#/definitions/Amd"},browserify:{type:"boolean"},commonjs:{type:"boolean"},commonjsMagicComments:{type:"boolean"},createRequire:{anyOf:[{type:"boolean"},{type:"string"}]},dynamicImportFetchPriority:{enum:["low","high","auto",!1]},dynamicImportMode:{enum:["eager","weak","lazy","lazy-once"]},dynamicImportPrefetch:{anyOf:[{type:"number"},{type:"boolean"}]},dynamicImportPreload:{anyOf:[{type:"number"},{type:"boolean"}]},dynamicUrl:{type:"boolean"},exportsPresence:{enum:["error","warn","auto",!1]},exprContextCritical:{type:"boolean"},exprContextRecursive:{type:"boolean"},exprContextRegExp:{anyOf:[{instanceof:"RegExp"},{type:"boolean"}]},exprContextRequest:{type:"string"},harmony:{type:"boolean"},import:{type:"boolean"},importExportsPresence:{enum:["error","warn","auto",!1]},importMeta:{type:"boolean"},importMetaContext:{type:"boolean"},node:{$ref:"#/definitions/Node"},overrideStrict:{enum:["strict","non-strict"]},reexportExportsPresence:{enum:["error","warn","auto",!1]},requireContext:{type:"boolean"},requireEnsure:{type:"boolean"},requireInclude:{type:"boolean"},requireJs:{type:"boolean"},strictExportPresence:{type:"boolean"},strictThisContextOnImports:{type:"boolean"},system:{type:"boolean"},unknownContextCritical:{type:"boolean"},unknownContextRecursive:{type:"boolean"},unknownContextRegExp:{anyOf:[{instanceof:"RegExp"},{type:"boolean"}]},unknownContextRequest:{type:"string"},url:{anyOf:[{enum:["relative"]},{type:"boolean"}]},worker:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"boolean"}]},wrappedContextCritical:{type:"boolean"},wrappedContextRecursive:{type:"boolean"},wrappedContextRegExp:{instanceof:"RegExp"}}},JsonGeneratorOptions:{type:"object",additionalProperties:!1,properties:{JSONParse:{type:"boolean"}}},JsonParserOptions:{type:"object",additionalProperties:!1,properties:{exportsDepth:{type:"number"},parse:{instanceof:"Function"}}},Layer:{anyOf:[{enum:[null]},{type:"string",minLength:1}]},LazyCompilationDefaultBackendOptions:{type:"object",additionalProperties:!1,properties:{client:{type:"string"},listen:{anyOf:[{type:"number"},{type:"object",additionalProperties:!0,properties:{host:{type:"string"},port:{type:"number"}}},{instanceof:"Function"}]},protocol:{enum:["http","https"]},server:{anyOf:[{type:"object",additionalProperties:!0,properties:{}},{instanceof:"Function"}]}}},LazyCompilationOptions:{type:"object",additionalProperties:!1,properties:{backend:{anyOf:[{instanceof:"Function"},{$ref:"#/definitions/LazyCompilationDefaultBackendOptions"}]},entries:{type:"boolean"},imports:{type:"boolean"},test:{anyOf:[{instanceof:"RegExp"},{type:"string"},{instanceof:"Function"}]}}},Library:{anyOf:[{$ref:"#/definitions/LibraryName"},{$ref:"#/definitions/LibraryOptions"}]},LibraryCustomUmdCommentObject:{type:"object",additionalProperties:!1,properties:{amd:{type:"string"},commonjs:{type:"string"},commonjs2:{type:"string"},root:{type:"string"}}},LibraryCustomUmdObject:{type:"object",additionalProperties:!1,properties:{amd:{type:"string",minLength:1},commonjs:{type:"string",minLength:1},root:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"string",minLength:1}]}}},LibraryExport:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"string",minLength:1}]},LibraryName:{anyOf:[{type:"array",items:{type:"string",minLength:1},minItems:1},{type:"string",minLength:1},{$ref:"#/definitions/LibraryCustomUmdObject"}]},LibraryOptions:{type:"object",additionalProperties:!1,properties:{amdContainer:{$ref:"#/definitions/AmdContainer"},auxiliaryComment:{$ref:"#/definitions/AuxiliaryComment"},export:{$ref:"#/definitions/LibraryExport"},name:{$ref:"#/definitions/LibraryName"},type:{$ref:"#/definitions/LibraryType"},umdNamedDefine:{$ref:"#/definitions/UmdNamedDefine"}},required:["type"]},LibraryType:{anyOf:[{enum:["var","module","assign","assign-properties","this","window","self","global","commonjs","commonjs2","commonjs-module","commonjs-static","amd","amd-require","umd","umd2","jsonp","system"]},{type:"string"}]},Loader:{type:"object"},MemoryCacheOptions:{type:"object",additionalProperties:!1,properties:{cacheUnaffected:{type:"boolean"},maxGenerations:{type:"number",minimum:1},type:{enum:["memory"]}},required:["type"]},Mode:{enum:["development","production","none"]},ModuleFilterItemTypes:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!1},{instanceof:"Function"}]},ModuleFilterTypes:{anyOf:[{type:"array",items:{oneOf:[{$ref:"#/definitions/ModuleFilterItemTypes"}]}},{$ref:"#/definitions/ModuleFilterItemTypes"}]},ModuleOptions:{type:"object",additionalProperties:!1,properties:{defaultRules:{oneOf:[{$ref:"#/definitions/RuleSetRules"}]},exprContextCritical:{type:"boolean"},exprContextRecursive:{type:"boolean"},exprContextRegExp:{anyOf:[{instanceof:"RegExp"},{type:"boolean"}]},exprContextRequest:{type:"string"},generator:{$ref:"#/definitions/GeneratorOptionsByModuleType"},noParse:{$ref:"#/definitions/NoParse"},parser:{$ref:"#/definitions/ParserOptionsByModuleType"},rules:{oneOf:[{$ref:"#/definitions/RuleSetRules"}]},strictExportPresence:{type:"boolean"},strictThisContextOnImports:{type:"boolean"},unknownContextCritical:{type:"boolean"},unknownContextRecursive:{type:"boolean"},unknownContextRegExp:{anyOf:[{instanceof:"RegExp"},{type:"boolean"}]},unknownContextRequest:{type:"string"},unsafeCache:{anyOf:[{type:"boolean"},{instanceof:"Function"}]},wrappedContextCritical:{type:"boolean"},wrappedContextRecursive:{type:"boolean"},wrappedContextRegExp:{instanceof:"RegExp"}}},ModuleOptionsNormalized:{type:"object",additionalProperties:!1,properties:{defaultRules:{oneOf:[{$ref:"#/definitions/RuleSetRules"}]},generator:{$ref:"#/definitions/GeneratorOptionsByModuleType"},noParse:{$ref:"#/definitions/NoParse"},parser:{$ref:"#/definitions/ParserOptionsByModuleType"},rules:{oneOf:[{$ref:"#/definitions/RuleSetRules"}]},unsafeCache:{anyOf:[{type:"boolean"},{instanceof:"Function"}]}},required:["defaultRules","generator","parser","rules"]},Name:{type:"string"},NoParse:{anyOf:[{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0},{instanceof:"Function"}]},minItems:1},{instanceof:"RegExp"},{type:"string",absolutePath:!0},{instanceof:"Function"}]},Node:{anyOf:[{enum:[!1]},{$ref:"#/definitions/NodeOptions"}]},NodeOptions:{type:"object",additionalProperties:!1,properties:{__dirname:{enum:[!1,!0,"warn-mock","mock","node-module","eval-only"]},__filename:{enum:[!1,!0,"warn-mock","mock","node-module","eval-only"]},global:{enum:[!1,!0,"warn"]}}},Optimization:{type:"object",additionalProperties:!1,properties:{avoidEntryIife:{type:"boolean"},checkWasmTypes:{type:"boolean"},chunkIds:{enum:["natural","named","deterministic","size","total-size",!1]},concatenateModules:{type:"boolean"},emitOnErrors:{type:"boolean"},flagIncludedChunks:{type:"boolean"},innerGraph:{type:"boolean"},mangleExports:{anyOf:[{enum:["size","deterministic"]},{type:"boolean"}]},mangleWasmImports:{type:"boolean"},mergeDuplicateChunks:{type:"boolean"},minimize:{type:"boolean"},minimizer:{type:"array",items:{anyOf:[{enum:["..."]},{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/WebpackPluginInstance"},{$ref:"#/definitions/WebpackPluginFunction"}]}},moduleIds:{enum:["natural","named","hashed","deterministic","size",!1]},noEmitOnErrors:{type:"boolean"},nodeEnv:{anyOf:[{enum:[!1]},{type:"string"}]},portableRecords:{type:"boolean"},providedExports:{type:"boolean"},realContentHash:{type:"boolean"},removeAvailableModules:{type:"boolean"},removeEmptyChunks:{type:"boolean"},runtimeChunk:{$ref:"#/definitions/OptimizationRuntimeChunk"},sideEffects:{anyOf:[{enum:["flag"]},{type:"boolean"}]},splitChunks:{anyOf:[{enum:[!1]},{$ref:"#/definitions/OptimizationSplitChunksOptions"}]},usedExports:{anyOf:[{enum:["global"]},{type:"boolean"}]}}},OptimizationNormalized:{type:"object",additionalProperties:!1,properties:{avoidEntryIife:{type:"boolean"},checkWasmTypes:{type:"boolean"},chunkIds:{enum:["natural","named","deterministic","size","total-size",!1]},concatenateModules:{type:"boolean"},emitOnErrors:{type:"boolean"},flagIncludedChunks:{type:"boolean"},innerGraph:{type:"boolean"},mangleExports:{anyOf:[{enum:["size","deterministic"]},{type:"boolean"}]},mangleWasmImports:{type:"boolean"},mergeDuplicateChunks:{type:"boolean"},minimize:{type:"boolean"},minimizer:{type:"array",items:{anyOf:[{enum:["..."]},{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/WebpackPluginInstance"},{$ref:"#/definitions/WebpackPluginFunction"}]}},moduleIds:{enum:["natural","named","hashed","deterministic","size",!1]},noEmitOnErrors:{type:"boolean"},nodeEnv:{anyOf:[{enum:[!1]},{type:"string"}]},portableRecords:{type:"boolean"},providedExports:{type:"boolean"},realContentHash:{type:"boolean"},removeAvailableModules:{type:"boolean"},removeEmptyChunks:{type:"boolean"},runtimeChunk:{$ref:"#/definitions/OptimizationRuntimeChunkNormalized"},sideEffects:{anyOf:[{enum:["flag"]},{type:"boolean"}]},splitChunks:{anyOf:[{enum:[!1]},{$ref:"#/definitions/OptimizationSplitChunksOptions"}]},usedExports:{anyOf:[{enum:["global"]},{type:"boolean"}]}}},OptimizationRuntimeChunk:{anyOf:[{enum:["single","multiple"]},{type:"boolean"},{type:"object",additionalProperties:!1,properties:{name:{anyOf:[{type:"string"},{instanceof:"Function"}]}}}]},OptimizationRuntimeChunkNormalized:{anyOf:[{enum:[!1]},{type:"object",additionalProperties:!1,properties:{name:{instanceof:"Function"}}}]},OptimizationSplitChunksCacheGroup:{type:"object",additionalProperties:!1,properties:{automaticNameDelimiter:{type:"string",minLength:1},chunks:{anyOf:[{enum:["initial","async","all"]},{instanceof:"RegExp"},{instanceof:"Function"}]},enforce:{type:"boolean"},enforceSizeThreshold:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},filename:{anyOf:[{type:"string",absolutePath:!1,minLength:1},{instanceof:"Function"}]},idHint:{type:"string"},layer:{anyOf:[{instanceof:"RegExp"},{type:"string"},{instanceof:"Function"}]},maxAsyncRequests:{type:"number",minimum:1},maxAsyncSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxInitialRequests:{type:"number",minimum:1},maxInitialSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minChunks:{type:"number",minimum:1},minRemainingSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSizeReduction:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},name:{anyOf:[{enum:[!1]},{type:"string"},{instanceof:"Function"}]},priority:{type:"number"},reuseExistingChunk:{type:"boolean"},test:{anyOf:[{instanceof:"RegExp"},{type:"string"},{instanceof:"Function"}]},type:{anyOf:[{instanceof:"RegExp"},{type:"string"},{instanceof:"Function"}]},usedExports:{type:"boolean"}}},OptimizationSplitChunksGetCacheGroups:{instanceof:"Function"},OptimizationSplitChunksOptions:{type:"object",additionalProperties:!1,properties:{automaticNameDelimiter:{type:"string",minLength:1},cacheGroups:{type:"object",additionalProperties:{anyOf:[{enum:[!1]},{instanceof:"RegExp"},{type:"string"},{$ref:"#/definitions/OptimizationSplitChunksGetCacheGroups"},{$ref:"#/definitions/OptimizationSplitChunksCacheGroup"}]},not:{type:"object",additionalProperties:!0,properties:{test:{anyOf:[{instanceof:"RegExp"},{type:"string"},{$ref:"#/definitions/OptimizationSplitChunksGetCacheGroups"}]}},required:["test"]}},chunks:{anyOf:[{enum:["initial","async","all"]},{instanceof:"RegExp"},{instanceof:"Function"}]},defaultSizeTypes:{type:"array",items:{type:"string"},minItems:1},enforceSizeThreshold:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},fallbackCacheGroup:{type:"object",additionalProperties:!1,properties:{automaticNameDelimiter:{type:"string",minLength:1},chunks:{anyOf:[{enum:["initial","async","all"]},{instanceof:"RegExp"},{instanceof:"Function"}]},maxAsyncSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxInitialSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSizeReduction:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]}}},filename:{anyOf:[{type:"string",absolutePath:!1,minLength:1},{instanceof:"Function"}]},hidePathInfo:{type:"boolean"},maxAsyncRequests:{type:"number",minimum:1},maxAsyncSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxInitialRequests:{type:"number",minimum:1},maxInitialSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minChunks:{type:"number",minimum:1},minRemainingSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSizeReduction:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},name:{anyOf:[{enum:[!1]},{type:"string"},{instanceof:"Function"}]},usedExports:{type:"boolean"}}},OptimizationSplitChunksSizes:{anyOf:[{type:"number",minimum:0},{type:"object",additionalProperties:{type:"number"}}]},Output:{type:"object",additionalProperties:!1,properties:{amdContainer:{oneOf:[{$ref:"#/definitions/AmdContainer"}]},assetModuleFilename:{$ref:"#/definitions/AssetModuleFilename"},asyncChunks:{type:"boolean"},auxiliaryComment:{oneOf:[{$ref:"#/definitions/AuxiliaryComment"}]},charset:{$ref:"#/definitions/Charset"},chunkFilename:{$ref:"#/definitions/ChunkFilename"},chunkFormat:{$ref:"#/definitions/ChunkFormat"},chunkLoadTimeout:{$ref:"#/definitions/ChunkLoadTimeout"},chunkLoading:{$ref:"#/definitions/ChunkLoading"},chunkLoadingGlobal:{$ref:"#/definitions/ChunkLoadingGlobal"},clean:{$ref:"#/definitions/Clean"},compareBeforeEmit:{$ref:"#/definitions/CompareBeforeEmit"},crossOriginLoading:{$ref:"#/definitions/CrossOriginLoading"},cssChunkFilename:{$ref:"#/definitions/CssChunkFilename"},cssFilename:{$ref:"#/definitions/CssFilename"},devtoolFallbackModuleFilenameTemplate:{$ref:"#/definitions/DevtoolFallbackModuleFilenameTemplate"},devtoolModuleFilenameTemplate:{$ref:"#/definitions/DevtoolModuleFilenameTemplate"},devtoolNamespace:{$ref:"#/definitions/DevtoolNamespace"},enabledChunkLoadingTypes:{$ref:"#/definitions/EnabledChunkLoadingTypes"},enabledLibraryTypes:{$ref:"#/definitions/EnabledLibraryTypes"},enabledWasmLoadingTypes:{$ref:"#/definitions/EnabledWasmLoadingTypes"},environment:{$ref:"#/definitions/Environment"},filename:{$ref:"#/definitions/Filename"},globalObject:{$ref:"#/definitions/GlobalObject"},hashDigest:{$ref:"#/definitions/HashDigest"},hashDigestLength:{$ref:"#/definitions/HashDigestLength"},hashFunction:{$ref:"#/definitions/HashFunction"},hashSalt:{$ref:"#/definitions/HashSalt"},hotUpdateChunkFilename:{$ref:"#/definitions/HotUpdateChunkFilename"},hotUpdateGlobal:{$ref:"#/definitions/HotUpdateGlobal"},hotUpdateMainFilename:{$ref:"#/definitions/HotUpdateMainFilename"},ignoreBrowserWarnings:{type:"boolean"},iife:{$ref:"#/definitions/Iife"},importFunctionName:{$ref:"#/definitions/ImportFunctionName"},importMetaName:{$ref:"#/definitions/ImportMetaName"},library:{$ref:"#/definitions/Library"},libraryExport:{oneOf:[{$ref:"#/definitions/LibraryExport"}]},libraryTarget:{oneOf:[{$ref:"#/definitions/LibraryType"}]},module:{$ref:"#/definitions/OutputModule"},path:{$ref:"#/definitions/Path"},pathinfo:{$ref:"#/definitions/Pathinfo"},publicPath:{$ref:"#/definitions/PublicPath"},scriptType:{$ref:"#/definitions/ScriptType"},sourceMapFilename:{$ref:"#/definitions/SourceMapFilename"},sourcePrefix:{$ref:"#/definitions/SourcePrefix"},strictModuleErrorHandling:{$ref:"#/definitions/StrictModuleErrorHandling"},strictModuleExceptionHandling:{$ref:"#/definitions/StrictModuleExceptionHandling"},trustedTypes:{anyOf:[{enum:[!0]},{type:"string",minLength:1},{$ref:"#/definitions/TrustedTypes"}]},umdNamedDefine:{oneOf:[{$ref:"#/definitions/UmdNamedDefine"}]},uniqueName:{$ref:"#/definitions/UniqueName"},wasmLoading:{$ref:"#/definitions/WasmLoading"},webassemblyModuleFilename:{$ref:"#/definitions/WebassemblyModuleFilename"},workerChunkLoading:{$ref:"#/definitions/ChunkLoading"},workerPublicPath:{$ref:"#/definitions/WorkerPublicPath"},workerWasmLoading:{$ref:"#/definitions/WasmLoading"}}},OutputModule:{type:"boolean"},OutputNormalized:{type:"object",additionalProperties:!1,properties:{assetModuleFilename:{$ref:"#/definitions/AssetModuleFilename"},asyncChunks:{type:"boolean"},charset:{$ref:"#/definitions/Charset"},chunkFilename:{$ref:"#/definitions/ChunkFilename"},chunkFormat:{$ref:"#/definitions/ChunkFormat"},chunkLoadTimeout:{$ref:"#/definitions/ChunkLoadTimeout"},chunkLoading:{$ref:"#/definitions/ChunkLoading"},chunkLoadingGlobal:{$ref:"#/definitions/ChunkLoadingGlobal"},clean:{$ref:"#/definitions/Clean"},compareBeforeEmit:{$ref:"#/definitions/CompareBeforeEmit"},crossOriginLoading:{$ref:"#/definitions/CrossOriginLoading"},cssChunkFilename:{$ref:"#/definitions/CssChunkFilename"},cssFilename:{$ref:"#/definitions/CssFilename"},devtoolFallbackModuleFilenameTemplate:{$ref:"#/definitions/DevtoolFallbackModuleFilenameTemplate"},devtoolModuleFilenameTemplate:{$ref:"#/definitions/DevtoolModuleFilenameTemplate"},devtoolNamespace:{$ref:"#/definitions/DevtoolNamespace"},enabledChunkLoadingTypes:{$ref:"#/definitions/EnabledChunkLoadingTypes"},enabledLibraryTypes:{$ref:"#/definitions/EnabledLibraryTypes"},enabledWasmLoadingTypes:{$ref:"#/definitions/EnabledWasmLoadingTypes"},environment:{$ref:"#/definitions/Environment"},filename:{$ref:"#/definitions/Filename"},globalObject:{$ref:"#/definitions/GlobalObject"},hashDigest:{$ref:"#/definitions/HashDigest"},hashDigestLength:{$ref:"#/definitions/HashDigestLength"},hashFunction:{$ref:"#/definitions/HashFunction"},hashSalt:{$ref:"#/definitions/HashSalt"},hotUpdateChunkFilename:{$ref:"#/definitions/HotUpdateChunkFilename"},hotUpdateGlobal:{$ref:"#/definitions/HotUpdateGlobal"},hotUpdateMainFilename:{$ref:"#/definitions/HotUpdateMainFilename"},ignoreBrowserWarnings:{type:"boolean"},iife:{$ref:"#/definitions/Iife"},importFunctionName:{$ref:"#/definitions/ImportFunctionName"},importMetaName:{$ref:"#/definitions/ImportMetaName"},library:{$ref:"#/definitions/LibraryOptions"},module:{$ref:"#/definitions/OutputModule"},path:{$ref:"#/definitions/Path"},pathinfo:{$ref:"#/definitions/Pathinfo"},publicPath:{$ref:"#/definitions/PublicPath"},scriptType:{$ref:"#/definitions/ScriptType"},sourceMapFilename:{$ref:"#/definitions/SourceMapFilename"},sourcePrefix:{$ref:"#/definitions/SourcePrefix"},strictModuleErrorHandling:{$ref:"#/definitions/StrictModuleErrorHandling"},strictModuleExceptionHandling:{$ref:"#/definitions/StrictModuleExceptionHandling"},trustedTypes:{$ref:"#/definitions/TrustedTypes"},uniqueName:{$ref:"#/definitions/UniqueName"},wasmLoading:{$ref:"#/definitions/WasmLoading"},webassemblyModuleFilename:{$ref:"#/definitions/WebassemblyModuleFilename"},workerChunkLoading:{$ref:"#/definitions/ChunkLoading"},workerPublicPath:{$ref:"#/definitions/WorkerPublicPath"},workerWasmLoading:{$ref:"#/definitions/WasmLoading"}},required:["environment","enabledChunkLoadingTypes","enabledLibraryTypes","enabledWasmLoadingTypes"]},Parallelism:{type:"number",minimum:1},ParserOptionsByModuleType:{type:"object",additionalProperties:{type:"object",additionalProperties:!0},properties:{asset:{$ref:"#/definitions/AssetParserOptions"},"asset/inline":{$ref:"#/definitions/EmptyParserOptions"},"asset/resource":{$ref:"#/definitions/EmptyParserOptions"},"asset/source":{$ref:"#/definitions/EmptyParserOptions"},css:{$ref:"#/definitions/CssParserOptions"},"css/auto":{$ref:"#/definitions/CssAutoParserOptions"},"css/global":{$ref:"#/definitions/CssGlobalParserOptions"},"css/module":{$ref:"#/definitions/CssModuleParserOptions"},javascript:{$ref:"#/definitions/JavascriptParserOptions"},"javascript/auto":{$ref:"#/definitions/JavascriptParserOptions"},"javascript/dynamic":{$ref:"#/definitions/JavascriptParserOptions"},"javascript/esm":{$ref:"#/definitions/JavascriptParserOptions"},json:{$ref:"#/definitions/JsonParserOptions"}}},Path:{type:"string",absolutePath:!0},Pathinfo:{anyOf:[{enum:["verbose"]},{type:"boolean"}]},Performance:{anyOf:[{enum:[!1]},{$ref:"#/definitions/PerformanceOptions"}]},PerformanceOptions:{type:"object",additionalProperties:!1,properties:{assetFilter:{instanceof:"Function"},hints:{enum:[!1,"warning","error"]},maxAssetSize:{type:"number"},maxEntrypointSize:{type:"number"}}},Plugins:{type:"array",items:{anyOf:[{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/WebpackPluginInstance"},{$ref:"#/definitions/WebpackPluginFunction"}]}},Profile:{type:"boolean"},PublicPath:{anyOf:[{enum:["auto"]},{$ref:"#/definitions/RawPublicPath"}]},RawPublicPath:{anyOf:[{type:"string"},{instanceof:"Function"}]},RecordsInputPath:{anyOf:[{enum:[!1]},{type:"string",absolutePath:!0}]},RecordsOutputPath:{anyOf:[{enum:[!1]},{type:"string",absolutePath:!0}]},RecordsPath:{anyOf:[{enum:[!1]},{type:"string",absolutePath:!0}]},Resolve:{oneOf:[{$ref:"#/definitions/ResolveOptions"}]},ResolveAlias:{anyOf:[{type:"array",items:{type:"object",additionalProperties:!1,properties:{alias:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{enum:[!1]},{type:"string",minLength:1}]},name:{type:"string"},onlyModule:{type:"boolean"}},required:["alias","name"]}},{type:"object",additionalProperties:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{enum:[!1]},{type:"string",minLength:1}]}}]},ResolveLoader:{oneOf:[{$ref:"#/definitions/ResolveOptions"}]},ResolveOptions:{type:"object",additionalProperties:!1,properties:{alias:{$ref:"#/definitions/ResolveAlias"},aliasFields:{type:"array",items:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"string",minLength:1}]}},byDependency:{type:"object",additionalProperties:{oneOf:[{$ref:"#/definitions/ResolveOptions"}]}},cache:{type:"boolean"},cachePredicate:{instanceof:"Function"},cacheWithContext:{type:"boolean"},conditionNames:{type:"array",items:{type:"string"}},descriptionFiles:{type:"array",items:{type:"string",minLength:1}},enforceExtension:{type:"boolean"},exportsFields:{type:"array",items:{type:"string"}},extensionAlias:{type:"object",additionalProperties:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"string",minLength:1}]}},extensions:{type:"array",items:{type:"string"}},fallback:{oneOf:[{$ref:"#/definitions/ResolveAlias"}]},fileSystem:{},fullySpecified:{type:"boolean"},importsFields:{type:"array",items:{type:"string"}},mainFields:{type:"array",items:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"string",minLength:1}]}},mainFiles:{type:"array",items:{type:"string",minLength:1}},modules:{type:"array",items:{type:"string",minLength:1}},plugins:{type:"array",items:{anyOf:[{enum:["..."]},{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/ResolvePluginInstance"}]}},preferAbsolute:{type:"boolean"},preferRelative:{type:"boolean"},resolver:{},restrictions:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}},roots:{type:"array",items:{type:"string"}},symlinks:{type:"boolean"},unsafeCache:{anyOf:[{type:"boolean"},{type:"object",additionalProperties:!0}]},useSyncFileSystemCalls:{type:"boolean"}}},ResolvePluginInstance:{anyOf:[{type:"object",additionalProperties:!0,properties:{apply:{instanceof:"Function"}},required:["apply"]},{instanceof:"Function"}]},RuleSetCondition:{anyOf:[{instanceof:"RegExp"},{type:"string"},{instanceof:"Function"},{$ref:"#/definitions/RuleSetLogicalConditions"},{$ref:"#/definitions/RuleSetConditions"}]},RuleSetConditionAbsolute:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0},{instanceof:"Function"},{$ref:"#/definitions/RuleSetLogicalConditionsAbsolute"},{$ref:"#/definitions/RuleSetConditionsAbsolute"}]},RuleSetConditionOrConditions:{anyOf:[{$ref:"#/definitions/RuleSetCondition"},{$ref:"#/definitions/RuleSetConditions"}]},RuleSetConditionOrConditionsAbsolute:{anyOf:[{$ref:"#/definitions/RuleSetConditionAbsolute"},{$ref:"#/definitions/RuleSetConditionsAbsolute"}]},RuleSetConditions:{type:"array",items:{oneOf:[{$ref:"#/definitions/RuleSetCondition"}]}},RuleSetConditionsAbsolute:{type:"array",items:{oneOf:[{$ref:"#/definitions/RuleSetConditionAbsolute"}]}},RuleSetLoader:{type:"string",minLength:1},RuleSetLoaderOptions:{anyOf:[{type:"string"},{type:"object"}]},RuleSetLogicalConditions:{type:"object",additionalProperties:!1,properties:{and:{oneOf:[{$ref:"#/definitions/RuleSetConditions"}]},not:{oneOf:[{$ref:"#/definitions/RuleSetCondition"}]},or:{oneOf:[{$ref:"#/definitions/RuleSetConditions"}]}}},RuleSetLogicalConditionsAbsolute:{type:"object",additionalProperties:!1,properties:{and:{oneOf:[{$ref:"#/definitions/RuleSetConditionsAbsolute"}]},not:{oneOf:[{$ref:"#/definitions/RuleSetConditionAbsolute"}]},or:{oneOf:[{$ref:"#/definitions/RuleSetConditionsAbsolute"}]}}},RuleSetRule:{type:"object",additionalProperties:!1,properties:{assert:{type:"object",additionalProperties:{$ref:"#/definitions/RuleSetConditionOrConditions"}},compiler:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},dependency:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},descriptionData:{type:"object",additionalProperties:{$ref:"#/definitions/RuleSetConditionOrConditions"}},enforce:{enum:["pre","post"]},exclude:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},generator:{type:"object"},include:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},issuer:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},issuerLayer:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},layer:{type:"string"},loader:{oneOf:[{$ref:"#/definitions/RuleSetLoader"}]},mimetype:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},oneOf:{type:"array",items:{anyOf:[{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/RuleSetRule"}]}},options:{oneOf:[{$ref:"#/definitions/RuleSetLoaderOptions"}]},parser:{type:"object",additionalProperties:!0},realResource:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},resolve:{type:"object",oneOf:[{$ref:"#/definitions/ResolveOptions"}]},resource:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},resourceFragment:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},resourceQuery:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},rules:{type:"array",items:{anyOf:[{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/RuleSetRule"}]}},scheme:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},sideEffects:{type:"boolean"},test:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},type:{type:"string"},use:{oneOf:[{$ref:"#/definitions/RuleSetUse"}]},with:{type:"object",additionalProperties:{$ref:"#/definitions/RuleSetConditionOrConditions"}}}},RuleSetRules:{type:"array",items:{anyOf:[{enum:["..."]},{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/RuleSetRule"}]}},RuleSetUse:{anyOf:[{type:"array",items:{anyOf:[{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/RuleSetUseItem"}]}},{$ref:"#/definitions/RuleSetUseFunction"},{$ref:"#/definitions/RuleSetUseItem"}]},RuleSetUseFunction:{instanceof:"Function"},RuleSetUseItem:{anyOf:[{type:"object",additionalProperties:!1,properties:{ident:{type:"string"},loader:{oneOf:[{$ref:"#/definitions/RuleSetLoader"}]},options:{oneOf:[{$ref:"#/definitions/RuleSetLoaderOptions"}]}}},{$ref:"#/definitions/RuleSetUseFunction"},{$ref:"#/definitions/RuleSetLoader"}]},ScriptType:{enum:[!1,"text/javascript","module"]},SnapshotOptions:{type:"object",additionalProperties:!1,properties:{buildDependencies:{type:"object",additionalProperties:!1,properties:{hash:{type:"boolean"},timestamp:{type:"boolean"}}},immutablePaths:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}},managedPaths:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}},module:{type:"object",additionalProperties:!1,properties:{hash:{type:"boolean"},timestamp:{type:"boolean"}}},resolve:{type:"object",additionalProperties:!1,properties:{hash:{type:"boolean"},timestamp:{type:"boolean"}}},resolveBuildDependencies:{type:"object",additionalProperties:!1,properties:{hash:{type:"boolean"},timestamp:{type:"boolean"}}},unmanagedPaths:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}}}},SourceMapFilename:{type:"string",absolutePath:!1},SourcePrefix:{type:"string"},StatsOptions:{type:"object",additionalProperties:!1,properties:{all:{type:"boolean"},assets:{type:"boolean"},assetsSort:{anyOf:[{enum:[!1]},{type:"string"}]},assetsSpace:{type:"number"},builtAt:{type:"boolean"},cached:{type:"boolean"},cachedAssets:{type:"boolean"},cachedModules:{type:"boolean"},children:{type:"boolean"},chunkGroupAuxiliary:{type:"boolean"},chunkGroupChildren:{type:"boolean"},chunkGroupMaxAssets:{type:"number"},chunkGroups:{type:"boolean"},chunkModules:{type:"boolean"},chunkModulesSpace:{type:"number"},chunkOrigins:{type:"boolean"},chunkRelations:{type:"boolean"},chunks:{type:"boolean"},chunksSort:{anyOf:[{enum:[!1]},{type:"string"}]},colors:{anyOf:[{type:"boolean"},{type:"object",additionalProperties:!1,properties:{bold:{type:"string"},cyan:{type:"string"},green:{type:"string"},magenta:{type:"string"},red:{type:"string"},yellow:{type:"string"}}}]},context:{type:"string",absolutePath:!0},dependentModules:{type:"boolean"},depth:{type:"boolean"},entrypoints:{anyOf:[{enum:["auto"]},{type:"boolean"}]},env:{type:"boolean"},errorCause:{anyOf:[{enum:["auto"]},{type:"boolean"}]},errorDetails:{anyOf:[{enum:["auto"]},{type:"boolean"}]},errorErrors:{anyOf:[{enum:["auto"]},{type:"boolean"}]},errorStack:{type:"boolean"},errors:{type:"boolean"},errorsCount:{type:"boolean"},errorsSpace:{type:"number"},exclude:{anyOf:[{type:"boolean"},{$ref:"#/definitions/ModuleFilterTypes"}]},excludeAssets:{oneOf:[{$ref:"#/definitions/AssetFilterTypes"}]},excludeModules:{anyOf:[{type:"boolean"},{$ref:"#/definitions/ModuleFilterTypes"}]},groupAssetsByChunk:{type:"boolean"},groupAssetsByEmitStatus:{type:"boolean"},groupAssetsByExtension:{type:"boolean"},groupAssetsByInfo:{type:"boolean"},groupAssetsByPath:{type:"boolean"},groupModulesByAttributes:{type:"boolean"},groupModulesByCacheStatus:{type:"boolean"},groupModulesByExtension:{type:"boolean"},groupModulesByLayer:{type:"boolean"},groupModulesByPath:{type:"boolean"},groupModulesByType:{type:"boolean"},groupReasonsByOrigin:{type:"boolean"},hash:{type:"boolean"},ids:{type:"boolean"},logging:{anyOf:[{enum:["none","error","warn","info","log","verbose"]},{type:"boolean"}]},loggingDebug:{anyOf:[{type:"boolean"},{$ref:"#/definitions/FilterTypes"}]},loggingTrace:{type:"boolean"},moduleAssets:{type:"boolean"},moduleTrace:{type:"boolean"},modules:{type:"boolean"},modulesSort:{anyOf:[{enum:[!1]},{type:"string"}]},modulesSpace:{type:"number"},nestedModules:{type:"boolean"},nestedModulesSpace:{type:"number"},optimizationBailout:{type:"boolean"},orphanModules:{type:"boolean"},outputPath:{type:"boolean"},performance:{type:"boolean"},preset:{anyOf:[{type:"boolean"},{type:"string"}]},providedExports:{type:"boolean"},publicPath:{type:"boolean"},reasons:{type:"boolean"},reasonsSpace:{type:"number"},relatedAssets:{type:"boolean"},runtime:{type:"boolean"},runtimeModules:{type:"boolean"},source:{type:"boolean"},timings:{type:"boolean"},usedExports:{type:"boolean"},version:{type:"boolean"},warnings:{type:"boolean"},warningsCount:{type:"boolean"},warningsFilter:{oneOf:[{$ref:"#/definitions/WarningFilterTypes"}]},warningsSpace:{type:"number"}}},StatsValue:{anyOf:[{enum:["none","summary","errors-only","errors-warnings","minimal","normal","detailed","verbose"]},{type:"boolean"},{$ref:"#/definitions/StatsOptions"}]},StrictModuleErrorHandling:{type:"boolean"},StrictModuleExceptionHandling:{type:"boolean"},Target:{anyOf:[{type:"array",items:{type:"string",minLength:1},minItems:1},{enum:[!1]},{type:"string",minLength:1}]},TrustedTypes:{type:"object",additionalProperties:!1,properties:{onPolicyCreationFailure:{enum:["continue","stop"]},policyName:{type:"string",minLength:1}}},UmdNamedDefine:{type:"boolean"},UniqueName:{type:"string",minLength:1},WarningFilterItemTypes:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!1},{instanceof:"Function"}]},WarningFilterTypes:{anyOf:[{type:"array",items:{oneOf:[{$ref:"#/definitions/WarningFilterItemTypes"}]}},{$ref:"#/definitions/WarningFilterItemTypes"}]},WasmLoading:{anyOf:[{enum:[!1]},{$ref:"#/definitions/WasmLoadingType"}]},WasmLoadingType:{anyOf:[{enum:["fetch","async-node"]},{type:"string"}]},Watch:{type:"boolean"},WatchOptions:{type:"object",additionalProperties:!1,properties:{aggregateTimeout:{type:"number"},followSymlinks:{type:"boolean"},ignored:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{instanceof:"RegExp"},{type:"string",minLength:1}]},poll:{anyOf:[{type:"number"},{type:"boolean"}]},stdin:{type:"boolean"}}},WebassemblyModuleFilename:{type:"string",absolutePath:!1},WebpackOptionsNormalized:{type:"object",additionalProperties:!1,properties:{amd:{$ref:"#/definitions/Amd"},bail:{$ref:"#/definitions/Bail"},cache:{$ref:"#/definitions/CacheOptionsNormalized"},context:{$ref:"#/definitions/Context"},dependencies:{$ref:"#/definitions/Dependencies"},devServer:{$ref:"#/definitions/DevServer"},devtool:{$ref:"#/definitions/DevTool"},entry:{$ref:"#/definitions/EntryNormalized"},experiments:{$ref:"#/definitions/ExperimentsNormalized"},externals:{$ref:"#/definitions/Externals"},externalsPresets:{$ref:"#/definitions/ExternalsPresets"},externalsType:{$ref:"#/definitions/ExternalsType"},ignoreWarnings:{$ref:"#/definitions/IgnoreWarningsNormalized"},infrastructureLogging:{$ref:"#/definitions/InfrastructureLogging"},loader:{$ref:"#/definitions/Loader"},mode:{$ref:"#/definitions/Mode"},module:{$ref:"#/definitions/ModuleOptionsNormalized"},name:{$ref:"#/definitions/Name"},node:{$ref:"#/definitions/Node"},optimization:{$ref:"#/definitions/OptimizationNormalized"},output:{$ref:"#/definitions/OutputNormalized"},parallelism:{$ref:"#/definitions/Parallelism"},performance:{$ref:"#/definitions/Performance"},plugins:{$ref:"#/definitions/Plugins"},profile:{$ref:"#/definitions/Profile"},recordsInputPath:{$ref:"#/definitions/RecordsInputPath"},recordsOutputPath:{$ref:"#/definitions/RecordsOutputPath"},resolve:{$ref:"#/definitions/Resolve"},resolveLoader:{$ref:"#/definitions/ResolveLoader"},snapshot:{$ref:"#/definitions/SnapshotOptions"},stats:{$ref:"#/definitions/StatsValue"},target:{$ref:"#/definitions/Target"},watch:{$ref:"#/definitions/Watch"},watchOptions:{$ref:"#/definitions/WatchOptions"}},required:["cache","snapshot","entry","experiments","externals","externalsPresets","infrastructureLogging","module","node","optimization","output","plugins","resolve","resolveLoader","stats","watchOptions"]},WebpackPluginFunction:{instanceof:"Function"},WebpackPluginInstance:{type:"object",additionalProperties:!0,properties:{apply:{instanceof:"Function"}},required:["apply"]},WorkerPublicPath:{type:"string"}},type:"object",additionalProperties:!1,properties:{amd:{$ref:"#/definitions/Amd"},bail:{$ref:"#/definitions/Bail"},cache:{$ref:"#/definitions/CacheOptions"},context:{$ref:"#/definitions/Context"},dependencies:{$ref:"#/definitions/Dependencies"},devServer:{$ref:"#/definitions/DevServer"},devtool:{$ref:"#/definitions/DevTool"},entry:{$ref:"#/definitions/Entry"},experiments:{$ref:"#/definitions/Experiments"},extends:{$ref:"#/definitions/Extends"},externals:{$ref:"#/definitions/Externals"},externalsPresets:{$ref:"#/definitions/ExternalsPresets"},externalsType:{$ref:"#/definitions/ExternalsType"},ignoreWarnings:{$ref:"#/definitions/IgnoreWarnings"},infrastructureLogging:{$ref:"#/definitions/InfrastructureLogging"},loader:{$ref:"#/definitions/Loader"},mode:{$ref:"#/definitions/Mode"},module:{$ref:"#/definitions/ModuleOptions"},name:{$ref:"#/definitions/Name"},node:{$ref:"#/definitions/Node"},optimization:{$ref:"#/definitions/Optimization"},output:{$ref:"#/definitions/Output"},parallelism:{$ref:"#/definitions/Parallelism"},performance:{$ref:"#/definitions/Performance"},plugins:{$ref:"#/definitions/Plugins"},profile:{$ref:"#/definitions/Profile"},recordsInputPath:{$ref:"#/definitions/RecordsInputPath"},recordsOutputPath:{$ref:"#/definitions/RecordsOutputPath"},recordsPath:{$ref:"#/definitions/RecordsPath"},resolve:{$ref:"#/definitions/Resolve"},resolveLoader:{$ref:"#/definitions/ResolveLoader"},snapshot:{$ref:"#/definitions/SnapshotOptions"},stats:{$ref:"#/definitions/StatsValue"},target:{$ref:"#/definitions/Target"},watch:{$ref:"#/definitions/Watch"},watchOptions:{$ref:"#/definitions/WatchOptions"}}},n=Object.prototype.hasOwnProperty,r={type:"object",additionalProperties:!1,properties:{allowCollectingMemory:{type:"boolean"},buildDependencies:{type:"object",additionalProperties:{type:"array",items:{type:"string",minLength:1}}},cacheDirectory:{type:"string",absolutePath:!0},cacheLocation:{type:"string",absolutePath:!0},compression:{enum:[!1,"gzip","brotli"]},hashAlgorithm:{type:"string"},idleTimeout:{type:"number",minimum:0},idleTimeoutAfterLargeChanges:{type:"number",minimum:0},idleTimeoutForInitialStore:{type:"number",minimum:0},immutablePaths:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}},managedPaths:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}},maxAge:{type:"number",minimum:0},maxMemoryGenerations:{type:"number",minimum:0},memoryCacheUnaffected:{type:"boolean"},name:{type:"string"},profile:{type:"boolean"},readonly:{type:"boolean"},store:{enum:["pack"]},type:{enum:["filesystem"]},version:{type:"string"}},required:["type"]};function o(t,{instancePath:s="",parentData:i,parentDataProperty:a,rootData:l=t}={}){let p=null,f=0;const u=f;let c=!1;const y=f;if(!1!==t){const e={params:{}};null===p?p=[e]:p.push(e),f++}var m=y===f;if(c=c||m,!c){const o=f;if(f==f)if(t&&"object"==typeof t&&!Array.isArray(t)){let e;if(void 0===t.type&&(e="type")){const t={params:{missingProperty:e}};null===p?p=[t]:p.push(t),f++}else{const e=f;for(const e in t)if("cacheUnaffected"!==e&&"maxGenerations"!==e&&"type"!==e){const t={params:{additionalProperty:e}};null===p?p=[t]:p.push(t),f++;break}if(e===f){if(void 0!==t.cacheUnaffected){const e=f;if("boolean"!=typeof t.cacheUnaffected){const e={params:{type:"boolean"}};null===p?p=[e]:p.push(e),f++}var d=e===f}else d=!0;if(d){if(void 0!==t.maxGenerations){let e=t.maxGenerations;const n=f;if(f===n)if("number"==typeof e){if(e<1||isNaN(e)){const e={params:{comparison:">=",limit:1}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"number"}};null===p?p=[e]:p.push(e),f++}d=n===f}else d=!0;if(d)if(void 0!==t.type){const e=f;if("memory"!==t.type){const e={params:{}};null===p?p=[e]:p.push(e),f++}d=e===f}else d=!0}}}}else{const e={params:{type:"object"}};null===p?p=[e]:p.push(e),f++}if(m=o===f,c=c||m,!c){const o=f;if(f==f)if(t&&"object"==typeof t&&!Array.isArray(t)){let o;if(void 0===t.type&&(o="type")){const e={params:{missingProperty:o}};null===p?p=[e]:p.push(e),f++}else{const o=f;for(const e in t)if(!n.call(r.properties,e)){const t={params:{additionalProperty:e}};null===p?p=[t]:p.push(t),f++;break}if(o===f){if(void 0!==t.allowCollectingMemory){const e=f;if("boolean"!=typeof t.allowCollectingMemory){const e={params:{type:"boolean"}};null===p?p=[e]:p.push(e),f++}var h=e===f}else h=!0;if(h){if(void 0!==t.buildDependencies){let e=t.buildDependencies;const n=f;if(f===n)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){let n=e[t];const r=f;if(f===r)if(Array.isArray(n)){const e=n.length;for(let t=0;t<e;t++){let e=n[t];const r=f;if(f===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}if(r!==f)break}}else{const e={params:{type:"array"}};null===p?p=[e]:p.push(e),f++}if(r!==f)break}else{const e={params:{type:"object"}};null===p?p=[e]:p.push(e),f++}h=n===f}else h=!0;if(h){if(void 0!==t.cacheDirectory){let n=t.cacheDirectory;const r=f;if(f===r)if("string"==typeof n){if(n.includes("!")||!0!==e.test(n)){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}h=r===f}else h=!0;if(h){if(void 0!==t.cacheLocation){let n=t.cacheLocation;const r=f;if(f===r)if("string"==typeof n){if(n.includes("!")||!0!==e.test(n)){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}h=r===f}else h=!0;if(h){if(void 0!==t.compression){let e=t.compression;const n=f;if(!1!==e&&"gzip"!==e&&"brotli"!==e){const e={params:{}};null===p?p=[e]:p.push(e),f++}h=n===f}else h=!0;if(h){if(void 0!==t.hashAlgorithm){const e=f;if("string"!=typeof t.hashAlgorithm){const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}h=e===f}else h=!0;if(h){if(void 0!==t.idleTimeout){let e=t.idleTimeout;const n=f;if(f===n)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"number"}};null===p?p=[e]:p.push(e),f++}h=n===f}else h=!0;if(h){if(void 0!==t.idleTimeoutAfterLargeChanges){let e=t.idleTimeoutAfterLargeChanges;const n=f;if(f===n)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"number"}};null===p?p=[e]:p.push(e),f++}h=n===f}else h=!0;if(h){if(void 0!==t.idleTimeoutForInitialStore){let e=t.idleTimeoutForInitialStore;const n=f;if(f===n)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"number"}};null===p?p=[e]:p.push(e),f++}h=n===f}else h=!0;if(h){if(void 0!==t.immutablePaths){let n=t.immutablePaths;const r=f;if(f===r)if(Array.isArray(n)){const t=n.length;for(let r=0;r<t;r++){let t=n[r];const o=f,s=f;let i=!1;const a=f;if(!(t instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}var b=a===f;if(i=i||b,!i){const n=f;if(f===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===p?p=[e]:p.push(e),f++}else if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}b=n===f,i=i||b}if(i)f=s,null!==p&&(s?p.length=s:p=null);else{const e={params:{}};null===p?p=[e]:p.push(e),f++}if(o!==f)break}}else{const e={params:{type:"array"}};null===p?p=[e]:p.push(e),f++}h=r===f}else h=!0;if(h){if(void 0!==t.managedPaths){let n=t.managedPaths;const r=f;if(f===r)if(Array.isArray(n)){const t=n.length;for(let r=0;r<t;r++){let t=n[r];const o=f,s=f;let i=!1;const a=f;if(!(t instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}var g=a===f;if(i=i||g,!i){const n=f;if(f===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===p?p=[e]:p.push(e),f++}else if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}g=n===f,i=i||g}if(i)f=s,null!==p&&(s?p.length=s:p=null);else{const e={params:{}};null===p?p=[e]:p.push(e),f++}if(o!==f)break}}else{const e={params:{type:"array"}};null===p?p=[e]:p.push(e),f++}h=r===f}else h=!0;if(h){if(void 0!==t.maxAge){let e=t.maxAge;const n=f;if(f===n)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"number"}};null===p?p=[e]:p.push(e),f++}h=n===f}else h=!0;if(h){if(void 0!==t.maxMemoryGenerations){let e=t.maxMemoryGenerations;const n=f;if(f===n)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"number"}};null===p?p=[e]:p.push(e),f++}h=n===f}else h=!0;if(h){if(void 0!==t.memoryCacheUnaffected){const e=f;if("boolean"!=typeof t.memoryCacheUnaffected){const e={params:{type:"boolean"}};null===p?p=[e]:p.push(e),f++}h=e===f}else h=!0;if(h){if(void 0!==t.name){const e=f;if("string"!=typeof t.name){const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}h=e===f}else h=!0;if(h){if(void 0!==t.profile){const e=f;if("boolean"!=typeof t.profile){const e={params:{type:"boolean"}};null===p?p=[e]:p.push(e),f++}h=e===f}else h=!0;if(h){if(void 0!==t.readonly){const e=f;if("boolean"!=typeof t.readonly){const e={params:{type:"boolean"}};null===p?p=[e]:p.push(e),f++}h=e===f}else h=!0;if(h){if(void 0!==t.store){const e=f;if("pack"!==t.store){const e={params:{}};null===p?p=[e]:p.push(e),f++}h=e===f}else h=!0;if(h){if(void 0!==t.type){const e=f;if("filesystem"!==t.type){const e={params:{}};null===p?p=[e]:p.push(e),f++}h=e===f}else h=!0;if(h)if(void 0!==t.version){const e=f;if("string"!=typeof t.version){const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}h=e===f}else h=!0}}}}}}}}}}}}}}}}}}}}}else{const e={params:{type:"object"}};null===p?p=[e]:p.push(e),f++}m=o===f,c=c||m}}if(!c){const e={params:{}};return null===p?p=[e]:p.push(e),f++,o.errors=p,!1}return f=u,null!==p&&(u?p.length=u:p=null),o.errors=p,0===f}function s(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:i=e}={}){let a=null,l=0;const p=l;let f=!1;const u=l;if(!0!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var c=u===l;if(f=f||c,!f){const s=l;o(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:i})||(a=null===a?o.errors:a.concat(o.errors),l=a.length),c=s===l,f=f||c}if(!f){const e={params:{}};return null===a?a=[e]:a.push(e),l++,s.errors=a,!1}return l=p,null!==a&&(p?a.length=p:a=null),s.errors=a,0===l}const i={type:"object",additionalProperties:!1,properties:{asyncChunks:{type:"boolean"},baseUri:{type:"string"},chunkLoading:{$ref:"#/definitions/ChunkLoading"},dependOn:{anyOf:[{type:"array",items:{type:"string",minLength:1},minItems:1,uniqueItems:!0},{type:"string",minLength:1}]},filename:{$ref:"#/definitions/EntryFilename"},import:{$ref:"#/definitions/EntryItem"},layer:{$ref:"#/definitions/Layer"},library:{$ref:"#/definitions/LibraryOptions"},publicPath:{$ref:"#/definitions/PublicPath"},runtime:{$ref:"#/definitions/EntryRuntime"},wasmLoading:{$ref:"#/definitions/WasmLoading"}},required:["import"]};function a(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const l=i;let p=!1;const f=i;if(!1!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}var u=f===i;if(p=p||u,!p){const t=i,n=i;let r=!1;const o=i;if("jsonp"!==e&&"import-scripts"!==e&&"require"!==e&&"async-node"!==e&&"import"!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}var c=o===i;if(r=r||c,!r){const t=i;if("string"!=typeof e){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}c=t===i,r=r||c}if(r)i=n,null!==s&&(n?s.length=n:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}u=t===i,p=p||u}if(!p){const e={params:{}};return null===s?s=[e]:s.push(e),i++,a.errors=s,!1}return i=l,null!==s&&(l?s.length=l:s=null),a.errors=s,0===i}function l(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const p=a;let f=!1,u=null;const c=a,y=a;let m=!1;const d=a;if(a===d)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}else if(t.length<1){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var h=d===a;if(m=m||h,!m){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}h=e===a,m=m||h}if(m)a=y,null!==i&&(y?i.length=y:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(c===a&&(f=!0,u=0),!f){const e={params:{passingSchemas:u}};return null===i?i=[e]:i.push(e),a++,l.errors=i,!1}return a=p,null!==i&&(p?i.length=p:i=null),l.errors=i,0===a}function p(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const f=i;if("string"!=typeof e){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}var u=f===i;if(l=l||u,!l){const t=i;if(i==i)if(e&&"object"==typeof e&&!Array.isArray(e)){const t=i;for(const t in e)if("amd"!==t&&"commonjs"!==t&&"commonjs2"!==t&&"root"!==t){const e={params:{additionalProperty:t}};null===s?s=[e]:s.push(e),i++;break}if(t===i){if(void 0!==e.amd){const t=i;if("string"!=typeof e.amd){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}var c=t===i}else c=!0;if(c){if(void 0!==e.commonjs){const t=i;if("string"!=typeof e.commonjs){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}c=t===i}else c=!0;if(c){if(void 0!==e.commonjs2){const t=i;if("string"!=typeof e.commonjs2){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}c=t===i}else c=!0;if(c)if(void 0!==e.root){const t=i;if("string"!=typeof e.root){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}c=t===i}else c=!0}}}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}u=t===i,l=l||u}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,p.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),p.errors=s,0===i}function f(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(i===p)if(Array.isArray(e))if(e.length<1){const e={params:{limit:1}};null===s?s=[e]:s.push(e),i++}else{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=i;if(i===r)if("string"==typeof t){if(t.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(r!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var u=p===i;if(l=l||u,!l){const t=i;if(i===t)if("string"==typeof e){if(e.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(u=t===i,l=l||u,!l){const t=i;if(i==i)if(e&&"object"==typeof e&&!Array.isArray(e)){const t=i;for(const t in e)if("amd"!==t&&"commonjs"!==t&&"root"!==t){const e={params:{additionalProperty:t}};null===s?s=[e]:s.push(e),i++;break}if(t===i){if(void 0!==e.amd){let t=e.amd;const n=i;if(i===n)if("string"==typeof t){if(t.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}var c=n===i}else c=!0;if(c){if(void 0!==e.commonjs){let t=e.commonjs;const n=i;if(i===n)if("string"==typeof t){if(t.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}c=n===i}else c=!0;if(c)if(void 0!==e.root){let t=e.root;const n=i,r=i;let o=!1;const a=i;if(i===a)if(Array.isArray(t)){const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=i;if(i===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(r!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var y=a===i;if(o=o||y,!o){const e=i;if(i===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}y=e===i,o=o||y}if(o)i=r,null!==s&&(r?s.length=r:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}c=n===i}else c=!0}}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}u=t===i,l=l||u}}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,f.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),f.errors=s,0===i}function u(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return u.errors=[{params:{type:"object"}}],!1;{let n;if(void 0===e.type&&(n="type"))return u.errors=[{params:{missingProperty:n}}],!1;{const n=i;for(const t in e)if("amdContainer"!==t&&"auxiliaryComment"!==t&&"export"!==t&&"name"!==t&&"type"!==t&&"umdNamedDefine"!==t)return u.errors=[{params:{additionalProperty:t}}],!1;if(n===i){if(void 0!==e.amdContainer){let t=e.amdContainer;const n=i;if(i==i){if("string"!=typeof t)return u.errors=[{params:{type:"string"}}],!1;if(t.length<1)return u.errors=[{params:{}}],!1}var a=n===i}else a=!0;if(a){if(void 0!==e.auxiliaryComment){const n=i;p(e.auxiliaryComment,{instancePath:t+"/auxiliaryComment",parentData:e,parentDataProperty:"auxiliaryComment",rootData:o})||(s=null===s?p.errors:s.concat(p.errors),i=s.length),a=n===i}else a=!0;if(a){if(void 0!==e.export){let t=e.export;const n=i,r=i;let o=!1;const p=i;if(i===p)if(Array.isArray(t)){const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=i;if(i===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(r!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var l=p===i;if(o=o||l,!o){const e=i;if(i===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}l=e===i,o=o||l}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,u.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0;if(a){if(void 0!==e.name){const n=i;f(e.name,{instancePath:t+"/name",parentData:e,parentDataProperty:"name",rootData:o})||(s=null===s?f.errors:s.concat(f.errors),i=s.length),a=n===i}else a=!0;if(a){if(void 0!==e.type){let t=e.type;const n=i,r=i;let o=!1;const l=i;if("var"!==t&&"module"!==t&&"assign"!==t&&"assign-properties"!==t&&"this"!==t&&"window"!==t&&"self"!==t&&"global"!==t&&"commonjs"!==t&&"commonjs2"!==t&&"commonjs-module"!==t&&"commonjs-static"!==t&&"amd"!==t&&"amd-require"!==t&&"umd"!==t&&"umd2"!==t&&"jsonp"!==t&&"system"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var c=l===i;if(o=o||c,!o){const e=i;if("string"!=typeof t){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}c=e===i,o=o||c}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,u.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0;if(a)if(void 0!==e.umdNamedDefine){const t=i;if("boolean"!=typeof e.umdNamedDefine)return u.errors=[{params:{type:"boolean"}}],!1;a=t===i}else a=!0}}}}}}}}return u.errors=s,0===i}function c(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if("auto"!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const t=i,n=i;let r=!1;const o=i;if("string"!=typeof e){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}var u=o===i;if(r=r||u,!r){const t=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}u=t===i,r=r||u}if(r)i=n,null!==s&&(n?s.length=n:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}f=t===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,c.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),c.errors=s,0===i}function y(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(!1!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const t=i,n=i;let r=!1;const o=i;if("fetch"!==e&&"async-node"!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}var u=o===i;if(r=r||u,!r){const t=i;if("string"!=typeof e){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}u=t===i,r=r||u}if(r)i=n,null!==s&&(n?s.length=n:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}f=t===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,y.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),y.errors=s,0===i}function m(e,{instancePath:t="",parentData:r,parentDataProperty:o,rootData:s=e}={}){let p=null,f=0;if(0===f){if(!e||"object"!=typeof e||Array.isArray(e))return m.errors=[{params:{type:"object"}}],!1;{let r;if(void 0===e.import&&(r="import"))return m.errors=[{params:{missingProperty:r}}],!1;{const r=f;for(const t in e)if(!n.call(i.properties,t))return m.errors=[{params:{additionalProperty:t}}],!1;if(r===f){if(void 0!==e.asyncChunks){const t=f;if("boolean"!=typeof e.asyncChunks)return m.errors=[{params:{type:"boolean"}}],!1;var d=t===f}else d=!0;if(d){if(void 0!==e.baseUri){const t=f;if("string"!=typeof e.baseUri)return m.errors=[{params:{type:"string"}}],!1;d=t===f}else d=!0;if(d){if(void 0!==e.chunkLoading){const n=f;a(e.chunkLoading,{instancePath:t+"/chunkLoading",parentData:e,parentDataProperty:"chunkLoading",rootData:s})||(p=null===p?a.errors:p.concat(a.errors),f=p.length),d=n===f}else d=!0;if(d){if(void 0!==e.dependOn){let t=e.dependOn;const n=f,r=f;let o=!1;const s=f;if(f===s)if(Array.isArray(t))if(t.length<1){const e={params:{limit:1}};null===p?p=[e]:p.push(e),f++}else{var h=!0;const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=f;if(f===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}if(!(h=r===f))break}if(h){let e,n=t.length;if(n>1){const r={};for(;n--;){let o=t[n];if("string"==typeof o){if("number"==typeof r[o]){e=r[o];const t={params:{i:n,j:e}};null===p?p=[t]:p.push(t),f++;break}r[o]=n}}}}}else{const e={params:{type:"array"}};null===p?p=[e]:p.push(e),f++}var b=s===f;if(o=o||b,!o){const e=f;if(f===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}b=e===f,o=o||b}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,m.errors=p,!1}f=r,null!==p&&(r?p.length=r:p=null),d=n===f}else d=!0;if(d){if(void 0!==e.filename){const n=f;l(e.filename,{instancePath:t+"/filename",parentData:e,parentDataProperty:"filename",rootData:s})||(p=null===p?l.errors:p.concat(l.errors),f=p.length),d=n===f}else d=!0;if(d){if(void 0!==e.import){let t=e.import;const n=f,r=f;let o=!1;const s=f;if(f===s)if(Array.isArray(t))if(t.length<1){const e={params:{limit:1}};null===p?p=[e]:p.push(e),f++}else{var g=!0;const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=f;if(f===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}if(!(g=r===f))break}if(g){let e,n=t.length;if(n>1){const r={};for(;n--;){let o=t[n];if("string"==typeof o){if("number"==typeof r[o]){e=r[o];const t={params:{i:n,j:e}};null===p?p=[t]:p.push(t),f++;break}r[o]=n}}}}}else{const e={params:{type:"array"}};null===p?p=[e]:p.push(e),f++}var v=s===f;if(o=o||v,!o){const e=f;if(f===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}v=e===f,o=o||v}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,m.errors=p,!1}f=r,null!==p&&(r?p.length=r:p=null),d=n===f}else d=!0;if(d){if(void 0!==e.layer){let t=e.layer;const n=f,r=f;let o=!1;const s=f;if(null!==t){const e={params:{}};null===p?p=[e]:p.push(e),f++}var P=s===f;if(o=o||P,!o){const e=f;if(f===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}P=e===f,o=o||P}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,m.errors=p,!1}f=r,null!==p&&(r?p.length=r:p=null),d=n===f}else d=!0;if(d){if(void 0!==e.library){const n=f;u(e.library,{instancePath:t+"/library",parentData:e,parentDataProperty:"library",rootData:s})||(p=null===p?u.errors:p.concat(u.errors),f=p.length),d=n===f}else d=!0;if(d){if(void 0!==e.publicPath){const n=f;c(e.publicPath,{instancePath:t+"/publicPath",parentData:e,parentDataProperty:"publicPath",rootData:s})||(p=null===p?c.errors:p.concat(c.errors),f=p.length),d=n===f}else d=!0;if(d){if(void 0!==e.runtime){let t=e.runtime;const n=f,r=f;let o=!1;const s=f;if(!1!==t){const e={params:{}};null===p?p=[e]:p.push(e),f++}var D=s===f;if(o=o||D,!o){const e=f;if(f===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}D=e===f,o=o||D}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,m.errors=p,!1}f=r,null!==p&&(r?p.length=r:p=null),d=n===f}else d=!0;if(d)if(void 0!==e.wasmLoading){const n=f;y(e.wasmLoading,{instancePath:t+"/wasmLoading",parentData:e,parentDataProperty:"wasmLoading",rootData:s})||(p=null===p?y.errors:p.concat(y.errors),f=p.length),d=n===f}else d=!0}}}}}}}}}}}}}return m.errors=p,0===f}function d(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return d.errors=[{params:{type:"object"}}],!1;for(const n in e){let r=e[n];const f=i,u=i;let c=!1;const y=i,h=i;let b=!1;const g=i;if(i===g)if(Array.isArray(r))if(r.length<1){const e={params:{limit:1}};null===s?s=[e]:s.push(e),i++}else{var a=!0;const e=r.length;for(let t=0;t<e;t++){let e=r[t];const n=i;if(i===n)if("string"==typeof e){if(e.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(!(a=n===i))break}if(a){let e,t=r.length;if(t>1){const n={};for(;t--;){let o=r[t];if("string"==typeof o){if("number"==typeof n[o]){e=n[o];const r={params:{i:t,j:e}};null===s?s=[r]:s.push(r),i++;break}n[o]=t}}}}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var l=g===i;if(b=b||l,!b){const e=i;if(i===e)if("string"==typeof r){if(r.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}l=e===i,b=b||l}if(b)i=h,null!==s&&(h?s.length=h:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}var p=y===i;if(c=c||p,!c){const a=i;m(r,{instancePath:t+"/"+n.replace(/~/g,"~0").replace(/\//g,"~1"),parentData:e,parentDataProperty:n,rootData:o})||(s=null===s?m.errors:s.concat(m.errors),i=s.length),p=a===i,c=c||p}if(!c){const e={params:{}};return null===s?s=[e]:s.push(e),i++,d.errors=s,!1}if(i=u,null!==s&&(u?s.length=u:s=null),f!==i)break}}return d.errors=s,0===i}function h(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1,p=null;const f=i,u=i;let c=!1;const y=i;if(i===y)if(Array.isArray(e))if(e.length<1){const e={params:{limit:1}};null===s?s=[e]:s.push(e),i++}else{var m=!0;const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=i;if(i===r)if("string"==typeof t){if(t.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(!(m=r===i))break}if(m){let t,n=e.length;if(n>1){const r={};for(;n--;){let o=e[n];if("string"==typeof o){if("number"==typeof r[o]){t=r[o];const e={params:{i:n,j:t}};null===s?s=[e]:s.push(e),i++;break}r[o]=n}}}}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var d=y===i;if(c=c||d,!c){const t=i;if(i===t)if("string"==typeof e){if(e.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}d=t===i,c=c||d}if(c)i=u,null!==s&&(u?s.length=u:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}if(f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,h.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),h.errors=s,0===i}function b(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;d(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?d.errors:s.concat(d.errors),i=s.length);var f=p===i;if(l=l||f,!l){const a=i;h(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?h.errors:s.concat(h.errors),i=s.length),f=a===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,b.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),b.errors=s,0===i}function g(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const a=i;b(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?b.errors:s.concat(b.errors),i=s.length),f=a===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,g.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),g.errors=s,0===i}const v={type:"object",additionalProperties:!1,properties:{asyncWebAssembly:{type:"boolean"},backCompat:{type:"boolean"},buildHttp:{anyOf:[{$ref:"#/definitions/HttpUriAllowedUris"},{$ref:"#/definitions/HttpUriOptions"}]},cacheUnaffected:{type:"boolean"},css:{type:"boolean"},deferImport:{type:"boolean"},futureDefaults:{type:"boolean"},layers:{type:"boolean"},lazyCompilation:{anyOf:[{type:"boolean"},{$ref:"#/definitions/LazyCompilationOptions"}]},outputModule:{type:"boolean"},syncWebAssembly:{type:"boolean"},topLevelAwait:{type:"boolean"}}},P=new RegExp("^https?://","u");function D(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1,p=null;const f=i;if(i==i)if(Array.isArray(e)){const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=i,o=i;let a=!1;const l=i;if(!(t instanceof RegExp)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var u=l===i;if(a=a||u,!a){const e=i;if(i===e)if("string"==typeof t){if(!P.test(t)){const e={params:{pattern:"^https?://"}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(u=e===i,a=a||u,!a){const e=i;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}u=e===i,a=a||u}}if(a)i=o,null!==s&&(o?s.length=o:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}if(r!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}if(f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,D.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),D.errors=s,0===i}function O(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;if(0===a){if(!t||"object"!=typeof t||Array.isArray(t))return O.errors=[{params:{type:"object"}}],!1;{let n;if(void 0===t.allowedUris&&(n="allowedUris"))return O.errors=[{params:{missingProperty:n}}],!1;{const n=a;for(const e in t)if("allowedUris"!==e&&"cacheLocation"!==e&&"frozen"!==e&&"lockfileLocation"!==e&&"proxy"!==e&&"upgrade"!==e)return O.errors=[{params:{additionalProperty:e}}],!1;if(n===a){if(void 0!==t.allowedUris){let e=t.allowedUris;const n=a;if(a==a){if(!Array.isArray(e))return O.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=a,o=a;let s=!1;const p=a;if(!(t instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var l=p===a;if(s=s||l,!s){const e=a;if(a===e)if("string"==typeof t){if(!P.test(t)){const e={params:{pattern:"^https?://"}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(l=e===a,s=s||l,!s){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}l=e===a,s=s||l}}if(!s){const e={params:{}};return null===i?i=[e]:i.push(e),a++,O.errors=i,!1}if(a=o,null!==i&&(o?i.length=o:i=null),r!==a)break}}}var p=n===a}else p=!0;if(p){if(void 0!==t.cacheLocation){let n=t.cacheLocation;const r=a,o=a;let s=!1;const l=a;if(!1!==n){const e={params:{}};null===i?i=[e]:i.push(e),a++}var f=l===a;if(s=s||f,!s){const t=a;if(a===t)if("string"==typeof n){if(n.includes("!")||!0!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}f=t===a,s=s||f}if(!s){const e={params:{}};return null===i?i=[e]:i.push(e),a++,O.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),p=r===a}else p=!0;if(p){if(void 0!==t.frozen){const e=a;if("boolean"!=typeof t.frozen)return O.errors=[{params:{type:"boolean"}}],!1;p=e===a}else p=!0;if(p){if(void 0!==t.lockfileLocation){let n=t.lockfileLocation;const r=a;if(a===r){if("string"!=typeof n)return O.errors=[{params:{type:"string"}}],!1;if(n.includes("!")||!0!==e.test(n))return O.errors=[{params:{}}],!1}p=r===a}else p=!0;if(p){if(void 0!==t.proxy){const e=a;if("string"!=typeof t.proxy)return O.errors=[{params:{type:"string"}}],!1;p=e===a}else p=!0;if(p)if(void 0!==t.upgrade){const e=a;if("boolean"!=typeof t.upgrade)return O.errors=[{params:{type:"boolean"}}],!1;p=e===a}else p=!0}}}}}}}}return O.errors=i,0===a}function C(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return C.errors=[{params:{type:"object"}}],!1;{const t=i;for(const t in e)if("backend"!==t&&"entries"!==t&&"imports"!==t&&"test"!==t)return C.errors=[{params:{additionalProperty:t}}],!1;if(t===i){if(void 0!==e.backend){let t=e.backend;const n=i,r=i;let o=!1;const y=i;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var a=y===i;if(o=o||a,!o){const e=i;if(i==i)if(t&&"object"==typeof t&&!Array.isArray(t)){const e=i;for(const e in t)if("client"!==e&&"listen"!==e&&"protocol"!==e&&"server"!==e){const t={params:{additionalProperty:e}};null===s?s=[t]:s.push(t),i++;break}if(e===i){if(void 0!==t.client){const e=i;if("string"!=typeof t.client){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}var l=e===i}else l=!0;if(l){if(void 0!==t.listen){let e=t.listen;const n=i,r=i;let o=!1;const a=i;if("number"!=typeof e){const e={params:{type:"number"}};null===s?s=[e]:s.push(e),i++}var p=a===i;if(o=o||p,!o){const t=i;if(i===t)if(e&&"object"==typeof e&&!Array.isArray(e)){if(void 0!==e.host){const t=i;if("string"!=typeof e.host){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}var f=t===i}else f=!0;if(f)if(void 0!==e.port){const t=i;if("number"!=typeof e.port){const e={params:{type:"number"}};null===s?s=[e]:s.push(e),i++}f=t===i}else f=!0}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}if(p=t===i,o=o||p,!o){const t=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}p=t===i,o=o||p}}if(o)i=r,null!==s&&(r?s.length=r:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}l=n===i}else l=!0;if(l){if(void 0!==t.protocol){let e=t.protocol;const n=i;if("http"!==e&&"https"!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}l=n===i}else l=!0;if(l)if(void 0!==t.server){let e=t.server;const n=i,r=i;let o=!1;const a=i;if(i===a)if(e&&"object"==typeof e&&!Array.isArray(e));else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}var u=a===i;if(o=o||u,!o){const t=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}u=t===i,o=o||u}if(o)i=r,null!==s&&(r?s.length=r:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}l=n===i}else l=!0}}}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}a=e===i,o=o||a}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,C.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null);var c=n===i}else c=!0;if(c){if(void 0!==e.entries){const t=i;if("boolean"!=typeof e.entries)return C.errors=[{params:{type:"boolean"}}],!1;c=t===i}else c=!0;if(c){if(void 0!==e.imports){const t=i;if("boolean"!=typeof e.imports)return C.errors=[{params:{type:"boolean"}}],!1;c=t===i}else c=!0;if(c)if(void 0!==e.test){let t=e.test;const n=i,r=i;let o=!1;const a=i;if(!(t instanceof RegExp)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var y=a===i;if(o=o||y,!o){const e=i;if("string"!=typeof t){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(y=e===i,o=o||y,!o){const e=i;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}y=e===i,o=o||y}}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,C.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),c=n===i}else c=!0}}}}}return C.errors=s,0===i}function x(e,{instancePath:t="",parentData:r,parentDataProperty:o,rootData:s=e}={}){let i=null,a=0;if(0===a){if(!e||"object"!=typeof e||Array.isArray(e))return x.errors=[{params:{type:"object"}}],!1;{const r=a;for(const t in e)if(!n.call(v.properties,t))return x.errors=[{params:{additionalProperty:t}}],!1;if(r===a){if(void 0!==e.asyncWebAssembly){const t=a;if("boolean"!=typeof e.asyncWebAssembly)return x.errors=[{params:{type:"boolean"}}],!1;var l=t===a}else l=!0;if(l){if(void 0!==e.backCompat){const t=a;if("boolean"!=typeof e.backCompat)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.buildHttp){let n=e.buildHttp;const r=a,o=a;let f=!1;const u=a;D(n,{instancePath:t+"/buildHttp",parentData:e,parentDataProperty:"buildHttp",rootData:s})||(i=null===i?D.errors:i.concat(D.errors),a=i.length);var p=u===a;if(f=f||p,!f){const r=a;O(n,{instancePath:t+"/buildHttp",parentData:e,parentDataProperty:"buildHttp",rootData:s})||(i=null===i?O.errors:i.concat(O.errors),a=i.length),p=r===a,f=f||p}if(!f){const e={params:{}};return null===i?i=[e]:i.push(e),a++,x.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),l=r===a}else l=!0;if(l){if(void 0!==e.cacheUnaffected){const t=a;if("boolean"!=typeof e.cacheUnaffected)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.css){const t=a;if("boolean"!=typeof e.css)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.deferImport){const t=a;if("boolean"!=typeof e.deferImport)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.futureDefaults){const t=a;if("boolean"!=typeof e.futureDefaults)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.layers){const t=a;if("boolean"!=typeof e.layers)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.lazyCompilation){let n=e.lazyCompilation;const r=a,o=a;let p=!1;const u=a;if("boolean"!=typeof n){const e={params:{type:"boolean"}};null===i?i=[e]:i.push(e),a++}var f=u===a;if(p=p||f,!p){const r=a;C(n,{instancePath:t+"/lazyCompilation",parentData:e,parentDataProperty:"lazyCompilation",rootData:s})||(i=null===i?C.errors:i.concat(C.errors),a=i.length),f=r===a,p=p||f}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,x.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),l=r===a}else l=!0;if(l){if(void 0!==e.outputModule){const t=a;if("boolean"!=typeof e.outputModule)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.syncWebAssembly){const t=a;if("boolean"!=typeof e.syncWebAssembly)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l)if(void 0!==e.topLevelAwait){const t=a;if("boolean"!=typeof e.topLevelAwait)return x.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0}}}}}}}}}}}}}return x.errors=i,0===a}function $(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(i===p)if(Array.isArray(e)){const t=e.length;for(let n=0;n<t;n++){const t=i;if("string"!=typeof e[n]){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(t!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const t=i;if("string"!=typeof e){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}f=t===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,$.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),$.errors=s,0===i}const A={validate:j};function k(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const t=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}f=t===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,k.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),k.errors=s,0===i}function j(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(!(e instanceof RegExp)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const a=i;if("string"!=typeof e){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(f=a===i,l=l||f,!l){const a=i;if(i===a)if(e&&"object"==typeof e&&!Array.isArray(e)){const n=i;for(const t in e)if("byLayer"!==t){let n=e[t];const r=i,o=i;let a=!1;const l=i;if(i===l)if(Array.isArray(n)){const e=n.length;for(let t=0;t<e;t++){let e=n[t];const r=i;if(i===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(r!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var u=l===i;if(a=a||u,!a){const e=i;if("boolean"!=typeof n){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}if(u=e===i,a=a||u,!a){const e=i;if("string"!=typeof n){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(u=e===i,a=a||u,!a){const e=i;if(!n||"object"!=typeof n||Array.isArray(n)){const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}u=e===i,a=a||u}}}if(a)i=o,null!==s&&(o?s.length=o:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}if(r!==i)break}if(n===i&&void 0!==e.byLayer){let n=e.byLayer;const r=i;let a=!1;const l=i;if(i===l)if(n&&"object"==typeof n&&!Array.isArray(n))for(const e in n){const r=i;if(A.validate(n[e],{instancePath:t+"/byLayer/"+e.replace(/~/g,"~0").replace(/\//g,"~1"),parentData:n,parentDataProperty:e,rootData:o})||(s=null===s?A.validate.errors:s.concat(A.validate.errors),i=s.length),r!==i)break}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}var c=l===i;if(a=a||c,!a){const e=i;if(!(n instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}c=e===i,a=a||c}if(a)i=r,null!==s&&(r?s.length=r:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}if(f=a===i,l=l||f,!l){const a=i;k(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?k.errors:s.concat(k.errors),i=s.length),f=a===i,l=l||f}}}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,j.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),j.errors=s,0===i}function S(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(i===p)if(Array.isArray(e)){const n=e.length;for(let r=0;r<n;r++){const n=i;if(j(e[r],{instancePath:t+"/"+r,parentData:e,parentDataProperty:r,rootData:o})||(s=null===s?j.errors:s.concat(j.errors),i=s.length),n!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const a=i;j(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?j.errors:s.concat(j.errors),i=s.length),f=a===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,S.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),S.errors=s,0===i}function F(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1;const f=a;if(a===f)if(Array.isArray(t)){const n=t.length;for(let r=0;r<n;r++){let n=t[r];const o=a,s=a;let l=!1,p=null;const f=a,c=a;let y=!1;const m=a;if(!(n instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var u=m===a;if(y=y||u,!y){const t=a;if(a===t)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(u=t===a,y=y||u,!y){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}u=e===a,y=y||u}}if(y)a=c,null!==i&&(c?i.length=c:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(f===a&&(l=!0,p=0),l)a=s,null!==i&&(s?i.length=s:i=null);else{const e={params:{passingSchemas:p}};null===i?i=[e]:i.push(e),a++}if(o!==a)break}}else{const e={params:{type:"array"}};null===i?i=[e]:i.push(e),a++}var c=f===a;if(p=p||c,!p){const n=a,r=a;let o=!1;const s=a;if(!(t instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var y=s===a;if(o=o||y,!o){const n=a;if(a===n)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(y=n===a,o=o||y,!o){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}y=e===a,o=o||y}}if(o)a=r,null!==i&&(r?i.length=r:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}c=n===a,p=p||c}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,F.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),F.errors=i,0===a}function E(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return E.errors=[{params:{type:"object"}}],!1;{const n=i;for(const t in e)if("appendOnly"!==t&&"colors"!==t&&"console"!==t&&"debug"!==t&&"level"!==t&&"stream"!==t)return E.errors=[{params:{additionalProperty:t}}],!1;if(n===i){if(void 0!==e.appendOnly){const t=i;if("boolean"!=typeof e.appendOnly)return E.errors=[{params:{type:"boolean"}}],!1;var a=t===i}else a=!0;if(a){if(void 0!==e.colors){const t=i;if("boolean"!=typeof e.colors)return E.errors=[{params:{type:"boolean"}}],!1;a=t===i}else a=!0;if(a){if(void 0!==e.debug){let n=e.debug;const r=i,p=i;let f=!1;const u=i;if("boolean"!=typeof n){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}var l=u===i;if(f=f||l,!f){const r=i;F(n,{instancePath:t+"/debug",parentData:e,parentDataProperty:"debug",rootData:o})||(s=null===s?F.errors:s.concat(F.errors),i=s.length),l=r===i,f=f||l}if(!f){const e={params:{}};return null===s?s=[e]:s.push(e),i++,E.errors=s,!1}i=p,null!==s&&(p?s.length=p:s=null),a=r===i}else a=!0;if(a)if(void 0!==e.level){let t=e.level;const n=i;if("none"!==t&&"error"!==t&&"warn"!==t&&"info"!==t&&"log"!==t&&"verbose"!==t)return E.errors=[{params:{}}],!1;a=n===i}else a=!0}}}}}return E.errors=s,0===i}const R={type:"object",additionalProperties:!1,properties:{defaultRules:{oneOf:[{$ref:"#/definitions/RuleSetRules"}]},exprContextCritical:{type:"boolean"},exprContextRecursive:{type:"boolean"},exprContextRegExp:{anyOf:[{instanceof:"RegExp"},{type:"boolean"}]},exprContextRequest:{type:"string"},generator:{$ref:"#/definitions/GeneratorOptionsByModuleType"},noParse:{$ref:"#/definitions/NoParse"},parser:{$ref:"#/definitions/ParserOptionsByModuleType"},rules:{oneOf:[{$ref:"#/definitions/RuleSetRules"}]},strictExportPresence:{type:"boolean"},strictThisContextOnImports:{type:"boolean"},unknownContextCritical:{type:"boolean"},unknownContextRecursive:{type:"boolean"},unknownContextRegExp:{anyOf:[{instanceof:"RegExp"},{type:"boolean"}]},unknownContextRequest:{type:"string"},unsafeCache:{anyOf:[{type:"boolean"},{instanceof:"Function"}]},wrappedContextCritical:{type:"boolean"},wrappedContextRecursive:{type:"boolean"},wrappedContextRegExp:{instanceof:"RegExp"}}},L={type:"object",additionalProperties:!1,properties:{assert:{type:"object",additionalProperties:{$ref:"#/definitions/RuleSetConditionOrConditions"}},compiler:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},dependency:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},descriptionData:{type:"object",additionalProperties:{$ref:"#/definitions/RuleSetConditionOrConditions"}},enforce:{enum:["pre","post"]},exclude:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},generator:{type:"object"},include:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},issuer:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},issuerLayer:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},layer:{type:"string"},loader:{oneOf:[{$ref:"#/definitions/RuleSetLoader"}]},mimetype:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},oneOf:{type:"array",items:{anyOf:[{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/RuleSetRule"}]}},options:{oneOf:[{$ref:"#/definitions/RuleSetLoaderOptions"}]},parser:{type:"object",additionalProperties:!0},realResource:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},resolve:{type:"object",oneOf:[{$ref:"#/definitions/ResolveOptions"}]},resource:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},resourceFragment:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},resourceQuery:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},rules:{type:"array",items:{anyOf:[{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/RuleSetRule"}]}},scheme:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditions"}]},sideEffects:{type:"boolean"},test:{oneOf:[{$ref:"#/definitions/RuleSetConditionOrConditionsAbsolute"}]},type:{type:"string"},use:{oneOf:[{$ref:"#/definitions/RuleSetUse"}]},with:{type:"object",additionalProperties:{$ref:"#/definitions/RuleSetConditionOrConditions"}}}},z={validate:I};function M(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!Array.isArray(e))return M.errors=[{params:{type:"array"}}],!1;{const n=e.length;for(let r=0;r<n;r++){const n=i,a=i;let l=!1,p=null;const f=i;if(z.validate(e[r],{instancePath:t+"/"+r,parentData:e,parentDataProperty:r,rootData:o})||(s=null===s?z.validate.errors:s.concat(z.validate.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,M.errors=s,!1}if(i=a,null!==s&&(a?s.length=a:s=null),n!==i)break}}}return M.errors=s,0===i}function w(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return w.errors=[{params:{type:"object"}}],!1;{const n=i;for(const t in e)if("and"!==t&&"not"!==t&&"or"!==t)return w.errors=[{params:{additionalProperty:t}}],!1;if(n===i){if(void 0!==e.and){const n=i,r=i;let l=!1,p=null;const f=i;if(M(e.and,{instancePath:t+"/and",parentData:e,parentDataProperty:"and",rootData:o})||(s=null===s?M.errors:s.concat(M.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,w.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null);var a=n===i}else a=!0;if(a){if(void 0!==e.not){const n=i,r=i;let l=!1,p=null;const f=i;if(z.validate(e.not,{instancePath:t+"/not",parentData:e,parentDataProperty:"not",rootData:o})||(s=null===s?z.validate.errors:s.concat(z.validate.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,w.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0;if(a)if(void 0!==e.or){const n=i,r=i;let l=!1,p=null;const f=i;if(M(e.or,{instancePath:t+"/or",parentData:e,parentDataProperty:"or",rootData:o})||(s=null===s?M.errors:s.concat(M.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,w.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0}}}}return w.errors=s,0===i}function I(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(!(e instanceof RegExp)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const a=i;if("string"!=typeof e){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(f=a===i,l=l||f,!l){const a=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}if(f=a===i,l=l||f,!l){const a=i;if(w(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?w.errors:s.concat(w.errors),i=s.length),f=a===i,l=l||f,!l){const a=i;M(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?M.errors:s.concat(M.errors),i=s.length),f=a===i,l=l||f}}}}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,I.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),I.errors=s,0===i}function T(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;I(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?I.errors:s.concat(I.errors),i=s.length);var f=p===i;if(l=l||f,!l){const a=i;M(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?M.errors:s.concat(M.errors),i=s.length),f=a===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,T.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),T.errors=s,0===i}const N={validate:W};function G(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!Array.isArray(e))return G.errors=[{params:{type:"array"}}],!1;{const n=e.length;for(let r=0;r<n;r++){const n=i,a=i;let l=!1,p=null;const f=i;if(N.validate(e[r],{instancePath:t+"/"+r,parentData:e,parentDataProperty:r,rootData:o})||(s=null===s?N.validate.errors:s.concat(N.validate.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,G.errors=s,!1}if(i=a,null!==s&&(a?s.length=a:s=null),n!==i)break}}}return G.errors=s,0===i}function U(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return U.errors=[{params:{type:"object"}}],!1;{const n=i;for(const t in e)if("and"!==t&&"not"!==t&&"or"!==t)return U.errors=[{params:{additionalProperty:t}}],!1;if(n===i){if(void 0!==e.and){const n=i,r=i;let l=!1,p=null;const f=i;if(G(e.and,{instancePath:t+"/and",parentData:e,parentDataProperty:"and",rootData:o})||(s=null===s?G.errors:s.concat(G.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,U.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null);var a=n===i}else a=!0;if(a){if(void 0!==e.not){const n=i,r=i;let l=!1,p=null;const f=i;if(N.validate(e.not,{instancePath:t+"/not",parentData:e,parentDataProperty:"not",rootData:o})||(s=null===s?N.validate.errors:s.concat(N.validate.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,U.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0;if(a)if(void 0!==e.or){const n=i,r=i;let l=!1,p=null;const f=i;if(G(e.or,{instancePath:t+"/or",parentData:e,parentDataProperty:"or",rootData:o})||(s=null===s?G.errors:s.concat(G.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,U.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0}}}}return U.errors=s,0===i}function W(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1;const f=a;if(!(t instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var u=f===a;if(p=p||u,!p){const l=a;if(a===l)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(u=l===a,p=p||u,!p){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}if(u=e===a,p=p||u,!p){const e=a;if(U(t,{instancePath:n,parentData:r,parentDataProperty:o,rootData:s})||(i=null===i?U.errors:i.concat(U.errors),a=i.length),u=e===a,p=p||u,!p){const e=a;G(t,{instancePath:n,parentData:r,parentDataProperty:o,rootData:s})||(i=null===i?G.errors:i.concat(G.errors),a=i.length),u=e===a,p=p||u}}}}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,W.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),W.errors=i,0===a}function q(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;W(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?W.errors:s.concat(W.errors),i=s.length);var f=p===i;if(l=l||f,!l){const a=i;G(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?G.errors:s.concat(G.errors),i=s.length),f=a===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,q.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),q.errors=s,0===i}const B={type:"object",additionalProperties:!1,properties:{alias:{$ref:"#/definitions/ResolveAlias"},aliasFields:{type:"array",items:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"string",minLength:1}]}},byDependency:{type:"object",additionalProperties:{oneOf:[{$ref:"#/definitions/ResolveOptions"}]}},cache:{type:"boolean"},cachePredicate:{instanceof:"Function"},cacheWithContext:{type:"boolean"},conditionNames:{type:"array",items:{type:"string"}},descriptionFiles:{type:"array",items:{type:"string",minLength:1}},enforceExtension:{type:"boolean"},exportsFields:{type:"array",items:{type:"string"}},extensionAlias:{type:"object",additionalProperties:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"string",minLength:1}]}},extensions:{type:"array",items:{type:"string"}},fallback:{oneOf:[{$ref:"#/definitions/ResolveAlias"}]},fileSystem:{},fullySpecified:{type:"boolean"},importsFields:{type:"array",items:{type:"string"}},mainFields:{type:"array",items:{anyOf:[{type:"array",items:{type:"string",minLength:1}},{type:"string",minLength:1}]}},mainFiles:{type:"array",items:{type:"string",minLength:1}},modules:{type:"array",items:{type:"string",minLength:1}},plugins:{type:"array",items:{anyOf:[{enum:["..."]},{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/ResolvePluginInstance"}]}},preferAbsolute:{type:"boolean"},preferRelative:{type:"boolean"},resolver:{},restrictions:{type:"array",items:{anyOf:[{instanceof:"RegExp"},{type:"string",absolutePath:!0,minLength:1}]}},roots:{type:"array",items:{type:"string"}},symlinks:{type:"boolean"},unsafeCache:{anyOf:[{type:"boolean"},{type:"object",additionalProperties:!0}]},useSyncFileSystemCalls:{type:"boolean"}}},H={validate:J};function J(t,{instancePath:r="",parentData:o,parentDataProperty:s,rootData:i=t}={}){let a=null,l=0;if(0===l){if(!t||"object"!=typeof t||Array.isArray(t))return J.errors=[{params:{type:"object"}}],!1;{const o=l;for(const e in t)if(!n.call(B.properties,e))return J.errors=[{params:{additionalProperty:e}}],!1;if(o===l){if(void 0!==t.alias){let e=t.alias;const n=l,r=l;let o=!1;const s=l;if(l===s)if(Array.isArray(e)){const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l;if(l===r)if(t&&"object"==typeof t&&!Array.isArray(t)){let e;if(void 0===t.alias&&(e="alias")||void 0===t.name&&(e="name")){const t={params:{missingProperty:e}};null===a?a=[t]:a.push(t),l++}else{const e=l;for(const e in t)if("alias"!==e&&"name"!==e&&"onlyModule"!==e){const t={params:{additionalProperty:e}};null===a?a=[t]:a.push(t),l++;break}if(e===l){if(void 0!==t.alias){let e=t.alias;const n=l,r=l;let o=!1;const s=l;if(l===s)if(Array.isArray(e)){const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l;if(l===r)if("string"==typeof t){if(t.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var p=s===l;if(o=o||p,!o){const t=l;if(!1!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(p=t===l,o=o||p,!o){const t=l;if(l===t)if("string"==typeof e){if(e.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}p=t===l,o=o||p}}if(o)l=r,null!==a&&(r?a.length=r:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}var f=n===l}else f=!0;if(f){if(void 0!==t.name){const e=l;if("string"!=typeof t.name){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}f=e===l}else f=!0;if(f)if(void 0!==t.onlyModule){const e=l;if("boolean"!=typeof t.onlyModule){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}f=e===l}else f=!0}}}}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var u=s===l;if(o=o||u,!o){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){let n=e[t];const r=l,o=l;let s=!1;const i=l;if(l===i)if(Array.isArray(n)){const e=n.length;for(let t=0;t<e;t++){let e=n[t];const r=l;if(l===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var c=i===l;if(s=s||c,!s){const e=l;if(!1!==n){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(c=e===l,s=s||c,!s){const e=l;if(l===e)if("string"==typeof n){if(n.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}c=e===l,s=s||c}}if(s)l=o,null!==a&&(o?a.length=o:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}u=t===l,o=o||u}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null);var y=n===l}else y=!0;if(y){if(void 0!==t.aliasFields){let e=t.aliasFields;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l,o=l;let s=!1;const i=l;if(l===i)if(Array.isArray(t)){const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=l;if(l===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var m=i===l;if(s=s||m,!s){const e=l;if(l===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}m=e===l,s=s||m}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}if(l=o,null!==a&&(o?a.length=o:a=null),r!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.byDependency){let e=t.byDependency;const n=l;if(l===n){if(!e||"object"!=typeof e||Array.isArray(e))return J.errors=[{params:{type:"object"}}],!1;for(const t in e){const n=l,o=l;let s=!1,p=null;const f=l;if(H.validate(e[t],{instancePath:r+"/byDependency/"+t.replace(/~/g,"~0").replace(/\//g,"~1"),parentData:e,parentDataProperty:t,rootData:i})||(a=null===a?H.validate.errors:a.concat(H.validate.errors),l=a.length),f===l&&(s=!0,p=0),!s){const e={params:{passingSchemas:p}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}if(l=o,null!==a&&(o?a.length=o:a=null),n!==l)break}}y=n===l}else y=!0;if(y){if(void 0!==t.cache){const e=l;if("boolean"!=typeof t.cache)return J.errors=[{params:{type:"boolean"}}],!1;y=e===l}else y=!0;if(y){if(void 0!==t.cachePredicate){const e=l;if(!(t.cachePredicate instanceof Function))return J.errors=[{params:{}}],!1;y=e===l}else y=!0;if(y){if(void 0!==t.cacheWithContext){const e=l;if("boolean"!=typeof t.cacheWithContext)return J.errors=[{params:{type:"boolean"}}],!1;y=e===l}else y=!0;if(y){if(void 0!==t.conditionNames){let e=t.conditionNames;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){const t=l;if("string"!=typeof e[n])return J.errors=[{params:{type:"string"}}],!1;if(t!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.descriptionFiles){let e=t.descriptionFiles;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l;if(l===r){if("string"!=typeof t)return J.errors=[{params:{type:"string"}}],!1;if(t.length<1)return J.errors=[{params:{}}],!1}if(r!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.enforceExtension){const e=l;if("boolean"!=typeof t.enforceExtension)return J.errors=[{params:{type:"boolean"}}],!1;y=e===l}else y=!0;if(y){if(void 0!==t.exportsFields){let e=t.exportsFields;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){const t=l;if("string"!=typeof e[n])return J.errors=[{params:{type:"string"}}],!1;if(t!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.extensionAlias){let e=t.extensionAlias;const n=l;if(l===n){if(!e||"object"!=typeof e||Array.isArray(e))return J.errors=[{params:{type:"object"}}],!1;for(const t in e){let n=e[t];const r=l,o=l;let s=!1;const i=l;if(l===i)if(Array.isArray(n)){const e=n.length;for(let t=0;t<e;t++){let e=n[t];const r=l;if(l===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var d=i===l;if(s=s||d,!s){const e=l;if(l===e)if("string"==typeof n){if(n.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}d=e===l,s=s||d}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}if(l=o,null!==a&&(o?a.length=o:a=null),r!==l)break}}y=n===l}else y=!0;if(y){if(void 0!==t.extensions){let e=t.extensions;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){const t=l;if("string"!=typeof e[n])return J.errors=[{params:{type:"string"}}],!1;if(t!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.fallback){let e=t.fallback;const n=l,r=l;let o=!1,s=null;const i=l,p=l;let f=!1;const u=l;if(l===u)if(Array.isArray(e)){const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l;if(l===r)if(t&&"object"==typeof t&&!Array.isArray(t)){let e;if(void 0===t.alias&&(e="alias")||void 0===t.name&&(e="name")){const t={params:{missingProperty:e}};null===a?a=[t]:a.push(t),l++}else{const e=l;for(const e in t)if("alias"!==e&&"name"!==e&&"onlyModule"!==e){const t={params:{additionalProperty:e}};null===a?a=[t]:a.push(t),l++;break}if(e===l){if(void 0!==t.alias){let e=t.alias;const n=l,r=l;let o=!1;const s=l;if(l===s)if(Array.isArray(e)){const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l;if(l===r)if("string"==typeof t){if(t.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var h=s===l;if(o=o||h,!o){const t=l;if(!1!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(h=t===l,o=o||h,!o){const t=l;if(l===t)if("string"==typeof e){if(e.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}h=t===l,o=o||h}}if(o)l=r,null!==a&&(r?a.length=r:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}var b=n===l}else b=!0;if(b){if(void 0!==t.name){const e=l;if("string"!=typeof t.name){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}b=e===l}else b=!0;if(b)if(void 0!==t.onlyModule){const e=l;if("boolean"!=typeof t.onlyModule){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}b=e===l}else b=!0}}}}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var g=u===l;if(f=f||g,!f){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){let n=e[t];const r=l,o=l;let s=!1;const i=l;if(l===i)if(Array.isArray(n)){const e=n.length;for(let t=0;t<e;t++){let e=n[t];const r=l;if(l===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var v=i===l;if(s=s||v,!s){const e=l;if(!1!==n){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(v=e===l,s=s||v,!s){const e=l;if(l===e)if("string"==typeof n){if(n.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}v=e===l,s=s||v}}if(s)l=o,null!==a&&(o?a.length=o:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}g=t===l,f=f||g}if(f)l=p,null!==a&&(p?a.length=p:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),y=n===l}else y=!0;if(y){if(void 0!==t.fullySpecified){const e=l;if("boolean"!=typeof t.fullySpecified)return J.errors=[{params:{type:"boolean"}}],!1;y=e===l}else y=!0;if(y){if(void 0!==t.importsFields){let e=t.importsFields;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){const t=l;if("string"!=typeof e[n])return J.errors=[{params:{type:"string"}}],!1;if(t!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.mainFields){let e=t.mainFields;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l,o=l;let s=!1;const i=l;if(l===i)if(Array.isArray(t)){const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=l;if(l===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(r!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var P=i===l;if(s=s||P,!s){const e=l;if(l===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}P=e===l,s=s||P}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}if(l=o,null!==a&&(o?a.length=o:a=null),r!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.mainFiles){let e=t.mainFiles;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l;if(l===r){if("string"!=typeof t)return J.errors=[{params:{type:"string"}}],!1;if(t.length<1)return J.errors=[{params:{}}],!1}if(r!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.modules){let e=t.modules;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l;if(l===r){if("string"!=typeof t)return J.errors=[{params:{type:"string"}}],!1;if(t.length<1)return J.errors=[{params:{}}],!1}if(r!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.plugins){let e=t.plugins;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=l,o=l;let s=!1;const i=l;if("..."!==t){const e={params:{}};null===a?a=[e]:a.push(e),l++}var D=i===l;if(s=s||D,!s){const e=l;if(!1!==t&&0!==t&&""!==t&&null!=t){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(D=e===l,s=s||D,!s){const e=l,n=l;let r=!1;const o=l;if(l===o)if(t&&"object"==typeof t&&!Array.isArray(t)){let e;if(void 0===t.apply&&(e="apply")){const t={params:{missingProperty:e}};null===a?a=[t]:a.push(t),l++}else if(void 0!==t.apply&&!(t.apply instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}var O=o===l;if(r=r||O,!r){const e=l;if(!(t instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}O=e===l,r=r||O}if(r)l=n,null!==a&&(n?a.length=n:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}D=e===l,s=s||D}}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}if(l=o,null!==a&&(o?a.length=o:a=null),r!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.preferAbsolute){const e=l;if("boolean"!=typeof t.preferAbsolute)return J.errors=[{params:{type:"boolean"}}],!1;y=e===l}else y=!0;if(y){if(void 0!==t.preferRelative){const e=l;if("boolean"!=typeof t.preferRelative)return J.errors=[{params:{type:"boolean"}}],!1;y=e===l}else y=!0;if(y){if(void 0!==t.restrictions){let n=t.restrictions;const r=l;if(l===r){if(!Array.isArray(n))return J.errors=[{params:{type:"array"}}],!1;{const t=n.length;for(let r=0;r<t;r++){let t=n[r];const o=l,s=l;let i=!1;const p=l;if(!(t instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}var C=p===l;if(i=i||C,!i){const n=l;if(l===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===a?a=[e]:a.push(e),l++}else if(t.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}C=n===l,i=i||C}if(!i){const e={params:{}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}if(l=s,null!==a&&(s?a.length=s:a=null),o!==l)break}}}y=r===l}else y=!0;if(y){if(void 0!==t.roots){let e=t.roots;const n=l;if(l===n){if(!Array.isArray(e))return J.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){const t=l;if("string"!=typeof e[n])return J.errors=[{params:{type:"string"}}],!1;if(t!==l)break}}}y=n===l}else y=!0;if(y){if(void 0!==t.symlinks){const e=l;if("boolean"!=typeof t.symlinks)return J.errors=[{params:{type:"boolean"}}],!1;y=e===l}else y=!0;if(y){if(void 0!==t.unsafeCache){let e=t.unsafeCache;const n=l,r=l;let o=!1;const s=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}var x=s===l;if(o=o||x,!o){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e));else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}x=t===l,o=o||x}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,J.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),y=n===l}else y=!0;if(y)if(void 0!==t.useSyncFileSystemCalls){const e=l;if("boolean"!=typeof t.useSyncFileSystemCalls)return J.errors=[{params:{type:"boolean"}}],!1;y=e===l}else y=!0}}}}}}}}}}}}}}}}}}}}}}}}}}}return J.errors=a,0===l}function _(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(i===p)if(e&&"object"==typeof e&&!Array.isArray(e)){const t=i;for(const t in e)if("ident"!==t&&"loader"!==t&&"options"!==t){const e={params:{additionalProperty:t}};null===s?s=[e]:s.push(e),i++;break}if(t===i){if(void 0!==e.ident){const t=i;if("string"!=typeof e.ident){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}var f=t===i}else f=!0;if(f){if(void 0!==e.loader){let t=e.loader;const n=i,r=i;let o=!1,a=null;const l=i;if(i==i)if("string"==typeof t){if(t.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(l===i&&(o=!0,a=0),o)i=r,null!==s&&(r?s.length=r:s=null);else{const e={params:{passingSchemas:a}};null===s?s=[e]:s.push(e),i++}f=n===i}else f=!0;if(f)if(void 0!==e.options){let t=e.options;const n=i,r=i;let o=!1,a=null;const l=i,p=i;let c=!1;const y=i;if("string"!=typeof t){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}var u=y===i;if(c=c||u,!c){const e=i;if(!t||"object"!=typeof t||Array.isArray(t)){const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}u=e===i,c=c||u}if(c)i=p,null!==s&&(p?s.length=p:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}if(l===i&&(o=!0,a=0),o)i=r,null!==s&&(r?s.length=r:s=null);else{const e={params:{passingSchemas:a}};null===s?s=[e]:s.push(e),i++}f=n===i}else f=!0}}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}var c=p===i;if(l=l||c,!l){const t=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}if(c=t===i,l=l||c,!l){const t=i;if(i==i)if("string"==typeof e){if(e.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}c=t===i,l=l||c}}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,_.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),_.errors=s,0===i}function Q(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(i===p)if(Array.isArray(e)){const n=e.length;for(let r=0;r<n;r++){let n=e[r];const a=i,l=i;let p=!1;const u=i;if(!1!==n&&0!==n&&""!==n&&null!=n){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=u===i;if(p=p||f,!p){const a=i;_(n,{instancePath:t+"/"+r,parentData:e,parentDataProperty:r,rootData:o})||(s=null===s?_.errors:s.concat(_.errors),i=s.length),f=a===i,p=p||f}if(p)i=l,null!==s&&(l?s.length=l:s=null);else{const e={params:{}};null===s?s=[e]:s.push(e),i++}if(a!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var u=p===i;if(l=l||u,!l){const a=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}if(u=a===i,l=l||u,!l){const a=i;_(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?_.errors:s.concat(_.errors),i=s.length),u=a===i,l=l||u}}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,Q.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),Q.errors=s,0===i}const V={validate:Z};function Z(e,{instancePath:t="",parentData:r,parentDataProperty:o,rootData:s=e}={}){let i=null,a=0;if(0===a){if(!e||"object"!=typeof e||Array.isArray(e))return Z.errors=[{params:{type:"object"}}],!1;{const r=a;for(const t in e)if(!n.call(L.properties,t))return Z.errors=[{params:{additionalProperty:t}}],!1;if(r===a){if(void 0!==e.assert){let n=e.assert;const r=a;if(a===r){if(!n||"object"!=typeof n||Array.isArray(n))return Z.errors=[{params:{type:"object"}}],!1;for(const e in n){const r=a;if(T(n[e],{instancePath:t+"/assert/"+e.replace(/~/g,"~0").replace(/\//g,"~1"),parentData:n,parentDataProperty:e,rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),r!==a)break}}var l=r===a}else l=!0;if(l){if(void 0!==e.compiler){const n=a,r=a;let o=!1,p=null;const f=a;if(T(e.compiler,{instancePath:t+"/compiler",parentData:e,parentDataProperty:"compiler",rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.dependency){const n=a,r=a;let o=!1,p=null;const f=a;if(T(e.dependency,{instancePath:t+"/dependency",parentData:e,parentDataProperty:"dependency",rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.descriptionData){let n=e.descriptionData;const r=a;if(a===r){if(!n||"object"!=typeof n||Array.isArray(n))return Z.errors=[{params:{type:"object"}}],!1;for(const e in n){const r=a;if(T(n[e],{instancePath:t+"/descriptionData/"+e.replace(/~/g,"~0").replace(/\//g,"~1"),parentData:n,parentDataProperty:e,rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),r!==a)break}}l=r===a}else l=!0;if(l){if(void 0!==e.enforce){let t=e.enforce;const n=a;if("pre"!==t&&"post"!==t)return Z.errors=[{params:{}}],!1;l=n===a}else l=!0;if(l){if(void 0!==e.exclude){const n=a,r=a;let o=!1,p=null;const f=a;if(q(e.exclude,{instancePath:t+"/exclude",parentData:e,parentDataProperty:"exclude",rootData:s})||(i=null===i?q.errors:i.concat(q.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.generator){let t=e.generator;const n=a;if(!t||"object"!=typeof t||Array.isArray(t))return Z.errors=[{params:{type:"object"}}],!1;l=n===a}else l=!0;if(l){if(void 0!==e.include){const n=a,r=a;let o=!1,p=null;const f=a;if(q(e.include,{instancePath:t+"/include",parentData:e,parentDataProperty:"include",rootData:s})||(i=null===i?q.errors:i.concat(q.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.issuer){const n=a,r=a;let o=!1,p=null;const f=a;if(q(e.issuer,{instancePath:t+"/issuer",parentData:e,parentDataProperty:"issuer",rootData:s})||(i=null===i?q.errors:i.concat(q.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.issuerLayer){const n=a,r=a;let o=!1,p=null;const f=a;if(T(e.issuerLayer,{instancePath:t+"/issuerLayer",parentData:e,parentDataProperty:"issuerLayer",rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.layer){const t=a;if("string"!=typeof e.layer)return Z.errors=[{params:{type:"string"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.loader){let t=e.loader;const n=a,r=a;let o=!1,s=null;const p=a;if(a==a)if("string"==typeof t){if(t.length<1){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(p===a&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.mimetype){const n=a,r=a;let o=!1,p=null;const f=a;if(T(e.mimetype,{instancePath:t+"/mimetype",parentData:e,parentDataProperty:"mimetype",rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.oneOf){let n=e.oneOf;const r=a;if(a===r){if(!Array.isArray(n))return Z.errors=[{params:{type:"array"}}],!1;{const e=n.length;for(let r=0;r<e;r++){let e=n[r];const o=a,l=a;let f=!1;const u=a;if(!1!==e&&0!==e&&""!==e&&null!=e){const e={params:{}};null===i?i=[e]:i.push(e),a++}var p=u===a;if(f=f||p,!f){const o=a;V.validate(e,{instancePath:t+"/oneOf/"+r,parentData:n,parentDataProperty:r,rootData:s})||(i=null===i?V.validate.errors:i.concat(V.validate.errors),a=i.length),p=o===a,f=f||p}if(!f){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}if(a=l,null!==i&&(l?i.length=l:i=null),o!==a)break}}}l=r===a}else l=!0;if(l){if(void 0!==e.options){let t=e.options;const n=a,r=a;let o=!1,s=null;const p=a,u=a;let c=!1;const y=a;if("string"!=typeof t){const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var f=y===a;if(c=c||f,!c){const e=a;if(!t||"object"!=typeof t||Array.isArray(t)){const e={params:{type:"object"}};null===i?i=[e]:i.push(e),a++}f=e===a,c=c||f}if(c)a=u,null!==i&&(u?i.length=u:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(p===a&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.parser){let t=e.parser;const n=a;if(a===n&&(!t||"object"!=typeof t||Array.isArray(t)))return Z.errors=[{params:{type:"object"}}],!1;l=n===a}else l=!0;if(l){if(void 0!==e.realResource){const n=a,r=a;let o=!1,p=null;const f=a;if(q(e.realResource,{instancePath:t+"/realResource",parentData:e,parentDataProperty:"realResource",rootData:s})||(i=null===i?q.errors:i.concat(q.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.resolve){let n=e.resolve;const r=a;if(!n||"object"!=typeof n||Array.isArray(n))return Z.errors=[{params:{type:"object"}}],!1;const o=a;let p=!1,f=null;const u=a;if(J(n,{instancePath:t+"/resolve",parentData:e,parentDataProperty:"resolve",rootData:s})||(i=null===i?J.errors:i.concat(J.errors),a=i.length),u===a&&(p=!0,f=0),!p){const e={params:{passingSchemas:f}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),l=r===a}else l=!0;if(l){if(void 0!==e.resource){const n=a,r=a;let o=!1,p=null;const f=a;if(q(e.resource,{instancePath:t+"/resource",parentData:e,parentDataProperty:"resource",rootData:s})||(i=null===i?q.errors:i.concat(q.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.resourceFragment){const n=a,r=a;let o=!1,p=null;const f=a;if(T(e.resourceFragment,{instancePath:t+"/resourceFragment",parentData:e,parentDataProperty:"resourceFragment",rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.resourceQuery){const n=a,r=a;let o=!1,p=null;const f=a;if(T(e.resourceQuery,{instancePath:t+"/resourceQuery",parentData:e,parentDataProperty:"resourceQuery",rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.rules){let n=e.rules;const r=a;if(a===r){if(!Array.isArray(n))return Z.errors=[{params:{type:"array"}}],!1;{const e=n.length;for(let r=0;r<e;r++){let e=n[r];const o=a,l=a;let p=!1;const f=a;if(!1!==e&&0!==e&&""!==e&&null!=e){const e={params:{}};null===i?i=[e]:i.push(e),a++}var u=f===a;if(p=p||u,!p){const o=a;V.validate(e,{instancePath:t+"/rules/"+r,parentData:n,parentDataProperty:r,rootData:s})||(i=null===i?V.validate.errors:i.concat(V.validate.errors),a=i.length),u=o===a,p=p||u}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}if(a=l,null!==i&&(l?i.length=l:i=null),o!==a)break}}}l=r===a}else l=!0;if(l){if(void 0!==e.scheme){const n=a,r=a;let o=!1,p=null;const f=a;if(T(e.scheme,{instancePath:t+"/scheme",parentData:e,parentDataProperty:"scheme",rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.sideEffects){const t=a;if("boolean"!=typeof e.sideEffects)return Z.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.test){const n=a,r=a;let o=!1,p=null;const f=a;if(q(e.test,{instancePath:t+"/test",parentData:e,parentDataProperty:"test",rootData:s})||(i=null===i?q.errors:i.concat(q.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.type){const t=a;if("string"!=typeof e.type)return Z.errors=[{params:{type:"string"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.use){const n=a,r=a;let o=!1,p=null;const f=a;if(Q(e.use,{instancePath:t+"/use",parentData:e,parentDataProperty:"use",rootData:s})||(i=null===i?Q.errors:i.concat(Q.errors),a=i.length),f===a&&(o=!0,p=0),!o){const e={params:{passingSchemas:p}};return null===i?i=[e]:i.push(e),a++,Z.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l)if(void 0!==e.with){let n=e.with;const r=a;if(a===r){if(!n||"object"!=typeof n||Array.isArray(n))return Z.errors=[{params:{type:"object"}}],!1;for(const e in n){const r=a;if(T(n[e],{instancePath:t+"/with/"+e.replace(/~/g,"~0").replace(/\//g,"~1"),parentData:n,parentDataProperty:e,rootData:s})||(i=null===i?T.errors:i.concat(T.errors),a=i.length),r!==a)break}}l=r===a}else l=!0}}}}}}}}}}}}}}}}}}}}}}}}}}}}}return Z.errors=i,0===a}function K(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!Array.isArray(e))return K.errors=[{params:{type:"array"}}],!1;{const n=e.length;for(let r=0;r<n;r++){let n=e[r];const l=i,p=i;let f=!1;const u=i;if("..."!==n){const e={params:{}};null===s?s=[e]:s.push(e),i++}var a=u===i;if(f=f||a,!f){const l=i;if(!1!==n&&0!==n&&""!==n&&null!=n){const e={params:{}};null===s?s=[e]:s.push(e),i++}if(a=l===i,f=f||a,!f){const l=i;Z(n,{instancePath:t+"/"+r,parentData:e,parentDataProperty:r,rootData:o})||(s=null===s?Z.errors:s.concat(Z.errors),i=s.length),a=l===i,f=f||a}}if(!f){const e={params:{}};return null===s?s=[e]:s.push(e),i++,K.errors=s,!1}if(i=p,null!==s&&(p?s.length=p:s=null),l!==i)break}}}return K.errors=s,0===i}const X={type:"object",additionalProperties:{type:"object",additionalProperties:!0},properties:{asset:{$ref:"#/definitions/AssetGeneratorOptions"},"asset/inline":{$ref:"#/definitions/AssetInlineGeneratorOptions"},"asset/resource":{$ref:"#/definitions/AssetResourceGeneratorOptions"},css:{$ref:"#/definitions/CssGeneratorOptions"},"css/auto":{$ref:"#/definitions/CssAutoGeneratorOptions"},"css/global":{$ref:"#/definitions/CssGlobalGeneratorOptions"},"css/module":{$ref:"#/definitions/CssModuleGeneratorOptions"},javascript:{$ref:"#/definitions/EmptyGeneratorOptions"},"javascript/auto":{$ref:"#/definitions/EmptyGeneratorOptions"},"javascript/dynamic":{$ref:"#/definitions/EmptyGeneratorOptions"},"javascript/esm":{$ref:"#/definitions/EmptyGeneratorOptions"},json:{$ref:"#/definitions/JsonGeneratorOptions"}}};function Y(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(i==i)if(e&&"object"==typeof e&&!Array.isArray(e)){const t=i;for(const t in e)if("encoding"!==t&&"mimetype"!==t){const e={params:{additionalProperty:t}};null===s?s=[e]:s.push(e),i++;break}if(t===i){if(void 0!==e.encoding){let t=e.encoding;const n=i;if(!1!==t&&"base64"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=n===i}else f=!0;if(f)if(void 0!==e.mimetype){const t=i;if("string"!=typeof e.mimetype){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}f=t===i}else f=!0}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}var u=p===i;if(l=l||u,!l){const t=i;if(!(e instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}u=t===i,l=l||u}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,Y.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),Y.errors=s,0===i}function ee(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;if(0===a){if(!t||"object"!=typeof t||Array.isArray(t))return ee.errors=[{params:{type:"object"}}],!1;{const r=a;for(const e in t)if("binary"!==e&&"dataUrl"!==e&&"emit"!==e&&"filename"!==e&&"outputPath"!==e&&"publicPath"!==e)return ee.errors=[{params:{additionalProperty:e}}],!1;if(r===a){if(void 0!==t.binary){const e=a;if("boolean"!=typeof t.binary)return ee.errors=[{params:{type:"boolean"}}],!1;var l=e===a}else l=!0;if(l){if(void 0!==t.dataUrl){const e=a;Y(t.dataUrl,{instancePath:n+"/dataUrl",parentData:t,parentDataProperty:"dataUrl",rootData:s})||(i=null===i?Y.errors:i.concat(Y.errors),a=i.length),l=e===a}else l=!0;if(l){if(void 0!==t.emit){const e=a;if("boolean"!=typeof t.emit)return ee.errors=[{params:{type:"boolean"}}],!1;l=e===a}else l=!0;if(l){if(void 0!==t.filename){let n=t.filename;const r=a,o=a;let s=!1;const f=a;if(a===f)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}else if(n.length<1){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var p=f===a;if(s=s||p,!s){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}p=e===a,s=s||p}if(!s){const e={params:{}};return null===i?i=[e]:i.push(e),a++,ee.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),l=r===a}else l=!0;if(l){if(void 0!==t.outputPath){let n=t.outputPath;const r=a,o=a;let s=!1;const p=a;if(a===p)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var f=p===a;if(s=s||f,!s){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}f=e===a,s=s||f}if(!s){const e={params:{}};return null===i?i=[e]:i.push(e),a++,ee.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),l=r===a}else l=!0;if(l)if(void 0!==t.publicPath){let e=t.publicPath;const n=a,r=a;let o=!1;const s=a;if("string"!=typeof e){const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var u=s===a;if(o=o||u,!o){const t=a;if(!(e instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}u=t===a,o=o||u}if(!o){const e={params:{}};return null===i?i=[e]:i.push(e),a++,ee.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0}}}}}}}return ee.errors=i,0===a}function te(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return te.errors=[{params:{type:"object"}}],!1;{const n=i;for(const t in e)if("binary"!==t&&"dataUrl"!==t)return te.errors=[{params:{additionalProperty:t}}],!1;if(n===i){if(void 0!==e.binary){const t=i;if("boolean"!=typeof e.binary)return te.errors=[{params:{type:"boolean"}}],!1;var a=t===i}else a=!0;if(a)if(void 0!==e.dataUrl){const n=i;Y(e.dataUrl,{instancePath:t+"/dataUrl",parentData:e,parentDataProperty:"dataUrl",rootData:o})||(s=null===s?Y.errors:s.concat(Y.errors),i=s.length),a=n===i}else a=!0}}}return te.errors=s,0===i}function ne(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;if(0===a){if(!t||"object"!=typeof t||Array.isArray(t))return ne.errors=[{params:{type:"object"}}],!1;{const n=a;for(const e in t)if("binary"!==e&&"emit"!==e&&"filename"!==e&&"outputPath"!==e&&"publicPath"!==e)return ne.errors=[{params:{additionalProperty:e}}],!1;if(n===a){if(void 0!==t.binary){const e=a;if("boolean"!=typeof t.binary)return ne.errors=[{params:{type:"boolean"}}],!1;var l=e===a}else l=!0;if(l){if(void 0!==t.emit){const e=a;if("boolean"!=typeof t.emit)return ne.errors=[{params:{type:"boolean"}}],!1;l=e===a}else l=!0;if(l){if(void 0!==t.filename){let n=t.filename;const r=a,o=a;let s=!1;const f=a;if(a===f)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}else if(n.length<1){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var p=f===a;if(s=s||p,!s){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}p=e===a,s=s||p}if(!s){const e={params:{}};return null===i?i=[e]:i.push(e),a++,ne.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),l=r===a}else l=!0;if(l){if(void 0!==t.outputPath){let n=t.outputPath;const r=a,o=a;let s=!1;const p=a;if(a===p)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var f=p===a;if(s=s||f,!s){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}f=e===a,s=s||f}if(!s){const e={params:{}};return null===i?i=[e]:i.push(e),a++,ne.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),l=r===a}else l=!0;if(l)if(void 0!==t.publicPath){let e=t.publicPath;const n=a,r=a;let o=!1;const s=a;if("string"!=typeof e){const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var u=s===a;if(o=o||u,!o){const t=a;if(!(e instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}u=t===a,o=o||u}if(!o){const e={params:{}};return null===i?i=[e]:i.push(e),a++,ne.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0}}}}}}return ne.errors=i,0===a}function re(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){if(!e||"object"!=typeof e||Array.isArray(e))return re.errors=[{params:{type:"object"}}],!1;{const t=0;for(const t in e)if("esModule"!==t&&"exportsOnly"!==t)return re.errors=[{params:{additionalProperty:t}}],!1;if(0===t){if(void 0!==e.esModule){const t=0;if("boolean"!=typeof e.esModule)return re.errors=[{params:{type:"boolean"}}],!1;var s=0===t}else s=!0;if(s)if(void 0!==e.exportsOnly){const t=0;if("boolean"!=typeof e.exportsOnly)return re.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0}}return re.errors=null,!0}function oe(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return oe.errors=[{params:{type:"object"}}],!1;{const t=i;for(const t in e)if("esModule"!==t&&"exportsConvention"!==t&&"exportsOnly"!==t&&"localIdentName"!==t)return oe.errors=[{params:{additionalProperty:t}}],!1;if(t===i){if(void 0!==e.esModule){const t=i;if("boolean"!=typeof e.esModule)return oe.errors=[{params:{type:"boolean"}}],!1;var a=t===i}else a=!0;if(a){if(void 0!==e.exportsConvention){let t=e.exportsConvention;const n=i,r=i;let o=!1;const p=i;if("as-is"!==t&&"camel-case"!==t&&"camel-case-only"!==t&&"dashes"!==t&&"dashes-only"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var l=p===i;if(o=o||l,!o){const e=i;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}l=e===i,o=o||l}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,oe.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0;if(a){if(void 0!==e.exportsOnly){const t=i;if("boolean"!=typeof e.exportsOnly)return oe.errors=[{params:{type:"boolean"}}],!1;a=t===i}else a=!0;if(a)if(void 0!==e.localIdentName){const t=i;if("string"!=typeof e.localIdentName)return oe.errors=[{params:{type:"string"}}],!1;a=t===i}else a=!0}}}}}return oe.errors=s,0===i}function se(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return se.errors=[{params:{type:"object"}}],!1;{const t=i;for(const t in e)if("esModule"!==t&&"exportsConvention"!==t&&"exportsOnly"!==t&&"localIdentName"!==t)return se.errors=[{params:{additionalProperty:t}}],!1;if(t===i){if(void 0!==e.esModule){const t=i;if("boolean"!=typeof e.esModule)return se.errors=[{params:{type:"boolean"}}],!1;var a=t===i}else a=!0;if(a){if(void 0!==e.exportsConvention){let t=e.exportsConvention;const n=i,r=i;let o=!1;const p=i;if("as-is"!==t&&"camel-case"!==t&&"camel-case-only"!==t&&"dashes"!==t&&"dashes-only"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var l=p===i;if(o=o||l,!o){const e=i;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}l=e===i,o=o||l}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,se.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0;if(a){if(void 0!==e.exportsOnly){const t=i;if("boolean"!=typeof e.exportsOnly)return se.errors=[{params:{type:"boolean"}}],!1;a=t===i}else a=!0;if(a)if(void 0!==e.localIdentName){const t=i;if("string"!=typeof e.localIdentName)return se.errors=[{params:{type:"string"}}],!1;a=t===i}else a=!0}}}}}return se.errors=s,0===i}function ie(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return ie.errors=[{params:{type:"object"}}],!1;{const t=i;for(const t in e)if("esModule"!==t&&"exportsConvention"!==t&&"exportsOnly"!==t&&"localIdentName"!==t)return ie.errors=[{params:{additionalProperty:t}}],!1;if(t===i){if(void 0!==e.esModule){const t=i;if("boolean"!=typeof e.esModule)return ie.errors=[{params:{type:"boolean"}}],!1;var a=t===i}else a=!0;if(a){if(void 0!==e.exportsConvention){let t=e.exportsConvention;const n=i,r=i;let o=!1;const p=i;if("as-is"!==t&&"camel-case"!==t&&"camel-case-only"!==t&&"dashes"!==t&&"dashes-only"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var l=p===i;if(o=o||l,!o){const e=i;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}l=e===i,o=o||l}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,ie.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),a=n===i}else a=!0;if(a){if(void 0!==e.exportsOnly){const t=i;if("boolean"!=typeof e.exportsOnly)return ie.errors=[{params:{type:"boolean"}}],!1;a=t===i}else a=!0;if(a)if(void 0!==e.localIdentName){const t=i;if("string"!=typeof e.localIdentName)return ie.errors=[{params:{type:"string"}}],!1;a=t===i}else a=!0}}}}}return ie.errors=s,0===i}function ae(e,{instancePath:t="",parentData:r,parentDataProperty:o,rootData:s=e}={}){let i=null,a=0;if(0===a){if(!e||"object"!=typeof e||Array.isArray(e))return ae.errors=[{params:{type:"object"}}],!1;{const r=a;for(const t in e)if(!n.call(X.properties,t)){let n=e[t];const r=a;if(a===r&&(!n||"object"!=typeof n||Array.isArray(n)))return ae.errors=[{params:{type:"object"}}],!1;if(r!==a)break}if(r===a){if(void 0!==e.asset){const n=a;ee(e.asset,{instancePath:t+"/asset",parentData:e,parentDataProperty:"asset",rootData:s})||(i=null===i?ee.errors:i.concat(ee.errors),a=i.length);var l=n===a}else l=!0;if(l){if(void 0!==e["asset/inline"]){const n=a;te(e["asset/inline"],{instancePath:t+"/asset~1inline",parentData:e,parentDataProperty:"asset/inline",rootData:s})||(i=null===i?te.errors:i.concat(te.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["asset/resource"]){const n=a;ne(e["asset/resource"],{instancePath:t+"/asset~1resource",parentData:e,parentDataProperty:"asset/resource",rootData:s})||(i=null===i?ne.errors:i.concat(ne.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e.css){const n=a;re(e.css,{instancePath:t+"/css",parentData:e,parentDataProperty:"css",rootData:s})||(i=null===i?re.errors:i.concat(re.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["css/auto"]){const n=a;oe(e["css/auto"],{instancePath:t+"/css~1auto",parentData:e,parentDataProperty:"css/auto",rootData:s})||(i=null===i?oe.errors:i.concat(oe.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["css/global"]){const n=a;se(e["css/global"],{instancePath:t+"/css~1global",parentData:e,parentDataProperty:"css/global",rootData:s})||(i=null===i?se.errors:i.concat(se.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["css/module"]){const n=a;ie(e["css/module"],{instancePath:t+"/css~1module",parentData:e,parentDataProperty:"css/module",rootData:s})||(i=null===i?ie.errors:i.concat(ie.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e.javascript){let t=e.javascript;const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return ae.errors=[{params:{type:"object"}}],!1;for(const e in t)return ae.errors=[{params:{additionalProperty:e}}],!1}l=n===a}else l=!0;if(l){if(void 0!==e["javascript/auto"]){let t=e["javascript/auto"];const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return ae.errors=[{params:{type:"object"}}],!1;for(const e in t)return ae.errors=[{params:{additionalProperty:e}}],!1}l=n===a}else l=!0;if(l){if(void 0!==e["javascript/dynamic"]){let t=e["javascript/dynamic"];const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return ae.errors=[{params:{type:"object"}}],!1;for(const e in t)return ae.errors=[{params:{additionalProperty:e}}],!1}l=n===a}else l=!0;if(l){if(void 0!==e["javascript/esm"]){let t=e["javascript/esm"];const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return ae.errors=[{params:{type:"object"}}],!1;for(const e in t)return ae.errors=[{params:{additionalProperty:e}}],!1}l=n===a}else l=!0;if(l)if(void 0!==e.json){let t=e.json;const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return ae.errors=[{params:{type:"object"}}],!1;{const e=a;for(const e in t)if("JSONParse"!==e)return ae.errors=[{params:{additionalProperty:e}}],!1;if(e===a&&void 0!==t.JSONParse&&"boolean"!=typeof t.JSONParse)return ae.errors=[{params:{type:"boolean"}}],!1}}l=n===a}else l=!0}}}}}}}}}}}}}return ae.errors=i,0===a}const le={type:"object",additionalProperties:{type:"object",additionalProperties:!0},properties:{asset:{$ref:"#/definitions/AssetParserOptions"},"asset/inline":{$ref:"#/definitions/EmptyParserOptions"},"asset/resource":{$ref:"#/definitions/EmptyParserOptions"},"asset/source":{$ref:"#/definitions/EmptyParserOptions"},css:{$ref:"#/definitions/CssParserOptions"},"css/auto":{$ref:"#/definitions/CssAutoParserOptions"},"css/global":{$ref:"#/definitions/CssGlobalParserOptions"},"css/module":{$ref:"#/definitions/CssModuleParserOptions"},javascript:{$ref:"#/definitions/JavascriptParserOptions"},"javascript/auto":{$ref:"#/definitions/JavascriptParserOptions"},"javascript/dynamic":{$ref:"#/definitions/JavascriptParserOptions"},"javascript/esm":{$ref:"#/definitions/JavascriptParserOptions"},json:{$ref:"#/definitions/JsonParserOptions"}}};function pe(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return pe.errors=[{params:{type:"object"}}],!1;{const t=i;for(const t in e)if("dataUrlCondition"!==t)return pe.errors=[{params:{additionalProperty:t}}],!1;if(t===i&&void 0!==e.dataUrlCondition){let t=e.dataUrlCondition;const n=i;let r=!1;const o=i;if(i==i)if(t&&"object"==typeof t&&!Array.isArray(t)){const e=i;for(const e in t)if("maxSize"!==e){const t={params:{additionalProperty:e}};null===s?s=[t]:s.push(t),i++;break}if(e===i&&void 0!==t.maxSize&&"number"!=typeof t.maxSize){const e={params:{type:"number"}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}var a=o===i;if(r=r||a,!r){const e=i;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}a=e===i,r=r||a}if(!r){const e={params:{}};return null===s?s=[e]:s.push(e),i++,pe.errors=s,!1}i=n,null!==s&&(n?s.length=n:s=null)}}}return pe.errors=s,0===i}function fe(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){if(!e||"object"!=typeof e||Array.isArray(e))return fe.errors=[{params:{type:"object"}}],!1;{const t=0;for(const t in e)if("import"!==t&&"namedExports"!==t&&"url"!==t)return fe.errors=[{params:{additionalProperty:t}}],!1;if(0===t){if(void 0!==e.import){const t=0;if("boolean"!=typeof e.import)return fe.errors=[{params:{type:"boolean"}}],!1;var s=0===t}else s=!0;if(s){if(void 0!==e.namedExports){const t=0;if("boolean"!=typeof e.namedExports)return fe.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0;if(s)if(void 0!==e.url){const t=0;if("boolean"!=typeof e.url)return fe.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0}}}return fe.errors=null,!0}function ue(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){if(!e||"object"!=typeof e||Array.isArray(e))return ue.errors=[{params:{type:"object"}}],!1;{const t=0;for(const t in e)if("import"!==t&&"namedExports"!==t&&"url"!==t)return ue.errors=[{params:{additionalProperty:t}}],!1;if(0===t){if(void 0!==e.import){const t=0;if("boolean"!=typeof e.import)return ue.errors=[{params:{type:"boolean"}}],!1;var s=0===t}else s=!0;if(s){if(void 0!==e.namedExports){const t=0;if("boolean"!=typeof e.namedExports)return ue.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0;if(s)if(void 0!==e.url){const t=0;if("boolean"!=typeof e.url)return ue.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0}}}return ue.errors=null,!0}function ce(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){if(!e||"object"!=typeof e||Array.isArray(e))return ce.errors=[{params:{type:"object"}}],!1;{const t=0;for(const t in e)if("import"!==t&&"namedExports"!==t&&"url"!==t)return ce.errors=[{params:{additionalProperty:t}}],!1;if(0===t){if(void 0!==e.import){const t=0;if("boolean"!=typeof e.import)return ce.errors=[{params:{type:"boolean"}}],!1;var s=0===t}else s=!0;if(s){if(void 0!==e.namedExports){const t=0;if("boolean"!=typeof e.namedExports)return ce.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0;if(s)if(void 0!==e.url){const t=0;if("boolean"!=typeof e.url)return ce.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0}}}return ce.errors=null,!0}function ye(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){if(!e||"object"!=typeof e||Array.isArray(e))return ye.errors=[{params:{type:"object"}}],!1;{const t=0;for(const t in e)if("import"!==t&&"namedExports"!==t&&"url"!==t)return ye.errors=[{params:{additionalProperty:t}}],!1;if(0===t){if(void 0!==e.import){const t=0;if("boolean"!=typeof e.import)return ye.errors=[{params:{type:"boolean"}}],!1;var s=0===t}else s=!0;if(s){if(void 0!==e.namedExports){const t=0;if("boolean"!=typeof e.namedExports)return ye.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0;if(s)if(void 0!==e.url){const t=0;if("boolean"!=typeof e.url)return ye.errors=[{params:{type:"boolean"}}],!1;s=0===t}else s=!0}}}return ye.errors=null,!0}function me(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(!1!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const t=i;if(i==i)if(e&&"object"==typeof e&&!Array.isArray(e)){const t=i;for(const t in e)if("__dirname"!==t&&"__filename"!==t&&"global"!==t){const e={params:{additionalProperty:t}};null===s?s=[e]:s.push(e),i++;break}if(t===i){if(void 0!==e.__dirname){let t=e.__dirname;const n=i;if(!1!==t&&!0!==t&&"warn-mock"!==t&&"mock"!==t&&"node-module"!==t&&"eval-only"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var u=n===i}else u=!0;if(u){if(void 0!==e.__filename){let t=e.__filename;const n=i;if(!1!==t&&!0!==t&&"warn-mock"!==t&&"mock"!==t&&"node-module"!==t&&"eval-only"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}u=n===i}else u=!0;if(u)if(void 0!==e.global){let t=e.global;const n=i;if(!1!==t&&!0!==t&&"warn"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}u=n===i}else u=!0}}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}f=t===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,me.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),me.errors=s,0===i}function de(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!e||"object"!=typeof e||Array.isArray(e))return de.errors=[{params:{type:"object"}}],!1;if(void 0!==e.amd){let t=e.amd;const n=i,r=i;let o=!1;const p=i;if(!1!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var a=p===i;if(o=o||a,!o){const e=i;if(!t||"object"!=typeof t||Array.isArray(t)){const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}a=e===i,o=o||a}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,de.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null);var l=n===i}else l=!0;if(l){if(void 0!==e.browserify){const t=i;if("boolean"!=typeof e.browserify)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.commonjs){const t=i;if("boolean"!=typeof e.commonjs)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.commonjsMagicComments){const t=i;if("boolean"!=typeof e.commonjsMagicComments)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.createRequire){let t=e.createRequire;const n=i,r=i;let o=!1;const a=i;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}var p=a===i;if(o=o||p,!o){const e=i;if("string"!=typeof t){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}p=e===i,o=o||p}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,de.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),l=n===i}else l=!0;if(l){if(void 0!==e.dynamicImportFetchPriority){let t=e.dynamicImportFetchPriority;const n=i;if("low"!==t&&"high"!==t&&"auto"!==t&&!1!==t)return de.errors=[{params:{}}],!1;l=n===i}else l=!0;if(l){if(void 0!==e.dynamicImportMode){let t=e.dynamicImportMode;const n=i;if("eager"!==t&&"weak"!==t&&"lazy"!==t&&"lazy-once"!==t)return de.errors=[{params:{}}],!1;l=n===i}else l=!0;if(l){if(void 0!==e.dynamicImportPrefetch){let t=e.dynamicImportPrefetch;const n=i,r=i;let o=!1;const a=i;if("number"!=typeof t){const e={params:{type:"number"}};null===s?s=[e]:s.push(e),i++}var f=a===i;if(o=o||f,!o){const e=i;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}f=e===i,o=o||f}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,de.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),l=n===i}else l=!0;if(l){if(void 0!==e.dynamicImportPreload){let t=e.dynamicImportPreload;const n=i,r=i;let o=!1;const a=i;if("number"!=typeof t){const e={params:{type:"number"}};null===s?s=[e]:s.push(e),i++}var u=a===i;if(o=o||u,!o){const e=i;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}u=e===i,o=o||u}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,de.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),l=n===i}else l=!0;if(l){if(void 0!==e.dynamicUrl){const t=i;if("boolean"!=typeof e.dynamicUrl)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.exportsPresence){let t=e.exportsPresence;const n=i;if("error"!==t&&"warn"!==t&&"auto"!==t&&!1!==t)return de.errors=[{params:{}}],!1;l=n===i}else l=!0;if(l){if(void 0!==e.exprContextCritical){const t=i;if("boolean"!=typeof e.exprContextCritical)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.exprContextRecursive){const t=i;if("boolean"!=typeof e.exprContextRecursive)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.exprContextRegExp){let t=e.exprContextRegExp;const n=i,r=i;let o=!1;const a=i;if(!(t instanceof RegExp)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var c=a===i;if(o=o||c,!o){const e=i;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}c=e===i,o=o||c}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,de.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),l=n===i}else l=!0;if(l){if(void 0!==e.exprContextRequest){const t=i;if("string"!=typeof e.exprContextRequest)return de.errors=[{params:{type:"string"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.harmony){const t=i;if("boolean"!=typeof e.harmony)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.import){const t=i;if("boolean"!=typeof e.import)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.importExportsPresence){let t=e.importExportsPresence;const n=i;if("error"!==t&&"warn"!==t&&"auto"!==t&&!1!==t)return de.errors=[{params:{}}],!1;l=n===i}else l=!0;if(l){if(void 0!==e.importMeta){const t=i;if("boolean"!=typeof e.importMeta)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.importMetaContext){const t=i;if("boolean"!=typeof e.importMetaContext)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.node){const n=i;me(e.node,{instancePath:t+"/node",parentData:e,parentDataProperty:"node",rootData:o})||(s=null===s?me.errors:s.concat(me.errors),i=s.length),l=n===i}else l=!0;if(l){if(void 0!==e.overrideStrict){let t=e.overrideStrict;const n=i;if("strict"!==t&&"non-strict"!==t)return de.errors=[{params:{}}],!1;l=n===i}else l=!0;if(l){if(void 0!==e.reexportExportsPresence){let t=e.reexportExportsPresence;const n=i;if("error"!==t&&"warn"!==t&&"auto"!==t&&!1!==t)return de.errors=[{params:{}}],!1;l=n===i}else l=!0;if(l){if(void 0!==e.requireContext){const t=i;if("boolean"!=typeof e.requireContext)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.requireEnsure){const t=i;if("boolean"!=typeof e.requireEnsure)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.requireInclude){const t=i;if("boolean"!=typeof e.requireInclude)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.requireJs){const t=i;if("boolean"!=typeof e.requireJs)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.strictExportPresence){const t=i;if("boolean"!=typeof e.strictExportPresence)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.strictThisContextOnImports){const t=i;if("boolean"!=typeof e.strictThisContextOnImports)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.system){const t=i;if("boolean"!=typeof e.system)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.unknownContextCritical){const t=i;if("boolean"!=typeof e.unknownContextCritical)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.unknownContextRecursive){const t=i;if("boolean"!=typeof e.unknownContextRecursive)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.unknownContextRegExp){let t=e.unknownContextRegExp;const n=i,r=i;let o=!1;const a=i;if(!(t instanceof RegExp)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var y=a===i;if(o=o||y,!o){const e=i;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}y=e===i,o=o||y}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,de.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),l=n===i}else l=!0;if(l){if(void 0!==e.unknownContextRequest){const t=i;if("string"!=typeof e.unknownContextRequest)return de.errors=[{params:{type:"string"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.url){let t=e.url;const n=i,r=i;let o=!1;const a=i;if("relative"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var m=a===i;if(o=o||m,!o){const e=i;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}m=e===i,o=o||m}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,de.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),l=n===i}else l=!0;if(l){if(void 0!==e.worker){let t=e.worker;const n=i,r=i;let o=!1;const a=i;if(i===a)if(Array.isArray(t)){const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=i;if(i===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}if(r!==i)break}}else{const e={params:{type:"array"}};null===s?s=[e]:s.push(e),i++}var d=a===i;if(o=o||d,!o){const e=i;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}d=e===i,o=o||d}if(!o){const e={params:{}};return null===s?s=[e]:s.push(e),i++,de.errors=s,!1}i=r,null!==s&&(r?s.length=r:s=null),l=n===i}else l=!0;if(l){if(void 0!==e.wrappedContextCritical){const t=i;if("boolean"!=typeof e.wrappedContextCritical)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l){if(void 0!==e.wrappedContextRecursive){const t=i;if("boolean"!=typeof e.wrappedContextRecursive)return de.errors=[{params:{type:"boolean"}}],!1;l=t===i}else l=!0;if(l)if(void 0!==e.wrappedContextRegExp){const t=i;if(!(e.wrappedContextRegExp instanceof RegExp))return de.errors=[{params:{}}],!1;l=t===i}else l=!0}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}return de.errors=s,0===i}function he(e,{instancePath:t="",parentData:r,parentDataProperty:o,rootData:s=e}={}){let i=null,a=0;if(0===a){if(!e||"object"!=typeof e||Array.isArray(e))return he.errors=[{params:{type:"object"}}],!1;{const r=a;for(const t in e)if(!n.call(le.properties,t)){let n=e[t];const r=a;if(a===r&&(!n||"object"!=typeof n||Array.isArray(n)))return he.errors=[{params:{type:"object"}}],!1;if(r!==a)break}if(r===a){if(void 0!==e.asset){const n=a;pe(e.asset,{instancePath:t+"/asset",parentData:e,parentDataProperty:"asset",rootData:s})||(i=null===i?pe.errors:i.concat(pe.errors),a=i.length);var l=n===a}else l=!0;if(l){if(void 0!==e["asset/inline"]){let t=e["asset/inline"];const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return he.errors=[{params:{type:"object"}}],!1;for(const e in t)return he.errors=[{params:{additionalProperty:e}}],!1}l=n===a}else l=!0;if(l){if(void 0!==e["asset/resource"]){let t=e["asset/resource"];const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return he.errors=[{params:{type:"object"}}],!1;for(const e in t)return he.errors=[{params:{additionalProperty:e}}],!1}l=n===a}else l=!0;if(l){if(void 0!==e["asset/source"]){let t=e["asset/source"];const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return he.errors=[{params:{type:"object"}}],!1;for(const e in t)return he.errors=[{params:{additionalProperty:e}}],!1}l=n===a}else l=!0;if(l){if(void 0!==e.css){const n=a;fe(e.css,{instancePath:t+"/css",parentData:e,parentDataProperty:"css",rootData:s})||(i=null===i?fe.errors:i.concat(fe.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["css/auto"]){const n=a;ue(e["css/auto"],{instancePath:t+"/css~1auto",parentData:e,parentDataProperty:"css/auto",rootData:s})||(i=null===i?ue.errors:i.concat(ue.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["css/global"]){const n=a;ce(e["css/global"],{instancePath:t+"/css~1global",parentData:e,parentDataProperty:"css/global",rootData:s})||(i=null===i?ce.errors:i.concat(ce.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["css/module"]){const n=a;ye(e["css/module"],{instancePath:t+"/css~1module",parentData:e,parentDataProperty:"css/module",rootData:s})||(i=null===i?ye.errors:i.concat(ye.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e.javascript){const n=a;de(e.javascript,{instancePath:t+"/javascript",parentData:e,parentDataProperty:"javascript",rootData:s})||(i=null===i?de.errors:i.concat(de.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["javascript/auto"]){const n=a;de(e["javascript/auto"],{instancePath:t+"/javascript~1auto",parentData:e,parentDataProperty:"javascript/auto",rootData:s})||(i=null===i?de.errors:i.concat(de.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["javascript/dynamic"]){const n=a;de(e["javascript/dynamic"],{instancePath:t+"/javascript~1dynamic",parentData:e,parentDataProperty:"javascript/dynamic",rootData:s})||(i=null===i?de.errors:i.concat(de.errors),a=i.length),l=n===a}else l=!0;if(l){if(void 0!==e["javascript/esm"]){const n=a;de(e["javascript/esm"],{instancePath:t+"/javascript~1esm",parentData:e,parentDataProperty:"javascript/esm",rootData:s})||(i=null===i?de.errors:i.concat(de.errors),a=i.length),l=n===a}else l=!0;if(l)if(void 0!==e.json){let t=e.json;const n=a;if(a==a){if(!t||"object"!=typeof t||Array.isArray(t))return he.errors=[{params:{type:"object"}}],!1;{const e=a;for(const e in t)if("exportsDepth"!==e&&"parse"!==e)return he.errors=[{params:{additionalProperty:e}}],!1;if(e===a){if(void 0!==t.exportsDepth){const e=a;if("number"!=typeof t.exportsDepth)return he.errors=[{params:{type:"number"}}],!1;var p=e===a}else p=!0;if(p)if(void 0!==t.parse){const e=a;if(!(t.parse instanceof Function))return he.errors=[{params:{}}],!1;p=e===a}else p=!0}}}l=n===a}else l=!0}}}}}}}}}}}}}}return he.errors=i,0===a}function be(t,{instancePath:r="",parentData:o,parentDataProperty:s,rootData:i=t}={}){let a=null,l=0;if(0===l){if(!t||"object"!=typeof t||Array.isArray(t))return be.errors=[{params:{type:"object"}}],!1;{const o=l;for(const e in t)if(!n.call(R.properties,e))return be.errors=[{params:{additionalProperty:e}}],!1;if(o===l){if(void 0!==t.defaultRules){const e=l,n=l;let o=!1,s=null;const f=l;if(K(t.defaultRules,{instancePath:r+"/defaultRules",parentData:t,parentDataProperty:"defaultRules",rootData:i})||(a=null===a?K.errors:a.concat(K.errors),l=a.length),f===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,be.errors=a,!1}l=n,null!==a&&(n?a.length=n:a=null);var p=e===l}else p=!0;if(p){if(void 0!==t.exprContextCritical){const e=l;if("boolean"!=typeof t.exprContextCritical)return be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.exprContextRecursive){const e=l;if("boolean"!=typeof t.exprContextRecursive)return be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.exprContextRegExp){let e=t.exprContextRegExp;const n=l,r=l;let o=!1;const s=l;if(!(e instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}var f=s===l;if(o=o||f,!o){const t=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}f=t===l,o=o||f}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.exprContextRequest){const e=l;if("string"!=typeof t.exprContextRequest)return be.errors=[{params:{type:"string"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.generator){const e=l;ae(t.generator,{instancePath:r+"/generator",parentData:t,parentDataProperty:"generator",rootData:i})||(a=null===a?ae.errors:a.concat(ae.errors),l=a.length),p=e===l}else p=!0;if(p){if(void 0!==t.noParse){let n=t.noParse;const r=l,o=l;let s=!1;const i=l;if(l===i)if(Array.isArray(n))if(n.length<1){const e={params:{limit:1}};null===a?a=[e]:a.push(e),l++}else{const t=n.length;for(let r=0;r<t;r++){let t=n[r];const o=l,s=l;let i=!1;const p=l;if(!(t instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}var u=p===l;if(i=i||u,!i){const n=l;if(l===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(u=n===l,i=i||u,!i){const e=l;if(!(t instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}u=e===l,i=i||u}}if(i)l=s,null!==a&&(s?a.length=s:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(o!==l)break}}else{const e={params:{type:"array"}};null===a?a=[e]:a.push(e),l++}var c=i===l;if(s=s||c,!s){const t=l;if(!(n instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(c=t===l,s=s||c,!s){const t=l;if(l===t)if("string"==typeof n){if(n.includes("!")||!0!==e.test(n)){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(c=t===l,s=s||c,!s){const e=l;if(!(n instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}c=e===l,s=s||c}}}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,be.errors=a,!1}l=o,null!==a&&(o?a.length=o:a=null),p=r===l}else p=!0;if(p){if(void 0!==t.parser){const e=l;he(t.parser,{instancePath:r+"/parser",parentData:t,parentDataProperty:"parser",rootData:i})||(a=null===a?he.errors:a.concat(he.errors),l=a.length),p=e===l}else p=!0;if(p){if(void 0!==t.rules){const e=l,n=l;let o=!1,s=null;const f=l;if(K(t.rules,{instancePath:r+"/rules",parentData:t,parentDataProperty:"rules",rootData:i})||(a=null===a?K.errors:a.concat(K.errors),l=a.length),f===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,be.errors=a,!1}l=n,null!==a&&(n?a.length=n:a=null),p=e===l}else p=!0;if(p){if(void 0!==t.strictExportPresence){const e=l;if("boolean"!=typeof t.strictExportPresence)return be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.strictThisContextOnImports){const e=l;if("boolean"!=typeof t.strictThisContextOnImports)return be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.unknownContextCritical){const e=l;if("boolean"!=typeof t.unknownContextCritical)return be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.unknownContextRecursive){const e=l;if("boolean"!=typeof t.unknownContextRecursive)return be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.unknownContextRegExp){let e=t.unknownContextRegExp;const n=l,r=l;let o=!1;const s=l;if(!(e instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}var y=s===l;if(o=o||y,!o){const t=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}y=t===l,o=o||y}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.unknownContextRequest){const e=l;if("string"!=typeof t.unknownContextRequest)return be.errors=[{params:{type:"string"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.unsafeCache){let e=t.unsafeCache;const n=l,r=l;let o=!1;const s=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}var m=s===l;if(o=o||m,!o){const t=l;if(!(e instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}m=t===l,o=o||m}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.wrappedContextCritical){const e=l;if("boolean"!=typeof t.wrappedContextCritical)return be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.wrappedContextRecursive){const e=l;if("boolean"!=typeof t.wrappedContextRecursive)return be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p)if(void 0!==t.wrappedContextRegExp){const e=l;if(!(t.wrappedContextRegExp instanceof RegExp))return be.errors=[{params:{}}],!1;p=e===l}else p=!0}}}}}}}}}}}}}}}}}}}}return be.errors=a,0===l}const ge={type:"object",additionalProperties:!1,properties:{avoidEntryIife:{type:"boolean"},checkWasmTypes:{type:"boolean"},chunkIds:{enum:["natural","named","deterministic","size","total-size",!1]},concatenateModules:{type:"boolean"},emitOnErrors:{type:"boolean"},flagIncludedChunks:{type:"boolean"},innerGraph:{type:"boolean"},mangleExports:{anyOf:[{enum:["size","deterministic"]},{type:"boolean"}]},mangleWasmImports:{type:"boolean"},mergeDuplicateChunks:{type:"boolean"},minimize:{type:"boolean"},minimizer:{type:"array",items:{anyOf:[{enum:["..."]},{$ref:"#/definitions/Falsy"},{$ref:"#/definitions/WebpackPluginInstance"},{$ref:"#/definitions/WebpackPluginFunction"}]}},moduleIds:{enum:["natural","named","hashed","deterministic","size",!1]},noEmitOnErrors:{type:"boolean"},nodeEnv:{anyOf:[{enum:[!1]},{type:"string"}]},portableRecords:{type:"boolean"},providedExports:{type:"boolean"},realContentHash:{type:"boolean"},removeAvailableModules:{type:"boolean"},removeEmptyChunks:{type:"boolean"},runtimeChunk:{$ref:"#/definitions/OptimizationRuntimeChunk"},sideEffects:{anyOf:[{enum:["flag"]},{type:"boolean"}]},splitChunks:{anyOf:[{enum:[!1]},{$ref:"#/definitions/OptimizationSplitChunksOptions"}]},usedExports:{anyOf:[{enum:["global"]},{type:"boolean"}]}}},ve={type:"object",additionalProperties:!1,properties:{automaticNameDelimiter:{type:"string",minLength:1},cacheGroups:{type:"object",additionalProperties:{anyOf:[{enum:[!1]},{instanceof:"RegExp"},{type:"string"},{$ref:"#/definitions/OptimizationSplitChunksGetCacheGroups"},{$ref:"#/definitions/OptimizationSplitChunksCacheGroup"}]},not:{type:"object",additionalProperties:!0,properties:{test:{anyOf:[{instanceof:"RegExp"},{type:"string"},{$ref:"#/definitions/OptimizationSplitChunksGetCacheGroups"}]}},required:["test"]}},chunks:{anyOf:[{enum:["initial","async","all"]},{instanceof:"RegExp"},{instanceof:"Function"}]},defaultSizeTypes:{type:"array",items:{type:"string"},minItems:1},enforceSizeThreshold:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},fallbackCacheGroup:{type:"object",additionalProperties:!1,properties:{automaticNameDelimiter:{type:"string",minLength:1},chunks:{anyOf:[{enum:["initial","async","all"]},{instanceof:"RegExp"},{instanceof:"Function"}]},maxAsyncSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxInitialSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSizeReduction:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]}}},filename:{anyOf:[{type:"string",absolutePath:!1,minLength:1},{instanceof:"Function"}]},hidePathInfo:{type:"boolean"},maxAsyncRequests:{type:"number",minimum:1},maxAsyncSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxInitialRequests:{type:"number",minimum:1},maxInitialSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minChunks:{type:"number",minimum:1},minRemainingSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSizeReduction:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},name:{anyOf:[{enum:[!1]},{type:"string"},{instanceof:"Function"}]},usedExports:{type:"boolean"}}},Pe={type:"object",additionalProperties:!1,properties:{automaticNameDelimiter:{type:"string",minLength:1},chunks:{anyOf:[{enum:["initial","async","all"]},{instanceof:"RegExp"},{instanceof:"Function"}]},enforce:{type:"boolean"},enforceSizeThreshold:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},filename:{anyOf:[{type:"string",absolutePath:!1,minLength:1},{instanceof:"Function"}]},idHint:{type:"string"},layer:{anyOf:[{instanceof:"RegExp"},{type:"string"},{instanceof:"Function"}]},maxAsyncRequests:{type:"number",minimum:1},maxAsyncSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxInitialRequests:{type:"number",minimum:1},maxInitialSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},maxSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minChunks:{type:"number",minimum:1},minRemainingSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSize:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},minSizeReduction:{oneOf:[{$ref:"#/definitions/OptimizationSplitChunksSizes"}]},name:{anyOf:[{enum:[!1]},{type:"string"},{instanceof:"Function"}]},priority:{type:"number"},reuseExistingChunk:{type:"boolean"},test:{anyOf:[{instanceof:"RegExp"},{type:"string"},{instanceof:"Function"}]},type:{anyOf:[{instanceof:"RegExp"},{type:"string"},{instanceof:"Function"}]},usedExports:{type:"boolean"}}};function De(t,{instancePath:r="",parentData:o,parentDataProperty:s,rootData:i=t}={}){let a=null,l=0;if(0===l){if(!t||"object"!=typeof t||Array.isArray(t))return De.errors=[{params:{type:"object"}}],!1;{const r=l;for(const e in t)if(!n.call(Pe.properties,e))return De.errors=[{params:{additionalProperty:e}}],!1;if(r===l){if(void 0!==t.automaticNameDelimiter){let e=t.automaticNameDelimiter;const n=l;if(l===n){if("string"!=typeof e)return De.errors=[{params:{type:"string"}}],!1;if(e.length<1)return De.errors=[{params:{}}],!1}var p=n===l}else p=!0;if(p){if(void 0!==t.chunks){let e=t.chunks;const n=l,r=l;let o=!1;const s=l;if("initial"!==e&&"async"!==e&&"all"!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var f=s===l;if(o=o||f,!o){const t=l;if(!(e instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(f=t===l,o=o||f,!o){const t=l;if(!(e instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}f=t===l,o=o||f}}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.enforce){const e=l;if("boolean"!=typeof t.enforce)return De.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.enforceSizeThreshold){let e=t.enforceSizeThreshold;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let c=!1;const y=l;if(l===y)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var u=y===l;if(c=c||u,!c){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}u=t===l,c=c||u}if(c)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.filename){let n=t.filename;const r=l,o=l;let s=!1;const i=l;if(l===i)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===a?a=[e]:a.push(e),l++}else if(n.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}var c=i===l;if(s=s||c,!s){const e=l;if(!(n instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}c=e===l,s=s||c}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=o,null!==a&&(o?a.length=o:a=null),p=r===l}else p=!0;if(p){if(void 0!==t.idHint){const e=l;if("string"!=typeof t.idHint)return De.errors=[{params:{type:"string"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.layer){let e=t.layer;const n=l,r=l;let o=!1;const s=l;if(!(e instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}var y=s===l;if(o=o||y,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(y=t===l,o=o||y,!o){const t=l;if(!(e instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}y=t===l,o=o||y}}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.maxAsyncRequests){let e=t.maxAsyncRequests;const n=l;if(l===n){if("number"!=typeof e)return De.errors=[{params:{type:"number"}}],!1;if(e<1||isNaN(e))return De.errors=[{params:{comparison:">=",limit:1}}],!1}p=n===l}else p=!0;if(p){if(void 0!==t.maxAsyncSize){let e=t.maxAsyncSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var m=c===l;if(u=u||m,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}m=t===l,u=u||m}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.maxInitialRequests){let e=t.maxInitialRequests;const n=l;if(l===n){if("number"!=typeof e)return De.errors=[{params:{type:"number"}}],!1;if(e<1||isNaN(e))return De.errors=[{params:{comparison:">=",limit:1}}],!1}p=n===l}else p=!0;if(p){if(void 0!==t.maxInitialSize){let e=t.maxInitialSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var d=c===l;if(u=u||d,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}d=t===l,u=u||d}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.maxSize){let e=t.maxSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var h=c===l;if(u=u||h,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}h=t===l,u=u||h}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.minChunks){let e=t.minChunks;const n=l;if(l===n){if("number"!=typeof e)return De.errors=[{params:{type:"number"}}],!1;if(e<1||isNaN(e))return De.errors=[{params:{comparison:">=",limit:1}}],!1}p=n===l}else p=!0;if(p){if(void 0!==t.minRemainingSize){let e=t.minRemainingSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var b=c===l;if(u=u||b,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}b=t===l,u=u||b}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.minSize){let e=t.minSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var g=c===l;if(u=u||g,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}g=t===l,u=u||g}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.minSizeReduction){let e=t.minSizeReduction;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var v=c===l;if(u=u||v,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}v=t===l,u=u||v}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.name){let e=t.name;const n=l,r=l;let o=!1;const s=l;if(!1!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var P=s===l;if(o=o||P,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(P=t===l,o=o||P,!o){const t=l;if(!(e instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}P=t===l,o=o||P}}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.priority){const e=l;if("number"!=typeof t.priority)return De.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.reuseExistingChunk){const e=l;if("boolean"!=typeof t.reuseExistingChunk)return De.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.test){let e=t.test;const n=l,r=l;let o=!1;const s=l;if(!(e instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}var D=s===l;if(o=o||D,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(D=t===l,o=o||D,!o){const t=l;if(!(e instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}D=t===l,o=o||D}}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.type){let e=t.type;const n=l,r=l;let o=!1;const s=l;if(!(e instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}var O=s===l;if(o=o||O,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(O=t===l,o=o||O,!o){const t=l;if(!(e instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}O=t===l,o=o||O}}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,De.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p)if(void 0!==t.usedExports){const e=l;if("boolean"!=typeof t.usedExports)return De.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0}}}}}}}}}}}}}}}}}}}}}}}return De.errors=a,0===l}function Oe(t,{instancePath:r="",parentData:o,parentDataProperty:s,rootData:i=t}={}){let a=null,l=0;if(0===l){if(!t||"object"!=typeof t||Array.isArray(t))return Oe.errors=[{params:{type:"object"}}],!1;{const o=l;for(const e in t)if(!n.call(ve.properties,e))return Oe.errors=[{params:{additionalProperty:e}}],!1;if(o===l){if(void 0!==t.automaticNameDelimiter){let e=t.automaticNameDelimiter;const n=l;if(l===n){if("string"!=typeof e)return Oe.errors=[{params:{type:"string"}}],!1;if(e.length<1)return Oe.errors=[{params:{}}],!1}var p=n===l}else p=!0;if(p){if(void 0!==t.cacheGroups){let e=t.cacheGroups;const n=l,o=l,s=l;if(l===s)if(e&&"object"==typeof e&&!Array.isArray(e)){let t;if(void 0===e.test&&(t="test")){const e={};null===a?a=[e]:a.push(e),l++}else if(void 0!==e.test){let t=e.test;const n=l;let r=!1;const o=l;if(!(t instanceof RegExp)){const e={};null===a?a=[e]:a.push(e),l++}var f=o===l;if(r=r||f,!r){const e=l;if("string"!=typeof t){const e={};null===a?a=[e]:a.push(e),l++}if(f=e===l,r=r||f,!r){const e=l;if(!(t instanceof Function)){const e={};null===a?a=[e]:a.push(e),l++}f=e===l,r=r||f}}if(r)l=n,null!==a&&(n?a.length=n:a=null);else{const e={};null===a?a=[e]:a.push(e),l++}}}else{const e={};null===a?a=[e]:a.push(e),l++}if(s===l)return Oe.errors=[{params:{}}],!1;if(l=o,null!==a&&(o?a.length=o:a=null),l===n){if(!e||"object"!=typeof e||Array.isArray(e))return Oe.errors=[{params:{type:"object"}}],!1;for(const t in e){let n=e[t];const o=l,s=l;let p=!1;const f=l;if(!1!==n){const e={params:{}};null===a?a=[e]:a.push(e),l++}var u=f===l;if(p=p||u,!p){const o=l;if(!(n instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(u=o===l,p=p||u,!p){const o=l;if("string"!=typeof n){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(u=o===l,p=p||u,!p){const o=l;if(!(n instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(u=o===l,p=p||u,!p){const o=l;De(n,{instancePath:r+"/cacheGroups/"+t.replace(/~/g,"~0").replace(/\//g,"~1"),parentData:e,parentDataProperty:t,rootData:i})||(a=null===a?De.errors:a.concat(De.errors),l=a.length),u=o===l,p=p||u}}}}if(!p){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}if(l=s,null!==a&&(s?a.length=s:a=null),o!==l)break}}p=n===l}else p=!0;if(p){if(void 0!==t.chunks){let e=t.chunks;const n=l,r=l;let o=!1;const s=l;if("initial"!==e&&"async"!==e&&"all"!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var c=s===l;if(o=o||c,!o){const t=l;if(!(e instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(c=t===l,o=o||c,!o){const t=l;if(!(e instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}c=t===l,o=o||c}}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.defaultSizeTypes){let e=t.defaultSizeTypes;const n=l;if(l===n){if(!Array.isArray(e))return Oe.errors=[{params:{type:"array"}}],!1;if(e.length<1)return Oe.errors=[{params:{limit:1}}],!1;{const t=e.length;for(let n=0;n<t;n++){const t=l;if("string"!=typeof e[n])return Oe.errors=[{params:{type:"string"}}],!1;if(t!==l)break}}}p=n===l}else p=!0;if(p){if(void 0!==t.enforceSizeThreshold){let e=t.enforceSizeThreshold;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var y=c===l;if(u=u||y,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}y=t===l,u=u||y}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.fallbackCacheGroup){let e=t.fallbackCacheGroup;const n=l;if(l===n){if(!e||"object"!=typeof e||Array.isArray(e))return Oe.errors=[{params:{type:"object"}}],!1;{const t=l;for(const t in e)if("automaticNameDelimiter"!==t&&"chunks"!==t&&"maxAsyncSize"!==t&&"maxInitialSize"!==t&&"maxSize"!==t&&"minSize"!==t&&"minSizeReduction"!==t)return Oe.errors=[{params:{additionalProperty:t}}],!1;if(t===l){if(void 0!==e.automaticNameDelimiter){let t=e.automaticNameDelimiter;const n=l;if(l===n){if("string"!=typeof t)return Oe.errors=[{params:{type:"string"}}],!1;if(t.length<1)return Oe.errors=[{params:{}}],!1}var m=n===l}else m=!0;if(m){if(void 0!==e.chunks){let t=e.chunks;const n=l,r=l;let o=!1;const s=l;if("initial"!==t&&"async"!==t&&"all"!==t){const e={params:{}};null===a?a=[e]:a.push(e),l++}var d=s===l;if(o=o||d,!o){const e=l;if(!(t instanceof RegExp)){const e={params:{}};null===a?a=[e]:a.push(e),l++}if(d=e===l,o=o||d,!o){const e=l;if(!(t instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}d=e===l,o=o||d}}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),m=n===l}else m=!0;if(m){if(void 0!==e.maxAsyncSize){let t=e.maxAsyncSize;const n=l,r=l;let o=!1,s=null;const i=l,p=l;let f=!1;const u=l;if(l===u)if("number"==typeof t){if(t<0||isNaN(t)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var h=u===l;if(f=f||h,!f){const e=l;if(l===e)if(t&&"object"==typeof t&&!Array.isArray(t))for(const e in t){const n=l;if("number"!=typeof t[e]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}h=e===l,f=f||h}if(f)l=p,null!==a&&(p?a.length=p:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),m=n===l}else m=!0;if(m){if(void 0!==e.maxInitialSize){let t=e.maxInitialSize;const n=l,r=l;let o=!1,s=null;const i=l,p=l;let f=!1;const u=l;if(l===u)if("number"==typeof t){if(t<0||isNaN(t)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var b=u===l;if(f=f||b,!f){const e=l;if(l===e)if(t&&"object"==typeof t&&!Array.isArray(t))for(const e in t){const n=l;if("number"!=typeof t[e]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}b=e===l,f=f||b}if(f)l=p,null!==a&&(p?a.length=p:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),m=n===l}else m=!0;if(m){if(void 0!==e.maxSize){let t=e.maxSize;const n=l,r=l;let o=!1,s=null;const i=l,p=l;let f=!1;const u=l;if(l===u)if("number"==typeof t){if(t<0||isNaN(t)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var g=u===l;if(f=f||g,!f){const e=l;if(l===e)if(t&&"object"==typeof t&&!Array.isArray(t))for(const e in t){const n=l;if("number"!=typeof t[e]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}g=e===l,f=f||g}if(f)l=p,null!==a&&(p?a.length=p:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),m=n===l}else m=!0;if(m){if(void 0!==e.minSize){let t=e.minSize;const n=l,r=l;let o=!1,s=null;const i=l,p=l;let f=!1;const u=l;if(l===u)if("number"==typeof t){if(t<0||isNaN(t)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var v=u===l;if(f=f||v,!f){const e=l;if(l===e)if(t&&"object"==typeof t&&!Array.isArray(t))for(const e in t){const n=l;if("number"!=typeof t[e]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}v=e===l,f=f||v}if(f)l=p,null!==a&&(p?a.length=p:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),m=n===l}else m=!0;if(m)if(void 0!==e.minSizeReduction){let t=e.minSizeReduction;const n=l,r=l;let o=!1,s=null;const i=l,p=l;let f=!1;const u=l;if(l===u)if("number"==typeof t){if(t<0||isNaN(t)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var P=u===l;if(f=f||P,!f){const e=l;if(l===e)if(t&&"object"==typeof t&&!Array.isArray(t))for(const e in t){const n=l;if("number"!=typeof t[e]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}P=e===l,f=f||P}if(f)l=p,null!==a&&(p?a.length=p:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),m=n===l}else m=!0}}}}}}}}p=n===l}else p=!0;if(p){if(void 0!==t.filename){let n=t.filename;const r=l,o=l;let s=!1;const i=l;if(l===i)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===a?a=[e]:a.push(e),l++}else if(n.length<1){const e={params:{}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}var D=i===l;if(s=s||D,!s){const e=l;if(!(n instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}D=e===l,s=s||D}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=o,null!==a&&(o?a.length=o:a=null),p=r===l}else p=!0;if(p){if(void 0!==t.hidePathInfo){const e=l;if("boolean"!=typeof t.hidePathInfo)return Oe.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.maxAsyncRequests){let e=t.maxAsyncRequests;const n=l;if(l===n){if("number"!=typeof e)return Oe.errors=[{params:{type:"number"}}],!1;if(e<1||isNaN(e))return Oe.errors=[{params:{comparison:">=",limit:1}}],!1}p=n===l}else p=!0;if(p){if(void 0!==t.maxAsyncSize){let e=t.maxAsyncSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var O=c===l;if(u=u||O,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}O=t===l,u=u||O}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.maxInitialRequests){let e=t.maxInitialRequests;const n=l;if(l===n){if("number"!=typeof e)return Oe.errors=[{params:{type:"number"}}],!1;if(e<1||isNaN(e))return Oe.errors=[{params:{comparison:">=",limit:1}}],!1}p=n===l}else p=!0;if(p){if(void 0!==t.maxInitialSize){let e=t.maxInitialSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var C=c===l;if(u=u||C,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}C=t===l,u=u||C}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.maxSize){let e=t.maxSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var x=c===l;if(u=u||x,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}x=t===l,u=u||x}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.minChunks){let e=t.minChunks;const n=l;if(l===n){if("number"!=typeof e)return Oe.errors=[{params:{type:"number"}}],!1;if(e<1||isNaN(e))return Oe.errors=[{params:{comparison:">=",limit:1}}],!1}p=n===l}else p=!0;if(p){if(void 0!==t.minRemainingSize){let e=t.minRemainingSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var $=c===l;if(u=u||$,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}$=t===l,u=u||$}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.minSize){let e=t.minSize;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var A=c===l;if(u=u||A,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}A=t===l,u=u||A}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.minSizeReduction){let e=t.minSizeReduction;const n=l,r=l;let o=!1,s=null;const i=l,f=l;let u=!1;const c=l;if(l===c)if("number"==typeof e){if(e<0||isNaN(e)){const e={params:{comparison:">=",limit:0}};null===a?a=[e]:a.push(e),l++}}else{const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}var k=c===l;if(u=u||k,!u){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e))for(const t in e){const n=l;if("number"!=typeof e[t]){const e={params:{type:"number"}};null===a?a=[e]:a.push(e),l++}if(n!==l)break}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}k=t===l,u=u||k}if(u)l=f,null!==a&&(f?a.length=f:a=null);else{const e={params:{}};null===a?a=[e]:a.push(e),l++}if(i===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.name){let e=t.name;const n=l,r=l;let o=!1;const s=l;if(!1!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var j=s===l;if(o=o||j,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}if(j=t===l,o=o||j,!o){const t=l;if(!(e instanceof Function)){const e={params:{}};null===a?a=[e]:a.push(e),l++}j=t===l,o=o||j}}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Oe.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p)if(void 0!==t.usedExports){const e=l;if("boolean"!=typeof t.usedExports)return Oe.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0}}}}}}}}}}}}}}}}}}}}return Oe.errors=a,0===l}function Ce(e,{instancePath:t="",parentData:r,parentDataProperty:o,rootData:s=e}={}){let i=null,a=0;if(0===a){if(!e||"object"!=typeof e||Array.isArray(e))return Ce.errors=[{params:{type:"object"}}],!1;{const r=a;for(const t in e)if(!n.call(ge.properties,t))return Ce.errors=[{params:{additionalProperty:t}}],!1;if(r===a){if(void 0!==e.avoidEntryIife){const t=a;if("boolean"!=typeof e.avoidEntryIife)return Ce.errors=[{params:{type:"boolean"}}],!1;var l=t===a}else l=!0;if(l){if(void 0!==e.checkWasmTypes){const t=a;if("boolean"!=typeof e.checkWasmTypes)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.chunkIds){let t=e.chunkIds;const n=a;if("natural"!==t&&"named"!==t&&"deterministic"!==t&&"size"!==t&&"total-size"!==t&&!1!==t)return Ce.errors=[{params:{}}],!1;l=n===a}else l=!0;if(l){if(void 0!==e.concatenateModules){const t=a;if("boolean"!=typeof e.concatenateModules)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.emitOnErrors){const t=a;if("boolean"!=typeof e.emitOnErrors)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.flagIncludedChunks){const t=a;if("boolean"!=typeof e.flagIncludedChunks)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.innerGraph){const t=a;if("boolean"!=typeof e.innerGraph)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.mangleExports){let t=e.mangleExports;const n=a,r=a;let o=!1;const s=a;if("size"!==t&&"deterministic"!==t){const e={params:{}};null===i?i=[e]:i.push(e),a++}var p=s===a;if(o=o||p,!o){const e=a;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===i?i=[e]:i.push(e),a++}p=e===a,o=o||p}if(!o){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Ce.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.mangleWasmImports){const t=a;if("boolean"!=typeof e.mangleWasmImports)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.mergeDuplicateChunks){const t=a;if("boolean"!=typeof e.mergeDuplicateChunks)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.minimize){const t=a;if("boolean"!=typeof e.minimize)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.minimizer){let t=e.minimizer;const n=a;if(a===n){if(!Array.isArray(t))return Ce.errors=[{params:{type:"array"}}],!1;{const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=a,o=a;let s=!1;const l=a;if("..."!==e){const e={params:{}};null===i?i=[e]:i.push(e),a++}var f=l===a;if(s=s||f,!s){const t=a;if(!1!==e&&0!==e&&""!==e&&null!=e){const e={params:{}};null===i?i=[e]:i.push(e),a++}if(f=t===a,s=s||f,!s){const t=a;if(a==a)if(e&&"object"==typeof e&&!Array.isArray(e)){let t;if(void 0===e.apply&&(t="apply")){const e={params:{missingProperty:t}};null===i?i=[e]:i.push(e),a++}else if(void 0!==e.apply&&!(e.apply instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"object"}};null===i?i=[e]:i.push(e),a++}if(f=t===a,s=s||f,!s){const t=a;if(!(e instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}f=t===a,s=s||f}}}if(!s){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Ce.errors=i,!1}if(a=o,null!==i&&(o?i.length=o:i=null),r!==a)break}}}l=n===a}else l=!0;if(l){if(void 0!==e.moduleIds){let t=e.moduleIds;const n=a;if("natural"!==t&&"named"!==t&&"hashed"!==t&&"deterministic"!==t&&"size"!==t&&!1!==t)return Ce.errors=[{params:{}}],!1;l=n===a}else l=!0;if(l){if(void 0!==e.noEmitOnErrors){const t=a;if("boolean"!=typeof e.noEmitOnErrors)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.nodeEnv){let t=e.nodeEnv;const n=a,r=a;let o=!1;const s=a;if(!1!==t){const e={params:{}};null===i?i=[e]:i.push(e),a++}var u=s===a;if(o=o||u,!o){const e=a;if("string"!=typeof t){const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}u=e===a,o=o||u}if(!o){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Ce.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.portableRecords){const t=a;if("boolean"!=typeof e.portableRecords)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.providedExports){const t=a;if("boolean"!=typeof e.providedExports)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.realContentHash){const t=a;if("boolean"!=typeof e.realContentHash)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.removeAvailableModules){const t=a;if("boolean"!=typeof e.removeAvailableModules)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.removeEmptyChunks){const t=a;if("boolean"!=typeof e.removeEmptyChunks)return Ce.errors=[{params:{type:"boolean"}}],!1;l=t===a}else l=!0;if(l){if(void 0!==e.runtimeChunk){let t=e.runtimeChunk;const n=a,r=a;let o=!1;const s=a;if("single"!==t&&"multiple"!==t){const e={params:{}};null===i?i=[e]:i.push(e),a++}var c=s===a;if(o=o||c,!o){const e=a;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===i?i=[e]:i.push(e),a++}if(c=e===a,o=o||c,!o){const e=a;if(a===e)if(t&&"object"==typeof t&&!Array.isArray(t)){const e=a;for(const e in t)if("name"!==e){const t={params:{additionalProperty:e}};null===i?i=[t]:i.push(t),a++;break}if(e===a&&void 0!==t.name){let e=t.name;const n=a;let r=!1;const o=a;if("string"!=typeof e){const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var y=o===a;if(r=r||y,!r){const t=a;if(!(e instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}y=t===a,r=r||y}if(r)a=n,null!==i&&(n?i.length=n:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}}}else{const e={params:{type:"object"}};null===i?i=[e]:i.push(e),a++}c=e===a,o=o||c}}if(!o){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Ce.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.sideEffects){let t=e.sideEffects;const n=a,r=a;let o=!1;const s=a;if("flag"!==t){const e={params:{}};null===i?i=[e]:i.push(e),a++}var m=s===a;if(o=o||m,!o){const e=a;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===i?i=[e]:i.push(e),a++}m=e===a,o=o||m}if(!o){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Ce.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0;if(l){if(void 0!==e.splitChunks){let n=e.splitChunks;const r=a,o=a;let p=!1;const f=a;if(!1!==n){const e={params:{}};null===i?i=[e]:i.push(e),a++}var d=f===a;if(p=p||d,!p){const r=a;Oe(n,{instancePath:t+"/splitChunks",parentData:e,parentDataProperty:"splitChunks",rootData:s})||(i=null===i?Oe.errors:i.concat(Oe.errors),a=i.length),d=r===a,p=p||d}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Ce.errors=i,!1}a=o,null!==i&&(o?i.length=o:i=null),l=r===a}else l=!0;if(l)if(void 0!==e.usedExports){let t=e.usedExports;const n=a,r=a;let o=!1;const s=a;if("global"!==t){const e={params:{}};null===i?i=[e]:i.push(e),a++}var h=s===a;if(o=o||h,!o){const e=a;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===i?i=[e]:i.push(e),a++}h=e===a,o=o||h}if(!o){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Ce.errors=i,!1}a=r,null!==i&&(r?i.length=r:i=null),l=n===a}else l=!0}}}}}}}}}}}}}}}}}}}}}}}}}return Ce.errors=i,0===a}const xe={type:"object",additionalProperties:!1,properties:{amdContainer:{oneOf:[{$ref:"#/definitions/AmdContainer"}]},assetModuleFilename:{$ref:"#/definitions/AssetModuleFilename"},asyncChunks:{type:"boolean"},auxiliaryComment:{oneOf:[{$ref:"#/definitions/AuxiliaryComment"}]},charset:{$ref:"#/definitions/Charset"},chunkFilename:{$ref:"#/definitions/ChunkFilename"},chunkFormat:{$ref:"#/definitions/ChunkFormat"},chunkLoadTimeout:{$ref:"#/definitions/ChunkLoadTimeout"},chunkLoading:{$ref:"#/definitions/ChunkLoading"},chunkLoadingGlobal:{$ref:"#/definitions/ChunkLoadingGlobal"},clean:{$ref:"#/definitions/Clean"},compareBeforeEmit:{$ref:"#/definitions/CompareBeforeEmit"},crossOriginLoading:{$ref:"#/definitions/CrossOriginLoading"},cssChunkFilename:{$ref:"#/definitions/CssChunkFilename"},cssFilename:{$ref:"#/definitions/CssFilename"},devtoolFallbackModuleFilenameTemplate:{$ref:"#/definitions/DevtoolFallbackModuleFilenameTemplate"},devtoolModuleFilenameTemplate:{$ref:"#/definitions/DevtoolModuleFilenameTemplate"},devtoolNamespace:{$ref:"#/definitions/DevtoolNamespace"},enabledChunkLoadingTypes:{$ref:"#/definitions/EnabledChunkLoadingTypes"},enabledLibraryTypes:{$ref:"#/definitions/EnabledLibraryTypes"},enabledWasmLoadingTypes:{$ref:"#/definitions/EnabledWasmLoadingTypes"},environment:{$ref:"#/definitions/Environment"},filename:{$ref:"#/definitions/Filename"},globalObject:{$ref:"#/definitions/GlobalObject"},hashDigest:{$ref:"#/definitions/HashDigest"},hashDigestLength:{$ref:"#/definitions/HashDigestLength"},hashFunction:{$ref:"#/definitions/HashFunction"},hashSalt:{$ref:"#/definitions/HashSalt"},hotUpdateChunkFilename:{$ref:"#/definitions/HotUpdateChunkFilename"},hotUpdateGlobal:{$ref:"#/definitions/HotUpdateGlobal"},hotUpdateMainFilename:{$ref:"#/definitions/HotUpdateMainFilename"},ignoreBrowserWarnings:{type:"boolean"},iife:{$ref:"#/definitions/Iife"},importFunctionName:{$ref:"#/definitions/ImportFunctionName"},importMetaName:{$ref:"#/definitions/ImportMetaName"},library:{$ref:"#/definitions/Library"},libraryExport:{oneOf:[{$ref:"#/definitions/LibraryExport"}]},libraryTarget:{oneOf:[{$ref:"#/definitions/LibraryType"}]},module:{$ref:"#/definitions/OutputModule"},path:{$ref:"#/definitions/Path"},pathinfo:{$ref:"#/definitions/Pathinfo"},publicPath:{$ref:"#/definitions/PublicPath"},scriptType:{$ref:"#/definitions/ScriptType"},sourceMapFilename:{$ref:"#/definitions/SourceMapFilename"},sourcePrefix:{$ref:"#/definitions/SourcePrefix"},strictModuleErrorHandling:{$ref:"#/definitions/StrictModuleErrorHandling"},strictModuleExceptionHandling:{$ref:"#/definitions/StrictModuleExceptionHandling"},trustedTypes:{anyOf:[{enum:[!0]},{type:"string",minLength:1},{$ref:"#/definitions/TrustedTypes"}]},umdNamedDefine:{oneOf:[{$ref:"#/definitions/UmdNamedDefine"}]},uniqueName:{$ref:"#/definitions/UniqueName"},wasmLoading:{$ref:"#/definitions/WasmLoading"},webassemblyModuleFilename:{$ref:"#/definitions/WebassemblyModuleFilename"},workerChunkLoading:{$ref:"#/definitions/ChunkLoading"},workerPublicPath:{$ref:"#/definitions/WorkerPublicPath"},workerWasmLoading:{$ref:"#/definitions/WasmLoading"}}},$e={type:"object",additionalProperties:!1,properties:{arrowFunction:{type:"boolean"},asyncFunction:{type:"boolean"},bigIntLiteral:{type:"boolean"},const:{type:"boolean"},destructuring:{type:"boolean"},document:{type:"boolean"},dynamicImport:{type:"boolean"},dynamicImportInWorker:{type:"boolean"},forOf:{type:"boolean"},globalThis:{type:"boolean"},module:{type:"boolean"},nodePrefixForCoreModules:{type:"boolean"},optionalChaining:{type:"boolean"},templateLiteral:{type:"boolean"}}};function Ae(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1,f=null;const u=a,c=a;let y=!1;const m=a;if(a===m)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}else if(t.length<1){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var d=m===a;if(y=y||d,!y){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}d=e===a,y=y||d}if(y)a=c,null!==i&&(c?i.length=c:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(u===a&&(p=!0,f=0),!p){const e={params:{passingSchemas:f}};return null===i?i=[e]:i.push(e),a++,Ae.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),Ae.errors=i,0===a}function ke(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1;const f=a;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===i?i=[e]:i.push(e),a++}var u=f===a;if(p=p||u,!p){const n=a;if(a==a)if(t&&"object"==typeof t&&!Array.isArray(t)){const n=a;for(const e in t)if("dry"!==e&&"keep"!==e){const t={params:{additionalProperty:e}};null===i?i=[t]:i.push(t),a++;break}if(n===a){if(void 0!==t.dry){const e=a;if("boolean"!=typeof t.dry){const e={params:{type:"boolean"}};null===i?i=[e]:i.push(e),a++}var c=e===a}else c=!0;if(c)if(void 0!==t.keep){let n=t.keep;const r=a,o=a;let s=!1;const l=a;if(!(n instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var y=l===a;if(s=s||y,!s){const t=a;if(a===t)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(y=t===a,s=s||y,!s){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}y=e===a,s=s||y}}if(s)a=o,null!==i&&(o?i.length=o:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}c=r===a}else c=!0}}else{const e={params:{type:"object"}};null===i?i=[e]:i.push(e),a++}u=n===a,p=p||u}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,ke.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),ke.errors=i,0===a}function je(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1,f=null;const u=a,c=a;let y=!1;const m=a;if(a===m)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}else if(t.length<1){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var d=m===a;if(y=y||d,!y){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}d=e===a,y=y||d}if(y)a=c,null!==i&&(c?i.length=c:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(u===a&&(p=!0,f=0),!p){const e={params:{passingSchemas:f}};return null===i?i=[e]:i.push(e),a++,je.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),je.errors=i,0===a}function Se(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1,f=null;const u=a,c=a;let y=!1;const m=a;if(a===m)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}else if(t.length<1){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var d=m===a;if(y=y||d,!y){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}d=e===a,y=y||d}if(y)a=c,null!==i&&(c?i.length=c:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(u===a&&(p=!0,f=0),!p){const e={params:{passingSchemas:f}};return null===i?i=[e]:i.push(e),a++,Se.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),Se.errors=i,0===a}function Fe(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!Array.isArray(e))return Fe.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=i,o=i;let l=!1;const p=i;if("jsonp"!==t&&"import-scripts"!==t&&"require"!==t&&"async-node"!==t&&"import"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var a=p===i;if(l=l||a,!l){const e=i;if("string"!=typeof t){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}a=e===i,l=l||a}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,Fe.errors=s,!1}if(i=o,null!==s&&(o?s.length=o:s=null),r!==i)break}}}return Fe.errors=s,0===i}function Ee(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!Array.isArray(e))return Ee.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=i,o=i;let l=!1;const p=i;if("var"!==t&&"module"!==t&&"assign"!==t&&"assign-properties"!==t&&"this"!==t&&"window"!==t&&"self"!==t&&"global"!==t&&"commonjs"!==t&&"commonjs2"!==t&&"commonjs-module"!==t&&"commonjs-static"!==t&&"amd"!==t&&"amd-require"!==t&&"umd"!==t&&"umd2"!==t&&"jsonp"!==t&&"system"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var a=p===i;if(l=l||a,!l){const e=i;if("string"!=typeof t){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}a=e===i,l=l||a}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,Ee.errors=s,!1}if(i=o,null!==s&&(o?s.length=o:s=null),r!==i)break}}}return Ee.errors=s,0===i}function Re(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!Array.isArray(e))return Re.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=i,o=i;let l=!1;const p=i;if("fetch"!==t&&"async-node"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var a=p===i;if(l=l||a,!l){const e=i;if("string"!=typeof t){const e={params:{type:"string"}};null===s?s=[e]:s.push(e),i++}a=e===i,l=l||a}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,Re.errors=s,!1}if(i=o,null!==s&&(o?s.length=o:s=null),r!==i)break}}}return Re.errors=s,0===i}function Le(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1,f=null;const u=a,c=a;let y=!1;const m=a;if(a===m)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}else if(t.length<1){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}var d=m===a;if(y=y||d,!y){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}d=e===a,y=y||d}if(y)a=c,null!==i&&(c?i.length=c:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(u===a&&(p=!0,f=0),!p){const e={params:{passingSchemas:f}};return null===i?i=[e]:i.push(e),a++,Le.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),Le.errors=i,0===a}function ze(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;f(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?f.errors:s.concat(f.errors),i=s.length);var c=p===i;if(l=l||c,!l){const a=i;u(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?u.errors:s.concat(u.errors),i=s.length),c=a===i,l=l||c}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,ze.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),ze.errors=s,0===i}function Me(t,{instancePath:r="",parentData:o,parentDataProperty:s,rootData:i=t}={}){let l=null,f=0;if(0===f){if(!t||"object"!=typeof t||Array.isArray(t))return Me.errors=[{params:{type:"object"}}],!1;{const o=f;for(const e in t)if(!n.call(xe.properties,e))return Me.errors=[{params:{additionalProperty:e}}],!1;if(o===f){if(void 0!==t.amdContainer){let e=t.amdContainer;const n=f,r=f;let o=!1,s=null;const i=f;if(f==f)if("string"==typeof e){if(e.length<1){const e={params:{}};null===l?l=[e]:l.push(e),f++}}else{const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}if(i===f&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null);var u=n===f}else u=!0;if(u){if(void 0!==t.assetModuleFilename){let n=t.assetModuleFilename;const r=f,o=f;let s=!1;const i=f;if(f===i)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===l?l=[e]:l.push(e),f++}}else{const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}var m=i===f;if(s=s||m,!s){const e=f;if(!(n instanceof Function)){const e={params:{}};null===l?l=[e]:l.push(e),f++}m=e===f,s=s||m}if(!s){const e={params:{}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=o,null!==l&&(o?l.length=o:l=null),u=r===f}else u=!0;if(u){if(void 0!==t.asyncChunks){const e=f;if("boolean"!=typeof t.asyncChunks)return Me.errors=[{params:{type:"boolean"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.auxiliaryComment){const e=f,n=f;let o=!1,s=null;const a=f;if(p(t.auxiliaryComment,{instancePath:r+"/auxiliaryComment",parentData:t,parentDataProperty:"auxiliaryComment",rootData:i})||(l=null===l?p.errors:l.concat(p.errors),f=l.length),a===f&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=n,null!==l&&(n?l.length=n:l=null),u=e===f}else u=!0;if(u){if(void 0!==t.charset){const e=f;if("boolean"!=typeof t.charset)return Me.errors=[{params:{type:"boolean"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.chunkFilename){const e=f;Ae(t.chunkFilename,{instancePath:r+"/chunkFilename",parentData:t,parentDataProperty:"chunkFilename",rootData:i})||(l=null===l?Ae.errors:l.concat(Ae.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.chunkFormat){let e=t.chunkFormat;const n=f,r=f;let o=!1;const s=f;if("array-push"!==e&&"commonjs"!==e&&"module"!==e&&!1!==e){const e={params:{}};null===l?l=[e]:l.push(e),f++}var d=s===f;if(o=o||d,!o){const t=f;if("string"!=typeof e){const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}d=t===f,o=o||d}if(!o){const e={params:{}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null),u=n===f}else u=!0;if(u){if(void 0!==t.chunkLoadTimeout){const e=f;if("number"!=typeof t.chunkLoadTimeout)return Me.errors=[{params:{type:"number"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.chunkLoading){const e=f;a(t.chunkLoading,{instancePath:r+"/chunkLoading",parentData:t,parentDataProperty:"chunkLoading",rootData:i})||(l=null===l?a.errors:l.concat(a.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.chunkLoadingGlobal){const e=f;if("string"!=typeof t.chunkLoadingGlobal)return Me.errors=[{params:{type:"string"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.clean){const e=f;ke(t.clean,{instancePath:r+"/clean",parentData:t,parentDataProperty:"clean",rootData:i})||(l=null===l?ke.errors:l.concat(ke.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.compareBeforeEmit){const e=f;if("boolean"!=typeof t.compareBeforeEmit)return Me.errors=[{params:{type:"boolean"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.crossOriginLoading){let e=t.crossOriginLoading;const n=f;if(!1!==e&&"anonymous"!==e&&"use-credentials"!==e)return Me.errors=[{params:{}}],!1;u=n===f}else u=!0;if(u){if(void 0!==t.cssChunkFilename){const e=f;je(t.cssChunkFilename,{instancePath:r+"/cssChunkFilename",parentData:t,parentDataProperty:"cssChunkFilename",rootData:i})||(l=null===l?je.errors:l.concat(je.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.cssFilename){const e=f;Se(t.cssFilename,{instancePath:r+"/cssFilename",parentData:t,parentDataProperty:"cssFilename",rootData:i})||(l=null===l?Se.errors:l.concat(Se.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.devtoolFallbackModuleFilenameTemplate){let e=t.devtoolFallbackModuleFilenameTemplate;const n=f,r=f;let o=!1;const s=f;if("string"!=typeof e){const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}var h=s===f;if(o=o||h,!o){const t=f;if(!(e instanceof Function)){const e={params:{}};null===l?l=[e]:l.push(e),f++}h=t===f,o=o||h}if(!o){const e={params:{}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null),u=n===f}else u=!0;if(u){if(void 0!==t.devtoolModuleFilenameTemplate){let e=t.devtoolModuleFilenameTemplate;const n=f,r=f;let o=!1;const s=f;if("string"!=typeof e){const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}var b=s===f;if(o=o||b,!o){const t=f;if(!(e instanceof Function)){const e={params:{}};null===l?l=[e]:l.push(e),f++}b=t===f,o=o||b}if(!o){const e={params:{}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null),u=n===f}else u=!0;if(u){if(void 0!==t.devtoolNamespace){const e=f;if("string"!=typeof t.devtoolNamespace)return Me.errors=[{params:{type:"string"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.enabledChunkLoadingTypes){const e=f;Fe(t.enabledChunkLoadingTypes,{instancePath:r+"/enabledChunkLoadingTypes",parentData:t,parentDataProperty:"enabledChunkLoadingTypes",rootData:i})||(l=null===l?Fe.errors:l.concat(Fe.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.enabledLibraryTypes){const e=f;Ee(t.enabledLibraryTypes,{instancePath:r+"/enabledLibraryTypes",parentData:t,parentDataProperty:"enabledLibraryTypes",rootData:i})||(l=null===l?Ee.errors:l.concat(Ee.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.enabledWasmLoadingTypes){const e=f;Re(t.enabledWasmLoadingTypes,{instancePath:r+"/enabledWasmLoadingTypes",parentData:t,parentDataProperty:"enabledWasmLoadingTypes",rootData:i})||(l=null===l?Re.errors:l.concat(Re.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.environment){let e=t.environment;const r=f;if(f==f){if(!e||"object"!=typeof e||Array.isArray(e))return Me.errors=[{params:{type:"object"}}],!1;{const t=f;for(const t in e)if(!n.call($e.properties,t))return Me.errors=[{params:{additionalProperty:t}}],!1;if(t===f){if(void 0!==e.arrowFunction){const t=f;if("boolean"!=typeof e.arrowFunction)return Me.errors=[{params:{type:"boolean"}}],!1;var g=t===f}else g=!0;if(g){if(void 0!==e.asyncFunction){const t=f;if("boolean"!=typeof e.asyncFunction)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.bigIntLiteral){const t=f;if("boolean"!=typeof e.bigIntLiteral)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.const){const t=f;if("boolean"!=typeof e.const)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.destructuring){const t=f;if("boolean"!=typeof e.destructuring)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.document){const t=f;if("boolean"!=typeof e.document)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.dynamicImport){const t=f;if("boolean"!=typeof e.dynamicImport)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.dynamicImportInWorker){const t=f;if("boolean"!=typeof e.dynamicImportInWorker)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.forOf){const t=f;if("boolean"!=typeof e.forOf)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.globalThis){const t=f;if("boolean"!=typeof e.globalThis)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.module){const t=f;if("boolean"!=typeof e.module)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.nodePrefixForCoreModules){const t=f;if("boolean"!=typeof e.nodePrefixForCoreModules)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g){if(void 0!==e.optionalChaining){const t=f;if("boolean"!=typeof e.optionalChaining)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0;if(g)if(void 0!==e.templateLiteral){const t=f;if("boolean"!=typeof e.templateLiteral)return Me.errors=[{params:{type:"boolean"}}],!1;g=t===f}else g=!0}}}}}}}}}}}}}}}u=r===f}else u=!0;if(u){if(void 0!==t.filename){const e=f;Le(t.filename,{instancePath:r+"/filename",parentData:t,parentDataProperty:"filename",rootData:i})||(l=null===l?Le.errors:l.concat(Le.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.globalObject){let e=t.globalObject;const n=f;if(f==f){if("string"!=typeof e)return Me.errors=[{params:{type:"string"}}],!1;if(e.length<1)return Me.errors=[{params:{}}],!1}u=n===f}else u=!0;if(u){if(void 0!==t.hashDigest){const e=f;if("string"!=typeof t.hashDigest)return Me.errors=[{params:{type:"string"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.hashDigestLength){let e=t.hashDigestLength;const n=f;if(f==f){if("number"!=typeof e)return Me.errors=[{params:{type:"number"}}],!1;if(e<1||isNaN(e))return Me.errors=[{params:{comparison:">=",limit:1}}],!1}u=n===f}else u=!0;if(u){if(void 0!==t.hashFunction){let e=t.hashFunction;const n=f,r=f;let o=!1;const s=f;if(f===s)if("string"==typeof e){if(e.length<1){const e={params:{}};null===l?l=[e]:l.push(e),f++}}else{const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}var v=s===f;if(o=o||v,!o){const t=f;if(!(e instanceof Function)){const e={params:{}};null===l?l=[e]:l.push(e),f++}v=t===f,o=o||v}if(!o){const e={params:{}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null),u=n===f}else u=!0;if(u){if(void 0!==t.hashSalt){let e=t.hashSalt;const n=f;if(f==f){if("string"!=typeof e)return Me.errors=[{params:{type:"string"}}],!1;if(e.length<1)return Me.errors=[{params:{}}],!1}u=n===f}else u=!0;if(u){if(void 0!==t.hotUpdateChunkFilename){let n=t.hotUpdateChunkFilename;const r=f;if(f==f){if("string"!=typeof n)return Me.errors=[{params:{type:"string"}}],!1;if(n.includes("!")||!1!==e.test(n))return Me.errors=[{params:{}}],!1}u=r===f}else u=!0;if(u){if(void 0!==t.hotUpdateGlobal){const e=f;if("string"!=typeof t.hotUpdateGlobal)return Me.errors=[{params:{type:"string"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.hotUpdateMainFilename){let n=t.hotUpdateMainFilename;const r=f;if(f==f){if("string"!=typeof n)return Me.errors=[{params:{type:"string"}}],!1;if(n.includes("!")||!1!==e.test(n))return Me.errors=[{params:{}}],!1}u=r===f}else u=!0;if(u){if(void 0!==t.ignoreBrowserWarnings){const e=f;if("boolean"!=typeof t.ignoreBrowserWarnings)return Me.errors=[{params:{type:"boolean"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.iife){const e=f;if("boolean"!=typeof t.iife)return Me.errors=[{params:{type:"boolean"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.importFunctionName){const e=f;if("string"!=typeof t.importFunctionName)return Me.errors=[{params:{type:"string"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.importMetaName){const e=f;if("string"!=typeof t.importMetaName)return Me.errors=[{params:{type:"string"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.library){const e=f;ze(t.library,{instancePath:r+"/library",parentData:t,parentDataProperty:"library",rootData:i})||(l=null===l?ze.errors:l.concat(ze.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.libraryExport){let e=t.libraryExport;const n=f,r=f;let o=!1,s=null;const i=f,a=f;let p=!1;const c=f;if(f===c)if(Array.isArray(e)){const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=f;if(f===r)if("string"==typeof t){if(t.length<1){const e={params:{}};null===l?l=[e]:l.push(e),f++}}else{const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}if(r!==f)break}}else{const e={params:{type:"array"}};null===l?l=[e]:l.push(e),f++}var P=c===f;if(p=p||P,!p){const t=f;if(f===t)if("string"==typeof e){if(e.length<1){const e={params:{}};null===l?l=[e]:l.push(e),f++}}else{const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}P=t===f,p=p||P}if(p)f=a,null!==l&&(a?l.length=a:l=null);else{const e={params:{}};null===l?l=[e]:l.push(e),f++}if(i===f&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null),u=n===f}else u=!0;if(u){if(void 0!==t.libraryTarget){let e=t.libraryTarget;const n=f,r=f;let o=!1,s=null;const i=f,a=f;let p=!1;const c=f;if("var"!==e&&"module"!==e&&"assign"!==e&&"assign-properties"!==e&&"this"!==e&&"window"!==e&&"self"!==e&&"global"!==e&&"commonjs"!==e&&"commonjs2"!==e&&"commonjs-module"!==e&&"commonjs-static"!==e&&"amd"!==e&&"amd-require"!==e&&"umd"!==e&&"umd2"!==e&&"jsonp"!==e&&"system"!==e){const e={params:{}};null===l?l=[e]:l.push(e),f++}var D=c===f;if(p=p||D,!p){const t=f;if("string"!=typeof e){const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}D=t===f,p=p||D}if(p)f=a,null!==l&&(a?l.length=a:l=null);else{const e={params:{}};null===l?l=[e]:l.push(e),f++}if(i===f&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null),u=n===f}else u=!0;if(u){if(void 0!==t.module){const e=f;if("boolean"!=typeof t.module)return Me.errors=[{params:{type:"boolean"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.path){let n=t.path;const r=f;if(f==f){if("string"!=typeof n)return Me.errors=[{params:{type:"string"}}],!1;if(n.includes("!")||!0!==e.test(n))return Me.errors=[{params:{}}],!1}u=r===f}else u=!0;if(u){if(void 0!==t.pathinfo){let e=t.pathinfo;const n=f,r=f;let o=!1;const s=f;if("verbose"!==e){const e={params:{}};null===l?l=[e]:l.push(e),f++}var O=s===f;if(o=o||O,!o){const t=f;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===l?l=[e]:l.push(e),f++}O=t===f,o=o||O}if(!o){const e={params:{}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null),u=n===f}else u=!0;if(u){if(void 0!==t.publicPath){const e=f;c(t.publicPath,{instancePath:r+"/publicPath",parentData:t,parentDataProperty:"publicPath",rootData:i})||(l=null===l?c.errors:l.concat(c.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.scriptType){let e=t.scriptType;const n=f;if(!1!==e&&"text/javascript"!==e&&"module"!==e)return Me.errors=[{params:{}}],!1;u=n===f}else u=!0;if(u){if(void 0!==t.sourceMapFilename){let n=t.sourceMapFilename;const r=f;if(f==f){if("string"!=typeof n)return Me.errors=[{params:{type:"string"}}],!1;if(n.includes("!")||!1!==e.test(n))return Me.errors=[{params:{}}],!1}u=r===f}else u=!0;if(u){if(void 0!==t.sourcePrefix){const e=f;if("string"!=typeof t.sourcePrefix)return Me.errors=[{params:{type:"string"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.strictModuleErrorHandling){const e=f;if("boolean"!=typeof t.strictModuleErrorHandling)return Me.errors=[{params:{type:"boolean"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.strictModuleExceptionHandling){const e=f;if("boolean"!=typeof t.strictModuleExceptionHandling)return Me.errors=[{params:{type:"boolean"}}],!1;u=e===f}else u=!0;if(u){if(void 0!==t.trustedTypes){let e=t.trustedTypes;const n=f,r=f;let o=!1;const s=f;if(!0!==e){const e={params:{}};null===l?l=[e]:l.push(e),f++}var C=s===f;if(o=o||C,!o){const t=f;if(f===t)if("string"==typeof e){if(e.length<1){const e={params:{}};null===l?l=[e]:l.push(e),f++}}else{const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}if(C=t===f,o=o||C,!o){const t=f;if(f==f)if(e&&"object"==typeof e&&!Array.isArray(e)){const t=f;for(const t in e)if("onPolicyCreationFailure"!==t&&"policyName"!==t){const e={params:{additionalProperty:t}};null===l?l=[e]:l.push(e),f++;break}if(t===f){if(void 0!==e.onPolicyCreationFailure){let t=e.onPolicyCreationFailure;const n=f;if("continue"!==t&&"stop"!==t){const e={params:{}};null===l?l=[e]:l.push(e),f++}var x=n===f}else x=!0;if(x)if(void 0!==e.policyName){let t=e.policyName;const n=f;if(f===n)if("string"==typeof t){if(t.length<1){const e={params:{}};null===l?l=[e]:l.push(e),f++}}else{const e={params:{type:"string"}};null===l?l=[e]:l.push(e),f++}x=n===f}else x=!0}}else{const e={params:{type:"object"}};null===l?l=[e]:l.push(e),f++}C=t===f,o=o||C}}if(!o){const e={params:{}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=r,null!==l&&(r?l.length=r:l=null),u=n===f}else u=!0;if(u){if(void 0!==t.umdNamedDefine){const e=f,n=f;let r=!1,o=null;const s=f;if("boolean"!=typeof t.umdNamedDefine){const e={params:{type:"boolean"}};null===l?l=[e]:l.push(e),f++}if(s===f&&(r=!0,o=0),!r){const e={params:{passingSchemas:o}};return null===l?l=[e]:l.push(e),f++,Me.errors=l,!1}f=n,null!==l&&(n?l.length=n:l=null),u=e===f}else u=!0;if(u){if(void 0!==t.uniqueName){let e=t.uniqueName;const n=f;if(f==f){if("string"!=typeof e)return Me.errors=[{params:{type:"string"}}],!1;if(e.length<1)return Me.errors=[{params:{}}],!1}u=n===f}else u=!0;if(u){if(void 0!==t.wasmLoading){const e=f;y(t.wasmLoading,{instancePath:r+"/wasmLoading",parentData:t,parentDataProperty:"wasmLoading",rootData:i})||(l=null===l?y.errors:l.concat(y.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.webassemblyModuleFilename){let n=t.webassemblyModuleFilename;const r=f;if(f==f){if("string"!=typeof n)return Me.errors=[{params:{type:"string"}}],!1;if(n.includes("!")||!1!==e.test(n))return Me.errors=[{params:{}}],!1}u=r===f}else u=!0;if(u){if(void 0!==t.workerChunkLoading){const e=f;a(t.workerChunkLoading,{instancePath:r+"/workerChunkLoading",parentData:t,parentDataProperty:"workerChunkLoading",rootData:i})||(l=null===l?a.errors:l.concat(a.errors),f=l.length),u=e===f}else u=!0;if(u){if(void 0!==t.workerPublicPath){const e=f;if("string"!=typeof t.workerPublicPath)return Me.errors=[{params:{type:"string"}}],!1;u=e===f}else u=!0;if(u)if(void 0!==t.workerWasmLoading){const e=f;y(t.workerWasmLoading,{instancePath:r+"/workerWasmLoading",parentData:t,parentDataProperty:"workerWasmLoading",rootData:i})||(l=null===l?y.errors:l.concat(y.errors),f=l.length),u=e===f}else u=!0}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}return Me.errors=l,0===f}function we(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if(!1!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const t=i;if(i==i)if(e&&"object"==typeof e&&!Array.isArray(e)){const t=i;for(const t in e)if("assetFilter"!==t&&"hints"!==t&&"maxAssetSize"!==t&&"maxEntrypointSize"!==t){const e={params:{additionalProperty:t}};null===s?s=[e]:s.push(e),i++;break}if(t===i){if(void 0!==e.assetFilter){const t=i;if(!(e.assetFilter instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}var u=t===i}else u=!0;if(u){if(void 0!==e.hints){let t=e.hints;const n=i;if(!1!==t&&"warning"!==t&&"error"!==t){const e={params:{}};null===s?s=[e]:s.push(e),i++}u=n===i}else u=!0;if(u){if(void 0!==e.maxAssetSize){const t=i;if("number"!=typeof e.maxAssetSize){const e={params:{type:"number"}};null===s?s=[e]:s.push(e),i++}u=t===i}else u=!0;if(u)if(void 0!==e.maxEntrypointSize){const t=i;if("number"!=typeof e.maxEntrypointSize){const e={params:{type:"number"}};null===s?s=[e]:s.push(e),i++}u=t===i}else u=!0}}}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}f=t===i,l=l||f}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,we.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),we.errors=s,0===i}function Ie(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;if(0===i){if(!Array.isArray(e))return Ie.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=i,o=i;let l=!1;const p=i;if(!1!==t&&0!==t&&""!==t&&null!=t){const e={params:{}};null===s?s=[e]:s.push(e),i++}var a=p===i;if(l=l||a,!l){const e=i;if(i==i)if(t&&"object"==typeof t&&!Array.isArray(t)){let e;if(void 0===t.apply&&(e="apply")){const t={params:{missingProperty:e}};null===s?s=[t]:s.push(t),i++}else if(void 0!==t.apply&&!(t.apply instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}}else{const e={params:{type:"object"}};null===s?s=[e]:s.push(e),i++}if(a=e===i,l=l||a,!l){const e=i;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),i++}a=e===i,l=l||a}}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,Ie.errors=s,!1}if(i=o,null!==s&&(o?s.length=o:s=null),r!==i)break}}}return Ie.errors=s,0===i}function Te(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1,p=null;const f=i;if(J(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?J.errors:s.concat(J.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,Te.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),Te.errors=s,0===i}function Ne(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1,p=null;const f=i;if(J(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?J.errors:s.concat(J.errors),i=s.length),f===i&&(l=!0,p=0),!l){const e={params:{passingSchemas:p}};return null===s?s=[e]:s.push(e),i++,Ne.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),Ne.errors=s,0===i}const Ge={type:"object",additionalProperties:!1,properties:{all:{type:"boolean"},assets:{type:"boolean"},assetsSort:{anyOf:[{enum:[!1]},{type:"string"}]},assetsSpace:{type:"number"},builtAt:{type:"boolean"},cached:{type:"boolean"},cachedAssets:{type:"boolean"},cachedModules:{type:"boolean"},children:{type:"boolean"},chunkGroupAuxiliary:{type:"boolean"},chunkGroupChildren:{type:"boolean"},chunkGroupMaxAssets:{type:"number"},chunkGroups:{type:"boolean"},chunkModules:{type:"boolean"},chunkModulesSpace:{type:"number"},chunkOrigins:{type:"boolean"},chunkRelations:{type:"boolean"},chunks:{type:"boolean"},chunksSort:{anyOf:[{enum:[!1]},{type:"string"}]},colors:{anyOf:[{type:"boolean"},{type:"object",additionalProperties:!1,properties:{bold:{type:"string"},cyan:{type:"string"},green:{type:"string"},magenta:{type:"string"},red:{type:"string"},yellow:{type:"string"}}}]},context:{type:"string",absolutePath:!0},dependentModules:{type:"boolean"},depth:{type:"boolean"},entrypoints:{anyOf:[{enum:["auto"]},{type:"boolean"}]},env:{type:"boolean"},errorCause:{anyOf:[{enum:["auto"]},{type:"boolean"}]},errorDetails:{anyOf:[{enum:["auto"]},{type:"boolean"}]},errorErrors:{anyOf:[{enum:["auto"]},{type:"boolean"}]},errorStack:{type:"boolean"},errors:{type:"boolean"},errorsCount:{type:"boolean"},errorsSpace:{type:"number"},exclude:{anyOf:[{type:"boolean"},{$ref:"#/definitions/ModuleFilterTypes"}]},excludeAssets:{oneOf:[{$ref:"#/definitions/AssetFilterTypes"}]},excludeModules:{anyOf:[{type:"boolean"},{$ref:"#/definitions/ModuleFilterTypes"}]},groupAssetsByChunk:{type:"boolean"},groupAssetsByEmitStatus:{type:"boolean"},groupAssetsByExtension:{type:"boolean"},groupAssetsByInfo:{type:"boolean"},groupAssetsByPath:{type:"boolean"},groupModulesByAttributes:{type:"boolean"},groupModulesByCacheStatus:{type:"boolean"},groupModulesByExtension:{type:"boolean"},groupModulesByLayer:{type:"boolean"},groupModulesByPath:{type:"boolean"},groupModulesByType:{type:"boolean"},groupReasonsByOrigin:{type:"boolean"},hash:{type:"boolean"},ids:{type:"boolean"},logging:{anyOf:[{enum:["none","error","warn","info","log","verbose"]},{type:"boolean"}]},loggingDebug:{anyOf:[{type:"boolean"},{$ref:"#/definitions/FilterTypes"}]},loggingTrace:{type:"boolean"},moduleAssets:{type:"boolean"},moduleTrace:{type:"boolean"},modules:{type:"boolean"},modulesSort:{anyOf:[{enum:[!1]},{type:"string"}]},modulesSpace:{type:"number"},nestedModules:{type:"boolean"},nestedModulesSpace:{type:"number"},optimizationBailout:{type:"boolean"},orphanModules:{type:"boolean"},outputPath:{type:"boolean"},performance:{type:"boolean"},preset:{anyOf:[{type:"boolean"},{type:"string"}]},providedExports:{type:"boolean"},publicPath:{type:"boolean"},reasons:{type:"boolean"},reasonsSpace:{type:"number"},relatedAssets:{type:"boolean"},runtime:{type:"boolean"},runtimeModules:{type:"boolean"},source:{type:"boolean"},timings:{type:"boolean"},usedExports:{type:"boolean"},version:{type:"boolean"},warnings:{type:"boolean"},warningsCount:{type:"boolean"},warningsFilter:{oneOf:[{$ref:"#/definitions/WarningFilterTypes"}]},warningsSpace:{type:"number"}}};function Ue(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1;const f=a;if(a===f)if(Array.isArray(t)){const n=t.length;for(let r=0;r<n;r++){let n=t[r];const o=a,s=a;let l=!1,p=null;const f=a,c=a;let y=!1;const m=a;if(!(n instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var u=m===a;if(y=y||u,!y){const t=a;if(a===t)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(u=t===a,y=y||u,!y){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}u=e===a,y=y||u}}if(y)a=c,null!==i&&(c?i.length=c:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(f===a&&(l=!0,p=0),l)a=s,null!==i&&(s?i.length=s:i=null);else{const e={params:{passingSchemas:p}};null===i?i=[e]:i.push(e),a++}if(o!==a)break}}else{const e={params:{type:"array"}};null===i?i=[e]:i.push(e),a++}var c=f===a;if(p=p||c,!p){const n=a,r=a;let o=!1;const s=a;if(!(t instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var y=s===a;if(o=o||y,!o){const n=a;if(a===n)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(y=n===a,o=o||y,!o){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}y=e===a,o=o||y}}if(o)a=r,null!==i&&(r?i.length=r:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}c=n===a,p=p||c}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,Ue.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),Ue.errors=i,0===a}function We(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1;const f=a;if(a===f)if(Array.isArray(t)){const n=t.length;for(let r=0;r<n;r++){let n=t[r];const o=a,s=a;let l=!1,p=null;const f=a,c=a;let y=!1;const m=a;if(!(n instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var u=m===a;if(y=y||u,!y){const t=a;if(a===t)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(u=t===a,y=y||u,!y){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}u=e===a,y=y||u}}if(y)a=c,null!==i&&(c?i.length=c:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(f===a&&(l=!0,p=0),l)a=s,null!==i&&(s?i.length=s:i=null);else{const e={params:{passingSchemas:p}};null===i?i=[e]:i.push(e),a++}if(o!==a)break}}else{const e={params:{type:"array"}};null===i?i=[e]:i.push(e),a++}var c=f===a;if(p=p||c,!p){const n=a,r=a;let o=!1;const s=a;if(!(t instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var y=s===a;if(o=o||y,!o){const n=a;if(a===n)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(y=n===a,o=o||y,!o){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}y=e===a,o=o||y}}if(o)a=r,null!==i&&(r?i.length=r:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}c=n===a,p=p||c}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,We.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),We.errors=i,0===a}function qe(t,{instancePath:n="",parentData:r,parentDataProperty:o,rootData:s=t}={}){let i=null,a=0;const l=a;let p=!1;const f=a;if(a===f)if(Array.isArray(t)){const n=t.length;for(let r=0;r<n;r++){let n=t[r];const o=a,s=a;let l=!1,p=null;const f=a,c=a;let y=!1;const m=a;if(!(n instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var u=m===a;if(y=y||u,!y){const t=a;if(a===t)if("string"==typeof n){if(n.includes("!")||!1!==e.test(n)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(u=t===a,y=y||u,!y){const e=a;if(!(n instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}u=e===a,y=y||u}}if(y)a=c,null!==i&&(c?i.length=c:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}if(f===a&&(l=!0,p=0),l)a=s,null!==i&&(s?i.length=s:i=null);else{const e={params:{passingSchemas:p}};null===i?i=[e]:i.push(e),a++}if(o!==a)break}}else{const e={params:{type:"array"}};null===i?i=[e]:i.push(e),a++}var c=f===a;if(p=p||c,!p){const n=a,r=a;let o=!1;const s=a;if(!(t instanceof RegExp)){const e={params:{}};null===i?i=[e]:i.push(e),a++}var y=s===a;if(o=o||y,!o){const n=a;if(a===n)if("string"==typeof t){if(t.includes("!")||!1!==e.test(t)){const e={params:{}};null===i?i=[e]:i.push(e),a++}}else{const e={params:{type:"string"}};null===i?i=[e]:i.push(e),a++}if(y=n===a,o=o||y,!o){const e=a;if(!(t instanceof Function)){const e={params:{}};null===i?i=[e]:i.push(e),a++}y=e===a,o=o||y}}if(o)a=r,null!==i&&(r?i.length=r:i=null);else{const e={params:{}};null===i?i=[e]:i.push(e),a++}c=n===a,p=p||c}if(!p){const e={params:{}};return null===i?i=[e]:i.push(e),a++,qe.errors=i,!1}return a=l,null!==i&&(l?i.length=l:i=null),qe.errors=i,0===a}function Be(t,{instancePath:r="",parentData:o,parentDataProperty:s,rootData:i=t}={}){let a=null,l=0;if(0===l){if(!t||"object"!=typeof t||Array.isArray(t))return Be.errors=[{params:{type:"object"}}],!1;{const o=l;for(const e in t)if(!n.call(Ge.properties,e))return Be.errors=[{params:{additionalProperty:e}}],!1;if(o===l){if(void 0!==t.all){const e=l;if("boolean"!=typeof t.all)return Be.errors=[{params:{type:"boolean"}}],!1;var p=e===l}else p=!0;if(p){if(void 0!==t.assets){const e=l;if("boolean"!=typeof t.assets)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.assetsSort){let e=t.assetsSort;const n=l,r=l;let o=!1;const s=l;if(!1!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var f=s===l;if(o=o||f,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}f=t===l,o=o||f}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.assetsSpace){const e=l;if("number"!=typeof t.assetsSpace)return Be.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.builtAt){const e=l;if("boolean"!=typeof t.builtAt)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.cached){const e=l;if("boolean"!=typeof t.cached)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.cachedAssets){const e=l;if("boolean"!=typeof t.cachedAssets)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.cachedModules){const e=l;if("boolean"!=typeof t.cachedModules)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.children){const e=l;if("boolean"!=typeof t.children)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunkGroupAuxiliary){const e=l;if("boolean"!=typeof t.chunkGroupAuxiliary)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunkGroupChildren){const e=l;if("boolean"!=typeof t.chunkGroupChildren)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunkGroupMaxAssets){const e=l;if("number"!=typeof t.chunkGroupMaxAssets)return Be.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunkGroups){const e=l;if("boolean"!=typeof t.chunkGroups)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunkModules){const e=l;if("boolean"!=typeof t.chunkModules)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunkModulesSpace){const e=l;if("number"!=typeof t.chunkModulesSpace)return Be.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunkOrigins){const e=l;if("boolean"!=typeof t.chunkOrigins)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunkRelations){const e=l;if("boolean"!=typeof t.chunkRelations)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunks){const e=l;if("boolean"!=typeof t.chunks)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.chunksSort){let e=t.chunksSort;const n=l,r=l;let o=!1;const s=l;if(!1!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var u=s===l;if(o=o||u,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}u=t===l,o=o||u}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.colors){let e=t.colors;const n=l,r=l;let o=!1;const s=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}var c=s===l;if(o=o||c,!o){const t=l;if(l===t)if(e&&"object"==typeof e&&!Array.isArray(e)){const t=l;for(const t in e)if("bold"!==t&&"cyan"!==t&&"green"!==t&&"magenta"!==t&&"red"!==t&&"yellow"!==t){const e={params:{additionalProperty:t}};null===a?a=[e]:a.push(e),l++;break}if(t===l){if(void 0!==e.bold){const t=l;if("string"!=typeof e.bold){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}var y=t===l}else y=!0;if(y){if(void 0!==e.cyan){const t=l;if("string"!=typeof e.cyan){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}y=t===l}else y=!0;if(y){if(void 0!==e.green){const t=l;if("string"!=typeof e.green){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}y=t===l}else y=!0;if(y){if(void 0!==e.magenta){const t=l;if("string"!=typeof e.magenta){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}y=t===l}else y=!0;if(y){if(void 0!==e.red){const t=l;if("string"!=typeof e.red){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}y=t===l}else y=!0;if(y)if(void 0!==e.yellow){const t=l;if("string"!=typeof e.yellow){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}y=t===l}else y=!0}}}}}}else{const e={params:{type:"object"}};null===a?a=[e]:a.push(e),l++}c=t===l,o=o||c}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.context){let n=t.context;const r=l;if(l===r){if("string"!=typeof n)return Be.errors=[{params:{type:"string"}}],!1;if(n.includes("!")||!0!==e.test(n))return Be.errors=[{params:{}}],!1}p=r===l}else p=!0;if(p){if(void 0!==t.dependentModules){const e=l;if("boolean"!=typeof t.dependentModules)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.depth){const e=l;if("boolean"!=typeof t.depth)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.entrypoints){let e=t.entrypoints;const n=l,r=l;let o=!1;const s=l;if("auto"!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var m=s===l;if(o=o||m,!o){const t=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}m=t===l,o=o||m}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.env){const e=l;if("boolean"!=typeof t.env)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.errorCause){let e=t.errorCause;const n=l,r=l;let o=!1;const s=l;if("auto"!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var d=s===l;if(o=o||d,!o){const t=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}d=t===l,o=o||d}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.errorDetails){let e=t.errorDetails;const n=l,r=l;let o=!1;const s=l;if("auto"!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var h=s===l;if(o=o||h,!o){const t=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}h=t===l,o=o||h}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.errorErrors){let e=t.errorErrors;const n=l,r=l;let o=!1;const s=l;if("auto"!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var b=s===l;if(o=o||b,!o){const t=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}b=t===l,o=o||b}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.errorStack){const e=l;if("boolean"!=typeof t.errorStack)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.errors){const e=l;if("boolean"!=typeof t.errors)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.errorsCount){const e=l;if("boolean"!=typeof t.errorsCount)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.errorsSpace){const e=l;if("number"!=typeof t.errorsSpace)return Be.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.exclude){let e=t.exclude;const n=l,o=l;let s=!1;const f=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}var g=f===l;if(s=s||g,!s){const n=l;Ue(e,{instancePath:r+"/exclude",parentData:t,parentDataProperty:"exclude",rootData:i})||(a=null===a?Ue.errors:a.concat(Ue.errors),l=a.length),g=n===l,s=s||g}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=o,null!==a&&(o?a.length=o:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.excludeAssets){const e=l,n=l;let o=!1,s=null;const f=l;if(We(t.excludeAssets,{instancePath:r+"/excludeAssets",parentData:t,parentDataProperty:"excludeAssets",rootData:i})||(a=null===a?We.errors:a.concat(We.errors),l=a.length),f===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=n,null!==a&&(n?a.length=n:a=null),p=e===l}else p=!0;if(p){if(void 0!==t.excludeModules){let e=t.excludeModules;const n=l,o=l;let s=!1;const f=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}var v=f===l;if(s=s||v,!s){const n=l;Ue(e,{instancePath:r+"/excludeModules",parentData:t,parentDataProperty:"excludeModules",rootData:i})||(a=null===a?Ue.errors:a.concat(Ue.errors),l=a.length),v=n===l,s=s||v}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=o,null!==a&&(o?a.length=o:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.groupAssetsByChunk){const e=l;if("boolean"!=typeof t.groupAssetsByChunk)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupAssetsByEmitStatus){const e=l;if("boolean"!=typeof t.groupAssetsByEmitStatus)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupAssetsByExtension){const e=l;if("boolean"!=typeof t.groupAssetsByExtension)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupAssetsByInfo){const e=l;if("boolean"!=typeof t.groupAssetsByInfo)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupAssetsByPath){const e=l;if("boolean"!=typeof t.groupAssetsByPath)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupModulesByAttributes){const e=l;if("boolean"!=typeof t.groupModulesByAttributes)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupModulesByCacheStatus){const e=l;if("boolean"!=typeof t.groupModulesByCacheStatus)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupModulesByExtension){const e=l;if("boolean"!=typeof t.groupModulesByExtension)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupModulesByLayer){const e=l;if("boolean"!=typeof t.groupModulesByLayer)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupModulesByPath){const e=l;if("boolean"!=typeof t.groupModulesByPath)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupModulesByType){const e=l;if("boolean"!=typeof t.groupModulesByType)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.groupReasonsByOrigin){const e=l;if("boolean"!=typeof t.groupReasonsByOrigin)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.hash){const e=l;if("boolean"!=typeof t.hash)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.ids){const e=l;if("boolean"!=typeof t.ids)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.logging){let e=t.logging;const n=l,r=l;let o=!1;const s=l;if("none"!==e&&"error"!==e&&"warn"!==e&&"info"!==e&&"log"!==e&&"verbose"!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var P=s===l;if(o=o||P,!o){const t=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}P=t===l,o=o||P}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.loggingDebug){let e=t.loggingDebug;const n=l,o=l;let s=!1;const f=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}var D=f===l;if(s=s||D,!s){const n=l;F(e,{instancePath:r+"/loggingDebug",parentData:t,parentDataProperty:"loggingDebug",rootData:i})||(a=null===a?F.errors:a.concat(F.errors),l=a.length),D=n===l,s=s||D}if(!s){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=o,null!==a&&(o?a.length=o:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.loggingTrace){const e=l;if("boolean"!=typeof t.loggingTrace)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.moduleAssets){const e=l;if("boolean"!=typeof t.moduleAssets)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.moduleTrace){const e=l;if("boolean"!=typeof t.moduleTrace)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.modules){const e=l;if("boolean"!=typeof t.modules)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.modulesSort){let e=t.modulesSort;const n=l,r=l;let o=!1;const s=l;if(!1!==e){const e={params:{}};null===a?a=[e]:a.push(e),l++}var O=s===l;if(o=o||O,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}O=t===l,o=o||O}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.modulesSpace){const e=l;if("number"!=typeof t.modulesSpace)return Be.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.nestedModules){const e=l;if("boolean"!=typeof t.nestedModules)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.nestedModulesSpace){const e=l;if("number"!=typeof t.nestedModulesSpace)return Be.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.optimizationBailout){const e=l;if("boolean"!=typeof t.optimizationBailout)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.orphanModules){const e=l;if("boolean"!=typeof t.orphanModules)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.outputPath){const e=l;if("boolean"!=typeof t.outputPath)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.performance){const e=l;if("boolean"!=typeof t.performance)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.preset){let e=t.preset;const n=l,r=l;let o=!1;const s=l;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===a?a=[e]:a.push(e),l++}var C=s===l;if(o=o||C,!o){const t=l;if("string"!=typeof e){const e={params:{type:"string"}};null===a?a=[e]:a.push(e),l++}C=t===l,o=o||C}if(!o){const e={params:{}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=r,null!==a&&(r?a.length=r:a=null),p=n===l}else p=!0;if(p){if(void 0!==t.providedExports){const e=l;if("boolean"!=typeof t.providedExports)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.publicPath){const e=l;if("boolean"!=typeof t.publicPath)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.reasons){const e=l;if("boolean"!=typeof t.reasons)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.reasonsSpace){const e=l;if("number"!=typeof t.reasonsSpace)return Be.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.relatedAssets){const e=l;if("boolean"!=typeof t.relatedAssets)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.runtime){const e=l;if("boolean"!=typeof t.runtime)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.runtimeModules){const e=l;if("boolean"!=typeof t.runtimeModules)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.source){const e=l;if("boolean"!=typeof t.source)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.timings){const e=l;if("boolean"!=typeof t.timings)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.usedExports){const e=l;if("boolean"!=typeof t.usedExports)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.version){const e=l;if("boolean"!=typeof t.version)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.warnings){const e=l;if("boolean"!=typeof t.warnings)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.warningsCount){const e=l;if("boolean"!=typeof t.warningsCount)return Be.errors=[{params:{type:"boolean"}}],!1;p=e===l}else p=!0;if(p){if(void 0!==t.warningsFilter){const e=l,n=l;let o=!1,s=null;const f=l;if(qe(t.warningsFilter,{instancePath:r+"/warningsFilter",parentData:t,parentDataProperty:"warningsFilter",rootData:i})||(a=null===a?qe.errors:a.concat(qe.errors),l=a.length),f===l&&(o=!0,s=0),!o){const e={params:{passingSchemas:s}};return null===a?a=[e]:a.push(e),l++,Be.errors=a,!1}l=n,null!==a&&(n?a.length=n:a=null),p=e===l}else p=!0;if(p)if(void 0!==t.warningsSpace){const e=l;if("number"!=typeof t.warningsSpace)return Be.errors=[{params:{type:"number"}}],!1;p=e===l}else p=!0}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}return Be.errors=a,0===l}function He(e,{instancePath:t="",parentData:n,parentDataProperty:r,rootData:o=e}={}){let s=null,i=0;const a=i;let l=!1;const p=i;if("none"!==e&&"summary"!==e&&"errors-only"!==e&&"errors-warnings"!==e&&"minimal"!==e&&"normal"!==e&&"detailed"!==e&&"verbose"!==e){const e={params:{}};null===s?s=[e]:s.push(e),i++}var f=p===i;if(l=l||f,!l){const a=i;if("boolean"!=typeof e){const e={params:{type:"boolean"}};null===s?s=[e]:s.push(e),i++}if(f=a===i,l=l||f,!l){const a=i;Be(e,{instancePath:t,parentData:n,parentDataProperty:r,rootData:o})||(s=null===s?Be.errors:s.concat(Be.errors),i=s.length),f=a===i,l=l||f}}if(!l){const e={params:{}};return null===s?s=[e]:s.push(e),i++,He.errors=s,!1}return i=a,null!==s&&(a?s.length=a:s=null),He.errors=s,0===i}const Je=new RegExp("^(inline-|hidden-|eval-)?(nosources-)?(cheap-(module-)?)?source-map(-debugids)?$","u");function _e(r,{instancePath:o="",parentData:i,parentDataProperty:a,rootData:l=r}={}){let p=null,f=0;if(0===f){if(!r||"object"!=typeof r||Array.isArray(r))return _e.errors=[{params:{type:"object"}}],!1;{const i=f;for(const e in r)if(!n.call(t.properties,e))return _e.errors=[{params:{additionalProperty:e}}],!1;if(i===f){if(void 0!==r.amd){let e=r.amd;const t=f,n=f;let o=!1;const s=f;if(!1!==e){const e={params:{}};null===p?p=[e]:p.push(e),f++}var u=s===f;if(o=o||u,!o){const t=f;if(!e||"object"!=typeof e||Array.isArray(e)){const e={params:{type:"object"}};null===p?p=[e]:p.push(e),f++}u=t===f,o=o||u}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=n,null!==p&&(n?p.length=n:p=null);var c=t===f}else c=!0;if(c){if(void 0!==r.bail){const e=f;if("boolean"!=typeof r.bail)return _e.errors=[{params:{type:"boolean"}}],!1;c=e===f}else c=!0;if(c){if(void 0!==r.cache){const e=f;s(r.cache,{instancePath:o+"/cache",parentData:r,parentDataProperty:"cache",rootData:l})||(p=null===p?s.errors:p.concat(s.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.context){let t=r.context;const n=f;if(f==f){if("string"!=typeof t)return _e.errors=[{params:{type:"string"}}],!1;if(t.includes("!")||!0!==e.test(t))return _e.errors=[{params:{}}],!1}c=n===f}else c=!0;if(c){if(void 0!==r.dependencies){let e=r.dependencies;const t=f;if(f==f){if(!Array.isArray(e))return _e.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){const t=f;if("string"!=typeof e[n])return _e.errors=[{params:{type:"string"}}],!1;if(t!==f)break}}}c=t===f}else c=!0;if(c){if(void 0!==r.devServer){let e=r.devServer;const t=f,n=f;let o=!1;const s=f;if(!1!==e){const e={params:{}};null===p?p=[e]:p.push(e),f++}var y=s===f;if(o=o||y,!o){const t=f;if(!e||"object"!=typeof e||Array.isArray(e)){const e={params:{type:"object"}};null===p?p=[e]:p.push(e),f++}y=t===f,o=o||y}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=n,null!==p&&(n?p.length=n:p=null),c=t===f}else c=!0;if(c){if(void 0!==r.devtool){let e=r.devtool;const t=f,n=f;let o=!1;const s=f;if(!1!==e&&"eval"!==e){const e={params:{}};null===p?p=[e]:p.push(e),f++}var m=s===f;if(o=o||m,!o){const t=f;if(f===t)if("string"==typeof e){if(!Je.test(e)){const e={params:{pattern:"^(inline-|hidden-|eval-)?(nosources-)?(cheap-(module-)?)?source-map(-debugids)?$"}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}m=t===f,o=o||m}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=n,null!==p&&(n?p.length=n:p=null),c=t===f}else c=!0;if(c){if(void 0!==r.entry){const e=f;g(r.entry,{instancePath:o+"/entry",parentData:r,parentDataProperty:"entry",rootData:l})||(p=null===p?g.errors:p.concat(g.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.experiments){const e=f;x(r.experiments,{instancePath:o+"/experiments",parentData:r,parentDataProperty:"experiments",rootData:l})||(p=null===p?x.errors:p.concat(x.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.extends){const e=f;$(r.extends,{instancePath:o+"/extends",parentData:r,parentDataProperty:"extends",rootData:l})||(p=null===p?$.errors:p.concat($.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.externals){const e=f;S(r.externals,{instancePath:o+"/externals",parentData:r,parentDataProperty:"externals",rootData:l})||(p=null===p?S.errors:p.concat(S.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.externalsPresets){let e=r.externalsPresets;const t=f;if(f==f){if(!e||"object"!=typeof e||Array.isArray(e))return _e.errors=[{params:{type:"object"}}],!1;{const t=f;for(const t in e)if("electron"!==t&&"electronMain"!==t&&"electronPreload"!==t&&"electronRenderer"!==t&&"node"!==t&&"nwjs"!==t&&"web"!==t&&"webAsync"!==t)return _e.errors=[{params:{additionalProperty:t}}],!1;if(t===f){if(void 0!==e.electron){const t=f;if("boolean"!=typeof e.electron)return _e.errors=[{params:{type:"boolean"}}],!1;var d=t===f}else d=!0;if(d){if(void 0!==e.electronMain){const t=f;if("boolean"!=typeof e.electronMain)return _e.errors=[{params:{type:"boolean"}}],!1;d=t===f}else d=!0;if(d){if(void 0!==e.electronPreload){const t=f;if("boolean"!=typeof e.electronPreload)return _e.errors=[{params:{type:"boolean"}}],!1;d=t===f}else d=!0;if(d){if(void 0!==e.electronRenderer){const t=f;if("boolean"!=typeof e.electronRenderer)return _e.errors=[{params:{type:"boolean"}}],!1;d=t===f}else d=!0;if(d){if(void 0!==e.node){const t=f;if("boolean"!=typeof e.node)return _e.errors=[{params:{type:"boolean"}}],!1;d=t===f}else d=!0;if(d){if(void 0!==e.nwjs){const t=f;if("boolean"!=typeof e.nwjs)return _e.errors=[{params:{type:"boolean"}}],!1;d=t===f}else d=!0;if(d){if(void 0!==e.web){const t=f;if("boolean"!=typeof e.web)return _e.errors=[{params:{type:"boolean"}}],!1;d=t===f}else d=!0;if(d)if(void 0!==e.webAsync){const t=f;if("boolean"!=typeof e.webAsync)return _e.errors=[{params:{type:"boolean"}}],!1;d=t===f}else d=!0}}}}}}}}}c=t===f}else c=!0;if(c){if(void 0!==r.externalsType){let e=r.externalsType;const t=f;if("var"!==e&&"module"!==e&&"assign"!==e&&"this"!==e&&"window"!==e&&"self"!==e&&"global"!==e&&"commonjs"!==e&&"commonjs2"!==e&&"commonjs-module"!==e&&"commonjs-static"!==e&&"amd"!==e&&"amd-require"!==e&&"umd"!==e&&"umd2"!==e&&"jsonp"!==e&&"system"!==e&&"promise"!==e&&"import"!==e&&"module-import"!==e&&"script"!==e&&"node-commonjs"!==e)return _e.errors=[{params:{}}],!1;c=t===f}else c=!0;if(c){if(void 0!==r.ignoreWarnings){let e=r.ignoreWarnings;const t=f;if(f==f){if(!Array.isArray(e))return _e.errors=[{params:{type:"array"}}],!1;{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=f,o=f;let s=!1;const i=f;if(!(t instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}var h=i===f;if(s=s||h,!s){const e=f;if(f===e)if(t&&"object"==typeof t&&!Array.isArray(t)){const e=f;for(const e in t)if("file"!==e&&"message"!==e&&"module"!==e){const t={params:{additionalProperty:e}};null===p?p=[t]:p.push(t),f++;break}if(e===f){if(void 0!==t.file){const e=f;if(!(t.file instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}var b=e===f}else b=!0;if(b){if(void 0!==t.message){const e=f;if(!(t.message instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}b=e===f}else b=!0;if(b)if(void 0!==t.module){const e=f;if(!(t.module instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}b=e===f}else b=!0}}}else{const e={params:{type:"object"}};null===p?p=[e]:p.push(e),f++}if(h=e===f,s=s||h,!s){const e=f;if(!(t instanceof Function)){const e={params:{}};null===p?p=[e]:p.push(e),f++}h=e===f,s=s||h}}if(!s){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}if(f=o,null!==p&&(o?p.length=o:p=null),r!==f)break}}}c=t===f}else c=!0;if(c){if(void 0!==r.infrastructureLogging){const e=f;E(r.infrastructureLogging,{instancePath:o+"/infrastructureLogging",parentData:r,parentDataProperty:"infrastructureLogging",rootData:l})||(p=null===p?E.errors:p.concat(E.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.loader){let e=r.loader;const t=f;if(!e||"object"!=typeof e||Array.isArray(e))return _e.errors=[{params:{type:"object"}}],!1;c=t===f}else c=!0;if(c){if(void 0!==r.mode){let e=r.mode;const t=f;if("development"!==e&&"production"!==e&&"none"!==e)return _e.errors=[{params:{}}],!1;c=t===f}else c=!0;if(c){if(void 0!==r.module){const e=f;be(r.module,{instancePath:o+"/module",parentData:r,parentDataProperty:"module",rootData:l})||(p=null===p?be.errors:p.concat(be.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.name){const e=f;if("string"!=typeof r.name)return _e.errors=[{params:{type:"string"}}],!1;c=e===f}else c=!0;if(c){if(void 0!==r.node){const e=f;me(r.node,{instancePath:o+"/node",parentData:r,parentDataProperty:"node",rootData:l})||(p=null===p?me.errors:p.concat(me.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.optimization){const e=f;Ce(r.optimization,{instancePath:o+"/optimization",parentData:r,parentDataProperty:"optimization",rootData:l})||(p=null===p?Ce.errors:p.concat(Ce.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.output){const e=f;Me(r.output,{instancePath:o+"/output",parentData:r,parentDataProperty:"output",rootData:l})||(p=null===p?Me.errors:p.concat(Me.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.parallelism){let e=r.parallelism;const t=f;if(f==f){if("number"!=typeof e)return _e.errors=[{params:{type:"number"}}],!1;if(e<1||isNaN(e))return _e.errors=[{params:{comparison:">=",limit:1}}],!1}c=t===f}else c=!0;if(c){if(void 0!==r.performance){const e=f;we(r.performance,{instancePath:o+"/performance",parentData:r,parentDataProperty:"performance",rootData:l})||(p=null===p?we.errors:p.concat(we.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.plugins){const e=f;Ie(r.plugins,{instancePath:o+"/plugins",parentData:r,parentDataProperty:"plugins",rootData:l})||(p=null===p?Ie.errors:p.concat(Ie.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.profile){const e=f;if("boolean"!=typeof r.profile)return _e.errors=[{params:{type:"boolean"}}],!1;c=e===f}else c=!0;if(c){if(void 0!==r.recordsInputPath){let t=r.recordsInputPath;const n=f,o=f;let s=!1;const i=f;if(!1!==t){const e={params:{}};null===p?p=[e]:p.push(e),f++}var v=i===f;if(s=s||v,!s){const n=f;if(f===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}v=n===f,s=s||v}if(!s){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=o,null!==p&&(o?p.length=o:p=null),c=n===f}else c=!0;if(c){if(void 0!==r.recordsOutputPath){let t=r.recordsOutputPath;const n=f,o=f;let s=!1;const i=f;if(!1!==t){const e={params:{}};null===p?p=[e]:p.push(e),f++}var P=i===f;if(s=s||P,!s){const n=f;if(f===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}P=n===f,s=s||P}if(!s){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=o,null!==p&&(o?p.length=o:p=null),c=n===f}else c=!0;if(c){if(void 0!==r.recordsPath){let t=r.recordsPath;const n=f,o=f;let s=!1;const i=f;if(!1!==t){const e={params:{}};null===p?p=[e]:p.push(e),f++}var D=i===f;if(s=s||D,!s){const n=f;if(f===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}D=n===f,s=s||D}if(!s){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=o,null!==p&&(o?p.length=o:p=null),c=n===f}else c=!0;if(c){if(void 0!==r.resolve){const e=f;Te(r.resolve,{instancePath:o+"/resolve",parentData:r,parentDataProperty:"resolve",rootData:l})||(p=null===p?Te.errors:p.concat(Te.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.resolveLoader){const e=f;Ne(r.resolveLoader,{instancePath:o+"/resolveLoader",parentData:r,parentDataProperty:"resolveLoader",rootData:l})||(p=null===p?Ne.errors:p.concat(Ne.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.snapshot){let t=r.snapshot;const n=f;if(f==f){if(!t||"object"!=typeof t||Array.isArray(t))return _e.errors=[{params:{type:"object"}}],!1;{const n=f;for(const e in t)if("buildDependencies"!==e&&"immutablePaths"!==e&&"managedPaths"!==e&&"module"!==e&&"resolve"!==e&&"resolveBuildDependencies"!==e&&"unmanagedPaths"!==e)return _e.errors=[{params:{additionalProperty:e}}],!1;if(n===f){if(void 0!==t.buildDependencies){let e=t.buildDependencies;const n=f;if(f===n){if(!e||"object"!=typeof e||Array.isArray(e))return _e.errors=[{params:{type:"object"}}],!1;{const t=f;for(const t in e)if("hash"!==t&&"timestamp"!==t)return _e.errors=[{params:{additionalProperty:t}}],!1;if(t===f){if(void 0!==e.hash){const t=f;if("boolean"!=typeof e.hash)return _e.errors=[{params:{type:"boolean"}}],!1;var O=t===f}else O=!0;if(O)if(void 0!==e.timestamp){const t=f;if("boolean"!=typeof e.timestamp)return _e.errors=[{params:{type:"boolean"}}],!1;O=t===f}else O=!0}}}var C=n===f}else C=!0;if(C){if(void 0!==t.immutablePaths){let n=t.immutablePaths;const r=f;if(f===r){if(!Array.isArray(n))return _e.errors=[{params:{type:"array"}}],!1;{const t=n.length;for(let r=0;r<t;r++){let t=n[r];const o=f,s=f;let i=!1;const a=f;if(!(t instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}var A=a===f;if(i=i||A,!i){const n=f;if(f===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===p?p=[e]:p.push(e),f++}else if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}A=n===f,i=i||A}if(!i){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}if(f=s,null!==p&&(s?p.length=s:p=null),o!==f)break}}}C=r===f}else C=!0;if(C){if(void 0!==t.managedPaths){let n=t.managedPaths;const r=f;if(f===r){if(!Array.isArray(n))return _e.errors=[{params:{type:"array"}}],!1;{const t=n.length;for(let r=0;r<t;r++){let t=n[r];const o=f,s=f;let i=!1;const a=f;if(!(t instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}var k=a===f;if(i=i||k,!i){const n=f;if(f===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===p?p=[e]:p.push(e),f++}else if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}k=n===f,i=i||k}if(!i){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}if(f=s,null!==p&&(s?p.length=s:p=null),o!==f)break}}}C=r===f}else C=!0;if(C){if(void 0!==t.module){let e=t.module;const n=f;if(f===n){if(!e||"object"!=typeof e||Array.isArray(e))return _e.errors=[{params:{type:"object"}}],!1;{const t=f;for(const t in e)if("hash"!==t&&"timestamp"!==t)return _e.errors=[{params:{additionalProperty:t}}],!1;if(t===f){if(void 0!==e.hash){const t=f;if("boolean"!=typeof e.hash)return _e.errors=[{params:{type:"boolean"}}],!1;var j=t===f}else j=!0;if(j)if(void 0!==e.timestamp){const t=f;if("boolean"!=typeof e.timestamp)return _e.errors=[{params:{type:"boolean"}}],!1;j=t===f}else j=!0}}}C=n===f}else C=!0;if(C){if(void 0!==t.resolve){let e=t.resolve;const n=f;if(f===n){if(!e||"object"!=typeof e||Array.isArray(e))return _e.errors=[{params:{type:"object"}}],!1;{const t=f;for(const t in e)if("hash"!==t&&"timestamp"!==t)return _e.errors=[{params:{additionalProperty:t}}],!1;if(t===f){if(void 0!==e.hash){const t=f;if("boolean"!=typeof e.hash)return _e.errors=[{params:{type:"boolean"}}],!1;var F=t===f}else F=!0;if(F)if(void 0!==e.timestamp){const t=f;if("boolean"!=typeof e.timestamp)return _e.errors=[{params:{type:"boolean"}}],!1;F=t===f}else F=!0}}}C=n===f}else C=!0;if(C){if(void 0!==t.resolveBuildDependencies){let e=t.resolveBuildDependencies;const n=f;if(f===n){if(!e||"object"!=typeof e||Array.isArray(e))return _e.errors=[{params:{type:"object"}}],!1;{const t=f;for(const t in e)if("hash"!==t&&"timestamp"!==t)return _e.errors=[{params:{additionalProperty:t}}],!1;if(t===f){if(void 0!==e.hash){const t=f;if("boolean"!=typeof e.hash)return _e.errors=[{params:{type:"boolean"}}],!1;var R=t===f}else R=!0;if(R)if(void 0!==e.timestamp){const t=f;if("boolean"!=typeof e.timestamp)return _e.errors=[{params:{type:"boolean"}}],!1;R=t===f}else R=!0}}}C=n===f}else C=!0;if(C)if(void 0!==t.unmanagedPaths){let n=t.unmanagedPaths;const r=f;if(f===r){if(!Array.isArray(n))return _e.errors=[{params:{type:"array"}}],!1;{const t=n.length;for(let r=0;r<t;r++){let t=n[r];const o=f,s=f;let i=!1;const a=f;if(!(t instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}var L=a===f;if(i=i||L,!i){const n=f;if(f===n)if("string"==typeof t){if(t.includes("!")||!0!==e.test(t)){const e={params:{}};null===p?p=[e]:p.push(e),f++}else if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}L=n===f,i=i||L}if(!i){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}if(f=s,null!==p&&(s?p.length=s:p=null),o!==f)break}}}C=r===f}else C=!0}}}}}}}}c=n===f}else c=!0;if(c){if(void 0!==r.stats){const e=f;He(r.stats,{instancePath:o+"/stats",parentData:r,parentDataProperty:"stats",rootData:l})||(p=null===p?He.errors:p.concat(He.errors),f=p.length),c=e===f}else c=!0;if(c){if(void 0!==r.target){let e=r.target;const t=f,n=f;let o=!1;const s=f;if(f===s)if(Array.isArray(e))if(e.length<1){const e={params:{limit:1}};null===p?p=[e]:p.push(e),f++}else{const t=e.length;for(let n=0;n<t;n++){let t=e[n];const r=f;if(f===r)if("string"==typeof t){if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}if(r!==f)break}}else{const e={params:{type:"array"}};null===p?p=[e]:p.push(e),f++}var z=s===f;if(o=o||z,!o){const t=f;if(!1!==e){const e={params:{}};null===p?p=[e]:p.push(e),f++}if(z=t===f,o=o||z,!o){const t=f;if(f===t)if("string"==typeof e){if(e.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}z=t===f,o=o||z}}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=n,null!==p&&(n?p.length=n:p=null),c=t===f}else c=!0;if(c){if(void 0!==r.watch){const e=f;if("boolean"!=typeof r.watch)return _e.errors=[{params:{type:"boolean"}}],!1;c=e===f}else c=!0;if(c)if(void 0!==r.watchOptions){let e=r.watchOptions;const t=f;if(f==f){if(!e||"object"!=typeof e||Array.isArray(e))return _e.errors=[{params:{type:"object"}}],!1;{const t=f;for(const t in e)if("aggregateTimeout"!==t&&"followSymlinks"!==t&&"ignored"!==t&&"poll"!==t&&"stdin"!==t)return _e.errors=[{params:{additionalProperty:t}}],!1;if(t===f){if(void 0!==e.aggregateTimeout){const t=f;if("number"!=typeof e.aggregateTimeout)return _e.errors=[{params:{type:"number"}}],!1;var M=t===f}else M=!0;if(M){if(void 0!==e.followSymlinks){const t=f;if("boolean"!=typeof e.followSymlinks)return _e.errors=[{params:{type:"boolean"}}],!1;M=t===f}else M=!0;if(M){if(void 0!==e.ignored){let t=e.ignored;const n=f,r=f;let o=!1;const s=f;if(f===s)if(Array.isArray(t)){const e=t.length;for(let n=0;n<e;n++){let e=t[n];const r=f;if(f===r)if("string"==typeof e){if(e.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}if(r!==f)break}}else{const e={params:{type:"array"}};null===p?p=[e]:p.push(e),f++}var w=s===f;if(o=o||w,!o){const e=f;if(!(t instanceof RegExp)){const e={params:{}};null===p?p=[e]:p.push(e),f++}if(w=e===f,o=o||w,!o){const e=f;if(f===e)if("string"==typeof t){if(t.length<1){const e={params:{}};null===p?p=[e]:p.push(e),f++}}else{const e={params:{type:"string"}};null===p?p=[e]:p.push(e),f++}w=e===f,o=o||w}}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=r,null!==p&&(r?p.length=r:p=null),M=n===f}else M=!0;if(M){if(void 0!==e.poll){let t=e.poll;const n=f,r=f;let o=!1;const s=f;if("number"!=typeof t){const e={params:{type:"number"}};null===p?p=[e]:p.push(e),f++}var I=s===f;if(o=o||I,!o){const e=f;if("boolean"!=typeof t){const e={params:{type:"boolean"}};null===p?p=[e]:p.push(e),f++}I=e===f,o=o||I}if(!o){const e={params:{}};return null===p?p=[e]:p.push(e),f++,_e.errors=p,!1}f=r,null!==p&&(r?p.length=r:p=null),M=n===f}else M=!0;if(M)if(void 0!==e.stdin){const t=f;if("boolean"!=typeof e.stdin)return _e.errors=[{params:{type:"boolean"}}],!1;M=t===f}else M=!0}}}}}}c=t===f}else c=!0}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}return _e.errors=p,0===f}