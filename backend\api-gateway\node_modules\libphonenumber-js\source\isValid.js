import Metadata from './metadata.js'
import matchesEntirely from './helpers/matchesEntirely.js'
import getNumberType from './helpers/getNumberType.js'

/**
 * Checks if a given phone number is valid.
 *
 * isValid(phoneNumberInstance, { ..., v2: true }, metadata)
 *
 * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)
 * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)
 *
 * If the `number` is a string, it will be parsed to an object,
 * but only if it contains only valid phone number characters (including punctuation).
 * If the `number` is an object, it is used as is.
 *
 * The optional `defaultCountry` argument is the default country.
 * I.e. it does not restrict to just that country,
 * e.g. in those cases where several countries share
 * the same phone numbering rules (NANPA, Britain, etc).
 * For example, even though the number `07624 369230`
 * belongs to the Isle of Man ("IM" country code)
 * calling `isValidNumber('07624369230', 'GB', metadata)`
 * still returns `true` because the country is not restricted to `GB`,
 * it's just that `GB` is the default one for the phone numbering rules.
 * For restricting the country see `isValidNumberForRegion()`
 * though restricting a country might not be a good idea.
 * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion
 *
 * Examples:
 *
 * ```js
 * isValidNumber('+78005553535', metadata)
 * isValidNumber('8005553535', 'RU', metadata)
 * isValidNumber('88005553535', 'RU', metadata)
 * isValidNumber({ phone: '8005553535', country: 'RU' }, metadata)
 * ```
 */
export default function isValidNumber(input, options, metadata)
{
	// If assigning the `{}` default value is moved to the arguments above,
	// code coverage would decrease for some weird reason.
	options = options || {}

	metadata = new Metadata(metadata)

	metadata.selectNumberingPlan(input.country, input.countryCallingCode)

	// By default, countries only have type regexps when it's required for
	// distinguishing different countries having the same `countryCallingCode`.
	if (metadata.hasTypes()) {
		return getNumberType(input, options, metadata.metadata) !== undefined
	}

	// If there are no type regexps for this country in metadata then use
	// `nationalNumberPattern` as a "better than nothing" replacement.
	const nationalNumber = options.v2 ? input.nationalNumber : input.phone
	return matchesEntirely(nationalNumber, metadata.nationalNumberPattern())
}