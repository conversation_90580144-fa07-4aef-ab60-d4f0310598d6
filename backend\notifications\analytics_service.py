"""
Notification Analytics Service
=============================
خدمة تحليلات الإشعارات المتقدمة
"""

import asyncio
import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from decimal import Decimal

import asyncpg
from ..shared.database.connection import DatabaseConnection
from .notification_service import NotificationChannel, NotificationType, NotificationStatus

logger = logging.getLogger(__name__)


class AnalyticsPeriod(Enum):
    """فترات التحليل"""
    HOUR = "hour"
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"


@dataclass
class NotificationMetrics:
    """مقاييس الإشعارات"""
    total_sent: int
    total_delivered: int
    total_failed: int
    delivery_rate: float
    failure_rate: float
    average_delivery_time: float
    bounce_rate: float
    open_rate: float
    click_rate: float


@dataclass
class ChannelPerformance:
    """أداء القناة"""
    channel: str
    total_notifications: int
    successful_deliveries: int
    failed_deliveries: int
    delivery_rate: float
    average_cost: float
    user_engagement: float


@dataclass
class UserEngagement:
    """تفاعل المستخدم"""
    user_id: str
    total_received: int
    total_opened: int
    total_clicked: int
    open_rate: float
    click_rate: float
    preferred_channel: str
    last_interaction: datetime


class NotificationAnalytics:
    """خدمة تحليلات الإشعارات"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Analytics cache
        self.analytics_cache = {}
        self.cache_duration = 1800  # 30 minutes
        
        # Performance tracking
        self.query_performance = {}
    
    async def get_delivery_analytics(
        self,
        start_date: date,
        end_date: date,
        channel: NotificationChannel = None,
        notification_type: NotificationType = None,
        group_by: AnalyticsPeriod = AnalyticsPeriod.DAY
    ) -> Dict[str, Any]:
        """تحليلات التسليم"""
        try:
            logger.info(f"📊 Getting delivery analytics from {start_date} to {end_date}")
            
            # Check cache
            cache_key = f"delivery_{start_date}_{end_date}_{channel}_{notification_type}_{group_by.value}"
            if self._is_cached(cache_key):
                return self.analytics_cache[cache_key]['data']
            
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = ["DATE(created_at) BETWEEN $1 AND $2"]
                params = [start_date, end_date]
                param_count = 2
                
                if channel:
                    param_count += 1
                    where_conditions.append(f"channel = ${param_count}")
                    params.append(channel.value)
                
                if notification_type:
                    param_count += 1
                    where_conditions.append(f"notification_type = ${param_count}")
                    params.append(notification_type.value)
                
                where_clause = " AND ".join(where_conditions)
                
                # Determine grouping
                if group_by == AnalyticsPeriod.HOUR:
                    date_group = "DATE_TRUNC('hour', created_at)"
                    date_format = "YYYY-MM-DD HH24:00:00"
                elif group_by == AnalyticsPeriod.DAY:
                    date_group = "DATE(created_at)"
                    date_format = "YYYY-MM-DD"
                elif group_by == AnalyticsPeriod.WEEK:
                    date_group = "DATE_TRUNC('week', created_at)"
                    date_format = "YYYY-MM-DD"
                elif group_by == AnalyticsPeriod.MONTH:
                    date_group = "DATE_TRUNC('month', created_at)"
                    date_format = "YYYY-MM"
                else:
                    date_group = "DATE(created_at)"
                    date_format = "YYYY-MM-DD"
                
                # Main analytics query
                analytics_query = f"""
                    SELECT 
                        {date_group} as period,
                        TO_CHAR({date_group}, '{date_format}') as period_label,
                        COUNT(*) as total_notifications,
                        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
                        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                        AVG(CASE 
                            WHEN delivered_at IS NOT NULL AND sent_at IS NOT NULL 
                            THEN EXTRACT(EPOCH FROM (delivered_at - sent_at))
                            ELSE NULL 
                        END) as avg_delivery_time_seconds
                    FROM notifications 
                    WHERE {where_clause}
                    GROUP BY {date_group}
                    ORDER BY period
                """
                
                analytics_data = await conn.fetch(analytics_query, *params)
                
                # Calculate overall metrics
                total_notifications = sum(row['total_notifications'] for row in analytics_data)
                total_sent = sum(row['sent_count'] for row in analytics_data)
                total_delivered = sum(row['delivered_count'] for row in analytics_data)
                total_failed = sum(row['failed_count'] for row in analytics_data)
                
                delivery_rate = (total_delivered / max(total_notifications, 1)) * 100
                failure_rate = (total_failed / max(total_notifications, 1)) * 100
                
                # Channel breakdown
                channel_query = f"""
                    SELECT 
                        channel,
                        COUNT(*) as total,
                        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                        AVG(CASE 
                            WHEN delivered_at IS NOT NULL AND sent_at IS NOT NULL 
                            THEN EXTRACT(EPOCH FROM (delivered_at - sent_at))
                            ELSE NULL 
                        END) as avg_delivery_time
                    FROM notifications 
                    WHERE {where_clause}
                    GROUP BY channel
                    ORDER BY total DESC
                """
                
                channel_data = await conn.fetch(channel_query, *params)
                
                # Type breakdown
                type_query = f"""
                    SELECT 
                        notification_type,
                        COUNT(*) as total,
                        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
                    FROM notifications 
                    WHERE {where_clause}
                    GROUP BY notification_type
                    ORDER BY total DESC
                """
                
                type_data = await conn.fetch(type_query, *params)
                
                result = {
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "group_by": group_by.value
                    },
                    "overall_metrics": {
                        "total_notifications": total_notifications,
                        "total_sent": total_sent,
                        "total_delivered": total_delivered,
                        "total_failed": total_failed,
                        "delivery_rate": round(delivery_rate, 2),
                        "failure_rate": round(failure_rate, 2)
                    },
                    "time_series": [
                        {
                            "period": row['period_label'],
                            "total_notifications": row['total_notifications'],
                            "sent_count": row['sent_count'],
                            "delivered_count": row['delivered_count'],
                            "failed_count": row['failed_count'],
                            "pending_count": row['pending_count'],
                            "delivery_rate": round((row['delivered_count'] / max(row['total_notifications'], 1)) * 100, 2),
                            "avg_delivery_time": round(row['avg_delivery_time_seconds'] or 0, 2)
                        }
                        for row in analytics_data
                    ],
                    "by_channel": [
                        {
                            "channel": row['channel'],
                            "total": row['total'],
                            "delivered": row['delivered'],
                            "failed": row['failed'],
                            "delivery_rate": round((row['delivered'] / max(row['total'], 1)) * 100, 2),
                            "avg_delivery_time": round(row['avg_delivery_time'] or 0, 2)
                        }
                        for row in channel_data
                    ],
                    "by_type": [
                        {
                            "type": row['notification_type'],
                            "total": row['total'],
                            "delivered": row['delivered'],
                            "failed": row['failed'],
                            "delivery_rate": round((row['delivered'] / max(row['total'], 1)) * 100, 2)
                        }
                        for row in type_data
                    ]
                }
                
                # Cache the result
                self._cache_analytics(cache_key, result)
                
                logger.info("✅ Delivery analytics retrieved successfully")
                return result
                
        except Exception as e:
            logger.error(f"❌ Failed to get delivery analytics: {e}")
            return {'error': str(e)}
    
    async def get_user_engagement_analytics(
        self,
        start_date: date,
        end_date: date,
        user_id: str = None,
        limit: int = 100
    ) -> Dict[str, Any]:
        """تحليلات تفاعل المستخدمين"""
        try:
            logger.info(f"👥 Getting user engagement analytics")
            
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = ["DATE(n.created_at) BETWEEN $1 AND $2"]
                params = [start_date, end_date]
                param_count = 2
                
                if user_id:
                    param_count += 1
                    where_conditions.append(f"n.user_id = ${param_count}")
                    params.append(user_id)
                
                where_clause = " AND ".join(where_conditions)
                
                # User engagement query
                engagement_query = f"""
                    SELECT 
                        n.user_id,
                        u.first_name || ' ' || u.last_name as user_name,
                        u.email,
                        COUNT(*) as total_received,
                        COUNT(CASE WHEN ne.opened_at IS NOT NULL THEN 1 END) as total_opened,
                        COUNT(CASE WHEN ne.clicked_at IS NOT NULL THEN 1 END) as total_clicked,
                        MAX(COALESCE(ne.clicked_at, ne.opened_at, n.delivered_at)) as last_interaction,
                        MODE() WITHIN GROUP (ORDER BY n.channel) as preferred_channel
                    FROM notifications n
                    LEFT JOIN notification_events ne ON n.id = ne.notification_id
                    LEFT JOIN users u ON n.user_id = u.id
                    WHERE {where_clause}
                    GROUP BY n.user_id, u.first_name, u.last_name, u.email
                    ORDER BY total_received DESC
                    LIMIT ${param_count + 1}
                """
                
                params.append(limit)
                engagement_data = await conn.fetch(engagement_query, *params)
                
                # Channel preference analysis
                channel_pref_query = f"""
                    SELECT 
                        channel,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(*) as total_notifications,
                        COUNT(CASE WHEN ne.opened_at IS NOT NULL THEN 1 END) as total_opens,
                        COUNT(CASE WHEN ne.clicked_at IS NOT NULL THEN 1 END) as total_clicks
                    FROM notifications n
                    LEFT JOIN notification_events ne ON n.id = ne.notification_id
                    WHERE {where_clause}
                    GROUP BY channel
                    ORDER BY unique_users DESC
                """
                
                channel_pref_data = await conn.fetch(channel_pref_query, *params[:-1])
                
                # Time-based engagement patterns
                time_pattern_query = f"""
                    SELECT 
                        EXTRACT(HOUR FROM n.created_at) as hour_of_day,
                        COUNT(*) as notifications_sent,
                        COUNT(CASE WHEN ne.opened_at IS NOT NULL THEN 1 END) as notifications_opened,
                        COUNT(CASE WHEN ne.clicked_at IS NOT NULL THEN 1 END) as notifications_clicked
                    FROM notifications n
                    LEFT JOIN notification_events ne ON n.id = ne.notification_id
                    WHERE {where_clause}
                    GROUP BY EXTRACT(HOUR FROM n.created_at)
                    ORDER BY hour_of_day
                """
                
                time_pattern_data = await conn.fetch(time_pattern_query, *params[:-1])
                
                # Calculate overall engagement metrics
                total_users = len(engagement_data)
                total_notifications = sum(row['total_received'] for row in engagement_data)
                total_opens = sum(row['total_opened'] for row in engagement_data)
                total_clicks = sum(row['total_clicked'] for row in engagement_data)
                
                overall_open_rate = (total_opens / max(total_notifications, 1)) * 100
                overall_click_rate = (total_clicks / max(total_notifications, 1)) * 100
                
                return {
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat()
                    },
                    "overall_metrics": {
                        "total_users": total_users,
                        "total_notifications": total_notifications,
                        "total_opens": total_opens,
                        "total_clicks": total_clicks,
                        "overall_open_rate": round(overall_open_rate, 2),
                        "overall_click_rate": round(overall_click_rate, 2)
                    },
                    "user_engagement": [
                        {
                            "user_id": row['user_id'],
                            "user_name": row['user_name'],
                            "email": row['email'],
                            "total_received": row['total_received'],
                            "total_opened": row['total_opened'],
                            "total_clicked": row['total_clicked'],
                            "open_rate": round((row['total_opened'] / max(row['total_received'], 1)) * 100, 2),
                            "click_rate": round((row['total_clicked'] / max(row['total_received'], 1)) * 100, 2),
                            "preferred_channel": row['preferred_channel'],
                            "last_interaction": row['last_interaction'].isoformat() if row['last_interaction'] else None
                        }
                        for row in engagement_data
                    ],
                    "channel_preferences": [
                        {
                            "channel": row['channel'],
                            "unique_users": row['unique_users'],
                            "total_notifications": row['total_notifications'],
                            "total_opens": row['total_opens'],
                            "total_clicks": row['total_clicks'],
                            "open_rate": round((row['total_opens'] / max(row['total_notifications'], 1)) * 100, 2),
                            "click_rate": round((row['total_clicks'] / max(row['total_notifications'], 1)) * 100, 2)
                        }
                        for row in channel_pref_data
                    ],
                    "time_patterns": [
                        {
                            "hour": int(row['hour_of_day']),
                            "notifications_sent": row['notifications_sent'],
                            "notifications_opened": row['notifications_opened'],
                            "notifications_clicked": row['notifications_clicked'],
                            "open_rate": round((row['notifications_opened'] / max(row['notifications_sent'], 1)) * 100, 2),
                            "click_rate": round((row['notifications_clicked'] / max(row['notifications_sent'], 1)) * 100, 2)
                        }
                        for row in time_pattern_data
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get user engagement analytics: {e}")
            return {'error': str(e)}
    
    async def get_channel_performance_comparison(
        self,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """مقارنة أداء القنوات"""
        try:
            logger.info(f"📈 Getting channel performance comparison")
            
            async with self.db_connection.get_connection() as conn:
                # Channel performance query
                performance_query = """
                    SELECT 
                        n.channel,
                        COUNT(*) as total_notifications,
                        COUNT(CASE WHEN n.status = 'sent' THEN 1 END) as sent_count,
                        COUNT(CASE WHEN n.status = 'delivered' THEN 1 END) as delivered_count,
                        COUNT(CASE WHEN n.status = 'failed' THEN 1 END) as failed_count,
                        COUNT(CASE WHEN ne.opened_at IS NOT NULL THEN 1 END) as opened_count,
                        COUNT(CASE WHEN ne.clicked_at IS NOT NULL THEN 1 END) as clicked_count,
                        AVG(CASE 
                            WHEN n.delivered_at IS NOT NULL AND n.sent_at IS NOT NULL 
                            THEN EXTRACT(EPOCH FROM (n.delivered_at - n.sent_at))
                            ELSE NULL 
                        END) as avg_delivery_time,
                        COUNT(DISTINCT n.user_id) as unique_users,
                        AVG(CASE 
                            WHEN ne.opened_at IS NOT NULL AND n.delivered_at IS NOT NULL
                            THEN EXTRACT(EPOCH FROM (ne.opened_at - n.delivered_at))
                            ELSE NULL
                        END) as avg_open_time
                    FROM notifications n
                    LEFT JOIN notification_events ne ON n.id = ne.notification_id
                    WHERE DATE(n.created_at) BETWEEN $1 AND $2
                    GROUP BY n.channel
                    ORDER BY total_notifications DESC
                """
                
                performance_data = await conn.fetch(performance_query, start_date, end_date)
                
                # Cost analysis (if cost data is available)
                cost_query = """
                    SELECT 
                        channel,
                        AVG(cost_per_notification) as avg_cost,
                        SUM(total_cost) as total_cost
                    FROM notification_costs 
                    WHERE DATE(date) BETWEEN $1 AND $2
                    GROUP BY channel
                """
                
                cost_data = await conn.fetch(cost_query, start_date, end_date)
                cost_lookup = {row['channel']: row for row in cost_data}
                
                # Build comparison data
                comparison_data = []
                for row in performance_data:
                    channel = row['channel']
                    cost_info = cost_lookup.get(channel, {'avg_cost': 0, 'total_cost': 0})
                    
                    delivery_rate = (row['delivered_count'] / max(row['total_notifications'], 1)) * 100
                    open_rate = (row['opened_count'] / max(row['delivered_count'], 1)) * 100
                    click_rate = (row['clicked_count'] / max(row['delivered_count'], 1)) * 100
                    
                    comparison_data.append({
                        "channel": channel,
                        "total_notifications": row['total_notifications'],
                        "delivered_count": row['delivered_count'],
                        "failed_count": row['failed_count'],
                        "delivery_rate": round(delivery_rate, 2),
                        "open_rate": round(open_rate, 2),
                        "click_rate": round(click_rate, 2),
                        "avg_delivery_time": round(row['avg_delivery_time'] or 0, 2),
                        "avg_open_time": round(row['avg_open_time'] or 0, 2),
                        "unique_users": row['unique_users'],
                        "avg_cost": float(cost_info['avg_cost'] or 0),
                        "total_cost": float(cost_info['total_cost'] or 0),
                        "cost_per_delivery": round(
                            float(cost_info['total_cost'] or 0) / max(row['delivered_count'], 1), 4
                        )
                    })
                
                # Calculate rankings
                for metric in ['delivery_rate', 'open_rate', 'click_rate']:
                    sorted_data = sorted(comparison_data, key=lambda x: x[metric], reverse=True)
                    for i, item in enumerate(sorted_data):
                        item[f"{metric}_rank"] = i + 1
                
                return {
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat()
                    },
                    "channel_comparison": comparison_data,
                    "best_performers": {
                        "highest_delivery_rate": max(comparison_data, key=lambda x: x['delivery_rate'])['channel'],
                        "highest_open_rate": max(comparison_data, key=lambda x: x['open_rate'])['channel'],
                        "highest_click_rate": max(comparison_data, key=lambda x: x['click_rate'])['channel'],
                        "lowest_cost_per_delivery": min(comparison_data, key=lambda x: x['cost_per_delivery'])['channel']
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get channel performance comparison: {e}")
            return {'error': str(e)}
    
    async def get_notification_trends(
        self,
        days: int = 30,
        notification_type: NotificationType = None
    ) -> Dict[str, Any]:
        """اتجاهات الإشعارات"""
        try:
            logger.info(f"📊 Getting notification trends for last {days} days")
            
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = ["DATE(created_at) BETWEEN $1 AND $2"]
                params = [start_date, end_date]
                
                if notification_type:
                    where_conditions.append("notification_type = $3")
                    params.append(notification_type.value)
                
                where_clause = " AND ".join(where_conditions)
                
                # Daily trends
                trends_query = f"""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as total_notifications,
                        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                        COUNT(DISTINCT user_id) as unique_users
                    FROM notifications 
                    WHERE {where_clause}
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """
                
                trends_data = await conn.fetch(trends_query, *params)
                
                # Calculate growth rates
                trends_with_growth = []
                prev_total = 0
                
                for i, row in enumerate(trends_data):
                    current_total = row['total_notifications']
                    growth_rate = 0
                    
                    if i > 0 and prev_total > 0:
                        growth_rate = ((current_total - prev_total) / prev_total) * 100
                    
                    trends_with_growth.append({
                        "date": row['date'].isoformat(),
                        "total_notifications": current_total,
                        "delivered_count": row['delivered_count'],
                        "failed_count": row['failed_count'],
                        "unique_users": row['unique_users'],
                        "delivery_rate": round((row['delivered_count'] / max(current_total, 1)) * 100, 2),
                        "growth_rate": round(growth_rate, 2)
                    })
                    
                    prev_total = current_total
                
                # Calculate overall trend metrics
                if len(trends_data) >= 2:
                    first_week = trends_data[:7]
                    last_week = trends_data[-7:]
                    
                    first_week_avg = sum(row['total_notifications'] for row in first_week) / len(first_week)
                    last_week_avg = sum(row['total_notifications'] for row in last_week) / len(last_week)
                    
                    overall_trend = ((last_week_avg - first_week_avg) / max(first_week_avg, 1)) * 100
                else:
                    overall_trend = 0
                
                return {
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "days": days
                    },
                    "overall_trend": round(overall_trend, 2),
                    "daily_trends": trends_with_growth,
                    "summary": {
                        "total_notifications": sum(row['total_notifications'] for row in trends_data),
                        "total_delivered": sum(row['delivered_count'] for row in trends_data),
                        "total_failed": sum(row['failed_count'] for row in trends_data),
                        "avg_daily_notifications": round(
                            sum(row['total_notifications'] for row in trends_data) / max(len(trends_data), 1), 2
                        ),
                        "peak_day": max(trends_data, key=lambda x: x['total_notifications'])['date'].isoformat() if trends_data else None
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get notification trends: {e}")
            return {'error': str(e)}
    
    async def generate_analytics_report(
        self,
        start_date: date,
        end_date: date,
        include_user_engagement: bool = True,
        include_channel_comparison: bool = True,
        include_trends: bool = True
    ) -> Dict[str, Any]:
        """إنشاء تقرير تحليلات شامل"""
        try:
            logger.info(f"📋 Generating comprehensive analytics report")
            
            report = {
                "report_info": {
                    "generated_at": datetime.now().isoformat(),
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "days": (end_date - start_date).days + 1
                    }
                }
            }
            
            # Get delivery analytics
            delivery_analytics = await self.get_delivery_analytics(start_date, end_date)
            report["delivery_analytics"] = delivery_analytics
            
            # Get user engagement analytics
            if include_user_engagement:
                user_engagement = await self.get_user_engagement_analytics(start_date, end_date)
                report["user_engagement"] = user_engagement
            
            # Get channel performance comparison
            if include_channel_comparison:
                channel_performance = await self.get_channel_performance_comparison(start_date, end_date)
                report["channel_performance"] = channel_performance
            
            # Get notification trends
            if include_trends:
                days = (end_date - start_date).days + 1
                trends = await self.get_notification_trends(days)
                report["trends"] = trends
            
            # Generate insights and recommendations
            insights = await self._generate_insights(report)
            report["insights"] = insights
            
            logger.info("✅ Analytics report generated successfully")
            return report
            
        except Exception as e:
            logger.error(f"❌ Failed to generate analytics report: {e}")
            return {'error': str(e)}
    
    async def _generate_insights(self, report: Dict[str, Any]) -> List[Dict[str, str]]:
        """إنشاء رؤى وتوصيات"""
        insights = []
        
        try:
            # Delivery rate insights
            if 'delivery_analytics' in report:
                delivery_rate = report['delivery_analytics']['overall_metrics']['delivery_rate']
                
                if delivery_rate < 85:
                    insights.append({
                        "type": "warning",
                        "category": "delivery",
                        "message": f"معدل التسليم منخفض ({delivery_rate}%). يُنصح بمراجعة إعدادات القنوات وجودة قوائم المستقبلين."
                    })
                elif delivery_rate > 95:
                    insights.append({
                        "type": "success",
                        "category": "delivery",
                        "message": f"معدل تسليم ممتاز ({delivery_rate}%). استمر في الحفاظ على هذا الأداء."
                    })
            
            # Channel performance insights
            if 'channel_performance' in report:
                channels = report['channel_performance']['channel_comparison']
                best_channel = max(channels, key=lambda x: x['delivery_rate'])
                worst_channel = min(channels, key=lambda x: x['delivery_rate'])
                
                insights.append({
                    "type": "info",
                    "category": "channels",
                    "message": f"أفضل قناة أداءً: {best_channel['channel']} ({best_channel['delivery_rate']}% تسليم)"
                })
                
                if worst_channel['delivery_rate'] < 80:
                    insights.append({
                        "type": "warning",
                        "category": "channels",
                        "message": f"قناة {worst_channel['channel']} تحتاج تحسين (معدل تسليم {worst_channel['delivery_rate']}%)"
                    })
            
            # User engagement insights
            if 'user_engagement' in report:
                open_rate = report['user_engagement']['overall_metrics']['overall_open_rate']
                
                if open_rate < 20:
                    insights.append({
                        "type": "warning",
                        "category": "engagement",
                        "message": f"معدل فتح الإشعارات منخفض ({open_rate}%). يُنصح بتحسين عناوين الإشعارات ومحتواها."
                    })
                elif open_rate > 40:
                    insights.append({
                        "type": "success",
                        "category": "engagement",
                        "message": f"معدل فتح ممتاز ({open_rate}%). المحتوى يلقى تفاعلاً جيداً من المستخدمين."
                    })
            
            # Trend insights
            if 'trends' in report:
                overall_trend = report['trends']['overall_trend']
                
                if overall_trend > 10:
                    insights.append({
                        "type": "success",
                        "category": "growth",
                        "message": f"نمو إيجابي في الإشعارات ({overall_trend}%). استمر في الاستراتيجية الحالية."
                    })
                elif overall_trend < -10:
                    insights.append({
                        "type": "warning",
                        "category": "growth",
                        "message": f"انخفاض في الإشعارات ({overall_trend}%). قد تحتاج لمراجعة استراتيجية التواصل."
                    })
            
        except Exception as e:
            logger.error(f"❌ Failed to generate insights: {e}")
        
        return insights
    
    # Helper methods
    def _is_cached(self, cache_key: str) -> bool:
        """فحص إذا كانت البيانات محفوظة في الذاكرة المؤقتة"""
        if cache_key not in self.analytics_cache:
            return False
        
        cached_data = self.analytics_cache[cache_key]
        age = (datetime.now() - cached_data['timestamp']).seconds
        
        return age < self.cache_duration
    
    def _cache_analytics(self, cache_key: str, data: Any):
        """حفظ البيانات في الذاكرة المؤقتة"""
        self.analytics_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now()
        }
    
    async def get_analytics_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات خدمة التحليلات"""
        return {
            "cache_size": len(self.analytics_cache),
            "cache_hit_rate": 0,  # Would need to track hits/misses
            "query_performance": self.query_performance
        }
