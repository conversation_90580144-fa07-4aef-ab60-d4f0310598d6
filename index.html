<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WS Transfir - XAMPP System</title>
    <meta name="description" content="نظام التحويلات المالية WS Transfir على منصة XAMPP">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔥</text></svg>">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #ff6b35;
            --secondary-color: #f7931e;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
            --xampp-orange: #fb7a24;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #fb7a24 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            border: 2px solid rgba(255, 107, 53, 0.2);
        }
        
        .header h1 {
            font-size: 2.5em;
            color: var(--xampp-orange);
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }
        
        .xampp-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: var(--xampp-orange);
            color: white;
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1em;
            animation: glow 2s infinite alternate;
        }
        
        @keyframes glow {
            from { box-shadow: 0 0 10px rgba(251, 122, 36, 0.5); }
            to { box-shadow: 0 0 20px rgba(251, 122, 36, 0.8); }
        }
        
        .main-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(255, 107, 53, 0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
            border-color: var(--xampp-orange);
        }
        
        .card h2 {
            color: var(--xampp-orange);
            margin-bottom: 20px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, var(--xampp-orange) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin: 8px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(251, 122, 36, 0.3);
        }
        
        .btn.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
        }
        
        .btn.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #fd7e14 100%);
        }
        
        .system-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-item {
            background: #fff3e0;
            padding: 15px;
            border-radius: 12px;
            border-left: 4px solid var(--xampp-orange);
        }
        
        .info-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .xampp-features {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid rgba(255, 107, 53, 0.1);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: #fff3e0;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(251, 122, 36, 0.2);
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            border: 2px solid rgba(255, 107, 53, 0.1);
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .footer-link {
            color: var(--xampp-orange);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        
        .footer-link:hover {
            color: var(--secondary-color);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .card {
                padding: 20px;
            }
            
            .system-info {
                grid-template-columns: 1fr;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 WS Transfir على XAMPP</h1>
            <p>نظام التحويلات المالية المتقدم على منصة XAMPP</p>
            <div class="xampp-badge">
                <span>🔥</span>
                <span>يعمل على XAMPP</span>
            </div>
        </div>

        <div class="main-content">
            <!-- System Status Card -->
            <div class="card">
                <h2><span>📊</span>حالة النظام</h2>
                <div class="system-info">
                    <div class="info-item">
                        <div class="info-label">حالة الخادم</div>
                        <div class="info-value">🟢 يعمل</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">المنصة</div>
                        <div class="info-value">XAMPP</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">المنفذ</div>
                        <div class="info-value">8080</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الحالة</div>
                        <div class="info-value">متاح أونلاين</div>
                    </div>
                </div>
                <a href="/api/health" class="btn success">
                    <span>🏥</span>
                    فحص صحة النظام
                </a>
            </div>

            <!-- Quick Access Card -->
            <div class="card">
                <h2><span>🚀</span>الوصول السريع</h2>
                <a href="/api/auth/login" class="btn">
                    <span>🔐</span>
                    نظام المصادقة
                </a>
                <a href="/api/transfers" class="btn">
                    <span>💸</span>
                    إدارة التحويلات
                </a>
                <a href="/api/profile/me" class="btn">
                    <span>👤</span>
                    الملف الشخصي
                </a>
                <a href="/api/status" class="btn warning">
                    <span>📈</span>
                    حالة النظام
                </a>
            </div>

            <!-- Login Info Card -->
            <div class="card">
                <h2><span>🔐</span>بيانات الدخول</h2>
                <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 15px 0;">
                    <h4>👨‍💼 مدير النظام:</h4>
                    <p><strong>📧 البريد:</strong> <EMAIL></p>
                    <p><strong>🔑 كلمة المرور:</strong> admin123</p>
                </div>
                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin: 15px 0;">
                    <h4>👤 مستخدم عادي:</h4>
                    <p><strong>📧 البريد:</strong> <EMAIL></p>
                    <p><strong>🔑 كلمة المرور:</strong> password123</p>
                </div>
            </div>
        </div>

        <!-- XAMPP Features -->
        <div class="xampp-features">
            <h2 style="text-align: center; color: var(--xampp-orange); margin-bottom: 30px;">
                <span>🔥</span> ميزات النظام على XAMPP
            </h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">🌐</div>
                    <h3>تكامل XAMPP</h3>
                    <p>متوافق بالكامل مع بيئة XAMPP</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔐</div>
                    <h3>أمان متقدم</h3>
                    <p>نظام مصادقة وحماية شامل</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💸</div>
                    <h3>إدارة التحويلات</h3>
                    <p>نظام متكامل لإدارة التحويلات المالية</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <h3>تقارير وإحصائيات</h3>
                    <p>تقارير مفصلة وإحصائيات شاملة</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <h3>واجهة متجاوبة</h3>
                    <p>تصميم متجاوب لجميع الأجهزة</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <h3>أداء عالي</h3>
                    <p>محسن للسرعة والكفاءة</p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <h3>🔥 WS Transfir XAMPP System</h3>
            <p>نظام التحويلات المالية المتقدم على منصة XAMPP</p>
            <div class="footer-links">
                <a href="/api/health" class="footer-link">📊 فحص الصحة</a>
                <a href="/api/status" class="footer-link">📈 حالة النظام</a>
                <a href="mailto:<EMAIL>" class="footer-link">📧 الدعم الفني</a>
                <a href="https://wstransfir.com" target="_blank" class="footer-link">🌐 الموقع الرسمي</a>
            </div>
            <p style="margin-top: 20px; color: #666; font-size: 0.9em;">
                © 2024 WS Transfir. جميع الحقوق محفوظة. | مدعوم بـ XAMPP
            </p>
        </div>
    </div>

    <script>
        // Auto-refresh system status
        function updateSystemStatus() {
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('System Status:', data.status);
                })
                .catch(error => {
                    console.log('System check failed:', error);
                });
        }

        // Update status every 30 seconds
        setInterval(updateSystemStatus, 30000);

        // Initial status check
        updateSystemStatus();

        // Add click animations
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Welcome message
        console.log('🔥 WS Transfir XAMPP System loaded successfully!');
        console.log('🌐 API Health: /api/health');
        console.log('📊 System Status: /api/status');
        console.log('🔐 Authentication: /api/auth/login');
    </script>
</body>
</html>
