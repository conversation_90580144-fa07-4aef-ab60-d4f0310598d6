{"version": 3, "names": ["_objectWithoutPropertiesLoose", "require", "_objectWithoutProperties", "source", "excluded", "target", "objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "prototype", "propertyIsEnumerable", "call"], "sources": ["../../src/helpers/objectWithoutProperties.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.ts\";\n\nexport default function _objectWithoutProperties(\n  source: null | undefined,\n  excluded: PropertyKey[],\n): Record<string, never>;\nexport default function _objectWithoutProperties<\n  T extends object,\n  K extends PropertyKey[],\n>(\n  source: T | null | undefined,\n  excluded: K,\n): Pick<T, Exclude<keyof T, K[number]>>;\nexport default function _objectWithoutProperties<\n  T extends object,\n  K extends PropertyKey[],\n>(\n  source: T | null | undefined,\n  excluded: K,\n): Pick<T, Exclude<keyof T, K[number]>> | Record<string, never> {\n  if (source == null) return {};\n\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i] as keyof typeof source & keyof typeof target;\n      if (excluded.indexOf(key) !== -1) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,6BAAA,GAAAC,OAAA;AAae,SAASC,wBAAwBA,CAI9CC,MAA4B,EAC5BC,QAAW,EACmD;EAC9D,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAE7B,IAAIE,MAAM,GAAG,IAAAC,qCAA4B,EAACH,MAAM,EAAEC,QAAQ,CAAC;EAC3D,IAAIG,GAAG,EAAEC,CAAC;EAEV,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAC3D,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAC5CD,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAA8C;MACtE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAClC,IAAI,CAACE,MAAM,CAACK,SAAS,CAACC,oBAAoB,CAACC,IAAI,CAACb,MAAM,EAAEI,GAAG,CAAC,EAAE;MAC9DF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAC3B;EACF;EAEA,OAAOF,MAAM;AACf", "ignoreList": []}