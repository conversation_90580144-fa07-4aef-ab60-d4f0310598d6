import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { NotificationsService } from '../services/notifications.service';
import { CreateNotificationDto } from '../dto/create-notification.dto';
import { Notification, NotificationStatus, NotificationType } from '../entities/notification.entity';
import { JwtAuthGuard } from '../../../common/guards/auth.guard';
import { RolesGuard } from '../../../common/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { GetUser } from '../../../common/decorators/get-user.decorator';

@ApiTags('Notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin', 'system')
  @ApiOperation({
    summary: 'إنشاء إشعار جديد',
    description: 'إنشاء إشعار جديد وإرساله للمستخدم',
  })
  @ApiResponse({
    status: 201,
    description: 'تم إنشاء الإشعار بنجاح',
    type: Notification,
  })
  @ApiResponse({
    status: 400,
    description: 'بيانات غير صالحة',
  })
  async create(@Body() createNotificationDto: CreateNotificationDto): Promise<Notification> {
    return this.notificationsService.create(createNotificationDto);
  }

  @Get('my')
  @ApiOperation({
    summary: 'الحصول على إشعارات المستخدم',
    description: 'استرجاع قائمة إشعارات المستخدم الحالي',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'رقم الصفحة',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'عدد العناصر في الصفحة',
    example: 20,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: NotificationStatus,
    description: 'تصفية حسب الحالة',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: NotificationType,
    description: 'تصفية حسب النوع',
  })
  @ApiQuery({
    name: 'unreadOnly',
    required: false,
    description: 'عرض غير المقروءة فقط',
    example: false,
  })
  @ApiResponse({
    status: 200,
    description: 'قائمة الإشعارات',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Notification' },
        },
        total: { type: 'number' },
        unreadCount: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  })
  async getMyNotifications(
    @GetUser() user: any,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('status') status?: NotificationStatus,
    @Query('type') type?: NotificationType,
    @Query('unreadOnly') unreadOnly?: boolean,
  ) {
    return this.notificationsService.getUserNotifications(user.sub, {
      page,
      limit,
      status,
      type,
      unreadOnly,
    });
  }

  @Get('my/unread-count')
  @ApiOperation({
    summary: 'عدد الإشعارات غير المقروءة',
    description: 'الحصول على عدد الإشعارات غير المقروءة للمستخدم',
  })
  @ApiResponse({
    status: 200,
    description: 'عدد الإشعارات غير المقروءة',
    schema: {
      type: 'object',
      properties: {
        unreadCount: { type: 'number', example: 5 },
        byType: {
          type: 'object',
          properties: {
            email: { type: 'number' },
            sms: { type: 'number' },
            push: { type: 'number' },
            in_app: { type: 'number' },
          },
        },
      },
    },
  })
  async getUnreadCount(@GetUser() user: any) {
    return this.notificationsService.getUnreadCount(user.sub);
  }

  @Patch('my/:id/read')
  @ApiOperation({
    summary: 'تحديد إشعار كمقروء',
    description: 'تحديد إشعار محدد كمقروء',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف الإشعار',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تحديد الإشعار كمقروء',
    type: Notification,
  })
  async markAsRead(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: any,
  ): Promise<Notification> {
    return this.notificationsService.markAsRead(id, user.sub);
  }

  @Patch('my/read-all')
  @ApiOperation({
    summary: 'تحديد جميع الإشعارات كمقروءة',
    description: 'تحديد جميع إشعارات المستخدم كمقروءة',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تحديد جميع الإشعارات كمقروءة',
    schema: {
      type: 'object',
      properties: {
        updatedCount: { type: 'number', example: 10 },
      },
    },
  })
  async markAllAsRead(@GetUser() user: any) {
    return this.notificationsService.markAllAsRead(user.sub);
  }

  @Delete('my/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'حذف إشعار',
    description: 'حذف إشعار محدد (أرشفة)',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف الإشعار',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 204,
    description: 'تم حذف الإشعار بنجاح',
  })
  async deleteNotification(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: any,
  ): Promise<void> {
    return this.notificationsService.archiveNotification(id, user.sub);
  }

  @Get('stats')
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({
    summary: 'إحصائيات الإشعارات',
    description: 'الحصول على إحصائيات الإشعارات (للمديرين فقط)',
  })
  @ApiResponse({
    status: 200,
    description: 'إحصائيات الإشعارات',
    schema: {
      type: 'object',
      properties: {
        totalNotifications: { type: 'number' },
        sentNotifications: { type: 'number' },
        failedNotifications: { type: 'number' },
        deliveryRate: { type: 'number' },
        byType: { type: 'object' },
        byStatus: { type: 'object' },
      },
    },
  })
  async getStats() {
    return this.notificationsService.getNotificationStats();
  }

  @Get('failed')
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({
    summary: 'الإشعارات الفاشلة',
    description: 'الحصول على قائمة الإشعارات الفاشلة (للمديرين فقط)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'عدد النتائج',
    example: 50,
  })
  @ApiResponse({
    status: 200,
    description: 'قائمة الإشعارات الفاشلة',
    type: [Notification],
  })
  async getFailedNotifications(@Query('limit') limit: number = 50) {
    return this.notificationsService.getFailedNotifications(limit);
  }

  @Post(':id/retry')
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({
    summary: 'إعادة محاولة إرسال إشعار',
    description: 'إعادة محاولة إرسال إشعار فاشل (للمديرين فقط)',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف الإشعار',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تم إعادة جدولة الإشعار للإرسال',
    type: Notification,
  })
  async retryNotification(@Param('id', ParseUUIDPipe) id: string): Promise<Notification> {
    return this.notificationsService.retryNotification(id);
  }

  @Get(':id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({
    summary: 'الحصول على إشعار محدد',
    description: 'استرجاع تفاصيل إشعار محدد (للمديرين فقط)',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف الإشعار',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تفاصيل الإشعار',
    type: Notification,
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Notification> {
    return this.notificationsService.findById(id);
  }

  @Post('bulk')
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({
    summary: 'إرسال إشعارات جماعية',
    description: 'إرسال إشعار لمجموعة من المستخدمين (للمديرين فقط)',
  })
  @ApiResponse({
    status: 201,
    description: 'تم إنشاء الإشعارات الجماعية بنجاح',
    schema: {
      type: 'object',
      properties: {
        createdCount: { type: 'number', example: 100 },
        notifications: {
          type: 'array',
          items: { $ref: '#/components/schemas/Notification' },
        },
      },
    },
  })
  async createBulkNotifications(
    @Body() bulkNotificationDto: {
      userIds: string[];
      notification: Omit<CreateNotificationDto, 'userId'>;
    },
  ) {
    return this.notificationsService.createBulkNotifications(
      bulkNotificationDto.userIds,
      bulkNotificationDto.notification,
    );
  }
}
