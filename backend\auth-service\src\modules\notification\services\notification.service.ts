import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(private readonly configService: ConfigService) {}

  async sendEmailVerification(email: string, token: string): Promise<void> {
    // Mock implementation - in production, integrate with email service
    const verificationUrl = `${this.configService.get('APP_URL')}/verify-email?token=${token}`;
    
    this.logger.log(`📧 Email verification sent to: ${email}`);
    this.logger.log(`🔗 Verification URL: ${verificationUrl}`);
    
    // Simulate email sending
    console.log(`
    ===============================
    📧 Email Verification
    ===============================
    To: ${email}
    Subject: تفعيل حسابك في WS Transfir
    
    مرحباً،
    
    يرجى النقر على الرابط التالي لتفعيل حسابك:
    ${verificationUrl}
    
    هذا الرابط صالح لمدة 24 ساعة.
    
    مع تحيات فريق WS Transfir
    ===============================
    `);
  }

  async sendPhoneVerification(phone: string, code: string): Promise<void> {
    // Mock implementation - in production, integrate with SMS service
    this.logger.log(`📱 SMS verification sent to: ${phone}`);
    this.logger.log(`🔢 Verification code: ${code}`);
    
    // Simulate SMS sending
    console.log(`
    ===============================
    📱 SMS Verification
    ===============================
    To: ${phone}
    Message: رمز التفعيل الخاص بك في WS Transfir هو: ${code}
    
    هذا الرمز صالح لمدة 10 دقائق.
    ===============================
    `);
  }

  async sendPasswordReset(email: string, token: string): Promise<void> {
    const resetUrl = `${this.configService.get('APP_URL')}/reset-password?token=${token}`;
    
    this.logger.log(`🔐 Password reset sent to: ${email}`);
    
    console.log(`
    ===============================
    🔐 Password Reset
    ===============================
    To: ${email}
    Subject: إعادة تعيين كلمة المرور
    
    تم طلب إعادة تعيين كلمة المرور لحسابك.
    
    يرجى النقر على الرابط التالي:
    ${resetUrl}
    
    هذا الرابط صالح لمدة ساعة واحدة.
    
    إذا لم تطلب هذا، يرجى تجاهل هذه الرسالة.
    ===============================
    `);
  }

  async sendWelcomeEmail(email: string, firstName: string): Promise<void> {
    this.logger.log(`🎉 Welcome email sent to: ${email}`);
    
    console.log(`
    ===============================
    🎉 Welcome Email
    ===============================
    To: ${email}
    Subject: مرحباً بك في WS Transfir
    
    مرحباً ${firstName}،
    
    نرحب بك في WS Transfir - نظام تحويل الأموال المتقدم.
    
    يمكنك الآن:
    ✅ إرسال واستقبال الأموال
    ✅ إدارة محفظتك الرقمية
    ✅ تتبع معاملاتك
    
    مع تحيات فريق WS Transfir
    ===============================
    `);
  }
}
