{"version": 3, "file": "IsNegative.js", "sourceRoot": "", "sources": ["../../../../src/decorator/number/IsNegative.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,WAAW,GAAG,YAAY,CAAC;AAExC;;GAEG;AACH,SAAgB,UAAU,CAAC,KAAc;IACvC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC;AAChD,CAAC;AAFD,gCAEC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,iBAAqC;IAC9D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,mBAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;YACrD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,qCAAqC,EAChE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,gCAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_NEGATIVE = 'isNegative';\n\n/**\n * Checks if the value is a negative number smaller than zero.\n */\nexport function isNegative(value: unknown): boolean {\n  return typeof value === 'number' && value < 0;\n}\n\n/**\n * Checks if the value is a negative number smaller than zero.\n */\nexport function IsNegative(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_NEGATIVE,\n      validator: {\n        validate: (value, args): boolean => isNegative(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a negative number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}