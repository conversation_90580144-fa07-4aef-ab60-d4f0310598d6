{"version": 3, "file": "BuiltInDocNodes.js", "sourceRoot": "", "sources": ["../../src/nodes/BuiltInDocNodes.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;AAI3D,qCAAwC;AACxC,wCAA4B;AAE5B;IAAA;IAoDA,CAAC;IAnDe,wBAAQ,GAAtB,UAAuB,aAAiC;QACtD,IAAM,cAAc,GAAmB,aAAa,CAAC,cAAc,CAAC;QAEpE,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,EAAE;YAClD,EAAE,WAAW,EAAE,qBAAW,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,CAAC,QAAQ,EAAE;YAC/D,EAAE,WAAW,EAAE,qBAAW,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE;YACrE,EAAE,WAAW,EAAE,qBAAW,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE;YACrE,EAAE,WAAW,EAAE,qBAAW,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,CAAC,UAAU,EAAE;YACnE,EAAE,WAAW,EAAE,qBAAW,CAAC,oBAAoB,EAAE,WAAW,EAAE,KAAK,CAAC,uBAAuB,EAAE;YAC7F,EAAE,WAAW,EAAE,qBAAW,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,YAAY,EAAE;YACvE,EAAE,WAAW,EAAE,qBAAW,CAAC,WAAW,EAAE,WAAW,EAAE,KAAK,CAAC,cAAc,EAAE;YAC3E,EAAE,WAAW,EAAE,qBAAW,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,CAAC,UAAU,EAAE;YACnE,EAAE,WAAW,EAAE,qBAAW,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC,aAAa,EAAE;YACzE,EAAE,WAAW,EAAE,qBAAW,CAAC,aAAa,EAAE,WAAW,EAAE,KAAK,CAAC,gBAAgB,EAAE;YAC/E,EAAE,WAAW,EAAE,qBAAW,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC,aAAa,EAAE;YACzE,EAAE,WAAW,EAAE,qBAAW,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC,eAAe,EAAE;YAC7E,EAAE,WAAW,EAAE,qBAAW,CAAC,aAAa,EAAE,WAAW,EAAE,KAAK,CAAC,gBAAgB,EAAE;YAC/E,EAAE,WAAW,EAAE,qBAAW,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,YAAY,EAAE;YACvE,EAAE,WAAW,EAAE,qBAAW,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,CAAC,UAAU,EAAE;YACnE,EAAE,WAAW,EAAE,qBAAW,CAAC,gBAAgB,EAAE,WAAW,EAAE,KAAK,CAAC,mBAAmB,EAAE;YACrF,EAAE,WAAW,EAAE,qBAAW,CAAC,eAAe,EAAE,WAAW,EAAE,KAAK,CAAC,kBAAkB,EAAE;YACnF,EAAE,WAAW,EAAE,qBAAW,CAAC,cAAc,EAAE,WAAW,EAAE,KAAK,CAAC,iBAAiB,EAAE;YACjF,EAAE,WAAW,EAAE,qBAAW,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC,eAAe,EAAE;YAC7E,EAAE,WAAW,EAAE,qBAAW,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,YAAY,EAAE;YACvE,EAAE,WAAW,EAAE,qBAAW,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,CAAC,aAAa,EAAE;YACzE,EAAE,WAAW,EAAE,qBAAW,CAAC,eAAe,EAAE,WAAW,EAAE,KAAK,CAAC,kBAAkB,EAAE;YACnF,EAAE,WAAW,EAAE,qBAAW,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,YAAY,EAAE;YACvE,EAAE,WAAW,EAAE,qBAAW,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,CAAC,UAAU,EAAE;YACnE,EAAE,WAAW,EAAE,qBAAW,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,YAAY,EAAE;SACxE,CAAC,CAAC;QAEH,cAAc,CAAC,yBAAyB,CAAC,qBAAW,CAAC,OAAO,EAAE;YAC5D,qBAAW,CAAC,UAAU;YACtB,qBAAW,CAAC,SAAS;YACrB,qBAAW,CAAC,YAAY;YACxB,qBAAW,CAAC,UAAU;SACvB,CAAC,CAAC;QAEH,cAAc,CAAC,yBAAyB,CAAC,qBAAW,CAAC,SAAS,EAAE;YAC9D,qBAAW,CAAC,QAAQ;YACpB,qBAAW,CAAC,QAAQ;YACpB,qBAAW,CAAC,SAAS;YACrB,qBAAW,CAAC,WAAW;YACvB,qBAAW,CAAC,YAAY;YACxB,qBAAW,CAAC,UAAU;YACtB,qBAAW,CAAC,SAAS;YACrB,qBAAW,CAAC,OAAO;YACnB,qBAAW,CAAC,SAAS;YACrB,qBAAW,CAAC,SAAS;SACtB,CAAC,CAAC;IACL,CAAC;IACH,sBAAC;AAAD,CAAC,AApDD,IAoDC;AApDY,0CAAe", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport type { TSDocConfiguration } from '../configuration/TSDocConfiguration';\r\nimport type { DocNodeManager } from '../configuration/DocNodeManager';\r\nimport { DocNodeKind } from './DocNode';\r\nimport * as nodes from '..';\r\n\r\nexport class BuiltInDocNodes {\r\n  public static register(configuration: TSDocConfiguration): void {\r\n    const docNodeManager: DocNodeManager = configuration.docNodeManager;\r\n\r\n    docNodeManager.registerDocNodes('@microsoft/tsdoc', [\r\n      { docNodeKind: DocNodeKind.Block, constructor: nodes.DocBlock },\r\n      { docNodeKind: DocNodeKind.BlockTag, constructor: nodes.DocBlockTag },\r\n      { docNodeKind: DocNodeKind.CodeSpan, constructor: nodes.DocCodeSpan },\r\n      { docNodeKind: DocNodeKind.Comment, constructor: nodes.DocComment },\r\n      { docNodeKind: DocNodeKind.DeclarationReference, constructor: nodes.DocDeclarationReference },\r\n      { docNodeKind: DocNodeKind.ErrorText, constructor: nodes.DocErrorText },\r\n      { docNodeKind: DocNodeKind.EscapedText, constructor: nodes.DocEscapedText },\r\n      { docNodeKind: DocNodeKind.Excerpt, constructor: nodes.DocExcerpt },\r\n      { docNodeKind: DocNodeKind.FencedCode, constructor: nodes.DocFencedCode },\r\n      { docNodeKind: DocNodeKind.HtmlAttribute, constructor: nodes.DocHtmlAttribute },\r\n      { docNodeKind: DocNodeKind.HtmlEndTag, constructor: nodes.DocHtmlEndTag },\r\n      { docNodeKind: DocNodeKind.HtmlStartTag, constructor: nodes.DocHtmlStartTag },\r\n      { docNodeKind: DocNodeKind.InheritDocTag, constructor: nodes.DocInheritDocTag },\r\n      { docNodeKind: DocNodeKind.InlineTag, constructor: nodes.DocInlineTag },\r\n      { docNodeKind: DocNodeKind.LinkTag, constructor: nodes.DocLinkTag },\r\n      { docNodeKind: DocNodeKind.MemberIdentifier, constructor: nodes.DocMemberIdentifier },\r\n      { docNodeKind: DocNodeKind.MemberReference, constructor: nodes.DocMemberReference },\r\n      { docNodeKind: DocNodeKind.MemberSelector, constructor: nodes.DocMemberSelector },\r\n      { docNodeKind: DocNodeKind.MemberSymbol, constructor: nodes.DocMemberSymbol },\r\n      { docNodeKind: DocNodeKind.Paragraph, constructor: nodes.DocParagraph },\r\n      { docNodeKind: DocNodeKind.ParamBlock, constructor: nodes.DocParamBlock },\r\n      { docNodeKind: DocNodeKind.ParamCollection, constructor: nodes.DocParamCollection },\r\n      { docNodeKind: DocNodeKind.PlainText, constructor: nodes.DocPlainText },\r\n      { docNodeKind: DocNodeKind.Section, constructor: nodes.DocSection },\r\n      { docNodeKind: DocNodeKind.SoftBreak, constructor: nodes.DocSoftBreak }\r\n    ]);\r\n\r\n    docNodeManager.registerAllowableChildren(DocNodeKind.Section, [\r\n      DocNodeKind.FencedCode,\r\n      DocNodeKind.Paragraph,\r\n      DocNodeKind.HtmlStartTag,\r\n      DocNodeKind.HtmlEndTag\r\n    ]);\r\n\r\n    docNodeManager.registerAllowableChildren(DocNodeKind.Paragraph, [\r\n      DocNodeKind.BlockTag,\r\n      DocNodeKind.CodeSpan,\r\n      DocNodeKind.ErrorText,\r\n      DocNodeKind.EscapedText,\r\n      DocNodeKind.HtmlStartTag,\r\n      DocNodeKind.HtmlEndTag,\r\n      DocNodeKind.InlineTag,\r\n      DocNodeKind.LinkTag,\r\n      DocNodeKind.PlainText,\r\n      DocNodeKind.SoftBreak\r\n    ]);\r\n  }\r\n}\r\n"]}