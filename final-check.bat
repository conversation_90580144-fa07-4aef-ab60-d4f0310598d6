@echo off

echo ========================================
echo WS Transfir - Final System Check
echo ========================================
echo.

echo Checking Node.js processes...
tasklist /FI "IMAGENAME eq node.exe" /FO TABLE 2>nul | findstr node.exe
if errorlevel 1 (
    echo ❌ No Node.js processes running
    echo.
    echo Starting system...
    start "WS Transfir System" cmd /k "node start-simple.js"
    timeout /t 5 /nobreak >nul
) else (
    echo ✅ Node.js processes found
)

echo.
echo Checking ports...

netstat -an | findstr :3000 >nul 2>&1
if errorlevel 1 (
    echo ❌ Port 3000 API: NOT RUNNING
) else (
    echo ✅ Port 3000 API: RUNNING
)

netstat -an | findstr :3100 >nul 2>&1
if errorlevel 1 (
    echo ❌ Port 3100 Frontend: NOT RUNNING
) else (
    echo ✅ Port 3100 Frontend: RUNNING
)

echo.
echo Testing API endpoints...

curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo ❌ API Health: NOT RESPONDING
) else (
    echo ✅ API Health: RESPONDING
)

curl -s http://localhost:3100 >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend: NOT RESPONDING
) else (
    echo ✅ Frontend: RESPONDING
)

echo.
echo ========================================
echo System Status Summary
echo ========================================

:: Count working services
set /a working=0

netstat -an | findstr :3000 >nul 2>&1
if not errorlevel 1 set /a working+=1

netstat -an | findstr :3100 >nul 2>&1
if not errorlevel 1 set /a working+=1

if %working%==2 (
    echo 🟢 SYSTEM STATUS: FULLY OPERATIONAL ^(%working%/2^)
    echo.
    echo ✅ All services are running successfully!
    echo.
    echo 🌐 Access URLs:
    echo ================
    echo Frontend: http://localhost:3100
    echo API: http://localhost:3000
    echo Health: http://localhost:3000/api/health
    echo.
    echo 🔐 Test Credentials:
    echo ===================
    echo Admin: <EMAIL> / admin123
    echo User: <EMAIL> / password123
    echo.
    echo 📱 Available Features:
    echo =====================
    echo ✅ User Authentication
    echo ✅ Profile Management
    echo ✅ Transfer Viewing
    echo ✅ API Testing Interface
    echo ✅ Real-time Health Monitoring
    echo.
    echo 🎉 System is ready for use and demonstration!
    echo.
    echo Opening browser...
    start "" "http://localhost:3100"
) else if %working%==1 (
    echo 🟡 SYSTEM STATUS: PARTIALLY RUNNING ^(%working%/2^)
    echo ⚠️ Some services are not responding
    echo.
    echo 🔧 Troubleshooting:
    echo ===================
    echo 1. Run: node start-simple.js
    echo 2. Wait 10 seconds
    echo 3. Run this check again
) else (
    echo 🔴 SYSTEM STATUS: NOT RUNNING ^(%working%/2^)
    echo.
    echo 🚀 To start the system:
    echo ======================
    echo node start-simple.js
    echo.
    echo Or run: guaranteed-start.bat
)

echo.
echo 🔧 Available Commands:
echo =====================
echo final-check.bat        - Run this system check
echo node start-simple.js   - Start the complete system
echo guaranteed-start.bat   - Alternative startup method
echo.

pause
