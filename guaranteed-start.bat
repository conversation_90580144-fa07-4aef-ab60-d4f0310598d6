@echo off
title WS Transfir - Guaranteed Start

echo ========================================
echo WS Transfir - Guaranteed System Start
echo ========================================
echo.

:: Kill any existing Node processes
echo Stopping any existing processes...
taskkill /F /IM node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

:: Check Node.js
echo Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from: https://nodejs.org
    pause
    exit /b 1
)
echo Node.js is available

:: Install dependencies if needed
if not exist "node_modules" (
    echo Installing root dependencies...
    npm install express cors >nul 2>&1
)

:: Create guaranteed API server
echo Creating API server...
(
echo const express = require('express'^);
echo const cors = require('cors'^);
echo const app = express(^);
echo.
echo app.use(cors(^)^);
echo app.use(express.json(^)^);
echo.
echo // Health endpoint
echo app.get('/api/health', (req, res^) =^> {
echo   res.json({
echo     status: 'OK',
echo     message: 'WS Transfir API is running',
echo     timestamp: new Date(^).toISOString(^),
echo     port: 3000
echo   }^);
echo }^);
echo.
echo // Auth endpoints
echo app.post('/api/auth/login', (req, res^) =^> {
echo   const { email, password } = req.body;
echo   console.log('Login attempt:', email^);
echo   
echo   if (email === '<EMAIL>' ^&^& password === 'admin123'^) {
echo     res.json({
echo       success: true,
echo       token: 'admin-token-123',
echo       user: {
echo         id: '1',
echo         firstName: 'مدير',
echo         lastName: 'النظام',
echo         email: email,
echo         role: 'admin'
echo       }
echo     }^);
echo   } else if (email ^&^& password^) {
echo     res.json({
echo       success: true,
echo       token: 'user-token-456',
echo       user: {
echo         id: '2',
echo         firstName: 'أحمد',
echo         lastName: 'محمد',
echo         email: email,
echo         role: 'user'
echo       }
echo     }^);
echo   } else {
echo     res.status(401^).json({
echo       success: false,
echo       message: 'بيانات الدخول غير صحيحة'
echo     }^);
echo   }
echo }^);
echo.
echo app.post('/api/auth/register', (req, res^) =^> {
echo   res.json({
echo     success: true,
echo     message: 'تم إنشاء الحساب بنجاح'
echo   }^);
echo }^);
echo.
echo // Profile endpoint
echo app.get('/api/profile/me', (req, res^) =^> {
echo   res.json({
echo     id: '1',
echo     firstName: 'أحمد',
echo     lastName: 'محمد',
echo     email: '<EMAIL>',
echo     phone: '+966501234567',
echo     isVerified: true,
echo     completionPercentage: 85
echo   }^);
echo }^);
echo.
echo // Transfers endpoint
echo app.get('/api/transfers', (req, res^) =^> {
echo   res.json({
echo     data: [
echo       {
echo         id: '1',
echo         referenceNumber: 'WS20241225001',
echo         amount: '1,500.00',
echo         currency: 'SAR',
echo         receiverName: 'أحمد محمد',
echo         status: 'completed'
echo       },
echo       {
echo         id: '2',
echo         referenceNumber: 'WS20241224002',
echo         amount: '750.00',
echo         currency: 'USD',
echo         receiverName: 'فاطمة علي',
echo         status: 'pending'
echo       }
echo     ],
echo     total: 2
echo   }^);
echo }^);
echo.
echo const PORT = 3000;
echo app.listen(PORT, '0.0.0.0', (^) =^> {
echo   console.log(''^);
echo   console.log('🚀 ================================'^);
echo   console.log('🚀 WS TRANSFIR API SERVER RUNNING'^);
echo   console.log('🚀 ================================'^);
echo   console.log(`🌐 URL: http://localhost:${PORT}`^);
echo   console.log(`📊 Health: http://localhost:${PORT}/api/health`^);
echo   console.log('🔐 Admin: <EMAIL> / admin123'^);
echo   console.log('🚀 ================================'^);
echo   console.log(''^);
echo }^);
) > guaranteed-api.js

echo Starting API server on port 3000...
start "WS Transfir API" cmd /k "node guaranteed-api.js"

:: Wait for API to start
timeout /t 5 /nobreak >nul

:: Test API
echo Testing API connection...
curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo WARNING: API might not be ready yet
) else (
    echo API is responding
)

:: Check if frontend exists
if exist "frontend\web-app" (
    echo Starting frontend...
    cd frontend\web-app
    
    :: Install frontend dependencies if needed
    if not exist "node_modules" (
        echo Installing frontend dependencies...
        npm install >nul 2>&1
    )
    
    :: Create next.config.js if missing
    if not exist "next.config.js" (
        echo Creating Next.js config...
        (
            echo /** @type {import('next'^).NextConfig} */
            echo const nextConfig = {
            echo   reactStrictMode: true,
            echo   async rewrites(^) {
            echo     return [
            echo       {
            echo         source: '/api/:path*',
            echo         destination: 'http://localhost:3000/api/:path*'
            echo       }
            echo     ];
            echo   }
            echo };
            echo module.exports = nextConfig;
        ) > next.config.js
    )
    
    :: Start frontend
    echo Starting frontend on port 3100...
    start "WS Transfir Frontend" cmd /k "npm run dev"
    
    cd ..\..
) else (
    echo Frontend directory not found, creating simple HTML server...
    
    :: Create simple HTML page
    mkdir temp-frontend 2>nul
    (
        echo ^<!DOCTYPE html^>
        echo ^<html lang="ar"^>
        echo ^<head^>
        echo     ^<meta charset="UTF-8"^>
        echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
        echo     ^<title^>WS Transfir^</title^>
        echo     ^<style^>
        echo         body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        echo         .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1^); }
        echo         h1 { color: #2c3e50; text-align: center; }
        echo         .status { background: #27ae60; color: white; padding: 10px; border-radius: 5px; text-align: center; margin: 20px 0; }
        echo         .links { display: grid; gap: 10px; margin: 20px 0; }
        echo         .link { background: #3498db; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center; }
        echo         .link:hover { background: #2980b9; }
        echo         .credentials { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        echo     ^</style^>
        echo ^</head^>
        echo ^<body^>
        echo     ^<div class="container"^>
        echo         ^<h1^>🚀 WS Transfir - نظام التحويلات المالية^</h1^>
        echo         ^<div class="status"^>✅ النظام يعمل بنجاح^</div^>
        echo         
        echo         ^<div class="links"^>
        echo             ^<a href="http://localhost:3000/api/health" class="link"^>📊 فحص صحة API^</a^>
        echo             ^<a href="#" onclick="testLogin(^)" class="link"^>🔐 اختبار تسجيل الدخول^</a^>
        echo             ^<a href="#" onclick="testProfile(^)" class="link"^>👤 اختبار الملف الشخصي^</a^>
        echo             ^<a href="#" onclick="testTransfers(^)" class="link"^>💸 اختبار التحويلات^</a^>
        echo         ^</div^>
        echo         
        echo         ^<div class="credentials"^>
        echo             ^<h3^>🔐 بيانات الدخول التجريبية:^</h3^>
        echo             ^<p^>^<strong^>مدير النظام:^</strong^> <EMAIL> / admin123^</p^>
        echo             ^<p^>^<strong^>مستخدم عادي:^</strong^> <EMAIL> / password123^</p^>
        echo         ^</div^>
        echo         
        echo         ^<div id="result"^>^</div^>
        echo     ^</div^>
        echo     
        echo     ^<script^>
        echo         async function testLogin(^) {
        echo             try {
        echo                 const response = await fetch('http://localhost:3000/api/auth/login', {
        echo                     method: 'POST',
        echo                     headers: { 'Content-Type': 'application/json' },
        echo                     body: JSON.stringify({ email: '<EMAIL>', password: 'admin123' }^)
        echo                 }^);
        echo                 const data = await response.json(^);
        echo                 document.getElementById('result'^).innerHTML = '^<pre^>' + JSON.stringify(data, null, 2^) + '^</pre^>';
        echo             } catch (e^) {
        echo                 document.getElementById('result'^).innerHTML = 'خطأ: ' + e.message;
        echo             }
        echo         }
        echo         
        echo         async function testProfile(^) {
        echo             try {
        echo                 const response = await fetch('http://localhost:3000/api/profile/me'^);
        echo                 const data = await response.json(^);
        echo                 document.getElementById('result'^).innerHTML = '^<pre^>' + JSON.stringify(data, null, 2^) + '^</pre^>';
        echo             } catch (e^) {
        echo                 document.getElementById('result'^).innerHTML = 'خطأ: ' + e.message;
        echo             }
        echo         }
        echo         
        echo         async function testTransfers(^) {
        echo             try {
        echo                 const response = await fetch('http://localhost:3000/api/transfers'^);
        echo                 const data = await response.json(^);
        echo                 document.getElementById('result'^).innerHTML = '^<pre^>' + JSON.stringify(data, null, 2^) + '^</pre^>';
        echo             } catch (e^) {
        echo                 document.getElementById('result'^).innerHTML = 'خطأ: ' + e.message;
        echo             }
        echo         }
        echo     ^</script^>
        echo ^</body^>
        echo ^</html^>
    ) > temp-frontend\index.html
    
    :: Create simple HTTP server for HTML
    (
        echo const http = require('http'^);
        echo const fs = require('fs'^);
        echo const path = require('path'^);
        echo.
        echo const server = http.createServer((req, res^) =^> {
        echo   res.setHeader('Access-Control-Allow-Origin', '*'^);
        echo   res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE'^);
        echo   res.setHeader('Access-Control-Allow-Headers', 'Content-Type'^);
        echo   
        echo   if (req.url === '/' ^|^| req.url === '/index.html'^) {
        echo     const html = fs.readFileSync('temp-frontend/index.html', 'utf8'^);
        echo     res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' }^);
        echo     res.end(html^);
        echo   } else {
        echo     res.writeHead(404^);
        echo     res.end('Not Found'^);
        echo   }
        echo }^);
        echo.
        echo server.listen(3100, (^) =^> {
        echo   console.log('🌐 Frontend server running on http://localhost:3100'^);
        echo }^);
    ) > simple-frontend-server.js
    
    start "WS Transfir Frontend" cmd /k "node simple-frontend-server.js"
)

:: Wait for services to start
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

:: Test both services
echo Testing services...
curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo ❌ API not responding
) else (
    echo ✅ API is working
)

curl -s http://localhost:3100 >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend not responding
) else (
    echo ✅ Frontend is working
)

:: Open browser
echo Opening browser...
start "" "http://localhost:3100"
timeout /t 2 /nobreak >nul
start "" "http://localhost:3000/api/health"

echo.
echo ========================================
echo ✅ WS Transfir System Started!
echo ========================================
echo.
echo 🌐 Frontend: http://localhost:3100
echo 🔧 API: http://localhost:3000
echo 📊 Health: http://localhost:3000/api/health
echo.
echo 🔐 Login credentials:
echo    Admin: <EMAIL> / admin123
echo    User: <EMAIL> / password123
echo.
echo Press any key to continue...
pause >nul
