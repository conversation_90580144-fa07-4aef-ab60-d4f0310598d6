{"name": "@nestjs/passport", "version": "10.0.3", "description": "Nest - modern, fast, powerful node.js web framework (@passport)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"build": "rimraf dist && tsc -p tsconfig.build.json", "format": "prettier --write \"lib/**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "precommit": "lint-staged", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prerelease": "npm run build", "release": "release-it", "test": "jest", "prepare": "husky install"}, "files": ["dist", "index.js", "index.d.ts"], "peerDependencies": {"@nestjs/common": "^8.0.0 || ^9.0.0 || ^10.0.0", "passport": "^0.4.0 || ^0.5.0 || ^0.6.0 || ^0.7.0"}, "devDependencies": {"@commitlint/cli": "18.4.3", "@commitlint/config-angular": "18.4.3", "@nestjs/common": "10.2.10", "@nestjs/core": "10.2.10", "@nestjs/jwt": "10.2.0", "@nestjs/platform-express": "10.2.10", "@nestjs/testing": "10.2.10", "@types/jest": "29.5.10", "@types/node": "20.10.3", "@types/passport": "1.0.16", "@types/passport-jwt": "3.0.13", "@types/passport-local": "1.0.38", "@typescript-eslint/eslint-plugin": "6.13.1", "@typescript-eslint/parser": "6.13.1", "eslint": "8.55.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.0", "husky": "8.0.3", "jest": "29.7.0", "lint-staged": "15.2.0", "pactum": "3.5.1", "passport": "0.7.0", "passport-jwt": "4.0.1", "passport-local": "1.0.0", "prettier": "3.1.0", "reflect-metadata": "0.1.13", "release-it": "17.0.0", "rimraf": "5.0.5", "rxjs": "7.8.1", "ts-jest": "29.1.1", "typescript": "5.3.2"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/passport"}}