# WS Transfir - Makefile for Development and Deployment
# =====================================================

# Variables
DOCKER_COMPOSE = docker-compose
DOCKER_COMPOSE_PROD = docker-compose -f docker-compose.prod.yml
PROJECT_NAME = ws-transfir
NETWORK_NAME = ws-network

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
PURPLE = \033[0;35m
CYAN = \033[0;36m
WHITE = \033[1;37m
NC = \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)WS Transfir - Development Commands$(NC)"
	@echo "=================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Environment Setup
.PHONY: setup
setup: ## Initial project setup
	@echo "$(BLUE)Setting up WS Transfir project...$(NC)"
	@cp .env.example .env
	@echo "$(GREEN)✓ Environment file created$(NC)"
	@mkdir -p backend/logs backend/uploads nginx/logs monitoring/prometheus monitoring/grafana
	@echo "$(GREEN)✓ Directories created$(NC)"
	@echo "$(YELLOW)Please edit .env file with your configuration$(NC)"

# Development Commands
.PHONY: dev
dev: ## Start development environment
	@echo "$(BLUE)Starting development environment...$(NC)"
	@$(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)✓ Development environment started$(NC)"
	@echo "$(CYAN)Services available at:$(NC)"
	@echo "  - Web App: http://localhost:3100"
	@echo "  - API Gateway: http://localhost:3000"
	@echo "  - Grafana: http://localhost:3007"
	@echo "  - RabbitMQ: http://localhost:15672"
	@echo "  - Adminer: http://localhost:8080"

.PHONY: dev-build
dev-build: ## Build and start development environment
	@echo "$(BLUE)Building and starting development environment...$(NC)"
	@$(DOCKER_COMPOSE) up -d --build
	@echo "$(GREEN)✓ Development environment built and started$(NC)"

.PHONY: dev-logs
dev-logs: ## Show development logs
	@$(DOCKER_COMPOSE) logs -f

.PHONY: dev-stop
dev-stop: ## Stop development environment
	@echo "$(YELLOW)Stopping development environment...$(NC)"
	@$(DOCKER_COMPOSE) stop
	@echo "$(GREEN)✓ Development environment stopped$(NC)"

.PHONY: dev-down
dev-down: ## Stop and remove development containers
	@echo "$(RED)Stopping and removing development containers...$(NC)"
	@$(DOCKER_COMPOSE) down
	@echo "$(GREEN)✓ Development containers removed$(NC)"

.PHONY: dev-clean
dev-clean: ## Clean development environment (remove containers, networks, volumes)
	@echo "$(RED)Cleaning development environment...$(NC)"
	@$(DOCKER_COMPOSE) down -v --remove-orphans
	@docker system prune -f
	@echo "$(GREEN)✓ Development environment cleaned$(NC)"

# Production Commands
.PHONY: prod
prod: ## Start production environment
	@echo "$(BLUE)Starting production environment...$(NC)"
	@$(DOCKER_COMPOSE_PROD) up -d
	@echo "$(GREEN)✓ Production environment started$(NC)"

.PHONY: prod-build
prod-build: ## Build and start production environment
	@echo "$(BLUE)Building and starting production environment...$(NC)"
	@$(DOCKER_COMPOSE_PROD) up -d --build
	@echo "$(GREEN)✓ Production environment built and started$(NC)"

.PHONY: prod-logs
prod-logs: ## Show production logs
	@$(DOCKER_COMPOSE_PROD) logs -f

.PHONY: prod-stop
prod-stop: ## Stop production environment
	@echo "$(YELLOW)Stopping production environment...$(NC)"
	@$(DOCKER_COMPOSE_PROD) stop
	@echo "$(GREEN)✓ Production environment stopped$(NC)"

.PHONY: prod-down
prod-down: ## Stop and remove production containers
	@echo "$(RED)Stopping and removing production containers...$(NC)"
	@$(DOCKER_COMPOSE_PROD) down
	@echo "$(GREEN)✓ Production containers removed$(NC)"

# Database Commands
.PHONY: db-migrate
db-migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run migrate
	@echo "$(GREEN)✓ Database migrations completed$(NC)"

.PHONY: db-seed
db-seed: ## Seed database with initial data
	@echo "$(BLUE)Seeding database...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run seed
	@echo "$(GREEN)✓ Database seeded$(NC)"

.PHONY: db-reset
db-reset: ## Reset database (drop and recreate)
	@echo "$(RED)Resetting database...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run db:reset
	@echo "$(GREEN)✓ Database reset$(NC)"

.PHONY: db-backup
db-backup: ## Backup database
	@echo "$(BLUE)Creating database backup...$(NC)"
	@mkdir -p backups
	@docker exec ws-postgres pg_dump -U ws_user ws_transfir > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✓ Database backup created$(NC)"

.PHONY: db-restore
db-restore: ## Restore database from backup (usage: make db-restore BACKUP=backup_file.sql)
	@echo "$(BLUE)Restoring database from backup...$(NC)"
	@docker exec -i ws-postgres psql -U ws_user -d ws_transfir < backups/$(BACKUP)
	@echo "$(GREEN)✓ Database restored$(NC)"

# Testing Commands
.PHONY: test
test: ## Run all tests
	@echo "$(BLUE)Running tests...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm test
	@$(DOCKER_COMPOSE) exec auth-service npm test
	@$(DOCKER_COMPOSE) exec user-service npm test
	@$(DOCKER_COMPOSE) exec transfer-service npm test
	@$(DOCKER_COMPOSE) exec wallet-service npm test
	@echo "$(GREEN)✓ All tests completed$(NC)"

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run test:coverage
	@echo "$(GREEN)✓ Test coverage completed$(NC)"

.PHONY: test-e2e
test-e2e: ## Run end-to-end tests
	@echo "$(BLUE)Running E2E tests...$(NC)"
	@$(DOCKER_COMPOSE) exec web-app npm run test:e2e
	@echo "$(GREEN)✓ E2E tests completed$(NC)"

# Code Quality Commands
.PHONY: lint
lint: ## Run linting
	@echo "$(BLUE)Running linting...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run lint
	@$(DOCKER_COMPOSE) exec web-app npm run lint
	@echo "$(GREEN)✓ Linting completed$(NC)"

.PHONY: format
format: ## Format code
	@echo "$(BLUE)Formatting code...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run format
	@$(DOCKER_COMPOSE) exec web-app npm run format
	@echo "$(GREEN)✓ Code formatting completed$(NC)"

.PHONY: type-check
type-check: ## Run TypeScript type checking
	@echo "$(BLUE)Running type checking...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run type-check
	@$(DOCKER_COMPOSE) exec web-app npm run type-check
	@echo "$(GREEN)✓ Type checking completed$(NC)"

# Monitoring Commands
.PHONY: logs
logs: ## Show logs for all services
	@$(DOCKER_COMPOSE) logs -f

.PHONY: logs-api
logs-api: ## Show API Gateway logs
	@$(DOCKER_COMPOSE) logs -f api-gateway

.PHONY: logs-web
logs-web: ## Show Web App logs
	@$(DOCKER_COMPOSE) logs -f web-app

.PHONY: logs-db
logs-db: ## Show database logs
	@$(DOCKER_COMPOSE) logs -f postgres

.PHONY: status
status: ## Show status of all services
	@echo "$(BLUE)Service Status:$(NC)"
	@$(DOCKER_COMPOSE) ps

.PHONY: health
health: ## Check health of all services
	@echo "$(BLUE)Checking service health...$(NC)"
	@curl -f http://localhost:3000/health || echo "$(RED)API Gateway: DOWN$(NC)"
	@curl -f http://localhost:3100 || echo "$(RED)Web App: DOWN$(NC)"
	@echo "$(GREEN)✓ Health check completed$(NC)"

# Utility Commands
.PHONY: shell-api
shell-api: ## Open shell in API Gateway container
	@$(DOCKER_COMPOSE) exec api-gateway sh

.PHONY: shell-web
shell-web: ## Open shell in Web App container
	@$(DOCKER_COMPOSE) exec web-app sh

.PHONY: shell-db
shell-db: ## Open PostgreSQL shell
	@$(DOCKER_COMPOSE) exec postgres psql -U ws_user -d ws_transfir

.PHONY: shell-redis
shell-redis: ## Open Redis shell
	@$(DOCKER_COMPOSE) exec redis redis-cli

.PHONY: shell-mongo
shell-mongo: ## Open MongoDB shell
	@$(DOCKER_COMPOSE) exec mongodb mongosh

# Deployment Commands
.PHONY: deploy-staging
deploy-staging: ## Deploy to staging environment
	@echo "$(BLUE)Deploying to staging...$(NC)"
	@git push staging main
	@echo "$(GREEN)✓ Deployed to staging$(NC)"

.PHONY: deploy-prod
deploy-prod: ## Deploy to production environment
	@echo "$(BLUE)Deploying to production...$(NC)"
	@git push production main
	@echo "$(GREEN)✓ Deployed to production$(NC)"

# Security Commands
.PHONY: security-scan
security-scan: ## Run security scan
	@echo "$(BLUE)Running security scan...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm audit
	@$(DOCKER_COMPOSE) exec web-app npm audit
	@echo "$(GREEN)✓ Security scan completed$(NC)"

.PHONY: update-deps
update-deps: ## Update dependencies
	@echo "$(BLUE)Updating dependencies...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm update
	@$(DOCKER_COMPOSE) exec web-app npm update
	@echo "$(GREEN)✓ Dependencies updated$(NC)"

# Documentation Commands
.PHONY: docs
docs: ## Generate documentation
	@echo "$(BLUE)Generating documentation...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run docs
	@echo "$(GREEN)✓ Documentation generated$(NC)"

.PHONY: docs-serve
docs-serve: ## Serve documentation
	@echo "$(BLUE)Serving documentation...$(NC)"
	@$(DOCKER_COMPOSE) exec api-gateway npm run docs:serve
	@echo "$(CYAN)Documentation available at: http://localhost:8080$(NC)"

# Mobile App Commands
.PHONY: mobile-build-android
mobile-build-android: ## Build Android app
	@echo "$(BLUE)Building Android app...$(NC)"
	@cd mobile/flutter_app && flutter build apk
	@echo "$(GREEN)✓ Android app built$(NC)"

.PHONY: mobile-build-ios
mobile-build-ios: ## Build iOS app
	@echo "$(BLUE)Building iOS app...$(NC)"
	@cd mobile/flutter_app && flutter build ios
	@echo "$(GREEN)✓ iOS app built$(NC)"

# Cleanup Commands
.PHONY: clean
clean: ## Clean all Docker resources
	@echo "$(RED)Cleaning Docker resources...$(NC)"
	@docker system prune -af --volumes
	@echo "$(GREEN)✓ Docker resources cleaned$(NC)"

.PHONY: clean-logs
clean-logs: ## Clean log files
	@echo "$(YELLOW)Cleaning log files...$(NC)"
	@rm -rf backend/logs/* nginx/logs/*
	@echo "$(GREEN)✓ Log files cleaned$(NC)"

# Info Commands
.PHONY: info
info: ## Show project information
	@echo "$(CYAN)WS Transfir Project Information$(NC)"
	@echo "==============================="
	@echo "Project: $(PROJECT_NAME)"
	@echo "Network: $(NETWORK_NAME)"
	@echo "Docker Compose: $(DOCKER_COMPOSE)"
	@echo ""
	@echo "$(YELLOW)Available Services:$(NC)"
	@echo "  - API Gateway (Port 3000)"
	@echo "  - Auth Service (Port 3001)"
	@echo "  - User Service (Port 3002)"
	@echo "  - Transfer Service (Port 3003)"
	@echo "  - Wallet Service (Port 3004)"
	@echo "  - Notification Service (Port 3005)"
	@echo "  - Analytics Service (Port 3006)"
	@echo "  - AI Engine (Port 8000)"
	@echo "  - Web App (Port 3100)"
	@echo ""
	@echo "$(YELLOW)Databases:$(NC)"
	@echo "  - PostgreSQL (Port 5432)"
	@echo "  - Redis (Port 6379)"
	@echo "  - MongoDB (Port 27017)"
	@echo "  - Elasticsearch (Port 9200)"
	@echo "  - RabbitMQ (Port 5672, Management: 15672)"
