"""
Agent System API
===============
واجهة برمجة التطبيقات لنظام الوكلاء
"""

import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body # type: ignore
from fastapi.responses import JSONResponse # type: ignore
from pydantic import BaseModel, Field, validator # type: ignore

from ..shared.auth.middleware import get_current_user, require_role, require_permission
from ..shared.auth.jwt_manager import TokenPayload, UserRole, Permission
from ..shared.database.connection import DatabaseConnection # type: ignore

from .agent_service import AgentService, AgentType, AgentStatus
from .commission_engine import CommissionEngine
from .agent_dashboard import AgentDashboard

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/agents", tags=["Agents"])


# Pydantic Models
class AgentCreateRequest(BaseModel):
    """طلب إنشاء وكيل"""
    user_id: str = Field(..., description="معرف المستخدم")
    agent_type: str = Field(default="individual", description="نوع الوكيل")
    parent_agent_id: Optional[str] = Field(None, description="معرف الوكيل الأب")
    business_name: Optional[str] = Field(None, description="اسم الشركة")
    region: str = Field(..., description="المنطقة")
    city: str = Field(..., description="المدينة")
    district: Optional[str] = Field(None, description="الحي")
    address: Optional[str] = Field(None, description="العنوان")
    primary_phone: str = Field(..., description="رقم الهاتف الأساسي")
    business_email: Optional[str] = Field(None, description="البريد الإلكتروني التجاري")
    bank_name: Optional[str] = Field(None, description="اسم البنك")
    bank_account_number: Optional[str] = Field(None, description="رقم الحساب البنكي")
    iban: Optional[str] = Field(None, description="رقم الآيبان")
    base_commission_rate: Optional[Decimal] = Field(default=Decimal('0.0100'), description="معدل العمولة الأساسي")
    tier_commission_rate: Optional[Decimal] = Field(default=Decimal('0.0050'), description="معدل عمولة المستوى")
    volume_bonus_rate: Optional[Decimal] = Field(default=Decimal('0.0025'), description="معدل مكافأة الحجم")
    daily_transaction_limit: Optional[Decimal] = Field(default=Decimal('100000'), description="الحد اليومي للمعاملات")
    monthly_transaction_limit: Optional[Decimal] = Field(default=Decimal('2000000'), description="الحد الشهري للمعاملات")
    single_transaction_limit: Optional[Decimal] = Field(default=Decimal('50000'), description="حد المعاملة الواحدة")

    @validator('agent_type')
    def validate_agent_type(cls, v):
        if v not in ['individual', 'business', 'corporate']:
            raise ValueError('نوع الوكيل غير صحيح')
        return v


class AgentUpdateRequest(BaseModel):
    """طلب تحديث الوكيل"""
    business_name: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    district: Optional[str] = None
    address: Optional[str] = None
    primary_phone: Optional[str] = None
    business_email: Optional[str] = None
    bank_name: Optional[str] = None
    bank_account_number: Optional[str] = None
    iban: Optional[str] = None
    base_commission_rate: Optional[Decimal] = None
    tier_commission_rate: Optional[Decimal] = None
    volume_bonus_rate: Optional[Decimal] = None
    daily_transaction_limit: Optional[Decimal] = None
    monthly_transaction_limit: Optional[Decimal] = None
    single_transaction_limit: Optional[Decimal] = None


class AgentApprovalRequest(BaseModel):
    """طلب الموافقة على الوكيل"""
    approval_notes: Optional[str] = Field(None, description="ملاحظات الموافقة")


class CommissionCalculationRequest(BaseModel):
    """طلب حساب العمولة"""
    transaction_id: str = Field(..., description="معرف المعاملة")
    transaction_amount: Decimal = Field(..., description="مبلغ المعاملة")
    commission_type: str = Field(default="direct", description="نوع العمولة")

    @validator('commission_type')
    def validate_commission_type(cls, v):
        if v not in ['direct', 'tier', 'volume_bonus', 'special']:
            raise ValueError('نوع العمولة غير صحيح')
        return v


# Dependency injection
async def get_agent_service() -> AgentService:
    """الحصول على خدمة الوكلاء"""
    db_connection = DatabaseConnection()  # This would be injected properly
    return AgentService(db_connection)


async def get_commission_engine() -> CommissionEngine:
    """الحصول على محرك العمولات"""
    db_connection = DatabaseConnection()  # This would be injected properly
    return CommissionEngine(db_connection)


async def get_agent_dashboard() -> AgentDashboard:
    """الحصول على لوحة تحكم الوكلاء"""
    db_connection = DatabaseConnection()  # This would be injected properly
    return AgentDashboard(db_connection)


# API Endpoints

@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_agent(
    agent_data: AgentCreateRequest,
    current_user: TokenPayload = Depends(require_permission(Permission.CREATE_AGENT)),
    agent_service: AgentService = Depends(get_agent_service)
):
    """إنشاء وكيل جديد"""
    try:
        logger.info(f"🏢 Creating new agent for user: {agent_data.user_id}")
        
        # Convert Pydantic model to dict
        agent_dict = agent_data.dict(exclude_unset=True)
        
        # Create agent profile
        agent_profile = await agent_service.create_agent_profile(
            user_id=agent_data.user_id,
            agent_data=agent_dict,
            created_by=current_user.user_id
        )
        
        if not agent_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="فشل في إنشاء ملف الوكيل"
            )
        
        return {
            "success": True,
            "message": "تم إنشاء ملف الوكيل بنجاح",
            "data": {
                "agent_id": agent_profile.id,
                "agent_code": agent_profile.agent_code,
                "status": agent_profile.status.value,
                "level": agent_profile.level
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to create agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء إنشاء الوكيل"
        )


@router.get("/{agent_id}")
async def get_agent(
    agent_id: str = Path(..., description="معرف الوكيل"),
    current_user: TokenPayload = Depends(require_permission(Permission.READ_AGENT)),
    agent_service: AgentService = Depends(get_agent_service)
):
    """الحصول على بيانات الوكيل"""
    try:
        agent_profile = await agent_service.get_agent_profile(agent_id)
        
        if not agent_profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الوكيل غير موجود"
            )
        
        return {
            "success": True,
            "data": {
                "id": agent_profile.id,
                "user_id": agent_profile.user_id,
                "agent_code": agent_profile.agent_code,
                "agent_type": agent_profile.agent_type.value,
                "status": agent_profile.status.value,
                "parent_agent_id": agent_profile.parent_agent_id,
                "level": agent_profile.level,
                "hierarchy_path": agent_profile.hierarchy_path,
                "business_name": agent_profile.business_name,
                "region": agent_profile.region,
                "city": agent_profile.city,
                "base_commission_rate": float(agent_profile.base_commission_rate),
                "tier_commission_rate": float(agent_profile.tier_commission_rate),
                "volume_bonus_rate": float(agent_profile.volume_bonus_rate),
                "daily_transaction_limit": float(agent_profile.daily_transaction_limit),
                "monthly_transaction_limit": float(agent_profile.monthly_transaction_limit),
                "single_transaction_limit": float(agent_profile.single_transaction_limit),
                "total_transactions": agent_profile.total_transactions,
                "total_volume": float(agent_profile.total_volume),
                "total_commission_earned": float(agent_profile.total_commission_earned),
                "customer_count": agent_profile.customer_count,
                "rating": float(agent_profile.rating),
                "review_count": agent_profile.review_count,
                "training_completed": agent_profile.training_completed,
                "certification_level": agent_profile.certification_level,
                "onboarding_completed": agent_profile.onboarding_completed,
                "created_at": agent_profile.created_at.isoformat(),
                "updated_at": agent_profile.updated_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب بيانات الوكيل"
        )


@router.put("/{agent_id}/approve")
async def approve_agent(
    agent_id: str = Path(..., description="معرف الوكيل"),
    approval_data: AgentApprovalRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.APPROVE_AGENT)),
    agent_service: AgentService = Depends(get_agent_service)
):
    """الموافقة على الوكيل"""
    try:
        success = await agent_service.approve_agent(
            agent_id=agent_id,
            approved_by=current_user.user_id,
            approval_notes=approval_data.approval_notes
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="فشل في الموافقة على الوكيل"
            )
        
        return {
            "success": True,
            "message": "تم الموافقة على الوكيل بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to approve agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء الموافقة على الوكيل"
        )


@router.get("/{agent_id}/hierarchy")
async def get_agent_hierarchy(
    agent_id: str = Path(..., description="معرف الوكيل"),
    current_user: TokenPayload = Depends(require_permission(Permission.READ_AGENT)),
    agent_service: AgentService = Depends(get_agent_service)
):
    """الحصول على الهيكل الهرمي للوكيل"""
    try:
        hierarchy = await agent_service.get_agent_hierarchy(agent_id)
        
        return {
            "success": True,
            "data": [
                {
                    "id": agent.id,
                    "agent_code": agent.agent_code,
                    "business_name": agent.business_name,
                    "level": agent.level,
                    "status": agent.status.value,
                    "total_volume": float(agent.total_volume),
                    "customer_count": agent.customer_count
                }
                for agent in hierarchy
            ]
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get agent hierarchy: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب الهيكل الهرمي"
        )


@router.post("/{agent_id}/commission/calculate")
async def calculate_commission(
    agent_id: str = Path(..., description="معرف الوكيل"),
    commission_data: CommissionCalculationRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.CALCULATE_COMMISSION)),
    commission_engine: CommissionEngine = Depends(get_commission_engine)
):
    """حساب عمولة الوكيل"""
    try:
        commission_calc = await commission_engine.calculate_commission(
            agent_id=agent_id,
            transaction_id=commission_data.transaction_id,
            transaction_amount=commission_data.transaction_amount
        )
        
        if not commission_calc:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="فشل في حساب العمولة"
            )
        
        return {
            "success": True,
            "data": {
                "agent_id": commission_calc.agent_id,
                "transaction_id": commission_calc.transaction_id,
                "base_amount": float(commission_calc.base_amount),
                "tier_amount": float(commission_calc.tier_amount),
                "volume_bonus": float(commission_calc.volume_bonus),
                "special_bonus": float(commission_calc.special_bonus),
                "total_amount": float(commission_calc.total_amount),
                "applied_rules": commission_calc.applied_rules,
                "calculation_details": commission_calc.calculation_details
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to calculate commission: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء حساب العمولة"
        )


@router.get("/{agent_id}/dashboard")
async def get_agent_dashboard(
    agent_id: str = Path(..., description="معرف الوكيل"),
    current_user: TokenPayload = Depends(get_current_user),
    dashboard: AgentDashboard = Depends(get_agent_dashboard)
):
    """الحصول على لوحة تحكم الوكيل"""
    try:
        # Check if user can access this agent's dashboard
        if (current_user.user_id != agent_id and 
            current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.AGENT_MANAGER]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="ليس لديك صلاحية للوصول لهذه البيانات"
            )
        
        dashboard_data = await dashboard.get_agent_dashboard(agent_id)
        
        return {
            "success": True,
            "data": dashboard_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get agent dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب لوحة التحكم"
        )


@router.get("/{agent_id}/performance")
async def get_agent_performance(
    agent_id: str = Path(..., description="معرف الوكيل"),
    period: str = Query(default="monthly", description="نوع الفترة"),
    months: int = Query(default=12, description="عدد الأشهر"),
    current_user: TokenPayload = Depends(require_permission(Permission.READ_AGENT)),
    agent_service: AgentService = Depends(get_agent_service)
):
    """الحصول على أداء الوكيل"""
    try:
        performance_data = await agent_service.get_agent_performance(
            agent_id=agent_id,
            period_type=period
        )
        
        return {
            "success": True,
            "data": [
                {
                    "period_start": perf.period_start.isoformat(),
                    "period_end": perf.period_end.isoformat(),
                    "transaction_count": perf.transaction_count,
                    "transaction_volume": float(perf.transaction_volume),
                    "average_transaction_amount": float(perf.average_transaction_amount),
                    "total_commission": float(perf.total_commission),
                    "direct_commission": float(perf.direct_commission),
                    "tier_commission": float(perf.tier_commission),
                    "bonus_commission": float(perf.bonus_commission),
                    "new_customers": perf.new_customers,
                    "active_customers": perf.active_customers,
                    "customer_retention_rate": float(perf.customer_retention_rate),
                    "success_rate": float(perf.success_rate),
                    "customer_satisfaction": float(perf.customer_satisfaction),
                    "regional_rank": perf.regional_rank,
                    "national_rank": perf.national_rank,
                    "volume_achievement_rate": float(perf.volume_achievement_rate),
                    "transaction_achievement_rate": float(perf.transaction_achievement_rate)
                }
                for perf in performance_data
            ]
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get agent performance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب بيانات الأداء"
        )


@router.get("/{agent_id}/commissions")
async def get_agent_commissions(
    agent_id: str = Path(..., description="معرف الوكيل"),
    month: Optional[str] = Query(None, description="الشهر (YYYY-MM)"),
    current_user: TokenPayload = Depends(require_permission(Permission.READ_COMMISSION)),
    commission_engine: CommissionEngine = Depends(get_commission_engine)
):
    """الحصول على عمولات الوكيل"""
    try:
        commission_summary = await commission_engine.get_agent_commission_summary(
            agent_id=agent_id,
            start_date=None,  # Could be derived from month parameter
            end_date=None
        )
        
        return {
            "success": True,
            "data": commission_summary
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get agent commissions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب بيانات العمولات"
        )


@router.get("/statistics/overview")
async def get_agents_statistics(
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_ANALYTICS)),
    agent_service: AgentService = Depends(get_agent_service)
):
    """الحصول على إحصائيات الوكلاء العامة"""
    try:
        statistics = await agent_service.get_statistics()
        
        return {
            "success": True,
            "data": statistics
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get agents statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب الإحصائيات"
        )


# Error handlers
@router.exception_handler(ValueError)
async def value_error_handler(request, exc):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "success": False,
            "error": "invalid_input",
            "message": str(exc)
        }
    )


@router.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"❌ Unhandled exception in agent API: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "error": "internal_server_error",
            "message": "حدث خطأ داخلي في الخادم"
        }
    )
