import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: any) {
    // التحقق من صحة الـ payload
    if (!payload.sub || !payload.email) {
      throw new UnauthorizedException('رمز الوصول غير صحيح');
    }

    // التحقق من أن الرمز ليس مؤقت
    if (payload.temp) {
      throw new UnauthorizedException('رمز الوصول المؤقت غير صالح لهذه العملية');
    }

    // TODO: البحث عن المستخدم في قاعدة البيانات
    const user = await this.findUserById(payload.sub);
    
    if (!user) {
      throw new UnauthorizedException('المستخدم غير موجود');
    }

    // TODO: التحقق من أن الرمز لم يتم إلغاؤه
    const isTokenValid = await this.isTokenValid(payload.sub, payload.iat);
    if (!isTokenValid) {
      throw new UnauthorizedException('رمز الوصول ملغي');
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isActive: user.isActive,
      isVerified: user.isVerified,
      role: user.role,
    };
  }

  // Helper methods (سيتم تنفيذها لاحقاً)
  private async findUserById(id: string) {
    // TODO: البحث في قاعدة البيانات
    return {
      id,
      email: '<EMAIL>',
      firstName: 'أحمد',
      lastName: 'محمد',
      isActive: true,
      isVerified: true,
      role: 'user',
    };
  }

  private async isTokenValid(userId: string, issuedAt: number) {
    // TODO: التحقق من صحة الرمز في Redis
    return true;
  }
}
