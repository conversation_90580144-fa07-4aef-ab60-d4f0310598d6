{"version": 3, "file": "isValidNumber.js", "names": ["_isValidNumber", "normalizeArguments", "isValidNumber", "arguments", "input", "options", "metadata", "phone"], "sources": ["../../source/legacy/isValidNumber.js"], "sourcesContent": ["import _isValidNumber from '../isValid.js'\r\nimport { normalizeArguments } from './getNumberType.js'\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function isValidNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isValidNumber(input, options, metadata)\r\n}"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,eAA3B;AACA,SAASC,kBAAT,QAAmC,oBAAnC,C,CAEA;;AACA,eAAe,SAASC,aAAT,GAAyB;EACvC,0BAAqCD,kBAAkB,CAACE,SAAD,CAAvD;EAAA,IAAQC,KAAR,uBAAQA,KAAR;EAAA,IAAeC,OAAf,uBAAeA,OAAf;EAAA,IAAwBC,QAAxB,uBAAwBA,QAAxB,CADuC,CAEvC;;;EACA,IAAI,CAACF,KAAK,CAACG,KAAX,EAAkB;IACjB,OAAO,KAAP;EACA;;EACD,OAAOP,cAAc,CAACI,KAAD,EAAQC,OAAR,EAAiBC,QAAjB,CAArB;AACA"}