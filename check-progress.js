/**
 * فحص سريع للتقدم المحرز
 * Quick Progress Check
 */

const fs = require('fs');

console.log('🚀 فحص التقدم المحرز في إكمال النواقص');
console.log('=========================================');

// الملفات التي تم إنشاؤها في هذه الجلسة
const newFiles = [
  // User Service DTOs
  'backend/user-service/src/modules/users/dto/create-user.dto.ts',
  'backend/user-service/src/modules/users/dto/update-user.dto.ts',
  
  // User Service Controllers & Services
  'backend/user-service/src/modules/users/controllers/users.controller.ts',
  'backend/user-service/src/modules/users/services/users.service.ts',
  
  // User Service Guards & Decorators
  'backend/user-service/src/common/guards/auth.guard.ts',
  'backend/user-service/src/common/guards/roles.guard.ts',
  'backend/user-service/src/common/decorators/roles.decorator.ts',
  'backend/user-service/src/common/decorators/get-user.decorator.ts',
  
  // Transfer Service DTOs & Services
  'backend/transfer-service/src/modules/transfers/dto/create-transfer.dto.ts',
  'backend/transfer-service/src/modules/transfers/services/transfers.service.ts',
  
  // Wallet Service DTOs
  'backend/wallet-service/src/modules/wallets/dto/create-wallet.dto.ts',
  
  // TypeScript configs
  'backend/user-service/tsconfig.json',
  'backend/transfer-service/tsconfig.json',
  'backend/wallet-service/tsconfig.json'
];

let completed = 0;
let missing = 0;

console.log('\n📁 الملفات المنشأة حديثاً:');
newFiles.forEach((file, index) => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${index + 1}. ${file}`);
    completed++;
  } else {
    console.log(`   ❌ ${index + 1}. ${file}`);
    missing++;
  }
});

console.log('\n📊 إحصائيات التقدم:');
console.log(`✅ ملفات مكتملة: ${completed}/${newFiles.length}`);
console.log(`❌ ملفات مفقودة: ${missing}/${newFiles.length}`);
console.log(`📈 نسبة الإنجاز: ${Math.round((completed / newFiles.length) * 100)}%`);

// فحص محتوى بعض الملفات المهمة
console.log('\n🔍 فحص محتوى الملفات:');

const importantFiles = [
  'backend/user-service/src/modules/users/controllers/users.controller.ts',
  'backend/transfer-service/src/modules/transfers/services/transfers.service.ts'
];

importantFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    const lines = content.split('\n').length;
    const hasImports = content.includes('import');
    const hasExports = content.includes('export');
    const hasDecorators = content.includes('@');
    
    console.log(`   📄 ${file}:`);
    console.log(`      📏 عدد الأسطر: ${lines}`);
    console.log(`      📦 يحتوي على imports: ${hasImports ? '✅' : '❌'}`);
    console.log(`      📤 يحتوي على exports: ${hasExports ? '✅' : '❌'}`);
    console.log(`      🎯 يحتوي على decorators: ${hasDecorators ? '✅' : '❌'}`);
  }
});

// الملفات المطلوبة التالية
console.log('\n📋 الملفات المطلوبة التالية (أولوية عالية):');
const nextFiles = [
  'backend/transfer-service/src/modules/transfers/controllers/transfers.controller.ts',
  'backend/wallet-service/src/modules/wallets/controllers/wallets.controller.ts',
  'backend/wallet-service/src/modules/wallets/services/wallets.service.ts',
  'backend/user-service/src/modules/profile/controllers/profile.controller.ts',
  'backend/notification-service/package.json',
  'backend/notification-service/src/main.ts'
];

nextFiles.forEach((file, index) => {
  console.log(`   ${index + 1}. ${file}`);
});

console.log('\n🎯 التوصيات:');
if (completed === newFiles.length) {
  console.log('🎉 ممتاز! تم إكمال جميع الملفات في هذه المرحلة');
  console.log('📝 الخطوة التالية: إكمال Controllers المتبقية');
} else if (completed >= newFiles.length * 0.8) {
  console.log('👍 جيد جداً! معظم الملفات مكتملة');
  console.log('📝 إكمال الملفات المفقودة القليلة');
} else {
  console.log('⚠️  يحتاج المزيد من العمل');
  console.log('📝 التركيز على إكمال الملفات الأساسية');
}

console.log('\n✨ انتهى فحص التقدم!');
