{"name": "busboy", "version": "1.6.0", "author": "<PERSON> <<EMAIL>>", "description": "A streaming parser for HTML form data for node.js", "main": "./lib/index.js", "dependencies": {"streamsearch": "^1.1.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test bench", "lint:fix": "npm run lint -- --fix"}, "engines": {"node": ">=10.16.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}}