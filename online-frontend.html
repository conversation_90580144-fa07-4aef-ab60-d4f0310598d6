<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WS Transfir - نظام التحويلات المالية الأونلاين</title>
    <meta name="description" content="نظام التحويلات المالية المتقدم - WS Transfir Online System">
    <meta name="keywords" content="تحويلات مالية, حوالات, نقود, أونلاين, WS Transfir">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    
    <!-- Progressive Web App -->
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="WS Transfir">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #10b981;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --text-color: #374151;
            --text-light: #6b7280;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            font-size: 2.5em;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2em;
            color: var(--text-light);
            margin-bottom: 20px;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: var(--success-color);
            color: white;
            padding: 8px 16px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9em;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .main-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
        }
        
        .card h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-icon {
            font-size: 1.2em;
        }
        
        .btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin: 8px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }
        
        .btn.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
        }
        
        .btn.danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
        }
        
        .system-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-item {
            background: var(--light-color);
            padding: 15px;
            border-radius: 12px;
            border-left: 4px solid var(--primary-color);
        }
        
        .info-label {
            font-size: 0.9em;
            color: var(--text-light);
            margin-bottom: 5px;
        }
        
        .info-value {
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .result-container {
            background: var(--dark-color);
            color: #ecf0f1;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            display: none;
            border: 1px solid #374151;
        }
        
        .endpoints-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .endpoint-item {
            background: var(--light-color);
            padding: 15px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }
        
        .endpoint-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .endpoint-method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8em;
            font-weight: 600;
            margin-left: 10px;
        }
        
        .method-get { background: var(--success-color); color: white; }
        .method-post { background: var(--primary-color); color: white; }
        .method-put { background: var(--warning-color); color: white; }
        .method-delete { background: var(--danger-color); color: white; }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .footer-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        
        .footer-link:hover {
            color: var(--primary-dark);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .card {
                padding: 20px;
            }
            
            .system-info {
                grid-template-columns: 1fr;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 15px;
            }
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--success-color);
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.error {
            background: var(--danger-color);
        }
        
        .notification.warning {
            background: var(--warning-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 WS Transfir Online</h1>
            <p>نظام التحويلات المالية الأونلاين المتكامل</p>
            <div class="status-badge">
                <span>🟢</span>
                <span>النظام متاح أونلاين</span>
            </div>
        </div>

        <div class="main-content">
            <!-- System Status Card -->
            <div class="card">
                <h2><span class="card-icon">📊</span>حالة النظام</h2>
                <div class="system-info">
                    <div class="info-item">
                        <div class="info-label">حالة الخادم</div>
                        <div class="info-value" id="server-status">جاري الفحص...</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">وقت التشغيل</div>
                        <div class="info-value" id="uptime">جاري الفحص...</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">استخدام الذاكرة</div>
                        <div class="info-value" id="memory-usage">جاري الفحص...</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">البيئة</div>
                        <div class="info-value" id="environment">جاري الفحص...</div>
                    </div>
                </div>
                <button class="btn success" onclick="checkSystemHealth()">
                    <span id="health-icon">🏥</span>
                    فحص صحة النظام
                </button>
            </div>

            <!-- Authentication Card -->
            <div class="card">
                <h2><span class="card-icon">🔐</span>نظام المصادقة</h2>
                <p>اختبار تسجيل الدخول والمصادقة</p>
                <button class="btn" onclick="testAdminLogin()">
                    <span>👨‍💼</span>
                    تسجيل دخول المدير
                </button>
                <button class="btn" onclick="testUserLogin()">
                    <span>👤</span>
                    تسجيل دخول المستخدم
                </button>
                <button class="btn warning" onclick="testRegistration()">
                    <span>📝</span>
                    اختبار التسجيل
                </button>
            </div>

            <!-- Data Management Card -->
            <div class="card">
                <h2><span class="card-icon">💾</span>إدارة البيانات</h2>
                <p>اختبار البيانات والتحويلات</p>
                <button class="btn" onclick="testProfile()">
                    <span>👤</span>
                    عرض الملف الشخصي
                </button>
                <button class="btn" onclick="testTransfers()">
                    <span>💸</span>
                    عرض التحويلات
                </button>
                <button class="btn success" onclick="testTransferStats()">
                    <span>📈</span>
                    إحصائيات التحويلات
                </button>
            </div>

            <!-- API Testing Card -->
            <div class="card">
                <h2><span class="card-icon">🧪</span>اختبار APIs</h2>
                <p>اختبار جميع نقاط API</p>
                <button class="btn" onclick="testAllEndpoints()">
                    <span>🔄</span>
                    اختبار جميع النقاط
                </button>
                <button class="btn warning" onclick="testErrorHandling()">
                    <span>⚠️</span>
                    اختبار معالجة الأخطاء
                </button>
                <button class="btn danger" onclick="clearResults()">
                    <span>🗑️</span>
                    مسح النتائج
                </button>
            </div>
        </div>

        <!-- API Endpoints Reference -->
        <div class="card">
            <h2><span class="card-icon">📋</span>نقاط API المتاحة</h2>
            <div class="endpoints-grid">
                <div class="endpoint-item">
                    <span class="endpoint-method method-get">GET</span>
                    <strong>/api/health</strong>
                    <p>فحص صحة النظام</p>
                </div>
                <div class="endpoint-item">
                    <span class="endpoint-method method-get">GET</span>
                    <strong>/api/status</strong>
                    <p>حالة النظام</p>
                </div>
                <div class="endpoint-item">
                    <span class="endpoint-method method-post">POST</span>
                    <strong>/api/auth/login</strong>
                    <p>تسجيل الدخول</p>
                </div>
                <div class="endpoint-item">
                    <span class="endpoint-method method-post">POST</span>
                    <strong>/api/auth/register</strong>
                    <p>إنشاء حساب جديد</p>
                </div>
                <div class="endpoint-item">
                    <span class="endpoint-method method-get">GET</span>
                    <strong>/api/profile/me</strong>
                    <p>الملف الشخصي</p>
                </div>
                <div class="endpoint-item">
                    <span class="endpoint-method method-get">GET</span>
                    <strong>/api/transfers</strong>
                    <p>قائمة التحويلات</p>
                </div>
                <div class="endpoint-item">
                    <span class="endpoint-method method-get">GET</span>
                    <strong>/api/transfers/stats</strong>
                    <p>إحصائيات التحويلات</p>
                </div>
            </div>
        </div>

        <!-- Results Display -->
        <div id="result" class="result-container"></div>

        <!-- Footer -->
        <div class="footer">
            <h3>🚀 WS Transfir Online System</h3>
            <p>نظام التحويلات المالية المتقدم - جاهز للاستخدام الأونلاين</p>
            <div class="footer-links">
                <a href="/api/health" target="_blank" class="footer-link">📊 فحص الصحة</a>
                <a href="/api/status" target="_blank" class="footer-link">📈 حالة النظام</a>
                <a href="mailto:<EMAIL>" class="footer-link">📧 الدعم الفني</a>
                <a href="https://wstransfir.com" target="_blank" class="footer-link">🌐 الموقع الرسمي</a>
            </div>
            <p style="margin-top: 20px; color: var(--text-light); font-size: 0.9em;">
                © 2024 WS Transfir. جميع الحقوق محفوظة.
            </p>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script>
        const API_BASE = window.location.origin;
        
        // Utility functions
        function showResult(data, title = 'النتيجة') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `=== ${title} ===\n${JSON.stringify(data, null, 2)}`;
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function showError(error, title = 'خطأ') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `=== ${title} ===\nخطأ: ${error.message || error}`;
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
        
        function setLoading(buttonElement, loading = true) {
            if (loading) {
                buttonElement.innerHTML = '<span class="loading"></span> جاري التحميل...';
                buttonElement.disabled = true;
            } else {
                buttonElement.disabled = false;
            }
        }
        
        // API Testing Functions
        async function checkSystemHealth() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                
                // Update system info
                document.getElementById('server-status').textContent = data.status;
                document.getElementById('uptime').textContent = data.server.uptime.human;
                document.getElementById('memory-usage').textContent = data.server.memory.usage;
                document.getElementById('environment').textContent = data.environment;
                
                showResult(data, 'فحص صحة النظام');
                showNotification('✅ النظام يعمل بشكل طبيعي');
            } catch (error) {
                showError(error, 'خطأ في فحص النظام');
                showNotification('❌ فشل في الاتصال بالنظام', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        async function testAdminLogin() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                const data = await response.json();
                showResult(data, 'تسجيل دخول المدير');
                
                if (data.success) {
                    showNotification('✅ تم تسجيل دخول المدير بنجاح');
                } else {
                    showNotification('❌ فشل في تسجيل الدخول', 'error');
                }
            } catch (error) {
                showError(error, 'خطأ في تسجيل الدخول');
                showNotification('❌ خطأ في الاتصال', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        async function testUserLogin() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                const data = await response.json();
                showResult(data, 'تسجيل دخول المستخدم');
                
                if (data.success) {
                    showNotification('✅ تم تسجيل دخول المستخدم بنجاح');
                } else {
                    showNotification('❌ فشل في تسجيل الدخول', 'error');
                }
            } catch (error) {
                showError(error, 'خطأ في تسجيل الدخول');
                showNotification('❌ خطأ في الاتصال', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        async function testRegistration() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        firstName: 'محمد',
                        lastName: 'أحمد',
                        email: `test${Date.now()}@example.com`,
                        password: 'password123',
                        phone: '+966501234567'
                    })
                });
                const data = await response.json();
                showResult(data, 'اختبار التسجيل');
                
                if (data.success) {
                    showNotification('✅ تم إنشاء الحساب بنجاح');
                } else {
                    showNotification('❌ فشل في إنشاء الحساب', 'error');
                }
            } catch (error) {
                showError(error, 'خطأ في التسجيل');
                showNotification('❌ خطأ في الاتصال', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        async function testProfile() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/profile/me`);
                const data = await response.json();
                showResult(data, 'الملف الشخصي');
                showNotification('✅ تم جلب الملف الشخصي');
            } catch (error) {
                showError(error, 'خطأ في جلب الملف الشخصي');
                showNotification('❌ خطأ في الاتصال', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        async function testTransfers() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/transfers`);
                const data = await response.json();
                showResult(data, 'قائمة التحويلات');
                showNotification('✅ تم جلب التحويلات');
            } catch (error) {
                showError(error, 'خطأ في جلب التحويلات');
                showNotification('❌ خطأ في الاتصال', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        async function testTransferStats() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            try {
                const response = await fetch(`${API_BASE}/api/transfers/stats`);
                const data = await response.json();
                showResult(data, 'إحصائيات التحويلات');
                showNotification('✅ تم جلب الإحصائيات');
            } catch (error) {
                showError(error, 'خطأ في جلب الإحصائيات');
                showNotification('❌ خطأ في الاتصال', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        async function testAllEndpoints() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            const results = {};
            
            try {
                // Test health
                const healthResponse = await fetch(`${API_BASE}/api/health`);
                results.health = await healthResponse.json();
                
                // Test status
                const statusResponse = await fetch(`${API_BASE}/api/status`);
                results.status = await statusResponse.json();
                
                // Test login
                const loginResponse = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: '<EMAIL>', password: 'admin123' })
                });
                results.login = await loginResponse.json();
                
                // Test profile
                const profileResponse = await fetch(`${API_BASE}/api/profile/me`);
                results.profile = await profileResponse.json();
                
                // Test transfers
                const transfersResponse = await fetch(`${API_BASE}/api/transfers`);
                results.transfers = await transfersResponse.json();
                
                // Test transfer stats
                const statsResponse = await fetch(`${API_BASE}/api/transfers/stats`);
                results.transferStats = await statsResponse.json();
                
                showResult(results, 'اختبار جميع النقاط');
                showNotification('✅ تم اختبار جميع النقاط بنجاح');
            } catch (error) {
                showError(error, 'خطأ في اختبار النقاط');
                showNotification('❌ فشل في اختبار بعض النقاط', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        async function testErrorHandling() {
            const button = event.target;
            const originalContent = button.innerHTML;
            setLoading(button);
            
            try {
                // Test 404
                const notFoundResponse = await fetch(`${API_BASE}/api/nonexistent`);
                const notFoundData = await notFoundResponse.json();
                
                // Test invalid login
                const invalidLoginResponse = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: '<EMAIL>', password: 'wrong' })
                });
                const invalidLoginData = await invalidLoginResponse.json();
                
                const errorResults = {
                    notFound: notFoundData,
                    invalidLogin: invalidLoginData
                };
                
                showResult(errorResults, 'اختبار معالجة الأخطاء');
                showNotification('✅ تم اختبار معالجة الأخطاء');
            } catch (error) {
                showError(error, 'خطأ في اختبار الأخطاء');
                showNotification('❌ خطأ في الاختبار', 'error');
            } finally {
                button.innerHTML = originalContent;
            }
        }
        
        function clearResults() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            resultDiv.innerHTML = '';
            showNotification('🗑️ تم مسح النتائج');
        }
        
        // Auto-check system health on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkSystemHealth();
            }, 1000);
        });
        
        // Auto-refresh system status every 30 seconds
        setInterval(() => {
            if (document.getElementById('server-status').textContent !== 'جاري الفحص...') {
                checkSystemHealth();
            }
        }, 30000);
    </script>
</body>
</html>
