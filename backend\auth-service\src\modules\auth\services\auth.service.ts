import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

// Entities
import { User, UserStatus } from '../../users/entities/user.entity';

// Services
import { TokenService } from './token.service';
import { SessionService } from './session.service';
import { UsersService } from '../../users/services/users.service';
import { NotificationService } from '../../notification/services/notification.service';
import { RedisService } from '../../../shared/redis/redis.service';

// DTOs
import { RegisterDto } from '../dto/register.dto';
import { LoginDto } from '../dto/login.dto';
import { VerifyEmailDto } from '../dto/verify-email.dto';
import { VerifyPhoneDto } from '../dto/verify-phone.dto';
import { ResendVerificationDto } from '../dto/resend-verification.dto';

// Interfaces
export interface AuthResult {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginAttempt {
  ip: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly tokenService: TokenService,
    private readonly sessionService: SessionService,
    private readonly usersService: UsersService,
    private readonly notificationService: NotificationService,
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * تسجيل مستخدم جديد
   */
  async register(registerDto: RegisterDto, ip?: string): Promise<{ message: string; userId: string }> {
    const { email, phone, password, firstName, lastName } = registerDto;

    // التحقق من وجود المستخدم
    const existingUser = await this.userRepository.findOne({
      where: [{ email }, { phone }],
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw new ConflictException('البريد الإلكتروني مستخدم بالفعل');
      }
      if (existingUser.phone === phone) {
        throw new ConflictException('رقم الهاتف مستخدم بالفعل');
      }
    }

    // إنشاء المستخدم الجديد
    const user = this.userRepository.create({
      email,
      phone,
      password,
      firstName,
      lastName,
      status: UserStatus.PENDING,
      emailVerificationToken: uuidv4(),
      phoneVerificationCode: this.generateVerificationCode(),
      emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      phoneVerificationExpires: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
    });

    await this.userRepository.save(user);

    // إرسال رسائل التحقق
    await Promise.all([
      this.notificationService.sendEmailVerification(user.email, user.emailVerificationToken),
      this.notificationService.sendPhoneVerification(user.phone, user.phoneVerificationCode),
    ]);

    // تسجيل محاولة التسجيل
    await this.logAuthEvent('register', user.id, ip, true);

    this.logger.log(`New user registered: ${user.email}`);

    return {
      message: 'تم إنشاء الحساب بنجاح. يرجى التحقق من البريد الإلكتروني ورقم الهاتف',
      userId: user.id,
    };
  }

  /**
   * تسجيل الدخول
   */
  async login(loginDto: LoginDto, ip?: string, userAgent?: string): Promise<AuthResult> {
    const { email, password, rememberMe } = loginDto;

    // البحث عن المستخدم
    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      await this.logAuthEvent('login_failed', null, ip, false, 'User not found');
      throw new UnauthorizedException('بيانات الدخول غير صحيحة');
    }

    // التحقق من حالة الحساب
    if (user.status === UserStatus.BLOCKED) {
      throw new ForbiddenException('الحساب محظور. يرجى التواصل مع الدعم');
    }

    if (user.status === UserStatus.SUSPENDED) {
      throw new ForbiddenException('الحساب معلق مؤقتاً');
    }

    // التحقق من القفل المؤقت
    if (user.isLocked) {
      throw new ForbiddenException('الحساب مقفل مؤقتاً بسبب محاولات دخول فاشلة متعددة');
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await user.validatePassword(password);
    if (!isPasswordValid) {
      user.incrementFailedAttempts();
      await this.userRepository.save(user);
      
      await this.logAuthEvent('login_failed', user.id, ip, false, 'Invalid password');
      throw new UnauthorizedException('بيانات الدخول غير صحيحة');
    }

    // التحقق من التفعيل
    if (!user.isVerified) {
      throw new ForbiddenException('يرجى تفعيل البريد الإلكتروني ورقم الهاتف أولاً');
    }

    // تحديث معلومات آخر دخول
    user.updateLastLogin(ip);
    await this.userRepository.save(user);

    // إنشاء الرموز المميزة
    const expiresIn = rememberMe ? '30d' : '24h';
    const tokens = await this.tokenService.generateTokens(user, expiresIn);

    // إنشاء جلسة
    await this.sessionService.createSession(user.id, tokens.refreshToken, ip, userAgent);

    // تسجيل نجاح الدخول
    await this.logAuthEvent('login_success', user.id, ip, true);

    this.logger.log(`User logged in: ${user.email}`);

    return {
      user,
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: tokens.expiresIn,
    };
  }

  /**
   * تسجيل الخروج
   */
  async logout(userId: string, refreshToken: string): Promise<{ message: string }> {
    // إلغاء الجلسة
    await this.sessionService.revokeSession(refreshToken);

    // إضافة الرمز المميز إلى القائمة السوداء
    await this.tokenService.blacklistToken(refreshToken);

    this.logger.log(`User logged out: ${userId}`);

    return { message: 'تم تسجيل الخروج بنجاح' };
  }

  /**
   * تحديث الرمز المميز
   */
  async refreshToken(refreshToken: string): Promise<{ accessToken: string; expiresIn: number }> {
    // التحقق من صحة الرمز المميز
    const payload = await this.tokenService.verifyRefreshToken(refreshToken);
    
    // البحث عن المستخدم
    const user = await this.userRepository.findOne({
      where: { id: payload.sub },
    });

    if (!user || !user.isActive) {
      throw new UnauthorizedException('المستخدم غير موجود أو غير نشط');
    }

    // التحقق من الجلسة
    const session = await this.sessionService.getSession(refreshToken);
    if (!session || !session.isActive) {
      throw new UnauthorizedException('الجلسة غير صالحة');
    }

    // إنشاء رمز مميز جديد
    const newTokens = await this.tokenService.generateAccessToken(user);

    return {
      accessToken: newTokens.accessToken,
      expiresIn: newTokens.expiresIn,
    };
  }

  /**
   * التحقق من البريد الإلكتروني
   */
  async verifyEmail(verifyEmailDto: VerifyEmailDto): Promise<{ message: string }> {
    const { token } = verifyEmailDto;

    const user = await this.userRepository.findOne({
      where: { emailVerificationToken: token },
    });

    if (!user) {
      throw new BadRequestException('رمز التحقق غير صالح');
    }

    if (user.emailVerificationExpires < new Date()) {
      throw new BadRequestException('رمز التحقق منتهي الصلاحية');
    }

    // تفعيل البريد الإلكتروني
    user.emailVerified = true;
    user.emailVerificationToken = null;
    user.emailVerificationExpires = null;

    // تفعيل الحساب إذا تم التحقق من الهاتف أيضاً
    if (user.phoneVerified && user.status === UserStatus.PENDING) {
      user.status = UserStatus.ACTIVE;
    }

    await this.userRepository.save(user);

    this.logger.log(`Email verified for user: ${user.email}`);

    return { message: 'تم تفعيل البريد الإلكتروني بنجاح' };
  }

  /**
   * التحقق من رقم الهاتف
   */
  async verifyPhone(verifyPhoneDto: VerifyPhoneDto): Promise<{ message: string }> {
    const { phone, code } = verifyPhoneDto;

    const user = await this.userRepository.findOne({
      where: { phone, phoneVerificationCode: code },
    });

    if (!user) {
      throw new BadRequestException('رمز التحقق غير صالح');
    }

    if (user.phoneVerificationExpires < new Date()) {
      throw new BadRequestException('رمز التحقق منتهي الصلاحية');
    }

    // تفعيل رقم الهاتف
    user.phoneVerified = true;
    user.phoneVerificationCode = null;
    user.phoneVerificationExpires = null;

    // تفعيل الحساب إذا تم التحقق من البريد الإلكتروني أيضاً
    if (user.emailVerified && user.status === UserStatus.PENDING) {
      user.status = UserStatus.ACTIVE;
    }

    await this.userRepository.save(user);

    this.logger.log(`Phone verified for user: ${user.email}`);

    return { message: 'تم تفعيل رقم الهاتف بنجاح' };
  }

  /**
   * إعادة إرسال رمز التحقق
   */
  async resendVerification(resendDto: ResendVerificationDto): Promise<{ message: string }> {
    const { email, type } = resendDto;

    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user) {
      throw new BadRequestException('المستخدم غير موجود');
    }

    if (type === 'email') {
      if (user.emailVerified) {
        throw new BadRequestException('البريد الإلكتروني مفعل بالفعل');
      }

      user.emailVerificationToken = uuidv4();
      user.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000);
      
      await this.userRepository.save(user);
      await this.notificationService.sendEmailVerification(user.email, user.emailVerificationToken);
      
      return { message: 'تم إرسال رمز التحقق إلى البريد الإلكتروني' };
    }

    if (type === 'phone') {
      if (user.phoneVerified) {
        throw new BadRequestException('رقم الهاتف مفعل بالفعل');
      }

      user.phoneVerificationCode = this.generateVerificationCode();
      user.phoneVerificationExpires = new Date(Date.now() + 10 * 60 * 1000);
      
      await this.userRepository.save(user);
      await this.notificationService.sendPhoneVerification(user.phone, user.phoneVerificationCode);
      
      return { message: 'تم إرسال رمز التحقق إلى رقم الهاتف' };
    }

    throw new BadRequestException('نوع التحقق غير صالح');
  }

  /**
   * توليد رمز التحقق
   */
  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * تسجيل أحداث المصادقة
   */
  private async logAuthEvent(
    event: string,
    userId: string | null,
    ip: string,
    success: boolean,
    details?: string,
  ): Promise<void> {
    const logData = {
      event,
      userId,
      ip,
      success,
      details,
      timestamp: new Date(),
    };

    // حفظ في Redis للمراقبة السريعة
    await this.redisService.set(
      `auth_log:${Date.now()}:${userId || 'anonymous'}`,
      JSON.stringify(logData),
      300, // 5 minutes
    );

    // يمكن إضافة حفظ في قاعدة البيانات أو نظام السجلات
  }
}
