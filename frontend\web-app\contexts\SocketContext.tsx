'use client';

import React, { createContext, useContext, useEffect, useRef, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { useNotifications } from './NotificationContext';
import { tokenStorage } from '@/utils/storage';

// أنواع الأحداث
export interface SocketEvents {
  // أحداث التحويلات
  'transfer:status_updated': {
    transferId: string;
    status: string;
    timestamp: string;
  };
  'transfer:received': {
    transferId: string;
    amount: number;
    currency: string;
    senderName: string;
  };
  
  // أحداث المحفظة
  'wallet:balance_updated': {
    currency: string;
    newBalance: number;
    change: number;
  };
  'wallet:transaction_completed': {
    transactionId: string;
    type: string;
    amount: number;
    currency: string;
  };
  
  // أحداث الأمان
  'security:login_detected': {
    location: string;
    device: string;
    timestamp: string;
  };
  'security:suspicious_activity': {
    type: string;
    description: string;
    timestamp: string;
  };
  
  // أحداث النظام
  'system:maintenance': {
    message: string;
    startTime: string;
    endTime: string;
  };
  'system:announcement': {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success';
  };
}

// حالة الاتصال
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

// واجهة سياق Socket
interface SocketContextType {
  socket: Socket | null;
  connectionStatus: ConnectionStatus;
  isConnected: boolean;
  emit: <T extends keyof SocketEvents>(event: T, data: SocketEvents[T]) => void;
  on: <T extends keyof SocketEvents>(event: T, callback: (data: SocketEvents[T]) => void) => void;
  off: <T extends keyof SocketEvents>(event: T, callback?: (data: SocketEvents[T]) => void) => void;
  connect: () => void;
  disconnect: () => void;
}

// إنشاء السياق
const SocketContext = createContext<SocketContextType | null>(null);

// مزود السياق
export function SocketProvider({ children }: { children: ReactNode }) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const { state: authState } = useAuth();
  const { addNotification } = useNotifications();
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  // إنشاء الاتصال
  const createConnection = () => {
    if (socket) {
      socket.disconnect();
    }

    const newSocket = io(process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000', {
      auth: {
        token: tokenStorage.getAccessToken(),
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: false, // سنتعامل مع إعادة الاتصال يدوياً
    });

    // أحداث الاتصال
    newSocket.on('connect', () => {
      console.log('Socket connected:', newSocket.id);
      setConnectionStatus('connected');
      reconnectAttemptsRef.current = 0;
      
      // إرسال معلومات المستخدم
      if (authState.user) {
        newSocket.emit('user:join', { userId: authState.user.id });
      }
    });

    newSocket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      setConnectionStatus('disconnected');
      
      // إعادة الاتصال التلقائي
      if (reason === 'io server disconnect') {
        // الخادم قطع الاتصال، لا تعيد الاتصال تلقائياً
        return;
      }
      
      scheduleReconnect();
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setConnectionStatus('error');
      scheduleReconnect();
    });

    // أحداث التحويلات
    newSocket.on('transfer:status_updated', (data) => {
      addNotification({
        type: 'transfer',
        title: 'تحديث حالة التحويل',
        message: `تم تحديث حالة التحويل إلى: ${getStatusText(data.status)}`,
        isRead: false,
        isImportant: true,
        actionUrl: `/dashboard/transfers/${data.transferId}`,
        actionText: 'عرض التفاصيل',
        metadata: data,
      });
    });

    newSocket.on('transfer:received', (data) => {
      addNotification({
        type: 'transfer',
        title: 'تحويل مستلم',
        message: `تم استلام ${data.amount} ${data.currency} من ${data.senderName}`,
        isRead: false,
        isImportant: true,
        actionUrl: `/dashboard/transfers/${data.transferId}`,
        actionText: 'عرض التفاصيل',
        metadata: data,
      });
    });

    // أحداث المحفظة
    newSocket.on('wallet:balance_updated', (data) => {
      const changeText = data.change > 0 ? 'زيادة' : 'نقصان';
      addNotification({
        type: 'info',
        title: 'تحديث الرصيد',
        message: `${changeText} في رصيد ${data.currency}: ${Math.abs(data.change)}`,
        isRead: false,
        isImportant: false,
        metadata: data,
      });
    });

    newSocket.on('wallet:transaction_completed', (data) => {
      addNotification({
        type: 'success',
        title: 'معاملة مكتملة',
        message: `تم إكمال ${getTransactionTypeText(data.type)} بقيمة ${data.amount} ${data.currency}`,
        isRead: false,
        isImportant: false,
        actionUrl: `/dashboard/wallet/transactions/${data.transactionId}`,
        actionText: 'عرض التفاصيل',
        metadata: data,
      });
    });

    // أحداث الأمان
    newSocket.on('security:login_detected', (data) => {
      addNotification({
        type: 'security',
        title: 'تسجيل دخول جديد',
        message: `تم تسجيل الدخول من ${data.device} في ${data.location}`,
        isRead: false,
        isImportant: true,
        metadata: data,
      });
    });

    newSocket.on('security:suspicious_activity', (data) => {
      addNotification({
        type: 'warning',
        title: 'نشاط مشبوه',
        message: data.description,
        isRead: false,
        isImportant: true,
        metadata: data,
      });
    });

    // أحداث النظام
    newSocket.on('system:maintenance', (data) => {
      addNotification({
        type: 'warning',
        title: 'صيانة النظام',
        message: data.message,
        isRead: false,
        isImportant: true,
        metadata: data,
      });
    });

    newSocket.on('system:announcement', (data) => {
      addNotification({
        type: data.type,
        title: data.title,
        message: data.message,
        isRead: false,
        isImportant: data.type === 'warning',
        metadata: data,
      });
    });

    setSocket(newSocket);
    return newSocket;
  };

  // جدولة إعادة الاتصال
  const scheduleReconnect = () => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
    reconnectAttemptsRef.current++;

    console.log(`Scheduling reconnection attempt ${reconnectAttemptsRef.current} in ${delay}ms`);

    reconnectTimeoutRef.current = setTimeout(() => {
      if (authState.isAuthenticated) {
        setConnectionStatus('connecting');
        createConnection();
      }
    }, delay);
  };

  // الاتصال
  const connect = () => {
    if (socket?.connected) {
      return;
    }
    
    setConnectionStatus('connecting');
    createConnection();
  };

  // قطع الاتصال
  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (socket) {
      socket.disconnect();
      setSocket(null);
    }
    
    setConnectionStatus('disconnected');
    reconnectAttemptsRef.current = 0;
  };

  // إرسال حدث
  const emit = <T extends keyof SocketEvents>(event: T, data: SocketEvents[T]) => {
    if (socket?.connected) {
      socket.emit(event, data);
    }
  };

  // الاستماع لحدث
  const on = <T extends keyof SocketEvents>(event: T, callback: (data: SocketEvents[T]) => void) => {
    if (socket) {
      socket.on(event as string, callback);
    }
  };

  // إلغاء الاستماع لحدث
  const off = <T extends keyof SocketEvents>(event: T, callback?: (data: SocketEvents[T]) => void) => {
    if (socket) {
      if (callback) {
        socket.off(event as string, callback);
      } else {
        socket.off(event as string);
      }
    }
  };

  // الاتصال عند تسجيل الدخول
  useEffect(() => {
    if (authState.isAuthenticated && !socket) {
      connect();
    } else if (!authState.isAuthenticated && socket) {
      disconnect();
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [authState.isAuthenticated]);

  // تنظيف عند إلغاء التحميل
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  const value: SocketContextType = {
    socket,
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    emit,
    on,
    off,
    connect,
    disconnect,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
}

// خطاف استخدام السياق
export function useSocket() {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
}

// دوال مساعدة
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    pending: 'قيد الانتظار',
    processing: 'قيد المعالجة',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    failed: 'فاشل',
  };
  return statusMap[status] || status;
}

function getTransactionTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    top_up: 'شحن المحفظة',
    withdrawal: 'سحب من المحفظة',
    transfer_sent: 'تحويل مرسل',
    transfer_received: 'تحويل مستلم',
    bill_payment: 'دفع فاتورة',
    currency_conversion: 'تحويل عملة',
  };
  return typeMap[type] || type;
}
