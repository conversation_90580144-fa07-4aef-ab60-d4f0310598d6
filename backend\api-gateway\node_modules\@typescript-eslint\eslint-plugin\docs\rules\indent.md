---
description: 'Enforce consistent indentation.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/indent** for documentation.

## Warning

:::warning

Please read [Issue #1824: Problems with the indent rule](https://github.com/typescript-eslint/typescript-eslint/issues/1824) before using this rule!

:::

This rule extends the base [`eslint/indent`](https://eslint.org/docs/rules/indent) rule.
It adds support for TypeScript nodes.
