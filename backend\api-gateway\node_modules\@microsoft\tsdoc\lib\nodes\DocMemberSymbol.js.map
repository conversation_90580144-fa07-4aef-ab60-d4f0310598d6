{"version": 3, "file": "DocMemberSymbol.js", "sourceRoot": "", "sources": ["../../src/nodes/DocMemberSymbol.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAE,OAAO,EAA0D,MAAM,WAAW,CAAC;AAGzG,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAqBvD;;;;;;;;;GASG;AACH;IAAqC,mCAAO;IAQ1C;;;OAGG;IACH,yBAAmB,UAAyE;QAC1F,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,mBAAmB,GAAG,IAAI,UAAU,CAAC;gBACxC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,2BAA2B;gBACpD,OAAO,EAAE,UAAU,CAAC,kBAAkB;aACvC,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,8BAA8B,EAAE,CAAC;gBAC9C,KAAI,CAAC,+BAA+B,GAAG,IAAI,UAAU,CAAC;oBACpD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,8BAA8B;iBACnD,CAAC,CAAC;YACL,CAAC;YAED,KAAI,CAAC,oBAAoB,GAAG,IAAI,UAAU,CAAC;gBACzC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,4BAA4B;gBACrD,OAAO,EAAE,UAAU,CAAC,mBAAmB;aACxC,CAAC,CAAC;QACL,CAAC;QAED,KAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,eAAe,CAAC;;IACrD,CAAC;IAGD,sBAAW,iCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,YAAY,CAAC;QAClC,CAAC;;;OAAA;IAMD,sBAAW,4CAAe;QAJ1B;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC;;;OAAA;IAED,gBAAgB;IACN,yCAAe,GAAzB;QACE,OAAO;YACL,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,+BAA+B;YACpC,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,oBAAoB;SAC1B,CAAC;IACJ,CAAC;IACH,sBAAC;AAAD,CAAC,AA9DD,CAAqC,OAAO,GA8D3C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { DocDeclarationReference } from './DocDeclarationReference';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberSymbol}.\r\n */\r\nexport interface IDocMemberSymbolParameters extends IDocNodeParameters {\r\n  symbolReference: DocDeclarationReference;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberSymbol}.\r\n */\r\nexport interface IDocMemberSymbolParsedParameters extends IDocNodeParsedParameters {\r\n  leftBracketExcerpt: TokenSequence;\r\n  spacingAfterLeftBracketExcerpt?: TokenSequence;\r\n\r\n  symbolReference: DocDeclarationReference;\r\n\r\n  rightBracketExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents a reference to an ECMAScript 6 symbol that is used\r\n * to identify a member declaration.\r\n *\r\n * @example\r\n *\r\n * In the declaration reference `{@link MyClass.([MySymbols.example]:instance)}`,\r\n * the member symbol `[MySymbols.example]` might be used to reference a property\r\n * of the class.\r\n */\r\nexport class DocMemberSymbol extends DocNode {\r\n  private readonly _leftBracketExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterLeftBracketExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _symbolReference: DocDeclarationReference;\r\n\r\n  private readonly _rightBracketExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocMemberSymbolParameters | IDocMemberSymbolParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._leftBracketExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.DocMemberSymbol_LeftBracket,\r\n        content: parameters.leftBracketExcerpt\r\n      });\r\n\r\n      if (parameters.spacingAfterLeftBracketExcerpt) {\r\n        this._spacingAfterLeftBracketExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterLeftBracketExcerpt\r\n        });\r\n      }\r\n\r\n      this._rightBracketExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.DocMemberSymbol_RightBracket,\r\n        content: parameters.rightBracketExcerpt\r\n      });\r\n    }\r\n\r\n    this._symbolReference = parameters.symbolReference;\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.MemberSymbol;\r\n  }\r\n\r\n  /**\r\n   * The declaration reference for the ECMAScript 6 symbol that will act as\r\n   * the identifier for the member.\r\n   */\r\n  public get symbolReference(): DocDeclarationReference {\r\n    return this._symbolReference;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this._leftBracketExcerpt,\r\n      this._spacingAfterLeftBracketExcerpt,\r\n      this._symbolReference,\r\n      this._rightBracketExcerpt\r\n    ];\r\n  }\r\n}\r\n"]}