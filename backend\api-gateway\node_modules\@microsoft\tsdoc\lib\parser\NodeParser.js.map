{"version": 3, "file": "NodeParser.js", "sourceRoot": "", "sources": ["../../src/parser/NodeParser.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;AAK3D,OAAO,EAAc,SAAS,EAAE,MAAM,SAAS,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EACL,WAAW,EACX,WAAW,EACX,YAAY,EACZ,cAAc,EACd,gBAAgB,EAChB,aAAa,EACb,eAAe,EACf,YAAY,EAEZ,YAAY,EACZ,YAAY,EACZ,WAAW,EAEX,QAAQ,EACR,WAAW,EAEX,aAAa,EACb,aAAa,EACb,UAAU,EAEV,kBAAkB,EAClB,uBAAuB,EACvB,eAAe,EACf,mBAAmB,EACnB,iBAAiB,EACjB,gBAAgB,EAMjB,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAG9C,OAAO,EAA2B,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAClG,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAYlD,SAAS,SAAS,CAAI,eAAmC;IACvD,OAAO,eAAe,KAAK,SAAS,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;AACxG,CAAC;AAED;;GAEG;AACH;IAKE,oBAAmB,aAA4B;QAC7C,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,aAAa,CAAC;QAElD,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,UAAU,CAAC,cAAc,CAAC;IACjE,CAAC;IAEM,0BAAK,GAAZ;QACE,IAAM,WAAW,GAAgB,IAAI,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEtE,IAAI,IAAI,GAAY,KAAK,CAAC;QAC1B,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,yBAAyB;YACzB,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,UAAU;oBACvB,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;gBACR,KAAK,SAAS,CAAC,OAAO;oBACpB,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;oBAC5C,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,IAAI,CAAC,SAAS,CACZ,IAAI,YAAY,CAAC;wBACf,MAAM,EAAE,IAAI;wBACZ,aAAa,EAAE,IAAI,CAAC,cAAc;wBAClC,gBAAgB,EAAE,WAAW,CAAC,0BAA0B,EAAE;qBAC3D,CAAC,CACH,CAAC;oBACF,MAAM;gBACR,KAAK,SAAS,CAAC,SAAS;oBACtB,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;oBAC5C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC;oBACxD,MAAM;gBACR,KAAK,SAAS,CAAC,MAAM;oBACnB,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;oBAC5C,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;oBACrC,MAAM;gBACR,KAAK,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;oBAChC,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;oBAE5C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;oBAClD,IAAM,OAAO,GAAY,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;oBAC3D,IAAM,UAAU,GAAe,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;oBAE9D,IAAI,OAAO,YAAY,gBAAgB,EAAE,CAAC;wBACxC,4EAA4E;wBAC5E,iDAAiD;wBACjD,IAAM,YAAY,GAAW,WAAW,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;wBAC5D,IAAI,UAAU,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;4BAC3C,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,GAAG,OAAO,CAAC;wBACzD,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,SAAS,CACZ,IAAI,CAAC,6BAA6B,CAChC,WAAW,EACX,MAAM,EACN,YAAY,EACZ,cAAc,CAAC,kBAAkB,EACjC,yDAAyD,CAC1D,CACF,CAAC;wBACJ,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC1B,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,SAAS,CAAC,iBAAiB;oBAC9B,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;oBAC5C,IAAI,CAAC,SAAS,CACZ,IAAI,CAAC,YAAY,CACf,WAAW,EACX,cAAc,CAAC,gBAAgB,EAC/B,kGAAkG,CACnG,CACF,CAAC;oBACF,MAAM;gBACR,KAAK,SAAS,CAAC,QAAQ;oBACrB,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;oBAC5C,2DAA2D;oBAC3D,IAAI,WAAW,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;wBACzD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;oBACrD,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;oBACvD,CAAC;oBACD,MAAM;gBACR,KAAK,SAAS,CAAC,WAAW;oBACxB,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;oBAC5C,IAAI,CAAC,SAAS,CACZ,IAAI,CAAC,YAAY,CACf,WAAW,EACX,cAAc,CAAC,iBAAiB,EAChC,2FAA2F,CAC5F,CACF,CAAC;oBACF,MAAM;gBACR,KAAK,SAAS,CAAC,QAAQ;oBACrB,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;oBAE5C,IACE,WAAW,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,QAAQ;wBACvD,WAAW,CAAC,uBAAuB,EAAE,KAAK,SAAS,CAAC,QAAQ,EAC5D,CAAC;wBACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;oBACrD,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;oBACnD,CAAC;oBACD,MAAM;gBACR;oBACE,8DAA8D;oBAC9D,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;YACV,CAAC;QACH,CAAC;QACD,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAEO,6CAAwB,GAAhC;QACE,IAAM,UAAU,GAAe,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;QAC9D,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;YAC/B,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACpE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,yBAAyB,EACxC,cAAO,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,+CAA4C;oBAC5F,8CAA8C,EAChD,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,gBAAgB,EAAE,EACtD,UAAU,CAAC,eAAe,CAC3B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;YAC7B,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC5B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,yBAAyB,EACxC,cAAM,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,4CAAwC;oBACpF,6CAA6C,EAC/C,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,gBAAgB,EAAE,EACnD,UAAU,CAAC,YAAY,CAAC,QAAQ,CACjC,CAAC;YACJ,CAAC;YACD,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,sBAAsB,CAC5C,cAAc,CAAC,6BAA6B,EAC5C,6DAA6D;oBAC3D,6CAA6C,EAC/C,IAAI,CAAC,cAAc,CAAC,YAAY,CACjC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,2CAAsB,GAA9B,UACE,aAA6C,EAC7C,OAAe,EACf,kBAA2B,EAC3B,4BAA2C,EAC3C,mBAA4B;QAE5B,IAAI,aAAa,EAAE,CAAC;YAClB,IAAM,WAAW,GAAY,aAAa,CAAC,UAAU,KAAK,kBAAkB,CAAC,SAAS,CAAC;YAEvF,IAAI,WAAW,KAAK,kBAAkB,EAAE,CAAC;gBACvC,iDAAiD;gBACjD,IAAI,kBAAkB,EAAE,CAAC;oBACvB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,sBAAsB,EACrC,0BAAkB,OAAO,uEAAiE,EAC1F,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,sBAAsB,EACrC,0BAAkB,OAAO,+DAAyD,EAClF,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC;oBACvE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,CAAC;wBACrE,wCAAwC;wBACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,cAAc,EAC7B,0BAAkB,OAAO,qCAAiC,EAC1D,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,yBAAyB;YACzB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC;gBACtE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,YAAY,EAC3B,0BAAkB,OAAO,4CAAwC,EACjE,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,8CAAyB,GAAjC,UAAkC,WAAwB;QACxD,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,EAAE,CAAC;YAC9C,IAAI,CAAC,SAAS,CACZ,IAAI,YAAY,CAAC;gBACf,MAAM,EAAE,IAAI;gBACZ,aAAa,EAAE,IAAI,CAAC,cAAc;gBAClC,WAAW,EAAE,WAAW,CAAC,0BAA0B,EAAE;aACtD,CAAC,CACH,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,uCAAkB,GAA1B,UAA2B,WAAwB;QACjD,IAAM,UAAU,GAAe,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;QAC9D,IAAM,aAAa,GAAuB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;QAC5E,IAAM,cAAc,GAAmB,UAAU,CAAC,cAAc,CAAC;QAEjE,IAAM,cAAc,GAAY,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QACjE,IAAI,cAAc,CAAC,IAAI,KAAK,WAAW,CAAC,QAAQ,EAAE,CAAC;YACjD,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC/B,OAAO;QACT,CAAC;QAED,IAAM,WAAW,GAAgB,cAA6B,CAAC;QAE/D,wCAAwC;QACxC,IAAM,aAAa,GAAmC,aAAa,CAAC,gCAAgC,CAClG,WAAW,CAAC,oBAAoB,CACjC,CAAC;QACF,IAAI,CAAC,sBAAsB,CACzB,aAAa,EACb,WAAW,CAAC,OAAO;QACnB,wBAAwB,CAAC,KAAK,EAC9B,WAAW,CAAC,gBAAgB,EAAE,EAC9B,WAAW,CACZ,CAAC;QAEF,IAAI,aAAa,EAAE,CAAC;YAClB,QAAQ,aAAa,CAAC,UAAU,EAAE,CAAC;gBACjC,KAAK,kBAAkB,CAAC,QAAQ;oBAC9B,IAAI,WAAW,CAAC,oBAAoB,KAAK,YAAY,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;wBACjF,IAAM,aAAa,GAAkB,IAAI,CAAC,gBAAgB,CACxD,WAAW,EACX,WAAW,EACX,YAAY,CAAC,KAAK,CAAC,OAAO,CAC3B,CAAC;wBAEF,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;wBAEzD,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,OAAO,CAAC;wBAC7C,OAAO;oBACT,CAAC;yBAAM,IAAI,WAAW,CAAC,oBAAoB,KAAK,YAAY,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;wBAC5F,IAAM,aAAa,GAAkB,IAAI,CAAC,gBAAgB,CACxD,WAAW,EACX,WAAW,EACX,YAAY,CAAC,SAAS,CAAC,OAAO,CAC/B,CAAC;wBAEF,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;wBAE7D,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,OAAO,CAAC;wBAC7C,OAAO;oBACT,CAAC;yBAAM,CAAC;wBACN,IAAM,QAAQ,GAAa,IAAI,QAAQ,CAAC;4BACtC,aAAa,EAAE,IAAI,CAAC,cAAc;4BAClC,QAAQ,EAAE,WAAW;yBACtB,CAAC,CAAC;wBAEH,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;wBAErC,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC;oBAC1C,CAAC;oBAED,OAAO;gBACT,KAAK,kBAAkB,CAAC,WAAW;oBACjC,gFAAgF;oBAChF,wDAAwD;oBACxD,cAAc,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;oBACnC,OAAO;YACX,CAAC;QACH,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IAEO,0CAAqB,GAA7B,UAA8B,KAAe;QAC3C,IAAM,UAAU,GAAe,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;QAE9D,QAAQ,KAAK,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;YAC5C,KAAK,YAAY,CAAC,OAAO,CAAC,oBAAoB;gBAC5C,UAAU,CAAC,YAAY,GAAG,KAAK,CAAC;gBAChC,MAAM;YACR,KAAK,YAAY,CAAC,cAAc,CAAC,oBAAoB;gBACnD,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC;gBAClC,MAAM;YACR,KAAK,YAAY,CAAC,UAAU,CAAC,oBAAoB;gBAC/C,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC;gBACnC,MAAM;YACR,KAAK,YAAY,CAAC,OAAO,CAAC,oBAAoB;gBAC5C,UAAU,CAAC,YAAY,GAAG,KAAK,CAAC;gBAChC,MAAM;YACR,KAAK,YAAY,CAAC,GAAG,CAAC,oBAAoB;gBACxC,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAClC,MAAM;YACR;gBACE,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,kDAA6B,GAArC,UACE,WAAwB,EACxB,QAAmB,EACnB,SAAoB,EACpB,WAAmB;QAEnB,IAAI,SAAgC,CAAC;QACrC,IAAI,SAAS,GAAW,CAAC,CAAC;QAC1B,OAAO,SAAS,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,SAAS,GAAc,WAAW,CAAC,aAAa,EAAE,CAAC;YACvD,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,QAAQ;oBACX,sDAAsD;oBACtD,IAAI,SAAS,KAAK,SAAS;wBAAE,SAAS,EAAE,CAAC;oBACzC,MAAM;gBACR,KAAK,SAAS;oBACZ,uDAAuD;oBACvD,IAAI,SAAS,KAAK,SAAS;wBAAE,SAAS,EAAE,CAAC;oBACzC,MAAM;gBACR,KAAK,SAAS,CAAC,SAAS;oBACtB,4CAA4C;oBAC5C,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;wBAC5B,6CAA6C;wBAC7C,WAAW,CAAC,SAAS,EAAE,CAAC;wBACxB,SAAS,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;oBAC1C,CAAC;oBACD,MAAM;gBACR,KAAK,SAAS,CAAC,WAAW,CAAC;gBAC3B,KAAK,SAAS,CAAC,WAAW,CAAC;gBAC3B,KAAK,SAAS,CAAC,QAAQ;oBACrB,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;wBAC5B,iDAAiD;wBACjD,SAAS,GAAG,SAAS,CAAC;oBACxB,CAAC;yBAAM,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;wBACnC,iDAAiD;wBACjD,SAAS,GAAG,SAAS,CAAC;oBACxB,CAAC;oBACD,MAAM;YACV,CAAC;YACD,kDAAkD;YAClD,IAAI,SAAS,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;gBACvC,WAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAC3C,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC;QACD,OAAO,WAAW,CAAC,6BAA6B,EAAE,CAAC;IACrD,CAAC;IAED;;;OAGG;IACK,kDAA6B,GAArC,UACE,WAAwB,EACxB,WAAwB,EACxB,OAAe;QAEf,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAE/C,uCAAuC;QACvC,IACE,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,gBAAgB;YAC1D,WAAW,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,MAAM,EACrD,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAM,WAAW,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QACvD,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,eAAe;QAExC,IAAI,gBAAgB,GAA8B,IAAI,CAAC,6BAA6B,CAClF,WAAW,EACX,SAAS,CAAC,gBAAgB,EAC1B,SAAS,CAAC,iBAAiB,EAC3B,WAAW,CACZ,CAAC;QAEF,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,uBAAuB,EACtC,MAAM,GAAG,OAAO,GAAG,kDAAkD,EACrE,gBAAgB,EAChB,WAAW,CACZ,CAAC;YAEF,IAAM,4BAA4B,GAChC,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;YAC/C,IAAI,4BAA4B,EAAE,CAAC;gBACjC,gBAAgB,GAAG,gBAAgB,CAAC,cAAc,CAChD,gBAAgB,CAAC,UAAU,EAC3B,4BAA4B,CAAC,QAAQ,CACtC,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACK,mDAA8B,GAAtC,UAAuC,WAAwB;QAC7D,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAC/C,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;YACzD,IAAM,WAAW,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;YACvD,OAAO,IAAI,CAAC,6BAA6B,CACvC,WAAW,EACX,SAAS,CAAC,iBAAiB,EAC3B,SAAS,CAAC,kBAAkB,EAC5B,WAAW,CACZ,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,qCAAgB,GAAxB,UACE,WAAwB,EACxB,WAAwB,EACxB,OAAe;QAEf,IAAM,WAAW,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAEvD,IAAM,iCAAiC,GACrC,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAE/C,2FAA2F;QAC3F,IAAM,8CAA8C,GAClD,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAExE,qEAAqE;QACrE,IAAI,8CAAyE,CAAC;QAC9E,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAChE,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,eAAe;YACxC,8CAA8C,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAC5F,CAAC;QAED,IAAI,aAAa,GAAW,EAAE,CAAC;QAE/B,IAAI,IAAI,GAAY,KAAK,CAAC;QAC1B,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,SAAS,CAAC;gBACzB,KAAK,SAAS,CAAC,MAAM,CAAC;gBACtB,KAAK,SAAS,CAAC,UAAU;oBACvB,aAAa,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;oBACzC,MAAM;gBACR;oBACE,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAM,WAAW,GAAuB,YAAY,CAAC,kCAAkC,CAAC,aAAa,CAAC,CAAC;QAEvG,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,WAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAE3C,IAAM,eAAe,GAAkB,IAAI,aAAa,CAAC;gBACvD,aAAa,EAAE,IAAI,CAAC,cAAc;gBAClC,QAAQ,EAAE,WAAW;gBACrB,aAAa,EAAE,EAAE;aAClB,CAAC,CAAC;YACH,IAAM,YAAY,GAChB,aAAa,CAAC,MAAM,GAAG,CAAC;gBACtB,CAAC,CAAC,MAAM,GAAG,OAAO,GAAG,uDAAuD,GAAG,WAAW;gBAC1F,CAAC,CAAC,MAAM,GAAG,OAAO,GAAG,+CAA+C,CAAC;YAEzE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,uBAAuB,EACtC,YAAY,EACZ,WAAW,CAAC,gBAAgB,EAAE,EAC9B,WAAW,CACZ,CAAC;YACF,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,IAAM,oBAAoB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAErF,mFAAmF;QACnF,IAAI,uCAAkE,CAAC;QACvE,IAAI,8CAA8C,EAAE,CAAC;YACnD,uCAAuC,GAAG,IAAI,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAC;YAE3F,IAAI,aAAa,GAA8B,8CAA8C,CAAC;YAC9F,IAAI,uCAAuC,EAAE,CAAC;gBAC5C,aAAa,GAAG,WAAW;qBACxB,gBAAgB,EAAE;qBAClB,cAAc,CACb,8CAA8C,CAAC,UAAU,EACzD,uCAAuC,CAAC,QAAQ,CACjD,CAAC;YACN,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,+BAA+B,EAC9C,MAAM;gBACJ,OAAO;gBACP,6FAA6F,EAC/F,aAAa,EACb,WAAW,CACZ,CAAC;QACJ,CAAC;QAED,IAAM,gCAAgC,GACpC,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAE/C,oGAAoG;QACpG,IAAM,6CAA6C,GACjD,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAExE,6DAA6D;QAC7D,IAAI,aAAwC,CAAC;QAC7C,IAAI,yBAAoD,CAAC;QACzD,IAAI,sCAAiE,CAAC;QACtE,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACrD,WAAW,CAAC,SAAS,EAAE,CAAC;YACxB,aAAa,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;YACzD,4BAA4B;YAC5B,yBAAyB,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;YAEzE,6FAA6F;YAC7F,sCAAsC,GAAG,IAAI,CAAC,6BAA6B,CACzE,WAAW,EACX,WAAW,EACX,OAAO,CACR,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,qBAAqB,EACpC,MAAM,GAAG,OAAO,GAAG,iEAAiE,EACpF,WAAW,CAAC,gBAAgB,EAAE,EAC9B,WAAW,CACZ,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,aAAa,CAAC;YACvB,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,QAAQ,EAAE,WAAW;YAErB,iCAAiC,mCAAA;YAEjC,8CAA8C,gDAAA;YAC9C,8CAA8C,gDAAA;YAE9C,oBAAoB,sBAAA;YACpB,aAAa,eAAA;YAEb,uCAAuC,yCAAA;YAEvC,gCAAgC,kCAAA;YAEhC,6CAA6C,+CAAA;YAE7C,aAAa,eAAA;YAEb,yBAAyB,2BAAA;YAEzB,sCAAsC,wCAAA;SACvC,CAAC,CAAC;IACL,CAAC;IAEO,8BAAS,GAAjB,UAAkB,OAAgB;QAChC,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3F,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACtD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAEO,0CAAqB,GAA7B,UAA8B,WAAwB;QACpD,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAC/C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAElD,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,qBAAqB;QAE9C,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,oBAAoB,EACnC,kEAAkE,CACnE,CAAC;QACJ,CAAC;QAED,IAAM,YAAY,GAAU,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,6BAA6B;QAElF,kEAAkE;QAClE,uEAAuE;QACvE,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,oBAAoB,EACnC,gEAAgE,CACjE,CAAC;QACJ,CAAC;QAED,IAAM,kBAAkB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAEnF,OAAO,IAAI,cAAc,CAAC;YACxB,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,WAAW,EAAE,WAAW,CAAC,mBAAmB;YAC5C,kBAAkB,oBAAA;YAClB,WAAW,EAAE,YAAY,CAAC,QAAQ,EAAE;SACrC,CAAC,CAAC;IACL,CAAC;IAEO,mCAAc,GAAtB,UAAuB,WAAwB;QAC7C,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAC/C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAElD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,UAAU,EACzB,yCAAyC,CAC1C,CAAC;QACJ,CAAC;QAED,wEAAwE;QACxE,iFAAiF;QACjF,0BAA0B;QAC1B,QAAQ,WAAW,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAC5C,KAAK,SAAS,CAAC,UAAU,CAAC;YAC1B,KAAK,SAAS,CAAC,OAAO,CAAC;YACvB,KAAK,SAAS,CAAC,OAAO;gBACpB,MAAM;YACR;gBACE,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,YAAY,EAC3B,gFAAgF,CACjF,CAAC;QACN,CAAC;QAED,yCAAyC;QACzC,IAAI,OAAO,GAAW,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;QAEzD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;YACxD,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,oBAAoB,EACnC,oGAAoG,CACrG,CAAC;QACJ,CAAC;QAED,IAAM,aAAa,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAEzD,OAAO,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;YAC3D,OAAO,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;QAChD,CAAC;QAED,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;YACpC,KAAK,SAAS,CAAC,OAAO,CAAC;YACvB,KAAK,SAAS,CAAC,OAAO,CAAC;YACvB,KAAK,SAAS,CAAC,UAAU;gBACvB,MAAM;YACR;gBACE,IAAM,YAAY,GAAW,WAAW,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;gBACzE,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,uBAAuB,EACtC,sBAAc,OAAO,gEAA4D;oBAC/E,WAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,8DAAyD,CAC5F,CAAC;QACN,CAAC;QAED,IAAI,YAAY,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAE,CAAC;YACvD,IAAM,OAAO,GAAa,IAAI,CAAC,4BAA4B,CACzD,WAAW,EACX,cAAc,CAAC,gBAAgB,EAC/B,gFAAgF,EAChF,aAAa,CACd,CAAC;YACF,OAAO,IAAI,CAAC,kCAAkC,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QACnF,CAAC;QAED,OAAO,IAAI,WAAW,CAAC;YACrB,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,OAAO,SAAA;YACP,cAAc,EAAE,WAAW,CAAC,0BAA0B,EAAE;SACzD,CAAC,CAAC;IACL,CAAC;IAEO,oCAAe,GAAvB,UAAwB,WAAwB;QAC9C,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAC/C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAElD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,UAAU,EACzB,yCAAyC,CAC1C,CAAC;QACJ,CAAC;QACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,IAAM,uBAAuB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAExF,6FAA6F;QAC7F,2FAA2F;QAC3F,6DAA6D;QAC7D,kEAAkE;QAClE,IAAM,YAAY,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAExD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,kBAAkB,EACjC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QAED,yCAAyC;QACzC,IAAI,OAAO,GAAW,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;QAEzD,OAAO,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;YAC3D,OAAO,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;QAChD,CAAC;QAED,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;YACpB,0BAA0B;YAC1B,IAAM,OAAO,GAAa,IAAI,CAAC,4BAA4B,CACzD,WAAW,EACX,cAAc,CAAC,kBAAkB,EACjC,6DAA6D,EAC7D,YAAY,CACb,CAAC;YACF,OAAO,IAAI,CAAC,uCAAuC,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QACtG,CAAC;QAED,IAAI,YAAY,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAE,CAAC;YACvD,IAAM,OAAO,GAAa,IAAI,CAAC,4BAA4B,CACzD,WAAW,EACX,cAAc,CAAC,gBAAgB,EAC/B,gFAAgF,EAChF,YAAY,CACb,CAAC;YACF,OAAO,IAAI,CAAC,uCAAuC,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QACtG,CAAC;QAED,IAAM,cAAc,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAE/E,IAAM,0BAA0B,GAC9B,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAE/C,IAAI,0BAA0B,KAAK,SAAS,EAAE,CAAC;YAC7C,2FAA2F;YAC3F,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,iBAAiB,EAAE,CAAC;gBAChE,IAAM,YAAY,GAAW,WAAW,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;gBACzE,IAAM,OAAO,GAAa,IAAI,CAAC,sBAAsB,CACnD,WAAW,EACX,cAAc,CAAC,wBAAwB,EACvC,wBAAiB,IAAI,CAAC,SAAS,CAC7B,YAAY,CACb,+DAA4D,CAC9D,CAAC;gBACF,OAAO,IAAI,CAAC,uCAAuC,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;YACtG,CAAC;QACH,CAAC;QAED,IAAI,IAAI,GAAY,KAAK,CAAC;QAC1B,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,UAAU;oBACvB,OAAO,IAAI,CAAC,6BAA6B,CACvC,WAAW,EACX,MAAM,EACN,YAAY,EACZ,cAAc,CAAC,0BAA0B,EACzC,sDAAsD,CACvD,CAAC;gBACJ,KAAK,SAAS,CAAC,SAAS;oBACtB,mDAAmD;oBACnD,kFAAkF;oBAClF,4BAA4B;oBAC5B,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,wBAAwB;oBAEjD,kEAAkE;oBAClE,uEAAuE;oBACvE,qBAAqB;oBACrB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC;wBAC1D,IAAM,OAAO,GAAa,IAAI,CAAC,sBAAsB,CACnD,WAAW,EACX,cAAc,CAAC,oBAAoB,EACnC,gEAAgE,CACjE,CAAC;wBACF,OAAO,IAAI,CAAC,uCAAuC,CACjD,WAAW,EACX,MAAM,EACN,YAAY,EACZ,kCAAkC,EAClC,OAAO,CACR,CAAC;oBACJ,CAAC;oBAED,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;gBACR,KAAK,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;oBAChC,IAAM,OAAO,GAAa,IAAI,CAAC,sBAAsB,CACnD,WAAW,EACX,cAAc,CAAC,uBAAuB,EACtC,wFAAwF,CACzF,CAAC;oBACF,OAAO,IAAI,CAAC,uCAAuC,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;gBACtG,CAAC;gBACD,KAAK,SAAS,CAAC,iBAAiB;oBAC9B,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;gBACR;oBACE,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAM,iBAAiB,GAA8B,WAAW,CAAC,6BAA6B,EAAE,CAAC;QAEjG,+BAA+B;QAC/B,WAAW,CAAC,SAAS,EAAE,CAAC;QACxB,IAAM,uBAAuB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAExF,IAAM,4BAA4B,GAAkC;YAClE,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,uBAAuB,yBAAA;YAEvB,cAAc,gBAAA;YACd,OAAO,SAAA;YACP,0BAA0B,4BAAA;YAE1B,iBAAiB,mBAAA;YAEjB,uBAAuB,yBAAA;SACxB,CAAC;QAEF,IAAM,oBAAoB,GAAW,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3D,yFAAyF;QACzF,IAAM,mBAAmB,GAAgB,IAAI,WAAW,CACtD,IAAI,CAAC,cAAc,EACnB,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CACvF,CAAC;QAEF,IAAI,OAAgB,CAAC;QACrB,QAAQ,oBAAoB,EAAE,CAAC;YAC7B,KAAK,YAAY,CAAC,UAAU,CAAC,oBAAoB;gBAC/C,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,4BAA4B,EAAE,mBAAmB,CAAC,CAAC;gBACtF,MAAM;YACR,KAAK,YAAY,CAAC,IAAI,CAAC,oBAAoB;gBACzC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE,mBAAmB,CAAC,CAAC;gBAChF,MAAM;YACR;gBACE,OAAO,GAAG,IAAI,YAAY,CAAC,4BAA4B,CAAC,CAAC;QAC7D,CAAC;QAED,mBAAmB;QACnB,IAAM,aAAa,GACjB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,gCAAgC,CAAC,oBAAoB,CAAC,CAAC;QAE3F,IAAI,CAAC,sBAAsB,CACzB,aAAa,EACb,OAAO;QACP,wBAAwB,CAAC,IAAI,EAC7B,cAAc,EACd,OAAO,CACR,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,wCAAmB,GAA3B,UACE,4BAA2D,EAC3D,mBAAgC;QAEhC,qFAAqF;QACrF,IAAM,QAAQ,GAAiB,IAAI,YAAY,CAAC,4BAA4B,CAAC,CAAC;QAE9E,IAAM,UAAU,gBACX,4BAA4B,CAChC,CAAC;QAEF,IAAI,mBAAmB,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;YACjE,UAAU,CAAC,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,CAC/D,mBAAmB,EACnB,4BAA4B,CAAC,cAAc,EAC3C,QAAQ,CACT,CAAC;YACF,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;gBACrC,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,IAAI,mBAAmB,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;gBACjE,mBAAmB,CAAC,SAAS,EAAE,CAAC;gBAEhC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,mBAAmB,EAClC,kDAAkD,EAClD,mBAAmB,CAAC,0BAA0B,EAAE,EAChD,QAAQ,CACT,CAAC;gBACF,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAC1C,CAAC;IAEO,kCAAa,GAArB,UACE,4BAA2D,EAC3D,mBAAgC;QAEhC,qFAAqF;QACrF,IAAM,QAAQ,GAAiB,IAAI,YAAY,CAAC,4BAA4B,CAAC,CAAC;QAE9E,IAAM,UAAU,gBACX,4BAA4B,CAChC,CAAC;QAEF,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,EAAE,CAAC;YACpD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,YAAY,EAC3B,kCAAkC,EAClC,UAAU,CAAC,cAAc,EACzB,QAAQ,CACT,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,4DAA4D;QAC5D,EAAE;QACF,kFAAkF;QAClF,oFAAoF;QACpF,4FAA4F;QAC5F,2FAA2F;QAC3F,8FAA8F;QAC9F,wDAAwD;QAExD,4FAA4F;QAC5F,8BAA8B;QAC9B,4BAA4B;QAE5B,IAAI,YAAY,GACd,mBAAmB,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,KAAK;YACvD,mBAAmB,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,KAAK,CAAC;QAC/D,IAAM,MAAM,GAAW,mBAAmB,CAAC,YAAY,EAAE,CAAC;QAE1D,IAAI,IAAI,GAAY,YAAY,CAAC;QACjC,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,mBAAmB,CAAC,aAAa,EAAE,EAAE,CAAC;gBAC5C,uEAAuE;gBACvE,KAAK,SAAS,CAAC,SAAS,CAAC;gBACzB,KAAK,SAAS,CAAC,MAAM,CAAC;gBACtB,KAAK,SAAS,CAAC,MAAM,CAAC;gBACtB,KAAK,SAAS,CAAC,IAAI;oBACjB,mBAAmB,CAAC,SAAS,EAAE,CAAC;oBAChC,MAAM;gBACR,KAAK,SAAS,CAAC,KAAK;oBAClB,mBAAmB,CAAC,SAAS,EAAE,CAAC;oBAChC,gEAAgE;oBAChE,YAAY;wBACV,mBAAmB,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,KAAK;4BACvD,mBAAmB,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,KAAK,CAAC;oBAC/D,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;gBACR;oBACE,IAAI,GAAG,IAAI,CAAC;YAChB,CAAC;QACH,CAAC;QAED,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE9C,qDAAqD;QACrD,IAAI,YAAY,EAAE,CAAC;YACjB,gEAAgE;YAChE,IACE,CAAC,IAAI,CAAC,2BAA2B,CAC/B,mBAAmB,EACnB,UAAU,EACV,4BAA4B,CAAC,cAAc,EAC3C,QAAQ,CACT,EACD,CAAC;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,iDAAiD;YACjD,IACE,CAAC,IAAI,CAAC,4BAA4B,CAChC,mBAAmB,EACnB,UAAU,EACV,4BAA4B,CAAC,cAAc,EAC3C,QAAQ,CACT,EACD,CAAC;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,IAAI,mBAAmB,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;YAC9D,0EAA0E;YAC1E,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,mBAAmB,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YAC3D,qBAAqB;YACrB,mBAAmB,CAAC,SAAS,EAAE,CAAC;YAChC,UAAU,CAAC,WAAW,GAAG,mBAAmB,CAAC,0BAA0B,EAAE,CAAC;YAC1E,UAAU,CAAC,uBAAuB,GAAG,IAAI,CAAC,0BAA0B,CAAC,mBAAmB,CAAC,CAAC;YAE1F,gCAAgC;YAChC,qFAAqF;YACrF,2DAA2D;YAC3D,IAAI,GAAG,KAAK,CAAC;YACb,IAAI,0BAA0B,GAAuB,SAAS,CAAC;YAC/D,OAAO,CAAC,IAAI,EAAE,CAAC;gBACb,QAAQ,mBAAmB,CAAC,aAAa,EAAE,EAAE,CAAC;oBAC5C,KAAK,SAAS,CAAC,UAAU;wBACvB,IAAI,GAAG,IAAI,CAAC;wBACZ,MAAM;oBACR,KAAK,SAAS,CAAC,IAAI,CAAC;oBACpB,KAAK,SAAS,CAAC,gBAAgB;wBAC7B,IAAM,YAAY,GAAW,mBAAmB,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;wBACxE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,oBAAoB,EACnC,gBAAQ,YAAY,sEAAkE,EACtF,mBAAmB,CAAC,0BAA0B,EAAE,EAChD,QAAQ,CACT,CAAC;wBACF,OAAO,QAAQ,CAAC;oBAClB,KAAK,SAAS,CAAC,OAAO,CAAC;oBACvB,KAAK,SAAS,CAAC,OAAO;wBACpB,mBAAmB,CAAC,SAAS,EAAE,CAAC;wBAChC,MAAM;oBACR;wBACE,2EAA2E;wBAC3E,0BAA0B,GAAG,mBAAmB,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;wBACpE,mBAAmB,CAAC,SAAS,EAAE,CAAC;gBACpC,CAAC;YACH,CAAC;YAED,IAAM,kBAAkB,GACtB,mBAAmB,CAAC,6BAA6B,EAAE,CAAC;YACtD,IAAI,kBAAkB,EAAE,CAAC;gBACvB,IAAI,0BAA0B,KAAK,SAAS,EAAE,CAAC;oBAC7C,+EAA+E;oBAC/E,UAAU,CAAC,2BAA2B,GAAG,kBAAkB,CAAC;gBAC9D,CAAC;qBAAM,IAAI,0BAA0B,IAAI,kBAAkB,CAAC,QAAQ,EAAE,CAAC;oBACrE,mEAAmE;oBACnE,UAAU,CAAC,eAAe,GAAG,kBAAkB,CAAC;gBAClD,CAAC;qBAAM,CAAC;oBACN,gDAAgD;oBAChD,UAAU,CAAC,eAAe,GAAG,kBAAkB,CAAC,cAAc,CAC5D,kBAAkB,CAAC,UAAU,EAC7B,0BAA0B,CAC3B,CAAC;oBACF,UAAU,CAAC,2BAA2B,GAAG,kBAAkB,CAAC,cAAc,CACxE,0BAA0B,EAC1B,kBAAkB,CAAC,QAAQ,CAC5B,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;YACxE,mBAAmB,CAAC,SAAS,EAAE,CAAC;YAEhC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,wBAAwB,EACvC,6CAA6C,EAC7C,mBAAmB,CAAC,0BAA0B,EAAE,EAChD,QAAQ,CACT,CAAC;YACF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAEO,gDAA2B,GAAnC,UACE,mBAAgC,EAChC,UAAuC,EACvC,4BAA2C,EAC3C,mBAA4B;QAE5B,wFAAwF;QACxF,mBAAmB;QACnB,IAAI,cAAc,GAAW,EAAE,CAAC;QAEhC,IAAI,IAAI,GAAY,KAAK,CAAC;QAC1B,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,mBAAmB,CAAC,aAAa,EAAE,EAAE,CAAC;gBAC5C,KAAK,SAAS,CAAC,OAAO,CAAC;gBACvB,KAAK,SAAS,CAAC,OAAO,CAAC;gBACvB,KAAK,SAAS,CAAC,UAAU,CAAC;gBAC1B,KAAK,SAAS,CAAC,IAAI,CAAC;gBACpB,KAAK,SAAS,CAAC,iBAAiB;oBAC9B,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;gBACR;oBACE,cAAc,IAAI,mBAAmB,CAAC,SAAS,EAAE,CAAC;oBAClD,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,kGAAkG;YAClG,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAM,qBAAqB,GAAkB,mBAAmB,CAAC,0BAA0B,EAAE,CAAC;QAE9F,IAAM,qBAAqB,GAAuB,YAAY,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QACvG,IAAI,qBAAqB,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,iBAAiB,EAChC,qBAAqB,EACrB,qBAAqB,EACrB,mBAAmB,CACpB,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,UAAU,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;QACzD,UAAU,CAAC,8BAA8B,GAAG,IAAI,CAAC,0BAA0B,CAAC,mBAAmB,CAAC,CAAC;QAEjG,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,iDAA4B,GAApC,UACE,mBAAgC,EAChC,UAAiC,EACjC,4BAA2C,EAC3C,mBAA4B;QAE5B,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAC1D,mBAAmB,EACnB,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;QAEF,OAAO,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC;IACtC,CAAC;IAEO,+CAA0B,GAAlC,UACE,WAAwB,EACxB,4BAA2C,EAC3C,mBAA4B;QAE5B,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAE/C,kHAAkH;QAClH,oHAAoH;QAEpH,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAClD,IAAI,OAAO,GAAY,KAAK,CAAC;QAE7B,4FAA4F;QAC5F,6FAA6F;QAC7F,6DAA6D;QAC7D,IAAI,0BAA0B,GAAY,IAAI,CAAC;QAC/C,IAAI,mBAAmB,GAAY,KAAK,CAAC;QAEzC,IAAI,IAAI,GAAY,KAAK,CAAC;QAC1B,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,WAAW,CAAC;gBAC3B,KAAK,SAAS,CAAC,UAAU,CAAC;gBAC1B,KAAK,SAAS,CAAC,gBAAgB,CAAC;gBAChC,KAAK,SAAS,CAAC,eAAe,CAAC;gBAC/B,KAAK,SAAS,CAAC,iBAAiB,CAAC;gBACjC,KAAK,SAAS,CAAC,OAAO,CAAC;gBACvB,KAAK,SAAS,CAAC,IAAI,CAAC;gBACpB,KAAK,SAAS,CAAC,iBAAiB,CAAC;gBACjC,KAAK,SAAS,CAAC,gBAAgB,CAAC;gBAChC,KAAK,SAAS,CAAC,kBAAkB,CAAC;gBAClC,KAAK,SAAS,CAAC,WAAW,CAAC;gBAC3B,KAAK,SAAS,CAAC,OAAO;oBACpB,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;gBACR,KAAK,SAAS,CAAC,WAAW;oBACxB,OAAO,GAAG,IAAI,CAAC;oBACf,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;gBACR,KAAK,SAAS,CAAC,KAAK,CAAC;gBACrB,KAAK,SAAS,CAAC,MAAM;oBACnB,IAAI,0BAA0B,EAAE,CAAC;wBAC/B,mBAAmB,GAAG,IAAI,CAAC;oBAC7B,CAAC;oBACD,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;gBACR,KAAK,SAAS,CAAC,SAAS,CAAC;gBACzB,KAAK,SAAS,CAAC,MAAM,CAAC;gBACtB,KAAK,SAAS,CAAC,MAAM;oBACnB,0EAA0E;oBAC1E,kDAAkD;oBAClD,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;gBACR;oBACE,+EAA+E;oBAC/E,6CAA6C;oBAC7C,0BAA0B,GAAG,KAAK,CAAC;oBAEnC,WAAW,CAAC,SAAS,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAO,IAAI,mBAAmB,EAAE,CAAC;YACpC,sFAAsF;YACtF,mFAAmF;YACnF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,oBAAoB,EACnC,6EAA6E;gBAC3E,sCAAsC,EACxC,WAAW,CAAC,0BAA0B,EAAE,EACxC,mBAAmB,CACpB,CAAC;YACF,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAEtC,IAAI,kBAA6C,CAAC;QAClD,IAAI,iBAA4C,CAAC;QACjD,IAAI,iBAA4C,CAAC;QACjD,IAAI,6BAAwD,CAAC;QAE7D,IAAI,OAAO,EAAE,CAAC;YACZ,wEAAwE;YACxE,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;gBACrD,yBAAyB;gBACzB,IAAM,iBAAiB,GAAY,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,MAAM,CAAC;gBACpF,IAAI,aAAa,GAAY,KAAK,CAAC;gBAEnC,IAAI,GAAG,KAAK,CAAC;gBACb,OAAO,CAAC,IAAI,EAAE,CAAC;oBACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;wBACpC,KAAK,SAAS,CAAC,UAAU;4BACvB,wEAAwE;4BACxE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;wBAC5C,KAAK,SAAS,CAAC,KAAK;4BAClB,sGAAsG;4BACtG,IAAI,iBAAiB,IAAI,CAAC,aAAa,EAAE,CAAC;gCACxC,WAAW,CAAC,SAAS,EAAE,CAAC;gCACxB,aAAa,GAAG,IAAI,CAAC;4BACvB,CAAC;iCAAM,CAAC;gCACN,IAAI,GAAG,IAAI,CAAC;4BACd,CAAC;4BACD,MAAM;wBACR,KAAK,SAAS,CAAC,WAAW;4BACxB,IAAI,GAAG,IAAI,CAAC;4BACZ,MAAM;wBACR;4BACE,WAAW,CAAC,SAAS,EAAE,CAAC;oBAC5B,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,EAAE,CAAC;oBAC9C,kBAAkB,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;oBAE9D,oDAAoD;oBACpD,IAAM,WAAW,GAAuB,YAAY,CAAC,2BAA2B,CAC9E,kBAAkB,CAAC,QAAQ,EAAE,CAC9B,CAAC;oBACF,IAAI,WAAW,EAAE,CAAC;wBAChB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,6BAA6B,EAC5C,WAAW,EACX,kBAAkB,EAClB,mBAAmB,CACpB,CAAC;wBACF,OAAO,SAAS,CAAC;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,wBAAwB;YACxB,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,CAAC,IAAI,EAAE,CAAC;gBACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;oBACpC,KAAK,SAAS,CAAC,UAAU;wBACvB,wEAAwE;wBACxE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;oBAC5C,KAAK,SAAS,CAAC,WAAW;wBACxB,IAAI,GAAG,IAAI,CAAC;wBACZ,MAAM;oBACR;wBACE,WAAW,CAAC,SAAS,EAAE,CAAC;gBAC5B,CAAC;YACH,CAAC;YAED,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,EAAE,CAAC;gBAC9C,iBAAiB,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;gBAE7D,mDAAmD;gBACnD,IAAM,WAAW,GAAuB,YAAY,CAAC,0BAA0B,CAC7E,iBAAiB,CAAC,QAAQ,EAAE,EAC5B,CAAC,CAAC,kBAAkB,CACrB,CAAC;gBACF,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,4BAA4B,EAC3C,WAAW,EACX,iBAAiB,EACjB,mBAAmB,CACpB,CAAC;oBACF,OAAO,SAAS,CAAC;gBACnB,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;gBAC1D,yDAAyD;gBACzD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YACD,WAAW,CAAC,SAAS,EAAE,CAAC;YACxB,iBAAiB,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAE7D,6BAA6B,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;YAE7E,IAAI,kBAAkB,KAAK,SAAS,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBACxE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,mBAAmB,EAClC,sEAAsE,EACtE,iBAAiB,EACjB,mBAAmB,CACpB,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,IAAM,gBAAgB,GAAyB,EAAE,CAAC;QAElD,IAAI,GAAG,KAAK,CAAC;QACb,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,MAAM,CAAC;gBACtB,KAAK,SAAS,CAAC,eAAe,CAAC;gBAC/B,KAAK,SAAS,CAAC,SAAS,CAAC;gBACzB,KAAK,SAAS,CAAC,KAAK,CAAC;gBACrB,KAAK,SAAS,CAAC,iBAAiB,CAAC;gBACjC,KAAK,SAAS,CAAC,WAAW;oBACxB,IAAM,YAAY,GAAY,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;oBAC1D,IAAM,eAAe,GAAmC,IAAI,CAAC,qBAAqB,CAChF,WAAW,EACX,YAAY,EACZ,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;oBAEF,IAAI,CAAC,eAAe,EAAE,CAAC;wBACrB,OAAO,SAAS,CAAC;oBACnB,CAAC;oBAED,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBACvC,MAAM;gBACR;oBACE,IAAI,GAAG,IAAI,CAAC;YAChB,CAAC;QACH,CAAC;QAED,IACE,kBAAkB,KAAK,SAAS;YAChC,iBAAiB,KAAK,SAAS;YAC/B,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAC7B,CAAC;YACD,sDAAsD;YACtD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,gBAAgB,EAC/B,mCAAmC,EACnC,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;YACF,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,uBAAuB,CAAC;YACjC,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,kBAAkB,oBAAA;YAClB,iBAAiB,mBAAA;YAEjB,iBAAiB,mBAAA;YACjB,6BAA6B,+BAAA;YAE7B,gBAAgB,kBAAA;SACjB,CAAC,CAAC;IACL,CAAC;IAEO,0CAAqB,GAA7B,UACE,WAAwB,EACxB,YAAqB,EACrB,4BAA2C,EAC3C,mBAA4B;QAE5B,IAAM,UAAU,GAAwC;YACtD,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;SACnC,CAAC;QAEF,wBAAwB;QACxB,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;gBACrD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,mBAAmB,EAClC,yEAAyE,EACzE,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,WAAW,CAAC,SAAS,EAAE,CAAC;YACxB,UAAU,CAAC,UAAU,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAEjE,UAAU,CAAC,sBAAsB,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QACnF,CAAC;QAED,4CAA4C;QAC5C,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,eAAe,EAAE,CAAC;YAC9D,WAAW,CAAC,SAAS,EAAE,CAAC;YACxB,UAAU,CAAC,sBAAsB,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAE7E,UAAU,CAAC,kCAAkC,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAC/F,CAAC;QAED,uCAAuC;QACvC,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAChE,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;YACpF,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC7B,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CACvD,WAAW,EACX,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBACjC,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QACD,UAAU,CAAC,yBAAyB,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAEpF,iBAAiB;QACjB,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;YACpD,WAAW,CAAC,SAAS,EAAE,CAAC;YAExB,UAAU,CAAC,YAAY,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAEnE,UAAU,CAAC,wBAAwB,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;YAEnF,IAAI,CAAC,UAAU,CAAC,sBAAsB,EAAE,CAAC;gBACvC,oGAAoG;gBACpG,oGAAoG;gBACpG,qEAAqE;gBACrE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,8BAA8B,EAC7C,4FAA4F,EAC5F,UAAU,CAAC,YAAY,EACvB,mBAAmB,CACpB,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,8CAA8C;YAC9C,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAC7C,WAAW,EACX,UAAU,CAAC,YAAY,EACvB,mBAAmB,CACpB,CAAC;YACF,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACzB,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,UAAU,CAAC,2BAA2B,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QACxF,CAAC;aAAM,CAAC;YACN,IAAI,UAAU,CAAC,sBAAsB,EAAE,CAAC;gBACtC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,qBAAqB,EACpC,iFAAiF,EACjF,UAAU,CAAC,sBAAsB,EACjC,mBAAmB,CACpB,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,UAAU,CAAC,sBAAsB,EAAE,CAAC;YACtC,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,gBAAgB,EAAE,CAAC;gBAC/D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,0BAA0B,EACzC,wCAAwC,EACxC,UAAU,CAAC,sBAAsB,EACjC,mBAAmB,CACpB,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,WAAW,CAAC,SAAS,EAAE,CAAC;YAExB,UAAU,CAAC,uBAAuB,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAE9E,UAAU,CAAC,mCAAmC,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAChG,CAAC;QAED,OAAO,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC;IAEO,uCAAkB,GAA1B,UACE,WAAwB,EACxB,mBAA4B;QAE5B,eAAe;QACf,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAChE,0GAA0G;YAC1G,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAED,WAAW,CAAC,SAAS,EAAE,CAAC;QACxB,IAAM,kBAAkB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAEnF,IAAM,8BAA8B,GAClC,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAE/C,iCAAiC;QACjC,IAAM,oBAAoB,GAAwC,IAAI,CAAC,0BAA0B,CAC/F,WAAW,EACX,kBAAkB,EAClB,mBAAmB,CACpB,CAAC;QAEF,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,qBAAqB,EACpC,mDAAmD,EACnD,kBAAkB,EAClB,mBAAmB,CACpB,CAAC;YAEF,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,yGAAyG;QAEzG,eAAe;QACf,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,kBAAkB,EAAE,CAAC;YACjE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,4BAA4B,EAC3C,qDAAqD,EACrD,kBAAkB,EAClB,mBAAmB,CACpB,CAAC;YAEF,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,WAAW,CAAC,SAAS,EAAE,CAAC;QACxB,IAAM,mBAAmB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAEpF,OAAO,IAAI,eAAe,CAAC;YACzB,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,kBAAkB,oBAAA;YAClB,8BAA8B,gCAAA;YAC9B,eAAe,EAAE,oBAAoB;YACrC,mBAAmB,qBAAA;SACpB,CAAC,CAAC;IACL,CAAC;IAEO,2CAAsB,GAA9B,UACE,WAAwB,EACxB,4BAA2C,EAC3C,mBAA4B;QAE5B,IAAI,gBAAgB,GAA8B,SAAS,CAAC;QAC5D,IAAI,iBAAiB,GAA8B,SAAS,CAAC;QAE7D,+BAA+B;QAC/B,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1D,uBAAuB;YACvB,WAAW,CAAC,SAAS,EAAE,CAAC;YACxB,gBAAgB,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAE5D,kCAAkC;YAClC,OAAO,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;gBAC7D,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;oBACzD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,qBAAqB,EACpC,yDAAyD,EACzD,gBAAgB,EAChB,mBAAmB,CACpB,CAAC;oBACF,OAAO,SAAS,CAAC;gBACnB,CAAC;gBAED,WAAW,CAAC,SAAS,EAAE,CAAC;YAC1B,CAAC;YAED,IAAI,WAAW,CAAC,0BAA0B,EAAE,EAAE,CAAC;gBAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,wBAAwB,EACvC,uCAAuC,EACvC,gBAAgB,EAChB,mBAAmB,CACpB,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,IAAM,iBAAiB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAElF,uBAAuB;YACvB,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,iBAAiB;YAC1C,iBAAiB,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAE7D,OAAO,IAAI,mBAAmB,CAAC;gBAC7B,MAAM,EAAE,IAAI;gBACZ,aAAa,EAAE,IAAI,CAAC,cAAc;gBAElC,gBAAgB,kBAAA;gBAChB,iBAAiB,mBAAA;gBACjB,iBAAiB,mBAAA;aAClB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,sDAAsD;YAEtD,IAAI,IAAI,GAAY,KAAK,CAAC;YAC1B,OAAO,CAAC,IAAI,EAAE,CAAC;gBACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;oBACpC,KAAK,SAAS,CAAC,SAAS,CAAC;oBACzB,KAAK,SAAS,CAAC,UAAU;wBACvB,WAAW,CAAC,SAAS,EAAE,CAAC;wBACxB,MAAM;oBACR;wBACE,IAAI,GAAG,IAAI,CAAC;wBACZ,MAAM;gBACV,CAAC;YACH,CAAC;YAED,IAAI,WAAW,CAAC,0BAA0B,EAAE,EAAE,CAAC;gBAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,0BAA0B,EACzC,sEAAsE,EACtE,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,IAAM,iBAAiB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;YAClF,IAAM,UAAU,GAAW,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YAExD,IAAM,WAAW,GACf,YAAY,CAAC,wCAAwC,CAAC,UAAU,CAAC,CAAC;YACpE,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,2BAA2B,EAC1C,WAAW,EACX,iBAAiB,EACjB,mBAAmB,CACpB,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,OAAO,IAAI,mBAAmB,CAAC;gBAC7B,MAAM,EAAE,IAAI;gBACZ,aAAa,EAAE,IAAI,CAAC,cAAc;gBAElC,gBAAgB,kBAAA;gBAChB,iBAAiB,mBAAA;gBACjB,iBAAiB,mBAAA;aAClB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,yCAAoB,GAA5B,UACE,WAAwB,EACxB,4BAA2C,EAC3C,mBAA4B;QAE5B,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;YACxD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,qBAAqB,EACpC,4CAA4C,EAC5C,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;QACJ,CAAC;QAED,IAAM,QAAQ,GAAW,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC5D,IAAM,eAAe,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAEhF,IAAM,iBAAiB,GAAsB,IAAI,iBAAiB,CAAC;YACjE,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,eAAe,iBAAA;YACf,QAAQ,UAAA;SACT,CAAC,CAAC;QAEH,IAAI,iBAAiB,CAAC,YAAY,EAAE,CAAC;YACnC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAChD,cAAc,CAAC,uBAAuB,EACtC,iBAAiB,CAAC,YAAY,EAC9B,eAAe,EACf,mBAAmB,CACpB,CAAC;YACF,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,uCAAkB,GAA1B,UAA2B,WAAwB;QACjD,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAC/C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAElD,yBAAyB;QACzB,IAAM,aAAa,GAAU,WAAW,CAAC,SAAS,EAAE,CAAC;QACrD,IAAI,aAAa,CAAC,IAAI,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC9C,mGAAmG;YACnG,gBAAgB;YAChB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,4DAA4D;QAE5D,IAAM,uBAAuB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAExF,wBAAwB;QACxB,IAAM,WAAW,GAAmC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QACrF,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,kCAAkC,CAC5C,WAAW,EACX,MAAM,EACN,wBAAwB,EACxB,WAAW,CACZ,CAAC;QACJ,CAAC;QAED,IAAM,uBAAuB,GAA8B,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAExG,IAAM,cAAc,GAAuB,EAAE,CAAC;QAE9C,iDAAiD;QACjD,OAAO,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;YAC3D,qBAAqB;YACrB,IAAM,aAAa,GAAsC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAC/F,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,kCAAkC,CAC5C,WAAW,EACX,MAAM,EACN,6CAA6C,EAC7C,aAAa,CACd,CAAC;YACJ,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrC,CAAC;QAED,qDAAqD;QACrD,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAC/C,IAAM,kBAAkB,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAE9D,IAAI,cAAc,GAAY,KAAK,CAAC;QACpC,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;YACpD,WAAW,CAAC,SAAS,EAAE,CAAC;YACxB,cAAc,GAAG,IAAI,CAAC;QACxB,CAAC;QACD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1D,IAAM,OAAO,GAAa,IAAI,CAAC,4BAA4B,CACzD,WAAW,EACX,cAAc,CAAC,yBAAyB,EACxC,uCAAuC,EACvC,kBAAkB,CACnB,CAAC;YACF,OAAO,IAAI,CAAC,kCAAkC,CAC5C,WAAW,EACX,MAAM,EACN,mCAAmC,EACnC,OAAO,CACR,CAAC;QACJ,CAAC;QACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,IAAM,uBAAuB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAExF,iFAAiF;QACjF,uCAAuC;QAEvC,OAAO,IAAI,eAAe,CAAC;YACzB,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,uBAAuB,yBAAA;YAEvB,WAAW,aAAA;YACX,uBAAuB,yBAAA;YAEvB,cAAc,gBAAA;YAEd,cAAc,gBAAA;YAEd,uBAAuB,yBAAA;SACxB,CAAC,CAAC;IACL,CAAC;IAEO,wCAAmB,GAA3B,UAA4B,WAAwB;QAClD,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAE/C,0BAA0B;QAC1B,IAAM,WAAW,GAAmC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QACrF,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3B,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,IAAM,uBAAuB,GAA8B,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAExG,kBAAkB;QAClB,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,sBAAsB,CAChC,WAAW,EACX,cAAc,CAAC,oBAAoB,EACnC,yCAAyC,CAC1C,CAAC;QACJ,CAAC;QACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,IAAM,aAAa,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAE9E,IAAM,yBAAyB,GAA8B,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAE1G,2BAA2B;QAC3B,IAAM,cAAc,GAA4B,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACnF,IAAI,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC;YAC9B,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,IAAM,YAAY,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAE7E,IAAM,wBAAwB,GAA8B,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAEzG,OAAO,IAAI,gBAAgB,CAAC;YAC1B,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,WAAW,aAAA;YACX,uBAAuB,yBAAA;YAEvB,aAAa,eAAA;YACb,yBAAyB,2BAAA;YAEzB,YAAY,cAAA;YACZ,wBAAwB,0BAAA;SACzB,CAAC,CAAC;IACL,CAAC;IAEO,qCAAgB,GAAxB,UAAyB,WAAwB;QAC/C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAClD,IAAM,cAAc,GAAc,WAAW,CAAC,aAAa,EAAE,CAAC;QAC9D,IAAI,cAAc,KAAK,SAAS,CAAC,WAAW,IAAI,cAAc,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;YACzF,OAAO,IAAI,CAAC,sBAAsB,CAChC,WAAW,EACX,cAAc,CAAC,oBAAoB,EACnC,iFAAiF,CAClF,CAAC;QACJ,CAAC;QACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,IAAI,iBAAiB,GAAW,EAAE,CAAC;QAEnC,SAAS,CAAC;YACR,IAAM,eAAe,GAAc,WAAW,CAAC,aAAa,EAAE,CAAC;YAC/D,kCAAkC;YAClC,IAAI,eAAe,KAAK,cAAc,EAAE,CAAC;gBACvC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,oBAAoB;gBAC7C,MAAM;YACR,CAAC;YACD,IAAI,eAAe,KAAK,SAAS,CAAC,UAAU,IAAI,eAAe,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;gBACtF,OAAO,IAAI,CAAC,sBAAsB,CAChC,WAAW,EACX,cAAc,CAAC,sBAAsB,EACrC,8CAA8C,EAC9C,MAAM,CACP,CAAC;YACJ,CAAC;YACD,iBAAiB,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC1D,CAAC;QAED,6DAA6D;QAC7D,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;YACxD,OAAO,IAAI,CAAC,sBAAsB,CAChC,WAAW,EACX,cAAc,CAAC,mBAAmB,EAClC,yEAAyE,CAC1E,CAAC;QACJ,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,qCAAgB,GAAxB,UAAyB,WAAwB;QAC/C,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAC/C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAElD,0BAA0B;QAC1B,IAAM,aAAa,GAAU,WAAW,CAAC,SAAS,EAAE,CAAC;QACrD,IAAI,aAAa,CAAC,IAAI,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,iBAAiB,EAChC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,IAAM,UAAU,GAAU,WAAW,CAAC,SAAS,EAAE,CAAC;QAClD,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,iBAAiB,EAChC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,sCAAsC;QACtC,mDAAmD;QAEnD,IAAM,uBAAuB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAExF,oBAAoB;QACpB,IAAM,WAAW,GAAmC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QACrF,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,kCAAkC,CAC5C,WAAW,EACX,MAAM,EACN,kCAAkC,EAClC,WAAW,CACZ,CAAC;QACJ,CAAC;QAED,IAAM,uBAAuB,GAA8B,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAExG,uBAAuB;QACvB,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1D,IAAM,OAAO,GAAa,IAAI,CAAC,sBAAsB,CACnD,WAAW,EACX,cAAc,CAAC,yBAAyB,EACxC,0CAA0C,CAC3C,CAAC;YACF,OAAO,IAAI,CAAC,kCAAkC,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QACnF,CAAC;QACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,IAAM,uBAAuB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAExF,OAAO,IAAI,aAAa,CAAC;YACvB,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,uBAAuB,yBAAA;YAEvB,WAAW,aAAA;YACX,uBAAuB,yBAAA;YAEvB,uBAAuB,yBAAA;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,mCAAc,GAAtB,UAAuB,WAAwB;QAC7C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAElD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC,4BAA4B,CACtC,WAAW,EACX,cAAc,CAAC,iBAAiB,EAChC,6BAA6B,EAC7B,MAAM,CACP,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,GAAY,KAAK,CAAC;QAC1B,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,MAAM,CAAC;gBACtB,KAAK,SAAS,CAAC,MAAM,CAAC;gBACtB,KAAK,SAAS,CAAC,SAAS;oBACtB,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;gBACR;oBACE,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;YACV,CAAC;QACH,CAAC;QACD,IAAM,OAAO,GAA8B,WAAW,CAAC,6BAA6B,EAAE,CAAC;QAEvF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,sBAAsB,CAChC,WAAW,EACX,cAAc,CAAC,iBAAiB,EAChC,wBAAwB,CACzB,CAAC;QACJ,CAAC;QAED,IAAM,QAAQ,GAAW,OAAO,CAAC,QAAQ,EAAE,CAAC;QAE5C,IAAM,WAAW,GAAuB,YAAY,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAExF,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,4BAA4B,CACtC,WAAW,EACX,cAAc,CAAC,iBAAiB,EAChC,WAAW,EACX,MAAM,CACP,CAAC;QACJ,CAAC;QAED,IACE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,6BAA6B;YAC5D,CAAC,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EACrD,CAAC;YACD,OAAO,IAAI,CAAC,sBAAsB,CAChC,WAAW,EACX,cAAc,CAAC,0BAA0B,EACzC,gCAAyB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,gDAA6C,EAC9F,MAAM,CACP,CAAC;QACJ,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,qCAAgB,GAAxB,UAAyB,WAAwB;QAC/C,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAE/C,IAAM,WAAW,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QACvD,IAAM,2BAA2B,GAAW,WAAW,GAAG,CAAC,CAAC;QAE5D,QAAQ,WAAW,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAC5C,KAAK,SAAS,CAAC,OAAO,CAAC;YACvB,KAAK,SAAS,CAAC,UAAU;gBACvB,MAAM;YACR;gBACE,OAAO,IAAI,CAAC,6BAA6B,CACvC,WAAW,EACX,WAAW;gBACX,6EAA6E;gBAC7E,2BAA2B,EAC3B,cAAc,CAAC,sBAAsB,EACrC,4EAA4E,CAC7E,CAAC;QACN,CAAC;QAED,iCAAiC;QACjC,IAAI,gBAAgB,GAAW,EAAE,CAAC;QAClC,gBAAgB,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;QAC5C,gBAAgB,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;QAC5C,gBAAgB,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;QAE5C,IAAI,gBAAgB,KAAK,KAAK,EAAE,CAAC;YAC/B,iGAAiG;YACjG,iDAAiD;YACjD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAM,mBAAmB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAEpF,uCAAuC;QACvC,2EAA2E;QAC3E,OAAO,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;YACzD,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC;QAED,IAAM,+BAA+B,GACnC,WAAW,CAAC,6BAA6B,EAAE,CAAC;QAE9C,uDAAuD;QACvD,IAAI,IAAI,GAAY,KAAK,CAAC;QAC1B,IAAI,oBAAoB,GAAuB,SAAS,CAAC;QACzD,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,OAAO,CAAC;gBACvB,KAAK,SAAS,CAAC,OAAO;oBACpB,IAAI,oBAAoB,KAAK,SAAS,EAAE,CAAC;wBACvC,2CAA2C;wBAC3C,oBAAoB,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;oBACpD,CAAC;oBACD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;wBACtD,IAAI,GAAG,IAAI,CAAC;oBACd,CAAC;oBACD,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;gBACR,KAAK,SAAS,CAAC,QAAQ;oBACrB,IAAM,OAAO,GAAa,IAAI,CAAC,sBAAsB,CACnD,WAAW,EACX,cAAc,CAAC,wBAAwB,EACvC,2DAA2D,CAC5D,CAAC;oBACF,OAAO,IAAI,CAAC,uCAAuC,CACjD,WAAW,EACX,WAAW,EACX,2BAA2B,EAC3B,4BAA4B,EAC5B,OAAO,CACR,CAAC;gBACJ,KAAK,SAAS,CAAC,UAAU;oBACvB,IAAM,QAAQ,GAAa,IAAI,CAAC,sBAAsB,CACpD,WAAW,EACX,cAAc,CAAC,yBAAyB,EACxC,2BAA2B,CAC5B,CAAC;oBACF,OAAO,IAAI,CAAC,uCAAuC,CACjD,WAAW,EACX,WAAW,EACX,2BAA2B,EAC3B,4BAA4B,EAC5B,QAAQ,CACT,CAAC;gBACJ;oBACE,2BAA2B;oBAC3B,oBAAoB,GAAG,SAAS,CAAC;oBACjC,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;YACV,CAAC;QACH,CAAC;QAED,oEAAoE;QACpE,+BAA+B;QAC/B,IAAM,iBAAiB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAElF,yBAAyB;QACzB,IAAM,eAAe,GAAkB,iBAAiB,CAAC,cAAc,CACrE,iBAAiB,CAAC,UAAU,EAC5B,oBAAqB,CACtB,CAAC;QAEF,oBAAoB;QACpB,IAAM,2BAA2B,GAA8B,iBAAiB,CAAC,cAAc,CAC7F,oBAAqB,EACrB,iBAAiB,CAAC,QAAQ,CAC3B,CAAC;QAEF,+DAA+D;QAC/D,IAAI,aAAa,GAAW,CAAC,CAAC,CAAC;QAC/B,IAAI,uBAAuB,GAAW,CAAC,CAAC,CAAC;QACzC,IAAI,GAAG,KAAK,CAAC;QACb,IAAI,oBAA2B,CAAC;QAChC,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,UAAU;oBACvB,IAAM,QAAQ,GAAa,IAAI,CAAC,sBAAsB,CACpD,WAAW,EACX,cAAc,CAAC,yBAAyB,EACxC,2BAA2B,CAC5B,CAAC;oBACF,OAAO,IAAI,CAAC,uCAAuC,CACjD,WAAW,EACX,WAAW,EACX,2BAA2B,EAC3B,4BAA4B,EAC5B,QAAQ,CACT,CAAC;gBACJ,KAAK,SAAS,CAAC,OAAO;oBACpB,oBAAoB,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;oBAC/C,aAAa,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;oBAE3C,OAAO,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;wBACzD,oBAAoB,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;oBACjD,CAAC;oBAED,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;wBACvD,MAAM;oBACR,CAAC;oBACD,uBAAuB,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;oBACrD,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,iBAAiB;oBAE1C,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;wBACvD,MAAM;oBACR,CAAC;oBACD,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,kBAAkB;oBAE3C,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;wBACvD,MAAM;oBACR,CAAC;oBACD,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,iBAAiB;oBAE1C,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;gBACR;oBACE,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAI,oBAAqB,CAAC,IAAI,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;YACrD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,sBAAsB,CAC5C,cAAc,CAAC,sBAAsB,EACrC,6DAA6D,EAC7D,oBAAqB,CAAC,KAAK,CAC5B,CAAC;QACJ,CAAC;QAED,mCAAmC;QACnC,IAAM,uBAAuB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAExF,8BAA8B;QAC9B,IAAM,WAAW,GAAkB,uBAAuB,CAAC,cAAc,CACvE,uBAAuB,CAAC,UAAU,EAClC,aAAa,CACd,CAAC;QAEF,gBAAgB;QAChB,IAAM,gCAAgC,GACpC,uBAAuB,CAAC,cAAc,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;QAEjF,iBAAiB;QACjB,IAAM,mBAAmB,GAAkB,uBAAuB,CAAC,cAAc,CAC/E,uBAAuB,EACvB,uBAAuB,CAAC,QAAQ,CACjC,CAAC;QAEF,2DAA2D;QAC3D,IAAI,GAAG,KAAK,CAAC;QACb,OAAO,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,OAAO;oBACpB,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;gBACR,KAAK,SAAS,CAAC,OAAO;oBACpB,IAAI,GAAG,IAAI,CAAC;oBACZ,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;gBACR,KAAK,SAAS,CAAC,UAAU;oBACvB,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;gBACR;oBACE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,sBAAsB,CAC5C,cAAc,CAAC,sBAAsB,EACrC,8DAA8D,EAC9D,WAAW,CAAC,SAAS,EAAE,CAAC,KAAK,CAC9B,CAAC;oBACF,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;YACV,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAM,+BAA+B,GACnC,WAAW,CAAC,6BAA6B,EAAE,CAAC;QAE9C,OAAO,IAAI,aAAa,CAAC;YACvB,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,mBAAmB,qBAAA;YACnB,+BAA+B,iCAAA;YAE/B,eAAe,iBAAA;YACf,2BAA2B,6BAAA;YAE3B,WAAW,aAAA;YAEX,gCAAgC,kCAAA;YAChC,mBAAmB,qBAAA;YACnB,+BAA+B,iCAAA;SAChC,CAAC,CAAC;IACL,CAAC;IAEO,mCAAc,GAAtB,UAAuB,WAAwB;QAC7C,WAAW,CAAC,gCAAgC,EAAE,CAAC;QAC/C,IAAM,MAAM,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QAElD,6BAA6B;QAC7B,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvD,+FAA+F;YAC/F,iDAAiD;YACjD,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;QAClF,CAAC;QAED,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,oBAAoB;QAE7C,IAAM,uBAAuB,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAExF,IAAI,WAAW,GAA8B,SAAS,CAAC;QACvD,IAAI,uBAAuB,GAA8B,SAAS,CAAC;QAEnE,6BAA6B;QAC7B,SAAS,CAAC;YACR,IAAM,eAAe,GAAc,WAAW,CAAC,aAAa,EAAE,CAAC;YAC/D,kCAAkC;YAClC,IAAI,eAAe,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC3C,IAAI,WAAW,CAAC,0BAA0B,EAAE,EAAE,CAAC;oBAC7C,OAAO,IAAI,CAAC,6BAA6B,CACvC,WAAW,EACX,MAAM,EACN,MAAM,GAAG,CAAC,EACV,cAAc,CAAC,aAAa,EAC5B,uEAAuE,CACxE,CAAC;gBACJ,CAAC;gBAED,WAAW,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;gBAEvD,WAAW,CAAC,SAAS,EAAE,CAAC;gBACxB,uBAAuB,GAAG,WAAW,CAAC,0BAA0B,EAAE,CAAC;gBACnE,MAAM;YACR,CAAC;YACD,IAAI,eAAe,KAAK,SAAS,CAAC,UAAU,IAAI,eAAe,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;gBACtF,OAAO,IAAI,CAAC,wBAAwB,CAClC,WAAW,EACX,MAAM,EACN,cAAc,CAAC,wBAAwB,EACvC,+CAA+C,CAChD,CAAC;YACJ,CAAC;YACD,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,IAAI,WAAW,CAAC;YACrB,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,uBAAuB,yBAAA;YAEvB,WAAW,aAAA;YAEX,uBAAuB,yBAAA;SACxB,CAAC,CAAC;IACL,CAAC;IAEO,+CAA0B,GAAlC,UAAmC,WAAwB;QACzD,IAAI,IAAI,GAAY,KAAK,CAAC;QAC1B,GAAG,CAAC;YACF,QAAQ,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC;gBACpC,KAAK,SAAS,CAAC,OAAO,CAAC;gBACvB,KAAK,SAAS,CAAC,OAAO;oBACpB,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxB,MAAM;gBACR;oBACE,IAAI,GAAG,IAAI,CAAC;oBACZ,MAAM;YACV,CAAC;QACH,CAAC,QAAQ,CAAC,IAAI,EAAE;QAChB,OAAO,WAAW,CAAC,6BAA6B,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,iCAAY,GAApB,UACE,WAAwB,EACxB,SAAyB,EACzB,YAAoB;QAEpB,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,IAAM,WAAW,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAE5E,IAAM,YAAY,GAAiB,IAAI,YAAY,CAAC;YAClD,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,WAAW,aAAA;YAEX,SAAS,WAAA;YACT,YAAY,cAAA;YACZ,aAAa,EAAE,WAAW;SAC3B,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,6CAAwB,GAAhC,UACE,WAAwB,EACxB,MAAc,EACd,SAAyB,EACzB,YAAoB;QAEpB,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IACjE,CAAC;IAED;;;OAGG;IACK,kDAA6B,GAArC,UACE,WAAwB,EACxB,gBAAwB,EACxB,uBAA+B,EAC/B,SAAyB,EACzB,YAAoB;QAEpB,WAAW,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChD,OAAO,WAAW,CAAC,YAAY,EAAE,KAAK,uBAAuB,EAAE,CAAC;YAC9D,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC;QACD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;YACzD,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC;QAED,IAAM,WAAW,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAE5E,IAAM,YAAY,GAAiB,IAAI,YAAY,CAAC;YAClD,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,WAAW,aAAA;YAEX,SAAS,WAAA;YACT,YAAY,EAAE,YAAY;YAC1B,aAAa,EAAE,WAAW;SAC3B,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;OAGG;IACK,uDAAkC,GAA1C,UACE,WAAwB,EACxB,MAAc,EACd,kBAA0B,EAC1B,OAAiB;QAEjB,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACtC,WAAW,CAAC,SAAS,EAAE,CAAC;QAExB,IAAM,WAAW,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAE5E,IAAM,YAAY,GAAiB,IAAI,YAAY,CAAC;YAClD,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,WAAW,aAAA;YAEX,SAAS,EAAE,OAAO,CAAC,gBAAgB;YACnC,YAAY,EAAE,kBAAkB,GAAG,OAAO,CAAC,cAAc;YACzD,aAAa,EAAE,OAAO,CAAC,eAAe;SACvC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;OAGG;IACK,4DAAuC,GAA/C,UACE,WAAwB,EACxB,gBAAwB,EACxB,uBAA+B,EAC/B,kBAA0B,EAC1B,OAAiB;QAEjB,WAAW,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChD,OAAO,WAAW,CAAC,YAAY,EAAE,KAAK,uBAAuB,EAAE,CAAC;YAC9D,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC;QACD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;YACzD,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,CAAC;QAED,IAAM,WAAW,GAAkB,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAE5E,IAAM,YAAY,GAAiB,IAAI,YAAY,CAAC;YAClD,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI,CAAC,cAAc;YAElC,WAAW,aAAA;YAEX,SAAS,EAAE,OAAO,CAAC,gBAAgB;YACnC,YAAY,EAAE,kBAAkB,GAAG,OAAO,CAAC,cAAc;YACzD,aAAa,EAAE,OAAO,CAAC,eAAe;SACvC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;OAGG;IACK,2CAAsB,GAA9B,UACE,WAAwB,EACxB,gBAAgC,EAChC,cAAsB,EACtB,WAAoB;QAEpB,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,WAAW,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;QAC3C,CAAC;QAED,IAAM,aAAa,GAAkB,IAAI,aAAa,CAAC;YACrD,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,UAAU,EAAE,WAAW;YACvB,QAAQ,EAAE,WAAW,GAAG,CAAC;SAC1B,CAAC,CAAC;QAEH,OAAO;YACL,gBAAgB,kBAAA;YAChB,cAAc,gBAAA;YACd,eAAe,EAAE,aAAa;SAC/B,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,iDAA4B,GAApC,UACE,WAAwB,EACxB,gBAAgC,EAChC,cAAsB,EACtB,WAAmB;QAEnB,IAAI,SAAS,GAAW,WAAW,CAAC,YAAY,EAAE,CAAC;QACnD,IAAI,SAAS,GAAG,WAAW,EAAE,CAAC;YAC5B,6BAA6B;YAC7B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,SAAS,KAAK,WAAW,EAAE,CAAC;YAC9B,EAAE,SAAS,CAAC;QACd,CAAC;QAED,IAAM,aAAa,GAAkB,IAAI,aAAa,CAAC;YACrD,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,UAAU,EAAE,WAAW;YACvB,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC;QAEH,OAAO;YACL,gBAAgB,kBAAA;YAChB,cAAc,gBAAA;YACd,eAAe,EAAE,aAAa;SAC/B,CAAC;IACJ,CAAC;IACH,iBAAC;AAAD,CAAC,AA7hFD,IA6hFC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\n/* eslint-disable max-lines */\r\n\r\nimport type { ParserContext } from './ParserContext';\r\nimport { type Token, TokenKind } from './Token';\r\nimport { Tokenizer } from './Tokenizer';\r\nimport {\r\n  DocBlockTag,\r\n  DocCodeSpan,\r\n  DocErrorText,\r\n  DocEscapedText,\r\n  DocHtmlAttribute,\r\n  DocHtmlEndTag,\r\n  DocHtmlStartTag,\r\n  DocInlineTag,\r\n  type DocNode,\r\n  DocPlainText,\r\n  DocSoftBreak,\r\n  EscapeStyle,\r\n  type DocComment,\r\n  DocBlock,\r\n  DocNodeKind,\r\n  type DocSection,\r\n  DocParamBlock,\r\n  DocFencedCode,\r\n  DocLinkTag,\r\n  type IDocLinkTagParameters,\r\n  DocMemberReference,\r\n  DocDeclarationReference,\r\n  DocMemberSymbol,\r\n  DocMemberIdentifier,\r\n  DocMemberSelector,\r\n  DocInheritDocTag,\r\n  type IDocInheritDocTagParameters,\r\n  type IDocInlineTagParsedParameters,\r\n  type DocInlineTagBase,\r\n  type IDocLinkTagParsedParameters,\r\n  type IDocMemberReferenceParsedParameters\r\n} from '../nodes';\r\nimport { TokenSequence } from './TokenSequence';\r\nimport { TokenReader } from './TokenReader';\r\nimport { StringChecks } from './StringChecks';\r\nimport type { ModifierTagSet } from '../details/ModifierTagSet';\r\nimport type { TSDocConfiguration } from '../configuration/TSDocConfiguration';\r\nimport { type TSDocTagDefinition, TSDocTagSyntaxKind } from '../configuration/TSDocTagDefinition';\r\nimport { StandardTags } from '../details/StandardTags';\r\nimport { PlainTextEmitter } from '../emitters/PlainTextEmitter';\r\nimport { TSDocMessageId } from './TSDocMessageId';\r\n\r\ninterface IFailure {\r\n  // (We use \"failureMessage\" instead of \"errorMessage\" here so that DocErrorText doesn't\r\n  // accidentally implement this interface.)\r\n  failureMessageId: TSDocMessageId;\r\n  failureMessage: string;\r\n  failureLocation: TokenSequence;\r\n}\r\n\r\ntype ResultOrFailure<T> = T | IFailure;\r\n\r\nfunction isFailure<T>(resultOrFailure: ResultOrFailure<T>): resultOrFailure is IFailure {\r\n  return resultOrFailure !== undefined && Object.hasOwnProperty.call(resultOrFailure, 'failureMessage');\r\n}\r\n\r\n/**\r\n * The main parser for TSDoc comments.\r\n */\r\nexport class NodeParser {\r\n  private readonly _parserContext: ParserContext;\r\n  private readonly _configuration: TSDocConfiguration;\r\n  private _currentSection: DocSection;\r\n\r\n  public constructor(parserContext: ParserContext) {\r\n    this._parserContext = parserContext;\r\n    this._configuration = parserContext.configuration;\r\n\r\n    this._currentSection = parserContext.docComment.summarySection;\r\n  }\r\n\r\n  public parse(): void {\r\n    const tokenReader: TokenReader = new TokenReader(this._parserContext);\r\n\r\n    let done: boolean = false;\r\n    while (!done) {\r\n      // Extract the next token\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.EndOfInput:\r\n          done = true;\r\n          break;\r\n        case TokenKind.Newline:\r\n          this._pushAccumulatedPlainText(tokenReader);\r\n          tokenReader.readToken();\r\n          this._pushNode(\r\n            new DocSoftBreak({\r\n              parsed: true,\r\n              configuration: this._configuration,\r\n              softBreakExcerpt: tokenReader.extractAccumulatedSequence()\r\n            })\r\n          );\r\n          break;\r\n        case TokenKind.Backslash:\r\n          this._pushAccumulatedPlainText(tokenReader);\r\n          this._pushNode(this._parseBackslashEscape(tokenReader));\r\n          break;\r\n        case TokenKind.AtSign:\r\n          this._pushAccumulatedPlainText(tokenReader);\r\n          this._parseAndPushBlock(tokenReader);\r\n          break;\r\n        case TokenKind.LeftCurlyBracket: {\r\n          this._pushAccumulatedPlainText(tokenReader);\r\n\r\n          const marker: number = tokenReader.createMarker();\r\n          const docNode: DocNode = this._parseInlineTag(tokenReader);\r\n          const docComment: DocComment = this._parserContext.docComment;\r\n\r\n          if (docNode instanceof DocInheritDocTag) {\r\n            // The @inheritDoc tag is irregular because it looks like an inline tag, but\r\n            // it actually represents the entire comment body\r\n            const tagEndMarker: number = tokenReader.createMarker() - 1;\r\n            if (docComment.inheritDocTag === undefined) {\r\n              this._parserContext.docComment.inheritDocTag = docNode;\r\n            } else {\r\n              this._pushNode(\r\n                this._backtrackAndCreateErrorRange(\r\n                  tokenReader,\r\n                  marker,\r\n                  tagEndMarker,\r\n                  TSDocMessageId.ExtraInheritDocTag,\r\n                  'A doc comment cannot have more than one @inheritDoc tag'\r\n                )\r\n              );\r\n            }\r\n          } else {\r\n            this._pushNode(docNode);\r\n          }\r\n          break;\r\n        }\r\n        case TokenKind.RightCurlyBracket:\r\n          this._pushAccumulatedPlainText(tokenReader);\r\n          this._pushNode(\r\n            this._createError(\r\n              tokenReader,\r\n              TSDocMessageId.EscapeRightBrace,\r\n              'The \"}\" character should be escaped using a backslash to avoid confusion with a TSDoc inline tag'\r\n            )\r\n          );\r\n          break;\r\n        case TokenKind.LessThan:\r\n          this._pushAccumulatedPlainText(tokenReader);\r\n          // Look ahead two tokens to see if this is \"<a>\" or \"</a>\".\r\n          if (tokenReader.peekTokenAfterKind() === TokenKind.Slash) {\r\n            this._pushNode(this._parseHtmlEndTag(tokenReader));\r\n          } else {\r\n            this._pushNode(this._parseHtmlStartTag(tokenReader));\r\n          }\r\n          break;\r\n        case TokenKind.GreaterThan:\r\n          this._pushAccumulatedPlainText(tokenReader);\r\n          this._pushNode(\r\n            this._createError(\r\n              tokenReader,\r\n              TSDocMessageId.EscapeGreaterThan,\r\n              'The \">\" character should be escaped using a backslash to avoid confusion with an HTML tag'\r\n            )\r\n          );\r\n          break;\r\n        case TokenKind.Backtick:\r\n          this._pushAccumulatedPlainText(tokenReader);\r\n\r\n          if (\r\n            tokenReader.peekTokenAfterKind() === TokenKind.Backtick &&\r\n            tokenReader.peekTokenAfterAfterKind() === TokenKind.Backtick\r\n          ) {\r\n            this._pushNode(this._parseFencedCode(tokenReader));\r\n          } else {\r\n            this._pushNode(this._parseCodeSpan(tokenReader));\r\n          }\r\n          break;\r\n        default:\r\n          // If nobody recognized this token, then accumulate plain text\r\n          tokenReader.readToken();\r\n          break;\r\n      }\r\n    }\r\n    this._pushAccumulatedPlainText(tokenReader);\r\n    this._performValidationChecks();\r\n  }\r\n\r\n  private _performValidationChecks(): void {\r\n    const docComment: DocComment = this._parserContext.docComment;\r\n    if (docComment.deprecatedBlock) {\r\n      if (!PlainTextEmitter.hasAnyTextContent(docComment.deprecatedBlock)) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.MissingDeprecationMessage,\r\n          `The ${docComment.deprecatedBlock.blockTag.tagName} block must include a deprecation message,` +\r\n            ` e.g. describing the recommended alternative`,\r\n          docComment.deprecatedBlock.blockTag.getTokenSequence(),\r\n          docComment.deprecatedBlock\r\n        );\r\n      }\r\n    }\r\n\r\n    if (docComment.inheritDocTag) {\r\n      if (docComment.remarksBlock) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.InheritDocIncompatibleTag,\r\n          `A \"${docComment.remarksBlock.blockTag.tagName}\" block must not be used, because that` +\r\n            ` content is provided by the @inheritDoc tag`,\r\n          docComment.remarksBlock.blockTag.getTokenSequence(),\r\n          docComment.remarksBlock.blockTag\r\n        );\r\n      }\r\n      if (PlainTextEmitter.hasAnyTextContent(docComment.summarySection)) {\r\n        this._parserContext.log.addMessageForTextRange(\r\n          TSDocMessageId.InheritDocIncompatibleSummary,\r\n          'The summary section must not have any content, because that' +\r\n            ' content is provided by the @inheritDoc tag',\r\n          this._parserContext.commentRange\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  private _validateTagDefinition(\r\n    tagDefinition: TSDocTagDefinition | undefined,\r\n    tagName: string,\r\n    expectingInlineTag: boolean,\r\n    tokenSequenceForErrorContext: TokenSequence,\r\n    nodeForErrorContext: DocNode\r\n  ): void {\r\n    if (tagDefinition) {\r\n      const isInlineTag: boolean = tagDefinition.syntaxKind === TSDocTagSyntaxKind.InlineTag;\r\n\r\n      if (isInlineTag !== expectingInlineTag) {\r\n        // The tag is defined, but it is used incorrectly\r\n        if (expectingInlineTag) {\r\n          this._parserContext.log.addMessageForTokenSequence(\r\n            TSDocMessageId.TagShouldNotHaveBraces,\r\n            `The TSDoc tag \"${tagName}\" is not an inline tag; it must not be enclosed in \"{ }\" braces`,\r\n            tokenSequenceForErrorContext,\r\n            nodeForErrorContext\r\n          );\r\n        } else {\r\n          this._parserContext.log.addMessageForTokenSequence(\r\n            TSDocMessageId.InlineTagMissingBraces,\r\n            `The TSDoc tag \"${tagName}\" is an inline tag; it must be enclosed in \"{ }\" braces`,\r\n            tokenSequenceForErrorContext,\r\n            nodeForErrorContext\r\n          );\r\n        }\r\n      } else {\r\n        if (this._parserContext.configuration.validation.reportUnsupportedTags) {\r\n          if (!this._parserContext.configuration.isTagSupported(tagDefinition)) {\r\n            // The tag is defined, but not supported\r\n            this._parserContext.log.addMessageForTokenSequence(\r\n              TSDocMessageId.UnsupportedTag,\r\n              `The TSDoc tag \"${tagName}\" is not supported by this tool`,\r\n              tokenSequenceForErrorContext,\r\n              nodeForErrorContext\r\n            );\r\n          }\r\n        }\r\n      }\r\n    } else {\r\n      // The tag is not defined\r\n      if (!this._parserContext.configuration.validation.ignoreUndefinedTags) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.UndefinedTag,\r\n          `The TSDoc tag \"${tagName}\" is not defined in this configuration`,\r\n          tokenSequenceForErrorContext,\r\n          nodeForErrorContext\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  private _pushAccumulatedPlainText(tokenReader: TokenReader): void {\r\n    if (!tokenReader.isAccumulatedSequenceEmpty()) {\r\n      this._pushNode(\r\n        new DocPlainText({\r\n          parsed: true,\r\n          configuration: this._configuration,\r\n          textExcerpt: tokenReader.extractAccumulatedSequence()\r\n        })\r\n      );\r\n    }\r\n  }\r\n\r\n  private _parseAndPushBlock(tokenReader: TokenReader): void {\r\n    const docComment: DocComment = this._parserContext.docComment;\r\n    const configuration: TSDocConfiguration = this._parserContext.configuration;\r\n    const modifierTagSet: ModifierTagSet = docComment.modifierTagSet;\r\n\r\n    const parsedBlockTag: DocNode = this._parseBlockTag(tokenReader);\r\n    if (parsedBlockTag.kind !== DocNodeKind.BlockTag) {\r\n      this._pushNode(parsedBlockTag);\r\n      return;\r\n    }\r\n\r\n    const docBlockTag: DocBlockTag = parsedBlockTag as DocBlockTag;\r\n\r\n    // Do we have a definition for this tag?\r\n    const tagDefinition: TSDocTagDefinition | undefined = configuration.tryGetTagDefinitionWithUpperCase(\r\n      docBlockTag.tagNameWithUpperCase\r\n    );\r\n    this._validateTagDefinition(\r\n      tagDefinition,\r\n      docBlockTag.tagName,\r\n      /* expectingInlineTag */ false,\r\n      docBlockTag.getTokenSequence(),\r\n      docBlockTag\r\n    );\r\n\r\n    if (tagDefinition) {\r\n      switch (tagDefinition.syntaxKind) {\r\n        case TSDocTagSyntaxKind.BlockTag:\r\n          if (docBlockTag.tagNameWithUpperCase === StandardTags.param.tagNameWithUpperCase) {\r\n            const docParamBlock: DocParamBlock = this._parseParamBlock(\r\n              tokenReader,\r\n              docBlockTag,\r\n              StandardTags.param.tagName\r\n            );\r\n\r\n            this._parserContext.docComment.params.add(docParamBlock);\r\n\r\n            this._currentSection = docParamBlock.content;\r\n            return;\r\n          } else if (docBlockTag.tagNameWithUpperCase === StandardTags.typeParam.tagNameWithUpperCase) {\r\n            const docParamBlock: DocParamBlock = this._parseParamBlock(\r\n              tokenReader,\r\n              docBlockTag,\r\n              StandardTags.typeParam.tagName\r\n            );\r\n\r\n            this._parserContext.docComment.typeParams.add(docParamBlock);\r\n\r\n            this._currentSection = docParamBlock.content;\r\n            return;\r\n          } else {\r\n            const newBlock: DocBlock = new DocBlock({\r\n              configuration: this._configuration,\r\n              blockTag: docBlockTag\r\n            });\r\n\r\n            this._addBlockToDocComment(newBlock);\r\n\r\n            this._currentSection = newBlock.content;\r\n          }\r\n\r\n          return;\r\n        case TSDocTagSyntaxKind.ModifierTag:\r\n          // The block tag was recognized as a modifier, so add it to the modifier tag set\r\n          // and do NOT call currentSection.appendNode(parsedNode)\r\n          modifierTagSet.addTag(docBlockTag);\r\n          return;\r\n      }\r\n    }\r\n\r\n    this._pushNode(docBlockTag);\r\n  }\r\n\r\n  private _addBlockToDocComment(block: DocBlock): void {\r\n    const docComment: DocComment = this._parserContext.docComment;\r\n\r\n    switch (block.blockTag.tagNameWithUpperCase) {\r\n      case StandardTags.remarks.tagNameWithUpperCase:\r\n        docComment.remarksBlock = block;\r\n        break;\r\n      case StandardTags.privateRemarks.tagNameWithUpperCase:\r\n        docComment.privateRemarks = block;\r\n        break;\r\n      case StandardTags.deprecated.tagNameWithUpperCase:\r\n        docComment.deprecatedBlock = block;\r\n        break;\r\n      case StandardTags.returns.tagNameWithUpperCase:\r\n        docComment.returnsBlock = block;\r\n        break;\r\n      case StandardTags.see.tagNameWithUpperCase:\r\n        docComment._appendSeeBlock(block);\r\n        break;\r\n      default:\r\n        docComment.appendCustomBlock(block);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Used by `_parseParamBlock()`, this parses a JSDoc expression remainder like `string}` or `=\"]\"]` from\r\n   * an input like `@param {string} [x=\"]\"] - the X value`.  It detects nested balanced pairs of delimiters\r\n   * and escaped string literals.\r\n   */\r\n  private _tryParseJSDocTypeOrValueRest(\r\n    tokenReader: TokenReader,\r\n    openKind: TokenKind,\r\n    closeKind: TokenKind,\r\n    startMarker: number\r\n  ): TokenSequence | undefined {\r\n    let quoteKind: TokenKind | undefined;\r\n    let openCount: number = 1;\r\n    while (openCount > 0) {\r\n      let tokenKind: TokenKind = tokenReader.peekTokenKind();\r\n      switch (tokenKind) {\r\n        case openKind:\r\n          // ignore open bracket/brace inside of a quoted string\r\n          if (quoteKind === undefined) openCount++;\r\n          break;\r\n        case closeKind:\r\n          // ignore close bracket/brace inside of a quoted string\r\n          if (quoteKind === undefined) openCount--;\r\n          break;\r\n        case TokenKind.Backslash:\r\n          // ignore backslash outside of quoted string\r\n          if (quoteKind !== undefined) {\r\n            // skip the backslash and the next character.\r\n            tokenReader.readToken();\r\n            tokenKind = tokenReader.peekTokenKind();\r\n          }\r\n          break;\r\n        case TokenKind.DoubleQuote:\r\n        case TokenKind.SingleQuote:\r\n        case TokenKind.Backtick:\r\n          if (quoteKind === tokenKind) {\r\n            // exit quoted string if quote character matches.\r\n            quoteKind = undefined;\r\n          } else if (quoteKind === undefined) {\r\n            // start quoted string if not in a quoted string.\r\n            quoteKind = tokenKind;\r\n          }\r\n          break;\r\n      }\r\n      // give up at end of input and backtrack to start.\r\n      if (tokenKind === TokenKind.EndOfInput) {\r\n        tokenReader.backtrackToMarker(startMarker);\r\n        return undefined;\r\n      }\r\n      tokenReader.readToken();\r\n    }\r\n    return tokenReader.tryExtractAccumulatedSequence();\r\n  }\r\n\r\n  /**\r\n   * Used by `_parseParamBlock()`, this parses a JSDoc expression like `{string}` from\r\n   * an input like `@param {string} x - the X value`.\r\n   */\r\n  private _tryParseUnsupportedJSDocType(\r\n    tokenReader: TokenReader,\r\n    docBlockTag: DocBlockTag,\r\n    tagName: string\r\n  ): TokenSequence | undefined {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n\r\n    // do not parse `{@...` as a JSDoc type\r\n    if (\r\n      tokenReader.peekTokenKind() !== TokenKind.LeftCurlyBracket ||\r\n      tokenReader.peekTokenAfterKind() === TokenKind.AtSign\r\n    ) {\r\n      return undefined;\r\n    }\r\n\r\n    const startMarker: number = tokenReader.createMarker();\r\n    tokenReader.readToken(); // read the \"{\"\r\n\r\n    let jsdocTypeExcerpt: TokenSequence | undefined = this._tryParseJSDocTypeOrValueRest(\r\n      tokenReader,\r\n      TokenKind.LeftCurlyBracket,\r\n      TokenKind.RightCurlyBracket,\r\n      startMarker\r\n    );\r\n\r\n    if (jsdocTypeExcerpt) {\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ParamTagWithInvalidType,\r\n        'The ' + tagName + \" block should not include a JSDoc-style '{type}'\",\r\n        jsdocTypeExcerpt,\r\n        docBlockTag\r\n      );\r\n\r\n      const spacingAfterJsdocTypeExcerpt: TokenSequence | undefined =\r\n        this._tryReadSpacingAndNewlines(tokenReader);\r\n      if (spacingAfterJsdocTypeExcerpt) {\r\n        jsdocTypeExcerpt = jsdocTypeExcerpt.getNewSequence(\r\n          jsdocTypeExcerpt.startIndex,\r\n          spacingAfterJsdocTypeExcerpt.endIndex\r\n        );\r\n      }\r\n    }\r\n    return jsdocTypeExcerpt;\r\n  }\r\n\r\n  /**\r\n   * Used by `_parseParamBlock()`, this parses a JSDoc expression remainder like `=[]]` from\r\n   * an input like `@param {string} [x=[]] - the X value`.\r\n   */\r\n  private _tryParseJSDocOptionalNameRest(tokenReader: TokenReader): TokenSequence | undefined {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n    if (tokenReader.peekTokenKind() !== TokenKind.EndOfInput) {\r\n      const startMarker: number = tokenReader.createMarker();\r\n      return this._tryParseJSDocTypeOrValueRest(\r\n        tokenReader,\r\n        TokenKind.LeftSquareBracket,\r\n        TokenKind.RightSquareBracket,\r\n        startMarker\r\n      );\r\n    }\r\n    return undefined;\r\n  }\r\n\r\n  private _parseParamBlock(\r\n    tokenReader: TokenReader,\r\n    docBlockTag: DocBlockTag,\r\n    tagName: string\r\n  ): DocParamBlock {\r\n    const startMarker: number = tokenReader.createMarker();\r\n\r\n    const spacingBeforeParameterNameExcerpt: TokenSequence | undefined =\r\n      this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    // Skip past a JSDoc type (i.e., '@param {type} paramName') if found, and report a warning.\r\n    const unsupportedJsdocTypeBeforeParameterNameExcerpt: TokenSequence | undefined =\r\n      this._tryParseUnsupportedJSDocType(tokenReader, docBlockTag, tagName);\r\n\r\n    // Parse opening of invalid JSDoc optional parameter name (e.g., '[')\r\n    let unsupportedJsdocOptionalNameOpenBracketExcerpt: TokenSequence | undefined;\r\n    if (tokenReader.peekTokenKind() === TokenKind.LeftSquareBracket) {\r\n      tokenReader.readToken(); // read the \"[\"\r\n      unsupportedJsdocOptionalNameOpenBracketExcerpt = tokenReader.extractAccumulatedSequence();\r\n    }\r\n\r\n    let parameterName: string = '';\r\n\r\n    let done: boolean = false;\r\n    while (!done) {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.AsciiWord:\r\n        case TokenKind.Period:\r\n        case TokenKind.DollarSign:\r\n          parameterName += tokenReader.readToken();\r\n          break;\r\n        default:\r\n          done = true;\r\n          break;\r\n      }\r\n    }\r\n\r\n    const explanation: string | undefined = StringChecks.explainIfInvalidUnquotedIdentifier(parameterName);\r\n\r\n    if (explanation !== undefined) {\r\n      tokenReader.backtrackToMarker(startMarker);\r\n\r\n      const errorParamBlock: DocParamBlock = new DocParamBlock({\r\n        configuration: this._configuration,\r\n        blockTag: docBlockTag,\r\n        parameterName: ''\r\n      });\r\n      const errorMessage: string =\r\n        parameterName.length > 0\r\n          ? 'The ' + tagName + ' block should be followed by a valid parameter name: ' + explanation\r\n          : 'The ' + tagName + ' block should be followed by a parameter name';\r\n\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ParamTagWithInvalidName,\r\n        errorMessage,\r\n        docBlockTag.getTokenSequence(),\r\n        docBlockTag\r\n      );\r\n      return errorParamBlock;\r\n    }\r\n\r\n    const parameterNameExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    // Parse closing of invalid JSDoc optional parameter name (e.g., ']', '=default]').\r\n    let unsupportedJsdocOptionalNameRestExcerpt: TokenSequence | undefined;\r\n    if (unsupportedJsdocOptionalNameOpenBracketExcerpt) {\r\n      unsupportedJsdocOptionalNameRestExcerpt = this._tryParseJSDocOptionalNameRest(tokenReader);\r\n\r\n      let errorSequence: TokenSequence | undefined = unsupportedJsdocOptionalNameOpenBracketExcerpt;\r\n      if (unsupportedJsdocOptionalNameRestExcerpt) {\r\n        errorSequence = docBlockTag\r\n          .getTokenSequence()\r\n          .getNewSequence(\r\n            unsupportedJsdocOptionalNameOpenBracketExcerpt.startIndex,\r\n            unsupportedJsdocOptionalNameRestExcerpt.endIndex\r\n          );\r\n      }\r\n\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ParamTagWithInvalidOptionalName,\r\n        'The ' +\r\n          tagName +\r\n          \" should not include a JSDoc-style optional name; it must not be enclosed in '[ ]' brackets.\",\r\n        errorSequence,\r\n        docBlockTag\r\n      );\r\n    }\r\n\r\n    const spacingAfterParameterNameExcerpt: TokenSequence | undefined =\r\n      this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    // Skip past a trailing JSDoc type (i.e., '@param paramName {type}') if found, and report a warning.\r\n    const unsupportedJsdocTypeAfterParameterNameExcerpt: TokenSequence | undefined =\r\n      this._tryParseUnsupportedJSDocType(tokenReader, docBlockTag, tagName);\r\n\r\n    // TODO: Warn if there is no space before or after the hyphen\r\n    let hyphenExcerpt: TokenSequence | undefined;\r\n    let spacingAfterHyphenExcerpt: TokenSequence | undefined;\r\n    let unsupportedJsdocTypeAfterHyphenExcerpt: TokenSequence | undefined;\r\n    if (tokenReader.peekTokenKind() === TokenKind.Hyphen) {\r\n      tokenReader.readToken();\r\n      hyphenExcerpt = tokenReader.extractAccumulatedSequence();\r\n      // TODO: Only read one space\r\n      spacingAfterHyphenExcerpt = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n      // Skip past a JSDoc type (i.e., '@param paramName - {type}') if found, and report a warning.\r\n      unsupportedJsdocTypeAfterHyphenExcerpt = this._tryParseUnsupportedJSDocType(\r\n        tokenReader,\r\n        docBlockTag,\r\n        tagName\r\n      );\r\n    } else {\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ParamTagMissingHyphen,\r\n        'The ' + tagName + ' block should be followed by a parameter name and then a hyphen',\r\n        docBlockTag.getTokenSequence(),\r\n        docBlockTag\r\n      );\r\n    }\r\n\r\n    return new DocParamBlock({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      blockTag: docBlockTag,\r\n\r\n      spacingBeforeParameterNameExcerpt,\r\n\r\n      unsupportedJsdocTypeBeforeParameterNameExcerpt,\r\n      unsupportedJsdocOptionalNameOpenBracketExcerpt,\r\n\r\n      parameterNameExcerpt,\r\n      parameterName,\r\n\r\n      unsupportedJsdocOptionalNameRestExcerpt,\r\n\r\n      spacingAfterParameterNameExcerpt,\r\n\r\n      unsupportedJsdocTypeAfterParameterNameExcerpt,\r\n\r\n      hyphenExcerpt,\r\n\r\n      spacingAfterHyphenExcerpt,\r\n\r\n      unsupportedJsdocTypeAfterHyphenExcerpt\r\n    });\r\n  }\r\n\r\n  private _pushNode(docNode: DocNode): void {\r\n    if (this._configuration.docNodeManager.isAllowedChild(DocNodeKind.Paragraph, docNode.kind)) {\r\n      this._currentSection.appendNodeInParagraph(docNode);\r\n    } else {\r\n      this._currentSection.appendNode(docNode);\r\n    }\r\n  }\r\n\r\n  private _parseBackslashEscape(tokenReader: TokenReader): DocNode {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n    const marker: number = tokenReader.createMarker();\r\n\r\n    tokenReader.readToken(); // read the backslash\r\n\r\n    if (tokenReader.peekTokenKind() === TokenKind.EndOfInput) {\r\n      return this._backtrackAndCreateError(\r\n        tokenReader,\r\n        marker,\r\n        TSDocMessageId.UnnecessaryBackslash,\r\n        'A backslash must precede another character that is being escaped'\r\n      );\r\n    }\r\n\r\n    const escapedToken: Token = tokenReader.readToken(); // read the escaped character\r\n\r\n    // In CommonMark, a backslash is only allowed before a punctuation\r\n    // character.  In all other contexts, the backslash is interpreted as a\r\n    // literal character.\r\n    if (!Tokenizer.isPunctuation(escapedToken.kind)) {\r\n      return this._backtrackAndCreateError(\r\n        tokenReader,\r\n        marker,\r\n        TSDocMessageId.UnnecessaryBackslash,\r\n        'A backslash can only be used to escape a punctuation character'\r\n      );\r\n    }\r\n\r\n    const encodedTextExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    return new DocEscapedText({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      escapeStyle: EscapeStyle.CommonMarkBackslash,\r\n      encodedTextExcerpt,\r\n      decodedText: escapedToken.toString()\r\n    });\r\n  }\r\n\r\n  private _parseBlockTag(tokenReader: TokenReader): DocNode {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n    const marker: number = tokenReader.createMarker();\r\n\r\n    if (tokenReader.peekTokenKind() !== TokenKind.AtSign) {\r\n      return this._backtrackAndCreateError(\r\n        tokenReader,\r\n        marker,\r\n        TSDocMessageId.MissingTag,\r\n        'Expecting a TSDoc tag starting with \"@\"'\r\n      );\r\n    }\r\n\r\n    // \"@one\" is a valid TSDoc tag at the start of a line, but \"@one@two\" is\r\n    // a syntax error.  For two tags it should be \"@one @two\", or for literal text it\r\n    // should be \"\\@one\\@two\".\r\n    switch (tokenReader.peekPreviousTokenKind()) {\r\n      case TokenKind.EndOfInput:\r\n      case TokenKind.Spacing:\r\n      case TokenKind.Newline:\r\n        break;\r\n      default:\r\n        return this._backtrackAndCreateError(\r\n          tokenReader,\r\n          marker,\r\n          TSDocMessageId.AtSignInWord,\r\n          'The \"@\" character looks like part of a TSDoc tag; use a backslash to escape it'\r\n        );\r\n    }\r\n\r\n    // Include the \"@\" as part of the tagName\r\n    let tagName: string = tokenReader.readToken().toString();\r\n\r\n    if (tokenReader.peekTokenKind() !== TokenKind.AsciiWord) {\r\n      return this._backtrackAndCreateError(\r\n        tokenReader,\r\n        marker,\r\n        TSDocMessageId.AtSignWithoutTagName,\r\n        'Expecting a TSDoc tag name after \"@\"; if it is not a tag, use a backslash to escape this character'\r\n      );\r\n    }\r\n\r\n    const tagNameMarker: number = tokenReader.createMarker();\r\n\r\n    while (tokenReader.peekTokenKind() === TokenKind.AsciiWord) {\r\n      tagName += tokenReader.readToken().toString();\r\n    }\r\n\r\n    switch (tokenReader.peekTokenKind()) {\r\n      case TokenKind.Spacing:\r\n      case TokenKind.Newline:\r\n      case TokenKind.EndOfInput:\r\n        break;\r\n      default:\r\n        const badCharacter: string = tokenReader.peekToken().range.toString()[0];\r\n        return this._backtrackAndCreateError(\r\n          tokenReader,\r\n          marker,\r\n          TSDocMessageId.CharactersAfterBlockTag,\r\n          `The token \"${tagName}\" looks like a TSDoc tag but contains an invalid character` +\r\n            ` ${JSON.stringify(badCharacter)}; if it is not a tag, use a backslash to escape the \"@\"`\r\n        );\r\n    }\r\n\r\n    if (StringChecks.explainIfInvalidTSDocTagName(tagName)) {\r\n      const failure: IFailure = this._createFailureForTokensSince(\r\n        tokenReader,\r\n        TSDocMessageId.MalformedTagName,\r\n        'A TSDoc tag name must start with a letter and contain only letters and numbers',\r\n        tagNameMarker\r\n      );\r\n      return this._backtrackAndCreateErrorForFailure(tokenReader, marker, '', failure);\r\n    }\r\n\r\n    return new DocBlockTag({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      tagName,\r\n      tagNameExcerpt: tokenReader.extractAccumulatedSequence()\r\n    });\r\n  }\r\n\r\n  private _parseInlineTag(tokenReader: TokenReader): DocNode {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n    const marker: number = tokenReader.createMarker();\r\n\r\n    if (tokenReader.peekTokenKind() !== TokenKind.LeftCurlyBracket) {\r\n      return this._backtrackAndCreateError(\r\n        tokenReader,\r\n        marker,\r\n        TSDocMessageId.MissingTag,\r\n        'Expecting a TSDoc tag starting with \"{\"'\r\n      );\r\n    }\r\n    tokenReader.readToken();\r\n\r\n    const openingDelimiterExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    // For inline tags, if we handle errors by backtracking to the \"{\"  token, then the main loop\r\n    // will then interpret the \"@\" as a block tag, which is almost certainly incorrect.  So the\r\n    // DocErrorText needs to include both the \"{\" and \"@\" tokens.\r\n    // We will use _backtrackAndCreateErrorRangeForFailure() for that.\r\n    const atSignMarker: number = tokenReader.createMarker();\r\n\r\n    if (tokenReader.peekTokenKind() !== TokenKind.AtSign) {\r\n      return this._backtrackAndCreateError(\r\n        tokenReader,\r\n        marker,\r\n        TSDocMessageId.MalformedInlineTag,\r\n        'Expecting a TSDoc tag starting with \"{@\"'\r\n      );\r\n    }\r\n\r\n    // Include the \"@\" as part of the tagName\r\n    let tagName: string = tokenReader.readToken().toString();\r\n\r\n    while (tokenReader.peekTokenKind() === TokenKind.AsciiWord) {\r\n      tagName += tokenReader.readToken().toString();\r\n    }\r\n\r\n    if (tagName === '@') {\r\n      // This is an unusual case\r\n      const failure: IFailure = this._createFailureForTokensSince(\r\n        tokenReader,\r\n        TSDocMessageId.MalformedInlineTag,\r\n        'Expecting a TSDoc inline tag name after the \"{@\" characters',\r\n        atSignMarker\r\n      );\r\n      return this._backtrackAndCreateErrorRangeForFailure(tokenReader, marker, atSignMarker, '', failure);\r\n    }\r\n\r\n    if (StringChecks.explainIfInvalidTSDocTagName(tagName)) {\r\n      const failure: IFailure = this._createFailureForTokensSince(\r\n        tokenReader,\r\n        TSDocMessageId.MalformedTagName,\r\n        'A TSDoc tag name must start with a letter and contain only letters and numbers',\r\n        atSignMarker\r\n      );\r\n      return this._backtrackAndCreateErrorRangeForFailure(tokenReader, marker, atSignMarker, '', failure);\r\n    }\r\n\r\n    const tagNameExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const spacingAfterTagNameExcerpt: TokenSequence | undefined =\r\n      this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    if (spacingAfterTagNameExcerpt === undefined) {\r\n      // If there were no spaces at all, that's an error unless it's the degenerate \"{@tag}\" case\r\n      if (tokenReader.peekTokenKind() !== TokenKind.RightCurlyBracket) {\r\n        const badCharacter: string = tokenReader.peekToken().range.toString()[0];\r\n        const failure: IFailure = this._createFailureForToken(\r\n          tokenReader,\r\n          TSDocMessageId.CharactersAfterInlineTag,\r\n          `The character ${JSON.stringify(\r\n            badCharacter\r\n          )} cannot appear after the TSDoc tag name; expecting a space`\r\n        );\r\n        return this._backtrackAndCreateErrorRangeForFailure(tokenReader, marker, atSignMarker, '', failure);\r\n      }\r\n    }\r\n\r\n    let done: boolean = false;\r\n    while (!done) {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.EndOfInput:\r\n          return this._backtrackAndCreateErrorRange(\r\n            tokenReader,\r\n            marker,\r\n            atSignMarker,\r\n            TSDocMessageId.InlineTagMissingRightBrace,\r\n            'The TSDoc inline tag name is missing its closing \"}\"'\r\n          );\r\n        case TokenKind.Backslash:\r\n          // http://usejsdoc.org/about-block-inline-tags.html\r\n          // \"If your tag's text includes a closing curly brace (}), you must escape it with\r\n          // a leading backslash (\\).\"\r\n          tokenReader.readToken(); // discard the backslash\r\n\r\n          // In CommonMark, a backslash is only allowed before a punctuation\r\n          // character.  In all other contexts, the backslash is interpreted as a\r\n          // literal character.\r\n          if (!Tokenizer.isPunctuation(tokenReader.peekTokenKind())) {\r\n            const failure: IFailure = this._createFailureForToken(\r\n              tokenReader,\r\n              TSDocMessageId.UnnecessaryBackslash,\r\n              'A backslash can only be used to escape a punctuation character'\r\n            );\r\n            return this._backtrackAndCreateErrorRangeForFailure(\r\n              tokenReader,\r\n              marker,\r\n              atSignMarker,\r\n              'Error reading inline TSDoc tag: ',\r\n              failure\r\n            );\r\n          }\r\n\r\n          tokenReader.readToken();\r\n          break;\r\n        case TokenKind.LeftCurlyBracket: {\r\n          const failure: IFailure = this._createFailureForToken(\r\n            tokenReader,\r\n            TSDocMessageId.InlineTagUnescapedBrace,\r\n            'The \"{\" character must be escaped with a backslash when used inside a TSDoc inline tag'\r\n          );\r\n          return this._backtrackAndCreateErrorRangeForFailure(tokenReader, marker, atSignMarker, '', failure);\r\n        }\r\n        case TokenKind.RightCurlyBracket:\r\n          done = true;\r\n          break;\r\n        default:\r\n          tokenReader.readToken();\r\n          break;\r\n      }\r\n    }\r\n\r\n    const tagContentExcerpt: TokenSequence | undefined = tokenReader.tryExtractAccumulatedSequence();\r\n\r\n    // Read the right curly bracket\r\n    tokenReader.readToken();\r\n    const closingDelimiterExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const docInlineTagParsedParameters: IDocInlineTagParsedParameters = {\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      openingDelimiterExcerpt,\r\n\r\n      tagNameExcerpt,\r\n      tagName,\r\n      spacingAfterTagNameExcerpt,\r\n\r\n      tagContentExcerpt,\r\n\r\n      closingDelimiterExcerpt\r\n    };\r\n\r\n    const tagNameWithUpperCase: string = tagName.toUpperCase();\r\n\r\n    // Create a new TokenReader that will reparse the tokens corresponding to the tagContent.\r\n    const embeddedTokenReader: TokenReader = new TokenReader(\r\n      this._parserContext,\r\n      tagContentExcerpt ? tagContentExcerpt : TokenSequence.createEmpty(this._parserContext)\r\n    );\r\n\r\n    let docNode: DocNode;\r\n    switch (tagNameWithUpperCase) {\r\n      case StandardTags.inheritDoc.tagNameWithUpperCase:\r\n        docNode = this._parseInheritDocTag(docInlineTagParsedParameters, embeddedTokenReader);\r\n        break;\r\n      case StandardTags.link.tagNameWithUpperCase:\r\n        docNode = this._parseLinkTag(docInlineTagParsedParameters, embeddedTokenReader);\r\n        break;\r\n      default:\r\n        docNode = new DocInlineTag(docInlineTagParsedParameters);\r\n    }\r\n\r\n    // Validate the tag\r\n    const tagDefinition: TSDocTagDefinition | undefined =\r\n      this._parserContext.configuration.tryGetTagDefinitionWithUpperCase(tagNameWithUpperCase);\r\n\r\n    this._validateTagDefinition(\r\n      tagDefinition,\r\n      tagName,\r\n      /* expectingInlineTag */ true,\r\n      tagNameExcerpt,\r\n      docNode\r\n    );\r\n\r\n    return docNode;\r\n  }\r\n\r\n  private _parseInheritDocTag(\r\n    docInlineTagParsedParameters: IDocInlineTagParsedParameters,\r\n    embeddedTokenReader: TokenReader\r\n  ): DocInlineTagBase {\r\n    // If an error occurs, then return a generic DocInlineTag instead of DocInheritDocTag\r\n    const errorTag: DocInlineTag = new DocInlineTag(docInlineTagParsedParameters);\r\n\r\n    const parameters: IDocInheritDocTagParameters = {\r\n      ...docInlineTagParsedParameters\r\n    };\r\n\r\n    if (embeddedTokenReader.peekTokenKind() !== TokenKind.EndOfInput) {\r\n      parameters.declarationReference = this._parseDeclarationReference(\r\n        embeddedTokenReader,\r\n        docInlineTagParsedParameters.tagNameExcerpt,\r\n        errorTag\r\n      );\r\n      if (!parameters.declarationReference) {\r\n        return errorTag;\r\n      }\r\n\r\n      if (embeddedTokenReader.peekTokenKind() !== TokenKind.EndOfInput) {\r\n        embeddedTokenReader.readToken();\r\n\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.InheritDocTagSyntax,\r\n          'Unexpected character after declaration reference',\r\n          embeddedTokenReader.extractAccumulatedSequence(),\r\n          errorTag\r\n        );\r\n        return errorTag;\r\n      }\r\n    }\r\n\r\n    return new DocInheritDocTag(parameters);\r\n  }\r\n\r\n  private _parseLinkTag(\r\n    docInlineTagParsedParameters: IDocInlineTagParsedParameters,\r\n    embeddedTokenReader: TokenReader\r\n  ): DocInlineTagBase {\r\n    // If an error occurs, then return a generic DocInlineTag instead of DocInheritDocTag\r\n    const errorTag: DocInlineTag = new DocInlineTag(docInlineTagParsedParameters);\r\n\r\n    const parameters: IDocLinkTagParsedParameters = {\r\n      ...docInlineTagParsedParameters\r\n    };\r\n\r\n    if (!docInlineTagParsedParameters.tagContentExcerpt) {\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.LinkTagEmpty,\r\n        'The @link tag content is missing',\r\n        parameters.tagNameExcerpt,\r\n        errorTag\r\n      );\r\n\r\n      return errorTag;\r\n    }\r\n\r\n    // Is the link destination a URL or a declaration reference?\r\n    //\r\n    // The JSDoc \"@link\" tag allows URLs, however supporting full URLs would be highly\r\n    // ambiguous, for example \"microsoft.windows.camera:\" is an actual valid URI scheme,\r\n    // and even the common \"mailto:example.com\" looks suspiciously like a declaration reference.\r\n    // In practice JSDoc URLs are nearly always HTTP or HTTPS, so it seems fairly reasonable to\r\n    // require the URL to have \"://\" and a scheme without any punctuation in it.  If a more exotic\r\n    // URL is needed, the HTML \"<a>\" tag can always be used.\r\n\r\n    // We start with a fairly broad classifier heuristic, and then the parsers will refine this:\r\n    // 1. Does it start with \"//\"?\r\n    // 2. Does it contain \"://\"?\r\n\r\n    let looksLikeUrl: boolean =\r\n      embeddedTokenReader.peekTokenKind() === TokenKind.Slash &&\r\n      embeddedTokenReader.peekTokenAfterKind() === TokenKind.Slash;\r\n    const marker: number = embeddedTokenReader.createMarker();\r\n\r\n    let done: boolean = looksLikeUrl;\r\n    while (!done) {\r\n      switch (embeddedTokenReader.peekTokenKind()) {\r\n        // An URI scheme can contain letters, numbers, minus, plus, and periods\r\n        case TokenKind.AsciiWord:\r\n        case TokenKind.Period:\r\n        case TokenKind.Hyphen:\r\n        case TokenKind.Plus:\r\n          embeddedTokenReader.readToken();\r\n          break;\r\n        case TokenKind.Colon:\r\n          embeddedTokenReader.readToken();\r\n          // Once we a reach a colon, then it's a URL only if we see \"://\"\r\n          looksLikeUrl =\r\n            embeddedTokenReader.peekTokenKind() === TokenKind.Slash &&\r\n            embeddedTokenReader.peekTokenAfterKind() === TokenKind.Slash;\r\n          done = true;\r\n          break;\r\n        default:\r\n          done = true;\r\n      }\r\n    }\r\n\r\n    embeddedTokenReader.backtrackToMarker(marker);\r\n\r\n    // Is the hyperlink a URL or a declaration reference?\r\n    if (looksLikeUrl) {\r\n      // It starts with something like \"http://\", so parse it as a URL\r\n      if (\r\n        !this._parseLinkTagUrlDestination(\r\n          embeddedTokenReader,\r\n          parameters,\r\n          docInlineTagParsedParameters.tagNameExcerpt,\r\n          errorTag\r\n        )\r\n      ) {\r\n        return errorTag;\r\n      }\r\n    } else {\r\n      // Otherwise, assume it's a declaration reference\r\n      if (\r\n        !this._parseLinkTagCodeDestination(\r\n          embeddedTokenReader,\r\n          parameters,\r\n          docInlineTagParsedParameters.tagNameExcerpt,\r\n          errorTag\r\n        )\r\n      ) {\r\n        return errorTag;\r\n      }\r\n    }\r\n\r\n    if (embeddedTokenReader.peekTokenKind() === TokenKind.Spacing) {\r\n      // The above parser rules should have consumed any spacing before the pipe\r\n      throw new Error('Unconsumed spacing encountered after construct');\r\n    }\r\n\r\n    if (embeddedTokenReader.peekTokenKind() === TokenKind.Pipe) {\r\n      // Read the link text\r\n      embeddedTokenReader.readToken();\r\n      parameters.pipeExcerpt = embeddedTokenReader.extractAccumulatedSequence();\r\n      parameters.spacingAfterPipeExcerpt = this._tryReadSpacingAndNewlines(embeddedTokenReader);\r\n\r\n      // Read everything until the end\r\n      // NOTE: Because we're using an embedded TokenReader, the TokenKind.EndOfInput occurs\r\n      // when we reach the \"}\", not the end of the original input\r\n      done = false;\r\n      let spacingAfterLinkTextMarker: number | undefined = undefined;\r\n      while (!done) {\r\n        switch (embeddedTokenReader.peekTokenKind()) {\r\n          case TokenKind.EndOfInput:\r\n            done = true;\r\n            break;\r\n          case TokenKind.Pipe:\r\n          case TokenKind.LeftCurlyBracket:\r\n            const badCharacter: string = embeddedTokenReader.readToken().toString();\r\n            this._parserContext.log.addMessageForTokenSequence(\r\n              TSDocMessageId.LinkTagUnescapedText,\r\n              `The \"${badCharacter}\" character may not be used in the link text without escaping it`,\r\n              embeddedTokenReader.extractAccumulatedSequence(),\r\n              errorTag\r\n            );\r\n            return errorTag;\r\n          case TokenKind.Spacing:\r\n          case TokenKind.Newline:\r\n            embeddedTokenReader.readToken();\r\n            break;\r\n          default:\r\n            // We found a non-spacing character, so move the spacingAfterLinkTextMarker\r\n            spacingAfterLinkTextMarker = embeddedTokenReader.createMarker() + 1;\r\n            embeddedTokenReader.readToken();\r\n        }\r\n      }\r\n\r\n      const linkTextAndSpacing: TokenSequence | undefined =\r\n        embeddedTokenReader.tryExtractAccumulatedSequence();\r\n      if (linkTextAndSpacing) {\r\n        if (spacingAfterLinkTextMarker === undefined) {\r\n          // We never found any non-spacing characters, so everything is trailing spacing\r\n          parameters.spacingAfterLinkTextExcerpt = linkTextAndSpacing;\r\n        } else if (spacingAfterLinkTextMarker >= linkTextAndSpacing.endIndex) {\r\n          // We found no trailing spacing, so everything we found is the text\r\n          parameters.linkTextExcerpt = linkTextAndSpacing;\r\n        } else {\r\n          // Split the trailing spacing from the link text\r\n          parameters.linkTextExcerpt = linkTextAndSpacing.getNewSequence(\r\n            linkTextAndSpacing.startIndex,\r\n            spacingAfterLinkTextMarker\r\n          );\r\n          parameters.spacingAfterLinkTextExcerpt = linkTextAndSpacing.getNewSequence(\r\n            spacingAfterLinkTextMarker,\r\n            linkTextAndSpacing.endIndex\r\n          );\r\n        }\r\n      }\r\n    } else if (embeddedTokenReader.peekTokenKind() !== TokenKind.EndOfInput) {\r\n      embeddedTokenReader.readToken();\r\n\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.LinkTagDestinationSyntax,\r\n        'Unexpected character after link destination',\r\n        embeddedTokenReader.extractAccumulatedSequence(),\r\n        errorTag\r\n      );\r\n      return errorTag;\r\n    }\r\n\r\n    return new DocLinkTag(parameters);\r\n  }\r\n\r\n  private _parseLinkTagUrlDestination(\r\n    embeddedTokenReader: TokenReader,\r\n    parameters: IDocLinkTagParsedParameters,\r\n    tokenSequenceForErrorContext: TokenSequence,\r\n    nodeForErrorContext: DocNode\r\n  ): boolean {\r\n    // Simply accumulate everything up to the next space. We won't try to implement a proper\r\n    // URI parser here.\r\n    let urlDestination: string = '';\r\n\r\n    let done: boolean = false;\r\n    while (!done) {\r\n      switch (embeddedTokenReader.peekTokenKind()) {\r\n        case TokenKind.Spacing:\r\n        case TokenKind.Newline:\r\n        case TokenKind.EndOfInput:\r\n        case TokenKind.Pipe:\r\n        case TokenKind.RightCurlyBracket:\r\n          done = true;\r\n          break;\r\n        default:\r\n          urlDestination += embeddedTokenReader.readToken();\r\n          break;\r\n      }\r\n    }\r\n\r\n    if (urlDestination.length === 0) {\r\n      // This should be impossible since the caller ensures that peekTokenKind() === TokenKind.AsciiWord\r\n      throw new Error('Missing URL in _parseLinkTagUrlDestination()');\r\n    }\r\n\r\n    const urlDestinationExcerpt: TokenSequence = embeddedTokenReader.extractAccumulatedSequence();\r\n\r\n    const invalidUrlExplanation: string | undefined = StringChecks.explainIfInvalidLinkUrl(urlDestination);\r\n    if (invalidUrlExplanation) {\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.LinkTagInvalidUrl,\r\n        invalidUrlExplanation,\r\n        urlDestinationExcerpt,\r\n        nodeForErrorContext\r\n      );\r\n      return false;\r\n    }\r\n\r\n    parameters.urlDestinationExcerpt = urlDestinationExcerpt;\r\n    parameters.spacingAfterDestinationExcerpt = this._tryReadSpacingAndNewlines(embeddedTokenReader);\r\n\r\n    return true;\r\n  }\r\n\r\n  private _parseLinkTagCodeDestination(\r\n    embeddedTokenReader: TokenReader,\r\n    parameters: IDocLinkTagParameters,\r\n    tokenSequenceForErrorContext: TokenSequence,\r\n    nodeForErrorContext: DocNode\r\n  ): boolean {\r\n    parameters.codeDestination = this._parseDeclarationReference(\r\n      embeddedTokenReader,\r\n      tokenSequenceForErrorContext,\r\n      nodeForErrorContext\r\n    );\r\n\r\n    return !!parameters.codeDestination;\r\n  }\r\n\r\n  private _parseDeclarationReference(\r\n    tokenReader: TokenReader,\r\n    tokenSequenceForErrorContext: TokenSequence,\r\n    nodeForErrorContext: DocNode\r\n  ): DocDeclarationReference | undefined {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n\r\n    // The package name can contain characters that look like a member reference.  This means we need to scan forwards\r\n    // to see if there is a \"#\".  However, we need to be careful not to match a \"#\" that is part of a quoted expression.\r\n\r\n    const marker: number = tokenReader.createMarker();\r\n    let hasHash: boolean = false;\r\n\r\n    // A common mistake is to forget the \"#\" for package name or import path.  The telltale sign\r\n    // of this is mistake is that we see path-only characters such as \"@\" or \"/\" in the beginning\r\n    // where this would be a syntax error for a member reference.\r\n    let lookingForImportCharacters: boolean = true;\r\n    let sawImportCharacters: boolean = false;\r\n\r\n    let done: boolean = false;\r\n    while (!done) {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.DoubleQuote:\r\n        case TokenKind.EndOfInput:\r\n        case TokenKind.LeftCurlyBracket:\r\n        case TokenKind.LeftParenthesis:\r\n        case TokenKind.LeftSquareBracket:\r\n        case TokenKind.Newline:\r\n        case TokenKind.Pipe:\r\n        case TokenKind.RightCurlyBracket:\r\n        case TokenKind.RightParenthesis:\r\n        case TokenKind.RightSquareBracket:\r\n        case TokenKind.SingleQuote:\r\n        case TokenKind.Spacing:\r\n          done = true;\r\n          break;\r\n        case TokenKind.PoundSymbol:\r\n          hasHash = true;\r\n          done = true;\r\n          break;\r\n        case TokenKind.Slash:\r\n        case TokenKind.AtSign:\r\n          if (lookingForImportCharacters) {\r\n            sawImportCharacters = true;\r\n          }\r\n          tokenReader.readToken();\r\n          break;\r\n        case TokenKind.AsciiWord:\r\n        case TokenKind.Period:\r\n        case TokenKind.Hyphen:\r\n          // It's a character that looks like part of a package name or import path,\r\n          // so don't set lookingForImportCharacters = false\r\n          tokenReader.readToken();\r\n          break;\r\n        default:\r\n          // Once we reach something other than AsciiWord and Period, then the meaning of\r\n          // slashes and at-signs is no longer obvious.\r\n          lookingForImportCharacters = false;\r\n\r\n          tokenReader.readToken();\r\n      }\r\n    }\r\n\r\n    if (!hasHash && sawImportCharacters) {\r\n      // We saw characters that will be a syntax error if interpreted as a member reference,\r\n      // but would make sense as a package name or import path, but we did not find a \"#\"\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ReferenceMissingHash,\r\n        'The declaration reference appears to contain a package name or import path,' +\r\n          ' but it is missing the \"#\" delimiter',\r\n        tokenReader.extractAccumulatedSequence(),\r\n        nodeForErrorContext\r\n      );\r\n      return undefined;\r\n    }\r\n\r\n    tokenReader.backtrackToMarker(marker);\r\n\r\n    let packageNameExcerpt: TokenSequence | undefined;\r\n    let importPathExcerpt: TokenSequence | undefined;\r\n    let importHashExcerpt: TokenSequence | undefined;\r\n    let spacingAfterImportHashExcerpt: TokenSequence | undefined;\r\n\r\n    if (hasHash) {\r\n      // If it starts with a \".\" then it's a relative path, not a package name\r\n      if (tokenReader.peekTokenKind() !== TokenKind.Period) {\r\n        // Read the package name:\r\n        const scopedPackageName: boolean = tokenReader.peekTokenKind() === TokenKind.AtSign;\r\n        let finishedScope: boolean = false;\r\n\r\n        done = false;\r\n        while (!done) {\r\n          switch (tokenReader.peekTokenKind()) {\r\n            case TokenKind.EndOfInput:\r\n              // If hasHash=true, then we are expecting to stop when we reach the hash\r\n              throw new Error('Expecting pound symbol');\r\n            case TokenKind.Slash:\r\n              // Stop at the first slash, unless this is a scoped package, in which case we stop at the second slash\r\n              if (scopedPackageName && !finishedScope) {\r\n                tokenReader.readToken();\r\n                finishedScope = true;\r\n              } else {\r\n                done = true;\r\n              }\r\n              break;\r\n            case TokenKind.PoundSymbol:\r\n              done = true;\r\n              break;\r\n            default:\r\n              tokenReader.readToken();\r\n          }\r\n        }\r\n\r\n        if (!tokenReader.isAccumulatedSequenceEmpty()) {\r\n          packageNameExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n          // Check that the packageName is syntactically valid\r\n          const explanation: string | undefined = StringChecks.explainIfInvalidPackageName(\r\n            packageNameExcerpt.toString()\r\n          );\r\n          if (explanation) {\r\n            this._parserContext.log.addMessageForTokenSequence(\r\n              TSDocMessageId.ReferenceMalformedPackageName,\r\n              explanation,\r\n              packageNameExcerpt,\r\n              nodeForErrorContext\r\n            );\r\n            return undefined;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Read the import path:\r\n      done = false;\r\n      while (!done) {\r\n        switch (tokenReader.peekTokenKind()) {\r\n          case TokenKind.EndOfInput:\r\n            // If hasHash=true, then we are expecting to stop when we reach the hash\r\n            throw new Error('Expecting pound symbol');\r\n          case TokenKind.PoundSymbol:\r\n            done = true;\r\n            break;\r\n          default:\r\n            tokenReader.readToken();\r\n        }\r\n      }\r\n\r\n      if (!tokenReader.isAccumulatedSequenceEmpty()) {\r\n        importPathExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n        // Check that the importPath is syntactically valid\r\n        const explanation: string | undefined = StringChecks.explainIfInvalidImportPath(\r\n          importPathExcerpt.toString(),\r\n          !!packageNameExcerpt\r\n        );\r\n        if (explanation) {\r\n          this._parserContext.log.addMessageForTokenSequence(\r\n            TSDocMessageId.ReferenceMalformedImportPath,\r\n            explanation,\r\n            importPathExcerpt,\r\n            nodeForErrorContext\r\n          );\r\n          return undefined;\r\n        }\r\n      }\r\n\r\n      // Read the import hash\r\n      if (tokenReader.peekTokenKind() !== TokenKind.PoundSymbol) {\r\n        // The above logic should have left us at the PoundSymbol\r\n        throw new Error('Expecting pound symbol');\r\n      }\r\n      tokenReader.readToken();\r\n      importHashExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n      spacingAfterImportHashExcerpt = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n      if (packageNameExcerpt === undefined && importPathExcerpt === undefined) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.ReferenceHashSyntax,\r\n          'The hash character must be preceded by a package name or import path',\r\n          importHashExcerpt,\r\n          nodeForErrorContext\r\n        );\r\n        return undefined;\r\n      }\r\n    }\r\n\r\n    // Read the member references:\r\n    const memberReferences: DocMemberReference[] = [];\r\n\r\n    done = false;\r\n    while (!done) {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.Period:\r\n        case TokenKind.LeftParenthesis:\r\n        case TokenKind.AsciiWord:\r\n        case TokenKind.Colon:\r\n        case TokenKind.LeftSquareBracket:\r\n        case TokenKind.DoubleQuote:\r\n          const expectingDot: boolean = memberReferences.length > 0;\r\n          const memberReference: DocMemberReference | undefined = this._parseMemberReference(\r\n            tokenReader,\r\n            expectingDot,\r\n            tokenSequenceForErrorContext,\r\n            nodeForErrorContext\r\n          );\r\n\r\n          if (!memberReference) {\r\n            return undefined;\r\n          }\r\n\r\n          memberReferences.push(memberReference);\r\n          break;\r\n        default:\r\n          done = true;\r\n      }\r\n    }\r\n\r\n    if (\r\n      packageNameExcerpt === undefined &&\r\n      importPathExcerpt === undefined &&\r\n      memberReferences.length === 0\r\n    ) {\r\n      // We didn't find any parts of a declaration reference\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.MissingReference,\r\n        'Expecting a declaration reference',\r\n        tokenSequenceForErrorContext,\r\n        nodeForErrorContext\r\n      );\r\n      return undefined;\r\n    }\r\n\r\n    return new DocDeclarationReference({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      packageNameExcerpt,\r\n      importPathExcerpt,\r\n\r\n      importHashExcerpt,\r\n      spacingAfterImportHashExcerpt,\r\n\r\n      memberReferences\r\n    });\r\n  }\r\n\r\n  private _parseMemberReference(\r\n    tokenReader: TokenReader,\r\n    expectingDot: boolean,\r\n    tokenSequenceForErrorContext: TokenSequence,\r\n    nodeForErrorContext: DocNode\r\n  ): DocMemberReference | undefined {\r\n    const parameters: IDocMemberReferenceParsedParameters = {\r\n      parsed: true,\r\n      configuration: this._configuration\r\n    };\r\n\r\n    // Read the dot operator\r\n    if (expectingDot) {\r\n      if (tokenReader.peekTokenKind() !== TokenKind.Period) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.ReferenceMissingDot,\r\n          'Expecting a period before the next component of a declaration reference',\r\n          tokenSequenceForErrorContext,\r\n          nodeForErrorContext\r\n        );\r\n        return undefined;\r\n      }\r\n      tokenReader.readToken();\r\n      parameters.dotExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n      parameters.spacingAfterDotExcerpt = this._tryReadSpacingAndNewlines(tokenReader);\r\n    }\r\n\r\n    // Read the left parenthesis if there is one\r\n    if (tokenReader.peekTokenKind() === TokenKind.LeftParenthesis) {\r\n      tokenReader.readToken();\r\n      parameters.leftParenthesisExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n      parameters.spacingAfterLeftParenthesisExcerpt = this._tryReadSpacingAndNewlines(tokenReader);\r\n    }\r\n\r\n    // Read the member identifier or symbol\r\n    if (tokenReader.peekTokenKind() === TokenKind.LeftSquareBracket) {\r\n      parameters.memberSymbol = this._parseMemberSymbol(tokenReader, nodeForErrorContext);\r\n      if (!parameters.memberSymbol) {\r\n        return undefined;\r\n      }\r\n    } else {\r\n      parameters.memberIdentifier = this._parseMemberIdentifier(\r\n        tokenReader,\r\n        tokenSequenceForErrorContext,\r\n        nodeForErrorContext\r\n      );\r\n\r\n      if (!parameters.memberIdentifier) {\r\n        return undefined;\r\n      }\r\n    }\r\n    parameters.spacingAfterMemberExcerpt = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    // Read the colon\r\n    if (tokenReader.peekTokenKind() === TokenKind.Colon) {\r\n      tokenReader.readToken();\r\n\r\n      parameters.colonExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n      parameters.spacingAfterColonExcerpt = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n      if (!parameters.leftParenthesisExcerpt) {\r\n        // In the current TSDoc draft standard, a member reference with a selector requires the parentheses.\r\n        // It would be reasonable to make the parentheses optional, and we are contemplating simplifying the\r\n        // notation in the future.  But for now the parentheses are required.\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.ReferenceSelectorMissingParens,\r\n          'Syntax error in declaration reference: the member selector must be enclosed in parentheses',\r\n          parameters.colonExcerpt,\r\n          nodeForErrorContext\r\n        );\r\n        return undefined;\r\n      }\r\n\r\n      // If there is a colon, then read the selector\r\n      parameters.selector = this._parseMemberSelector(\r\n        tokenReader,\r\n        parameters.colonExcerpt,\r\n        nodeForErrorContext\r\n      );\r\n      if (!parameters.selector) {\r\n        return undefined;\r\n      }\r\n\r\n      parameters.spacingAfterSelectorExcerpt = this._tryReadSpacingAndNewlines(tokenReader);\r\n    } else {\r\n      if (parameters.leftParenthesisExcerpt) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.ReferenceMissingColon,\r\n          'Expecting a colon after the identifier because the expression is in parentheses',\r\n          parameters.leftParenthesisExcerpt,\r\n          nodeForErrorContext\r\n        );\r\n        return undefined;\r\n      }\r\n    }\r\n\r\n    // Read the right parenthesis\r\n    if (parameters.leftParenthesisExcerpt) {\r\n      if (tokenReader.peekTokenKind() !== TokenKind.RightParenthesis) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.ReferenceMissingRightParen,\r\n          'Expecting a matching right parenthesis',\r\n          parameters.leftParenthesisExcerpt,\r\n          nodeForErrorContext\r\n        );\r\n        return undefined;\r\n      }\r\n\r\n      tokenReader.readToken();\r\n\r\n      parameters.rightParenthesisExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n      parameters.spacingAfterRightParenthesisExcerpt = this._tryReadSpacingAndNewlines(tokenReader);\r\n    }\r\n\r\n    return new DocMemberReference(parameters);\r\n  }\r\n\r\n  private _parseMemberSymbol(\r\n    tokenReader: TokenReader,\r\n    nodeForErrorContext: DocNode\r\n  ): DocMemberSymbol | undefined {\r\n    // Read the \"[\"\r\n    if (tokenReader.peekTokenKind() !== TokenKind.LeftSquareBracket) {\r\n      // This should be impossible since the caller ensures that peekTokenKind() === TokenKind.LeftSquareBracket\r\n      throw new Error('Expecting \"[\"');\r\n    }\r\n\r\n    tokenReader.readToken();\r\n    const leftBracketExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const spacingAfterLeftBracketExcerpt: TokenSequence | undefined =\r\n      this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    // Read the declaration reference\r\n    const declarationReference: DocDeclarationReference | undefined = this._parseDeclarationReference(\r\n      tokenReader,\r\n      leftBracketExcerpt,\r\n      nodeForErrorContext\r\n    );\r\n\r\n    if (!declarationReference) {\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ReferenceSymbolSyntax,\r\n        'Missing declaration reference in symbol reference',\r\n        leftBracketExcerpt,\r\n        nodeForErrorContext\r\n      );\r\n\r\n      return undefined;\r\n    }\r\n\r\n    // (We don't need to worry about spacing here since _parseDeclarationReference() absorbs trailing spaces)\r\n\r\n    // Read the \"]\"\r\n    if (tokenReader.peekTokenKind() !== TokenKind.RightSquareBracket) {\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ReferenceMissingRightBracket,\r\n        'Missing closing square bracket for symbol reference',\r\n        leftBracketExcerpt,\r\n        nodeForErrorContext\r\n      );\r\n\r\n      return undefined;\r\n    }\r\n\r\n    tokenReader.readToken();\r\n    const rightBracketExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    return new DocMemberSymbol({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      leftBracketExcerpt,\r\n      spacingAfterLeftBracketExcerpt,\r\n      symbolReference: declarationReference,\r\n      rightBracketExcerpt\r\n    });\r\n  }\r\n\r\n  private _parseMemberIdentifier(\r\n    tokenReader: TokenReader,\r\n    tokenSequenceForErrorContext: TokenSequence,\r\n    nodeForErrorContext: DocNode\r\n  ): DocMemberIdentifier | undefined {\r\n    let leftQuoteExcerpt: TokenSequence | undefined = undefined;\r\n    let rightQuoteExcerpt: TokenSequence | undefined = undefined;\r\n\r\n    // Is this a quoted identifier?\r\n    if (tokenReader.peekTokenKind() === TokenKind.DoubleQuote) {\r\n      // Read the opening '\"'\r\n      tokenReader.readToken();\r\n      leftQuoteExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n      // Read the text inside the quotes\r\n      while (tokenReader.peekTokenKind() !== TokenKind.DoubleQuote) {\r\n        if (tokenReader.peekTokenKind() === TokenKind.EndOfInput) {\r\n          this._parserContext.log.addMessageForTokenSequence(\r\n            TSDocMessageId.ReferenceMissingQuote,\r\n            'Unexpected end of input inside quoted member identifier',\r\n            leftQuoteExcerpt,\r\n            nodeForErrorContext\r\n          );\r\n          return undefined;\r\n        }\r\n\r\n        tokenReader.readToken();\r\n      }\r\n\r\n      if (tokenReader.isAccumulatedSequenceEmpty()) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.ReferenceEmptyIdentifier,\r\n          'The quoted identifier cannot be empty',\r\n          leftQuoteExcerpt,\r\n          nodeForErrorContext\r\n        );\r\n        return undefined;\r\n      }\r\n\r\n      const identifierExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n      // Read the closing '\"\"\r\n      tokenReader.readToken(); // read the quote\r\n      rightQuoteExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n      return new DocMemberIdentifier({\r\n        parsed: true,\r\n        configuration: this._configuration,\r\n\r\n        leftQuoteExcerpt,\r\n        identifierExcerpt,\r\n        rightQuoteExcerpt\r\n      });\r\n    } else {\r\n      // Otherwise assume it's a valid TypeScript identifier\r\n\r\n      let done: boolean = false;\r\n      while (!done) {\r\n        switch (tokenReader.peekTokenKind()) {\r\n          case TokenKind.AsciiWord:\r\n          case TokenKind.DollarSign:\r\n            tokenReader.readToken();\r\n            break;\r\n          default:\r\n            done = true;\r\n            break;\r\n        }\r\n      }\r\n\r\n      if (tokenReader.isAccumulatedSequenceEmpty()) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.ReferenceMissingIdentifier,\r\n          'Syntax error in declaration reference: expecting a member identifier',\r\n          tokenSequenceForErrorContext,\r\n          nodeForErrorContext\r\n        );\r\n        return undefined;\r\n      }\r\n\r\n      const identifierExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n      const identifier: string = identifierExcerpt.toString();\r\n\r\n      const explanation: string | undefined =\r\n        StringChecks.explainIfInvalidUnquotedMemberIdentifier(identifier);\r\n      if (explanation) {\r\n        this._parserContext.log.addMessageForTokenSequence(\r\n          TSDocMessageId.ReferenceUnquotedIdentifier,\r\n          explanation,\r\n          identifierExcerpt,\r\n          nodeForErrorContext\r\n        );\r\n        return undefined;\r\n      }\r\n\r\n      return new DocMemberIdentifier({\r\n        parsed: true,\r\n        configuration: this._configuration,\r\n\r\n        leftQuoteExcerpt,\r\n        identifierExcerpt,\r\n        rightQuoteExcerpt\r\n      });\r\n    }\r\n  }\r\n\r\n  private _parseMemberSelector(\r\n    tokenReader: TokenReader,\r\n    tokenSequenceForErrorContext: TokenSequence,\r\n    nodeForErrorContext: DocNode\r\n  ): DocMemberSelector | undefined {\r\n    if (tokenReader.peekTokenKind() !== TokenKind.AsciiWord) {\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ReferenceMissingLabel,\r\n        'Expecting a selector label after the colon',\r\n        tokenSequenceForErrorContext,\r\n        nodeForErrorContext\r\n      );\r\n    }\r\n\r\n    const selector: string = tokenReader.readToken().toString();\r\n    const selectorExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const docMemberSelector: DocMemberSelector = new DocMemberSelector({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      selectorExcerpt,\r\n      selector\r\n    });\r\n\r\n    if (docMemberSelector.errorMessage) {\r\n      this._parserContext.log.addMessageForTokenSequence(\r\n        TSDocMessageId.ReferenceSelectorSyntax,\r\n        docMemberSelector.errorMessage,\r\n        selectorExcerpt,\r\n        nodeForErrorContext\r\n      );\r\n      return undefined;\r\n    }\r\n\r\n    return docMemberSelector;\r\n  }\r\n\r\n  private _parseHtmlStartTag(tokenReader: TokenReader): DocNode {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n    const marker: number = tokenReader.createMarker();\r\n\r\n    // Read the \"<\" delimiter\r\n    const lessThanToken: Token = tokenReader.readToken();\r\n    if (lessThanToken.kind !== TokenKind.LessThan) {\r\n      // This would be a parser bug -- the caller of _parseHtmlStartTag() should have verified this while\r\n      // looking ahead\r\n      throw new Error('Expecting an HTML tag starting with \"<\"');\r\n    }\r\n\r\n    // NOTE: CommonMark does not permit whitespace after the \"<\"\r\n\r\n    const openingDelimiterExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    // Read the element name\r\n    const nameExcerpt: ResultOrFailure<TokenSequence> = this._parseHtmlName(tokenReader);\r\n    if (isFailure(nameExcerpt)) {\r\n      return this._backtrackAndCreateErrorForFailure(\r\n        tokenReader,\r\n        marker,\r\n        'Invalid HTML element: ',\r\n        nameExcerpt\r\n      );\r\n    }\r\n\r\n    const spacingAfterNameExcerpt: TokenSequence | undefined = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    const htmlAttributes: DocHtmlAttribute[] = [];\r\n\r\n    // Read the attributes until we see a \">\" or \"/>\"\r\n    while (tokenReader.peekTokenKind() === TokenKind.AsciiWord) {\r\n      // Read the attribute\r\n      const attributeNode: ResultOrFailure<DocHtmlAttribute> = this._parseHtmlAttribute(tokenReader);\r\n      if (isFailure(attributeNode)) {\r\n        return this._backtrackAndCreateErrorForFailure(\r\n          tokenReader,\r\n          marker,\r\n          'The HTML element has an invalid attribute: ',\r\n          attributeNode\r\n        );\r\n      }\r\n\r\n      htmlAttributes.push(attributeNode);\r\n    }\r\n\r\n    // Read the closing \"/>\" or \">\" as the Excerpt.suffix\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n    const endDelimiterMarker: number = tokenReader.createMarker();\r\n\r\n    let selfClosingTag: boolean = false;\r\n    if (tokenReader.peekTokenKind() === TokenKind.Slash) {\r\n      tokenReader.readToken();\r\n      selfClosingTag = true;\r\n    }\r\n    if (tokenReader.peekTokenKind() !== TokenKind.GreaterThan) {\r\n      const failure: IFailure = this._createFailureForTokensSince(\r\n        tokenReader,\r\n        TSDocMessageId.HtmlTagMissingGreaterThan,\r\n        'Expecting an attribute or \">\" or \"/>\"',\r\n        endDelimiterMarker\r\n      );\r\n      return this._backtrackAndCreateErrorForFailure(\r\n        tokenReader,\r\n        marker,\r\n        'The HTML tag has invalid syntax: ',\r\n        failure\r\n      );\r\n    }\r\n    tokenReader.readToken();\r\n\r\n    const closingDelimiterExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    // NOTE: We don't read excerptParameters.separator here, since if there is any it\r\n    // will be represented as DocPlainText.\r\n\r\n    return new DocHtmlStartTag({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      openingDelimiterExcerpt,\r\n\r\n      nameExcerpt,\r\n      spacingAfterNameExcerpt,\r\n\r\n      htmlAttributes,\r\n\r\n      selfClosingTag,\r\n\r\n      closingDelimiterExcerpt\r\n    });\r\n  }\r\n\r\n  private _parseHtmlAttribute(tokenReader: TokenReader): ResultOrFailure<DocHtmlAttribute> {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n\r\n    // Read the attribute name\r\n    const nameExcerpt: ResultOrFailure<TokenSequence> = this._parseHtmlName(tokenReader);\r\n    if (isFailure(nameExcerpt)) {\r\n      return nameExcerpt;\r\n    }\r\n\r\n    const spacingAfterNameExcerpt: TokenSequence | undefined = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    // Read the equals\r\n    if (tokenReader.peekTokenKind() !== TokenKind.Equals) {\r\n      return this._createFailureForToken(\r\n        tokenReader,\r\n        TSDocMessageId.HtmlTagMissingEquals,\r\n        'Expecting \"=\" after HTML attribute name'\r\n      );\r\n    }\r\n    tokenReader.readToken();\r\n\r\n    const equalsExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const spacingAfterEqualsExcerpt: TokenSequence | undefined = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    // Read the attribute value\r\n    const attributeValue: ResultOrFailure<string> = this._parseHtmlString(tokenReader);\r\n    if (isFailure(attributeValue)) {\r\n      return attributeValue;\r\n    }\r\n\r\n    const valueExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const spacingAfterValueExcerpt: TokenSequence | undefined = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    return new DocHtmlAttribute({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      nameExcerpt,\r\n      spacingAfterNameExcerpt,\r\n\r\n      equalsExcerpt,\r\n      spacingAfterEqualsExcerpt,\r\n\r\n      valueExcerpt,\r\n      spacingAfterValueExcerpt\r\n    });\r\n  }\r\n\r\n  private _parseHtmlString(tokenReader: TokenReader): ResultOrFailure<string> {\r\n    const marker: number = tokenReader.createMarker();\r\n    const quoteTokenKind: TokenKind = tokenReader.peekTokenKind();\r\n    if (quoteTokenKind !== TokenKind.DoubleQuote && quoteTokenKind !== TokenKind.SingleQuote) {\r\n      return this._createFailureForToken(\r\n        tokenReader,\r\n        TSDocMessageId.HtmlTagMissingString,\r\n        'Expecting an HTML string starting with a single-quote or double-quote character'\r\n      );\r\n    }\r\n    tokenReader.readToken();\r\n\r\n    let textWithoutQuotes: string = '';\r\n\r\n    for (;;) {\r\n      const peekedTokenKind: TokenKind = tokenReader.peekTokenKind();\r\n      // Did we find the matching token?\r\n      if (peekedTokenKind === quoteTokenKind) {\r\n        tokenReader.readToken(); // extract the quote\r\n        break;\r\n      }\r\n      if (peekedTokenKind === TokenKind.EndOfInput || peekedTokenKind === TokenKind.Newline) {\r\n        return this._createFailureForToken(\r\n          tokenReader,\r\n          TSDocMessageId.HtmlStringMissingQuote,\r\n          'The HTML string is missing its closing quote',\r\n          marker\r\n        );\r\n      }\r\n      textWithoutQuotes += tokenReader.readToken().toString();\r\n    }\r\n\r\n    // The next attribute cannot start immediately after this one\r\n    if (tokenReader.peekTokenKind() === TokenKind.AsciiWord) {\r\n      return this._createFailureForToken(\r\n        tokenReader,\r\n        TSDocMessageId.TextAfterHtmlString,\r\n        'The next character after a closing quote must be spacing or punctuation'\r\n      );\r\n    }\r\n\r\n    return textWithoutQuotes;\r\n  }\r\n\r\n  private _parseHtmlEndTag(tokenReader: TokenReader): DocNode {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n    const marker: number = tokenReader.createMarker();\r\n\r\n    // Read the \"</\" delimiter\r\n    const lessThanToken: Token = tokenReader.peekToken();\r\n    if (lessThanToken.kind !== TokenKind.LessThan) {\r\n      return this._backtrackAndCreateError(\r\n        tokenReader,\r\n        marker,\r\n        TSDocMessageId.MissingHtmlEndTag,\r\n        'Expecting an HTML tag starting with \"</\"'\r\n      );\r\n    }\r\n    tokenReader.readToken();\r\n\r\n    const slashToken: Token = tokenReader.peekToken();\r\n    if (slashToken.kind !== TokenKind.Slash) {\r\n      return this._backtrackAndCreateError(\r\n        tokenReader,\r\n        marker,\r\n        TSDocMessageId.MissingHtmlEndTag,\r\n        'Expecting an HTML tag starting with \"</\"'\r\n      );\r\n    }\r\n    tokenReader.readToken();\r\n\r\n    // NOTE: Spaces are not permitted here\r\n    // https://www.w3.org/TR/html5/syntax.html#end-tags\r\n\r\n    const openingDelimiterExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    // Read the tag name\r\n    const nameExcerpt: ResultOrFailure<TokenSequence> = this._parseHtmlName(tokenReader);\r\n    if (isFailure(nameExcerpt)) {\r\n      return this._backtrackAndCreateErrorForFailure(\r\n        tokenReader,\r\n        marker,\r\n        'Expecting an HTML element name: ',\r\n        nameExcerpt\r\n      );\r\n    }\r\n\r\n    const spacingAfterNameExcerpt: TokenSequence | undefined = this._tryReadSpacingAndNewlines(tokenReader);\r\n\r\n    // Read the closing \">\"\r\n    if (tokenReader.peekTokenKind() !== TokenKind.GreaterThan) {\r\n      const failure: IFailure = this._createFailureForToken(\r\n        tokenReader,\r\n        TSDocMessageId.HtmlTagMissingGreaterThan,\r\n        'Expecting a closing \">\" for the HTML tag'\r\n      );\r\n      return this._backtrackAndCreateErrorForFailure(tokenReader, marker, '', failure);\r\n    }\r\n    tokenReader.readToken();\r\n\r\n    const closingDelimiterExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    return new DocHtmlEndTag({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      openingDelimiterExcerpt,\r\n\r\n      nameExcerpt,\r\n      spacingAfterNameExcerpt,\r\n\r\n      closingDelimiterExcerpt\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Parses an HTML name such as an element name or attribute name.\r\n   */\r\n  private _parseHtmlName(tokenReader: TokenReader): ResultOrFailure<TokenSequence> {\r\n    const marker: number = tokenReader.createMarker();\r\n\r\n    if (tokenReader.peekTokenKind() === TokenKind.Spacing) {\r\n      return this._createFailureForTokensSince(\r\n        tokenReader,\r\n        TSDocMessageId.MalformedHtmlName,\r\n        'A space is not allowed here',\r\n        marker\r\n      );\r\n    }\r\n\r\n    let done: boolean = false;\r\n    while (!done) {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.Hyphen:\r\n        case TokenKind.Period:\r\n        case TokenKind.AsciiWord:\r\n          tokenReader.readToken();\r\n          break;\r\n        default:\r\n          done = true;\r\n          break;\r\n      }\r\n    }\r\n    const excerpt: TokenSequence | undefined = tokenReader.tryExtractAccumulatedSequence();\r\n\r\n    if (!excerpt) {\r\n      return this._createFailureForToken(\r\n        tokenReader,\r\n        TSDocMessageId.MalformedHtmlName,\r\n        'Expecting an HTML name'\r\n      );\r\n    }\r\n\r\n    const htmlName: string = excerpt.toString();\r\n\r\n    const explanation: string | undefined = StringChecks.explainIfInvalidHtmlName(htmlName);\r\n\r\n    if (explanation) {\r\n      return this._createFailureForTokensSince(\r\n        tokenReader,\r\n        TSDocMessageId.MalformedHtmlName,\r\n        explanation,\r\n        marker\r\n      );\r\n    }\r\n\r\n    if (\r\n      this._configuration.validation.reportUnsupportedHtmlElements &&\r\n      !this._configuration.isHtmlElementSupported(htmlName)\r\n    ) {\r\n      return this._createFailureForToken(\r\n        tokenReader,\r\n        TSDocMessageId.UnsupportedHtmlElementName,\r\n        `The HTML element name ${JSON.stringify(htmlName)} is not defined by your TSDoc configuration`,\r\n        marker\r\n      );\r\n    }\r\n\r\n    return excerpt;\r\n  }\r\n\r\n  private _parseFencedCode(tokenReader: TokenReader): DocNode {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n\r\n    const startMarker: number = tokenReader.createMarker();\r\n    const endOfOpeningDelimiterMarker: number = startMarker + 2;\r\n\r\n    switch (tokenReader.peekPreviousTokenKind()) {\r\n      case TokenKind.Newline:\r\n      case TokenKind.EndOfInput:\r\n        break;\r\n      default:\r\n        return this._backtrackAndCreateErrorRange(\r\n          tokenReader,\r\n          startMarker,\r\n          // include the three backticks so they don't get reinterpreted as a code span\r\n          endOfOpeningDelimiterMarker,\r\n          TSDocMessageId.CodeFenceOpeningIndent,\r\n          'The opening backtick for a code fence must appear at the start of the line'\r\n        );\r\n    }\r\n\r\n    // Read the opening ``` delimiter\r\n    let openingDelimiter: string = '';\r\n    openingDelimiter += tokenReader.readToken();\r\n    openingDelimiter += tokenReader.readToken();\r\n    openingDelimiter += tokenReader.readToken();\r\n\r\n    if (openingDelimiter !== '```') {\r\n      // This would be a parser bug -- the caller of _parseFencedCode() should have verified this while\r\n      // looking ahead to distinguish code spans/fences\r\n      throw new Error('Expecting three backticks');\r\n    }\r\n\r\n    const openingFenceExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    // Read any spaces after the delimiter,\r\n    // but NOT the Newline since that goes with the spacingAfterLanguageExcerpt\r\n    while (tokenReader.peekTokenKind() === TokenKind.Spacing) {\r\n      tokenReader.readToken();\r\n    }\r\n\r\n    const spacingAfterOpeningFenceExcerpt: TokenSequence | undefined =\r\n      tokenReader.tryExtractAccumulatedSequence();\r\n\r\n    // Read the language specifier (if present) and newline\r\n    let done: boolean = false;\r\n    let startOfPaddingMarker: number | undefined = undefined;\r\n    while (!done) {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.Spacing:\r\n        case TokenKind.Newline:\r\n          if (startOfPaddingMarker === undefined) {\r\n            // Starting a new run of spacing characters\r\n            startOfPaddingMarker = tokenReader.createMarker();\r\n          }\r\n          if (tokenReader.peekTokenKind() === TokenKind.Newline) {\r\n            done = true;\r\n          }\r\n          tokenReader.readToken();\r\n          break;\r\n        case TokenKind.Backtick:\r\n          const failure: IFailure = this._createFailureForToken(\r\n            tokenReader,\r\n            TSDocMessageId.CodeFenceSpecifierSyntax,\r\n            'The language specifier cannot contain backtick characters'\r\n          );\r\n          return this._backtrackAndCreateErrorRangeForFailure(\r\n            tokenReader,\r\n            startMarker,\r\n            endOfOpeningDelimiterMarker,\r\n            'Error parsing code fence: ',\r\n            failure\r\n          );\r\n        case TokenKind.EndOfInput:\r\n          const failure2: IFailure = this._createFailureForToken(\r\n            tokenReader,\r\n            TSDocMessageId.CodeFenceMissingDelimiter,\r\n            'Missing closing delimiter'\r\n          );\r\n          return this._backtrackAndCreateErrorRangeForFailure(\r\n            tokenReader,\r\n            startMarker,\r\n            endOfOpeningDelimiterMarker,\r\n            'Error parsing code fence: ',\r\n            failure2\r\n          );\r\n        default:\r\n          // more non-spacing content\r\n          startOfPaddingMarker = undefined;\r\n          tokenReader.readToken();\r\n          break;\r\n      }\r\n    }\r\n\r\n    // At this point, we must have accumulated at least a newline token.\r\n    // Example: \"pov-ray sdl    \\n\"\r\n    const restOfLineExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    // Example: \"pov-ray sdl\"\r\n    const languageExcerpt: TokenSequence = restOfLineExcerpt.getNewSequence(\r\n      restOfLineExcerpt.startIndex,\r\n      startOfPaddingMarker!\r\n    );\r\n\r\n    // Example: \"    \\n\"\r\n    const spacingAfterLanguageExcerpt: TokenSequence | undefined = restOfLineExcerpt.getNewSequence(\r\n      startOfPaddingMarker!,\r\n      restOfLineExcerpt.endIndex\r\n    );\r\n\r\n    // Read the code content until we see the closing ``` delimiter\r\n    let codeEndMarker: number = -1;\r\n    let closingFenceStartMarker: number = -1;\r\n    done = false;\r\n    let tokenBeforeDelimiter: Token;\r\n    while (!done) {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.EndOfInput:\r\n          const failure2: IFailure = this._createFailureForToken(\r\n            tokenReader,\r\n            TSDocMessageId.CodeFenceMissingDelimiter,\r\n            'Missing closing delimiter'\r\n          );\r\n          return this._backtrackAndCreateErrorRangeForFailure(\r\n            tokenReader,\r\n            startMarker,\r\n            endOfOpeningDelimiterMarker,\r\n            'Error parsing code fence: ',\r\n            failure2\r\n          );\r\n        case TokenKind.Newline:\r\n          tokenBeforeDelimiter = tokenReader.readToken();\r\n          codeEndMarker = tokenReader.createMarker();\r\n\r\n          while (tokenReader.peekTokenKind() === TokenKind.Spacing) {\r\n            tokenBeforeDelimiter = tokenReader.readToken();\r\n          }\r\n\r\n          if (tokenReader.peekTokenKind() !== TokenKind.Backtick) {\r\n            break;\r\n          }\r\n          closingFenceStartMarker = tokenReader.createMarker();\r\n          tokenReader.readToken(); // first backtick\r\n\r\n          if (tokenReader.peekTokenKind() !== TokenKind.Backtick) {\r\n            break;\r\n          }\r\n          tokenReader.readToken(); // second backtick\r\n\r\n          if (tokenReader.peekTokenKind() !== TokenKind.Backtick) {\r\n            break;\r\n          }\r\n          tokenReader.readToken(); // third backtick\r\n\r\n          done = true;\r\n          break;\r\n        default:\r\n          tokenReader.readToken();\r\n          break;\r\n      }\r\n    }\r\n\r\n    if (tokenBeforeDelimiter!.kind !== TokenKind.Newline) {\r\n      this._parserContext.log.addMessageForTextRange(\r\n        TSDocMessageId.CodeFenceClosingIndent,\r\n        'The closing delimiter for a code fence must not be indented',\r\n        tokenBeforeDelimiter!.range\r\n      );\r\n    }\r\n\r\n    // Example: \"code 1\\ncode 2\\n  ```\"\r\n    const codeAndDelimiterExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    // Example: \"code 1\\ncode 2\\n\"\r\n    const codeExcerpt: TokenSequence = codeAndDelimiterExcerpt.getNewSequence(\r\n      codeAndDelimiterExcerpt.startIndex,\r\n      codeEndMarker\r\n    );\r\n\r\n    // Example: \"  \"\r\n    const spacingBeforeClosingFenceExcerpt: TokenSequence | undefined =\r\n      codeAndDelimiterExcerpt.getNewSequence(codeEndMarker, closingFenceStartMarker);\r\n\r\n    // Example: \"```\"\r\n    const closingFenceExcerpt: TokenSequence = codeAndDelimiterExcerpt.getNewSequence(\r\n      closingFenceStartMarker,\r\n      codeAndDelimiterExcerpt.endIndex\r\n    );\r\n\r\n    // Read the spacing and newline after the closing delimiter\r\n    done = false;\r\n    while (!done) {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.Spacing:\r\n          tokenReader.readToken();\r\n          break;\r\n        case TokenKind.Newline:\r\n          done = true;\r\n          tokenReader.readToken();\r\n          break;\r\n        case TokenKind.EndOfInput:\r\n          done = true;\r\n          break;\r\n        default:\r\n          this._parserContext.log.addMessageForTextRange(\r\n            TSDocMessageId.CodeFenceClosingSyntax,\r\n            'Unexpected characters after closing delimiter for code fence',\r\n            tokenReader.peekToken().range\r\n          );\r\n          done = true;\r\n          break;\r\n      }\r\n    }\r\n\r\n    // Example: \"   \\n\"\r\n    const spacingAfterClosingFenceExcerpt: TokenSequence | undefined =\r\n      tokenReader.tryExtractAccumulatedSequence();\r\n\r\n    return new DocFencedCode({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      openingFenceExcerpt,\r\n      spacingAfterOpeningFenceExcerpt,\r\n\r\n      languageExcerpt,\r\n      spacingAfterLanguageExcerpt,\r\n\r\n      codeExcerpt,\r\n\r\n      spacingBeforeClosingFenceExcerpt,\r\n      closingFenceExcerpt,\r\n      spacingAfterClosingFenceExcerpt\r\n    });\r\n  }\r\n\r\n  private _parseCodeSpan(tokenReader: TokenReader): DocNode {\r\n    tokenReader.assertAccumulatedSequenceIsEmpty();\r\n    const marker: number = tokenReader.createMarker();\r\n\r\n    // Parse the opening backtick\r\n    if (tokenReader.peekTokenKind() !== TokenKind.Backtick) {\r\n      // This would be a parser bug -- the caller of _parseCodeSpan() should have verified this while\r\n      // looking ahead to distinguish code spans/fences\r\n      throw new Error('Expecting a code span starting with a backtick character \"`\"');\r\n    }\r\n\r\n    tokenReader.readToken(); // read the backtick\r\n\r\n    const openingDelimiterExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    let codeExcerpt: TokenSequence | undefined = undefined;\r\n    let closingDelimiterExcerpt: TokenSequence | undefined = undefined;\r\n\r\n    // Parse the content backtick\r\n    for (;;) {\r\n      const peekedTokenKind: TokenKind = tokenReader.peekTokenKind();\r\n      // Did we find the matching token?\r\n      if (peekedTokenKind === TokenKind.Backtick) {\r\n        if (tokenReader.isAccumulatedSequenceEmpty()) {\r\n          return this._backtrackAndCreateErrorRange(\r\n            tokenReader,\r\n            marker,\r\n            marker + 1,\r\n            TSDocMessageId.CodeSpanEmpty,\r\n            'A code span must contain at least one character between the backticks'\r\n          );\r\n        }\r\n\r\n        codeExcerpt = tokenReader.extractAccumulatedSequence();\r\n\r\n        tokenReader.readToken();\r\n        closingDelimiterExcerpt = tokenReader.extractAccumulatedSequence();\r\n        break;\r\n      }\r\n      if (peekedTokenKind === TokenKind.EndOfInput || peekedTokenKind === TokenKind.Newline) {\r\n        return this._backtrackAndCreateError(\r\n          tokenReader,\r\n          marker,\r\n          TSDocMessageId.CodeSpanMissingDelimiter,\r\n          'The code span is missing its closing backtick'\r\n        );\r\n      }\r\n      tokenReader.readToken();\r\n    }\r\n\r\n    return new DocCodeSpan({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      openingDelimiterExcerpt,\r\n\r\n      codeExcerpt,\r\n\r\n      closingDelimiterExcerpt\r\n    });\r\n  }\r\n\r\n  private _tryReadSpacingAndNewlines(tokenReader: TokenReader): TokenSequence | undefined {\r\n    let done: boolean = false;\r\n    do {\r\n      switch (tokenReader.peekTokenKind()) {\r\n        case TokenKind.Spacing:\r\n        case TokenKind.Newline:\r\n          tokenReader.readToken();\r\n          break;\r\n        default:\r\n          done = true;\r\n          break;\r\n      }\r\n    } while (!done);\r\n    return tokenReader.tryExtractAccumulatedSequence();\r\n  }\r\n\r\n  /**\r\n   * Read the next token, and report it as a DocErrorText node.\r\n   */\r\n  private _createError(\r\n    tokenReader: TokenReader,\r\n    messageId: TSDocMessageId,\r\n    errorMessage: string\r\n  ): DocErrorText {\r\n    tokenReader.readToken();\r\n\r\n    const textExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const docErrorText: DocErrorText = new DocErrorText({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      textExcerpt,\r\n\r\n      messageId,\r\n      errorMessage,\r\n      errorLocation: textExcerpt\r\n    });\r\n    this._parserContext.log.addMessageForDocErrorText(docErrorText);\r\n    return docErrorText;\r\n  }\r\n\r\n  /**\r\n   * Rewind to the specified marker, read the next token, and report it as a DocErrorText node.\r\n   */\r\n  private _backtrackAndCreateError(\r\n    tokenReader: TokenReader,\r\n    marker: number,\r\n    messageId: TSDocMessageId,\r\n    errorMessage: string\r\n  ): DocErrorText {\r\n    tokenReader.backtrackToMarker(marker);\r\n    return this._createError(tokenReader, messageId, errorMessage);\r\n  }\r\n\r\n  /**\r\n   * Rewind to the errorStartMarker, read the tokens up to and including errorInclusiveEndMarker,\r\n   * and report it as a DocErrorText node.\r\n   */\r\n  private _backtrackAndCreateErrorRange(\r\n    tokenReader: TokenReader,\r\n    errorStartMarker: number,\r\n    errorInclusiveEndMarker: number,\r\n    messageId: TSDocMessageId,\r\n    errorMessage: string\r\n  ): DocErrorText {\r\n    tokenReader.backtrackToMarker(errorStartMarker);\r\n    while (tokenReader.createMarker() !== errorInclusiveEndMarker) {\r\n      tokenReader.readToken();\r\n    }\r\n    if (tokenReader.peekTokenKind() !== TokenKind.EndOfInput) {\r\n      tokenReader.readToken();\r\n    }\r\n\r\n    const textExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const docErrorText: DocErrorText = new DocErrorText({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      textExcerpt,\r\n\r\n      messageId,\r\n      errorMessage: errorMessage,\r\n      errorLocation: textExcerpt\r\n    });\r\n    this._parserContext.log.addMessageForDocErrorText(docErrorText);\r\n    return docErrorText;\r\n  }\r\n\r\n  /**\r\n   * Rewind to the specified marker, read the next token, and report it as a DocErrorText node\r\n   * whose location is based on an IFailure.\r\n   */\r\n  private _backtrackAndCreateErrorForFailure(\r\n    tokenReader: TokenReader,\r\n    marker: number,\r\n    errorMessagePrefix: string,\r\n    failure: IFailure\r\n  ): DocErrorText {\r\n    tokenReader.backtrackToMarker(marker);\r\n    tokenReader.readToken();\r\n\r\n    const textExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const docErrorText: DocErrorText = new DocErrorText({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      textExcerpt,\r\n\r\n      messageId: failure.failureMessageId,\r\n      errorMessage: errorMessagePrefix + failure.failureMessage,\r\n      errorLocation: failure.failureLocation\r\n    });\r\n    this._parserContext.log.addMessageForDocErrorText(docErrorText);\r\n    return docErrorText;\r\n  }\r\n\r\n  /**\r\n   * Rewind to the errorStartMarker, read the tokens up to and including errorInclusiveEndMarker,\r\n   * and report it as a DocErrorText node whose location is based on an IFailure.\r\n   */\r\n  private _backtrackAndCreateErrorRangeForFailure(\r\n    tokenReader: TokenReader,\r\n    errorStartMarker: number,\r\n    errorInclusiveEndMarker: number,\r\n    errorMessagePrefix: string,\r\n    failure: IFailure\r\n  ): DocErrorText {\r\n    tokenReader.backtrackToMarker(errorStartMarker);\r\n    while (tokenReader.createMarker() !== errorInclusiveEndMarker) {\r\n      tokenReader.readToken();\r\n    }\r\n    if (tokenReader.peekTokenKind() !== TokenKind.EndOfInput) {\r\n      tokenReader.readToken();\r\n    }\r\n\r\n    const textExcerpt: TokenSequence = tokenReader.extractAccumulatedSequence();\r\n\r\n    const docErrorText: DocErrorText = new DocErrorText({\r\n      parsed: true,\r\n      configuration: this._configuration,\r\n\r\n      textExcerpt,\r\n\r\n      messageId: failure.failureMessageId,\r\n      errorMessage: errorMessagePrefix + failure.failureMessage,\r\n      errorLocation: failure.failureLocation\r\n    });\r\n    this._parserContext.log.addMessageForDocErrorText(docErrorText);\r\n    return docErrorText;\r\n  }\r\n\r\n  /**\r\n   * Creates an IFailure whose TokenSequence is a single token.  If a marker is not specified,\r\n   * then it is the current token.\r\n   */\r\n  private _createFailureForToken(\r\n    tokenReader: TokenReader,\r\n    failureMessageId: TSDocMessageId,\r\n    failureMessage: string,\r\n    tokenMarker?: number\r\n  ): IFailure {\r\n    if (!tokenMarker) {\r\n      tokenMarker = tokenReader.createMarker();\r\n    }\r\n\r\n    const tokenSequence: TokenSequence = new TokenSequence({\r\n      parserContext: this._parserContext,\r\n      startIndex: tokenMarker,\r\n      endIndex: tokenMarker + 1\r\n    });\r\n\r\n    return {\r\n      failureMessageId,\r\n      failureMessage,\r\n      failureLocation: tokenSequence\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates an IFailure whose TokenSequence starts from the specified marker and\r\n   * encompasses all tokens read since then.  If none were read, then the next token used.\r\n   */\r\n  private _createFailureForTokensSince(\r\n    tokenReader: TokenReader,\r\n    failureMessageId: TSDocMessageId,\r\n    failureMessage: string,\r\n    startMarker: number\r\n  ): IFailure {\r\n    let endMarker: number = tokenReader.createMarker();\r\n    if (endMarker < startMarker) {\r\n      // This would be a parser bug\r\n      throw new Error('Invalid startMarker');\r\n    }\r\n\r\n    if (endMarker === startMarker) {\r\n      ++endMarker;\r\n    }\r\n\r\n    const tokenSequence: TokenSequence = new TokenSequence({\r\n      parserContext: this._parserContext,\r\n      startIndex: startMarker,\r\n      endIndex: endMarker\r\n    });\r\n\r\n    return {\r\n      failureMessageId,\r\n      failureMessage,\r\n      failureLocation: tokenSequence\r\n    };\r\n  }\r\n}\r\n"]}