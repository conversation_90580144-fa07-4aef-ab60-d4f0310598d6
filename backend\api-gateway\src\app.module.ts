import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import { ThrottlerGuard } from '@nestjs/throttler';

// الوحدات
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { TransfersModule } from './modules/transfers/transfers.module';
import { WalletsModule } from './modules/wallets/wallets.module';
import { AgentsModule } from './modules/agents/agents.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { ReportsModule } from './modules/reports/reports.module';

// الخدمات المشتركة
import { DatabaseModule } from './shared/database/database.module';
import { RedisModule } from './shared/redis/redis.module';
import { LoggerModule } from './shared/logger/logger.module';

@Module({
  imports: [
    // إعدادات التطبيق
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // حماية من الهجمات
    ThrottlerModule.forRoot([
      {
        name: 'short',
        ttl: 1000, // 1 ثانية
        limit: 3, // 3 طلبات كحد أقصى
      },
      {
        name: 'medium',
        ttl: 10000, // 10 ثواني
        limit: 20, // 20 طلب كحد أقصى
      },
      {
        name: 'long',
        ttl: 60000, // دقيقة واحدة
        limit: 100, // 100 طلب كحد أقصى
      },
    ]),

    // قواعد البيانات والتخزين
    DatabaseModule,
    RedisModule,
    LoggerModule,

    // وحدات الأعمال
    AuthModule,
    UsersModule,
    TransfersModule,
    WalletsModule,
    AgentsModule,
    NotificationsModule,
    ReportsModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
