import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({
    summary: 'فحص صحة الخدمة',
    description: 'التحقق من حالة الخدمة وقواعد البيانات المتصلة',
  })
  @ApiResponse({
    status: 200,
    description: 'الخدمة تعمل بشكل طبيعي',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'string', example: '2024-01-15T10:30:00Z' },
        service: { type: 'string', example: 'auth-service' },
        version: { type: 'string', example: '1.0.0' },
        uptime: { type: 'number', example: 3600 },
        dependencies: {
          type: 'object',
          properties: {
            database: { type: 'string', example: 'healthy' },
            redis: { type: 'string', example: 'healthy' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 503,
    description: 'الخدمة غير متاحة',
  })
  async check() {
    return this.healthService.check();
  }

  @Get('ready')
  @ApiOperation({
    summary: 'فحص جاهزية الخدمة',
    description: 'التحقق من جاهزية الخدمة لاستقبال الطلبات',
  })
  @ApiResponse({
    status: 200,
    description: 'الخدمة جاهزة',
  })
  @ApiResponse({
    status: 503,
    description: 'الخدمة غير جاهزة',
  })
  async ready() {
    return this.healthService.ready();
  }

  @Get('live')
  @ApiOperation({
    summary: 'فحص حيوية الخدمة',
    description: 'التحقق من أن الخدمة ما زالت تعمل',
  })
  @ApiResponse({
    status: 200,
    description: 'الخدمة حية',
  })
  async live() {
    return this.healthService.live();
  }
}
