import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import * as helmet from 'helmet';
import * as compression from 'compression';

async function bootstrap() {
  const logger = new Logger('TransferService');
  
  try {
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);
    
    // Security middleware
    app.use(helmet());
    app.use(compression());
    
    // CORS configuration
    app.enableCors({
      origin: configService.get('CORS_ORIGIN')?.split(',') || ['http://localhost:3100'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });
    
    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );
    
    // API prefix
    app.setGlobalPrefix('api/v1');
    
    // Swagger documentation
    if (configService.get('SWAGGER_ENABLED') === 'true') {
      const config = new DocumentBuilder()
        .setTitle('WS Transfir Transfer Service')
        .setDescription('خدمة التحويلات المالية والمدفوعات')
        .setVersion('1.0.0')
        .addBearerAuth(
          {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
          },
          'JWT-auth',
        )
        .addTag('Transfers', 'إدارة التحويلات')
        .addTag('Payments', 'المدفوعات')
        .addTag('Exchange', 'أسعار الصرف')
        .addTag('Fees', 'الرسوم والعمولات')
        .addTag('Limits', 'حدود التحويل')
        .build();
      
      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('docs', app, document, {
        customSiteTitle: 'WS Transfir Transfer API',
        customfavIcon: '/favicon.ico',
        customCss: `
          .topbar-wrapper .link { content: url('/logo.png'); width: 120px; }
          .swagger-ui .topbar { background-color: #667eea; }
        `,
      });
    }
    
    // Start server
    const port = configService.get('TRANSFER_SERVICE_PORT') || 3003;
    const host = configService.get('API_GATEWAY_HOST') || '0.0.0.0';
    
    await app.listen(port, host);
    
    logger.log(`🚀 Transfer Service is running on: http://${host}:${port}`);
    logger.log(`📚 API Documentation: http://${host}:${port}/docs`);
    logger.log(`🏥 Health Check: http://${host}:${port}/api/v1/health`);
    
  } catch (error) {
    logger.error('❌ Failed to start Transfer Service:', error);
    process.exit(1);
  }
}

bootstrap();
