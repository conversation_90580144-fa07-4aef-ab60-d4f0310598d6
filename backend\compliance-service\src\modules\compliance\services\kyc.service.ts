import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { KycRecord, KycStatus, DocumentType } from '../entities/kyc-record.entity';
import { DocumentVerification } from '../entities/document-verification.entity';
import { CreateKycRecordDto } from '../dto/create-kyc-record.dto';
import { UpdateKycRecordDto } from '../dto/update-kyc-record.dto';

export interface KycVerificationResult {
  success: boolean;
  status: KycStatus;
  score: number;
  reasons: string[];
  recommendations: string[];
}

export interface DocumentAnalysisResult {
  isValid: boolean;
  confidence: number;
  extractedData: any;
  issues: string[];
}

@Injectable()
export class KycService {
  private readonly logger = new Logger(KycService.name);

  constructor(
    @InjectRepository(KycRecord)
    private readonly kycRepository: Repository<KycRecord>,
    @InjectRepository(DocumentVerification)
    private readonly documentRepository: Repository<DocumentVerification>,
    private readonly configService: ConfigService,
  ) {}

  async createKycRecord(createKycDto: CreateKycRecordDto): Promise<KycRecord> {
    // Check if user already has a KYC record
    const existingRecord = await this.kycRepository.findOne({
      where: { userId: createKycDto.userId },
    });

    if (existingRecord && existingRecord.status !== KycStatus.REJECTED) {
      throw new BadRequestException('المستخدم لديه طلب KYC موجود بالفعل');
    }

    const kycRecord = this.kycRepository.create({
      ...createKycDto,
      referenceNumber: this.generateReferenceNumber(),
      submittedAt: new Date(),
    });

    const savedRecord = await this.kycRepository.save(kycRecord);
    this.logger.log(`KYC record created: ${savedRecord.referenceNumber}`);

    // Start automatic verification process
    await this.initiateVerificationProcess(savedRecord.id);

    return savedRecord;
  }

  async updateKycRecord(
    kycId: string,
    updateKycDto: UpdateKycRecordDto,
  ): Promise<KycRecord> {
    const kycRecord = await this.findById(kycId);

    if (kycRecord.status === KycStatus.APPROVED) {
      throw new BadRequestException('لا يمكن تعديل طلب KYC المعتمد');
    }

    Object.assign(kycRecord, updateKycDto);
    kycRecord.lastUpdated = new Date();

    const updatedRecord = await this.kycRepository.save(kycRecord);
    this.logger.log(`KYC record updated: ${updatedRecord.referenceNumber}`);

    return updatedRecord;
  }

  async findById(kycId: string): Promise<KycRecord> {
    const kycRecord = await this.kycRepository.findOne({
      where: { id: kycId },
      relations: ['documentVerifications'],
    });

    if (!kycRecord) {
      throw new NotFoundException('سجل KYC غير موجود');
    }

    return kycRecord;
  }

  async findByUserId(userId: string): Promise<KycRecord | null> {
    return this.kycRepository.findOne({
      where: { userId },
      relations: ['documentVerifications'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByReferenceNumber(referenceNumber: string): Promise<KycRecord> {
    const kycRecord = await this.kycRepository.findOne({
      where: { referenceNumber },
      relations: ['documentVerifications'],
    });

    if (!kycRecord) {
      throw new NotFoundException('سجل KYC غير موجود');
    }

    return kycRecord;
  }

  async getAllKycRecords(
    status?: KycStatus,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ data: KycRecord[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    const queryBuilder = this.kycRepository.createQueryBuilder('kyc');

    if (status) {
      queryBuilder.where('kyc.status = :status', { status });
    }

    queryBuilder
      .orderBy('kyc.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return { data, total, totalPages };
  }

  async approveKyc(
    kycId: string,
    reviewerId: string,
    notes?: string,
  ): Promise<KycRecord> {
    const kycRecord = await this.findById(kycId);

    if (kycRecord.status === KycStatus.APPROVED) {
      throw new BadRequestException('طلب KYC معتمد بالفعل');
    }

    kycRecord.approve(reviewerId, notes);
    const approvedRecord = await this.kycRepository.save(kycRecord);

    this.logger.log(`KYC approved: ${approvedRecord.referenceNumber} by ${reviewerId}`);
    return approvedRecord;
  }

  async rejectKyc(
    kycId: string,
    reviewerId: string,
    reason: string,
    notes?: string,
  ): Promise<KycRecord> {
    const kycRecord = await this.findById(kycId);

    if (kycRecord.status === KycStatus.APPROVED) {
      throw new BadRequestException('لا يمكن رفض طلب KYC المعتمد');
    }

    kycRecord.reject(reviewerId, reason, notes);
    const rejectedRecord = await this.kycRepository.save(kycRecord);

    this.logger.log(`KYC rejected: ${rejectedRecord.referenceNumber} by ${reviewerId}`);
    return rejectedRecord;
  }

  async requestAdditionalInfo(
    kycId: string,
    reviewerId: string,
    requiredInfo: string[],
    notes?: string,
  ): Promise<KycRecord> {
    const kycRecord = await this.findById(kycId);

    kycRecord.requestAdditionalInfo(reviewerId, requiredInfo, notes);
    const updatedRecord = await this.kycRepository.save(kycRecord);

    this.logger.log(`Additional info requested for KYC: ${updatedRecord.referenceNumber}`);
    return updatedRecord;
  }

  async uploadDocument(
    kycId: string,
    documentType: DocumentType,
    fileUrl: string,
    fileName: string,
  ): Promise<DocumentVerification> {
    const kycRecord = await this.findById(kycId);

    if (kycRecord.status === KycStatus.APPROVED) {
      throw new BadRequestException('لا يمكن رفع مستندات لطلب KYC معتمد');
    }

    const document = this.documentRepository.create({
      kycRecordId: kycId,
      documentType,
      fileUrl,
      fileName,
      uploadedAt: new Date(),
    });

    const savedDocument = await this.documentRepository.save(document);

    // Start document verification
    await this.verifyDocument(savedDocument.id);

    this.logger.log(`Document uploaded for KYC: ${kycRecord.referenceNumber} - ${documentType}`);
    return savedDocument;
  }

  async verifyDocument(documentId: string): Promise<DocumentVerification> {
    const document = await this.documentRepository.findOne({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('المستند غير موجود');
    }

    try {
      // Simulate document verification (integrate with actual OCR/AI service)
      const analysisResult = await this.analyzeDocument(document);

      document.markAsVerified(
        analysisResult.isValid,
        analysisResult.confidence,
        analysisResult.extractedData,
        analysisResult.issues,
      );

      const verifiedDocument = await this.documentRepository.save(document);

      // Update KYC record status based on document verification
      await this.updateKycStatusBasedOnDocuments(document.kycRecordId);

      this.logger.log(`Document verified: ${documentId} - Valid: ${analysisResult.isValid}`);
      return verifiedDocument;
    } catch (error) {
      document.markAsFailed(error.message);
      await this.documentRepository.save(document);
      throw error;
    }
  }

  async getKycStatistics(): Promise<any> {
    const [
      totalRecords,
      pendingRecords,
      approvedRecords,
      rejectedRecords,
      underReviewRecords,
    ] = await Promise.all([
      this.kycRepository.count(),
      this.kycRepository.count({ where: { status: KycStatus.PENDING } }),
      this.kycRepository.count({ where: { status: KycStatus.APPROVED } }),
      this.kycRepository.count({ where: { status: KycStatus.REJECTED } }),
      this.kycRepository.count({ where: { status: KycStatus.UNDER_REVIEW } }),
    ]);

    const approvalRate = totalRecords > 0 ? (approvedRecords / totalRecords) * 100 : 0;

    // Get average processing time
    const avgProcessingTime = await this.getAverageProcessingTime();

    return {
      totalRecords,
      pendingRecords,
      approvedRecords,
      rejectedRecords,
      underReviewRecords,
      approvalRate: Math.round(approvalRate * 100) / 100,
      averageProcessingTimeHours: avgProcessingTime,
    };
  }

  private async initiateVerificationProcess(kycId: string): Promise<void> {
    const kycRecord = await this.findById(kycId);
    
    // Perform basic validation
    const validationResult = await this.performBasicValidation(kycRecord);
    
    if (validationResult.success) {
      kycRecord.status = KycStatus.UNDER_REVIEW;
    } else {
      kycRecord.status = KycStatus.PENDING;
      kycRecord.notes = validationResult.reasons.join('; ');
    }

    await this.kycRepository.save(kycRecord);
  }

  private async performBasicValidation(kycRecord: KycRecord): Promise<KycVerificationResult> {
    const reasons: string[] = [];
    let score = 100;

    // Validate personal information
    if (!kycRecord.personalInfo?.firstName || !kycRecord.personalInfo?.lastName) {
      reasons.push('معلومات الاسم غير مكتملة');
      score -= 20;
    }

    if (!kycRecord.personalInfo?.dateOfBirth) {
      reasons.push('تاريخ الميلاد مطلوب');
      score -= 15;
    }

    if (!kycRecord.personalInfo?.nationality) {
      reasons.push('الجنسية مطلوبة');
      score -= 10;
    }

    // Validate address information
    if (!kycRecord.addressInfo?.country || !kycRecord.addressInfo?.city) {
      reasons.push('معلومات العنوان غير مكتملة');
      score -= 15;
    }

    const success = score >= 70;
    const status = success ? KycStatus.UNDER_REVIEW : KycStatus.PENDING;

    return {
      success,
      status,
      score,
      reasons,
      recommendations: success ? [] : ['يرجى إكمال المعلومات المطلوبة'],
    };
  }

  private async analyzeDocument(document: DocumentVerification): Promise<DocumentAnalysisResult> {
    // Simulate document analysis (integrate with actual OCR/AI service)
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock analysis result
    const isValid = Math.random() > 0.2; // 80% success rate
    const confidence = Math.random() * 0.3 + 0.7; // 70-100% confidence

    return {
      isValid,
      confidence,
      extractedData: {
        documentNumber: `DOC${Date.now()}`,
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        issueDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      },
      issues: isValid ? [] : ['جودة الصورة منخفضة', 'بعض النصوص غير واضحة'],
    };
  }

  private async updateKycStatusBasedOnDocuments(kycId: string): Promise<void> {
    const kycRecord = await this.findById(kycId);
    const documents = kycRecord.documentVerifications || [];

    const requiredDocuments = [DocumentType.NATIONAL_ID, DocumentType.PROOF_OF_ADDRESS];
    const verifiedDocuments = documents.filter(doc => doc.isVerified);

    const hasAllRequiredDocs = requiredDocuments.every(reqType =>
      verifiedDocuments.some(doc => doc.documentType === reqType)
    );

    if (hasAllRequiredDocs && kycRecord.status === KycStatus.UNDER_REVIEW) {
      // Auto-approve if all documents are verified and basic validation passed
      kycRecord.status = KycStatus.APPROVED;
      kycRecord.approvedAt = new Date();
      kycRecord.approvedBy = 'system';
      kycRecord.reviewNotes = 'تم الاعتماد تلقائياً بعد التحقق من جميع المستندات';

      await this.kycRepository.save(kycRecord);
      this.logger.log(`KYC auto-approved: ${kycRecord.referenceNumber}`);
    }
  }

  private async getAverageProcessingTime(): Promise<number> {
    const approvedRecords = await this.kycRepository.find({
      where: { status: KycStatus.APPROVED },
      select: ['submittedAt', 'approvedAt'],
    });

    if (approvedRecords.length === 0) return 0;

    const totalHours = approvedRecords.reduce((sum, record) => {
      if (record.submittedAt && record.approvedAt) {
        const diffMs = record.approvedAt.getTime() - record.submittedAt.getTime();
        return sum + (diffMs / (1000 * 60 * 60)); // Convert to hours
      }
      return sum;
    }, 0);

    return Math.round((totalHours / approvedRecords.length) * 100) / 100;
  }

  private generateReferenceNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `KYC${timestamp.slice(-8)}${random}`;
  }
}
