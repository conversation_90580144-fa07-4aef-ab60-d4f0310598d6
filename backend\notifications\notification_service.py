"""
Notification Service
===================
خدمة الإشعارات المتقدمة متعددة القنوات
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import json
import uuid

import asyncpg
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class NotificationChannel(Enum):
    """قنوات الإشعارات"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    IN_APP = "in_app"
    WEBHOOK = "webhook"


class NotificationPriority(Enum):
    """أولويات الإشعارات"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class NotificationStatus(Enum):
    """حالات الإشعارات"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NotificationType(Enum):
    """أنواع الإشعارات"""
    TRANSACTION_SUCCESS = "transaction_success"
    TRANSACTION_FAILED = "transaction_failed"
    ACCOUNT_VERIFICATION = "account_verification"
    SECURITY_ALERT = "security_alert"
    PAYMENT_REMINDER = "payment_reminder"
    SYSTEM_MAINTENANCE = "system_maintenance"
    AGENT_COMMISSION = "agent_commission"
    KYC_UPDATE = "kyc_update"
    WALLET_BALANCE = "wallet_balance"
    PROMOTIONAL = "promotional"


@dataclass
class NotificationTemplate:
    """قالب الإشعار"""
    id: str
    name: str
    type: NotificationType
    channel: NotificationChannel
    subject_template: str
    body_template: str
    variables: List[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime


@dataclass
class NotificationRecipient:
    """مستقبل الإشعار"""
    user_id: str
    email: Optional[str] = None
    phone: Optional[str] = None
    push_token: Optional[str] = None
    language: str = "ar"
    timezone: str = "Asia/Riyadh"


@dataclass
class NotificationRequest:
    """طلب إرسال إشعار"""
    type: NotificationType
    channel: NotificationChannel
    recipient: NotificationRecipient
    priority: NotificationPriority
    data: Dict[str, Any]
    scheduled_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    template_id: Optional[str] = None


@dataclass
class NotificationResult:
    """نتيجة إرسال الإشعار"""
    notification_id: str
    status: NotificationStatus
    sent_at: Optional[datetime]
    delivered_at: Optional[datetime]
    error_message: Optional[str]
    provider_response: Optional[Dict[str, Any]]


class NotificationService:
    """خدمة الإشعارات المتقدمة"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Provider configurations
        self.email_provider = None
        self.sms_provider = None
        self.push_provider = None
        
        # Queue settings
        self.max_retries = 3
        self.retry_delay = 60  # seconds
        
        # Statistics
        self.notifications_sent = 0
        self.notifications_failed = 0
        self.delivery_rate = 0.0
        
        # Template cache
        self.template_cache = {}
        self.cache_duration = 3600  # 1 hour
    
    async def send_notification(
        self,
        request: NotificationRequest
    ) -> NotificationResult:
        """إرسال إشعار واحد"""
        try:
            logger.info(f"🔔 Sending notification: {request.type.value} via {request.channel.value}")
            
            # Generate notification ID
            notification_id = f"notif_{uuid.uuid4().hex[:12]}"
            
            # Get or create template
            template = await self._get_template(request.type, request.channel, request.template_id)
            
            if not template:
                raise ValueError(f"No template found for {request.type.value} via {request.channel.value}")
            
            # Render notification content
            subject, body = await self._render_notification(template, request.data, request.recipient.language)
            
            # Store notification in database
            await self._store_notification(
                notification_id=notification_id,
                request=request,
                template=template,
                subject=subject,
                body=body
            )
            
            # Send immediately or schedule
            if request.scheduled_at and request.scheduled_at > datetime.now():
                # Schedule for later
                await self._schedule_notification(notification_id, request.scheduled_at)
                status = NotificationStatus.PENDING
                sent_at = None
            else:
                # Send now
                result = await self._send_via_channel(
                    notification_id=notification_id,
                    channel=request.channel,
                    recipient=request.recipient,
                    subject=subject,
                    body=body,
                    data=request.data
                )
                status = result.status
                sent_at = result.sent_at
            
            # Update statistics
            if status == NotificationStatus.SENT:
                self.notifications_sent += 1
            elif status == NotificationStatus.FAILED:
                self.notifications_failed += 1
            
            self._update_delivery_rate()
            
            return NotificationResult(
                notification_id=notification_id,
                status=status,
                sent_at=sent_at,
                delivered_at=None,
                error_message=None,
                provider_response=None
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to send notification: {e}")
            self.notifications_failed += 1
            self._update_delivery_rate()
            
            return NotificationResult(
                notification_id=notification_id if 'notification_id' in locals() else None,
                status=NotificationStatus.FAILED,
                sent_at=None,
                delivered_at=None,
                error_message=str(e),
                provider_response=None
            )
    
    async def send_bulk_notifications(
        self,
        requests: List[NotificationRequest]
    ) -> List[NotificationResult]:
        """إرسال إشعارات متعددة"""
        try:
            logger.info(f"📢 Sending {len(requests)} bulk notifications")
            
            results = []
            
            # Process in batches to avoid overwhelming the system
            batch_size = 50
            for i in range(0, len(requests), batch_size):
                batch = requests[i:i + batch_size]
                
                # Send batch concurrently
                batch_tasks = [self.send_notification(request) for request in batch]
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # Handle results and exceptions
                for result in batch_results:
                    if isinstance(result, Exception):
                        logger.error(f"❌ Batch notification failed: {result}")
                        results.append(NotificationResult(
                            notification_id=None,
                            status=NotificationStatus.FAILED,
                            sent_at=None,
                            delivered_at=None,
                            error_message=str(result),
                            provider_response=None
                        ))
                    else:
                        results.append(result)
                
                # Small delay between batches
                if i + batch_size < len(requests):
                    await asyncio.sleep(0.1)
            
            successful = sum(1 for r in results if r.status == NotificationStatus.SENT)
            logger.info(f"✅ Bulk notifications completed: {successful}/{len(requests)} successful")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to send bulk notifications: {e}")
            return []
    
    async def send_transaction_notification(
        self,
        transaction_id: str,
        user_id: str,
        transaction_type: str,
        amount: float,
        currency: str,
        status: str,
        channels: List[NotificationChannel] = None
    ) -> List[NotificationResult]:
        """إرسال إشعار معاملة"""
        try:
            # Get user details
            user_details = await self._get_user_details(user_id)
            if not user_details:
                raise ValueError(f"User not found: {user_id}")
            
            # Determine notification type
            if status == "completed":
                notification_type = NotificationType.TRANSACTION_SUCCESS
            else:
                notification_type = NotificationType.TRANSACTION_FAILED
            
            # Default channels if not specified
            if not channels:
                channels = [NotificationChannel.EMAIL, NotificationChannel.IN_APP]
            
            # Prepare notification data
            data = {
                "transaction_id": transaction_id,
                "transaction_type": transaction_type,
                "amount": amount,
                "currency": currency,
                "status": status,
                "user_name": user_details.get("full_name", ""),
                "timestamp": datetime.now().isoformat()
            }
            
            # Create recipient
            recipient = NotificationRecipient(
                user_id=user_id,
                email=user_details.get("email"),
                phone=user_details.get("phone"),
                language=user_details.get("language", "ar"),
                timezone=user_details.get("timezone", "Asia/Riyadh")
            )
            
            # Send via all channels
            results = []
            for channel in channels:
                request = NotificationRequest(
                    type=notification_type,
                    channel=channel,
                    recipient=recipient,
                    priority=NotificationPriority.HIGH,
                    data=data
                )
                
                result = await self.send_notification(request)
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to send transaction notification: {e}")
            return []
    
    async def send_security_alert(
        self,
        user_id: str,
        alert_type: str,
        details: Dict[str, Any],
        priority: NotificationPriority = NotificationPriority.CRITICAL
    ) -> List[NotificationResult]:
        """إرسال تنبيه أمني"""
        try:
            # Get user details
            user_details = await self._get_user_details(user_id)
            if not user_details:
                raise ValueError(f"User not found: {user_id}")
            
            # Prepare notification data
            data = {
                "alert_type": alert_type,
                "user_name": user_details.get("full_name", ""),
                "timestamp": datetime.now().isoformat(),
                **details
            }
            
            # Create recipient
            recipient = NotificationRecipient(
                user_id=user_id,
                email=user_details.get("email"),
                phone=user_details.get("phone"),
                language=user_details.get("language", "ar")
            )
            
            # Send via multiple channels for security alerts
            channels = [NotificationChannel.EMAIL, NotificationChannel.SMS, NotificationChannel.IN_APP]
            
            results = []
            for channel in channels:
                request = NotificationRequest(
                    type=NotificationType.SECURITY_ALERT,
                    channel=channel,
                    recipient=recipient,
                    priority=priority,
                    data=data
                )
                
                result = await self.send_notification(request)
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to send security alert: {e}")
            return []
    
    async def send_system_announcement(
        self,
        title: str,
        message: str,
        target_users: List[str] = None,
        target_roles: List[str] = None,
        channels: List[NotificationChannel] = None,
        scheduled_at: datetime = None
    ) -> List[NotificationResult]:
        """إرسال إعلان نظام"""
        try:
            logger.info(f"📢 Sending system announcement: {title}")
            
            # Get target users
            if target_users:
                users = await self._get_users_by_ids(target_users)
            elif target_roles:
                users = await self._get_users_by_roles(target_roles)
            else:
                users = await self._get_all_active_users()
            
            # Default channels
            if not channels:
                channels = [NotificationChannel.IN_APP, NotificationChannel.EMAIL]
            
            # Prepare notification data
            data = {
                "title": title,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
            
            # Create notification requests
            requests = []
            for user in users:
                recipient = NotificationRecipient(
                    user_id=user["id"],
                    email=user.get("email"),
                    phone=user.get("phone"),
                    language=user.get("language", "ar")
                )
                
                for channel in channels:
                    request = NotificationRequest(
                        type=NotificationType.SYSTEM_MAINTENANCE,
                        channel=channel,
                        recipient=recipient,
                        priority=NotificationPriority.MEDIUM,
                        data=data,
                        scheduled_at=scheduled_at
                    )
                    requests.append(request)
            
            # Send bulk notifications
            results = await self.send_bulk_notifications(requests)
            
            logger.info(f"✅ System announcement sent to {len(users)} users via {len(channels)} channels")
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to send system announcement: {e}")
            return []
    
    async def get_notification_history(
        self,
        user_id: str = None,
        notification_type: NotificationType = None,
        channel: NotificationChannel = None,
        status: NotificationStatus = None,
        start_date: datetime = None,
        end_date: datetime = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """الحصول على تاريخ الإشعارات"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = []
                params = []
                param_count = 0
                
                if user_id:
                    param_count += 1
                    where_conditions.append(f"user_id = ${param_count}")
                    params.append(user_id)
                
                if notification_type:
                    param_count += 1
                    where_conditions.append(f"notification_type = ${param_count}")
                    params.append(notification_type.value)
                
                if channel:
                    param_count += 1
                    where_conditions.append(f"channel = ${param_count}")
                    params.append(channel.value)
                
                if status:
                    param_count += 1
                    where_conditions.append(f"status = ${param_count}")
                    params.append(status.value)
                
                if start_date:
                    param_count += 1
                    where_conditions.append(f"created_at >= ${param_count}")
                    params.append(start_date)
                
                if end_date:
                    param_count += 1
                    where_conditions.append(f"created_at <= ${param_count}")
                    params.append(end_date)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Add limit
                param_count += 1
                limit_clause = f"LIMIT ${param_count}"
                params.append(limit)
                
                query = f"""
                    SELECT 
                        id, user_id, notification_type, channel, priority,
                        subject, body, status, sent_at, delivered_at,
                        error_message, created_at, data
                    FROM notifications 
                    WHERE {where_clause}
                    ORDER BY created_at DESC
                    {limit_clause}
                """
                
                rows = await conn.fetch(query, *params)
                
                return [
                    {
                        "id": row["id"],
                        "user_id": row["user_id"],
                        "type": row["notification_type"],
                        "channel": row["channel"],
                        "priority": row["priority"],
                        "subject": row["subject"],
                        "body": row["body"],
                        "status": row["status"],
                        "sent_at": row["sent_at"].isoformat() if row["sent_at"] else None,
                        "delivered_at": row["delivered_at"].isoformat() if row["delivered_at"] else None,
                        "error_message": row["error_message"],
                        "created_at": row["created_at"].isoformat(),
                        "data": row["data"]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get notification history: {e}")
            return []
    
    async def get_notification_statistics(
        self,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> Dict[str, Any]:
        """الحصول على إحصائيات الإشعارات"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Date range filter
                date_filter = ""
                params = []
                
                if start_date and end_date:
                    date_filter = "WHERE created_at BETWEEN $1 AND $2"
                    params = [start_date, end_date]
                elif start_date:
                    date_filter = "WHERE created_at >= $1"
                    params = [start_date]
                elif end_date:
                    date_filter = "WHERE created_at <= $1"
                    params = [end_date]
                
                # Overall statistics
                stats_query = f"""
                    SELECT 
                        COUNT(*) as total_notifications,
                        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
                        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_count,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count
                    FROM notifications 
                    {date_filter}
                """
                
                stats = await conn.fetchrow(stats_query, *params)
                
                # Channel breakdown
                channel_query = f"""
                    SELECT 
                        channel,
                        COUNT(*) as count,
                        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
                    FROM notifications 
                    {date_filter}
                    GROUP BY channel
                    ORDER BY count DESC
                """
                
                channel_stats = await conn.fetch(channel_query, *params)
                
                # Type breakdown
                type_query = f"""
                    SELECT 
                        notification_type,
                        COUNT(*) as count,
                        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count
                    FROM notifications 
                    {date_filter}
                    GROUP BY notification_type
                    ORDER BY count DESC
                """
                
                type_stats = await conn.fetch(type_query, *params)
                
                # Calculate rates
                total = stats['total_notifications']
                delivery_rate = (stats['delivered_count'] / max(total, 1)) * 100
                failure_rate = (stats['failed_count'] / max(total, 1)) * 100
                
                return {
                    "overview": {
                        "total_notifications": total,
                        "sent_count": stats['sent_count'],
                        "delivered_count": stats['delivered_count'],
                        "failed_count": stats['failed_count'],
                        "pending_count": stats['pending_count'],
                        "delivery_rate": round(delivery_rate, 2),
                        "failure_rate": round(failure_rate, 2)
                    },
                    "by_channel": [
                        {
                            "channel": row['channel'],
                            "total": row['count'],
                            "sent": row['sent_count'],
                            "failed": row['failed_count'],
                            "success_rate": round((row['sent_count'] / max(row['count'], 1)) * 100, 2)
                        }
                        for row in channel_stats
                    ],
                    "by_type": [
                        {
                            "type": row['notification_type'],
                            "total": row['count'],
                            "sent": row['sent_count'],
                            "success_rate": round((row['sent_count'] / max(row['count'], 1)) * 100, 2)
                        }
                        for row in type_stats
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get notification statistics: {e}")
            return {}
    
    # Helper methods
    async def _get_template(
        self,
        notification_type: NotificationType,
        channel: NotificationChannel,
        template_id: str = None
    ) -> Optional[NotificationTemplate]:
        """الحصول على قالب الإشعار"""
        try:
            # Check cache first
            cache_key = f"{notification_type.value}_{channel.value}_{template_id or 'default'}"
            if cache_key in self.template_cache:
                cached_template = self.template_cache[cache_key]
                if (datetime.now() - cached_template['timestamp']).seconds < self.cache_duration:
                    return cached_template['template']
            
            async with self.db_connection.get_connection() as conn:
                if template_id:
                    query = """
                        SELECT * FROM notification_templates 
                        WHERE id = $1 AND is_active = true
                    """
                    params = [template_id]
                else:
                    query = """
                        SELECT * FROM notification_templates 
                        WHERE notification_type = $1 AND channel = $2 AND is_active = true
                        ORDER BY created_at DESC
                        LIMIT 1
                    """
                    params = [notification_type.value, channel.value]
                
                row = await conn.fetchrow(query, *params)
                
                if row:
                    template = NotificationTemplate(
                        id=row['id'],
                        name=row['name'],
                        type=NotificationType(row['notification_type']),
                        channel=NotificationChannel(row['channel']),
                        subject_template=row['subject_template'],
                        body_template=row['body_template'],
                        variables=row['variables'] or [],
                        is_active=row['is_active'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                    
                    # Cache the template
                    self.template_cache[cache_key] = {
                        'template': template,
                        'timestamp': datetime.now()
                    }
                    
                    return template
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get template: {e}")
            return None
    
    async def _render_notification(
        self,
        template: NotificationTemplate,
        data: Dict[str, Any],
        language: str = "ar"
    ) -> tuple[str, str]:
        """تقديم محتوى الإشعار"""
        try:
            # Simple template rendering (in production, use a proper template engine)
            subject = template.subject_template
            body = template.body_template
            
            # Replace variables
            for key, value in data.items():
                placeholder = f"{{{key}}}"
                subject = subject.replace(placeholder, str(value))
                body = body.replace(placeholder, str(value))
            
            return subject, body
            
        except Exception as e:
            logger.error(f"❌ Failed to render notification: {e}")
            return template.subject_template, template.body_template
    
    async def _store_notification(
        self,
        notification_id: str,
        request: NotificationRequest,
        template: NotificationTemplate,
        subject: str,
        body: str
    ):
        """حفظ الإشعار في قاعدة البيانات"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO notifications (
                        id, user_id, notification_type, channel, priority,
                        template_id, subject, body, status, data,
                        scheduled_at, expires_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                """
                
                await conn.execute(
                    query,
                    notification_id,
                    request.recipient.user_id,
                    request.type.value,
                    request.channel.value,
                    request.priority.value,
                    template.id,
                    subject,
                    body,
                    NotificationStatus.PENDING.value,
                    json.dumps(request.data),
                    request.scheduled_at,
                    request.expires_at
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store notification: {e}")
    
    async def _send_via_channel(
        self,
        notification_id: str,
        channel: NotificationChannel,
        recipient: NotificationRecipient,
        subject: str,
        body: str,
        data: Dict[str, Any]
    ) -> NotificationResult:
        """إرسال عبر قناة محددة"""
        try:
            if channel == NotificationChannel.EMAIL:
                return await self._send_email(notification_id, recipient, subject, body)
            elif channel == NotificationChannel.SMS:
                return await self._send_sms(notification_id, recipient, body)
            elif channel == NotificationChannel.PUSH:
                return await self._send_push(notification_id, recipient, subject, body, data)
            elif channel == NotificationChannel.IN_APP:
                return await self._send_in_app(notification_id, recipient, subject, body, data)
            else:
                raise ValueError(f"Unsupported channel: {channel.value}")
                
        except Exception as e:
            logger.error(f"❌ Failed to send via {channel.value}: {e}")
            return NotificationResult(
                notification_id=notification_id,
                status=NotificationStatus.FAILED,
                sent_at=None,
                delivered_at=None,
                error_message=str(e),
                provider_response=None
            )
    
    async def _send_email(
        self,
        notification_id: str,
        recipient: NotificationRecipient,
        subject: str,
        body: str
    ) -> NotificationResult:
        """إرسال بريد إلكتروني"""
        # Implementation would integrate with email provider (SendGrid, AWS SES, etc.)
        logger.info(f"📧 Sending email to {recipient.email}")
        
        # Simulate email sending
        await asyncio.sleep(0.1)
        
        return NotificationResult(
            notification_id=notification_id,
            status=NotificationStatus.SENT,
            sent_at=datetime.now(),
            delivered_at=None,
            error_message=None,
            provider_response={"message_id": f"email_{notification_id}"}
        )
    
    async def _send_sms(
        self,
        notification_id: str,
        recipient: NotificationRecipient,
        message: str
    ) -> NotificationResult:
        """إرسال رسالة نصية"""
        # Implementation would integrate with SMS provider (Twilio, AWS SNS, etc.)
        logger.info(f"📱 Sending SMS to {recipient.phone}")
        
        # Simulate SMS sending
        await asyncio.sleep(0.1)
        
        return NotificationResult(
            notification_id=notification_id,
            status=NotificationStatus.SENT,
            sent_at=datetime.now(),
            delivered_at=None,
            error_message=None,
            provider_response={"message_id": f"sms_{notification_id}"}
        )
    
    async def _send_push(
        self,
        notification_id: str,
        recipient: NotificationRecipient,
        title: str,
        body: str,
        data: Dict[str, Any]
    ) -> NotificationResult:
        """إرسال إشعار مباشر"""
        # Implementation would integrate with push provider (FCM, APNS, etc.)
        logger.info(f"🔔 Sending push notification to {recipient.user_id}")
        
        # Simulate push sending
        await asyncio.sleep(0.1)
        
        return NotificationResult(
            notification_id=notification_id,
            status=NotificationStatus.SENT,
            sent_at=datetime.now(),
            delivered_at=None,
            error_message=None,
            provider_response={"message_id": f"push_{notification_id}"}
        )
    
    async def _send_in_app(
        self,
        notification_id: str,
        recipient: NotificationRecipient,
        title: str,
        body: str,
        data: Dict[str, Any]
    ) -> NotificationResult:
        """إرسال إشعار داخل التطبيق"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO in_app_notifications (
                        id, user_id, title, message, data, is_read
                    ) VALUES ($1, $2, $3, $4, $5, $6)
                """
                
                await conn.execute(
                    query,
                    f"in_app_{notification_id}",
                    recipient.user_id,
                    title,
                    body,
                    json.dumps(data),
                    False
                )
                
                logger.info(f"📱 In-app notification stored for user {recipient.user_id}")
                
                return NotificationResult(
                    notification_id=notification_id,
                    status=NotificationStatus.SENT,
                    sent_at=datetime.now(),
                    delivered_at=datetime.now(),
                    error_message=None,
                    provider_response={"stored": True}
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to send in-app notification: {e}")
            return NotificationResult(
                notification_id=notification_id,
                status=NotificationStatus.FAILED,
                sent_at=None,
                delivered_at=None,
                error_message=str(e),
                provider_response=None
            )
    
    async def _get_user_details(self, user_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على تفاصيل المستخدم"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        id, email, phone, first_name, last_name,
                        language, country, timezone
                    FROM users 
                    WHERE id = $1 AND deleted_at IS NULL
                """
                
                row = await conn.fetchrow(query, user_id)
                
                if row:
                    return {
                        "id": row["id"],
                        "email": row["email"],
                        "phone": row["phone"],
                        "full_name": f"{row['first_name']} {row['last_name']}",
                        "language": row["language"] or "ar",
                        "country": row["country"],
                        "timezone": row["timezone"] or "Asia/Riyadh"
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get user details: {e}")
            return None
    
    async def _get_users_by_ids(self, user_ids: List[str]) -> List[Dict[str, Any]]:
        """الحصول على المستخدمين بالمعرفات"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        id, email, phone, first_name, last_name,
                        language, country, timezone
                    FROM users 
                    WHERE id = ANY($1) AND deleted_at IS NULL
                """
                
                rows = await conn.fetch(query, user_ids)
                
                return [
                    {
                        "id": row["id"],
                        "email": row["email"],
                        "phone": row["phone"],
                        "full_name": f"{row['first_name']} {row['last_name']}",
                        "language": row["language"] or "ar",
                        "country": row["country"],
                        "timezone": row["timezone"] or "Asia/Riyadh"
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get users by IDs: {e}")
            return []
    
    async def _get_users_by_roles(self, roles: List[str]) -> List[Dict[str, Any]]:
        """الحصول على المستخدمين بالأدوار"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        id, email, phone, first_name, last_name,
                        language, country, timezone
                    FROM users 
                    WHERE role = ANY($1) AND is_active = true AND deleted_at IS NULL
                """
                
                rows = await conn.fetch(query, roles)
                
                return [
                    {
                        "id": row["id"],
                        "email": row["email"],
                        "phone": row["phone"],
                        "full_name": f"{row['first_name']} {row['last_name']}",
                        "language": row["language"] or "ar",
                        "country": row["country"],
                        "timezone": row["timezone"] or "Asia/Riyadh"
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get users by roles: {e}")
            return []
    
    async def _get_all_active_users(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المستخدمين النشطين"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT 
                        id, email, phone, first_name, last_name,
                        language, country, timezone
                    FROM users 
                    WHERE is_active = true AND deleted_at IS NULL
                    LIMIT 10000
                """
                
                rows = await conn.fetch(query)
                
                return [
                    {
                        "id": row["id"],
                        "email": row["email"],
                        "phone": row["phone"],
                        "full_name": f"{row['first_name']} {row['last_name']}",
                        "language": row["language"] or "ar",
                        "country": row["country"],
                        "timezone": row["timezone"] or "Asia/Riyadh"
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get all active users: {e}")
            return []
    
    async def _schedule_notification(self, notification_id: str, scheduled_at: datetime):
        """جدولة الإشعار"""
        # Implementation would use a task queue (Celery, RQ, etc.)
        logger.info(f"⏰ Scheduling notification {notification_id} for {scheduled_at}")
    
    def _update_delivery_rate(self):
        """تحديث معدل التسليم"""
        total = self.notifications_sent + self.notifications_failed
        if total > 0:
            self.delivery_rate = (self.notifications_sent / total) * 100
    
    async def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمة"""
        return {
            "notifications_sent": self.notifications_sent,
            "notifications_failed": self.notifications_failed,
            "delivery_rate": round(self.delivery_rate, 2),
            "template_cache_size": len(self.template_cache)
        }
