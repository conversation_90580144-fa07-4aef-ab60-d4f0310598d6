import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany,
} from 'typeorm';
import { Decimal } from 'decimal.js';

export enum WalletStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  FROZEN = 'frozen',
  CLOSED = 'closed',
}

export enum WalletType {
  PERSONAL = 'personal',
  BUSINESS = 'business',
  SAVINGS = 'savings',
  ESCROW = 'escrow',
}

@Entity('wallets')
@Index(['userId'])
@Index(['currency'])
@Index(['status'])
@Index(['walletNumber'], { unique: true })
export class Wallet {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Wallet Identification
  @Column({ unique: true, length: 20 })
  walletNumber: string;

  @Column({ type: 'uuid' })
  userId: string;

  // Wallet Details
  @Column({
    type: 'enum',
    enum: WalletType,
    default: WalletType.PERSONAL,
  })
  type: WalletType;

  @Column({ length: 3 })
  currency: string;

  @Column({ length: 100, nullable: true })
  name?: string;

  @Column({ length: 500, nullable: true })
  description?: string;

  // Balance Information
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  availableBalance: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  pendingBalance: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  frozenBalance: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  totalBalance: string;

  // Limits
  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  dailyLimit?: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  monthlyLimit?: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  transactionLimit?: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  dailySpent: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  monthlySpent: string;

  // Status and Settings
  @Column({
    type: 'enum',
    enum: WalletStatus,
    default: WalletStatus.ACTIVE,
  })
  status: WalletStatus;

  @Column({ default: true })
  allowIncoming: boolean;

  @Column({ default: true })
  allowOutgoing: boolean;

  @Column({ default: false })
  requiresApproval: boolean;

  @Column({ default: true })
  notificationsEnabled: boolean;

  // Security
  @Column({ nullable: true, length: 6 })
  pin?: string;

  @Column({ default: false })
  pinEnabled: boolean;

  @Column({ type: 'timestamp', nullable: true })
  lastTransactionAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastAccessAt?: Date;

  // Metadata
  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  closedAt?: Date;

  // Computed properties
  get isActive(): boolean {
    return this.status === WalletStatus.ACTIVE;
  }

  get isFrozen(): boolean {
    return this.status === WalletStatus.FROZEN;
  }

  get canReceive(): boolean {
    return this.isActive && this.allowIncoming;
  }

  get canSend(): boolean {
    return this.isActive && this.allowOutgoing;
  }

  get effectiveBalance(): Decimal {
    return new Decimal(this.availableBalance);
  }

  get totalBalanceDecimal(): Decimal {
    const available = new Decimal(this.availableBalance);
    const pending = new Decimal(this.pendingBalance);
    const frozen = new Decimal(this.frozenBalance);
    return available.plus(pending).plus(frozen);
  }

  // Methods
  updateBalance(): void {
    const available = new Decimal(this.availableBalance);
    const pending = new Decimal(this.pendingBalance);
    const frozen = new Decimal(this.frozenBalance);
    this.totalBalance = available.plus(pending).plus(frozen).toString();
  }

  canWithdraw(amount: string): boolean {
    if (!this.canSend) return false;
    
    const withdrawAmount = new Decimal(amount);
    const available = new Decimal(this.availableBalance);
    
    return available.greaterThanOrEqualTo(withdrawAmount);
  }

  checkDailyLimit(amount: string): boolean {
    if (!this.dailyLimit) return true;
    
    const requestAmount = new Decimal(amount);
    const spent = new Decimal(this.dailySpent);
    const limit = new Decimal(this.dailyLimit);
    
    return spent.plus(requestAmount).lessThanOrEqualTo(limit);
  }

  checkMonthlyLimit(amount: string): boolean {
    if (!this.monthlyLimit) return true;
    
    const requestAmount = new Decimal(amount);
    const spent = new Decimal(this.monthlySpent);
    const limit = new Decimal(this.monthlyLimit);
    
    return spent.plus(requestAmount).lessThanOrEqualTo(limit);
  }

  checkTransactionLimit(amount: string): boolean {
    if (!this.transactionLimit) return true;
    
    const requestAmount = new Decimal(amount);
    const limit = new Decimal(this.transactionLimit);
    
    return requestAmount.lessThanOrEqualTo(limit);
  }

  credit(amount: string): void {
    const creditAmount = new Decimal(amount);
    const currentBalance = new Decimal(this.availableBalance);
    this.availableBalance = currentBalance.plus(creditAmount).toString();
    this.updateBalance();
    this.lastTransactionAt = new Date();
  }

  debit(amount: string): boolean {
    if (!this.canWithdraw(amount)) return false;
    
    const debitAmount = new Decimal(amount);
    const currentBalance = new Decimal(this.availableBalance);
    this.availableBalance = currentBalance.minus(debitAmount).toString();
    this.updateBalance();
    this.lastTransactionAt = new Date();
    
    return true;
  }

  freeze(amount: string): boolean {
    if (!this.canWithdraw(amount)) return false;
    
    const freezeAmount = new Decimal(amount);
    const currentAvailable = new Decimal(this.availableBalance);
    const currentFrozen = new Decimal(this.frozenBalance);
    
    this.availableBalance = currentAvailable.minus(freezeAmount).toString();
    this.frozenBalance = currentFrozen.plus(freezeAmount).toString();
    this.updateBalance();
    
    return true;
  }

  unfreeze(amount: string): boolean {
    const unfreezeAmount = new Decimal(amount);
    const currentFrozen = new Decimal(this.frozenBalance);
    
    if (currentFrozen.lessThan(unfreezeAmount)) return false;
    
    const currentAvailable = new Decimal(this.availableBalance);
    
    this.frozenBalance = currentFrozen.minus(unfreezeAmount).toString();
    this.availableBalance = currentAvailable.plus(unfreezeAmount).toString();
    this.updateBalance();
    
    return true;
  }

  generateWalletNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${this.currency}${timestamp.slice(-8)}${random}`;
  }

  suspend(reason?: string): void {
    this.status = WalletStatus.SUSPENDED;
    if (reason && this.metadata) {
      this.metadata.suspensionReason = reason;
      this.metadata.suspendedAt = new Date().toISOString();
    }
  }

  activate(): void {
    this.status = WalletStatus.ACTIVE;
    if (this.metadata) {
      delete this.metadata.suspensionReason;
      delete this.metadata.suspendedAt;
      this.metadata.reactivatedAt = new Date().toISOString();
    }
  }

  close(reason?: string): void {
    this.status = WalletStatus.CLOSED;
    this.closedAt = new Date();
    this.allowIncoming = false;
    this.allowOutgoing = false;
    
    if (reason && this.metadata) {
      this.metadata.closureReason = reason;
    }
  }
}
