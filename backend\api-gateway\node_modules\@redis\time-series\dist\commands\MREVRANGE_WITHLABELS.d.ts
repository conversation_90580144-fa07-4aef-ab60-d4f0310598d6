import { RedisCommandArguments } from '@redis/client/dist/lib/commands';
import { Timestamp, MRangeWithLabelsOptions, Filter } from '.';
export declare const IS_READ_ONLY = true;
export declare function transformArguments(fromTimestamp: Timestamp, toTimestamp: Timestamp, filters: Filter, options?: MRangeWithLabelsOptions): RedisCommandArguments;
export { transformMRangeWithLabelsReply as transformReply } from '.';
