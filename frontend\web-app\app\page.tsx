'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowRightIcon, 
  CurrencyDollarIcon, 
  ShieldCheckIcon, 
  ClockIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

// بيانات الميزات
const features = [
  {
    icon: CurrencyDollarIcon,
    titleKey: 'features.fastTransfers.title',
    descriptionKey: 'features.fastTransfers.description',
    color: 'text-blue-600',
  },
  {
    icon: ShieldCheckIcon,
    titleKey: 'features.security.title',
    descriptionKey: 'features.security.description',
    color: 'text-green-600',
  },
  {
    icon: ClockIcon,
    titleKey: 'features.support.title',
    descriptionKey: 'features.support.description',
    color: 'text-purple-600',
  },
  {
    icon: GlobeAltIcon,
    titleKey: 'features.global.title',
    descriptionKey: 'features.global.description',
    color: 'text-orange-600',
  },
  {
    icon: DevicePhoneMobileIcon,
    titleKey: 'features.mobile.title',
    descriptionKey: 'features.mobile.description',
    color: 'text-pink-600',
  },
  {
    icon: ChartBarIcon,
    titleKey: 'features.rates.title',
    descriptionKey: 'features.rates.description',
    color: 'text-indigo-600',
  },
];

// إحصائيات النظام
const stats = [
  { numberKey: 'stats.users', labelKey: 'stats.usersLabel' },
  { numberKey: 'stats.transfers', labelKey: 'stats.transfersLabel' },
  { numberKey: 'stats.countries', labelKey: 'stats.countriesLabel' },
  { numberKey: 'stats.satisfaction', labelKey: 'stats.satisfactionLabel' },
];

export default function HomePage() {
  const router = useRouter();
  const { state: authState } = useAuth();
  const { t, isRTL } = useLanguage();

  // إعادة توجيه المستخدمين المسجلين إلى لوحة التحكم
  useEffect(() => {
    if (authState.isAuthenticated) {
      router.push('/dashboard');
    }
  }, [authState.isAuthenticated, router]);

  // إذا كان المستخدم مسجل الدخول، لا تعرض الصفحة
  if (authState.isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Header />
      
      <main>
        {/* القسم الرئيسي */}
        <section className="relative overflow-hidden py-20 sm:py-32">
          <div className="container-custom">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* النص الرئيسي */}
              <motion.div
                initial={{ opacity: 0, x: isRTL ? 50 : -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="text-center lg:text-right"
              >
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                  <span className="text-gradient">
                    {t('home.hero.title')}
                  </span>
                </h1>
                
                <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                  {t('home.hero.subtitle')}
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <Button
                    size="lg"
                    onClick={() => router.push('/auth/register')}
                    className="group"
                  >
                    {t('home.hero.getStarted')}
                    <ArrowRightIcon className={`w-5 h-5 transition-transform group-hover:${isRTL ? '-translate-x-1' : 'translate-x-1'}`} />
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => router.push('/auth/login')}
                  >
                    {t('home.hero.login')}
                  </Button>
                </div>
              </motion.div>

              {/* الصورة التوضيحية */}
              <motion.div
                initial={{ opacity: 0, x: isRTL ? -50 : 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="relative z-10">
                  <img
                    src="/images/hero-illustration.svg"
                    alt={t('home.hero.imageAlt')}
                    className="w-full h-auto"
                  />
                </div>
                
                {/* تأثيرات بصرية */}
                <div className="absolute -top-4 -left-4 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
                <div className="absolute -bottom-8 -right-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
                <div className="absolute -bottom-4 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* قسم الإحصائيات */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="container-custom">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="text-3xl lg:text-4xl font-bold text-primary-600 mb-2">
                    {t(stat.numberKey)}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300">
                    {t(stat.labelKey)}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* قسم الميزات */}
        <section className="py-20 bg-gray-50 dark:bg-gray-900">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {t('home.features.title')}
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {t('home.features.subtitle')}
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="p-6 h-full hover:shadow-lg transition-shadow duration-300">
                    <div className={`w-12 h-12 ${feature.color} mb-4`}>
                      <feature.icon className="w-full h-full" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                      {t(feature.titleKey)}
                    </h3>
                    
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {t(feature.descriptionKey)}
                    </p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* قسم الدعوة للعمل */}
        <section className="py-20 bg-gradient-to-r from-primary-600 to-primary-700">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center text-white"
            >
              <h2 className="text-3xl lg:text-4xl font-bold mb-4">
                {t('home.cta.title')}
              </h2>
              
              <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                {t('home.cta.subtitle')}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="secondary"
                  size="lg"
                  onClick={() => router.push('/auth/register')}
                  className="bg-white text-primary-600 hover:bg-gray-100"
                >
                  {t('home.cta.register')}
                </Button>
                
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white text-white hover:bg-white hover:text-primary-600"
                >
                  {t('home.cta.learnMore')}
                </Button>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
