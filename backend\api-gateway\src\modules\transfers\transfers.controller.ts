import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';

import { TransfersService } from './transfers.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

// DTOs
import { CreateTransferDto } from './dto/create-transfer.dto';
import { ReceiveTransferDto } from './dto/receive-transfer.dto';
import { TransferQuoteDto } from './dto/transfer-quote.dto';
import { TransferHistoryQueryDto } from './dto/transfer-history-query.dto';

@ApiTags('transfers')
@Controller('transfers')
@UseGuards(ThrottlerGuard, JwtAuthGuard)
@ApiBearerAuth()
export class TransfersController {
  constructor(private readonly transfersService: TransfersService) {}

  @Post('quote')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'الحصول على عرض سعر للتحويل' })
  @ApiResponse({ status: 200, description: 'عرض السعر' })
  @ApiResponse({ status: 400, description: 'بيانات غير صحيحة' })
  async getQuote(@Body() quoteDto: TransferQuoteDto, @Request() req) {
    return this.transfersService.getTransferQuote(req.user.id, quoteDto);
  }

  @Post('send')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'إرسال حوالة مالية' })
  @ApiResponse({ status: 201, description: 'تم إرسال الحوالة بنجاح' })
  @ApiResponse({ status: 400, description: 'بيانات غير صحيحة' })
  @ApiResponse({ status: 402, description: 'رصيد غير كافي' })
  async sendTransfer(@Body() createTransferDto: CreateTransferDto, @Request() req) {
    return this.transfersService.sendTransfer(req.user.id, createTransferDto);
  }

  @Post('receive')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'استلام حوالة مالية' })
  @ApiResponse({ status: 200, description: 'تم استلام الحوالة بنجاح' })
  @ApiResponse({ status: 400, description: 'بيانات غير صحيحة' })
  @ApiResponse({ status: 404, description: 'الحوالة غير موجودة' })
  async receiveTransfer(@Body() receiveTransferDto: ReceiveTransferDto, @Request() req) {
    return this.transfersService.receiveTransfer(req.user.id, receiveTransferDto);
  }

  @Get('history')
  @ApiOperation({ summary: 'سجل التحويلات' })
  @ApiResponse({ status: 200, description: 'سجل التحويلات' })
  async getTransferHistory(
    @Query() queryDto: TransferHistoryQueryDto,
    @Request() req,
  ) {
    return this.transfersService.getTransferHistory(req.user.id, queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'تفاصيل التحويل' })
  @ApiResponse({ status: 200, description: 'تفاصيل التحويل' })
  @ApiResponse({ status: 404, description: 'التحويل غير موجود' })
  async getTransferDetails(@Param('id') transferId: string, @Request() req) {
    return this.transfersService.getTransferDetails(req.user.id, transferId);
  }

  @Put(':id/cancel')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'إلغاء التحويل' })
  @ApiResponse({ status: 200, description: 'تم إلغاء التحويل' })
  @ApiResponse({ status: 400, description: 'لا يمكن إلغاء التحويل' })
  async cancelTransfer(@Param('id') transferId: string, @Request() req) {
    return this.transfersService.cancelTransfer(req.user.id, transferId);
  }

  @Get('tracking/:trackingCode')
  @ApiOperation({ summary: 'تتبع التحويل برقم التتبع' })
  @ApiResponse({ status: 200, description: 'حالة التحويل' })
  @ApiResponse({ status: 404, description: 'رقم التتبع غير موجود' })
  async trackTransfer(@Param('trackingCode') trackingCode: string) {
    return this.transfersService.trackTransfer(trackingCode);
  }

  @Get('rates/current')
  @ApiOperation({ summary: 'أسعار الصرف الحالية' })
  @ApiResponse({ status: 200, description: 'أسعار الصرف' })
  async getCurrentExchangeRates() {
    return this.transfersService.getCurrentExchangeRates();
  }

  @Post('schedule')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'جدولة تحويل مستقبلي' })
  @ApiResponse({ status: 201, description: 'تم جدولة التحويل' })
  async scheduleTransfer(@Body() scheduleDto: any, @Request() req) {
    return this.transfersService.scheduleTransfer(req.user.id, scheduleDto);
  }

  @Get('scheduled/list')
  @ApiOperation({ summary: 'قائمة التحويلات المجدولة' })
  @ApiResponse({ status: 200, description: 'التحويلات المجدولة' })
  async getScheduledTransfers(@Request() req) {
    return this.transfersService.getScheduledTransfers(req.user.id);
  }

  @Post('bulk')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'تحويل مجمع (للشركات)' })
  @ApiResponse({ status: 201, description: 'تم إرسال التحويلات المجمعة' })
  async bulkTransfer(@Body() bulkDto: any, @Request() req) {
    return this.transfersService.bulkTransfer(req.user.id, bulkDto);
  }
}
