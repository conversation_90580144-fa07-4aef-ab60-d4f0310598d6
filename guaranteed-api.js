const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

// Health endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'WS Transfir API is running',
    timestamp: new Date().toISOString(),
    port: 3000
  });
});

// Auth endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  console.log('Login attempt:', email);
ECHO is off.
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      success: true,
      token: 'admin-token-123',
      user: {
        id: '1',
        firstName: 'مدير',
        lastName: 'النظام',
        email: email,
        role: 'admin'
      }
    });
  } else if (email && password) {
    res.json({
      success: true,
      token: 'user-token-456',
      user: {
        id: '2',
        firstName: 'أحمد',
        lastName: 'محمد',
        email: email,
        role: 'user'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة'
    });
  }
});

app.post('/api/auth/register', (req, res) => {
  res.json({
    success: true,
    message: 'تم إنشاء الحساب بنجاح'
  });
});

// Profile endpoint
app.get('/api/profile/me', (req, res) => {
  res.json({
    id: '1',
    firstName: 'أحمد',
    lastName: 'محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    isVerified: true,
    completionPercentage: 85
  });
});

// Transfers endpoint
app.get('/api/transfers', (req, res) => {
  res.json({
    data: [
      {
        id: '1',
        referenceNumber: 'WS20241225001',
        amount: '1,500.00',
        currency: 'SAR',
        receiverName: 'أحمد محمد',
        status: 'completed'
      },
      {
        id: '2',
        referenceNumber: 'WS20241224002',
        amount: '750.00',
        currency: 'USD',
        receiverName: 'فاطمة علي',
        status: 'pending'
      }
    ],
    total: 2
  });
});

const PORT = 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('🚀 ================================');
  console.log('🚀 WS TRANSFIR API SERVER RUNNING');
  console.log('🚀 ================================');
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`📊 Health: http://localhost:${PORT}/api/health`);
  console.log('🔐 Admin: <EMAIL> / admin123');
  console.log('🚀 ================================');
  console.log('');
});
