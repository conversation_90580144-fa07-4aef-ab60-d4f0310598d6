@echo off

echo WS Transfir System Status Check
echo =================================
echo.

echo Checking Node.js processes...
tasklist /FI "IMAGENAME eq node.exe" /FO TABLE 2>nul | findstr node.exe
if errorlevel 1 (
    echo No Node.js processes running
) else (
    echo Node.js processes found
)

echo.
echo Checking ports...

netstat -an | findstr :3000 >nul 2>&1
if errorlevel 1 (
    echo Port 3000 API: NOT RUNNING
) else (
    echo Port 3000 API: RUNNING
)

netstat -an | findstr :3100 >nul 2>&1
if errorlevel 1 (
    echo Port 3100 Frontend: NOT RUNNING
) else (
    echo Port 3100 Frontend: RUNNING
)

echo.
echo Testing services...

curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo API Server: NOT AVAILABLE
) else (
    echo API Server: AVAILABLE
)

curl -s http://localhost:3100 >nul 2>&1
if errorlevel 1 (
    echo Frontend: NOT AVAILABLE
) else (
    echo Frontend: AVAILABLE
)

echo.
echo System URLs:
echo ============
echo Frontend: http://localhost:3100
echo API: http://localhost:3000
echo Health: http://localhost:3000/api/health
echo.
echo Login credentials:
echo Admin: <EMAIL> / admin123
echo User: <EMAIL> / password123
echo.

pause
