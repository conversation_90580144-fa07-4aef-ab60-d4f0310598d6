{"version": 3, "file": "exclude.decorator.js", "sourceRoot": "", "sources": ["../../../src/decorators/exclude.decorator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAC;AAGpD;;;;;;GAMG;AACH,MAAM,UAAU,OAAO,CAAC,OAA4B;IAA5B,wBAAA,EAAA,YAA4B;IAClD;;;;;OAKG;IACH,OAAO,UAAU,MAAW,EAAE,YAA8B;QAC1D,sBAAsB,CAAC,kBAAkB,CAAC;YACxC,MAAM,EAAE,MAAM,YAAY,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW;YAChE,YAAY,EAAE,YAAsB;YACpC,OAAO,SAAA;SACR,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { defaultMetadataStorage } from '../storage';\nimport { ExcludeOptions } from '../interfaces';\n\n/**\n * Marks the given class or property as excluded. By default the property is excluded in both\n * constructorToPlain and plainToConstructor transformations. It can be limited to only one direction\n * via using the `toPlainOnly` or `toClassOnly` option.\n *\n * Can be applied to class definitions and properties.\n */\nexport function Exclude(options: ExcludeOptions = {}): PropertyDecorator & ClassDecorator {\n  /**\n   * NOTE: The `propertyName` property must be marked as optional because\n   * this decorator used both as a class and a property decorator and the\n   * Typescript compiler will freak out if we make it mandatory as a class\n   * decorator only receives one parameter.\n   */\n  return function (object: any, propertyName?: string | Symbol): void {\n    defaultMetadataStorage.addExcludeMetadata({\n      target: object instanceof Function ? object : object.constructor,\n      propertyName: propertyName as string,\n      options,\n    });\n  };\n}\n"]}