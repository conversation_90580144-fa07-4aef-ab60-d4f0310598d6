{"version": 3, "file": "PhoneNumber.test.js", "names": ["describe", "it", "phoneNumber", "PhoneNumber", "metadata", "setExt", "expect", "country", "to", "be", "undefined", "countryCallingCode", "should", "equal", "nationalNumber", "formatNational", "number", "ext", "format", "formatExtension", "extension", "isEqual", "isNonGeographic", "getPossibleCountries", "deep", "indexOf", "length"], "sources": ["../source/PhoneNumber.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport PhoneNumber from './PhoneNumber.js'\r\n\r\ndescribe('PhoneNumber', () => {\r\n\tit('should create a phone number via a public constructor', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('+78005553535', metadata)\r\n\t\tphoneNumber.setExt('1234')\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.countryCallingCode.should.equal('7')\r\n\t\tphoneNumber.nationalNumber.should.equal('8005553535')\r\n\t\tphoneNumber.formatNational().should.equal('8 (800) 555-35-35 ext. 1234')\r\n\t})\r\n\r\n\tit('should validate constructor arguments (public constructor)', () => {\r\n\t\texpect(() => new PhoneNumber()).to.throw('argument is required')\r\n\t\texpect(() => new PhoneNumber(undefined, metadata)).to.throw('argument is required')\r\n\t\texpect(() => new PhoneNumber('7', metadata)).to.throw('must consist of a \"+\"')\r\n\t\texpect(() => new PhoneNumber('+7', metadata)).to.throw('too short')\r\n\t\texpect(() => new PhoneNumber('+7800')).to.throw('`metadata` argument not passed')\r\n\t\texpect(() => new PhoneNumber(1234567890)).to.throw('must be a string')\r\n\t\texpect(() => new PhoneNumber('+1', 1234567890)).to.throw('must be a string')\r\n\t})\r\n\r\n\tit('should validate constructor arguments (private constructor)', () => {\r\n\t\texpect(() => new PhoneNumber(undefined, '800', metadata)).to.throw('First argument is required')\r\n\t\texpect(() => new PhoneNumber('7', undefined, metadata)).to.throw('`nationalNumber` argument is required')\r\n\t\texpect(() => new PhoneNumber('7', '8005553535')).to.throw('`metadata` argument not passed')\r\n\t})\r\n\r\n\tit('should accept country code argument', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('RU', '8005553535', metadata)\r\n\t\tphoneNumber.countryCallingCode.should.equal('7')\r\n\t\tphoneNumber.country.should.equal('RU')\r\n\t\tphoneNumber.number.should.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should format number with options', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('7', '8005553535', metadata)\r\n\t\tphoneNumber.ext = '123'\r\n\t\tphoneNumber.format('NATIONAL', {\r\n\t\t\tformatExtension: (number, extension) => `${number} доб. ${extension}`\r\n\t\t})\r\n\t\t.should.equal('8 (800) 555-35-35 доб. 123')\r\n\t})\r\n\r\n\tit('should compare phone numbers', () => {\r\n\t\tnew PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('RU', '8005553535', metadata)).should.equal(true)\r\n\t\tnew PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('7', '8005553535', metadata)).should.equal(true)\r\n\t\tnew PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('RU', '8005553536', metadata)).should.equal(false)\r\n\t})\r\n\r\n\tit('should tell if a number is non-geographic', () => {\r\n\t\tnew PhoneNumber('7', '8005553535', metadata).isNonGeographic().should.equal(false)\r\n\t\tnew PhoneNumber('870', '773111632', metadata).isNonGeographic().should.equal(true)\r\n\t})\r\n\r\n\tit('should allow setting extension', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '2133734253', metadata)\r\n\t\tphoneNumber.setExt('1234')\r\n\t\tphoneNumber.ext.should.equal('1234')\r\n\t\tphoneNumber.formatNational().should.equal('(************* ext. 1234')\r\n\t})\r\n\r\n\tit('should return possible countries', () => {\r\n      // \"599\": [\r\n      //    \"CW\", //  \"possible_lengths\": [7, 8]\r\n      //    \"BQ\" //  \"possible_lengths\": [7]\r\n      // ]\r\n\r\n\t\tlet phoneNumber = new PhoneNumber('599', '123456', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '1234567', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal(['CW', 'BQ'])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '12345678', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal(['CW'])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '123456789', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t})\r\n\r\n\tit('should return possible countries in case of ambiguity', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '2223334444', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().indexOf('US').should.equal(0)\r\n\t\tphoneNumber.getPossibleCountries().length.should.equal(25)\r\n\t})\r\n\r\n\t// it('should return empty possible countries when no national number has been input', () => {\r\n\t// \tconst phoneNumber = new PhoneNumber('1', '', metadata)\r\n\t// \texpect(phoneNumber.country).to.be.undefined\r\n\t// \tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t// })\r\n\r\n\tit('should return empty possible countries when not enough national number digits have been input', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '222', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t})\r\n\r\n\tit('should return possible countries in case of no ambiguity', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('US', '2133734253', metadata)\r\n\t\tphoneNumber.country.should.equal('US')\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal(['US'])\r\n\t})\r\n\r\n\tit('should return empty possible countries in case of an unknown calling code', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('777', '123', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t})\r\n\r\n\t// it('should validate phone number length', () => {\r\n\t// \tconst phoneNumber = new PhoneNumber('RU', '800', metadata)\r\n\t// \texpect(phoneNumber.validateLength()).to.equal('TOO_SHORT')\r\n\t//\r\n\t// \tconst phoneNumberValid = new PhoneNumber('RU', '8005553535', metadata)\r\n\t// \texpect(phoneNumberValid.validateLength()).to.be.undefined\r\n\t// })\r\n})"], "mappings": ";;AAAA;;AACA;;;;AAEAA,QAAQ,CAAC,aAAD,EAAgB,YAAM;EAC7BC,EAAE,CAAC,uDAAD,EAA0D,YAAM;IACjE,IAAMC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,cAAhB,EAAgCC,uBAAhC,CAApB;IACAF,WAAW,CAACG,MAAZ,CAAmB,MAAnB;IACAC,MAAM,CAACJ,WAAW,CAACK,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAR,WAAW,CAACS,kBAAZ,CAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAX,WAAW,CAACY,cAAZ,CAA2BF,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAX,WAAW,CAACa,cAAZ,GAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,6BAA1C;EACA,CAPC,CAAF;EASAZ,EAAE,CAAC,4DAAD,EAA+D,YAAM;IACtEK,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,EAAN;IAAA,CAAD,CAAN,CAAgCK,EAAhC,UAAyC,sBAAzC;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgBO,SAAhB,EAA2BN,uBAA3B,CAAN;IAAA,CAAD,CAAN,CAAmDI,EAAnD,UAA4D,sBAA5D;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgB,GAAhB,EAAqBC,uBAArB,CAAN;IAAA,CAAD,CAAN,CAA6CI,EAA7C,UAAsD,uBAAtD;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgB,IAAhB,EAAsBC,uBAAtB,CAAN;IAAA,CAAD,CAAN,CAA8CI,EAA9C,UAAuD,WAAvD;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgB,OAAhB,CAAN;IAAA,CAAD,CAAN,CAAuCK,EAAvC,UAAgD,gCAAhD;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgB,UAAhB,CAAN;IAAA,CAAD,CAAN,CAA0CK,EAA1C,UAAmD,kBAAnD;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgB,IAAhB,EAAsB,UAAtB,CAAN;IAAA,CAAD,CAAN,CAAgDK,EAAhD,UAAyD,kBAAzD;EACA,CARC,CAAF;EAUAP,EAAE,CAAC,6DAAD,EAAgE,YAAM;IACvEK,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgBO,SAAhB,EAA2B,KAA3B,EAAkCN,uBAAlC,CAAN;IAAA,CAAD,CAAN,CAA0DI,EAA1D,UAAmE,4BAAnE;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgB,GAAhB,EAAqBO,SAArB,EAAgCN,uBAAhC,CAAN;IAAA,CAAD,CAAN,CAAwDI,EAAxD,UAAiE,uCAAjE;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAJ,CAAgB,GAAhB,EAAqB,YAArB,CAAN;IAAA,CAAD,CAAN,CAAiDK,EAAjD,UAA0D,gCAA1D;EACA,CAJC,CAAF;EAMAP,EAAE,CAAC,qCAAD,EAAwC,YAAM;IAC/C,IAAMC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCC,uBAApC,CAApB;IACAF,WAAW,CAACS,kBAAZ,CAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAX,WAAW,CAACK,OAAZ,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;IACAX,WAAW,CAACc,MAAZ,CAAmBJ,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;EACA,CALC,CAAF;EAOAZ,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C,IAAMC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCC,uBAAnC,CAApB;IACAF,WAAW,CAACe,GAAZ,GAAkB,KAAlB;IACAf,WAAW,CAACgB,MAAZ,CAAmB,UAAnB,EAA+B;MAC9BC,eAAe,EAAE,yBAACH,MAAD,EAASI,SAAT;QAAA,iBAA0BJ,MAA1B,kCAAyCI,SAAzC;MAAA;IADa,CAA/B,EAGCR,MAHD,CAGQC,KAHR,CAGc,4BAHd;EAIA,CAPC,CAAF;EASAZ,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxC,IAAIE,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCC,uBAApC,EAA8CiB,OAA9C,CAAsD,IAAIlB,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCC,uBAApC,CAAtD,EAAqGQ,MAArG,CAA4GC,KAA5G,CAAkH,IAAlH;IACA,IAAIV,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCC,uBAApC,EAA8CiB,OAA9C,CAAsD,IAAIlB,uBAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCC,uBAAnC,CAAtD,EAAoGQ,MAApG,CAA2GC,KAA3G,CAAiH,IAAjH;IACA,IAAIV,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCC,uBAApC,EAA8CiB,OAA9C,CAAsD,IAAIlB,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCC,uBAApC,CAAtD,EAAqGQ,MAArG,CAA4GC,KAA5G,CAAkH,KAAlH;EACA,CAJC,CAAF;EAMAZ,EAAE,CAAC,2CAAD,EAA8C,YAAM;IACrD,IAAIE,uBAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCC,uBAAnC,EAA6CkB,eAA7C,GAA+DV,MAA/D,CAAsEC,KAAtE,CAA4E,KAA5E;IACA,IAAIV,uBAAJ,CAAgB,KAAhB,EAAuB,WAAvB,EAAoCC,uBAApC,EAA8CkB,eAA9C,GAAgEV,MAAhE,CAAuEC,KAAvE,CAA6E,IAA7E;EACA,CAHC,CAAF;EAKAZ,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1C,IAAMC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCC,uBAAnC,CAApB;IACAF,WAAW,CAACG,MAAZ,CAAmB,MAAnB;IACAH,WAAW,CAACe,GAAZ,CAAgBL,MAAhB,CAAuBC,KAAvB,CAA6B,MAA7B;IACAX,WAAW,CAACa,cAAZ,GAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,0BAA1C;EACA,CALC,CAAF;EAOAZ,EAAE,CAAC,kCAAD,EAAqC,YAAM;IACxC;IACA;IACA;IACA;IAEJ,IAAIC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,KAAhB,EAAuB,QAAvB,EAAiCC,uBAAjC,CAAlB;IACAE,MAAM,CAACJ,WAAW,CAACK,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAR,WAAW,CAACqB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,EAArD;IAEAX,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,KAAhB,EAAuB,SAAvB,EAAkCC,uBAAlC,CAAd;IACAE,MAAM,CAACJ,WAAW,CAACK,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAR,WAAW,CAACqB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,CAAC,IAAD,EAAO,IAAP,CAArD;IAEAX,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,KAAhB,EAAuB,UAAvB,EAAmCC,uBAAnC,CAAd;IACAE,MAAM,CAACJ,WAAW,CAACK,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAR,WAAW,CAACqB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,CAAC,IAAD,CAArD;IAEAX,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,KAAhB,EAAuB,WAAvB,EAAoCC,uBAApC,CAAd;IACAE,MAAM,CAACJ,WAAW,CAACK,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAR,WAAW,CAACqB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,EAArD;EACA,CArBC,CAAF;EAuBAZ,EAAE,CAAC,uDAAD,EAA0D,YAAM;IACjE,IAAMC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCC,uBAAnC,CAApB;IACAE,MAAM,CAACJ,WAAW,CAACK,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAR,WAAW,CAACqB,oBAAZ,GAAmCE,OAAnC,CAA2C,IAA3C,EAAiDb,MAAjD,CAAwDC,KAAxD,CAA8D,CAA9D;IACAX,WAAW,CAACqB,oBAAZ,GAAmCG,MAAnC,CAA0Cd,MAA1C,CAAiDC,KAAjD,CAAuD,EAAvD;EACA,CALC,CAAF,CAnF6B,CA0F7B;EACA;EACA;EACA;EACA;;EAEAZ,EAAE,CAAC,+FAAD,EAAkG,YAAM;IACzG,IAAMC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,GAAhB,EAAqB,KAArB,EAA4BC,uBAA5B,CAApB;IACAE,MAAM,CAACJ,WAAW,CAACK,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAR,WAAW,CAACqB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,EAArD;EACA,CAJC,CAAF;EAMAZ,EAAE,CAAC,0DAAD,EAA6D,YAAM;IACpE,IAAMC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCC,uBAApC,CAApB;IACAF,WAAW,CAACK,OAAZ,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;IACAX,WAAW,CAACqB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,CAAC,IAAD,CAArD;EACA,CAJC,CAAF;EAMAZ,EAAE,CAAC,2EAAD,EAA8E,YAAM;IACrF,IAAMC,WAAW,GAAG,IAAIC,uBAAJ,CAAgB,KAAhB,EAAuB,KAAvB,EAA8BC,uBAA9B,CAApB;IACAE,MAAM,CAACJ,WAAW,CAACK,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAR,WAAW,CAACqB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,EAArD;EACA,CAJC,CAAF,CA5G6B,CAkH7B;EACA;EACA;EACA;EACA;EACA;EACA;AACA,CAzHO,CAAR"}