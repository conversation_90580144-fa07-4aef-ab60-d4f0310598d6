@echo off
chcp 65001 >nul
title WS Transfir - Online System Launcher

:: WS Transfir Online System Launcher
:: مشغل نظام WS Transfir الأونلاين المتكامل

color 0B
cls

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🌐 WS TRANSFIR - ONLINE SYSTEM LAUNCHER 🌐             ██
echo ██                                                            ██
echo ██    نظام التحويلات المالية الأونلاين المتكامل              ██
echo ██    Advanced Online Money Transfer System                   ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 📅 التاريخ: %DATE%
echo ⏰ الوقت: %TIME%
echo 🌍 الوضع: Online Production Ready
echo 📡 الوصول: External Access Enabled
echo.

:: Set startup timestamp
set START_TIME=%TIME%
echo 🕐 بدء التشغيل الأونلاين: %START_TIME%
echo.

:: Phase 1: System Validation
echo ═══════════════════════════════════════════════════════════════
echo 📋 المرحلة 1: فحص النظام والمتطلبات الأونلاين
echo ═══════════════════════════════════════════════════════════════
echo.

:: Check Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL ERROR: Node.js غير مثبت
    echo.
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    echo 🔧 الإصدار المطلوب: 18.0.0 أو أحدث
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js متاح - الإصدار: %NODE_VERSION%

:: Check npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ CRITICAL ERROR: npm غير متاح
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm متاح - الإصدار: %NPM_VERSION%

:: Check network connectivity
echo 🌐 فحص الاتصال بالإنترنت...
ping -n 1 8.8.8.8 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: لا يوجد اتصال بالإنترنت
    echo 📡 النظام سيعمل في الوضع المحلي
) else (
    echo ✅ الاتصال بالإنترنت متاح
)

:: Check required files
echo 🔍 فحص الملفات المطلوبة...
if not exist "online-system-server.js" (
    echo ❌ CRITICAL ERROR: ملف الخادم الأونلاين غير موجود
    pause
    exit /b 1
)
echo ✅ ملف الخادم الأونلاين موجود

if not exist "package.json" (
    echo ❌ CRITICAL ERROR: ملف package.json غير موجود
    pause
    exit /b 1
)
echo ✅ ملف package.json موجود

echo.
echo ✅ جميع المتطلبات متوفرة - النظام جاهز للتشغيل الأونلاين
echo.

:: Phase 2: Dependencies Check and Install
echo ═══════════════════════════════════════════════════════════════
echo 📦 المرحلة 2: فحص وتثبيت المكتبات المطلوبة
echo ═══════════════════════════════════════════════════════════════
echo.

:: Check if node_modules exists
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق بضع دقائق...
    npm install express cors helmet compression express-rate-limit morgan --silent
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🔧 جاري المحاولة مرة أخرى...
        npm install --silent
        if errorlevel 1 (
            echo ❌ فشل في تثبيت المكتبات نهائياً
            pause
            exit /b 1
        )
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات مثبتة مسبقاً
)

:: Verify critical dependencies
echo 🔍 فحص المكتبات الحرجة...
node -e "require('express'); require('cors'); require('helmet'); console.log('✅ المكتبات الحرجة متاحة');" 2>nul
if errorlevel 1 (
    echo ❌ بعض المكتبات الحرجة مفقودة
    echo 📦 إعادة تثبيت المكتبات...
    npm install express cors helmet compression express-rate-limit morgan --force --silent
)

echo.

:: Phase 3: Environment Setup
echo ═══════════════════════════════════════════════════════════════
echo ⚙️ المرحلة 3: إعداد البيئة الأونلاين
echo ═══════════════════════════════════════════════════════════════
echo.

:: Set environment variables for online operation
set NODE_ENV=production
set PORT=3000
set FRONTEND_PORT=3100
set HOST=0.0.0.0

echo 🔧 إعداد متغيرات البيئة...
echo    NODE_ENV: %NODE_ENV%
echo    PORT: %PORT%
echo    FRONTEND_PORT: %FRONTEND_PORT%
echo    HOST: %HOST%

:: Create .env file for online operation
echo 🔧 إنشاء ملف البيئة الأونلاين...
(
    echo # WS Transfir Online Environment Configuration
    echo # Generated on %DATE% at %TIME%
    echo.
    echo NODE_ENV=production
    echo PORT=3000
    echo FRONTEND_PORT=3100
    echo HOST=0.0.0.0
    echo.
    echo # Online Security Settings
    echo JWT_SECRET=ws-transfir-online-jwt-secret-2024
    echo ENCRYPTION_KEY=ws-transfir-online-encryption-key-32
    echo SESSION_SECRET=ws-transfir-online-session-secret
    echo.
    echo # Online Features
    echo ENABLE_CORS=true
    echo ENABLE_COMPRESSION=true
    echo ENABLE_RATE_LIMITING=true
    echo ENABLE_LOGGING=true
    echo ENABLE_MONITORING=true
    echo.
    echo # External Access
    echo ALLOW_EXTERNAL_ACCESS=true
    echo ENABLE_HTTPS_REDIRECT=false
    echo.
    echo # Performance Settings
    echo MAX_REQUEST_SIZE=10mb
    echo COMPRESSION_LEVEL=6
    echo RATE_LIMIT_WINDOW=900000
    echo RATE_LIMIT_MAX=100
) > .env.online

echo ✅ تم إنشاء ملف البيئة الأونلاين
echo.

:: Phase 4: Port Availability Check
echo ═══════════════════════════════════════════════════════════════
echo 🌐 المرحلة 4: فحص توفر المنافذ
echo ═══════════════════════════════════════════════════════════════
echo.

:: Check if ports are available
echo 🔍 فحص المنفذ 3000 (API Server)...
netstat -an | findstr :3000 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ المنفذ 3000 مستخدم - سيتم إيقاف العملية الحالية
    taskkill /F /IM node.exe >nul 2>&1
    timeout /t 2 /nobreak >nul
)

echo 🔍 فحص المنفذ 3100 (Frontend)...
netstat -an | findstr :3100 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ المنفذ 3100 مستخدم - سيتم إيقاف العملية الحالية
    taskkill /F /IM node.exe >nul 2>&1
    timeout /t 2 /nobreak >nul
)

echo ✅ المنافذ متاحة للاستخدام
echo.

:: Phase 5: Firewall Configuration (Optional)
echo ═══════════════════════════════════════════════════════════════
echo 🛡️ المرحلة 5: إعداد الحماية (اختياري)
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🔥 فحص إعدادات Windows Firewall...
echo    ملاحظة: قد تحتاج للسماح للتطبيق عبر الجدار الناري
echo    إذا ظهرت رسالة Windows Security، اختر "Allow access"
echo.

:: Phase 6: Online System Launch
echo ═══════════════════════════════════════════════════════════════
echo 🚀 المرحلة 6: تشغيل النظام الأونلاين
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🌐 بدء تشغيل النظام الأونلاين...
echo.

:: Get local IP address
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%j in ("%%i") do set LOCAL_IP=%%j
)

:: Start the online system server
echo 🎬 تشغيل الخادم الأونلاين...
start "WS Transfir Online System" cmd /k "node online-system-server.js"

:: Wait for server to start
echo ⏳ انتظار تشغيل الخادم...
timeout /t 8 /nobreak >nul

:: Test server availability
echo 🧪 اختبار توفر الخادم...
curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️ الخادم قد يحتاج وقت إضافي للتشغيل
) else (
    echo ✅ الخادم يعمل بنجاح
)

:: Calculate startup time
set END_TIME=%TIME%
echo.

:: Phase 7: Access Information
echo ═══════════════════════════════════════════════════════════════
echo 🌍 معلومات الوصول الأونلاين
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🌐 روابط الوصول المحلي:
echo ========================
echo 📱 التطبيق:              http://localhost:3000
echo 📊 فحص الصحة:           http://localhost:3000/api/health
echo 📈 حالة النظام:          http://localhost:3000/api/status
echo.

if defined LOCAL_IP (
    echo 🌍 روابط الوصول الخارجي:
    echo =========================
    echo 📱 التطبيق:              http://%LOCAL_IP%:3000
    echo 📊 فحص الصحة:           http://%LOCAL_IP%:3000/api/health
    echo 📈 حالة النظام:          http://%LOCAL_IP%:3000/api/status
    echo.
)

echo 🔐 بيانات الدخول التجريبية:
echo ============================
echo 👨‍💼 مدير النظام:        <EMAIL> / admin123
echo 👤 مستخدم عادي:        <EMAIL> / password123
echo.

echo 📋 نقاط API المتاحة:
echo ====================
echo 🔐 POST /api/auth/login     - تسجيل الدخول
echo 📝 POST /api/auth/register  - إنشاء حساب
echo 👤 GET  /api/profile/me     - الملف الشخصي
echo 💸 GET  /api/transfers      - قائمة التحويلات
echo 📊 GET  /api/transfers/stats - إحصائيات التحويلات
echo 🏥 GET  /api/health         - فحص الصحة
echo 📈 GET  /api/status         - حالة النظام
echo.

:: Open browser automatically
echo 🌐 فتح المتصفح تلقائياً...
start "" "http://localhost:3000/api/health"
timeout /t 2 /nobreak >nul

:: Final status
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    ✅ WS TRANSFIR ONLINE SYSTEM IS NOW RUNNING!           ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🕐 وقت البدء: %START_TIME%
echo 🕐 وقت الانتهاء: %END_TIME%
echo.
echo 🎯 حالة النظام:
echo ===============
echo ✅ الخادم الأونلاين: يعمل
echo ✅ API Endpoints: متاحة
echo ✅ الأمان: مفعل
echo ✅ المراقبة: مفعلة
echo ✅ الضغط: مفعل
echo ✅ Rate Limiting: مفعل
echo ✅ CORS: مفعل
echo ✅ الوصول الخارجي: مفعل
echo.
echo 💡 نصائح الاستخدام:
echo ==================
echo 🔹 النظام يعمل في وضع الإنتاج
echo 🔹 يمكن الوصول إليه من أجهزة أخرى في الشبكة
echo 🔹 جميع الميزات الأمنية مفعلة
echo 🔹 المراقبة والتسجيل مفعلان
echo 🔹 اضغط Ctrl+C في نافذة الخادم للإيقاف
echo.
echo 📞 الدعم الفني:
echo ================
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📱 الهاتف: +966 11 123 4567
echo 🌐 الموقع: https://wstransfir.com
echo.
echo 🎉 النظام الأونلاين جاهز للاستخدام!
echo.

pause
