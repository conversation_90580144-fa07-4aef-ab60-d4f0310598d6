#!/bin/bash

# WS Transfir System Stop Script
# نص إيقاف نظام WS Transfir

echo "🛑 إيقاف نظام WS Transfir"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if Docker Compose is available
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    elif docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        print_error "Docker Compose غير متاح"
        exit 1
    fi
}

# Stop services gracefully
stop_services() {
    print_header "🛑 إيقاف الخدمات..."
    
    print_status "إيقاف جميع الخدمات..."
    $COMPOSE_CMD down
    
    if [ $? -eq 0 ]; then
        print_status "تم إيقاف جميع الخدمات بنجاح"
    else
        print_error "حدث خطأ أثناء إيقاف الخدمات"
    fi
}

# Stop and remove containers, networks, and volumes
stop_and_clean() {
    print_header "🧹 إيقاف وتنظيف النظام..."
    
    print_status "إيقاف وإزالة الحاويات والشبكات..."
    $COMPOSE_CMD down --remove-orphans
    
    if [ "$1" = "--volumes" ] || [ "$1" = "-v" ]; then
        print_warning "إزالة البيانات المخزنة (volumes)..."
        $COMPOSE_CMD down --volumes --remove-orphans
        print_warning "تم حذف جميع البيانات المخزنة!"
    fi
    
    if [ "$1" = "--all" ] || [ "$1" = "-a" ]; then
        print_warning "إزالة الصور المبنية محلياً..."
        $COMPOSE_CMD down --rmi local --volumes --remove-orphans
        print_warning "تم حذف الصور والبيانات!"
    fi
}

# Show remaining containers
show_remaining() {
    print_header "📊 الحاويات المتبقية:"
    
    remaining=$(docker ps -a --filter "name=ws-" --format "table {{.Names}}\t{{.Status}}")
    
    if [ -n "$remaining" ]; then
        echo "$remaining"
        echo ""
        print_warning "يوجد حاويات متبقية. يمكنك إزالتها يدوياً إذا لزم الأمر."
    else
        print_status "لا توجد حاويات متبقية"
    fi
}

# Clean up orphaned containers
cleanup_orphans() {
    print_header "🧹 تنظيف الحاويات المهجورة..."
    
    orphans=$(docker ps -a --filter "name=ws-" -q)
    
    if [ -n "$orphans" ]; then
        print_status "إزالة الحاويات المهجورة..."
        docker rm -f $orphans
        print_status "تم تنظيف الحاويات المهجورة"
    else
        print_status "لا توجد حاويات مهجورة"
    fi
}

# Clean up unused networks
cleanup_networks() {
    print_header "🌐 تنظيف الشبكات غير المستخدمة..."
    
    networks=$(docker network ls --filter "name=ws-" -q)
    
    if [ -n "$networks" ]; then
        print_status "إزالة الشبكات غير المستخدمة..."
        docker network rm $networks 2>/dev/null || true
        print_status "تم تنظيف الشبكات"
    else
        print_status "لا توجد شبكات للتنظيف"
    fi
}

# Show help
show_help() {
    echo "استخدام: $0 [OPTIONS]"
    echo ""
    echo "الخيارات:"
    echo "  (بدون خيارات)    إيقاف الخدمات فقط"
    echo "  -v, --volumes     إيقاف الخدمات وحذف البيانات المخزنة"
    echo "  -a, --all         إيقاف الخدمات وحذف البيانات والصور"
    echo "  -c, --cleanup     تنظيف الحاويات والشبكات المهجورة"
    echo "  -h, --help        عرض هذه المساعدة"
    echo ""
    echo "أمثلة:"
    echo "  $0                إيقاف عادي"
    echo "  $0 -v             إيقاف مع حذف البيانات"
    echo "  $0 -a             إيقاف مع حذف كل شيء"
    echo "  $0 -c             تنظيف فقط"
}

# Main execution
main() {
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--cleanup)
            print_header "🧹 تنظيف النظام"
            check_docker_compose
            cleanup_orphans
            cleanup_networks
            ;;
        -v|--volumes)
            print_header "🛑 إيقاف النظام مع حذف البيانات"
            check_docker_compose
            stop_and_clean --volumes
            cleanup_orphans
            cleanup_networks
            ;;
        -a|--all)
            print_header "🛑 إيقاف النظام مع حذف كل شيء"
            check_docker_compose
            stop_and_clean --all
            cleanup_orphans
            cleanup_networks
            ;;
        "")
            print_header "🛑 إيقاف النظام"
            check_docker_compose
            stop_services
            ;;
        *)
            print_error "خيار غير معروف: $1"
            show_help
            exit 1
            ;;
    esac
    
    echo ""
    show_remaining
    echo ""
    print_header "✅ تم إيقاف النظام"
}

# Run main function
main "$@"
