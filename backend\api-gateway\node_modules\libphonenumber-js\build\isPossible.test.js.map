{"version": 3, "file": "isPossible.test.js", "names": ["isPossibleNumber", "v2", "parameters", "length", "push", "undefined", "parsePhoneNumber", "extract", "metadata", "_isPossibleNumber", "apply", "describe", "it", "should", "equal", "phone", "country", "countryCallingCode", "nationalNumber", "expect", "to"], "sources": ["../source/isPossible.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport _isPossibleNumber from './isPossible.js'\r\nimport parsePhoneNumber from './parsePhoneNumber.js'\r\n\r\nfunction isPossibleNumber(...parameters) {\r\n\tlet v2\r\n\tif (parameters.length < 1) {\r\n\t\t// `input` parameter.\r\n\t\tparameters.push(undefined)\r\n\t} else {\r\n\t\t// Convert string `input` to a `PhoneNumber` instance.\r\n\t\tif (typeof parameters[0] === 'string') {\r\n\t\t\tv2 = true\r\n\t\t\tparameters[0] = parsePhoneNumber(parameters[0], {\r\n\t\t\t\t...parameters[1],\r\n\t\t\t\textract: false\r\n\t\t\t}, metadata)\r\n\t\t}\r\n\t}\r\n\tif (parameters.length < 2) {\r\n\t\t// `options` parameter.\r\n\t\tparameters.push(undefined)\r\n\t}\r\n\t// Set `v2` flag.\r\n\tparameters[1] = {\r\n\t\tv2,\r\n\t\t...parameters[1]\r\n\t}\r\n\t// Add `metadata` parameter.\r\n\tparameters.push(metadata)\r\n\t// Call the function.\r\n\treturn _isPossibleNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isPossible', () => {\r\n\tit('should work', function()\r\n\t{\r\n\t\tisPossibleNumber('+79992223344').should.equal(true)\r\n\r\n\t\tisPossibleNumber({ phone: '1112223344', country: 'RU' }).should.equal(true)\r\n\t\tisPossibleNumber({ phone: '111222334', country: 'RU' }).should.equal(false)\r\n\t\tisPossibleNumber({ phone: '11122233445', country: 'RU' }).should.equal(false)\r\n\r\n\t\tisPossibleNumber({ phone: '1112223344', countryCallingCode: 7 }).should.equal(true)\r\n\t})\r\n\r\n\tit('should work v2', () => {\r\n\t\tisPossibleNumber({ nationalNumber: '111222334', countryCallingCode: 7 }, { v2: true }).should.equal(false)\r\n\t\tisPossibleNumber({ nationalNumber: '1112223344', countryCallingCode: 7 }, { v2: true }).should.equal(true)\r\n\t\tisPossibleNumber({ nationalNumber: '11122233445', countryCallingCode: 7 }, { v2: true }).should.equal(false)\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// Invalid `PhoneNumber` argument.\r\n\t\texpect(() => isPossibleNumber({}, { v2: true })).to.throw('Invalid phone number object passed')\r\n\r\n\t\t// Empty input is passed.\r\n\t\t// This is just to support `isValidNumber({})`\r\n\t\t// for cases when `parseNumber()` returns `{}`.\r\n\t\tisPossibleNumber({}).should.equal(false)\r\n\t\texpect(() => isPossibleNumber({ phone: '1112223344' })).to.throw('Invalid phone number object passed')\r\n\r\n\t\t// Incorrect country.\r\n\t\texpect(() => isPossibleNumber({ phone: '1112223344', country: 'XX' })).to.throw('Unknown country')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;AACA;;;;;;;;;;AAEA,SAASA,gBAAT,GAAyC;EACxC,IAAIC,EAAJ;;EADwC,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAExC,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAxB,EAA2B;IAC1B;IACAD,UAAU,CAACE,IAAX,CAAgBC,SAAhB;EACA,CAHD,MAGO;IACN;IACA,IAAI,OAAOH,UAAU,CAAC,CAAD,CAAjB,KAAyB,QAA7B,EAAuC;MACtCD,EAAE,GAAG,IAAL;MACAC,UAAU,CAAC,CAAD,CAAV,GAAgB,IAAAI,4BAAA,EAAiBJ,UAAU,CAAC,CAAD,CAA3B,kCACZA,UAAU,CAAC,CAAD,CADE;QAEfK,OAAO,EAAE;MAFM,IAGbC,uBAHa,CAAhB;IAIA;EACD;;EACD,IAAIN,UAAU,CAACC,MAAX,GAAoB,CAAxB,EAA2B;IAC1B;IACAD,UAAU,CAACE,IAAX,CAAgBC,SAAhB;EACA,CAlBuC,CAmBxC;;;EACAH,UAAU,CAAC,CAAD,CAAV;IACCD,EAAE,EAAFA;EADD,GAEIC,UAAU,CAAC,CAAD,CAFd,EApBwC,CAwBxC;;EACAA,UAAU,CAACE,IAAX,CAAgBI,uBAAhB,EAzBwC,CA0BxC;;EACA,OAAOC,sBAAA,CAAkBC,KAAlB,CAAwB,IAAxB,EAA8BR,UAA9B,CAAP;AACA;;AAEDS,QAAQ,CAAC,YAAD,EAAe,YAAM;EAC5BC,EAAE,CAAC,aAAD,EAAgB,YAClB;IACCZ,gBAAgB,CAAC,cAAD,CAAhB,CAAiCa,MAAjC,CAAwCC,KAAxC,CAA8C,IAA9C;IAEAd,gBAAgB,CAAC;MAAEe,KAAK,EAAE,YAAT;MAAuBC,OAAO,EAAE;IAAhC,CAAD,CAAhB,CAAyDH,MAAzD,CAAgEC,KAAhE,CAAsE,IAAtE;IACAd,gBAAgB,CAAC;MAAEe,KAAK,EAAE,WAAT;MAAsBC,OAAO,EAAE;IAA/B,CAAD,CAAhB,CAAwDH,MAAxD,CAA+DC,KAA/D,CAAqE,KAArE;IACAd,gBAAgB,CAAC;MAAEe,KAAK,EAAE,aAAT;MAAwBC,OAAO,EAAE;IAAjC,CAAD,CAAhB,CAA0DH,MAA1D,CAAiEC,KAAjE,CAAuE,KAAvE;IAEAd,gBAAgB,CAAC;MAAEe,KAAK,EAAE,YAAT;MAAuBE,kBAAkB,EAAE;IAA3C,CAAD,CAAhB,CAAiEJ,MAAjE,CAAwEC,KAAxE,CAA8E,IAA9E;EACA,CATC,CAAF;EAWAF,EAAE,CAAC,gBAAD,EAAmB,YAAM;IAC1BZ,gBAAgB,CAAC;MAAEkB,cAAc,EAAE,WAAlB;MAA+BD,kBAAkB,EAAE;IAAnD,CAAD,EAAyD;MAAEhB,EAAE,EAAE;IAAN,CAAzD,CAAhB,CAAuFY,MAAvF,CAA8FC,KAA9F,CAAoG,KAApG;IACAd,gBAAgB,CAAC;MAAEkB,cAAc,EAAE,YAAlB;MAAgCD,kBAAkB,EAAE;IAApD,CAAD,EAA0D;MAAEhB,EAAE,EAAE;IAAN,CAA1D,CAAhB,CAAwFY,MAAxF,CAA+FC,KAA/F,CAAqG,IAArG;IACAd,gBAAgB,CAAC;MAAEkB,cAAc,EAAE,aAAlB;MAAiCD,kBAAkB,EAAE;IAArD,CAAD,EAA2D;MAAEhB,EAAE,EAAE;IAAN,CAA3D,CAAhB,CAAyFY,MAAzF,CAAgGC,KAAhG,CAAsG,KAAtG;EACA,CAJC,CAAF;EAMAF,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACAO,MAAM,CAAC;MAAA,OAAMnB,gBAAgB,CAAC,EAAD,EAAK;QAAEC,EAAE,EAAE;MAAN,CAAL,CAAtB;IAAA,CAAD,CAAN,CAAiDmB,EAAjD,UAA0D,oCAA1D,EAFqC,CAIrC;IACA;IACA;;IACApB,gBAAgB,CAAC,EAAD,CAAhB,CAAqBa,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAK,MAAM,CAAC;MAAA,OAAMnB,gBAAgB,CAAC;QAAEe,KAAK,EAAE;MAAT,CAAD,CAAtB;IAAA,CAAD,CAAN,CAAwDK,EAAxD,UAAiE,oCAAjE,EARqC,CAUrC;;IACAD,MAAM,CAAC;MAAA,OAAMnB,gBAAgB,CAAC;QAAEe,KAAK,EAAE,YAAT;QAAuBC,OAAO,EAAE;MAAhC,CAAD,CAAtB;IAAA,CAAD,CAAN,CAAuEI,EAAvE,UAAgF,iBAAhF;EACA,CAZC,CAAF;AAaA,CA/BO,CAAR"}