import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  Length,
  Matches,
} from 'class-validator';

export class ReceiveTransferDto {
  @ApiProperty({
    description: 'رمز التتبع أو رقم المرجع',
    example: 'WS123456789',
  })
  @IsString({ message: 'رمز التتبع يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رمز التتبع مطلوب' })
  @Length(8, 20, { message: 'رمز التتبع يجب أن يكون بين 8 و 20 حرف' })
  trackingCode: string;

  @ApiProperty({
    description: 'رمز التحقق (OTP)',
    example: '123456',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رمز التحقق يجب أن يكون نص' })
  @Length(6, 6, { message: 'رمز التحقق يجب أن يكون 6 أرقام' })
  @Matches(/^\d{6}$/, { message: 'رمز التحقق يجب أن يكون أرقام فقط' })
  verificationCode?: string;

  @ApiProperty({
    description: 'رقم الهوية الوطنية للمستلم',
    example: '1234567890',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الهوية يجب أن يكون نص' })
  @Matches(/^\d{10}$/, { message: 'رقم الهوية يجب أن يكون 10 أرقام' })
  recipientNationalId?: string;

  @ApiProperty({
    description: 'رقم هاتف المستلم للتحقق',
    example: '+966501234567',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الهاتف يجب أن يكون نص' })
  recipientPhone?: string;

  @ApiProperty({
    description: 'موقع الاستلام (للاستلام النقدي)',
    example: 'فرع الرياض الرئيسي',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'موقع الاستلام يجب أن يكون نص' })
  pickupLocation?: string;

  @ApiProperty({
    description: 'ملاحظات إضافية',
    example: 'تم الاستلام بواسطة المستفيد شخصياً',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الملاحظات يجب أن تكون نص' })
  notes?: string;
}
