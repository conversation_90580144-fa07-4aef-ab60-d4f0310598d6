
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">72.72% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>120/165</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">66.66% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>62/93</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.5% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>33/40</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">72.67% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>117/161</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	</tr>

<tr>
	<td class="file high" data-value="logger.ts"><a href="logger.ts.html">logger.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="26" class="abs high">26/26</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="14" class="abs medium">11/14</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="14" class="abs high">13/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="26" class="abs high">26/26</td>
	</tr>

<tr>
	<td class="file high" data-value="options.ts"><a href="options.ts.html">options.ts</a></td>
	<td data-value="93.33" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 93%"></div><div class="cover-empty" style="width: 7%"></div></div>
	</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="15" class="abs high">14/15</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="14" class="abs high">13/14</td>
	</tr>

<tr>
	<td class="file medium" data-value="plugin.ts"><a href="plugin.ts.html">plugin.ts</a></td>
	<td data-value="68.96" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 68%"></div><div class="cover-empty" style="width: 32%"></div></div>
	</td>
	<td data-value="68.96" class="pct medium">68.96%</td>
	<td data-value="116" class="abs medium">80/116</td>
	<td data-value="65.38" class="pct medium">65.38%</td>
	<td data-value="78" class="abs medium">51/78</td>
	<td data-value="77.27" class="pct medium">77.27%</td>
	<td data-value="22" class="abs medium">17/22</td>
	<td data-value="68.42" class="pct medium">68.42%</td>
	<td data-value="114" class="abs medium">78/114</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2024-11-15T07:08:37.481Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    