{"version": 3, "file": "TSDocEmitter.test.js", "sourceRoot": "", "sources": ["../../../src/emitters/__tests__/TSDocEmitter.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EACL,WAAW,EAEX,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,kBAAkB,EACnB,MAAM,aAAa,CAAC;AAErB,SAAS,cAAc,CAAC,KAAa;IACnC,IAAM,WAAW,GAAgB,IAAI,WAAW,EAAE,CAAC;IACnD,IAAM,aAAa,GAAkB,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACpE,IAAM,MAAM,GAAW,aAAa,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;IAC9D,OAAO;QACL,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,CAAC;QAC3D,MAAM,EAAE,IAAI,GAAG,MAAM;KACtB,CAAC;AACJ,CAAC;AAED,IAAI,CAAC,0BAA0B,EAAE;IAC/B,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC,iEAMvD,CAAC,CAAC;IACD,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,qBAAqB,CAAC,iFASxD,CAAC,CAAC;IACD,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,qBAAqB,CAAC,iFAS1D,CAAC,CAAC;IACD,MAAM,CACJ,cAAc,CAAC,oBAIlB,CAAC,CACC,CAAC,qBAAqB,CAAC,iFASzB,CAAC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yBAAyB,EAAE;IAC9B,IAAM,KAAK,GAAW,0QAiBvB,CAAC;IAEA,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,mWA6BrD,CAAC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,iCAAiC,EAAE;IACtC,IAAM,aAAa,GAAuB,IAAI,kBAAkB,EAAE,CAAC;IACnE,IAAM,OAAO,GAAoB,IAAI,eAAe,CAAC;QACnD,aAAa,eAAA;QACb,IAAI,EAAE,KAAK;QACX,cAAc,EAAE,CAAC,IAAI,gBAAgB,CAAC,EAAE,aAAa,eAAA,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;KACtG,CAAC,CAAC;IACH,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,qBAAqB,CAAC,0CAAsC,CAAC,CAAC;AAC7F,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,8CAA8C,EAAE;IACnD,IAAM,aAAa,GAAuB,IAAI,kBAAkB,EAAE,CAAC;IACnE,IAAM,OAAO,GAA4B,IAAI,uBAAuB,CAAC;QACnE,aAAa,eAAA;QACb,WAAW,EAAE,YAAY;QACzB,gBAAgB,EAAE;YAChB,IAAI,kBAAkB,CAAC;gBACrB,aAAa,eAAA;gBACb,MAAM,EAAE,KAAK;gBACb,gBAAgB,EAAE,IAAI,mBAAmB,CAAC,EAAE,aAAa,eAAA,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;gBACnF,QAAQ,EAAE,IAAI,iBAAiB,CAAC,EAAE,aAAa,eAAA,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;aACtE,CAAC;SACH;KACF,CAAC,CAAC;IACH,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,qBAAqB,CAAC,gCAA8B,CAAC,CAAC;AACtF,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICEN<PERSON> in the project root for license information.\r\n\r\nimport {\r\n  TSDocParser,\r\n  type ParserContext,\r\n  DocHtmlStartTag,\r\n  DocHtmlAttribute,\r\n  DocDeclarationReference,\r\n  DocMemberReference,\r\n  DocMemberIdentifier,\r\n  DocMemberSelector,\r\n  TSDocConfiguration\r\n} from '../../index';\r\n\r\nfunction createSnapshot(input: string): {} {\r\n  const tsdocParser: TSDocParser = new TSDocParser();\r\n  const parserContext: ParserContext = tsdocParser.parseString(input);\r\n  const output: string = parserContext.docComment.emitAsTsdoc();\r\n  return {\r\n    errors: parserContext.log.messages.map((x) => x.toString()),\r\n    output: '\\n' + output\r\n  };\r\n}\r\n\r\ntest('01 Emit trivial comments', () => {\r\n  expect(createSnapshot(`/***/`)).toMatchInlineSnapshot(`\r\nObject {\r\n  \"errors\": Array [],\r\n  \"output\": \"\r\n\",\r\n}\r\n`);\r\n  expect(createSnapshot(`/**x*/`)).toMatchInlineSnapshot(`\r\nObject {\r\n  \"errors\": Array [],\r\n  \"output\": \"\r\n/**\r\n * x\r\n */\r\n\",\r\n}\r\n`);\r\n  expect(createSnapshot(`/** x */`)).toMatchInlineSnapshot(`\r\nObject {\r\n  \"errors\": Array [],\r\n  \"output\": \"\r\n/**\r\n * x\r\n */\r\n\",\r\n}\r\n`);\r\n  expect(\r\n    createSnapshot(`\r\n/**\r\n * x\r\n */\r\n`)\r\n  ).toMatchInlineSnapshot(`\r\nObject {\r\n  \"errors\": Array [],\r\n  \"output\": \"\r\n/**\r\n * x\r\n */\r\n\",\r\n}\r\n`);\r\n});\r\n\r\ntest('02 Emit a basic comment', () => {\r\n  const input: string = `\r\n/**\r\n * This is summary paragraph 1.\r\n *\r\n * This is summary paragraph 2. @remarks This is the remarks paragraph 1.\r\n *\r\n * This is the remarks paragraph 2.\r\n * @example\r\n * blah\r\n * @example\r\n * \\`\\`\\`ts\r\n * line1\r\n * line2\r\n * \\`\\`\\`\r\n *\r\n * @public @readonly\r\n */\r\n`;\r\n\r\n  expect(createSnapshot(input)).toMatchInlineSnapshot(`\r\nObject {\r\n  \"errors\": Array [],\r\n  \"output\": \"\r\n/**\r\n * This is summary paragraph 1.\r\n *\r\n * This is summary paragraph 2.\r\n *\r\n * @remarks\r\n *\r\n * This is the remarks paragraph 1.\r\n *\r\n * This is the remarks paragraph 2.\r\n *\r\n * @example\r\n *\r\n * blah\r\n *\r\n * @example\r\n * \\`\\`\\`ts\r\n * line1\r\n * line2\r\n * \\`\\`\\`\r\n *\r\n * @public @readonly\r\n */\r\n\",\r\n}\r\n`);\r\n});\r\n\r\ntest('03 TSDocEmitter.renderHtmlTag()', () => {\r\n  const configuration: TSDocConfiguration = new TSDocConfiguration();\r\n  const htmlTag: DocHtmlStartTag = new DocHtmlStartTag({\r\n    configuration,\r\n    name: 'img',\r\n    htmlAttributes: [new DocHtmlAttribute({ configuration, name: 'src', value: '\"http://example.com\"' })]\r\n  });\r\n  expect(htmlTag.emitAsHtml()).toMatchInlineSnapshot(`\"<img src=\\\\\"http://example.com\\\\\">\"`);\r\n});\r\n\r\ntest('04 TSDocEmitter.renderDeclarationReference()', () => {\r\n  const configuration: TSDocConfiguration = new TSDocConfiguration();\r\n  const htmlTag: DocDeclarationReference = new DocDeclarationReference({\r\n    configuration,\r\n    packageName: 'my-package',\r\n    memberReferences: [\r\n      new DocMemberReference({\r\n        configuration,\r\n        hasDot: false,\r\n        memberIdentifier: new DocMemberIdentifier({ configuration, identifier: 'MyClass' }),\r\n        selector: new DocMemberSelector({ configuration, selector: 'class' })\r\n      })\r\n    ]\r\n  });\r\n  expect(htmlTag.emitAsTsdoc()).toMatchInlineSnapshot(`\"my-package#(MyClass:class)\"`);\r\n});\r\n"]}