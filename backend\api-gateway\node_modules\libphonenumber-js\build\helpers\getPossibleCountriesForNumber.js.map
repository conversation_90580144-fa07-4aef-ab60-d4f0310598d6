{"version": 3, "file": "getPossibleCountriesForNumber.js", "names": ["getPossibleCountriesForNumber", "callingCode", "nationalNumber", "metadata", "_metadata", "<PERSON><PERSON><PERSON>", "possibleCountries", "getCountryCodesForCallingCode", "filter", "country", "couldNationalNumberBelongToCountry", "selectNumberingPlan", "numberingPlan", "possibleLengths", "indexOf", "length"], "sources": ["../../source/helpers/getPossibleCountriesForNumber.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\n\r\n/**\r\n * Returns a list of countries that the phone number could potentially belong to.\r\n * @param  {string} callingCode — Calling code.\r\n * @param  {string} nationalNumber — National (significant) number.\r\n * @param  {object} metadata — Metadata.\r\n * @return {string[]} A list of possible countries.\r\n */\r\nexport default function getPossibleCountriesForNumber(callingCode, nationalNumber, metadata) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\tlet possibleCountries = _metadata.getCountryCodesForCallingCode(callingCode)\r\n\tif (!possibleCountries) {\r\n\t\treturn []\r\n\t}\r\n\treturn possibleCountries.filter((country) => {\r\n\t\treturn couldNationalNumberBelongToCountry(nationalNumber, country, metadata)\r\n\t})\r\n}\r\n\r\nfunction couldNationalNumberBelongToCountry(nationalNumber, country, metadata) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\t_metadata.selectNumberingPlan(country)\r\n\tif (_metadata.numberingPlan.possibleLengths().indexOf(nationalNumber.length) >= 0) {\r\n\t\treturn true\r\n\t}\r\n\treturn false\r\n}"], "mappings": ";;;;;;;AAAA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,6BAAT,CAAuCC,WAAvC,EAAoDC,cAApD,EAAoEC,QAApE,EAA8E;EAC5F,IAAMC,SAAS,GAAG,IAAIC,qBAAJ,CAAaF,QAAb,CAAlB;;EACA,IAAIG,iBAAiB,GAAGF,SAAS,CAACG,6BAAV,CAAwCN,WAAxC,CAAxB;;EACA,IAAI,CAACK,iBAAL,EAAwB;IACvB,OAAO,EAAP;EACA;;EACD,OAAOA,iBAAiB,CAACE,MAAlB,CAAyB,UAACC,OAAD,EAAa;IAC5C,OAAOC,kCAAkC,CAACR,cAAD,EAAiBO,OAAjB,EAA0BN,QAA1B,CAAzC;EACA,CAFM,CAAP;AAGA;;AAED,SAASO,kCAAT,CAA4CR,cAA5C,EAA4DO,OAA5D,EAAqEN,QAArE,EAA+E;EAC9E,IAAMC,SAAS,GAAG,IAAIC,qBAAJ,CAAaF,QAAb,CAAlB;;EACAC,SAAS,CAACO,mBAAV,CAA8BF,OAA9B;;EACA,IAAIL,SAAS,CAACQ,aAAV,CAAwBC,eAAxB,GAA0CC,OAA1C,CAAkDZ,cAAc,CAACa,MAAjE,KAA4E,CAAhF,EAAmF;IAClF,OAAO,IAAP;EACA;;EACD,OAAO,KAAP;AACA"}