/**
 * WS Transfir System Status Display
 * عرض حالة نظام WS Transfir
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 حالة نظام WS Transfir');
console.log('========================');
console.log('');

// System Information
console.log('📊 معلومات النظام:');
console.log('==================');
console.log('📅 التاريخ:', new Date().toLocaleDateString('ar-SA'));
console.log('⏰ الوقت:', new Date().toLocaleTimeString('ar-SA'));
console.log('💻 النظام:', process.platform);
console.log('🔧 Node.js:', process.version);
console.log('📁 المجلد:', process.cwd());
console.log('');

// Check project structure
console.log('🏗️ هيكل المشروع:');
console.log('================');

const projectStructure = {
  'Backend Services': [
    'backend/api-gateway',
    'backend/auth-service',
    'backend/user-service',
    'backend/transfer-service',
    'backend/wallet-service',
    'backend/notification-service',
    'backend/payment-gateway-service',
    'backend/analytics-service',
    'backend/compliance-service'
  ],
  'Frontend Application': [
    'frontend/web-app'
  ],
  'Configuration Files': [
    'docker-compose.yml',
    'package.json',
    '.env'
  ],
  'Scripts': [
    'start-system.sh',
    'start-system.bat',
    'start-dev-mode.bat',
    'start-frontend-only.bat',
    'quick-start.bat',
    'stop-system.sh',
    'monitor-system.sh'
  ]
};

Object.keys(projectStructure).forEach(category => {
  console.log(`\n📦 ${category}:`);
  projectStructure[category].forEach(item => {
    const exists = fs.existsSync(item);
    const status = exists ? '✅' : '❌';
    console.log(`   ${status} ${item}`);
  });
});

// Check key files
console.log('\n📄 الملفات الرئيسية:');
console.log('===================');

const keyFiles = [
  { file: 'README.md', description: 'دليل المشروع' },
  { file: 'SYSTEM_STARTUP.md', description: 'دليل التشغيل' },
  { file: 'docker-compose.yml', description: 'إعداد Docker' },
  { file: '.gitignore', description: 'ملف Git ignore' },
  { file: 'package.json', description: 'إعدادات Node.js' }
];

keyFiles.forEach(({ file, description }) => {
  const exists = fs.existsSync(file);
  const status = exists ? '✅' : '❌';
  console.log(`   ${status} ${file} - ${description}`);
});

// Check frontend structure
console.log('\n🎨 هيكل الواجهة الأمامية:');
console.log('========================');

const frontendPath = 'frontend/web-app';
if (fs.existsSync(frontendPath)) {
  console.log('✅ مجلد الواجهة الأمامية موجود');
  
  const frontendFiles = [
    'package.json',
    'next.config.js',
    'tsconfig.json',
    'src/pages',
    'src/components',
    'src/styles'
  ];
  
  frontendFiles.forEach(file => {
    const fullPath = path.join(frontendPath, file);
    const exists = fs.existsSync(fullPath);
    const status = exists ? '✅' : '❌';
    console.log(`   ${status} ${file}`);
  });
  
  // Check pages
  const pagesPath = path.join(frontendPath, 'src/pages');
  if (fs.existsSync(pagesPath)) {
    console.log('\n📱 الصفحات المتاحة:');
    const pages = [
      'index.tsx',
      'login.tsx',
      'register.tsx',
      'profile.tsx',
      'forgot-password.tsx',
      'reset-password.tsx',
      'transfers/index.tsx',
      'transfers/[id].tsx'
    ];
    
    pages.forEach(page => {
      const pagePath = path.join(pagesPath, page);
      const exists = fs.existsSync(pagePath);
      const status = exists ? '✅' : '❌';
      const pageName = page.replace('.tsx', '').replace('/index', '');
      console.log(`   ${status} ${pageName}`);
    });
  }
} else {
  console.log('❌ مجلد الواجهة الأمامية غير موجود');
}

// Check backend services
console.log('\n🔧 خدمات Backend:');
console.log('=================');

const backendServices = [
  { name: 'API Gateway', path: 'backend/api-gateway' },
  { name: 'Auth Service', path: 'backend/auth-service' },
  { name: 'User Service', path: 'backend/user-service' },
  { name: 'Transfer Service', path: 'backend/transfer-service' },
  { name: 'Wallet Service', path: 'backend/wallet-service' },
  { name: 'Notification Service', path: 'backend/notification-service' },
  { name: 'Payment Gateway Service', path: 'backend/payment-gateway-service' },
  { name: 'Analytics Service', path: 'backend/analytics-service' },
  { name: 'Compliance Service', path: 'backend/compliance-service' }
];

backendServices.forEach(({ name, path: servicePath }) => {
  const exists = fs.existsSync(servicePath);
  const status = exists ? '✅' : '❌';
  console.log(`   ${status} ${name}`);
  
  if (exists) {
    const packageJsonPath = path.join(servicePath, 'package.json');
    const srcPath = path.join(servicePath, 'src');
    const dockerfilePath = path.join(servicePath, 'Dockerfile');
    
    const hasPackageJson = fs.existsSync(packageJsonPath);
    const hasSrc = fs.existsSync(srcPath);
    const hasDockerfile = fs.existsSync(dockerfilePath);
    
    console.log(`      📦 package.json: ${hasPackageJson ? '✅' : '❌'}`);
    console.log(`      📁 src: ${hasSrc ? '✅' : '❌'}`);
    console.log(`      🐳 Dockerfile: ${hasDockerfile ? '✅' : '❌'}`);
  }
});

// System readiness check
console.log('\n🎯 جاهزية النظام:');
console.log('================');

let readinessScore = 0;
let totalChecks = 0;

// Check Docker setup
totalChecks++;
if (fs.existsSync('docker-compose.yml')) {
  console.log('✅ إعداد Docker متاح');
  readinessScore++;
} else {
  console.log('❌ إعداد Docker غير متاح');
}

// Check frontend
totalChecks++;
if (fs.existsSync('frontend/web-app/package.json')) {
  console.log('✅ الواجهة الأمامية جاهزة');
  readinessScore++;
} else {
  console.log('❌ الواجهة الأمامية غير جاهزة');
}

// Check backend services
const backendReady = backendServices.filter(({ path: servicePath }) => 
  fs.existsSync(servicePath)
).length;

totalChecks++;
if (backendReady >= 7) {
  console.log(`✅ خدمات Backend جاهزة (${backendReady}/9)`);
  readinessScore++;
} else {
  console.log(`❌ خدمات Backend غير مكتملة (${backendReady}/9)`);
}

// Check startup scripts
totalChecks++;
const startupScripts = ['start-system.sh', 'start-system.bat', 'quick-start.bat'];
const scriptsReady = startupScripts.filter(script => fs.existsSync(script)).length;

if (scriptsReady >= 2) {
  console.log('✅ نصوص التشغيل متاحة');
  readinessScore++;
} else {
  console.log('❌ نصوص التشغيل غير متاحة');
}

// Calculate readiness percentage
const readinessPercentage = Math.round((readinessScore / totalChecks) * 100);

console.log('\n📈 نتيجة الجاهزية:');
console.log('==================');
console.log(`🎯 النتيجة: ${readinessScore}/${totalChecks} (${readinessPercentage}%)`);

if (readinessPercentage >= 90) {
  console.log('🟢 النظام جاهز للتشغيل الكامل');
} else if (readinessPercentage >= 70) {
  console.log('🟡 النظام جاهز للتشغيل الأساسي');
} else if (readinessPercentage >= 50) {
  console.log('🟠 النظام يحتاج إعداد إضافي');
} else {
  console.log('🔴 النظام غير جاهز للتشغيل');
}

// Recommendations
console.log('\n💡 التوصيات:');
console.log('=============');

if (readinessPercentage >= 90) {
  console.log('🚀 يمكنك تشغيل النظام الكامل باستخدام:');
  console.log('   - Docker: ./start-system.sh أو start-system.bat');
  console.log('   - التطوير: ./start-dev-mode.bat');
  console.log('   - الواجهة فقط: ./quick-start.bat');
} else {
  console.log('📝 خطوات مقترحة:');
  
  if (!fs.existsSync('frontend/web-app/package.json')) {
    console.log('   1. إعداد الواجهة الأمامية');
  }
  
  if (backendReady < 7) {
    console.log('   2. إكمال خدمات Backend');
  }
  
  if (!fs.existsSync('docker-compose.yml')) {
    console.log('   3. إعداد Docker');
  }
  
  console.log('   4. تشغيل الواجهة الأمامية فقط: ./quick-start.bat');
}

// Available commands
console.log('\n🔧 الأوامر المتاحة:');
console.log('==================');

const availableCommands = [
  { command: 'quick-start.bat', description: 'تشغيل سريع للواجهة الأمامية' },
  { command: 'start-frontend-only.bat', description: 'تشغيل الواجهة الأمامية مع إعداد كامل' },
  { command: 'start-dev-mode.bat', description: 'تشغيل وضع التطوير مع API وهمي' },
  { command: 'start-system.bat', description: 'تشغيل النظام الكامل (يتطلب Docker)' },
  { command: 'node system-status.js', description: 'عرض حالة النظام (هذا الأمر)' }
];

availableCommands.forEach(({ command, description }) => {
  const exists = fs.existsSync(command.split(' ')[0]);
  const status = exists ? '✅' : '❌';
  console.log(`   ${status} ${command} - ${description}`);
});

console.log('\n✨ انتهى فحص حالة النظام');
console.log('========================');
console.log('');
