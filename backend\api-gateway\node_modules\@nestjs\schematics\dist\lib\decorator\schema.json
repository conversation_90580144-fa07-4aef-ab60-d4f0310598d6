{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestDecorator", "title": "Nest Decorator Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the decorator.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the decorator?"}, "path": {"type": "string", "format": "path", "description": "The path to create the decorator."}, "language": {"type": "string", "description": "Nest decorator language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest decorator source root directory."}, "flat": {"type": "boolean", "default": true, "description": "Flag to indicate if a directory is created."}}, "required": ["name"]}