{"name": "@nestjs/throttler", "version": "5.2.0", "description": "A Rate-Limiting module for NestJS to work on Express, Fastify, Websockets, Socket.IO, and GraphQL, all rolled up into a simple package.", "author": "<PERSON> <<EMAIL>>", "contributors": [], "keywords": ["<PERSON><PERSON><PERSON>", "rate-limit", "throttle", "express", "fastify", "ws", "gql", "nest"], "publishConfig": {"access": "public"}, "private": false, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "devDependencies": {"@apollo/server": "4.10.4", "@changesets/cli": "2.27.5", "@commitlint/cli": "19.3.0", "@commitlint/config-angular": "19.3.0", "@nestjs/cli": "10.3.2", "@nestjs/common": "10.3.9", "@nestjs/core": "10.3.9", "@nestjs/graphql": "12.1.1", "@nestjs/platform-express": "10.3.9", "@nestjs/platform-fastify": "10.3.9", "@nestjs/platform-socket.io": "10.3.9", "@nestjs/platform-ws": "10.3.9", "@nestjs/schematics": "10.1.1", "@nestjs/testing": "10.3.9", "@nestjs/websockets": "10.3.9", "@semantic-release/git": "10.0.1", "@types/express": "4.17.21", "@types/express-serve-static-core": "4.19.3", "@types/jest": "29.5.12", "@types/node": "20.14.2", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "7.13.0", "@typescript-eslint/parser": "7.13.0", "apollo-server-fastify": "3.13.0", "conventional-changelog-cli": "5.0.0", "cz-conventional-changelog": "3.3.0", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "graphql": "16.8.2", "graphql-tools": "9.0.1", "husky": "9.0.11", "jest": "29.7.0", "lint-staged": "15.2.7", "nodemon": "3.1.3", "pactum": "^3.4.1", "pinst": "3.0.0", "prettier": "3.3.2", "reflect-metadata": "0.2.2", "rimraf": "5.0.7", "rxjs": "7.8.1", "socket.io": "4.7.5", "supertest": "7.0.0", "ts-jest": "29.1.4", "ts-loader": "9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.4.5", "ws": "8.17.0"}, "peerDependencies": {"@nestjs/common": "^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0", "@nestjs/core": "^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0", "reflect-metadata": "^0.1.13 || ^0.2.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}, "repository": {"type": "git", "url": "git+https://github.com/nestjs/throttler.git"}, "bugs": {"url": "https://github.com/nestjs/throttler/issues"}, "homepage": "https://github.com/nestjs/throttler#readme", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "preversion": "yarn run format && yarn run lint && yarn build", "build": "nest build", "commit": "git-cz", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:dev": "nodemon --watch '{src,test/app}/**/*.ts' --ignore '**/*.spec.ts' --exec 'ts-node' test/app/main.ts", "lint": "eslint \"{src,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --detect<PERSON><PERSON><PERSON>andles", "test:e2e:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --config test/jest-e2e.json --runInBand", "test:e2e:dev": "yarn test:e2e --watchAll", "_postinstall": "husky install", "release": "changeset publish"}}