export declare const TCP_DEFAULT_PORT = 3000;
export declare const TCP_DEFAULT_HOST = "localhost";
export declare const REDIS_DEFAULT_PORT = 6379;
export declare const REDIS_DEFAULT_HOST = "localhost";
export declare const NATS_DEFAULT_URL = "nats://localhost:4222";
export declare const MQTT_DEFAULT_URL = "mqtt://localhost:1883";
export declare const GRPC_DEFAULT_URL = "localhost:5000";
export declare const RQM_DEFAULT_URL = "amqp://localhost";
export declare const KAFKA_DEFAULT_BROKER = "localhost:9092";
export declare const CONNECT_EVENT = "connect";
export declare const DISCONNECT_EVENT = "disconnect";
export declare const CONNECT_FAILED_EVENT = "connectFailed";
export declare const MESSAGE_EVENT = "message";
export declare const DATA_EVENT = "data";
export declare const ERROR_EVENT = "error";
export declare const CLOSE_EVENT = "close";
export declare const SUBSCRIBE = "subscribe";
export declare const CANCEL_EVENT = "cancelled";
export declare const PATTERN_METADATA = "microservices:pattern";
export declare const PATTERN_EXTRAS_METADATA = "microservices:pattern_extras";
export declare const TRANSPORT_METADATA = "microservices:transport";
export declare const CLIENT_CONFIGURATION_METADATA = "microservices:client";
export declare const PATTERN_HANDLER_METADATA = "microservices:handler_type";
export declare const CLIENT_METADATA = "microservices:is_client_instance";
export declare const PARAM_ARGS_METADATA = "__routeArguments__";
export declare const REQUEST_PATTERN_METADATA = "microservices:request_pattern";
export declare const REPLY_PATTERN_METADATA = "microservices:reply_pattern";
export declare const RQM_DEFAULT_QUEUE = "default";
export declare const RQM_DEFAULT_PREFETCH_COUNT = 0;
export declare const RQM_DEFAULT_IS_GLOBAL_PREFETCH_COUNT = false;
export declare const RQM_DEFAULT_QUEUE_OPTIONS: {};
export declare const RQM_DEFAULT_NOACK = true;
export declare const RQM_DEFAULT_PERSISTENT = false;
export declare const RQM_DEFAULT_NO_ASSERT = false;
export declare const RQM_NO_EVENT_HANDLER: (text: TemplateStringsArray, pattern: string) => string;
export declare const RQM_NO_MESSAGE_HANDLER: (text: TemplateStringsArray, pattern: string) => string;
export declare const GRPC_DEFAULT_PROTO_LOADER = "@grpc/proto-loader";
export declare const NO_EVENT_HANDLER: (text: TemplateStringsArray, pattern: string) => string;
export declare const NO_MESSAGE_HANDLER = "There is no matching message handler defined in the remote service.";
export declare const DISCONNECTED_RMQ_MESSAGE = "Disconnected from RMQ. Trying to reconnect.";
export declare const KAFKA_DEFAULT_CLIENT = "nestjs-consumer";
export declare const KAFKA_DEFAULT_GROUP = "nestjs-group";
export declare const MQTT_SEPARATOR = "/";
export declare const MQTT_WILDCARD_SINGLE = "+";
export declare const MQTT_WILDCARD_ALL = "#";
export declare const ECONNREFUSED = "ECONNREFUSED";
export declare const CONN_ERR = "CONN_ERR";
export declare const EADDRINUSE = "EADDRINUSE";
export declare const CONNECTION_FAILED_MESSAGE = "Connection to transport failed. Trying to reconnect...";
