import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { UsersService } from '../services/users.service';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { User, UserStatus, UserRole } from '../entities/user.entity';
import { JwtAuthGuard } from '../../../common/guards/auth.guard';
import { RolesGuard } from '../../../common/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { GetUser } from '../../../common/decorators/get-user.decorator';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'إنشاء مستخدم جديد',
    description: 'إنشاء مستخدم جديد في النظام (للمديرين فقط)',
  })
  @ApiResponse({
    status: 201,
    description: 'تم إنشاء المستخدم بنجاح',
    type: User,
  })
  @ApiResponse({
    status: 400,
    description: 'بيانات غير صالحة',
  })
  @ApiResponse({
    status: 409,
    description: 'المستخدم موجود بالفعل',
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.AGENT)
  @ApiOperation({
    summary: 'الحصول على قائمة المستخدمين',
    description: 'استرجاع قائمة المستخدمين مع إمكانية التصفية والبحث',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'رقم الصفحة',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'عدد العناصر في الصفحة',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'البحث في الاسم أو البريد الإلكتروني',
    example: 'أحمد',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: UserStatus,
    description: 'تصفية حسب الحالة',
  })
  @ApiQuery({
    name: 'role',
    required: false,
    enum: UserRole,
    description: 'تصفية حسب الدور',
  })
  @ApiResponse({
    status: 200,
    description: 'قائمة المستخدمين',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/User' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  })
  async findAll(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') search?: string,
    @Query('status') status?: UserStatus,
    @Query('role') role?: UserRole,
  ) {
    return this.usersService.findAll({
      page,
      limit,
      search,
      status,
      role,
    });
  }

  @Get('me')
  @ApiOperation({
    summary: 'الحصول على بيانات المستخدم الحالي',
    description: 'استرجاع بيانات المستخدم المسجل حالياً',
  })
  @ApiResponse({
    status: 200,
    description: 'بيانات المستخدم',
    type: User,
  })
  async getProfile(@GetUser() user: User): Promise<User> {
    return this.usersService.findById(user.id);
  }

  @Get(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.AGENT)
  @ApiOperation({
    summary: 'الحصول على مستخدم محدد',
    description: 'استرجاع بيانات مستخدم محدد بواسطة ID',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'بيانات المستخدم',
    type: User,
  })
  @ApiResponse({
    status: 404,
    description: 'المستخدم غير موجود',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<User> {
    return this.usersService.findById(id);
  }

  @Patch('me')
  @ApiOperation({
    summary: 'تحديث بيانات المستخدم الحالي',
    description: 'تحديث بيانات المستخدم المسجل حالياً',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تحديث البيانات بنجاح',
    type: User,
  })
  @ApiResponse({
    status: 400,
    description: 'بيانات غير صالحة',
  })
  async updateProfile(
    @GetUser() user: User,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<User> {
    return this.usersService.update(user.id, updateUserDto);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'تحديث مستخدم محدد',
    description: 'تحديث بيانات مستخدم محدد (للمديرين فقط)',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تحديث المستخدم بنجاح',
    type: User,
  })
  @ApiResponse({
    status: 404,
    description: 'المستخدم غير موجود',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<User> {
    return this.usersService.update(id, updateUserDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(RolesGuard)
  @Roles(UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'حذف مستخدم',
    description: 'حذف مستخدم من النظام (للمدير العام فقط)',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 204,
    description: 'تم حذف المستخدم بنجاح',
  })
  @ApiResponse({
    status: 404,
    description: 'المستخدم غير موجود',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.usersService.remove(id);
  }

  @Patch(':id/status')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'تغيير حالة المستخدم',
    description: 'تفعيل أو تعطيل أو تعليق المستخدم',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'تم تغيير الحالة بنجاح',
    type: User,
  })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('status') status: UserStatus,
  ): Promise<User> {
    return this.usersService.updateStatus(id, status);
  }

  @Get(':id/activity')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.AGENT)
  @ApiOperation({
    summary: 'الحصول على نشاط المستخدم',
    description: 'استرجاع سجل نشاط المستخدم',
  })
  @ApiParam({
    name: 'id',
    description: 'معرف المستخدم',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'سجل نشاط المستخدم',
  })
  async getUserActivity(@Param('id', ParseUUIDPipe) id: string) {
    return this.usersService.getUserActivity(id);
  }
}
