{"name": "eslint-scope", "description": "ECMAScript scope analyzer for ESLint", "homepage": "http://github.com/eslint/eslint-scope", "main": "./dist/eslint-scope.cjs", "type": "module", "exports": {".": {"import": "./lib/index.js", "require": "./dist/eslint-scope.cjs"}, "./package.json": "./package.json"}, "version": "7.2.2", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "repository": "eslint/eslint-scope", "funding": "https://opencollective.com/eslint", "bugs": {"url": "https://github.com/eslint/eslint-scope/issues"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"build": "rollup -c", "lint": "npm run build && node Makefile.js lint", "update-version": "node tools/update-version.js", "test": "npm run build && node Makefile.js test", "prepublishOnly": "npm run update-version && npm run build", "generate-release": "eslint-generate-release", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "publish-release": "eslint-publish-release"}, "files": ["LICENSE", "README.md", "lib", "dist/eslint-scope.cjs"], "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"@typescript-eslint/parser": "^4.28.1", "c8": "^7.7.3", "chai": "^4.3.4", "eslint": "^7.29.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "mocha": "^9.0.1", "npm-license": "^0.3.3", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5"}}