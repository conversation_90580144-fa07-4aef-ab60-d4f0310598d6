{"version": 3, "file": "NodeParserLinkTag.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserLinkTag.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,IAAI,CAAC,iCAAiC,EAAE;IACtC,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,0CAA0C;QAC1C,sCAAsC;QACtC,YAAY;QACZ,kCAAkC;QAClC,eAAe;QACf,QAAQ;QACR,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,iCAAiC,EAAE;IACtC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,YAAY,EAAE,6CAA6C,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACvF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE;IAC5C,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,gCAAgC;QAChC,gDAAgD;QAChD,gCAAgC;QAChC,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE;IAC5C,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,uCAAuC;QACvC,iDAAiD;QACjD,mCAAmC;QACnC,oCAAoC;QACpC,2BAA2B;QAC3B,oBAAoB;QACpB,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+DAA+D,EAAE;IACpE,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,yBAAyB;QACzB,+BAA+B;QAC/B,qCAAqC;QACrC,4CAA4C;QAC5C,gCAAgC;QAChC,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+DAA+D,EAAE;IACpE,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,uBAAuB;QACvB,2BAA2B;QAC3B,6BAA6B;QAC7B,wBAAwB;QACxB,iCAAiC;QACjC,oBAAoB;QACpB,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACF,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACjF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,mEAAmE,EAAE;IACxE,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,8BAA8B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACzG,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,mEAAmE,EAAE;IACxE,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,oBAAoB,EAAE,0BAA0B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5E,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('00 Link text: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link http://example1.com}',\r\n      ' * {@link http://example2.com|}',\r\n      ' * {@link http://example3.com| }',\r\n      ' * {@link http://example4.com|link text}',\r\n      ' * 1{@link http://example5.com| link',\r\n      ' * text }2',\r\n      ' * 3{@link http://example5.com| ',\r\n      ' * link text ',\r\n      ' *  }4',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('01 Link text: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@link}', ' * {@link http://example1.com| link | text}', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('02 URL destination: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link http://example1.com}',\r\n      ' * {@link https://example2.com#hash|link text}',\r\n      ' * {@link customscheme://data}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('03 URL destination: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link http://example1.com spaces}',\r\n      ' * {@link http://example2.com spaces|link text}',\r\n      ' * {@link ftp+ssh://example3.com}',\r\n      ' * {@link mailto:<EMAIL>}',\r\n      ' * {@link //example5.com}',\r\n      ' * {@link http://}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('04 Declaration reference with package name: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link my-example1#}',\r\n      ' * {@link my-example2/path3#}',\r\n      ' * {@link my-example4/path5/path6#}',\r\n      ' * {@link @scope/my-example7/path8/path9#}',\r\n      ' * {@link @scope/my-example7#}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('05 Declaration reference with package name: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link example1/#}',\r\n      ' * {@link example2/a//b#}',\r\n      ' * {@link @scope/ex@mple3#}',\r\n      ' * {@link @/example4#}',\r\n      ' * {@link @scope//my-example5#}',\r\n      ' * {@link @scope#}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@link @#}', ' * {@link #}', ' * {@link #Button}', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('06 Declaration reference with import path only: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@link ../path1#}', ' * {@link ./path2#}', ' * {@link ./path3/../path4#}', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('07 Declaration reference with import path only: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@link /path1#}', ' * {@link /path1 path2#}', ' */'].join('\\n')\r\n  );\r\n});\r\n"]}