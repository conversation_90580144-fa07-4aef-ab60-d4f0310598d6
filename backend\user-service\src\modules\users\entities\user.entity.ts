import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToOne,
  OneToMany,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserProfile } from '../../profile/entities/user-profile.entity';
import { UserDocument } from '../../documents/entities/user-document.entity';
import { UserWallet } from '../../wallet/entities/user-wallet.entity';

export enum UserStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  BLOCKED = 'blocked',
  DELETED = 'deleted',
}

export enum UserRole {
  USER = 'user',
  AGENT = 'agent',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

export enum KYCStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
}

@Entity('users')
@Index(['email'], { unique: true })
@Index(['phone'], { unique: true })
@Index(['nationalId'], { unique: true })
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Personal Information
  @Column({ length: 100 })
  firstName: string;

  @Column({ length: 100 })
  lastName: string;

  @Column({ unique: true, length: 255 })
  email: string;

  @Column({ unique: true, length: 20 })
  phone: string;

  @Column({ nullable: true, length: 20 })
  nationalId?: string;

  @Column({ type: 'date', nullable: true })
  dateOfBirth?: Date;

  @Column({ length: 10, nullable: true })
  gender?: string;

  @Column({ length: 100, nullable: true })
  nationality?: string;

  // Authentication
  @Column()
  @Exclude()
  password: string;

  @Column({ default: false })
  emailVerified: boolean;

  @Column({ default: false })
  phoneVerified: boolean;

  // Account Status
  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.PENDING,
  })
  status: UserStatus;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  // KYC Information
  @Column({
    type: 'enum',
    enum: KYCStatus,
    default: KYCStatus.NOT_STARTED,
  })
  kycStatus: KYCStatus;

  // Security
  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt?: Date;

  @Column({ nullable: true, length: 45 })
  lastLoginIp?: string;

  // Preferences
  @Column({ length: 5, default: 'ar' })
  preferredLanguage: string;

  @Column({ length: 50, default: 'Asia/Riyadh' })
  timezone: string;

  @Column({ length: 3, default: 'SAR' })
  preferredCurrency: string;

  @Column({ default: true })
  notificationsEnabled: boolean;

  // Metadata
  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  // Relations
  @OneToOne(() => UserProfile, profile => profile.user)
  profile: UserProfile;

  @OneToMany(() => UserDocument, document => document.user)
  documents: UserDocument[];

  @OneToMany(() => UserWallet, wallet => wallet.user)
  wallets: UserWallet[];

  // Virtual fields
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get isActive(): boolean {
    return this.status === UserStatus.ACTIVE;
  }

  get isVerified(): boolean {
    return this.emailVerified && this.phoneVerified;
  }

  get isKycApproved(): boolean {
    return this.kycStatus === KYCStatus.APPROVED;
  }

  // Serialization
  toJSON() {
    const { password, ...result } = this;
    return result;
  }
}
