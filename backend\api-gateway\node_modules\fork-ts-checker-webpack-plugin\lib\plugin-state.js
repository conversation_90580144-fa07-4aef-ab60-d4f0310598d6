"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPluginState = void 0;
function createPluginState() {
    return {
        issuesPromise: Promise.resolve(undefined),
        dependenciesPromise: Promise.resolve(undefined),
        abortController: undefined,
        aggregatedFilesChange: undefined,
        lastDependencies: undefined,
        watching: false,
        initialized: false,
        iteration: 0,
        webpackDevServerDoneTap: undefined,
    };
}
exports.createPluginState = createPluginState;
