{"name": "@redis/client", "version": "1.6.0", "license": "MIT", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/"], "scripts": {"test": "nyc -r text-summary -r lcov mocha -r source-map-support/register -r ts-node/register './lib/**/*.spec.ts'", "build": "tsc", "lint": "eslint ./*.ts ./lib/**/*.ts", "documentation": "typedoc"}, "dependencies": {"cluster-key-slot": "1.1.2", "generic-pool": "3.9.0", "yallist": "4.0.0"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@redis/test-utils": "*", "@types/node": "^20.6.2", "@types/sinon": "^10.0.16", "@types/yallist": "^4.0.1", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "eslint": "^8.49.0", "nyc": "^15.1.0", "release-it": "^16.1.5", "sinon": "^16.0.0", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "typedoc": "^0.25.1", "typescript": "^5.2.2"}, "engines": {"node": ">=14"}, "repository": {"type": "git", "url": "git://github.com/redis/node-redis.git"}, "bugs": {"url": "https://github.com/redis/node-redis/issues"}, "homepage": "https://github.com/redis/node-redis/tree/master/packages/client", "keywords": ["redis"]}