{"version": 3, "file": "getESLintCoreRule.js", "sourceRoot": "", "sources": ["../../src/util/getESLintCoreRule.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oDAAuD;AACvD,sDAA8C;AAC9C,+CAAiC;AAEjC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,sBAAO,CAAC,IAAI,CAAC,CAAC;AA4CjC,QAAA,iBAAiB,GAC5B,UAAU;IACR,CAAC,CAAC,CAAmB,MAAS,EAAc,EAAE,CAC1C,mBAAW,CAAC,UAAU;IACpB,yGAAyG;IACzG,OAAO,CAAC,6BAA6B,CAAC,CAAC,YAAY,CAAC,GAAG,CACrD,MAAM,CACO,EACf,uBAAuB,MAAM,cAAc,CAC5C;IACL,CAAC,CAAC,CAAmB,MAAS,EAAc,EAAE,CAC1C,OAAO,CAAC,oBAAoB,MAAM,EAAE,CAAe,CAAC;AAE5D,SAAgB,sBAAsB,CACpC,MAAS;IAET,IAAI,CAAC;QACH,OAAO,IAAA,yBAAiB,EAAI,MAAM,CAAC,CAAC;IACtC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AARD,wDAQC"}