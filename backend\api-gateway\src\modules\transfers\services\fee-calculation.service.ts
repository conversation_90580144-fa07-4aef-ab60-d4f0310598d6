import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../shared/logger/logger.service';
import { TransferType } from '../dto/create-transfer.dto';

export interface FeeBreakdown {
  baseFee: number;
  percentageFee: number;
  exchangeFee: number;
  serviceFee: number;
  countryFee: number;
  urgentFee: number;
  totalFees: number;
  currency: string;
}

export interface FeeStructure {
  transferType: TransferType;
  country: string;
  baseFee: number;
  percentageRate: number;
  minFee: number;
  maxFee: number;
  exchangeMarkup: number;
}

@Injectable()
export class FeeCalculationService {
  private readonly feeStructures: Map<string, FeeStructure> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext('FeeCalculationService');
    this.initializeFeeStructures();
  }

  /**
   * حساب الرسوم الإجمالية للتحويل
   */
  async calculateFees(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
    transferType: TransferType,
    destinationCountry: string,
    isUrgent: boolean = false,
  ): Promise<FeeBreakdown> {
    try {
      const feeStructure = this.getFeeStructure(transferType, destinationCountry);
      
      // الرسوم الأساسية
      const baseFee = feeStructure.baseFee;
      
      // رسوم النسبة المئوية
      const percentageFee = amount * (feeStructure.percentageRate / 100);
      
      // رسوم الصرف
      const exchangeFee = this.calculateExchangeFee(
        amount,
        fromCurrency,
        toCurrency,
        feeStructure.exchangeMarkup,
      );
      
      // رسوم الخدمة
      const serviceFee = this.calculateServiceFee(transferType, amount);
      
      // رسوم البلد
      const countryFee = this.calculateCountryFee(destinationCountry, amount);
      
      // رسوم الاستعجال
      const urgentFee = isUrgent ? this.calculateUrgentFee(amount) : 0;
      
      // إجمالي الرسوم قبل التحقق من الحدود
      let totalFees = baseFee + percentageFee + exchangeFee + serviceFee + countryFee + urgentFee;
      
      // تطبيق الحد الأدنى والأقصى للرسوم
      totalFees = Math.max(totalFees, feeStructure.minFee);
      totalFees = Math.min(totalFees, feeStructure.maxFee);

      const feeBreakdown: FeeBreakdown = {
        baseFee,
        percentageFee,
        exchangeFee,
        serviceFee,
        countryFee,
        urgentFee,
        totalFees,
        currency: fromCurrency,
      };

      this.logger.log('تم حساب الرسوم', {
        amount,
        fromCurrency,
        toCurrency,
        transferType,
        destinationCountry,
        totalFees,
      });

      return feeBreakdown;
    } catch (error) {
      this.logger.error('خطأ في حساب الرسوم', error.stack, {
        amount,
        fromCurrency,
        toCurrency,
        transferType,
        destinationCountry,
      });
      throw error;
    }
  }

  /**
   * حساب الرسوم للتحويل المجمع
   */
  async calculateBulkTransferFees(
    transfers: Array<{
      amount: number;
      fromCurrency: string;
      toCurrency: string;
      transferType: TransferType;
      destinationCountry: string;
    }>,
  ): Promise<{ totalFees: number; individualFees: FeeBreakdown[]; discount: number }> {
    const individualFees: FeeBreakdown[] = [];
    let totalFees = 0;

    // حساب الرسوم لكل تحويل
    for (const transfer of transfers) {
      const fees = await this.calculateFees(
        transfer.amount,
        transfer.fromCurrency,
        transfer.toCurrency,
        transfer.transferType,
        transfer.destinationCountry,
      );
      individualFees.push(fees);
      totalFees += fees.totalFees;
    }

    // تطبيق خصم التحويل المجمع
    const discount = this.calculateBulkDiscount(transfers.length, totalFees);
    const finalTotalFees = totalFees - discount;

    return {
      totalFees: finalTotalFees,
      individualFees,
      discount,
    };
  }

  /**
   * حساب رسوم الاشتراك الشهري
   */
  calculateSubscriptionDiscount(
    subscriptionTier: 'basic' | 'premium' | 'enterprise',
    originalFees: number,
  ): { discountedFees: number; discount: number; discountPercentage: number } {
    const discountRates = {
      basic: 0.1, // 10% خصم
      premium: 0.25, // 25% خصم
      enterprise: 0.4, // 40% خصم
    };

    const discountPercentage = discountRates[subscriptionTier] || 0;
    const discount = originalFees * discountPercentage;
    const discountedFees = originalFees - discount;

    return {
      discountedFees,
      discount,
      discountPercentage: discountPercentage * 100,
    };
  }

  /**
   * حساب رسوم التحويل المجدول
   */
  calculateScheduledTransferFees(
    amount: number,
    frequency: 'weekly' | 'monthly' | 'quarterly',
    duration: number, // بالأشهر
  ): { totalFees: number; monthlyFees: number; discount: number } {
    const baseMonthlyFee = amount * 0.02; // 2% شهرياً
    
    const frequencyMultipliers = {
      weekly: 4.33, // متوسط الأسابيع في الشهر
      monthly: 1,
      quarterly: 0.33,
    };

    const monthlyFees = baseMonthlyFee * frequencyMultipliers[frequency];
    const totalFeesWithoutDiscount = monthlyFees * duration;
    
    // خصم للتحويل المجدول طويل المدى
    const longTermDiscount = duration > 12 ? 0.15 : duration > 6 ? 0.1 : 0.05;
    const discount = totalFeesWithoutDiscount * longTermDiscount;
    const totalFees = totalFeesWithoutDiscount - discount;

    return {
      totalFees,
      monthlyFees,
      discount,
    };
  }

  /**
   * الحصول على هيكل الرسوم لنوع التحويل والبلد
   */
  private getFeeStructure(transferType: TransferType, country: string): FeeStructure {
    const key = `${transferType}_${country}`;
    let structure = this.feeStructures.get(key);
    
    if (!structure) {
      // استخدام الهيكل الافتراضي للنوع
      structure = this.feeStructures.get(transferType) || this.getDefaultFeeStructure();
    }

    return structure;
  }

  /**
   * حساب رسوم الصرف
   */
  private calculateExchangeFee(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
    exchangeMarkup: number,
  ): number {
    if (fromCurrency === toCurrency) {
      return 0;
    }

    return amount * (exchangeMarkup / 100);
  }

  /**
   * حساب رسوم الخدمة حسب نوع التحويل
   */
  private calculateServiceFee(transferType: TransferType, amount: number): number {
    const serviceFeeRates = {
      [TransferType.CASH_PICKUP]: 0.005, // 0.5%
      [TransferType.BANK_DEPOSIT]: 0.003, // 0.3%
      [TransferType.MOBILE_WALLET]: 0.002, // 0.2%
      [TransferType.HOME_DELIVERY]: 0.01, // 1%
    };

    return amount * (serviceFeeRates[transferType] || 0.005);
  }

  /**
   * حساب رسوم البلد
   */
  private calculateCountryFee(country: string, amount: number): number {
    const countryFeeRates = {
      'US': 0.002, // 0.2%
      'GB': 0.003, // 0.3%
      'AE': 0.001, // 0.1%
      'KW': 0.001, // 0.1%
      'QA': 0.001, // 0.1%
      'BH': 0.001, // 0.1%
      'OM': 0.001, // 0.1%
      'EG': 0.005, // 0.5%
      'JO': 0.003, // 0.3%
      'LB': 0.007, // 0.7%
      'IN': 0.004, // 0.4%
      'PK': 0.006, // 0.6%
      'BD': 0.008, // 0.8%
    };

    const feeRate = countryFeeRates[country] || 0.005; // 0.5% افتراضي
    return amount * feeRate;
  }

  /**
   * حساب رسوم الاستعجال
   */
  private calculateUrgentFee(amount: number): number {
    return Math.max(amount * 0.02, 25); // 2% أو 25 كحد أدنى
  }

  /**
   * حساب خصم التحويل المجمع
   */
  private calculateBulkDiscount(transferCount: number, totalFees: number): number {
    if (transferCount < 5) return 0;
    
    const discountRates = {
      5: 0.05,   // 5% خصم للـ 5-9 تحويلات
      10: 0.1,   // 10% خصم للـ 10-19 تحويل
      20: 0.15,  // 15% خصم للـ 20-49 تحويل
      50: 0.2,   // 20% خصم للـ 50+ تحويل
    };

    let discountRate = 0;
    if (transferCount >= 50) discountRate = discountRates[50];
    else if (transferCount >= 20) discountRate = discountRates[20];
    else if (transferCount >= 10) discountRate = discountRates[10];
    else if (transferCount >= 5) discountRate = discountRates[5];

    return totalFees * discountRate;
  }

  /**
   * تهيئة هياكل الرسوم
   */
  private initializeFeeStructures(): void {
    // رسوم الاستلام النقدي
    this.feeStructures.set(TransferType.CASH_PICKUP, {
      transferType: TransferType.CASH_PICKUP,
      country: 'default',
      baseFee: 15,
      percentageRate: 2.5,
      minFee: 10,
      maxFee: 200,
      exchangeMarkup: 1.5,
    });

    // رسوم التحويل البنكي
    this.feeStructures.set(TransferType.BANK_DEPOSIT, {
      transferType: TransferType.BANK_DEPOSIT,
      country: 'default',
      baseFee: 10,
      percentageRate: 1.8,
      minFee: 8,
      maxFee: 150,
      exchangeMarkup: 1.2,
    });

    // رسوم المحفظة الإلكترونية
    this.feeStructures.set(TransferType.MOBILE_WALLET, {
      transferType: TransferType.MOBILE_WALLET,
      country: 'default',
      baseFee: 5,
      percentageRate: 1.5,
      minFee: 5,
      maxFee: 100,
      exchangeMarkup: 1.0,
    });

    // رسوم التوصيل المنزلي
    this.feeStructures.set(TransferType.HOME_DELIVERY, {
      transferType: TransferType.HOME_DELIVERY,
      country: 'default',
      baseFee: 25,
      percentageRate: 3.0,
      minFee: 20,
      maxFee: 300,
      exchangeMarkup: 2.0,
    });

    // رسوم خاصة لبلدان معينة
    this.addCountrySpecificFees();
  }

  /**
   * إضافة رسوم خاصة بالبلدان
   */
  private addCountrySpecificFees(): void {
    // الإمارات - رسوم منخفضة
    this.feeStructures.set(`${TransferType.BANK_DEPOSIT}_AE`, {
      transferType: TransferType.BANK_DEPOSIT,
      country: 'AE',
      baseFee: 5,
      percentageRate: 1.0,
      minFee: 5,
      maxFee: 100,
      exchangeMarkup: 0.8,
    });

    // الهند - رسوم متوسطة
    this.feeStructures.set(`${TransferType.CASH_PICKUP}_IN`, {
      transferType: TransferType.CASH_PICKUP,
      country: 'IN',
      baseFee: 12,
      percentageRate: 2.0,
      minFee: 8,
      maxFee: 180,
      exchangeMarkup: 1.3,
    });

    // مصر - رسوم متوسطة
    this.feeStructures.set(`${TransferType.CASH_PICKUP}_EG`, {
      transferType: TransferType.CASH_PICKUP,
      country: 'EG',
      baseFee: 18,
      percentageRate: 2.8,
      minFee: 12,
      maxFee: 220,
      exchangeMarkup: 1.8,
    });
  }

  /**
   * الحصول على هيكل الرسوم الافتراضي
   */
  private getDefaultFeeStructure(): FeeStructure {
    return {
      transferType: TransferType.CASH_PICKUP,
      country: 'default',
      baseFee: 20,
      percentageRate: 3.0,
      minFee: 15,
      maxFee: 250,
      exchangeMarkup: 2.0,
    };
  }
}
