-- Migration: Create Agents Tables
-- Description: إنشاء جداول نظام الوكلاء المتقدم
-- Version: 004
-- Created: 2024-01-15

-- Create agent profiles table
CREATE TABLE IF NOT EXISTS agent_profiles (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL UNIQUE,
    
    -- Agent Basic Info
    agent_code VARCHAR(20) UNIQUE NOT NULL,
    agent_type VARCHAR(20) NOT NULL DEFAULT 'individual' CHECK (agent_type IN ('individual', 'business', 'corporate')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'suspended', 'terminated')),
    
    -- Hierarchy
    parent_agent_id VARCHAR(50),
    level INTEGER NOT NULL DEFAULT 1 CHECK (level BETWEEN 1 AND 10),
    hierarchy_path TEXT, -- e.g., "1.2.5" for easy querying
    
    -- Business Information
    business_name VARCHAR(200),
    business_license VARCHAR(50),
    tax_number VARCHAR(50),
    commercial_registration VARCHAR(50),
    
    -- Location
    region VARCHAR(100),
    city VARCHAR(100),
    district VARCHAR(100),
    address TEXT,
    coordinates POINT,
    coverage_areas TEXT[], -- Array of covered areas
    
    -- Contact Information
    primary_phone VARCHAR(20),
    secondary_phone VARCHAR(20),
    whatsapp_number VARCHAR(20),
    business_email VARCHAR(255),
    
    -- Financial Information
    bank_name VARCHAR(100),
    bank_account_number VARCHAR(50),
    iban VARCHAR(34),
    
    -- Commission Structure
    base_commission_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0100, -- 1%
    tier_commission_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0050, -- 0.5%
    volume_bonus_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0025, -- 0.25%
    
    -- Limits and Quotas
    daily_transaction_limit DECIMAL(15,2) DEFAULT 100000.00,
    monthly_transaction_limit DECIMAL(15,2) DEFAULT 2000000.00,
    single_transaction_limit DECIMAL(15,2) DEFAULT 50000.00,
    
    -- Performance Metrics
    total_transactions INTEGER NOT NULL DEFAULT 0,
    total_volume DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_commission_earned DECIMAL(15,2) NOT NULL DEFAULT 0,
    customer_count INTEGER NOT NULL DEFAULT 0,
    
    -- Ratings and Reviews
    rating DECIMAL(3,2) DEFAULT 0 CHECK (rating BETWEEN 0 AND 5),
    review_count INTEGER NOT NULL DEFAULT 0,
    
    -- Training and Certification
    training_completed BOOLEAN NOT NULL DEFAULT false,
    certification_level INTEGER DEFAULT 1 CHECK (certification_level BETWEEN 1 AND 5),
    last_training_date DATE,
    next_training_due DATE,
    
    -- Onboarding
    onboarding_completed BOOLEAN NOT NULL DEFAULT false,
    onboarding_completed_at TIMESTAMP WITH TIME ZONE,
    approved_by VARCHAR(50),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    tags TEXT[],
    notes TEXT,
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Soft Delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by VARCHAR(50),
    
    -- Foreign Key Constraints
    CONSTRAINT fk_agent_profiles_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_agent_profiles_parent FOREIGN KEY (parent_agent_id) REFERENCES agent_profiles(id),
    CONSTRAINT fk_agent_profiles_approved_by FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- Create agent commissions table
CREATE TABLE IF NOT EXISTS agent_commissions (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Basic Info
    agent_id VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(50) NOT NULL,
    
    -- Commission Details
    commission_type VARCHAR(20) NOT NULL CHECK (commission_type IN ('direct', 'tier', 'volume_bonus', 'special')),
    commission_rate DECIMAL(5,4) NOT NULL,
    transaction_amount DECIMAL(15,2) NOT NULL,
    commission_amount DECIMAL(15,2) NOT NULL,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'paid', 'cancelled')),
    
    -- Payment Information
    payment_batch_id VARCHAR(50),
    paid_at TIMESTAMP WITH TIME ZONE,
    payment_reference VARCHAR(100),
    
    -- Period Information
    commission_period VARCHAR(7), -- YYYY-MM format
    due_date DATE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Foreign Key Constraints
    CONSTRAINT fk_agent_commissions_agent FOREIGN KEY (agent_id) REFERENCES agent_profiles(id),
    CONSTRAINT fk_agent_commissions_transaction FOREIGN KEY (transaction_id) REFERENCES transactions(id)
);

-- Create agent performance table
CREATE TABLE IF NOT EXISTS agent_performance (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Basic Info
    agent_id VARCHAR(50) NOT NULL,
    period_type VARCHAR(10) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    -- Transaction Metrics
    transaction_count INTEGER NOT NULL DEFAULT 0,
    transaction_volume DECIMAL(15,2) NOT NULL DEFAULT 0,
    average_transaction_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Commission Metrics
    total_commission DECIMAL(15,2) NOT NULL DEFAULT 0,
    direct_commission DECIMAL(15,2) NOT NULL DEFAULT 0,
    tier_commission DECIMAL(15,2) NOT NULL DEFAULT 0,
    bonus_commission DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Customer Metrics
    new_customers INTEGER NOT NULL DEFAULT 0,
    active_customers INTEGER NOT NULL DEFAULT 0,
    customer_retention_rate DECIMAL(5,4) DEFAULT 0,
    
    -- Quality Metrics
    success_rate DECIMAL(5,4) DEFAULT 1.0,
    average_processing_time INTEGER DEFAULT 0, -- in seconds
    customer_satisfaction DECIMAL(3,2) DEFAULT 0,
    complaint_count INTEGER NOT NULL DEFAULT 0,
    
    -- Ranking
    regional_rank INTEGER,
    national_rank INTEGER,
    tier_rank INTEGER,
    
    -- Goals and Targets
    volume_target DECIMAL(15,2) DEFAULT 0,
    volume_achievement_rate DECIMAL(5,4) DEFAULT 0,
    transaction_target INTEGER DEFAULT 0,
    transaction_achievement_rate DECIMAL(5,4) DEFAULT 0,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    CONSTRAINT fk_agent_performance_agent FOREIGN KEY (agent_id) REFERENCES agent_profiles(id),
    
    -- Unique constraint for period
    CONSTRAINT uk_agent_performance_period UNIQUE (agent_id, period_type, period_start)
);

-- Create agent customers table (relationship tracking)
CREATE TABLE IF NOT EXISTS agent_customers (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Basic Info
    agent_id VARCHAR(50) NOT NULL,
    customer_id VARCHAR(50) NOT NULL,
    
    -- Relationship Details
    relationship_type VARCHAR(20) NOT NULL DEFAULT 'direct' CHECK (relationship_type IN ('direct', 'referred', 'inherited')),
    acquisition_date DATE NOT NULL DEFAULT CURRENT_DATE,
    acquisition_channel VARCHAR(50),
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'transferred')),
    
    -- Performance with this customer
    total_transactions INTEGER NOT NULL DEFAULT 0,
    total_volume DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_commission DECIMAL(15,2) NOT NULL DEFAULT 0,
    last_transaction_date DATE,
    
    -- Customer Satisfaction
    satisfaction_rating DECIMAL(3,2) DEFAULT 0,
    complaint_count INTEGER NOT NULL DEFAULT 0,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    notes TEXT,
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Foreign Key Constraints
    CONSTRAINT fk_agent_customers_agent FOREIGN KEY (agent_id) REFERENCES agent_profiles(id),
    CONSTRAINT fk_agent_customers_customer FOREIGN KEY (customer_id) REFERENCES users(id),
    
    -- Unique constraint
    CONSTRAINT uk_agent_customers UNIQUE (agent_id, customer_id)
);

-- Create agent training table
CREATE TABLE IF NOT EXISTS agent_training (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Basic Info
    agent_id VARCHAR(50) NOT NULL,
    training_type VARCHAR(50) NOT NULL,
    training_title VARCHAR(200) NOT NULL,
    
    -- Training Details
    description TEXT,
    duration_hours INTEGER DEFAULT 0,
    training_date DATE NOT NULL,
    completion_date DATE,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled', 'failed')),
    
    -- Results
    score DECIMAL(5,2) DEFAULT 0 CHECK (score BETWEEN 0 AND 100),
    passed BOOLEAN DEFAULT false,
    certificate_issued BOOLEAN DEFAULT false,
    certificate_number VARCHAR(50),
    
    -- Training Provider
    trainer_name VARCHAR(100),
    training_provider VARCHAR(100),
    training_location VARCHAR(200),
    training_method VARCHAR(20) DEFAULT 'online' CHECK (training_method IN ('online', 'offline', 'hybrid')),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Foreign Key Constraints
    CONSTRAINT fk_agent_training_agent FOREIGN KEY (agent_id) REFERENCES agent_profiles(id)
);

-- Create indexes for better performance
CREATE INDEX idx_agent_profiles_user_id ON agent_profiles(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_agent_profiles_agent_code ON agent_profiles(agent_code) WHERE deleted_at IS NULL;
CREATE INDEX idx_agent_profiles_status ON agent_profiles(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_agent_profiles_parent ON agent_profiles(parent_agent_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_agent_profiles_level ON agent_profiles(level) WHERE deleted_at IS NULL;
CREATE INDEX idx_agent_profiles_region ON agent_profiles(region) WHERE deleted_at IS NULL;

-- Create indexes for commissions
CREATE INDEX idx_agent_commissions_agent ON agent_commissions(agent_id);
CREATE INDEX idx_agent_commissions_transaction ON agent_commissions(transaction_id);
CREATE INDEX idx_agent_commissions_status ON agent_commissions(status);
CREATE INDEX idx_agent_commissions_period ON agent_commissions(commission_period);
CREATE INDEX idx_agent_commissions_created_at ON agent_commissions(created_at);

-- Create indexes for performance
CREATE INDEX idx_agent_performance_agent ON agent_performance(agent_id);
CREATE INDEX idx_agent_performance_period ON agent_performance(period_type, period_start);
CREATE INDEX idx_agent_performance_volume ON agent_performance(transaction_volume);

-- Create indexes for customers
CREATE INDEX idx_agent_customers_agent ON agent_customers(agent_id);
CREATE INDEX idx_agent_customers_customer ON agent_customers(customer_id);
CREATE INDEX idx_agent_customers_status ON agent_customers(status);

-- Create indexes for training
CREATE INDEX idx_agent_training_agent ON agent_training(agent_id);
CREATE INDEX idx_agent_training_status ON agent_training(status);
CREATE INDEX idx_agent_training_date ON agent_training(training_date);

-- Create GIN indexes for JSONB fields
CREATE INDEX idx_agent_profiles_metadata ON agent_profiles USING GIN(metadata) WHERE deleted_at IS NULL;
CREATE INDEX idx_agent_profiles_tags ON agent_profiles USING GIN(tags) WHERE deleted_at IS NULL;
CREATE INDEX idx_agent_commissions_metadata ON agent_commissions USING GIN(metadata);
CREATE INDEX idx_agent_performance_metadata ON agent_performance USING GIN(metadata);

-- Create function to generate agent code
CREATE OR REPLACE FUNCTION generate_agent_code()
RETURNS TRIGGER AS $$
DECLARE
    region_code VARCHAR(3);
    sequence_part VARCHAR(6);
    new_code VARCHAR(20);
BEGIN
    -- Get region code (first 3 letters of region, default to 'GEN')
    region_code := COALESCE(UPPER(LEFT(NEW.region, 3)), 'GEN');
    
    -- Get sequence part
    SELECT LPAD(NEXTVAL('agent_code_seq')::TEXT, 6, '0') INTO sequence_part;
    
    -- Combine parts: AGT + region + sequence
    new_code := 'AGT' || region_code || sequence_part;
    
    NEW.agent_code := new_code;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for agent codes
CREATE SEQUENCE IF NOT EXISTS agent_code_seq START 1;

-- Create trigger to auto-generate agent code
CREATE TRIGGER generate_agent_code_trigger
    BEFORE INSERT ON agent_profiles
    FOR EACH ROW
    EXECUTE FUNCTION generate_agent_code();

-- Create function to generate agent profile ID
CREATE OR REPLACE FUNCTION generate_agent_profile_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'agent_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for ID generation
CREATE TRIGGER generate_agent_profile_id_trigger
    BEFORE INSERT ON agent_profiles
    FOR EACH ROW
    EXECUTE FUNCTION generate_agent_profile_id();

-- Similar ID generation for other tables
CREATE OR REPLACE FUNCTION generate_agent_commission_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'comm_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER generate_agent_commission_id_trigger
    BEFORE INSERT ON agent_commissions
    FOR EACH ROW
    EXECUTE FUNCTION generate_agent_commission_id();

-- Create triggers for updated_at
CREATE TRIGGER update_agent_profiles_updated_at
    BEFORE UPDATE ON agent_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_commissions_updated_at
    BEFORE UPDATE ON agent_commissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_performance_updated_at
    BEFORE UPDATE ON agent_performance
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_customers_updated_at
    BEFORE UPDATE ON agent_customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_training_updated_at
    BEFORE UPDATE ON agent_training
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to update hierarchy path
CREATE OR REPLACE FUNCTION update_agent_hierarchy_path()
RETURNS TRIGGER AS $$
DECLARE
    parent_path TEXT;
BEGIN
    IF NEW.parent_agent_id IS NOT NULL THEN
        -- Get parent's hierarchy path
        SELECT hierarchy_path INTO parent_path
        FROM agent_profiles
        WHERE id = NEW.parent_agent_id;
        
        -- Build new path
        IF parent_path IS NOT NULL THEN
            NEW.hierarchy_path := parent_path || '.' || NEW.id;
            NEW.level := array_length(string_to_array(NEW.hierarchy_path, '.'), 1);
        ELSE
            NEW.hierarchy_path := NEW.id;
            NEW.level := 1;
        END IF;
    ELSE
        NEW.hierarchy_path := NEW.id;
        NEW.level := 1;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for hierarchy path
CREATE TRIGGER update_agent_hierarchy_path_trigger
    BEFORE INSERT OR UPDATE ON agent_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_agent_hierarchy_path();

-- Add comments for documentation
COMMENT ON TABLE agent_profiles IS 'جدول ملفات الوكلاء - يحتوي على جميع بيانات الوكلاء والهيكل الهرمي';
COMMENT ON COLUMN agent_profiles.agent_code IS 'رمز الوكيل الفريد';
COMMENT ON COLUMN agent_profiles.hierarchy_path IS 'مسار الهيكل الهرمي للوكيل';
COMMENT ON COLUMN agent_profiles.base_commission_rate IS 'معدل العمولة الأساسي';

COMMENT ON TABLE agent_commissions IS 'جدول عمولات الوكلاء - تتبع جميع العمولات المستحقة والمدفوعة';
COMMENT ON TABLE agent_performance IS 'جدول أداء الوكلاء - إحصائيات الأداء حسب الفترات';
COMMENT ON TABLE agent_customers IS 'جدول عملاء الوكلاء - تتبع العلاقة بين الوكلاء والعملاء';
COMMENT ON TABLE agent_training IS 'جدول تدريب الوكلاء - سجل التدريبات والشهادات';

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON agent_profiles TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON agent_commissions TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON agent_performance TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON agent_customers TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON agent_training TO ws_app_user;
GRANT USAGE ON SEQUENCE agent_code_seq TO ws_app_user;
