-- Migration: Create Users Table
-- Description: إنشاء جدول المستخدمين الأساسي
-- Version: 001
-- Created: 2024-01-15

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(50) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    
    -- Personal Information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    full_name VARCHAR(200) GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    date_of_birth DATE,
    nationality VARCHAR(3), -- ISO 3166-1 alpha-3 country code
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
    
    -- Role and Permissions
    role VARCHAR(20) NOT NULL DEFAULT 'user' CHECK (role IN ('super_admin', 'admin', 'agent_manager', 'agent', 'user', 'guest')),
    
    -- Account Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    is_suspended BOOLEAN NOT NULL DEFAULT false,
    suspension_reason TEXT,
    suspended_until TIMESTAMP WITH TIME ZONE,
    
    -- Two-Factor Authentication
    two_factor_enabled BOOLEAN NOT NULL DEFAULT false,
    two_factor_secret VARCHAR(32),
    backup_codes TEXT[], -- Array of backup codes
    
    -- Security
    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    must_change_password BOOLEAN NOT NULL DEFAULT false,
    
    -- Login Tracking
    last_login TIMESTAMP WITH TIME ZONE,
    last_login_ip INET,
    last_login_user_agent TEXT,
    login_count INTEGER NOT NULL DEFAULT 0,
    
    -- Email Verification
    email_verification_token VARCHAR(255),
    email_verification_expires_at TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Phone Verification
    phone_verification_code VARCHAR(10),
    phone_verification_expires_at TIMESTAMP WITH TIME ZONE,
    phone_verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Password Reset
    password_reset_token VARCHAR(255),
    password_reset_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- KYC Information
    kyc_level INTEGER NOT NULL DEFAULT 0 CHECK (kyc_level BETWEEN 0 AND 3),
    kyc_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'in_review', 'approved', 'rejected', 'expired')),
    kyc_submitted_at TIMESTAMP WITH TIME ZONE,
    kyc_approved_at TIMESTAMP WITH TIME ZONE,
    kyc_rejected_reason TEXT,
    
    -- Address Information
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(3), -- ISO 3166-1 alpha-3 country code
    
    -- Financial Information
    preferred_currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    monthly_income_range VARCHAR(20),
    occupation VARCHAR(100),
    employer_name VARCHAR(200),
    
    -- Preferences
    language VARCHAR(5) NOT NULL DEFAULT 'ar',
    timezone VARCHAR(50) NOT NULL DEFAULT 'Asia/Riyadh',
    notification_preferences JSONB DEFAULT '{"email": true, "sms": true, "push": true}',
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    tags TEXT[],
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Soft Delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by VARCHAR(50)
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_phone ON users(phone) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_role ON users(role) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_is_active ON users(is_active) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_kyc_status ON users(kyc_status) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_created_at ON users(created_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_last_login ON users(last_login) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_country ON users(country) WHERE deleted_at IS NULL;

-- Create partial indexes for security
CREATE INDEX idx_users_locked_accounts ON users(locked_until) WHERE locked_until IS NOT NULL AND locked_until > CURRENT_TIMESTAMP;
CREATE INDEX idx_users_failed_attempts ON users(failed_login_attempts) WHERE failed_login_attempts > 0;

-- Create GIN index for JSONB fields
CREATE INDEX idx_users_metadata ON users USING GIN(metadata) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_notification_preferences ON users USING GIN(notification_preferences) WHERE deleted_at IS NULL;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to generate user ID
CREATE OR REPLACE FUNCTION generate_user_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'user_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to auto-generate user ID
CREATE TRIGGER generate_users_id 
    BEFORE INSERT ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION generate_user_id();

-- Add comments for documentation
COMMENT ON TABLE users IS 'جدول المستخدمين الأساسي - يحتوي على جميع بيانات المستخدمين والمصادقة';
COMMENT ON COLUMN users.id IS 'معرف المستخدم الفريد';
COMMENT ON COLUMN users.email IS 'البريد الإلكتروني للمستخدم';
COMMENT ON COLUMN users.phone IS 'رقم الهاتف للمستخدم';
COMMENT ON COLUMN users.password_hash IS 'كلمة المرور المشفرة';
COMMENT ON COLUMN users.role IS 'دور المستخدم في النظام';
COMMENT ON COLUMN users.kyc_level IS 'مستوى التحقق من الهوية (0-3)';
COMMENT ON COLUMN users.two_factor_enabled IS 'هل التحقق بخطوتين مفعل';
COMMENT ON COLUMN users.failed_login_attempts IS 'عدد محاولات تسجيل الدخول الفاشلة';
COMMENT ON COLUMN users.metadata IS 'بيانات إضافية بصيغة JSON';

-- Create view for active users
CREATE VIEW active_users AS
SELECT 
    id, email, phone, first_name, last_name, full_name,
    role, is_active, is_verified, kyc_level, kyc_status,
    last_login, created_at, updated_at
FROM users 
WHERE deleted_at IS NULL AND is_active = true;

-- Create view for user statistics
CREATE VIEW user_statistics AS
SELECT 
    role,
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_active THEN 1 END) as active_users,
    COUNT(CASE WHEN is_verified THEN 1 END) as verified_users,
    COUNT(CASE WHEN two_factor_enabled THEN 1 END) as two_factor_users,
    COUNT(CASE WHEN kyc_status = 'approved' THEN 1 END) as kyc_approved_users,
    COUNT(CASE WHEN last_login > CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 1 END) as active_last_30_days
FROM users 
WHERE deleted_at IS NULL
GROUP BY role;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON users TO ws_app_user;
GRANT SELECT ON active_users TO ws_app_user;
GRANT SELECT ON user_statistics TO ws_app_user;
GRANT USAGE ON SEQUENCE users_id_seq TO ws_app_user;
