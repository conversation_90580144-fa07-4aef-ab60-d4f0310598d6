{"version": 3, "file": "ColdObservable.js", "sourceRoot": "", "sources": ["../../../../src/internal/testing/ColdObservable.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAI/C,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAElD,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAEtD;IAAuC,kCAAa;IAQlD,wBAAmB,QAAuB,EAAE,SAAoB;QAAhE,YACE,kBAAM,UAA+B,UAA2B;YAC9D,IAAM,UAAU,GAAsB,IAAW,CAAC;YAClD,IAAM,KAAK,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAC9C,IAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;YACxC,YAAY,CAAC,GAAG,CACd,IAAI,YAAY,CAAC;gBACf,UAAU,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC,CAAC,CACH,CAAC;YACF,UAAU,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACxC,OAAO,YAAY,CAAC;QACtB,CAAC,CAAC,SAEH;QAdkB,cAAQ,GAAR,QAAQ,CAAe;QAPnC,mBAAa,GAAsB,EAAE,CAAC;QAoB3C,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC;;IAC7B,CAAC;IAED,yCAAgB,GAAhB,UAAiB,UAA2B;QAC1C,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACjC,UAAU,CAAC,GAAG,CACZ,IAAI,CAAC,SAAS,CAAC,QAAQ,CACrB,UAAC,KAAK;gBACE,IAAA,KAAyD,KAAM,EAAlD,YAAY,0BAAA,EAAgB,WAAW,gBAAW,CAAC;gBACtE,mBAAmB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;YACjD,CAAC,EACD,OAAO,CAAC,KAAK,EACb,EAAE,OAAO,SAAA,EAAE,UAAU,YAAA,EAAE,CACxB,CACF,CAAC;SACH;IACH,CAAC;IACH,qBAAC;AAAD,CAAC,AAxCD,CAAuC,UAAU,GAwChD;;AACD,WAAW,CAAC,cAAc,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC"}