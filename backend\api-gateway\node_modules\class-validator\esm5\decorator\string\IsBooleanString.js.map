{"version": 3, "file": "IsBooleanString.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBooleanString.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,kBAAkB,MAAM,yBAAyB,CAAC;AAEzD,MAAM,CAAC,IAAM,iBAAiB,GAAG,iBAAiB,CAAC;AAEnD;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,KAAc;IAC5C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,iBAAqC;IACnE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,iBAAiB;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,eAAe,CAAC,KAAK,CAAC,EAAtB,CAAsB;YAC1D,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,oCAAoC,EAAjD,CAAiD,EAC/D,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBooleanValidator from 'validator/lib/isBoolean';\n\nexport const IS_BOOLEAN_STRING = 'isBooleanString';\n\n/**\n * Checks if a string is a boolean.\n * If given value is not a string, then it returns false.\n */\nexport function isBooleanString(value: unknown): boolean {\n  return typeof value === 'string' && isBooleanValidator(value);\n}\n\n/**\n * Checks if a string is a boolean.\n * If given value is not a string, then it returns false.\n */\nexport function IsBooleanString(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BOOLEAN_STRING,\n      validator: {\n        validate: (value, args): boolean => isBooleanString(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a boolean string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}