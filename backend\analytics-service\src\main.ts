import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('AnalyticsService');
  
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: [
      'http://localhost:3000',
      'http://localhost:3100',
      'https://ws-transfir.com',
      'https://app.ws-transfir.com',
    ],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true,
  });

  // API prefix
  app.setGlobalPrefix('api/v1');

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('WS Transfir Analytics Service')
    .setDescription('خدمة التحليلات والتقارير لنظام WS Transfir - تحليل البيانات وإنتاج التقارير')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag('Analytics', 'التحليلات العامة')
    .addTag('Reports', 'التقارير')
    .addTag('Dashboards', 'لوحات المعلومات')
    .addTag('KPIs', 'مؤشرات الأداء الرئيسية')
    .addTag('Trends', 'الاتجاهات والتوقعات')
    .addTag('Health', 'فحص صحة النظام')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  // Health check endpoint
  app.getHttpAdapter().get('/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      service: 'analytics-service',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: configService.get('NODE_ENV'),
      version: '1.0.0',
      connections: {
        database: 'connected',
        elasticsearch: configService.get('ELASTICSEARCH_ENABLED') === 'true' ? 'connected' : 'disabled',
        mongodb: configService.get('MONGODB_ENABLED') === 'true' ? 'connected' : 'disabled',
        redis: 'connected',
      },
    });
  });

  // Start server
  const port = configService.get('ANALYTICS_SERVICE_PORT') || 3005;
  await app.listen(port);

  logger.log(`🚀 Analytics Service is running on: http://localhost:${port}`);
  logger.log(`📚 API Documentation: http://localhost:${port}/docs`);
  logger.log(`🏥 Health Check: http://localhost:${port}/health`);
  logger.log(`🌍 Environment: ${configService.get('NODE_ENV')}`);
  logger.log(`📊 Analytics Features: ${getAnalyticsFeatures(configService)}`);
}

function getAnalyticsFeatures(configService: ConfigService): string {
  const features = [];
  
  if (configService.get('REAL_TIME_ANALYTICS') === 'true') features.push('Real-time');
  if (configService.get('ELASTICSEARCH_ENABLED') === 'true') features.push('Search Analytics');
  if (configService.get('MONGODB_ENABLED') === 'true') features.push('Document Analytics');
  if (configService.get('ML_PREDICTIONS') === 'true') features.push('ML Predictions');
  
  return features.length > 0 ? features.join(', ') : 'Basic Analytics';
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start Analytics Service:', error);
  process.exit(1);
});
