{"version": 3, "file": "iterate.js", "sourceRoot": "", "sources": ["../src/iterate.ts"], "names": [], "mappings": ";;AAAA,qCAAyC;AACzC,qCAAyC;AACzC,uCAA2C;AAC3C,+BAAmC;AACnC,mCAAuC;AACvC,mCAAoC;AACpC,+BAAmC;AAEnC,MAAa,qBAAqB;IAC9B;;OAEG;IACH,YAAoB,MAAmB;QAAnB,WAAM,GAAN,MAAM,CAAa;IAAG,CAAC;IAE3C;;OAEG;IACH,IAAI;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;IAC7B,CAAC;IAED;;;OAGG;IACH,CAAC,MAAM,CAAC,QAAQ,CAAC;QACb,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,GAAG,CAAI,QAAyB;QAC5B,OAAO,IAAI,qBAAqB,CAAC,IAAI,iBAAW,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;IAC5E,CAAC;IAOD,MAAM,CAAC,SAAkC;QACrC,OAAO,IAAI,qBAAqB,CAAC,IAAI,uBAAc,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAA;IAChF,CAAC;IAED;;OAEG;IACH,MAAM,CAAI,UAAqC;QAC3C,OAAO,IAAI,qBAAqB,CAAC,IAAI,uBAAc,CAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,kBAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;IACtG,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,KAAa;QACd,OAAO,IAAI,qBAAqB,CAAC,IAAI,qBAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;IAClF,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,CAAS;QACV,OAAO,IAAI,qBAAqB,CAAC,IAAI,qBAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAA;IACjF,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAa,EAAE,GAAG,GAAG,QAAQ;QAC/B,OAAO,IAAI,qBAAqB,CAAC,IAAI,qBAAa,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAA;IAChF,CAAC;IAED;;OAEG;IACH,OAAO;QACH,OAAO,IAAI,qBAAqB,CAAC,IAAI,yBAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;IACtE,CAAC;IAeD,MAAM,CAAC,QAAqC,EAAE,WAAiB;QAC3D,IAAI,MAAyB,CAAA;QAC7B,IAAI,WAAW,KAAK,SAAS,EAAE;YAC3B,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,MAAM,CAAC,IAAI,EAAE;gBACb,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAA;aACxE;YACD,WAAW,GAAG,MAAM,CAAC,KAAK,CAAA;SAC7B;QACD,OAAO,IAAI,EAAE;YACT,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,MAAM,CAAC,IAAI,EAAE;gBACb,MAAK;aACR;YACD,WAAW,GAAG,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;SACpD;QACD,OAAO,WAAW,CAAA;IACtB,CAAC;IAiBD,IAAI,CAAC,SAAc;QACf,IAAI,MAAyB,CAAA;QAC7B,OAAO,IAAI,EAAE;YACT,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,MAAM,CAAC,IAAI,EAAE;gBACb,OAAO,SAAS,CAAA;aACnB;YACD,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACzB,OAAO,MAAM,CAAC,KAAK,CAAA;aACtB;SACJ;IACL,CAAC;IAED;;;;OAIG;IACH,QAAQ,CAAC,KAAQ;QACb,IAAI,MAAyB,CAAA;QAC7B,GAAG;YACC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;gBACxC,OAAO,IAAI,CAAA;aACd;SACJ,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAC;QACtB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,SAAgC;QACjC,IAAI,MAAyB,CAAA;QAC7B,GAAG;YACC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACzC,OAAO,IAAI,CAAA;aACd;SACJ,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAC;QACtB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAgC;QAClC,IAAI,MAAyB,CAAA;QAC7B,GAAG;YACC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC1C,OAAO,KAAK,CAAA;aACf;SACJ,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAC;QACtB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,QAA2B;QAC/B,IAAI,MAAyB,CAAA;QAC7B,OAAO,IAAI,EAAE;YACT,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,MAAM,CAAC,IAAI,EAAE;gBACb,MAAK;aACR;YACD,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;SACzB;IACL,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,SAAS,GAAG,GAAG;QAChB,IAAI,MAAM,GAAG,EAAE,CAAA;QACf,IAAI,MAAyB,CAAA;QAC7B,OAAO,IAAI,EAAE;YACT,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3B,IAAI,MAAM,CAAC,IAAI,EAAE;gBACb,MAAK;aACR;YACD,MAAM,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,CAAA;SACrC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IAC1C,CAAC;IAED;;;OAGG;IACH,OAAO;QACH,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK;QACD,MAAM,GAAG,GAAG,IAAI,GAAG,EAAK,CAAA;QACxB,OAAO,IAAI,EAAE;YACT,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;YACnC,IAAI,IAAI,EAAE;gBACN,OAAO,GAAG,CAAA;aACb;YACD,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;SACjB;IACL,CAAC;IAED;;;OAGG;IACH,KAAK;QACD,OAAO,IAAI,GAAG,CAAO,IAAI,CAAC,CAAA;IAC9B,CAAC;CACJ;AAnPD,sDAmPC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAI,UAAqC;IAC5D,OAAO,IAAI,qBAAqB,CAAC,kBAAU,CAAC,UAAU,CAAC,CAAC,CAAA;AAC5D,CAAC;AAFD,0BAEC;AAED;;GAEG;AACH,SAAgB,GAAG,CAAO,CAA4B,EAAE,CAA4B;IAChF,OAAO,IAAI,qBAAqB,CAAC,IAAI,iBAAW,CAAC,kBAAU,CAAC,CAAC,CAAC,EAAE,kBAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACnF,CAAC;AAFD,kBAEC;AAED,kBAAe,OAAO,CAAA"}