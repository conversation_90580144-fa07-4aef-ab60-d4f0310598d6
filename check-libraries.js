/**
 * WS Transfir Libraries Checker
 * فحص المكتبات المثبتة لنظام WS Transfir
 */

const fs = require('fs');
const path = require('path');

console.log('📦 فحص المكتبات المثبتة لنظام WS Transfir');
console.log('==========================================');
console.log('');

// Required libraries categorized
const requiredLibraries = {
  critical: {
    name: 'المكتبات الحرجة',
    backend: ['express', 'cors'],
    frontend: ['next', 'react', 'react-dom'],
  },
  security: {
    name: 'مكتبات الأمان',
    backend: ['helmet', 'express-rate-limit', 'dotenv', 'joi', 'bcryptjs', 'jsonwebtoken', 'uuid', 'morgan'],
    frontend: ['crypto-js', 'js-cookie'],
  },
  dataManagement: {
    name: 'إدارة البيانات',
    backend: ['axios', 'lodash', 'moment', 'multer'],
    frontend: ['swr', 'zustand'],
  },
  ui: {
    name: 'واجهة المستخدم',
    backend: [],
    frontend: [
      'typescript', 'tailwindcss', '@headlessui/react', '@heroicons/react',
      'framer-motion', 'react-hot-toast', 'react-loading-skeleton',
      'react-hook-form', '@hookform/resolvers', 'zod', 'react-select', 'react-datepicker',
      'chart.js', 'react-chartjs-2', 'react-qr-code', 'clsx'
    ],
  },
  development: {
    name: 'أدوات التطوير',
    backend: [
      '@types/node', '@types/express', '@types/cors', 'concurrently',
      'eslint', 'prettier', 'typescript', 'jest', 'supertest', 'rimraf', 'nodemon'
    ],
    frontend: [
      '@types/react', '@types/react-dom', '@types/node', 'eslint-config-next',
      'prettier', '@testing-library/react', '@testing-library/jest-dom'
    ],
  },
};

// Function to check if package.json exists and read it
function readPackageJson(packagePath) {
  try {
    if (fs.existsSync(packagePath)) {
      const content = fs.readFileSync(packagePath, 'utf8');
      return JSON.parse(content);
    }
    return null;
  } catch (error) {
    console.error(`خطأ في قراءة ${packagePath}:`, error.message);
    return null;
  }
}

// Function to check if a library is installed
function isLibraryInstalled(libraryName, dependencies, devDependencies) {
  return !!(dependencies[libraryName] || devDependencies[libraryName]);
}

// Function to get library version
function getLibraryVersion(libraryName, dependencies, devDependencies) {
  return dependencies[libraryName] || devDependencies[libraryName] || 'غير مثبت';
}

// Function to check libraries for a specific environment
function checkLibraries(environment, packageJson, requiredLibs) {
  if (!packageJson) {
    console.log(`❌ ملف package.json غير موجود في ${environment}`);
    return { installed: 0, total: 0, missing: [] };
  }

  const dependencies = packageJson.dependencies || {};
  const devDependencies = packageJson.devDependencies || {};
  
  let installedCount = 0;
  let totalCount = 0;
  const missingLibraries = [];

  Object.keys(requiredLibraries).forEach(category => {
    const categoryLibs = requiredLibs[category] || [];
    const categoryName = requiredLibraries[category].name;
    
    if (categoryLibs.length > 0) {
      console.log(`\n📋 ${categoryName} (${environment}):`);
      console.log('='.repeat(40));
      
      categoryLibs.forEach(lib => {
        totalCount++;
        const isInstalled = isLibraryInstalled(lib, dependencies, devDependencies);
        const version = getLibraryVersion(lib, dependencies, devDependencies);
        
        if (isInstalled) {
          installedCount++;
          console.log(`✅ ${lib} - ${version}`);
        } else {
          console.log(`❌ ${lib} - غير مثبت`);
          missingLibraries.push(lib);
        }
      });
    }
  });

  return { installed: installedCount, total: totalCount, missing: missingLibraries };
}

// Main function
function main() {
  console.log('📅 التاريخ:', new Date().toLocaleDateString('ar-SA'));
  console.log('⏰ الوقت:', new Date().toLocaleTimeString('ar-SA'));
  console.log('');

  // Check backend libraries
  console.log('🔧 فحص مكتبات Backend:');
  console.log('========================');
  
  const backendPackageJson = readPackageJson('package.json');
  const backendLibs = {
    critical: requiredLibraries.critical.backend,
    security: requiredLibraries.security.backend,
    dataManagement: requiredLibraries.dataManagement.backend,
    ui: requiredLibraries.ui.backend,
    development: requiredLibraries.development.backend,
  };
  
  const backendResults = checkLibraries('Backend', backendPackageJson, backendLibs);

  // Check frontend libraries
  console.log('\n\n🎨 فحص مكتبات Frontend:');
  console.log('=========================');
  
  const frontendPackageJson = readPackageJson('frontend/web-app/package.json');
  const frontendLibs = {
    critical: requiredLibraries.critical.frontend,
    security: requiredLibraries.security.frontend,
    dataManagement: requiredLibraries.dataManagement.frontend,
    ui: requiredLibraries.ui.frontend,
    development: requiredLibraries.development.frontend,
  };
  
  const frontendResults = checkLibraries('Frontend', frontendPackageJson, frontendLibs);

  // Summary
  console.log('\n\n📊 ملخص النتائج:');
  console.log('=================');
  
  const totalInstalled = backendResults.installed + frontendResults.installed;
  const totalRequired = backendResults.total + frontendResults.total;
  const installationPercentage = Math.round((totalInstalled / totalRequired) * 100);
  
  console.log(`🔧 Backend: ${backendResults.installed}/${backendResults.total} مثبت`);
  console.log(`🎨 Frontend: ${frontendResults.installed}/${frontendResults.total} مثبت`);
  console.log(`📦 المجموع: ${totalInstalled}/${totalRequired} مثبت`);
  console.log(`📈 نسبة التثبيت: ${installationPercentage}%`);
  
  // Status assessment
  console.log('\n🎯 تقييم الحالة:');
  console.log('================');
  
  if (installationPercentage >= 90) {
    console.log('🟢 ممتاز! جميع المكتبات الضرورية مثبتة');
    console.log('✅ النظام جاهز للتشغيل الكامل');
  } else if (installationPercentage >= 70) {
    console.log('🟡 جيد! معظم المكتبات مثبتة');
    console.log('⚠️ بعض المكتبات مفقودة لكن النظام قابل للتشغيل');
  } else if (installationPercentage >= 50) {
    console.log('🟠 متوسط! المكتبات الأساسية مثبتة');
    console.log('🔧 يُنصح بتثبيت المكتبات المفقودة');
  } else {
    console.log('🔴 ضعيف! العديد من المكتبات مفقودة');
    console.log('❌ النظام قد لا يعمل بشكل صحيح');
  }

  // Missing libraries
  const allMissing = [...backendResults.missing, ...frontendResults.missing];
  if (allMissing.length > 0) {
    console.log('\n❌ المكتبات المفقودة:');
    console.log('=====================');
    allMissing.forEach(lib => {
      console.log(`   - ${lib}`);
    });
    
    console.log('\n🔧 لتثبيت المكتبات المفقودة:');
    console.log('==============================');
    console.log('npm install ' + backendResults.missing.join(' '));
    if (frontendResults.missing.length > 0) {
      console.log('cd frontend/web-app && npm install ' + frontendResults.missing.join(' '));
    }
  }

  // Recommendations
  console.log('\n💡 التوصيات:');
  console.log('=============');
  
  if (installationPercentage >= 90) {
    console.log('🚀 يمكنك تشغيل النظام الآن:');
    console.log('   node api-server.js');
    console.log('   node start-simple.js');
  } else if (installationPercentage >= 50) {
    console.log('🔧 لتثبيت جميع المكتبات:');
    console.log('   .\\install-libraries.bat');
    console.log('   أو');
    console.log('   npm run setup');
  } else {
    console.log('📦 يُنصح بتشغيل التثبيت الشامل:');
    console.log('   .\\install-libraries.bat');
  }

  console.log('\n🔄 لإعادة فحص المكتبات: node check-libraries.js');
  console.log('');
}

// Run the check
main();
