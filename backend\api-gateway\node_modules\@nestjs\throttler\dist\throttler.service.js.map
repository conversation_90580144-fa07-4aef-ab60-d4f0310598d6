{"version": 3, "file": "throttler.service.js", "sourceRoot": "", "sources": ["../src/throttler.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAmE;AAS5D,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAA7B;QACG,aAAQ,GAA4C,EAAE,CAAC;QACvD,eAAU,GAAqB,EAAE,CAAC;IAmD5C,CAAC;IAjDC,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAKO,iBAAiB,CAAC,GAAW;QACnC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IACvE,CAAC;IAKO,iBAAiB,CAAC,GAAW,EAAE,eAAuB;QAC5D,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;YAC9B,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,SAAS,CAAC,CAAC;QACpE,CAAC,EAAE,eAAe,CAAC,CAAC;QACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAW,EAAE,GAAW;QACtC,MAAM,eAAe,GAAG,GAAG,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;QAChF,CAAC;QAED,IAAI,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAG/C,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,eAAe,CAAC;YAC3D,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;QAC9B,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAE7C,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS;YACtC,YAAY;SACb,CAAC;IACJ,CAAC;IAED,qBAAqB;QACnB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IACxC,CAAC;CACF,CAAA;AArDY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;GACA,uBAAuB,CAqDnC"}