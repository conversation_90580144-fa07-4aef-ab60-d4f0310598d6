/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { TaskExecutor } from '../../src';
import { RepositoryInitializerTaskFactoryOptions, RepositoryInitializerTaskOptions } from './options';
export default function (factoryOptions?: RepositoryInitializerTaskFactoryOptions): TaskExecutor<RepositoryInitializerTaskOptions>;
