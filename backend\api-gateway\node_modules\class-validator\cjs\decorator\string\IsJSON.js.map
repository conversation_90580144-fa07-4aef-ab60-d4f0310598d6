{"version": 3, "file": "IsJSON.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsJSON.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAEtC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAFD,wBAEC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,iBAAqC;IAC1D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YACjD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,iCAAiC,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,wBAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isJSONValidator from 'validator/lib/isJSON';\n\nexport const IS_JSON = 'isJson';\n\n/**\n * Checks if the string is valid JSON (note: uses JSON.parse).\n * If given value is not a string, then it returns false.\n */\nexport function isJSON(value: unknown): boolean {\n  return typeof value === 'string' && isJSONValidator(value);\n}\n\n/**\n * Checks if the string is valid JSON (note: uses JSON.parse).\n * If given value is not a string, then it returns false.\n */\nexport function IsJSON(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_JSON,\n      validator: {\n        validate: (value, args): boolean => isJSON(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a json string', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}