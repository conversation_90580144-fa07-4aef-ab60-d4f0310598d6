{"version": 3, "file": "extractNationalNumberFromPossiblyIncompleteNumber.js", "names": ["extractNationalNumberFromPossiblyIncompleteNumber", "number", "metadata", "numberingPlan", "nationalPrefixForParsing", "prefixPattern", "RegExp", "prefixMatch", "exec", "nationalNumber", "carrierCode", "capturedGroupsCount", "length", "hasCapturedGroups", "nationalPrefixTransformRule", "replace", "prefixBeforeNationalNumber", "slice", "nationalPrefix", "possiblePositionOfTheFirstCapturedGroup", "indexOf", "possibleNationalPrefix"], "sources": ["../../source/helpers/extractNationalNumberFromPossiblyIncompleteNumber.js"], "sourcesContent": ["/**\r\n * Strips any national prefix (such as 0, 1) present in a\r\n * (possibly incomplete) number provided.\r\n * \"Carrier codes\" are only used  in Colombia and Brazil,\r\n * and only when dialing within those countries from a mobile phone to a fixed line number.\r\n * Sometimes it won't actually strip national prefix\r\n * and will instead prepend some digits to the `number`:\r\n * for example, when number `2345678` is passed with `VI` country selected,\r\n * it will return `{ number: \"3402345678\" }`, because `340` area code is prepended.\r\n * @param {string} number — National number digits.\r\n * @param {object} metadata — Metadata with country selected.\r\n * @return {object} `{ nationalNumber: string, nationalPrefix: string? carrierCode: string? }`. Even if a national prefix was extracted, it's not necessarily present in the returned object, so don't rely on its presence in the returned object in order to find out whether a national prefix has been extracted or not.\r\n */\r\nexport default function extractNationalNumberFromPossiblyIncompleteNumber(number, metadata) {\r\n\tif (number && metadata.numberingPlan.nationalPrefixForParsing()) {\r\n\t\t// See METADATA.md for the description of\r\n\t\t// `national_prefix_for_parsing` and `national_prefix_transform_rule`.\r\n\t\t// Attempt to parse the first digits as a national prefix.\r\n\t\tconst prefixPattern = new RegExp('^(?:' + metadata.numberingPlan.nationalPrefixForParsing() + ')')\r\n\t\tconst prefixMatch = prefixPattern.exec(number)\r\n\t\tif (prefixMatch) {\r\n\t\t\tlet nationalNumber\r\n\t\t\tlet carrierCode\r\n\t\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\r\n\t\t\t// If a `national_prefix_for_parsing` has any \"capturing groups\"\r\n\t\t\t// then it means that the national (significant) number is equal to\r\n\t\t\t// those \"capturing groups\" transformed via `national_prefix_transform_rule`,\r\n\t\t\t// and nothing could be said about the actual national prefix:\r\n\t\t\t// what is it and was it even there.\r\n\t\t\t// If a `national_prefix_for_parsing` doesn't have any \"capturing groups\",\r\n\t\t\t// then everything it matches is a national prefix.\r\n\t\t\t// To determine whether `national_prefix_for_parsing` matched any\r\n\t\t\t// \"capturing groups\", the value of the result of calling `.exec()`\r\n\t\t\t// is looked at, and if it has non-undefined values where there're\r\n\t\t\t// \"capturing groups\" in the regular expression, then it means\r\n\t\t\t// that \"capturing groups\" have been matched.\r\n\t\t\t// It's not possible to tell whether there'll be any \"capturing gropus\"\r\n\t\t\t// before the matching process, because a `national_prefix_for_parsing`\r\n\t\t\t// could exhibit both behaviors.\r\n\t\t\tconst capturedGroupsCount = prefixMatch.length - 1\r\n\t\t\tconst hasCapturedGroups = capturedGroupsCount > 0 && prefixMatch[capturedGroupsCount]\r\n\t\t\tif (metadata.nationalPrefixTransformRule() && hasCapturedGroups) {\r\n\t\t\t\tnationalNumber = number.replace(\r\n\t\t\t\t\tprefixPattern,\r\n\t\t\t\t\tmetadata.nationalPrefixTransformRule()\r\n\t\t\t\t)\r\n\t\t\t\t// If there's more than one captured group,\r\n\t\t\t\t// then carrier code is the second one.\r\n\t\t\t\tif (capturedGroupsCount > 1) {\r\n\t\t\t\t\tcarrierCode = prefixMatch[1]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// If there're no \"capturing groups\",\r\n\t\t\t// or if there're \"capturing groups\" but no\r\n\t\t\t// `national_prefix_transform_rule`,\r\n\t\t\t// then just strip the national prefix from the number,\r\n\t\t\t// and possibly a carrier code.\r\n\t\t\t// Seems like there could be more.\r\n\t\t\telse {\r\n\t\t\t\t// `prefixBeforeNationalNumber` is the whole substring matched by\r\n\t\t\t\t// the `national_prefix_for_parsing` regular expression.\r\n\t\t\t\t// There seem to be no guarantees that it's just a national prefix.\r\n\t\t\t\t// For example, if there's a carrier code, it's gonna be a\r\n\t\t\t\t// part of `prefixBeforeNationalNumber` too.\r\n\t\t\t\tconst prefixBeforeNationalNumber = prefixMatch[0]\r\n\t\t\t\tnationalNumber = number.slice(prefixBeforeNationalNumber.length)\r\n\t\t\t\t// If there's at least one captured group,\r\n\t\t\t\t// then carrier code is the first one.\r\n\t\t\t\tif (hasCapturedGroups) {\r\n\t\t\t\t\tcarrierCode = prefixMatch[1]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// Tries to guess whether a national prefix was present in the input.\r\n\t\t\t// This is not something copy-pasted from Google's library:\r\n\t\t\t// they don't seem to have an equivalent for that.\r\n\t\t\t// So this isn't an \"officially approved\" way of doing something like that.\r\n\t\t\t// But since there seems no other existing method, this library uses it.\r\n\t\t\tlet nationalPrefix\r\n\t\t\tif (hasCapturedGroups) {\r\n\t\t\t\tconst possiblePositionOfTheFirstCapturedGroup = number.indexOf(prefixMatch[1])\r\n\t\t\t\tconst possibleNationalPrefix = number.slice(0, possiblePositionOfTheFirstCapturedGroup)\r\n\t\t\t\t// Example: an Argentinian (AR) phone number `0111523456789`.\r\n\t\t\t\t// `prefixMatch[0]` is `01115`, and `$1` is `11`,\r\n\t\t\t\t// and the rest of the phone number is `23456789`.\r\n\t\t\t\t// The national number is transformed via `9$1` to `91123456789`.\r\n\t\t\t\t// National prefix `0` is detected being present at the start.\r\n\t\t\t\t// if (possibleNationalPrefix.indexOf(metadata.numberingPlan.nationalPrefix()) === 0) {\r\n\t\t\t\tif (possibleNationalPrefix === metadata.numberingPlan.nationalPrefix()) {\r\n\t\t\t\t\tnationalPrefix = metadata.numberingPlan.nationalPrefix()\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tnationalPrefix = prefixMatch[0]\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tnationalNumber,\r\n\t\t\t\tnationalPrefix,\r\n\t\t\t\tcarrierCode\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n   return {\r\n   \tnationalNumber: number\r\n   }\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,iDAAT,CAA2DC,MAA3D,EAAmEC,QAAnE,EAA6E;EAC3F,IAAID,MAAM,IAAIC,QAAQ,CAACC,aAAT,CAAuBC,wBAAvB,EAAd,EAAiE;IAChE;IACA;IACA;IACA,IAAMC,aAAa,GAAG,IAAIC,MAAJ,CAAW,SAASJ,QAAQ,CAACC,aAAT,CAAuBC,wBAAvB,EAAT,GAA6D,GAAxE,CAAtB;IACA,IAAMG,WAAW,GAAGF,aAAa,CAACG,IAAd,CAAmBP,MAAnB,CAApB;;IACA,IAAIM,WAAJ,EAAiB;MAChB,IAAIE,cAAJ;MACA,IAAIC,WAAJ,CAFgB,CAGhB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAMC,mBAAmB,GAAGJ,WAAW,CAACK,MAAZ,GAAqB,CAAjD;MACA,IAAMC,iBAAiB,GAAGF,mBAAmB,GAAG,CAAtB,IAA2BJ,WAAW,CAACI,mBAAD,CAAhE;;MACA,IAAIT,QAAQ,CAACY,2BAAT,MAA0CD,iBAA9C,EAAiE;QAChEJ,cAAc,GAAGR,MAAM,CAACc,OAAP,CAChBV,aADgB,EAEhBH,QAAQ,CAACY,2BAAT,EAFgB,CAAjB,CADgE,CAKhE;QACA;;QACA,IAAIH,mBAAmB,GAAG,CAA1B,EAA6B;UAC5BD,WAAW,GAAGH,WAAW,CAAC,CAAD,CAAzB;QACA;MACD,CAVD,CAWA;MACA;MACA;MACA;MACA;MACA;MAhBA,KAiBK;QACJ;QACA;QACA;QACA;QACA;QACA,IAAMS,0BAA0B,GAAGT,WAAW,CAAC,CAAD,CAA9C;QACAE,cAAc,GAAGR,MAAM,CAACgB,KAAP,CAAaD,0BAA0B,CAACJ,MAAxC,CAAjB,CAPI,CAQJ;QACA;;QACA,IAAIC,iBAAJ,EAAuB;UACtBH,WAAW,GAAGH,WAAW,CAAC,CAAD,CAAzB;QACA;MACD,CAnDe,CAoDhB;MACA;MACA;MACA;MACA;;;MACA,IAAIW,cAAJ;;MACA,IAAIL,iBAAJ,EAAuB;QACtB,IAAMM,uCAAuC,GAAGlB,MAAM,CAACmB,OAAP,CAAeb,WAAW,CAAC,CAAD,CAA1B,CAAhD;QACA,IAAMc,sBAAsB,GAAGpB,MAAM,CAACgB,KAAP,CAAa,CAAb,EAAgBE,uCAAhB,CAA/B,CAFsB,CAGtB;QACA;QACA;QACA;QACA;QACA;;QACA,IAAIE,sBAAsB,KAAKnB,QAAQ,CAACC,aAAT,CAAuBe,cAAvB,EAA/B,EAAwE;UACvEA,cAAc,GAAGhB,QAAQ,CAACC,aAAT,CAAuBe,cAAvB,EAAjB;QACA;MACD,CAZD,MAYO;QACNA,cAAc,GAAGX,WAAW,CAAC,CAAD,CAA5B;MACA;;MACD,OAAO;QACNE,cAAc,EAAdA,cADM;QAENS,cAAc,EAAdA,cAFM;QAGNR,WAAW,EAAXA;MAHM,CAAP;IAKA;EACD;;EACC,OAAO;IACND,cAAc,EAAER;EADV,CAAP;AAGF"}