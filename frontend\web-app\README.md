# 🌟 WS Transfir - تطبيق الويب

تطبيق ويب متقدم لنظام تحويل الأموال WS Transfir مبني بأحدث التقنيات والمعايير العالمية.

## 🚀 الميزات الرئيسية

### 🔐 **نظام المصادقة المتقدم**
- تسجيل دخول آمن مع المصادقة الثنائية (2FA)
- إدارة الجلسات والرموز المميزة
- حماية متقدمة ضد الهجمات السيبرانية
- تشفير البيانات الحساسة

### 💸 **خدمات التحويلات**
- إرسال واستلام الحوالات المحلية والدولية
- تتبع الحوالات في الوقت الفعلي
- حساب الرسوم وأسعار الصرف تلقائياً
- دعم العملات المتعددة

### 💳 **المحفظة الرقمية**
- إدارة الأرصدة بعملات متعددة
- شحن وسحب الأموال
- دفع الفواتير والخدمات
- سجل شامل للمعاملات

### 📊 **التقارير والتحليلات**
- لوحة تحكم تفاعلية مع الرسوم البيانية
- تقارير مالية مفصلة
- إحصائيات الاستخدام والأداء
- تصدير البيانات بصيغ متعددة

### 🌐 **التدويل والإمكانية**
- دعم اللغتين العربية والإنجليزية
- واجهة متجاوبة لجميع الأجهزة
- دعم RTL/LTR
- إمكانية الوصول (Accessibility)

## 🛠️ التقنيات المستخدمة

### **Frontend Framework**
- **Next.js 14** - إطار عمل React متقدم
- **TypeScript** - للأمان والجودة في الكود
- **React 18** - مكتبة واجهة المستخدم

### **التصميم والواجهة**
- **Tailwind CSS** - إطار عمل CSS متقدم
- **Headless UI** - مكونات واجهة المستخدم
- **Heroicons** - مجموعة أيقونات احترافية
- **Framer Motion** - حركات وتأثيرات متقدمة

### **إدارة الحالة والبيانات**
- **React Query** - إدارة البيانات والتخزين المؤقت
- **React Hook Form** - إدارة النماذج
- **Zustand** - إدارة الحالة العامة
- **SWR** - جلب البيانات

### **الأمان والتشفير**
- **Crypto-JS** - تشفير البيانات
- **Zod** - التحقق من صحة البيانات
- **Axios** - طلبات HTTP آمنة

### **الاتصال المباشر**
- **Socket.IO Client** - الاتصال المباشر
- **React Hot Toast** - الإشعارات

### **الرسوم البيانية**
- **Chart.js** - مكتبة الرسوم البيانية
- **React Chart.js 2** - تكامل مع React

## 📁 هيكل المشروع

```
frontend/web-app/
├── app/                    # صفحات التطبيق (App Router)
│   ├── auth/              # صفحات المصادقة
│   ├── dashboard/         # لوحة التحكم
│   ├── globals.css        # الأنماط العامة
│   ├── layout.tsx         # التخطيط الرئيسي
│   ├── page.tsx          # الصفحة الرئيسية
│   └── providers.tsx     # مزودي السياق
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── ui/               # مكونات واجهة المستخدم
│   ├── dashboard/        # مكونات لوحة التحكم
│   └── layout/           # مكونات التخطيط
├── contexts/             # سياقات React
│   ├── AuthContext.tsx   # سياق المصادقة
│   ├── LanguageContext.tsx # سياق اللغة
│   ├── NotificationContext.tsx # سياق الإشعارات
│   └── SocketContext.tsx # سياق WebSocket
├── services/             # خدمات API
│   └── api/              # عملاء API
├── utils/                # الأدوات المساعدة
│   └── storage.ts        # إدارة التخزين
├── types/                # تعريفات TypeScript
├── hooks/                # خطافات React مخصصة
├── lib/                  # مكتبات مساعدة
└── public/               # الملفات العامة
```

## 🚀 التشغيل والتطوير

### **المتطلبات**
- Node.js 18.0.0 أو أحدث
- npm 9.0.0 أو أحدث

### **التثبيت**
```bash
# استنساخ المشروع
git clone <repository-url>
cd frontend/web-app

# تثبيت المتطلبات
npm install
```

### **متغيرات البيئة**
إنشاء ملف `.env.local`:
```env
# عنوان API الخلفي
NEXT_PUBLIC_API_URL=http://localhost:3000

# عنوان WebSocket
NEXT_PUBLIC_WS_URL=ws://localhost:3000

# عنوان التطبيق
NEXT_PUBLIC_APP_URL=http://localhost:3100

# مفتاح التشفير
NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key

# Google Site Verification
GOOGLE_SITE_VERIFICATION=your-verification-code
```

### **أوامر التشغيل**
```bash
# تشغيل التطوير
npm run dev

# بناء الإنتاج
npm run build

# تشغيل الإنتاج
npm run start

# فحص الكود
npm run lint

# فحص الأنواع
npm run type-check

# تشغيل الاختبارات
npm run test

# تشغيل Storybook
npm run storybook
```

## 🔧 الإعدادات والتخصيص

### **السمات (Themes)**
- دعم الوضع النهاري والليلي
- تخصيص الألوان والخطوط
- حفظ تفضيلات المستخدم

### **اللغات**
- العربية (افتراضي)
- الإنجليزية
- إمكانية إضافة لغات جديدة

### **التخزين المحلي**
- تشفير البيانات الحساسة
- إدارة الجلسات
- حفظ التفضيلات

## 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
npm run test

# تشغيل الاختبارات مع المراقبة
npm run test:watch

# تقرير التغطية
npm run test:coverage
```

## 📱 التوافق

### **المتصفحات المدعومة**
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### **الأجهزة**
- أجهزة سطح المكتب
- الأجهزة اللوحية
- الهواتف الذكية

## 🔒 الأمان

- تشفير البيانات الحساسة
- حماية CSRF
- التحقق من صحة البيانات
- إدارة آمنة للجلسات
- حماية XSS

## 📈 الأداء

- تحسين الصور والأصول
- تقسيم الكود (Code Splitting)
- التحميل الكسول (Lazy Loading)
- التخزين المؤقت الذكي
- ضغط البيانات

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://wstransfir.com
- **التوثيق**: https://docs.wstransfir.com

---

<div align="center">
  <strong>صُنع بـ ❤️ من فريق WS Transfir</strong>
</div>
