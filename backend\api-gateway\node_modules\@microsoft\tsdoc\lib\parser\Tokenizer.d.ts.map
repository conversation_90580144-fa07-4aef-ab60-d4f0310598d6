{"version": 3, "file": "Tokenizer.d.ts", "sourceRoot": "", "sources": ["../../src/parser/Tokenizer.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAE3C,qBAAa,SAAS;IACpB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,gCAAgC,CAA+C;IACvG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAC6B;IAEpE,OAAO,CAAC,MAAM,CAAC,YAAY,CAAgD;IAC3E,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAmC;IAEpE;;;OAGG;WACW,UAAU,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,KAAK,EAAE;IAuBrD;;;OAGG;WACW,aAAa,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO;IAK1D,OAAO,CAAC,MAAM,CAAC,kBAAkB;IA+CjC;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,sBAAsB;IAUrC,OAAO,CAAC,MAAM,CAAC,kBAAkB;CA0DlC"}