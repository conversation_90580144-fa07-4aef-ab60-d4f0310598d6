import { Injectable, Inject, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Redis } from 'ioredis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  constructor(@Inject('REDIS_CLIENT') private readonly redis: Redis) {}

  async onModuleInit() {
    try {
      await this.redis.connect();
      console.log('✅ تم الاتصال بـ Redis بنجاح');
    } catch (error) {
      console.error('❌ فشل الاتصال بـ Redis:', error);
    }
  }

  async onModuleDestroy() {
    await this.redis.disconnect();
  }

  /**
   * حفظ قيمة مع انتهاء صلاحية
   */
  async setex(key: string, seconds: number, value: string): Promise<void> {
    await this.redis.setex(key, seconds, value);
  }

  /**
   * حفظ قيمة
   */
  async set(key: string, value: string): Promise<void> {
    await this.redis.set(key, value);
  }

  /**
   * الحصول على قيمة
   */
  async get(key: string): Promise<string | null> {
    return await this.redis.get(key);
  }

  /**
   * حذف مفتاح
   */
  async del(key: string): Promise<number> {
    return await this.redis.del(key);
  }

  /**
   * التحقق من وجود مفتاح
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  /**
   * زيادة قيمة رقمية
   */
  async incr(key: string): Promise<number> {
    return await this.redis.incr(key);
  }

  /**
   * تعيين انتهاء صلاحية لمفتاح موجود
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    const result = await this.redis.expire(key, seconds);
    return result === 1;
  }

  /**
   * الحصول على الوقت المتبقي لانتهاء الصلاحية
   */
  async ttl(key: string): Promise<number> {
    return await this.redis.ttl(key);
  }

  /**
   * حفظ hash
   */
  async hset(key: string, field: string, value: string): Promise<void> {
    await this.redis.hset(key, field, value);
  }

  /**
   * الحصول على قيمة من hash
   */
  async hget(key: string, field: string): Promise<string | null> {
    return await this.redis.hget(key, field);
  }

  /**
   * حذف حقل من hash
   */
  async hdel(key: string, field: string): Promise<number> {
    return await this.redis.hdel(key, field);
  }

  /**
   * الحصول على جميع حقول hash
   */
  async hgetall(key: string): Promise<Record<string, string>> {
    return await this.redis.hgetall(key);
  }

  /**
   * إضافة عنصر إلى set
   */
  async sadd(key: string, member: string): Promise<number> {
    return await this.redis.sadd(key, member);
  }

  /**
   * التحقق من وجود عنصر في set
   */
  async sismember(key: string, member: string): Promise<boolean> {
    const result = await this.redis.sismember(key, member);
    return result === 1;
  }

  /**
   * حذف عنصر من set
   */
  async srem(key: string, member: string): Promise<number> {
    return await this.redis.srem(key, member);
  }

  /**
   * الحصول على جميع عناصر set
   */
  async smembers(key: string): Promise<string[]> {
    return await this.redis.smembers(key);
  }

  /**
   * البحث عن مفاتيح بنمط معين
   */
  async keys(pattern: string): Promise<string[]> {
    return await this.redis.keys(pattern);
  }

  /**
   * تنفيذ عدة أوامر في معاملة واحدة
   */
  async multi(commands: Array<[string, ...any[]]>): Promise<any[]> {
    const pipeline = this.redis.multi();
    
    commands.forEach(([command, ...args]) => {
      (pipeline as any)[command](...args);
    });
    
    const results = await pipeline.exec();
    return results?.map(([err, result]) => {
      if (err) throw err;
      return result;
    }) || [];
  }

  /**
   * الحصول على معلومات الخادم
   */
  async info(): Promise<string> {
    return await this.redis.info();
  }

  /**
   * مسح قاعدة البيانات (للاختبار فقط)
   */
  async flushdb(): Promise<void> {
    if (process.env.NODE_ENV !== 'production') {
      await this.redis.flushdb();
    }
  }
}
