@echo off

echo Starting WS Transfir System...
echo ===============================

:: Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not installed
    echo Please install Node.js from: https://nodejs.org
    pause
    exit /b 1
)

echo Node.js is available

:: Create simple API server
echo Creating API server...
echo const express = require('express'); > api.js
echo const cors = require('cors'); >> api.js
echo const app = express(); >> api.js
echo app.use(cors()); >> api.js
echo app.use(express.json()); >> api.js
echo app.get('/api/health', (req, res) =^> res.json({status: 'OK', message: 'WS Transfir API Running'})); >> api.js
echo app.post('/api/auth/login', (req, res) =^> { >> api.js
echo   const {email, password} = req.body; >> api.js
echo   if (email === '<EMAIL>' ^&^& password === 'admin123') { >> api.js
echo     res.json({success: true, token: 'admin-token', user: {firstName: 'Admin', lastName: 'User', email, role: 'admin'}}); >> api.js
echo   } else if (email ^&^& password) { >> api.js
echo     res.json({success: true, token: 'user-token', user: {firstName: 'Ahmed', lastName: 'Mohamed', email, role: 'user'}}); >> api.js
echo   } else { >> api.js
echo     res.status(401).json({success: false, message: 'Invalid credentials'}); >> api.js
echo   } >> api.js
echo }); >> api.js
echo app.get('/api/profile/me', (req, res) =^> res.json({id: '1', firstName: 'Ahmed', lastName: 'Mohamed', email: '<EMAIL>', phone: '+966501234567', isVerified: true})); >> api.js
echo app.get('/api/transfers', (req, res) =^> res.json({data: [{id: '1', referenceNumber: 'WS001', amount: '1500', currency: 'SAR', receiverName: 'Ahmed Mohamed', status: 'completed'}], total: 1})); >> api.js
echo app.listen(3000, () =^> console.log('API Server running on http://localhost:3000')); >> api.js

echo API server created

:: Install express if needed
if not exist "node_modules" (
    echo Installing dependencies...
    npm init -y >nul 2>&1
    npm install express cors >nul 2>&1
)

:: Start API server
echo Starting API server...
start "WS Transfir API" cmd /k "node api.js"

:: Wait a bit
timeout /t 3 /nobreak >nul

:: Check if frontend exists and start it
if exist "frontend\web-app" (
    echo Starting frontend...
    cd frontend\web-app
    
    if not exist "node_modules" (
        echo Installing frontend dependencies...
        npm install >nul 2>&1
    )
    
    if not exist "next.config.js" (
        echo const nextConfig = {reactStrictMode: true}; > next.config.js
        echo module.exports = nextConfig; >> next.config.js
    )
    
    start "WS Transfir Frontend" cmd /k "npm run dev"
    cd ..\..
) else (
    echo Frontend directory not found
)

:: Wait for services to start
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

:: Open browser
echo Opening browser...
start "" "http://localhost:3100"
start "" "http://localhost:3000/api/health"

echo.
echo ===============================
echo WS Transfir System Started!
echo ===============================
echo.
echo Frontend: http://localhost:3100
echo API: http://localhost:3000
echo Health: http://localhost:3000/api/health
echo.
echo Login: <EMAIL> / admin123
echo.

pause
