{"version": 3, "file": "NodeParser.d.ts", "sourceRoot": "", "sources": ["../../src/parser/NodeParser.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AA4DrD;;GAEG;AACH,qBAAa,UAAU;IACrB,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAgB;IAC/C,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAqB;IACpD,OAAO,CAAC,eAAe,CAAa;gBAEjB,aAAa,EAAE,aAAa;IAOxC,KAAK,IAAI,IAAI;IA6GpB,OAAO,CAAC,wBAAwB;IAmChC,OAAO,CAAC,sBAAsB;IAqD9B,OAAO,CAAC,yBAAyB;IAYjC,OAAO,CAAC,kBAAkB;IAyE1B,OAAO,CAAC,qBAAqB;IAwB7B;;;;OAIG;IACH,OAAO,CAAC,6BAA6B;IAiDrC;;;OAGG;IACH,OAAO,CAAC,6BAA6B;IA6CrC;;;OAGG;IACH,OAAO,CAAC,8BAA8B;IActC,OAAO,CAAC,gBAAgB;IAoJxB,OAAO,CAAC,SAAS;IAQjB,OAAO,CAAC,qBAAqB;IAyC7B,OAAO,CAAC,cAAc;IAmFtB,OAAO,CAAC,eAAe;IA6LvB,OAAO,CAAC,mBAAmB;IAqC3B,OAAO,CAAC,aAAa;IA0KrB,OAAO,CAAC,2BAA2B;IAkDnC,OAAO,CAAC,4BAA4B;IAepC,OAAO,CAAC,0BAA0B;IAuPlC,OAAO,CAAC,qBAAqB;IAyH7B,OAAO,CAAC,kBAAkB;IA8D1B,OAAO,CAAC,sBAAsB;IAyG9B,OAAO,CAAC,oBAAoB;IAsC5B,OAAO,CAAC,kBAAkB;IA8F1B,OAAO,CAAC,mBAAmB;IAkD3B,OAAO,CAAC,gBAAgB;IA4CxB,OAAO,CAAC,gBAAgB;IAuExB;;OAEG;IACH,OAAO,CAAC,cAAc;IA+DtB,OAAO,CAAC,gBAAgB;IA4OxB,OAAO,CAAC,cAAc;IA8DtB,OAAO,CAAC,0BAA0B;IAgBlC;;OAEG;IACH,OAAO,CAAC,YAAY;IAuBpB;;OAEG;IACH,OAAO,CAAC,wBAAwB;IAUhC;;;OAGG;IACH,OAAO,CAAC,6BAA6B;IA+BrC;;;OAGG;IACH,OAAO,CAAC,kCAAkC;IAyB1C;;;OAGG;IACH,OAAO,CAAC,uCAAuC;IA+B/C;;;OAGG;IACH,OAAO,CAAC,sBAAsB;IAuB9B;;;OAGG;IACH,OAAO,CAAC,4BAA4B;CA4BrC"}