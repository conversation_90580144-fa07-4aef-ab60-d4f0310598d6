{"version": 3, "file": "init-declarations.js", "sourceRoot": "", "sources": ["../../src/rules/init-declarations.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAM1D,kCAAqC;AACrC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,mBAAmB,CAAC,CAAC;AAKxD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,eAAe,EAAE,IAAI;SACtB;QACD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,QAAQ,CAAC;IAC1B,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,OAAO;YACL,0BAA0B,CAAC,IAAkC;gBAC3D,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;wBACjB,OAAO;oBACT,CAAC;oBACD,IAAI,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;wBACtC,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,KAAK,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;SACF,CAAC;QAEF,SAAS,2BAA2B,CAClC,IAAkC;YAElC,IAAI,QAAQ,GAA8B,IAAI,CAAC,MAAM,CAAC;YAEtD,OAAO,QAAQ,EAAE,CAAC;gBAChB,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;oBACpD,QAAQ,CAAC,OAAO,EAChB,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}