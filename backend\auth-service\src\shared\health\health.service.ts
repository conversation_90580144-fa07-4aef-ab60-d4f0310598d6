import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = Date.now();

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
  ) {}

  async check() {
    try {
      const dependencies = await this.checkDependencies();
      const status = this.getOverallStatus(dependencies);

      return {
        status,
        timestamp: new Date().toISOString(),
        service: 'auth-service',
        version: '1.0.0',
        uptime: Math.floor((Date.now() - this.startTime) / 1000),
        environment: this.configService.get('NODE_ENV'),
        dependencies,
        memory: this.getMemoryUsage(),
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'auth-service',
        error: error.message,
      };
    }
  }

  async ready() {
    try {
      const dependencies = await this.checkDependencies();
      const isReady = Object.values(dependencies).every(status => status === 'healthy');

      if (isReady) {
        return {
          status: 'ready',
          timestamp: new Date().toISOString(),
          service: 'auth-service',
        };
      } else {
        return {
          status: 'not ready',
          timestamp: new Date().toISOString(),
          service: 'auth-service',
          dependencies,
        };
      }
    } catch (error) {
      this.logger.error('Readiness check failed:', error);
      throw error;
    }
  }

  async live() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      service: 'auth-service',
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
    };
  }

  private async checkDependencies() {
    const dependencies: Record<string, string> = {};

    // Check database connection
    try {
      await this.dataSource.query('SELECT 1');
      dependencies.database = 'healthy';
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      dependencies.database = 'unhealthy';
    }

    // Check Redis connection (if available)
    try {
      // This would be implemented if Redis service is injected
      dependencies.redis = 'healthy';
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      dependencies.redis = 'unhealthy';
    }

    return dependencies;
  }

  private getOverallStatus(dependencies: Record<string, string>): string {
    const unhealthyServices = Object.values(dependencies).filter(
      status => status !== 'healthy'
    );

    if (unhealthyServices.length === 0) {
      return 'healthy';
    } else if (unhealthyServices.length === Object.keys(dependencies).length) {
      return 'unhealthy';
    } else {
      return 'degraded';
    }
  }

  private getMemoryUsage() {
    const memUsage = process.memoryUsage();
    return {
      rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
    };
  }
}
