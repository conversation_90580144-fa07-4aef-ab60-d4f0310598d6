import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsN<PERSON>ber,
  IsEnum,
  IsOptional,
  Min,
  <PERSON>,
} from 'class-validator';

export enum BillType {
  ELECTRICITY = 'electricity',
  WATER = 'water',
  TELECOM = 'telecom',
  INTERNET = 'internet',
  GAS = 'gas',
  INSURANCE = 'insurance',
  LOAN = 'loan',
  CREDIT_CARD = 'credit_card',
  SUBSCRIPTION = 'subscription',
  GOVERNMENT = 'government',
  EDUCATION = 'education',
  HEALTHCARE = 'healthcare',
  OTHER = 'other',
}

export enum BillProvider {
  SEC = 'sec', // الشركة السعودية للكهرباء
  NWC = 'nwc', // شركة المياه الوطنية
  STC = 'stc', // الاتصالات السعودية
  MOBILY = 'mobily',
  ZAIN = 'zain',
  ARAMCO = 'aramco',
  SABB = 'sabb',
  NCB = 'ncb',
  RAJHI = 'rajhi',
  OTHER = 'other',
}

export class PayBillDto {
  @ApiProperty({
    description: 'نوع الفاتورة',
    enum: BillType,
    example: BillType.ELECTRICITY,
  })
  @IsEnum(BillType, { message: 'نوع الفاتورة غير صحيح' })
  @IsNotEmpty({ message: 'نوع الفاتورة مطلوب' })
  billType: BillType;

  @ApiProperty({
    description: 'مقدم الخدمة',
    enum: BillProvider,
    example: BillProvider.SEC,
  })
  @IsEnum(BillProvider, { message: 'مقدم الخدمة غير صحيح' })
  @IsNotEmpty({ message: 'مقدم الخدمة مطلوب' })
  provider: BillProvider;

  @ApiProperty({
    description: 'رقم الحساب أو رقم العميل',
    example: '**********',
  })
  @IsString({ message: 'رقم الحساب يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رقم الحساب مطلوب' })
  accountNumber: string;

  @ApiProperty({
    description: 'مبلغ الفاتورة',
    example: 350.50,
    minimum: 1,
    maximum: 50000,
  })
  @IsNumber({}, { message: 'مبلغ الفاتورة يجب أن يكون رقم' })
  @IsNotEmpty({ message: 'مبلغ الفاتورة مطلوب' })
  @Min(1, { message: 'الحد الأدنى لمبلغ الفاتورة 1' })
  @Max(50000, { message: 'الحد الأقصى لمبلغ الفاتورة 50,000' })
  amount: number;

  @ApiProperty({
    description: 'العملة',
    example: 'SAR',
  })
  @IsString({ message: 'العملة يجب أن تكون نص' })
  @IsNotEmpty({ message: 'العملة مطلوبة' })
  currency: string;

  @ApiProperty({
    description: 'رقم الفاتورة',
    example: 'BILL-2024-001',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الفاتورة يجب أن يكون نص' })
  billNumber?: string;

  @ApiProperty({
    description: 'تاريخ استحقاق الفاتورة',
    example: '2024-02-15',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'تاريخ الاستحقاق يجب أن يكون نص' })
  dueDate?: string;

  @ApiProperty({
    description: 'اسم صاحب الحساب',
    example: 'أحمد محمد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'اسم صاحب الحساب يجب أن يكون نص' })
  accountHolderName?: string;

  @ApiProperty({
    description: 'ملاحظات إضافية',
    example: 'دفع فاتورة الكهرباء لشهر يناير',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الملاحظات يجب أن تكون نص' })
  notes?: string;

  @ApiProperty({
    description: 'حفظ الفاتورة للدفع المتكرر',
    example: false,
    required: false,
  })
  @IsOptional()
  saveForRecurring?: boolean;

  @ApiProperty({
    description: 'إرسال إشعار بعد الدفع',
    example: true,
    required: false,
  })
  @IsOptional()
  sendNotification?: boolean;
}
