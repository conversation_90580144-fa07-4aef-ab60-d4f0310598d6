{"version": 3, "file": "IsMagnetURI.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMagnetURI.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,oBAAoB,MAAM,2BAA2B,CAAC;AAE7D,MAAM,CAAC,MAAM,aAAa,GAAG,aAAa,CAAC;AAE3C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,iBAAqC;IAC/D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,aAAa;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YACtD,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,qCAAqC,EAChE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isMagnetURIValidator from 'validator/lib/isMagnetURI';\n\nexport const IS_MAGNET_URI = 'isMagnetURI';\n\n/**\n * Check if the string is a magnet uri format.\n * If given value is not a string, then it returns false.\n */\nexport function isMagnetURI(value: unknown): boolean {\n  return typeof value === 'string' && isMagnetURIValidator(value);\n}\n\n/**\n * Check if the string is a magnet uri format.\n * If given value is not a string, then it returns false.\n */\nexport function IsMagnetURI(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_MAGNET_URI,\n      validator: {\n        validate: (value, args): boolean => isMagnetURI(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be magnet uri format',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}