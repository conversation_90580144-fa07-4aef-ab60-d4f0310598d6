# Testing Requirements
# ===================
# متطلبات الاختبارات

# Core Testing Framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.3.1
pytest-html==4.1.1
pytest-json-report==1.5.0

# HTTP Testing
httpx==0.25.2
requests==2.31.0
responses==0.24.1

# Database Testing
pytest-postgresql==5.0.0
pytest-redis==3.0.2
pytest-mongodb==2.4.2

# Async Testing
asynctest==0.13.0
aioresponses==0.7.4

# Mocking and Fixtures
factory-boy==3.3.0
faker==20.1.0
freezegun==1.2.2

# Performance Testing
pytest-benchmark==4.0.0
locust==2.17.0

# Security Testing
bandit==1.7.5
safety==2.3.5

# Code Quality
flake8==6.1.0
black==23.11.0
isort==5.12.0
mypy==1.7.1

# Coverage
coverage==7.3.2
codecov==2.1.13

# Load Testing
artillery==1.7.9

# API Testing
tavern==2.4.1

# Authentication Testing
pyjwt==2.8.0
bcrypt==4.1.1
pyotp==2.9.0

# Database
asyncpg==0.29.0
motor==3.3.2
redis==5.0.1

# Email Testing
aiosmtpd==1.4.4.post2

# File Testing
aiofiles==23.2.1

# Validation Testing
email-validator==2.1.0
pydantic==2.5.0

# Logging Testing
structlog==23.2.0

# Environment Testing
python-dotenv==1.0.0

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
