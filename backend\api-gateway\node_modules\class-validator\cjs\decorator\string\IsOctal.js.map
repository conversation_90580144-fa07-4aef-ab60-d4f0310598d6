{"version": 3, "file": "IsOctal.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsOctal.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oEAAqD;AAExC,QAAA,QAAQ,GAAG,SAAS,CAAC;AAElC;;;GAGG;AACH,SAAgB,OAAO,CAAC,KAAc;IACpC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,iBAAgB,EAAC,KAAK,CAAC,CAAC;AAC9D,CAAC;AAFD,0BAEC;AAED;;;GAGG;AACH,SAAgB,OAAO,CAAC,iBAAqC;IAC3D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gBAAQ;QACd,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAClD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,sCAAsC,EACjE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,0BAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isOctalValidator from 'validator/lib/isOctal';\n\nexport const IS_OCTAL = 'isOctal';\n\n/**\n * Check if the string is a valid octal number.\n * If given value is not a string, then it returns false.\n */\nexport function isOctal(value: unknown): boolean {\n  return typeof value === 'string' && isOctalValidator(value);\n}\n\n/**\n * Check if the string is a valid octal number.\n * If given value is not a string, then it returns false.\n */\nexport function IsOctal(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_OCTAL,\n      validator: {\n        validate: (value, args): boolean => isOctal(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be valid octal number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}