import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum EventCategory {
  USER = 'user',
  TRANSFER = 'transfer',
  PAYMENT = 'payment',
  WALLET = 'wallet',
  NOTIFICATION = 'notification',
  PROFILE = 'profile',
  SECURITY = 'security',
  SYSTEM = 'system',
}

@Entity('analytics_events')
@Index(['eventType', 'timestamp'])
@Index(['userId', 'timestamp'])
@Index(['sessionId', 'timestamp'])
@Index(['category', 'timestamp'])
export class AnalyticsEvent {
  @ApiProperty({ description: 'معرف الحدث' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'نوع الحدث' })
  @Column({ length: 100 })
  @Index()
  eventType: string;

  @ApiProperty({ description: 'فئة الحدث', enum: EventCategory })
  @Column({
    type: 'enum',
    enum: EventCategory,
    default: EventCategory.SYSTEM,
  })
  @Index()
  category: EventCategory;

  @ApiProperty({ description: 'معرف المستخدم', required: false })
  @Column('uuid', { nullable: true })
  @Index()
  userId?: string;

  @ApiProperty({ description: 'معرف الجلسة', required: false })
  @Column({ length: 100, nullable: true })
  @Index()
  sessionId?: string;

  @ApiProperty({ description: 'خصائص الحدث الإضافية', required: false })
  @Column('jsonb', { nullable: true })
  properties?: Record<string, any>;

  @ApiProperty({ description: 'البيانات الوصفية', required: false })
  @Column('jsonb', { nullable: true })
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    country?: string;
    city?: string;
    device?: string;
    browser?: string;
    platform?: string;
    referrer?: string;
    url?: string;
  };

  @ApiProperty({ description: 'قيمة الحدث (للأحداث المالية)', required: false })
  @Column('decimal', { precision: 15, scale: 2, nullable: true })
  value?: string;

  @ApiProperty({ description: 'العملة (للأحداث المالية)', required: false })
  @Column({ length: 3, nullable: true })
  currency?: string;

  @ApiProperty({ description: 'مدة الحدث بالميلي ثانية', required: false })
  @Column('int', { nullable: true })
  duration?: number;

  @ApiProperty({ description: 'هل الحدث ناجح' })
  @Column('boolean', { default: true })
  success: boolean;

  @ApiProperty({ description: 'رسالة الخطأ في حالة الفشل', required: false })
  @Column('text', { nullable: true })
  errorMessage?: string;

  @ApiProperty({ description: 'كود الخطأ في حالة الفشل', required: false })
  @Column({ length: 50, nullable: true })
  errorCode?: string;

  @ApiProperty({ description: 'معرف الكيان المرتبط', required: false })
  @Column('uuid', { nullable: true })
  entityId?: string;

  @ApiProperty({ description: 'نوع الكيان المرتبط', required: false })
  @Column({ length: 50, nullable: true })
  entityType?: string;

  @ApiProperty({ description: 'تصنيفات إضافية', required: false })
  @Column('simple-array', { nullable: true })
  tags?: string[];

  @ApiProperty({ description: 'مستوى الأهمية' })
  @Column({
    type: 'enum',
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium',
  })
  severity: 'low' | 'medium' | 'high' | 'critical';

  @ApiProperty({ description: 'تاريخ ووقت الحدث' })
  @Column('timestamp')
  @Index()
  timestamp: Date;

  @ApiProperty({ description: 'تاريخ الإنشاء' })
  @CreateDateColumn()
  createdAt: Date;

  // Helper methods
  isUserEvent(): boolean {
    return this.category === EventCategory.USER;
  }

  isTransferEvent(): boolean {
    return this.category === EventCategory.TRANSFER;
  }

  isPaymentEvent(): boolean {
    return this.category === EventCategory.PAYMENT;
  }

  isSecurityEvent(): boolean {
    return this.category === EventCategory.SECURITY;
  }

  hasValue(): boolean {
    return this.value !== null && this.value !== undefined;
  }

  getValueAsNumber(): number {
    return this.value ? parseFloat(this.value) : 0;
  }

  isError(): boolean {
    return !this.success || !!this.errorMessage;
  }

  isCritical(): boolean {
    return this.severity === 'critical';
  }

  hasLocation(): boolean {
    return !!(this.metadata?.country || this.metadata?.city);
  }

  getLocation(): string {
    if (!this.hasLocation()) return 'Unknown';
    
    const parts = [];
    if (this.metadata?.city) parts.push(this.metadata.city);
    if (this.metadata?.country) parts.push(this.metadata.country);
    
    return parts.join(', ');
  }

  getDeviceInfo(): string {
    if (!this.metadata) return 'Unknown';
    
    const parts = [];
    if (this.metadata.device) parts.push(this.metadata.device);
    if (this.metadata.browser) parts.push(this.metadata.browser);
    if (this.metadata.platform) parts.push(this.metadata.platform);
    
    return parts.join(' - ') || 'Unknown';
  }

  getDurationInSeconds(): number {
    return this.duration ? this.duration / 1000 : 0;
  }

  addTag(tag: string): void {
    if (!this.tags) {
      this.tags = [];
    }
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  removeTag(tag: string): void {
    if (this.tags) {
      this.tags = this.tags.filter(t => t !== tag);
    }
  }

  hasTag(tag: string): boolean {
    return this.tags ? this.tags.includes(tag) : false;
  }

  setProperty(key: string, value: any): void {
    if (!this.properties) {
      this.properties = {};
    }
    this.properties[key] = value;
  }

  getProperty(key: string): any {
    return this.properties ? this.properties[key] : undefined;
  }

  setMetadata(key: string, value: any): void {
    if (!this.metadata) {
      this.metadata = {};
    }
    this.metadata[key] = value;
  }

  getMetadata(key: string): any {
    return this.metadata ? this.metadata[key] : undefined;
  }

  toSummary(): any {
    return {
      id: this.id,
      eventType: this.eventType,
      category: this.category,
      userId: this.userId,
      success: this.success,
      value: this.value,
      currency: this.currency,
      location: this.getLocation(),
      device: this.getDeviceInfo(),
      timestamp: this.timestamp,
      severity: this.severity,
    };
  }
}
