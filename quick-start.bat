@echo off
chcp 65001 >nul

echo 🚀 WS Transfir - تشغيل سريع
echo ============================
echo.

:: Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 حمل Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متاح
echo.

:: Check if frontend exists
if not exist "frontend\web-app" (
    echo ❌ مجلد frontend/web-app غير موجود
    echo 📁 تأكد من أنك في مجلد المشروع الصحيح
    pause
    exit /b 1
)

echo ✅ مجلد الواجهة الأمامية موجود
echo.

:: Go to frontend directory
cd frontend\web-app

:: Quick install if needed
if not exist "node_modules" (
    echo 📦 تثبيت dependencies...
    npm install --silent
)

:: Create basic next config if missing
if not exist "next.config.js" (
    echo /** @type {import('next').NextConfig} */ > next.config.js
    echo const nextConfig = { reactStrictMode: true }; >> next.config.js
    echo module.exports = nextConfig; >> next.config.js
)

:: Start development server
echo 🌐 تشغيل الخادم على http://localhost:3100
echo 📝 اضغط Ctrl+C للإيقاف
echo.

start "" "http://localhost:3100"
npm run dev

cd ..\..
pause
