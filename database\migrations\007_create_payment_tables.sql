-- Migration: Create Payment and Settlement System Tables
-- Description: إنشاء جداول نظام الدفع والتسوية المتقدم
-- Version: 007
-- Created: 2024-01-16

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id VARCHAR(50) PRIMARY KEY,
    customer_id VARCHAR(50) NOT NULL,
    amount DECIMAL(15,4) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN (
        'credit_card', 'debit_card', 'bank_transfer', 'digital_wallet', 
        'mobile_payment', 'cryptocurrency'
    )),
    provider VARCHAR(20) NOT NULL CHECK (provider IN (
        'stripe', 'paypal', 'mada', 'visa', 'mastercard', 'stcpay', 
        'applepay', 'googlepay', 'sadad', 'tabby'
    )),
    
    -- Payment details
    description TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded'
    )),
    
    -- Provider details
    provider_transaction_id VARCHAR(200),
    provider_response JSONB DEFAULT '{}',
    payment_url TEXT,
    
    -- Financial details
    fees_amount DECIMAL(15,4) NOT NULL DEFAULT 0,
    net_amount DECIMAL(15,4) GENERATED ALWAYS AS (amount - fees_amount) STORED,
    
    -- URLs and metadata
    return_url TEXT,
    webhook_url TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign keys
    CONSTRAINT fk_payments_customer FOREIGN KEY (customer_id) REFERENCES users(id)
);

-- Create refunds table
CREATE TABLE IF NOT EXISTS refunds (
    id VARCHAR(50) PRIMARY KEY,
    payment_id VARCHAR(50) NOT NULL,
    amount DECIMAL(15,4) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    reason TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'completed', 'failed', 'cancelled'
    )),
    
    -- Provider details
    provider_refund_id VARCHAR(200),
    provider_response JSONB DEFAULT '{}',
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_refunds_payment FOREIGN KEY (payment_id) REFERENCES payments(id)
);

-- Create account_balances table
CREATE TABLE IF NOT EXISTS account_balances (
    account_id VARCHAR(50) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    available_balance DECIMAL(15,4) NOT NULL DEFAULT 0,
    pending_balance DECIMAL(15,4) NOT NULL DEFAULT 0,
    reserved_balance DECIMAL(15,4) NOT NULL DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    PRIMARY KEY (account_id, currency),
    CHECK (available_balance >= 0),
    CHECK (pending_balance >= 0),
    CHECK (reserved_balance >= 0)
);

-- Create balance_changes table for audit trail
CREATE TABLE IF NOT EXISTS balance_changes (
    id VARCHAR(50) PRIMARY KEY,
    account_id VARCHAR(50) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    amount DECIMAL(15,4) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN (
        'credit', 'debit', 'reserve', 'release_reserve', 'settle_reserve'
    )),
    old_balance DECIMAL(15,4) NOT NULL,
    new_balance DECIMAL(15,4) NOT NULL,
    reference_id VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_balance_changes_account FOREIGN KEY (account_id, currency) 
        REFERENCES account_balances(account_id, currency)
);

-- Create settlements table
CREATE TABLE IF NOT EXISTS settlements (
    id VARCHAR(50) PRIMARY KEY,
    account_id VARCHAR(50) NOT NULL,
    amount DECIMAL(15,4) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    settlement_type VARCHAR(20) NOT NULL CHECK (settlement_type IN (
        'daily', 'weekly', 'monthly', 'instant', 'manual'
    )),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'completed', 'failed', 'cancelled', 'partially_settled'
    )),
    
    -- Financial details
    fees_amount DECIMAL(15,4) NOT NULL DEFAULT 0,
    net_amount DECIMAL(15,4) NOT NULL,
    
    -- Settlement details
    description TEXT NOT NULL,
    reference_number VARCHAR(100),
    bank_reference VARCHAR(100),
    error_message TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create risk_assessments table
CREATE TABLE IF NOT EXISTS risk_assessments (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    transaction_amount DECIMAL(15,4) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    transaction_type VARCHAR(50) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    
    -- Risk results
    risk_score DECIMAL(5,2) NOT NULL CHECK (risk_score >= 0 AND risk_score <= 100),
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    recommended_action VARCHAR(30) NOT NULL CHECK (recommended_action IN (
        'allow', 'review', 'block', 'require_verification', 'limit_amount', 'delay_processing'
    )),
    requires_manual_review BOOLEAN NOT NULL DEFAULT false,
    
    -- Request details
    ip_address INET,
    device_fingerprint VARCHAR(200),
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_risk_assessments_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create risk_factors table
CREATE TABLE IF NOT EXISTS risk_factors (
    id VARCHAR(50) PRIMARY KEY,
    assessment_id VARCHAR(50) NOT NULL,
    risk_type VARCHAR(20) NOT NULL CHECK (risk_type IN (
        'fraud', 'velocity', 'amount', 'geographic', 'behavioral', 'compliance', 'credit'
    )),
    risk_score DECIMAL(5,2) NOT NULL CHECK (risk_score >= 0 AND risk_score <= 100),
    description TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_risk_factors_assessment FOREIGN KEY (assessment_id) REFERENCES risk_assessments(id) ON DELETE CASCADE
);

-- Create risk_rules table
CREATE TABLE IF NOT EXISTS risk_rules (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    risk_type VARCHAR(20) NOT NULL,
    condition_text TEXT NOT NULL,
    risk_score DECIMAL(5,2) NOT NULL CHECK (risk_score >= 0 AND risk_score <= 100),
    action VARCHAR(30) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 5 CHECK (priority >= 1 AND priority <= 10),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create report_metadata table
CREATE TABLE IF NOT EXISTS report_metadata (
    report_id VARCHAR(50) PRIMARY KEY,
    report_type VARCHAR(20) NOT NULL CHECK (report_type IN (
        'revenue', 'transactions', 'settlements', 'risk_analysis', 
        'compliance', 'performance', 'reconciliation'
    )),
    period VARCHAR(20) NOT NULL CHECK (period IN (
        'daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom'
    )),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    format VARCHAR(10) NOT NULL CHECK (format IN ('json', 'csv', 'pdf', 'excel')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'completed', 'failed', 'expired'
    )),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    
    -- File details
    file_path TEXT,
    file_size BIGINT,
    
    -- Metadata
    filters JSONB DEFAULT '{}',
    requested_by VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    generated_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign keys
    CONSTRAINT fk_report_metadata_user FOREIGN KEY (requested_by) REFERENCES users(id)
);

-- Create scheduled_reports table
CREATE TABLE IF NOT EXISTS scheduled_reports (
    id VARCHAR(50) PRIMARY KEY,
    report_type VARCHAR(20) NOT NULL,
    period VARCHAR(20) NOT NULL,
    format VARCHAR(10) NOT NULL,
    filters JSONB DEFAULT '{}',
    
    -- Schedule details
    schedule_type VARCHAR(20) NOT NULL CHECK (schedule_type IN ('daily', 'weekly', 'monthly', 'custom')),
    schedule_config JSONB DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Execution tracking
    last_executed_at TIMESTAMP WITH TIME ZONE,
    next_execution_at TIMESTAMP WITH TIME ZONE,
    execution_count INTEGER NOT NULL DEFAULT 0,
    
    -- Metadata
    requested_by VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_scheduled_reports_user FOREIGN KEY (requested_by) REFERENCES users(id)
);

-- Create indexes for better performance

-- Payments indexes
CREATE INDEX idx_payments_customer_id ON payments(customer_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_provider ON payments(provider);
CREATE INDEX idx_payments_payment_method ON payments(payment_method);
CREATE INDEX idx_payments_created_at ON payments(created_at);
CREATE INDEX idx_payments_amount ON payments(amount);
CREATE INDEX idx_payments_currency ON payments(currency);
CREATE INDEX idx_payments_customer_status ON payments(customer_id, status);
CREATE INDEX idx_payments_provider_status ON payments(provider, status);
CREATE INDEX idx_payments_created_status ON payments(created_at, status);

-- Refunds indexes
CREATE INDEX idx_refunds_payment_id ON refunds(payment_id);
CREATE INDEX idx_refunds_status ON refunds(status);
CREATE INDEX idx_refunds_created_at ON refunds(created_at);
CREATE INDEX idx_refunds_amount ON refunds(amount);

-- Account balances indexes
CREATE INDEX idx_account_balances_account_id ON account_balances(account_id);
CREATE INDEX idx_account_balances_currency ON account_balances(currency);
CREATE INDEX idx_account_balances_updated_at ON account_balances(updated_at);

-- Balance changes indexes
CREATE INDEX idx_balance_changes_account_id ON balance_changes(account_id);
CREATE INDEX idx_balance_changes_currency ON balance_changes(currency);
CREATE INDEX idx_balance_changes_transaction_type ON balance_changes(transaction_type);
CREATE INDEX idx_balance_changes_created_at ON balance_changes(created_at);
CREATE INDEX idx_balance_changes_reference_id ON balance_changes(reference_id);

-- Settlements indexes
CREATE INDEX idx_settlements_account_id ON settlements(account_id);
CREATE INDEX idx_settlements_status ON settlements(status);
CREATE INDEX idx_settlements_settlement_type ON settlements(settlement_type);
CREATE INDEX idx_settlements_created_at ON settlements(created_at);
CREATE INDEX idx_settlements_scheduled_at ON settlements(scheduled_at);
CREATE INDEX idx_settlements_completed_at ON settlements(completed_at);
CREATE INDEX idx_settlements_account_status ON settlements(account_id, status);

-- Risk assessments indexes
CREATE INDEX idx_risk_assessments_user_id ON risk_assessments(user_id);
CREATE INDEX idx_risk_assessments_risk_level ON risk_assessments(risk_level);
CREATE INDEX idx_risk_assessments_risk_score ON risk_assessments(risk_score);
CREATE INDEX idx_risk_assessments_recommended_action ON risk_assessments(recommended_action);
CREATE INDEX idx_risk_assessments_created_at ON risk_assessments(created_at);
CREATE INDEX idx_risk_assessments_user_created ON risk_assessments(user_id, created_at);
CREATE INDEX idx_risk_assessments_manual_review ON risk_assessments(requires_manual_review);

-- Risk factors indexes
CREATE INDEX idx_risk_factors_assessment_id ON risk_factors(assessment_id);
CREATE INDEX idx_risk_factors_risk_type ON risk_factors(risk_type);
CREATE INDEX idx_risk_factors_risk_score ON risk_factors(risk_score);

-- Risk rules indexes
CREATE INDEX idx_risk_rules_risk_type ON risk_rules(risk_type);
CREATE INDEX idx_risk_rules_is_active ON risk_rules(is_active);
CREATE INDEX idx_risk_rules_priority ON risk_rules(priority);

-- Report metadata indexes
CREATE INDEX idx_report_metadata_report_type ON report_metadata(report_type);
CREATE INDEX idx_report_metadata_status ON report_metadata(status);
CREATE INDEX idx_report_metadata_requested_by ON report_metadata(requested_by);
CREATE INDEX idx_report_metadata_created_at ON report_metadata(created_at);
CREATE INDEX idx_report_metadata_expires_at ON report_metadata(expires_at);

-- Scheduled reports indexes
CREATE INDEX idx_scheduled_reports_is_active ON scheduled_reports(is_active);
CREATE INDEX idx_scheduled_reports_next_execution ON scheduled_reports(next_execution_at);
CREATE INDEX idx_scheduled_reports_requested_by ON scheduled_reports(requested_by);

-- Create GIN indexes for JSONB fields
CREATE INDEX idx_payments_metadata ON payments USING GIN(metadata);
CREATE INDEX idx_payments_provider_response ON payments USING GIN(provider_response);
CREATE INDEX idx_refunds_metadata ON refunds USING GIN(metadata);
CREATE INDEX idx_refunds_provider_response ON refunds USING GIN(provider_response);
CREATE INDEX idx_settlements_metadata ON settlements USING GIN(metadata);
CREATE INDEX idx_risk_assessments_metadata ON risk_assessments USING GIN(metadata);
CREATE INDEX idx_risk_factors_details ON risk_factors USING GIN(details);
CREATE INDEX idx_report_metadata_filters ON report_metadata USING GIN(filters);
CREATE INDEX idx_scheduled_reports_filters ON scheduled_reports USING GIN(filters);
CREATE INDEX idx_scheduled_reports_config ON scheduled_reports USING GIN(schedule_config);

-- Create functions for ID generation
CREATE OR REPLACE FUNCTION generate_payment_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'pay_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_refund_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'ref_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_balance_change_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'bal_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_settlement_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'set_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_risk_assessment_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'risk_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_risk_factor_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'rf_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_risk_rule_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'rule_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_report_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.report_id IS NULL THEN
        NEW.report_id = 'rpt_' || encode(gen_random_bytes(12), 'base64');
        NEW.report_id = replace(NEW.report_id, '/', '_');
        NEW.report_id = replace(NEW.report_id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION generate_scheduled_report_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'sched_rpt_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for ID generation
CREATE TRIGGER generate_payment_id_trigger
    BEFORE INSERT ON payments
    FOR EACH ROW
    EXECUTE FUNCTION generate_payment_id();

CREATE TRIGGER generate_refund_id_trigger
    BEFORE INSERT ON refunds
    FOR EACH ROW
    EXECUTE FUNCTION generate_refund_id();

CREATE TRIGGER generate_balance_change_id_trigger
    BEFORE INSERT ON balance_changes
    FOR EACH ROW
    EXECUTE FUNCTION generate_balance_change_id();

CREATE TRIGGER generate_settlement_id_trigger
    BEFORE INSERT ON settlements
    FOR EACH ROW
    EXECUTE FUNCTION generate_settlement_id();

CREATE TRIGGER generate_risk_assessment_id_trigger
    BEFORE INSERT ON risk_assessments
    FOR EACH ROW
    EXECUTE FUNCTION generate_risk_assessment_id();

CREATE TRIGGER generate_risk_factor_id_trigger
    BEFORE INSERT ON risk_factors
    FOR EACH ROW
    EXECUTE FUNCTION generate_risk_factor_id();

CREATE TRIGGER generate_risk_rule_id_trigger
    BEFORE INSERT ON risk_rules
    FOR EACH ROW
    EXECUTE FUNCTION generate_risk_rule_id();

CREATE TRIGGER generate_report_id_trigger
    BEFORE INSERT ON report_metadata
    FOR EACH ROW
    EXECUTE FUNCTION generate_report_id();

CREATE TRIGGER generate_scheduled_report_id_trigger
    BEFORE INSERT ON scheduled_reports
    FOR EACH ROW
    EXECUTE FUNCTION generate_scheduled_report_id();

-- Create triggers for updated_at
CREATE TRIGGER update_payments_updated_at
    BEFORE UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_refunds_updated_at
    BEFORE UPDATE ON refunds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_account_balances_updated_at
    BEFORE UPDATE ON account_balances
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settlements_updated_at
    BEFORE UPDATE ON settlements
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_risk_rules_updated_at
    BEFORE UPDATE ON risk_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scheduled_reports_updated_at
    BEFORE UPDATE ON scheduled_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create account balance records
CREATE OR REPLACE FUNCTION create_default_account_balance()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO account_balances (account_id, currency)
    VALUES (NEW.id, 'SAR')
    ON CONFLICT (account_id, currency) DO NOTHING;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for default account balance
CREATE TRIGGER create_default_account_balance_trigger
    AFTER INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_account_balance();

-- Create function to clean up expired reports
CREATE OR REPLACE FUNCTION cleanup_expired_reports()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired reports
    DELETE FROM report_metadata 
    WHERE expires_at IS NOT NULL AND expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Create function to calculate settlement fees
CREATE OR REPLACE FUNCTION calculate_settlement_fees(
    amount DECIMAL(15,4),
    settlement_type VARCHAR(20)
) RETURNS DECIMAL(15,4) AS $$
DECLARE
    fee_percentage DECIMAL(5,4);
BEGIN
    -- Set fee percentage based on settlement type
    CASE settlement_type
        WHEN 'instant' THEN fee_percentage := 0.0100; -- 1.0%
        WHEN 'daily' THEN fee_percentage := 0.0050;   -- 0.5%
        WHEN 'weekly' THEN fee_percentage := 0.0030;  -- 0.3%
        WHEN 'monthly' THEN fee_percentage := 0.0020; -- 0.2%
        ELSE fee_percentage := 0.0050; -- Default 0.5%
    END CASE;
    
    RETURN amount * fee_percentage;
END;
$$ language 'plpgsql';

-- Add comments for documentation
COMMENT ON TABLE payments IS 'جدول المدفوعات - يحتوي على جميع معاملات الدفع';
COMMENT ON COLUMN payments.provider_response IS 'استجابة مقدم الخدمة بتنسيق JSON';
COMMENT ON COLUMN payments.metadata IS 'بيانات إضافية بتنسيق JSON';

COMMENT ON TABLE refunds IS 'جدول المردودات - يحتوي على جميع عمليات الاسترداد';
COMMENT ON TABLE account_balances IS 'جدول أرصدة الحسابات - يحتوي على أرصدة المستخدمين';
COMMENT ON TABLE balance_changes IS 'جدول تغييرات الأرصدة - سجل تدقيق لتغييرات الأرصدة';

COMMENT ON TABLE settlements IS 'جدول التسويات - إدارة عمليات التسوية المالية';
COMMENT ON COLUMN settlements.settlement_type IS 'نوع التسوية: فورية، يومية، أسبوعية، شهرية';

COMMENT ON TABLE risk_assessments IS 'جدول تقييمات المخاطر - تحليل مخاطر المعاملات';
COMMENT ON TABLE risk_factors IS 'جدول عوامل المخاطر - تفاصيل عوامل المخاطر المكتشفة';
COMMENT ON TABLE risk_rules IS 'جدول قواعد المخاطر - قواعد تقييم المخاطر';

COMMENT ON TABLE report_metadata IS 'جدول بيانات التقارير الوصفية - معلومات التقارير المالية';
COMMENT ON TABLE scheduled_reports IS 'جدول التقارير المجدولة - إدارة التقارير التلقائية';

-- Create views for reporting
CREATE OR REPLACE VIEW payment_summary AS
SELECT
    DATE(created_at) as payment_date,
    provider,
    payment_method,
    currency,
    COUNT(*) as transaction_count,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_count,
    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount,
    SUM(CASE WHEN status = 'completed' THEN fees_amount ELSE 0 END) as total_fees,
    ROUND(AVG(CASE WHEN status = 'completed' THEN amount ELSE NULL END), 2) as avg_amount
FROM payments
GROUP BY DATE(created_at), provider, payment_method, currency;

CREATE OR REPLACE VIEW settlement_summary AS
SELECT
    DATE(created_at) as settlement_date,
    settlement_type,
    currency,
    COUNT(*) as settlement_count,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
    SUM(CASE WHEN status = 'completed' THEN net_amount ELSE 0 END) as total_settled,
    SUM(CASE WHEN status = 'completed' THEN fees_amount ELSE 0 END) as total_fees
FROM settlements
GROUP BY DATE(created_at), settlement_type, currency;

CREATE OR REPLACE VIEW risk_summary AS
SELECT
    DATE(created_at) as assessment_date,
    risk_level,
    recommended_action,
    COUNT(*) as assessment_count,
    ROUND(AVG(risk_score), 2) as avg_risk_score
FROM risk_assessments
GROUP BY DATE(created_at), risk_level, recommended_action;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON payments TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON refunds TO ws_app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON account_balances TO ws_app_user;
GRANT SELECT, INSERT ON balance_changes TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON settlements TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON risk_assessments TO ws_app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON risk_factors TO ws_app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON risk_rules TO ws_app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON report_metadata TO ws_app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON scheduled_reports TO ws_app_user;
GRANT SELECT ON payment_summary TO ws_app_user;
GRANT SELECT ON settlement_summary TO ws_app_user;
GRANT SELECT ON risk_summary TO ws_app_user;
