export * from './types.js';
import { Options, OptionsSync, PublicExplorer, PublicExplorerSync } from './types.js';
export declare const metaSearchPlaces: string[];
export declare const defaultLoaders: Readonly<{
    readonly '.mjs': import("./types.js").Loader;
    readonly '.cjs': import("./types.js").Loader;
    readonly '.js': import("./types.js").Loader;
    readonly '.ts': import("./types.js").Loader;
    readonly '.json': import("./types.js").LoaderSync;
    readonly '.yaml': import("./types.js").LoaderSync;
    readonly '.yml': import("./types.js").LoaderSync;
    readonly noExt: import("./types.js").LoaderSync;
}>;
export declare const defaultLoadersSync: Readonly<{
    readonly '.cjs': import("./types.js").LoaderSync;
    readonly '.js': import("./types.js").LoaderSync;
    readonly '.ts': import("./types.js").LoaderSync;
    readonly '.json': import("./types.js").LoaderSync;
    readonly '.yaml': import("./types.js").LoaderSync;
    readonly '.yml': import("./types.js").LoaderSync;
    readonly noExt: import("./types.js").LoaderSync;
}>;
export declare function cosmiconfig(moduleName: string, options?: Readonly<Options>): PublicExplorer;
export declare function cosmiconfigSync(moduleName: string, options?: Readonly<OptionsSync>): PublicExplorerSync;
//# sourceMappingURL=index.d.ts.map