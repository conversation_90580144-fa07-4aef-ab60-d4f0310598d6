{"version": 3, "file": "parseIncompletePhoneNumber.test.js", "names": ["describe", "it", "parsePhoneNumberCharacter", "should", "equal", "expect", "to", "be", "undefined", "parseIncompletePhoneNumber", "stopped", "emit", "event"], "sources": ["../source/parseIncompletePhoneNumber.test.js"], "sourcesContent": ["import parseIncompletePhoneNumber, { parsePhoneNumberCharacter } from './parseIncompletePhoneNumber.js'\r\n\r\ndescribe('parseIncompletePhoneNumber', () => {\r\n\tit('should parse phone number character', () => {\r\n\t\t// Accepts leading `+`.\r\n\t\tparsePhoneNumberCharacter('+').should.equal('+')\r\n\r\n\t\t// Doesn't accept non-leading `+`.\r\n\t\texpect(parsePhoneNumberCharacter('+', '+')).to.be.undefined\r\n\r\n\t\t// Parses digits.\r\n\t\tparsePhoneNumberCharacter('1').should.equal('1')\r\n\r\n\t\t// Parses non-European digits.\r\n\t\tparsePhoneNumberCharacter('٤').should.equal('4')\r\n\r\n\t\t// Dismisses other characters.\r\n\t\texpect(parsePhoneNumberCharacter('-')).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse incomplete phone number', () => {\r\n\t\tparseIncompletePhoneNumber('').should.equal('')\r\n\r\n\t\t// Doesn't accept non-leading `+`.\r\n\t\tparseIncompletePhoneNumber('++').should.equal('+')\r\n\r\n\t\t// Accepts leading `+`.\r\n\t\tparseIncompletePhoneNumber('****** 555').should.equal('+7800555')\r\n\r\n\t\t// Parses digits.\r\n\t\tparseIncompletePhoneNumber('8 (800) 555').should.equal('8800555')\r\n\r\n\t\t// Parses non-European digits.\r\n\t\tparseIncompletePhoneNumber('+٤٤٢٣٢٣٢٣٤').should.equal('+442323234')\r\n\t})\r\n\r\n\tit('should work with a new `context` argument in `parsePhoneNumberCharacter()` function (international number)', () => {\r\n\t\tlet stopped = false\r\n\r\n\t\tconst emit = (event) => {\r\n\t\t\tswitch (event) {\r\n\t\t\t\tcase 'end':\r\n\t\t\t\t\tstopped = true\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tparsePhoneNumberCharacter('+', undefined, emit).should.equal('+')\r\n\t\texpect(stopped).to.equal(false)\r\n\r\n\t\tparsePhoneNumberCharacter('1', '+', emit).should.equal('1')\r\n\t\texpect(stopped).to.equal(false)\r\n\r\n\t\texpect(parsePhoneNumberCharacter('+', '+1', emit)).to.equal(undefined)\r\n\t\texpect(stopped).to.equal(true)\r\n\r\n\t\texpect(parsePhoneNumberCharacter('2', '+1', emit)).to.equal('2')\r\n\t\texpect(stopped).to.equal(true)\r\n\t})\r\n\r\n\tit('should work with a new `context` argument in `parsePhoneNumberCharacter()` function (national number)', () => {\r\n\t\tlet stopped = false\r\n\r\n\t\tconst emit = (event) => {\r\n\t\t\tswitch (event) {\r\n\t\t\t\tcase 'end':\r\n\t\t\t\t\tstopped = true\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tparsePhoneNumberCharacter('2', undefined, emit).should.equal('2')\r\n\t\texpect(stopped).to.equal(false)\r\n\r\n\t\texpect(parsePhoneNumberCharacter('+', '2', emit)).to.equal(undefined)\r\n\t\texpect(stopped).to.equal(true)\r\n\r\n\t\texpect(parsePhoneNumberCharacter('1', '2', emit)).to.equal('1')\r\n\t\texpect(stopped).to.equal(true)\r\n\t})\r\n})"], "mappings": ";;;;AAAA;;;;;;AAEAA,QAAQ,CAAC,4BAAD,EAA+B,YAAM;EAC5CC,EAAE,CAAC,qCAAD,EAAwC,YAAM;IAC/C;IACA,IAAAC,qDAAA,EAA0B,GAA1B,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C,EAF+C,CAI/C;;IACAC,MAAM,CAAC,IAAAH,qDAAA,EAA0B,GAA1B,EAA+B,GAA/B,CAAD,CAAN,CAA4CI,EAA5C,CAA+CC,EAA/C,CAAkDC,SAAlD,CAL+C,CAO/C;;IACA,IAAAN,qDAAA,EAA0B,GAA1B,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C,EAR+C,CAU/C;;IACA,IAAAF,qDAAA,EAA0B,GAA1B,EAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C,EAX+C,CAa/C;;IACAC,MAAM,CAAC,IAAAH,qDAAA,EAA0B,GAA1B,CAAD,CAAN,CAAuCI,EAAvC,CAA0CC,EAA1C,CAA6CC,SAA7C;EACA,CAfC,CAAF;EAiBAP,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD,IAAAQ,sCAAA,EAA2B,EAA3B,EAA+BN,MAA/B,CAAsCC,KAAtC,CAA4C,EAA5C,EADgD,CAGhD;;IACA,IAAAK,sCAAA,EAA2B,IAA3B,EAAiCN,MAAjC,CAAwCC,KAAxC,CAA8C,GAA9C,EAJgD,CAMhD;;IACA,IAAAK,sCAAA,EAA2B,YAA3B,EAAyCN,MAAzC,CAAgDC,KAAhD,CAAsD,UAAtD,EAPgD,CAShD;;IACA,IAAAK,sCAAA,EAA2B,aAA3B,EAA0CN,MAA1C,CAAiDC,KAAjD,CAAuD,SAAvD,EAVgD,CAYhD;;IACA,IAAAK,sCAAA,EAA2B,YAA3B,EAAyCN,MAAzC,CAAgDC,KAAhD,CAAsD,YAAtD;EACA,CAdC,CAAF;EAgBAH,EAAE,CAAC,4GAAD,EAA+G,YAAM;IACtH,IAAIS,OAAO,GAAG,KAAd;;IAEA,IAAMC,IAAI,GAAG,SAAPA,IAAO,CAACC,KAAD,EAAW;MACvB,QAAQA,KAAR;QACC,KAAK,KAAL;UACCF,OAAO,GAAG,IAAV;UACA;MAHF;IAKA,CAND;;IAQA,IAAAR,qDAAA,EAA0B,GAA1B,EAA+BM,SAA/B,EAA0CG,IAA1C,EAAgDR,MAAhD,CAAuDC,KAAvD,CAA6D,GAA7D;IACAC,MAAM,CAACK,OAAD,CAAN,CAAgBJ,EAAhB,CAAmBF,KAAnB,CAAyB,KAAzB;IAEA,IAAAF,qDAAA,EAA0B,GAA1B,EAA+B,GAA/B,EAAoCS,IAApC,EAA0CR,MAA1C,CAAiDC,KAAjD,CAAuD,GAAvD;IACAC,MAAM,CAACK,OAAD,CAAN,CAAgBJ,EAAhB,CAAmBF,KAAnB,CAAyB,KAAzB;IAEAC,MAAM,CAAC,IAAAH,qDAAA,EAA0B,GAA1B,EAA+B,IAA/B,EAAqCS,IAArC,CAAD,CAAN,CAAmDL,EAAnD,CAAsDF,KAAtD,CAA4DI,SAA5D;IACAH,MAAM,CAACK,OAAD,CAAN,CAAgBJ,EAAhB,CAAmBF,KAAnB,CAAyB,IAAzB;IAEAC,MAAM,CAAC,IAAAH,qDAAA,EAA0B,GAA1B,EAA+B,IAA/B,EAAqCS,IAArC,CAAD,CAAN,CAAmDL,EAAnD,CAAsDF,KAAtD,CAA4D,GAA5D;IACAC,MAAM,CAACK,OAAD,CAAN,CAAgBJ,EAAhB,CAAmBF,KAAnB,CAAyB,IAAzB;EACA,CAtBC,CAAF;EAwBAH,EAAE,CAAC,uGAAD,EAA0G,YAAM;IACjH,IAAIS,OAAO,GAAG,KAAd;;IAEA,IAAMC,IAAI,GAAG,SAAPA,IAAO,CAACC,KAAD,EAAW;MACvB,QAAQA,KAAR;QACC,KAAK,KAAL;UACCF,OAAO,GAAG,IAAV;UACA;MAHF;IAKA,CAND;;IAQA,IAAAR,qDAAA,EAA0B,GAA1B,EAA+BM,SAA/B,EAA0CG,IAA1C,EAAgDR,MAAhD,CAAuDC,KAAvD,CAA6D,GAA7D;IACAC,MAAM,CAACK,OAAD,CAAN,CAAgBJ,EAAhB,CAAmBF,KAAnB,CAAyB,KAAzB;IAEAC,MAAM,CAAC,IAAAH,qDAAA,EAA0B,GAA1B,EAA+B,GAA/B,EAAoCS,IAApC,CAAD,CAAN,CAAkDL,EAAlD,CAAqDF,KAArD,CAA2DI,SAA3D;IACAH,MAAM,CAACK,OAAD,CAAN,CAAgBJ,EAAhB,CAAmBF,KAAnB,CAAyB,IAAzB;IAEAC,MAAM,CAAC,IAAAH,qDAAA,EAA0B,GAA1B,EAA+B,GAA/B,EAAoCS,IAApC,CAAD,CAAN,CAAkDL,EAAlD,CAAqDF,KAArD,CAA2D,GAA3D;IACAC,MAAM,CAACK,OAAD,CAAN,CAAgBJ,EAAhB,CAAmBF,KAAnB,CAAyB,IAAzB;EACA,CAnBC,CAAF;AAoBA,CA9EO,CAAR"}