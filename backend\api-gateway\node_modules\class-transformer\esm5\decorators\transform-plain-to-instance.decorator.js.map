{"version": 3, "file": "transform-plain-to-instance.decorator.js", "sourceRoot": "", "sources": ["../../../src/decorators/transform-plain-to-instance.decorator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAGvD;;;;GAIG;AACH,MAAM,UAAU,wBAAwB,CACtC,SAAgC,EAChC,MAA8B;IAE9B,OAAO,UAAU,MAA2B,EAAE,WAA4B,EAAE,UAA8B;QACxG,IAAM,gBAAgB,GAAqB,IAAI,gBAAgB,EAAE,CAAC;QAClE,IAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG;YAAU,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YACzC,IAAM,MAAM,GAAQ,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACrD,IAAM,SAAS,GACb,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,CAAC,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;YAChH,OAAO,SAAS;gBACd,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAC,IAAS,IAAK,OAAA,gBAAgB,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,EAAzD,CAAyD,CAAC;gBACvF,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAClE,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { ClassTransformer } from '../ClassTransformer';\nimport { ClassTransformOptions, ClassConstructor } from '../interfaces';\n\n/**\n * Return the class instance only with the exposed properties.\n *\n * Can be applied to functions and getters/setters only.\n */\nexport function TransformPlainToInstance(\n  classType: ClassConstructor<any>,\n  params?: ClassTransformOptions\n): MethodDecorator {\n  return function (target: Record<string, any>, propertyKey: string | Symbol, descriptor: PropertyDescriptor): void {\n    const classTransformer: ClassTransformer = new ClassTransformer();\n    const originalMethod = descriptor.value;\n\n    descriptor.value = function (...args: any[]): Record<string, any> {\n      const result: any = originalMethod.apply(this, args);\n      const isPromise =\n        !!result && (typeof result === 'object' || typeof result === 'function') && typeof result.then === 'function';\n      return isPromise\n        ? result.then((data: any) => classTransformer.plainToInstance(classType, data, params))\n        : classTransformer.plainToInstance(classType, result, params);\n    };\n  };\n}\n"]}