import _parseNumber from '../parse.js';
import normalizeArguments from '../normalizeArguments.js';
export default function parseNumber() {
  var _normalizeArguments = normalizeArguments(arguments),
      text = _normalizeArguments.text,
      options = _normalizeArguments.options,
      metadata = _normalizeArguments.metadata;

  return _parseNumber(text, options, metadata);
}
//# sourceMappingURL=parse.js.map