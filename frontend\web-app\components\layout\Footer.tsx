'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
} from '@heroicons/react/24/outline';
import { useLanguage } from '@/contexts/LanguageContext';

// روابط التذييل
const footerLinks = {
  company: [
    { key: 'footer.company.about', href: '/about' },
    { key: 'footer.company.careers', href: '/careers' },
    { key: 'footer.company.news', href: '/news' },
    { key: 'footer.company.investors', href: '/investors' },
  ],
  services: [
    { key: 'footer.services.sendMoney', href: '/services/send-money' },
    { key: 'footer.services.receiveMoney', href: '/services/receive-money' },
    { key: 'footer.services.wallet', href: '/services/wallet' },
    { key: 'footer.services.billPayment', href: '/services/bill-payment' },
  ],
  support: [
    { key: 'footer.support.help', href: '/help' },
    { key: 'footer.support.contact', href: '/contact' },
    { key: 'footer.support.faq', href: '/faq' },
    { key: 'footer.support.security', href: '/security' },
  ],
  legal: [
    { key: 'footer.legal.privacy', href: '/privacy' },
    { key: 'footer.legal.terms', href: '/terms' },
    { key: 'footer.legal.compliance', href: '/compliance' },
    { key: 'footer.legal.cookies', href: '/cookies' },
  ],
};

// معلومات الاتصال
const contactInfo = [
  {
    icon: PhoneIcon,
    key: 'footer.contact.phone',
    value: '+966 11 123 4567',
    href: 'tel:+966111234567',
  },
  {
    icon: EnvelopeIcon,
    key: 'footer.contact.email',
    value: '<EMAIL>',
    href: 'mailto:<EMAIL>',
  },
  {
    icon: MapPinIcon,
    key: 'footer.contact.address',
    value: 'الرياض، المملكة العربية السعودية',
    href: '#',
  },
];

// روابط وسائل التواصل الاجتماعي
const socialLinks = [
  {
    name: 'Twitter',
    href: 'https://twitter.com/wstransfir',
    icon: (props: any) => (
      <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
      </svg>
    ),
  },
  {
    name: 'LinkedIn',
    href: 'https://linkedin.com/company/wstransfir',
    icon: (props: any) => (
      <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
      </svg>
    ),
  },
  {
    name: 'Instagram',
    href: 'https://instagram.com/wstransfir',
    icon: (props: any) => (
      <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.49 0-.928-.175-1.297-.49-.368-.315-.49-.753-.49-1.243 0-.49.122-.928.49-1.243.369-.315.807-.49 1.297-.49s.928.175 1.297.49c.315.315.49.753.49 1.243 0 .49-.175.928-.49 1.243-.369.315-.807.49-1.297.49z" />
      </svg>
    ),
  },
];

export default function Footer() {
  const { t, isRTL } = useLanguage();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container-custom">
        {/* المحتوى الرئيسي */}
        <div className="py-12 lg:py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {/* معلومات الشركة */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                {/* الشعار */}
                <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-primary-700 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">WS</span>
                  </div>
                  <span className="text-xl font-bold">WS Transfir</span>
                </div>

                {/* الوصف */}
                <p className="text-gray-300 mb-6 leading-relaxed">
                  {t('footer.description')}
                </p>

                {/* معلومات الاتصال */}
                <div className="space-y-3">
                  {contactInfo.map((contact, index) => (
                    <motion.a
                      key={index}
                      href={contact.href}
                      initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center space-x-3 rtl:space-x-reverse text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      <contact.icon className="w-5 h-5 flex-shrink-0" />
                      <span className="text-sm">{contact.value}</span>
                    </motion.a>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* روابط الشركة */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-lg font-semibold mb-4">
                  {t('footer.company.title')}
                </h3>
                <ul className="space-y-3">
                  {footerLinks.company.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                      >
                        {t(link.key)}
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* روابط الخدمات */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h3 className="text-lg font-semibold mb-4">
                  {t('footer.services.title')}
                </h3>
                <ul className="space-y-3">
                  {footerLinks.services.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                      >
                        {t(link.key)}
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* روابط الدعم */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <h3 className="text-lg font-semibold mb-4">
                  {t('footer.support.title')}
                </h3>
                <ul className="space-y-3">
                  {footerLinks.support.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                      >
                        {t(link.key)}
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </div>
        </div>

        {/* الخط الفاصل */}
        <div className="border-t border-gray-800"></div>

        {/* الجزء السفلي */}
        <div className="py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* حقوق النشر */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-gray-400 text-sm"
            >
              © {new Date().getFullYear()} WS Transfir. {t('footer.copyright')}
            </motion.div>

            {/* روابط وسائل التواصل الاجتماعي */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="flex space-x-4 rtl:space-x-reverse"
            >
              {socialLinks.map((social, index) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-8 h-8 text-gray-400 hover:text-white transition-colors duration-200"
                  aria-label={social.name}
                >
                  <social.icon className="w-full h-full" />
                </motion.a>
              ))}
            </motion.div>

            {/* الروابط القانونية */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="flex space-x-6 rtl:space-x-reverse"
            >
              {footerLinks.legal.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  {t(link.key)}
                </a>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
}
