{"version": 3, "file": "AsYouTypeFormatter.util.js", "names": ["DIGIT_PLACEHOLDER", "DIGIT_PLACEHOLDER_MATCHER", "RegExp", "countOccurences", "symbol", "string", "count", "split", "character", "repeat", "times", "result", "cutAndStripNonPairedParens", "cutBeforeIndex", "stripNonPairedParens", "slice", "closeNonPairedParens", "template", "cut_before", "retained_template", "opening_braces", "closing_braces", "dangling_braces", "length", "i", "push", "pop", "start", "cleared_string", "index", "populateTemplateWithDigits", "position", "digits", "digit", "search", "replace"], "sources": ["../source/AsYouTypeFormatter.util.js"], "sourcesContent": ["// Should be the same as `DIGIT_PLACEHOLDER` in `libphonenumber-metadata-generator`.\r\nexport const DIGIT_PLACEHOLDER = 'x' // '\\u2008' (punctuation space)\r\nconst DIGIT_PLACEHOLDER_MATCHER = new RegExp(DIGIT_PLACEHOLDER)\r\n\r\n// Counts all occurences of a symbol in a string.\r\n// Unicode-unsafe (because using `.split()`).\r\nexport function countOccurences(symbol, string) {\r\n\tlet count = 0\r\n\t// Using `.split('')` to iterate through a string here\r\n\t// to avoid requiring `Symbol.iterator` polyfill.\r\n\t// `.split('')` is generally not safe for Unicode,\r\n\t// but in this particular case for counting brackets it is safe.\r\n\t// for (const character of string)\r\n\tfor (const character of string.split('')) {\r\n\t\tif (character === symbol) {\r\n\t\t\tcount++\r\n\t\t}\r\n\t}\r\n\treturn count\r\n}\r\n\r\n// Repeats a string (or a symbol) N times.\r\n// http://stackoverflow.com/questions/202605/repeat-string-javascript\r\nexport function repeat(string, times) {\r\n\tif (times < 1) {\r\n\t\treturn ''\r\n\t}\r\n\tlet result = ''\r\n\twhile (times > 1) {\r\n\t\tif (times & 1) {\r\n\t\t\tresult += string\r\n\t\t}\r\n\t\ttimes >>= 1\r\n\t\tstring += string\r\n\t}\r\n\treturn result + string\r\n}\r\n\r\nexport function cutAndStripNonPairedParens(string, cutBeforeIndex) {\r\n\tif (string[cutBeforeIndex] === ')') {\r\n\t\tcutBeforeIndex++\r\n\t}\r\n\treturn stripNonPairedParens(string.slice(0, cutBeforeIndex))\r\n}\r\n\r\nexport function closeNonPairedParens(template, cut_before) {\r\n\tconst retained_template = template.slice(0, cut_before)\r\n\tconst opening_braces = countOccurences('(', retained_template)\r\n\tconst closing_braces = countOccurences(')', retained_template)\r\n\tlet dangling_braces = opening_braces - closing_braces\r\n\twhile (dangling_braces > 0 && cut_before < template.length) {\r\n\t\tif (template[cut_before] === ')') {\r\n\t\t\tdangling_braces--\r\n\t\t}\r\n\t\tcut_before++\r\n\t}\r\n\treturn template.slice(0, cut_before)\r\n}\r\n\r\nexport function stripNonPairedParens(string) {\r\n\tconst dangling_braces =[]\r\n\tlet i = 0\r\n\twhile (i < string.length) {\r\n\t\tif (string[i] === '(') {\r\n\t\t\tdangling_braces.push(i)\r\n\t\t}\r\n\t\telse if (string[i] === ')') {\r\n\t\t\tdangling_braces.pop()\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\tlet start = 0\r\n\tlet cleared_string = ''\r\n\tdangling_braces.push(string.length)\r\n\tfor (const index of dangling_braces) {\r\n\t\tcleared_string += string.slice(start, index)\r\n\t\tstart = index + 1\r\n\t}\r\n\treturn cleared_string\r\n}\r\n\r\nexport function populateTemplateWithDigits(template, position, digits) {\r\n\t// Using `.split('')` to iterate through a string here\r\n\t// to avoid requiring `Symbol.iterator` polyfill.\r\n\t// `.split('')` is generally not safe for Unicode,\r\n\t// but in this particular case for `digits` it is safe.\r\n\t// for (const digit of digits)\r\n\tfor (const digit of digits.split('')) {\r\n\t\t// If there is room for more digits in current `template`,\r\n\t\t// then set the next digit in the `template`,\r\n\t\t// and return the formatted digits so far.\r\n\t\t// If more digits are entered than the current format could handle.\r\n\t\tif (template.slice(position + 1).search(DIGIT_PLACEHOLDER_MATCHER) < 0) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tposition = template.search(DIGIT_PLACEHOLDER_MATCHER)\r\n\t\ttemplate = template.replace(DIGIT_PLACEHOLDER_MATCHER, digit)\r\n\t}\r\n\treturn [template, position]\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACO,IAAMA,iBAAiB,GAAG,GAA1B,C,CAA8B;;;AACrC,IAAMC,yBAAyB,GAAG,IAAIC,MAAJ,CAAWF,iBAAX,CAAlC,C,CAEA;AACA;;AACO,SAASG,eAAT,CAAyBC,MAAzB,EAAiCC,MAAjC,EAAyC;EAC/C,IAAIC,KAAK,GAAG,CAAZ,CAD+C,CAE/C;EACA;EACA;EACA;EACA;;EACA,qDAAwBD,MAAM,CAACE,KAAP,CAAa,EAAb,CAAxB,wCAA0C;IAAA,IAA/BC,SAA+B;;IACzC,IAAIA,SAAS,KAAKJ,MAAlB,EAA0B;MACzBE,KAAK;IACL;EACD;;EACD,OAAOA,KAAP;AACA,C,CAED;AACA;;;AACO,SAASG,MAAT,CAAgBJ,MAAhB,EAAwBK,KAAxB,EAA+B;EACrC,IAAIA,KAAK,GAAG,CAAZ,EAAe;IACd,OAAO,EAAP;EACA;;EACD,IAAIC,MAAM,GAAG,EAAb;;EACA,OAAOD,KAAK,GAAG,CAAf,EAAkB;IACjB,IAAIA,KAAK,GAAG,CAAZ,EAAe;MACdC,MAAM,IAAIN,MAAV;IACA;;IACDK,KAAK,KAAK,CAAV;IACAL,MAAM,IAAIA,MAAV;EACA;;EACD,OAAOM,MAAM,GAAGN,MAAhB;AACA;;AAEM,SAASO,0BAAT,CAAoCP,MAApC,EAA4CQ,cAA5C,EAA4D;EAClE,IAAIR,MAAM,CAACQ,cAAD,CAAN,KAA2B,GAA/B,EAAoC;IACnCA,cAAc;EACd;;EACD,OAAOC,oBAAoB,CAACT,MAAM,CAACU,KAAP,CAAa,CAAb,EAAgBF,cAAhB,CAAD,CAA3B;AACA;;AAEM,SAASG,oBAAT,CAA8BC,QAA9B,EAAwCC,UAAxC,EAAoD;EAC1D,IAAMC,iBAAiB,GAAGF,QAAQ,CAACF,KAAT,CAAe,CAAf,EAAkBG,UAAlB,CAA1B;EACA,IAAME,cAAc,GAAGjB,eAAe,CAAC,GAAD,EAAMgB,iBAAN,CAAtC;EACA,IAAME,cAAc,GAAGlB,eAAe,CAAC,GAAD,EAAMgB,iBAAN,CAAtC;EACA,IAAIG,eAAe,GAAGF,cAAc,GAAGC,cAAvC;;EACA,OAAOC,eAAe,GAAG,CAAlB,IAAuBJ,UAAU,GAAGD,QAAQ,CAACM,MAApD,EAA4D;IAC3D,IAAIN,QAAQ,CAACC,UAAD,CAAR,KAAyB,GAA7B,EAAkC;MACjCI,eAAe;IACf;;IACDJ,UAAU;EACV;;EACD,OAAOD,QAAQ,CAACF,KAAT,CAAe,CAAf,EAAkBG,UAAlB,CAAP;AACA;;AAEM,SAASJ,oBAAT,CAA8BT,MAA9B,EAAsC;EAC5C,IAAMiB,eAAe,GAAE,EAAvB;EACA,IAAIE,CAAC,GAAG,CAAR;;EACA,OAAOA,CAAC,GAAGnB,MAAM,CAACkB,MAAlB,EAA0B;IACzB,IAAIlB,MAAM,CAACmB,CAAD,CAAN,KAAc,GAAlB,EAAuB;MACtBF,eAAe,CAACG,IAAhB,CAAqBD,CAArB;IACA,CAFD,MAGK,IAAInB,MAAM,CAACmB,CAAD,CAAN,KAAc,GAAlB,EAAuB;MAC3BF,eAAe,CAACI,GAAhB;IACA;;IACDF,CAAC;EACD;;EACD,IAAIG,KAAK,GAAG,CAAZ;EACA,IAAIC,cAAc,GAAG,EAArB;EACAN,eAAe,CAACG,IAAhB,CAAqBpB,MAAM,CAACkB,MAA5B;;EACA,oCAAoBD,eAApB,sCAAqC;IAAhC,IAAMO,KAAK,uBAAX;IACJD,cAAc,IAAIvB,MAAM,CAACU,KAAP,CAAaY,KAAb,EAAoBE,KAApB,CAAlB;IACAF,KAAK,GAAGE,KAAK,GAAG,CAAhB;EACA;;EACD,OAAOD,cAAP;AACA;;AAEM,SAASE,0BAAT,CAAoCb,QAApC,EAA8Cc,QAA9C,EAAwDC,MAAxD,EAAgE;EACtE;EACA;EACA;EACA;EACA;EACA,sDAAoBA,MAAM,CAACzB,KAAP,CAAa,EAAb,CAApB,2CAAsC;IAAA,IAA3B0B,KAA2B;;IACrC;IACA;IACA;IACA;IACA,IAAIhB,QAAQ,CAACF,KAAT,CAAegB,QAAQ,GAAG,CAA1B,EAA6BG,MAA7B,CAAoCjC,yBAApC,IAAiE,CAArE,EAAwE;MACvE;IACA;;IACD8B,QAAQ,GAAGd,QAAQ,CAACiB,MAAT,CAAgBjC,yBAAhB,CAAX;IACAgB,QAAQ,GAAGA,QAAQ,CAACkB,OAAT,CAAiBlC,yBAAjB,EAA4CgC,KAA5C,CAAX;EACA;;EACD,OAAO,CAAChB,QAAD,EAAWc,QAAX,CAAP;AACA"}