{"version": 3, "file": "parsePhoneNumber_.js", "names": ["parsePhoneNumber", "text", "options", "metadata", "defaultCountry", "isSupportedCountry", "undefined", "parsePhoneNumberWithError", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../source/parsePhoneNumber_.js"], "sourcesContent": ["import parsePhoneNumberWithError from './parsePhoneNumberWithError_.js'\r\nimport ParseError from './ParseError.js'\r\nimport { isSupportedCountry } from './metadata.js'\r\n\r\nexport default function parsePhoneNumber(text, options, metadata) {\r\n\t// Validate `defaultCountry`.\r\n\tif (options && options.defaultCountry && !isSupportedCountry(options.defaultCountry, metadata)) {\r\n\t\toptions = {\r\n\t\t\t...options,\r\n\t\t\tdefaultCountry: undefined\r\n\t\t}\r\n\t}\r\n\t// Parse phone number.\r\n\ttry {\r\n\t\treturn parsePhoneNumberWithError(text, options, metadata)\r\n\t} catch (error) {\r\n\t\t/* istanbul ignore else */\r\n\t\tif (error instanceof ParseError) {\r\n\t\t\t//\r\n\t\t} else {\r\n\t\t\tthrow error\r\n\t\t}\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;;;;;;;;;AAEe,SAASA,gBAAT,CAA0BC,IAA1B,EAAgCC,OAAhC,EAAyCC,QAAzC,EAAmD;EACjE;EACA,IAAID,OAAO,IAAIA,OAAO,CAACE,cAAnB,IAAqC,CAAC,IAAAC,4BAAA,EAAmBH,OAAO,CAACE,cAA3B,EAA2CD,QAA3C,CAA1C,EAAgG;IAC/FD,OAAO,mCACHA,OADG;MAENE,cAAc,EAAEE;IAFV,EAAP;EAIA,CAPgE,CAQjE;;;EACA,IAAI;IACH,OAAO,IAAAC,sCAAA,EAA0BN,IAA1B,EAAgCC,OAAhC,EAAyCC,QAAzC,CAAP;EACA,CAFD,CAEE,OAAOK,KAAP,EAAc;IACf;IACA,IAAIA,KAAK,YAAYC,sBAArB,EAAiC,CAChC;IACA,CAFD,MAEO;MACN,MAAMD,KAAN;IACA;EACD;AACD"}