{"version": 3, "file": "IsString.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsString.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,SAAS,GAAG,UAAU,CAAC;AAEpC;;GAEG;AACH,SAAgB,QAAQ,CAAC,KAAc;IACrC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;AAC9D,CAAC;AAFD,4BAEC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,iBAAS;QACf,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YACnD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,4BAA4B,EAAE,iBAAiB,CAAC;SACzG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,4BAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_STRING = 'isString';\n\n/**\n * Checks if a given value is a real string.\n */\nexport function isString(value: unknown): value is string {\n  return value instanceof String || typeof value === 'string';\n}\n\n/**\n * Checks if a given value is a real string.\n */\nexport function IsString(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_STRING,\n      validator: {\n        validate: (value, args): boolean => isString(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a string', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}