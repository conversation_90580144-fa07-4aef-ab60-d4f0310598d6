import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { Decimal } from 'decimal.js';
import { Transfer, TransferStatus, TransferType } from '../entities/transfer.entity';
import { CreateTransferDto } from '../dto/create-transfer.dto';

export interface FindTransfersOptions {
  page?: number;
  limit?: number;
  status?: TransferStatus;
  transferType?: TransferType;
  senderId?: string;
  receiverId?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface PaginatedTransfers {
  data: Transfer[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TransferQuote {
  sendAmount: string;
  sendCurrency: string;
  receiveAmount: string;
  receiveCurrency: string;
  exchangeRate: string;
  transferFee: string;
  exchangeFee: string;
  totalFees: string;
  totalAmount: string;
  estimatedDelivery: Date;
}

@Injectable()
export class TransfersService {
  private readonly logger = new Logger(TransfersService.name);

  constructor(
    @InjectRepository(Transfer)
    private readonly transferRepository: Repository<Transfer>,
  ) {}

  async create(senderId: string, createTransferDto: CreateTransferDto): Promise<Transfer> {
    const {
      sendAmount,
      sendCurrency,
      receiveCurrency,
      transferType,
      purpose,
      receiverId,
      receiverName,
      receiverPhone,
      receiverEmail,
      receiverAddress,
      description,
      paymentDetails,
      deliveryDetails,
      metadata,
    } = createTransferDto;

    // التحقق من صحة البيانات
    if (!receiverId && !receiverName) {
      throw new BadRequestException('يجب تحديد معرف المستقبل أو اسمه');
    }

    // الحصول على عرض سعر
    const quote = await this.getQuote(
      sendAmount.toString(),
      sendCurrency,
      receiveCurrency,
      transferType,
    );

    // إنشاء رقم مرجعي
    const referenceNumber = this.generateReferenceNumber();

    // إنشاء التحويل
    const transfer = this.transferRepository.create({
      referenceNumber,
      senderId,
      receiverId,
      receiverName,
      receiverPhone,
      receiverEmail,
      receiverAddress,
      transferType,
      purpose,
      description,
      sendAmount: quote.sendAmount,
      sendCurrency: quote.sendCurrency,
      receiveAmount: quote.receiveAmount,
      receiveCurrency: quote.receiveCurrency,
      exchangeRate: quote.exchangeRate,
      transferFee: quote.transferFee,
      exchangeFee: quote.exchangeFee,
      totalFees: quote.totalFees,
      paymentMethod: paymentDetails.method,
      paymentDetails,
      deliveryMethod: deliveryDetails.method,
      deliveryDetails,
      expectedDelivery: quote.estimatedDelivery,
      metadata,
      status: TransferStatus.PENDING,
    });

    const savedTransfer = await this.transferRepository.save(transfer);
    this.logger.log(`Transfer created: ${savedTransfer.referenceNumber}`);

    return savedTransfer;
  }

  async findAll(options: FindTransfersOptions = {}): Promise<PaginatedTransfers> {
    const {
      page = 1,
      limit = 10,
      status,
      transferType,
      senderId,
      receiverId,
      dateFrom,
      dateTo,
    } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.transferRepository.createQueryBuilder('transfer');

    // تصفية حسب الحالة
    if (status) {
      queryBuilder.andWhere('transfer.status = :status', { status });
    }

    // تصفية حسب نوع التحويل
    if (transferType) {
      queryBuilder.andWhere('transfer.transferType = :transferType', { transferType });
    }

    // تصفية حسب المرسل
    if (senderId) {
      queryBuilder.andWhere('transfer.senderId = :senderId', { senderId });
    }

    // تصفية حسب المستقبل
    if (receiverId) {
      queryBuilder.andWhere('transfer.receiverId = :receiverId', { receiverId });
    }

    // تصفية حسب التاريخ
    if (dateFrom) {
      queryBuilder.andWhere('transfer.createdAt >= :dateFrom', { dateFrom });
    }

    if (dateTo) {
      queryBuilder.andWhere('transfer.createdAt <= :dateTo', { dateTo });
    }

    // ترتيب وتصفح
    queryBuilder
      .orderBy('transfer.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findById(id: string): Promise<Transfer> {
    const transfer = await this.transferRepository.findOne({
      where: { id },
    });

    if (!transfer) {
      throw new NotFoundException('التحويل غير موجود');
    }

    return transfer;
  }

  async findByReference(referenceNumber: string): Promise<Transfer> {
    const transfer = await this.transferRepository.findOne({
      where: { referenceNumber },
    });

    if (!transfer) {
      throw new NotFoundException('التحويل غير موجود');
    }

    return transfer;
  }

  async updateStatus(
    id: string,
    status: TransferStatus,
    reason?: string,
    reviewedBy?: string,
  ): Promise<Transfer> {
    const transfer = await this.findById(id);

    // التحقق من إمكانية تغيير الحالة
    if (!this.canUpdateStatus(transfer.status, status)) {
      throw new BadRequestException(`لا يمكن تغيير الحالة من ${transfer.status} إلى ${status}`);
    }

    transfer.updateStatus(status, reason);

    if (reviewedBy) {
      transfer.reviewedBy = reviewedBy;
      transfer.reviewedAt = new Date();
    }

    const updatedTransfer = await this.transferRepository.save(transfer);
    this.logger.log(`Transfer status updated: ${transfer.referenceNumber} -> ${status}`);

    return updatedTransfer;
  }

  async cancel(id: string, userId: string, reason?: string): Promise<Transfer> {
    const transfer = await this.findById(id);

    // التحقق من الصلاحية
    if (transfer.senderId !== userId) {
      throw new ForbiddenException('غير مصرح لك بإلغاء هذا التحويل');
    }

    if (!transfer.canCancel) {
      throw new BadRequestException('لا يمكن إلغاء هذا التحويل في الحالة الحالية');
    }

    return this.updateStatus(id, TransferStatus.CANCELLED, reason);
  }

  async getQuote(
    sendAmount: string,
    sendCurrency: string,
    receiveCurrency: string,
    transferType: TransferType,
  ): Promise<TransferQuote> {
    const amount = new Decimal(sendAmount);

    // الحصول على سعر الصرف (محاكاة)
    const exchangeRate = await this.getExchangeRate(sendCurrency, receiveCurrency);
    const receiveAmount = amount.mul(exchangeRate);

    // حساب الرسوم
    const fees = this.calculateFees(amount, sendCurrency, receiveCurrency, transferType);

    // حساب المبلغ الإجمالي
    const totalAmount = amount.plus(fees.totalFees);

    // تقدير وقت التسليم
    const estimatedDelivery = this.calculateEstimatedDelivery(transferType);

    return {
      sendAmount: amount.toString(),
      sendCurrency,
      receiveAmount: receiveAmount.toFixed(2),
      receiveCurrency,
      exchangeRate: exchangeRate.toString(),
      transferFee: fees.transferFee.toString(),
      exchangeFee: fees.exchangeFee.toString(),
      totalFees: fees.totalFees.toString(),
      totalAmount: totalAmount.toString(),
      estimatedDelivery,
    };
  }

  async getUserTransfers(userId: string, options: FindTransfersOptions = {}): Promise<PaginatedTransfers> {
    return this.findAll({
      ...options,
      senderId: userId,
    });
  }

  async getTransferStats(userId?: string): Promise<any> {
    const queryBuilder = this.transferRepository.createQueryBuilder('transfer');

    if (userId) {
      queryBuilder.where('transfer.senderId = :userId', { userId });
    }

    const [
      totalTransfers,
      completedTransfers,
      pendingTransfers,
      totalVolume,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('transfer.status = :status', { status: TransferStatus.COMPLETED }).getCount(),
      queryBuilder.clone().andWhere('transfer.status = :status', { status: TransferStatus.PENDING }).getCount(),
      queryBuilder.clone().select('SUM(CAST(transfer.sendAmount AS DECIMAL))', 'total').getRawOne(),
    ]);

    return {
      totalTransfers,
      completedTransfers,
      pendingTransfers,
      totalVolume: totalVolume?.total || '0',
      successRate: totalTransfers > 0 ? (completedTransfers / totalTransfers) * 100 : 0,
    };
  }

  private async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<Decimal> {
    // محاكاة الحصول على سعر الصرف من API خارجي
    // في التطبيق الحقيقي، يجب الاتصال بـ API أسعار الصرف
    
    if (fromCurrency === toCurrency) {
      return new Decimal(1);
    }

    // أسعار وهمية للاختبار
    const rates = {
      'SAR-USD': 0.2667,
      'USD-SAR': 3.75,
      'SAR-EUR': 0.2444,
      'EUR-SAR': 4.09,
      'USD-EUR': 0.9167,
      'EUR-USD': 1.091,
    };

    const rateKey = `${fromCurrency}-${toCurrency}`;
    const rate = rates[rateKey];

    if (!rate) {
      throw new BadRequestException(`سعر الصرف غير متوفر للعملات ${fromCurrency} إلى ${toCurrency}`);
    }

    return new Decimal(rate);
  }

  private calculateFees(
    amount: Decimal,
    sendCurrency: string,
    receiveCurrency: string,
    transferType: TransferType,
  ): { transferFee: Decimal; exchangeFee: Decimal; totalFees: Decimal } {
    // حساب رسوم التحويل
    let transferFeeRate = new Decimal(0.01); // 1% افتراضي

    switch (transferType) {
      case TransferType.DOMESTIC:
        transferFeeRate = new Decimal(0.005); // 0.5%
        break;
      case TransferType.INTERNATIONAL:
        transferFeeRate = new Decimal(0.02); // 2%
        break;
      case TransferType.WALLET_TO_WALLET:
        transferFeeRate = new Decimal(0.001); // 0.1%
        break;
    }

    const transferFee = amount.mul(transferFeeRate);

    // حساب رسوم الصرف
    let exchangeFee = new Decimal(0);
    if (sendCurrency !== receiveCurrency) {
      exchangeFee = amount.mul(0.005); // 0.5% رسوم صرف
    }

    const totalFees = transferFee.plus(exchangeFee);

    return {
      transferFee,
      exchangeFee,
      totalFees,
    };
  }

  private calculateEstimatedDelivery(transferType: TransferType): Date {
    const now = new Date();
    let hoursToAdd = 24; // افتراضي 24 ساعة

    switch (transferType) {
      case TransferType.WALLET_TO_WALLET:
        hoursToAdd = 0; // فوري
        break;
      case TransferType.DOMESTIC:
        hoursToAdd = 2; // ساعتين
        break;
      case TransferType.INTERNATIONAL:
        hoursToAdd = 48; // يومين
        break;
      case TransferType.BANK_TRANSFER:
        hoursToAdd = 24; // يوم واحد
        break;
      case TransferType.CASH_PICKUP:
        hoursToAdd = 1; // ساعة واحدة
        break;
    }

    return new Date(now.getTime() + hoursToAdd * 60 * 60 * 1000);
  }

  private canUpdateStatus(currentStatus: TransferStatus, newStatus: TransferStatus): boolean {
    const allowedTransitions = {
      [TransferStatus.PENDING]: [
        TransferStatus.PROCESSING,
        TransferStatus.CANCELLED,
        TransferStatus.ON_HOLD,
      ],
      [TransferStatus.PROCESSING]: [
        TransferStatus.COMPLETED,
        TransferStatus.FAILED,
        TransferStatus.ON_HOLD,
      ],
      [TransferStatus.ON_HOLD]: [
        TransferStatus.PROCESSING,
        TransferStatus.CANCELLED,
      ],
      [TransferStatus.COMPLETED]: [
        TransferStatus.REFUNDED,
      ],
      [TransferStatus.FAILED]: [
        TransferStatus.PROCESSING,
      ],
      [TransferStatus.CANCELLED]: [],
      [TransferStatus.REFUNDED]: [],
    };

    return allowedTransitions[currentStatus]?.includes(newStatus) || false;
  }

  private generateReferenceNumber(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `WS${timestamp}${random}`.toUpperCase();
  }
}
