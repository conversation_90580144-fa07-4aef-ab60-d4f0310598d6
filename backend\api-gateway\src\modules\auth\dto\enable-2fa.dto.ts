import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, IsPhoneNumber, IsOptional } from 'class-validator';

export enum TwoFactorMethod {
  SMS = 'sms',
  EMAIL = 'email',
  AUTHENTICATOR = 'authenticator',
}

export class Enable2FaDto {
  @ApiProperty({
    description: 'طريقة المصادقة الثنائية',
    enum: TwoFactorMethod,
    example: TwoFactorMethod.SMS,
  })
  @IsEnum(TwoFactorMethod, { message: 'طريقة المصادقة الثنائية غير صحيحة' })
  @IsNotEmpty({ message: 'طريقة المصادقة الثنائية مطلوبة' })
  method: TwoFactorMethod;

  @ApiProperty({
    description: 'رقم الهاتف (مطلوب للـ SMS)',
    example: '+966501234567',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber('SA', { message: 'رقم الهاتف غير صحيح' })
  phone?: string;

  @ApiProperty({
    description: 'رمز التحقق من التطبيق (مطلوب للـ Authenticator)',
    example: '123456',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رمز التحقق يجب أن يكون نص' })
  verificationCode?: string;
}
