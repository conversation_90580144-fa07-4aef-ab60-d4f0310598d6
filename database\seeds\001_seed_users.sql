-- =====================================================
-- WS Transfir - User Seeds
-- بذور بيانات المستخدمين لنظام WS Transfir
-- =====================================================

-- إدراج مستخدمين تجريبيين
INSERT INTO users (
    id,
    first_name,
    last_name,
    email,
    phone,
    password,
    email_verified,
    phone_verified,
    status,
    role,
    kyc_status,
    preferred_language,
    preferred_currency,
    created_at,
    updated_at
) VALUES 
-- مدير النظام
(
    'admin-uuid-1234-5678-9012-************',
    'أحمد',
    'الإدارة',
    '<EMAIL>',
    '+************',
    '$2b$12$LQv3c1yqBwlVHpPjrGNDUOzw5/HCUDVUHlDkHM/VEHeIyCkOFrugO', -- password: Admin123!
    true,
    true,
    'active',
    'super_admin',
    'approved',
    'ar',
    'SAR',
    NOW(),
    NOW()
),

-- مستخدم عادي - محمد
(
    'user-uuid-1111-2222-3333-************',
    'محمد',
    'أحمد',
    '<EMAIL>',
    '+************',
    '$2b$12$LQv3c1yqBwlVHpPjrGNDUOzw5/HCUDVUHlDkHM/VEHeIyCkOFrugO', -- password: User123!
    true,
    true,
    'active',
    'user',
    'approved',
    'ar',
    'SAR',
    NOW(),
    NOW()
),

-- مستخدم عادي - فاطمة
(
    'user-uuid-2222-**************55555555',
    'فاطمة',
    'علي',
    '<EMAIL>',
    '+************',
    '$2b$12$LQv3c1yqBwlVHpPjrGNDUOzw5/HCUDVUHlDkHM/VEHeIyCkOFrugO', -- password: User123!
    true,
    true,
    'active',
    'user',
    'approved',
    'ar',
    'SAR',
    NOW(),
    NOW()
),

-- وكيل
(
    'agent-uuid-**************-************',
    'خالد',
    'السعيد',
    '<EMAIL>',
    '+************',
    '$2b$12$LQv3c1yqBwlVHpPjrGNDUOzw5/HCUDVUHlDkHM/VEHeIyCkOFrugO', -- password: Agent123!
    true,
    true,
    'active',
    'agent',
    'approved',
    'ar',
    'SAR',
    NOW(),
    NOW()
),

-- مستخدم في انتظار التفعيل
(
    'pending-uuid-4444-**************77777777',
    'سارة',
    'محمود',
    '<EMAIL>',
    '+************',
    '$2b$12$LQv3c1yqBwlVHpPjrGNDUOzw5/HCUDVUHlDkHM/VEHeIyCkOFrugO', -- password: User123!
    false,
    false,
    'pending',
    'user',
    'not_started',
    'ar',
    'SAR',
    NOW(),
    NOW()
),

-- مستخدم دولي
(
    'intl-uuid-**************-************',
    'John',
    'Smith',
    '<EMAIL>',
    '+1234567890',
    '$2b$12$LQv3c1yqBwlVHpPjrGNDUOzw5/HCUDVUHlDkHM/VEHeIyCkOFrugO', -- password: User123!
    true,
    true,
    'active',
    'user',
    'approved',
    'en',
    'USD',
    NOW(),
    NOW()
);

-- إدراج ملفات شخصية للمستخدمين
INSERT INTO user_profiles (
    id,
    user_id,
    address_line1,
    city,
    country,
    postal_code,
    occupation,
    monthly_income,
    source_of_income,
    created_at,
    updated_at
) VALUES 
-- ملف المدير
(
    'profile-admin-1234-5678-9012-************',
    'admin-uuid-1234-5678-9012-************',
    'شارع الملك فهد، حي العليا',
    'الرياض',
    'SA',
    '12345',
    'مدير نظم',
    15000.00,
    'راتب',
    NOW(),
    NOW()
),

-- ملف محمد
(
    'profile-user-1111-2222-3333-************',
    'user-uuid-1111-2222-3333-************',
    'شارع الأمير سلطان، حي الملز',
    'الرياض',
    'SA',
    '11564',
    'مهندس',
    8000.00,
    'راتب',
    NOW(),
    NOW()
),

-- ملف فاطمة
(
    'profile-user-2222-**************55555555',
    'user-uuid-2222-**************55555555',
    'شارع التحلية، حي السليمانية',
    'الرياض',
    'SA',
    '11321',
    'طبيبة',
    12000.00,
    'راتب',
    NOW(),
    NOW()
),

-- ملف الوكيل
(
    'profile-agent-**************-************',
    'agent-uuid-**************-************',
    'شارع العروبة، حي الشفا',
    'الرياض',
    'SA',
    '11142',
    'وكيل تحويلات',
    6000.00,
    'عمولات',
    NOW(),
    NOW()
),

-- ملف المستخدم الدولي
(
    'profile-intl-**************-************',
    'intl-uuid-**************-************',
    '123 Main Street',
    'New York',
    'US',
    '10001',
    'Software Engineer',
    5000.00,
    'Salary',
    NOW(),
    NOW()
);

-- إدراج محافظ للمستخدمين
INSERT INTO wallets (
    id,
    wallet_number,
    user_id,
    type,
    currency,
    name,
    available_balance,
    total_balance,
    status,
    created_at,
    updated_at
) VALUES 
-- محفظة المدير - ريال سعودي
(
    'wallet-admin-1234-5678-9012-************',
    'SAR202412250001',
    'admin-uuid-1234-5678-9012-************',
    'personal',
    'SAR',
    'المحفظة الرئيسية',
    50000.00,
    50000.00,
    'active',
    NOW(),
    NOW()
),

-- محفظة محمد - ريال سعودي
(
    'wallet-user1-1111-2222-3333-************',
    'SAR202412250002',
    'user-uuid-1111-2222-3333-************',
    'personal',
    'SAR',
    'محفظة محمد',
    1000.00,
    1000.00,
    'active',
    NOW(),
    NOW()
),

-- محفظة فاطمة - ريال سعودي
(
    'wallet-user2-2222-**************55555555',
    'SAR202412250003',
    'user-uuid-2222-**************55555555',
    'personal',
    'SAR',
    'محفظة فاطمة',
    2500.00,
    2500.00,
    'active',
    NOW(),
    NOW()
),

-- محفظة الوكيل - ريال سعودي
(
    'wallet-agent-**************-************',
    'SAR202412250004',
    'agent-uuid-**************-************',
    'business',
    'SAR',
    'محفظة الوكيل',
    10000.00,
    10000.00,
    'active',
    NOW(),
    NOW()
),

-- محفظة المستخدم الدولي - دولار أمريكي
(
    'wallet-intl-**************-************',
    'USD202412250005',
    'intl-uuid-**************-************',
    'personal',
    'USD',
    'John Main Wallet',
    500.00,
    500.00,
    'active',
    NOW(),
    NOW()
);

-- إدراج تحويلات تجريبية
INSERT INTO transfers (
    id,
    reference_number,
    sender_id,
    receiver_id,
    transfer_type,
    purpose,
    description,
    send_amount,
    send_currency,
    receive_amount,
    receive_currency,
    exchange_rate,
    transfer_fee,
    total_fees,
    status,
    created_at,
    updated_at,
    completed_at
) VALUES 
-- تحويل من محمد إلى فاطمة
(
    'transfer-1111-2222-3333-************',
    'WS20241225001',
    'user-uuid-1111-2222-3333-************',
    'user-uuid-2222-**************55555555',
    'wallet_to_wallet',
    'family_support',
    'مساعدة عائلية',
    500.00,
    'SAR',
    500.00,
    'SAR',
    1.000000,
    5.00,
    5.00,
    'completed',
    NOW() - INTERVAL '2 days',
    NOW() - INTERVAL '2 days',
    NOW() - INTERVAL '2 days'
),

-- تحويل دولي من John إلى محمد
(
    'transfer-2222-**************55555555',
    'WS20241225002',
    'intl-uuid-**************-************',
    'user-uuid-1111-2222-3333-************',
    'international',
    'business',
    'دفعة عمل',
    100.00,
    'USD',
    375.00,
    'SAR',
    3.750000,
    10.00,
    10.00,
    'completed',
    NOW() - INTERVAL '1 day',
    NOW() - INTERVAL '1 day',
    NOW() - INTERVAL '1 day'
),

-- تحويل معلق
(
    'transfer-**************-************',
    'WS20241225003',
    'user-uuid-2222-**************55555555',
    'user-uuid-1111-2222-3333-************',
    'wallet_to_wallet',
    'gift',
    'هدية',
    200.00,
    'SAR',
    200.00,
    'SAR',
    1.000000,
    2.00,
    2.00,
    'pending',
    NOW(),
    NOW(),
    NULL
);

-- تحديث أرصدة المحافظ بعد التحويلات
UPDATE wallets SET 
    available_balance = 495.00,
    total_balance = 495.00,
    updated_at = NOW()
WHERE id = 'wallet-user1-1111-2222-3333-************';

UPDATE wallets SET 
    available_balance = 2700.00,
    total_balance = 2700.00,
    updated_at = NOW()
WHERE id = 'wallet-user2-2222-**************55555555';

UPDATE wallets SET 
    available_balance = 490.00,
    total_balance = 490.00,
    updated_at = NOW()
WHERE id = 'wallet-intl-**************-************';

-- إضافة بعض الإحصائيات
INSERT INTO system_stats (
    stat_name,
    stat_value,
    stat_date,
    created_at
) VALUES 
('total_users', '6', CURRENT_DATE, NOW()),
('total_transfers', '3', CURRENT_DATE, NOW()),
('total_volume_sar', '1075.00', CURRENT_DATE, NOW()),
('total_volume_usd', '100.00', CURRENT_DATE, NOW()),
('active_wallets', '5', CURRENT_DATE, NOW());

COMMIT;
