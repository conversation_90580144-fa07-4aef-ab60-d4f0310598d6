import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserProfile } from '../entities/user-profile.entity';
import { CreateProfileDto } from '../dto/create-profile.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';

@Injectable()
export class ProfileService {
  private readonly logger = new Logger(ProfileService.name);

  constructor(
    @InjectRepository(UserProfile)
    private readonly profileRepository: Repository<UserProfile>,
  ) {}

  async create(userId: string, createProfileDto: CreateProfileDto): Promise<UserProfile> {
    // التحقق من عدم وجود ملف شخصي مسبقاً
    const existingProfile = await this.profileRepository.findOne({
      where: { userId },
    });

    if (existingProfile) {
      throw new BadRequestException('الملف الشخصي موجود بالفعل لهذا المستخدم');
    }

    // إنشاء الملف الشخصي
    const profile = this.profileRepository.create({
      ...createProfileDto,
      userId,
      isComplete: this.checkProfileCompleteness(createProfileDto),
    });

    const savedProfile = await this.profileRepository.save(profile);
    this.logger.log(`Profile created for user: ${userId}`);

    return savedProfile;
  }

  async findByUserId(userId: string): Promise<UserProfile> {
    const profile = await this.profileRepository.findOne({
      where: { userId },
      relations: ['user'],
    });

    if (!profile) {
      throw new NotFoundException('الملف الشخصي غير موجود');
    }

    return profile;
  }

  async update(userId: string, updateProfileDto: UpdateProfileDto): Promise<UserProfile> {
    const profile = await this.findByUserId(userId);

    // تحديث البيانات
    Object.assign(profile, updateProfileDto);
    
    // إعادة فحص اكتمال الملف الشخصي
    profile.isComplete = this.checkProfileCompleteness(profile);
    profile.lastUpdated = new Date();

    const updatedProfile = await this.profileRepository.save(profile);
    this.logger.log(`Profile updated for user: ${userId}`);

    return updatedProfile;
  }

  async remove(userId: string): Promise<void> {
    const profile = await this.findByUserId(userId);
    
    await this.profileRepository.remove(profile);
    this.logger.log(`Profile removed for user: ${userId}`);
  }

  async verifyProfile(userId: string, isVerified: boolean = true): Promise<UserProfile> {
    const profile = await this.findByUserId(userId);
    
    profile.isVerified = isVerified;
    profile.verifiedAt = isVerified ? new Date() : null;
    
    const updatedProfile = await this.profileRepository.save(profile);
    this.logger.log(`Profile verification updated for user: ${userId} - ${isVerified}`);

    return updatedProfile;
  }

  async getProfileCompletionPercentage(userId: string): Promise<number> {
    const profile = await this.findByUserId(userId);
    
    const requiredFields = [
      'occupation',
      'monthlyIncome',
      'sourceOfIncome',
      'address',
      'emergencyContactPhone',
      'emergencyContactName',
    ];

    const completedFields = requiredFields.filter(field => {
      const value = profile[field];
      if (field === 'address') {
        return value && value.addressLine1 && value.city && value.country;
      }
      return value !== null && value !== undefined && value !== '';
    });

    return Math.round((completedFields.length / requiredFields.length) * 100);
  }

  async getIncompleteProfiles(limit: number = 50): Promise<UserProfile[]> {
    return this.profileRepository.find({
      where: { isComplete: false },
      relations: ['user'],
      take: limit,
      order: { createdAt: 'DESC' },
    });
  }

  async getUnverifiedProfiles(limit: number = 50): Promise<UserProfile[]> {
    return this.profileRepository.find({
      where: { isVerified: false },
      relations: ['user'],
      take: limit,
      order: { createdAt: 'DESC' },
    });
  }

  async getProfileStats(): Promise<any> {
    const [
      totalProfiles,
      completeProfiles,
      verifiedProfiles,
      profilesWithIncome,
    ] = await Promise.all([
      this.profileRepository.count(),
      this.profileRepository.count({ where: { isComplete: true } }),
      this.profileRepository.count({ where: { isVerified: true } }),
      this.profileRepository.count({ 
        where: { monthlyIncome: { $ne: null } } as any 
      }),
    ]);

    return {
      totalProfiles,
      completeProfiles,
      verifiedProfiles,
      profilesWithIncome,
      completionRate: totalProfiles > 0 ? (completeProfiles / totalProfiles) * 100 : 0,
      verificationRate: totalProfiles > 0 ? (verifiedProfiles / totalProfiles) * 100 : 0,
    };
  }

  async searchProfiles(searchTerm: string, limit: number = 20): Promise<UserProfile[]> {
    return this.profileRepository
      .createQueryBuilder('profile')
      .leftJoinAndSelect('profile.user', 'user')
      .where(
        'profile.occupation ILIKE :search OR profile.employer ILIKE :search OR user.firstName ILIKE :search OR user.lastName ILIKE :search',
        { search: `%${searchTerm}%` }
      )
      .take(limit)
      .getMany();
  }

  private checkProfileCompleteness(profile: Partial<UserProfile>): boolean {
    const requiredFields = [
      'occupation',
      'monthlyIncome',
      'sourceOfIncome',
      'address',
      'emergencyContactPhone',
      'emergencyContactName',
    ];

    return requiredFields.every(field => {
      const value = profile[field];
      if (field === 'address') {
        return value && value.addressLine1 && value.city && value.country;
      }
      return value !== null && value !== undefined && value !== '';
    });
  }

  async updateProfilePicture(userId: string, pictureUrl: string): Promise<UserProfile> {
    const profile = await this.findByUserId(userId);
    
    if (!profile.metadata) {
      profile.metadata = {};
    }
    
    profile.metadata.profilePicture = pictureUrl;
    profile.lastUpdated = new Date();

    const updatedProfile = await this.profileRepository.save(profile);
    this.logger.log(`Profile picture updated for user: ${userId}`);

    return updatedProfile;
  }

  async addDocument(userId: string, documentType: string, documentUrl: string): Promise<UserProfile> {
    const profile = await this.findByUserId(userId);
    
    if (!profile.metadata) {
      profile.metadata = {};
    }
    
    if (!profile.metadata.documents) {
      profile.metadata.documents = {};
    }
    
    profile.metadata.documents[documentType] = {
      url: documentUrl,
      uploadedAt: new Date().toISOString(),
      verified: false,
    };
    
    profile.lastUpdated = new Date();

    const updatedProfile = await this.profileRepository.save(profile);
    this.logger.log(`Document ${documentType} added for user: ${userId}`);

    return updatedProfile;
  }
}
