version: 2

aliases:
  - &restore-cache
    restore_cache:
      key: dependency-cache-{{ checksum "package.json" }}
  - &install-deps
    run:
      name: Install dependencies
      command: npm ci --ignore-scripts
  - &build-packages
    run:
      name: Build
      command: npm run build

jobs:
  build:
    working_directory: ~/nest
    docker:
      - image: cimg/node:21.1
    steps:
      - checkout
      - run:
          name: Update NPM version
          command: sudo npm install -g npm@latest
      - restore_cache:
          key: dependency-cache-{{ checksum "package.json" }}
      - run:
          name: Install dependencies
          command: npm ci --ignore-scripts
      - save_cache:
          key: dependency-cache-{{ checksum "package.json" }}
          paths:
            - ./node_modules
      - run:
          name: Build
          command: npm run build
  test:
    working_directory: ~/nest
    docker:
      - image: cimg/node:21.1
    steps:
      - checkout
      - *restore-cache
      - *install-deps
      - run:
          name: Unit tests
          command: npm run test

workflows:
  version: 2
  build-and-test:
    jobs:
      - build
      - test:
          requires:
            - build
