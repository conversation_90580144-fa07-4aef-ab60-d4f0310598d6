"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EMOJIS = void 0;
const node_emoji_1 = require("node-emoji");
exports.EMOJIS = {
    HEART: (0, node_emoji_1.get)('heart'),
    COFFEE: (0, node_emoji_1.get)('coffee'),
    BEER: (0, node_emoji_1.get)('beer'),
    BROKEN_HEART: (0, node_emoji_1.get)('broken_heart'),
    CRYING: (0, node_emoji_1.get)('crying_cat_face'),
    HEART_EYES: (0, node_emoji_1.get)('heart_eyes_cat'),
    JOY: (0, node_emoji_1.get)('joy_cat'),
    KISSING: (0, node_emoji_1.get)('kissing_cat'),
    SCREAM: (0, node_emoji_1.get)('scream_cat'),
    ROCKET: (0, node_emoji_1.get)('rocket'),
    SMIRK: (0, node_emoji_1.get)('smirk_cat'),
    RAISED_HANDS: (0, node_emoji_1.get)('raised_hands'),
    POINT_RIGHT: (0, node_emoji_1.get)('point_right'),
    ZAP: (0, node_emoji_1.get)('zap'),
    BOOM: (0, node_emoji_1.get)('boom'),
    PRAY: (0, node_emoji_1.get)('pray'),
    WINE: (0, node_emoji_1.get)('wine_glass'),
};
