{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestGuard", "title": "Nest Guard Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the guard.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the guard?"}, "path": {"type": "string", "format": "path", "description": "The path to create the guard."}, "language": {"type": "string", "description": "Nest guard language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest guard source root directory."}, "flat": {"type": "boolean", "default": true, "description": "Flag to indicate if a directory is created."}, "spec": {"type": "boolean", "default": true, "description": "Specifies if a spec file is generated."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}}, "required": ["name"]}