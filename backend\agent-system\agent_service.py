"""
Agent Management Service
=======================
خدمة إدارة الوكلاء المتقدمة
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

import asyncpg # type: ignore
from ..shared.database.connection import DatabaseConnection # type: ignore

logger = logging.getLogger(__name__)


class AgentType(Enum):
    """أنواع الوكلاء"""
    INDIVIDUAL = "individual"
    BUSINESS = "business"
    CORPORATE = "corporate"


class AgentStatus(Enum):
    """حالات الوكيل"""
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"


class CommissionType(Enum):
    """أنواع العمولات"""
    DIRECT = "direct"
    TIER = "tier"
    VOLUME_BONUS = "volume_bonus"
    SPECIAL = "special"


@dataclass
class AgentProfile:
    """ملف الوكيل"""
    id: str
    user_id: str
    agent_code: str
    agent_type: AgentType
    status: AgentStatus
    parent_agent_id: Optional[str]
    level: int
    hierarchy_path: str
    business_name: Optional[str]
    region: str
    city: str
    base_commission_rate: Decimal
    tier_commission_rate: Decimal
    volume_bonus_rate: Decimal
    daily_transaction_limit: Decimal
    monthly_transaction_limit: Decimal
    single_transaction_limit: Decimal
    total_transactions: int
    total_volume: Decimal
    total_commission_earned: Decimal
    customer_count: int
    rating: Decimal
    review_count: int
    training_completed: bool
    certification_level: int
    onboarding_completed: bool
    created_at: datetime
    updated_at: datetime


@dataclass
class AgentCommission:
    """عمولة الوكيل"""
    id: str
    agent_id: str
    transaction_id: str
    commission_type: CommissionType
    commission_rate: Decimal
    transaction_amount: Decimal
    commission_amount: Decimal
    status: str
    commission_period: str
    due_date: date
    created_at: datetime


@dataclass
class AgentPerformance:
    """أداء الوكيل"""
    id: str
    agent_id: str
    period_type: str
    period_start: date
    period_end: date
    transaction_count: int
    transaction_volume: Decimal
    average_transaction_amount: Decimal
    total_commission: Decimal
    direct_commission: Decimal
    tier_commission: Decimal
    bonus_commission: Decimal
    new_customers: int
    active_customers: int
    customer_retention_rate: Decimal
    success_rate: Decimal
    average_processing_time: int
    customer_satisfaction: Decimal
    complaint_count: int
    regional_rank: Optional[int]
    national_rank: Optional[int]
    volume_target: Decimal
    volume_achievement_rate: Decimal
    transaction_target: int
    transaction_achievement_rate: Decimal


class AgentService:
    """خدمة إدارة الوكلاء"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Configuration
        self.max_hierarchy_levels = 10
        self.default_commission_rates = {
            'base': Decimal('0.0100'),  # 1%
            'tier': Decimal('0.0050'),  # 0.5%
            'volume_bonus': Decimal('0.0025')  # 0.25%
        }
        
        # Statistics
        self.total_agents = 0
        self.active_agents = 0
        self.pending_agents = 0
        self.total_commissions_paid = Decimal('0')
    
    async def create_agent_profile(
        self,
        user_id: str,
        agent_data: Dict[str, Any],
        created_by: str
    ) -> Optional[AgentProfile]:
        """إنشاء ملف وكيل جديد"""
        try:
            logger.info(f"🏢 Creating agent profile for user: {user_id}")
            
            # Validate user exists and has agent role
            user_valid = await self._validate_user_for_agent(user_id)
            if not user_valid:
                logger.error(f"❌ User {user_id} is not valid for agent creation")
                return None
            
            # Validate parent agent if specified
            parent_agent_id = agent_data.get('parent_agent_id')
            if parent_agent_id:
                parent_valid = await self._validate_parent_agent(parent_agent_id)
                if not parent_valid:
                    logger.error(f"❌ Parent agent {parent_agent_id} is not valid")
                    return None
            
            async with self.db_connection.get_connection() as conn:
                # Insert agent profile
                query = """
                    INSERT INTO agent_profiles (
                        user_id, agent_type, parent_agent_id, business_name,
                        region, city, district, address, primary_phone,
                        business_email, bank_name, bank_account_number, iban,
                        base_commission_rate, tier_commission_rate, volume_bonus_rate,
                        daily_transaction_limit, monthly_transaction_limit,
                        single_transaction_limit, created_by
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13,
                        $14, $15, $16, $17, $18, $19, $20
                    ) RETURNING *
                """
                
                row = await conn.fetchrow(
                    query,
                    user_id,
                    agent_data.get('agent_type', 'individual'),
                    parent_agent_id,
                    agent_data.get('business_name'),
                    agent_data.get('region'),
                    agent_data.get('city'),
                    agent_data.get('district'),
                    agent_data.get('address'),
                    agent_data.get('primary_phone'),
                    agent_data.get('business_email'),
                    agent_data.get('bank_name'),
                    agent_data.get('bank_account_number'),
                    agent_data.get('iban'),
                    agent_data.get('base_commission_rate', self.default_commission_rates['base']),
                    agent_data.get('tier_commission_rate', self.default_commission_rates['tier']),
                    agent_data.get('volume_bonus_rate', self.default_commission_rates['volume_bonus']),
                    agent_data.get('daily_transaction_limit', 100000),
                    agent_data.get('monthly_transaction_limit', 2000000),
                    agent_data.get('single_transaction_limit', 50000),
                    created_by
                )
                
                if row:
                    # Create initial performance record
                    await self._create_initial_performance_record(row['id'])
                    
                    # Update statistics
                    self.total_agents += 1
                    self.pending_agents += 1
                    
                    logger.info(f"✅ Agent profile created: {row['agent_code']}")
                    
                    return AgentProfile(
                        id=row['id'],
                        user_id=row['user_id'],
                        agent_code=row['agent_code'],
                        agent_type=AgentType(row['agent_type']),
                        status=AgentStatus(row['status']),
                        parent_agent_id=row['parent_agent_id'],
                        level=row['level'],
                        hierarchy_path=row['hierarchy_path'],
                        business_name=row['business_name'],
                        region=row['region'],
                        city=row['city'],
                        base_commission_rate=row['base_commission_rate'],
                        tier_commission_rate=row['tier_commission_rate'],
                        volume_bonus_rate=row['volume_bonus_rate'],
                        daily_transaction_limit=row['daily_transaction_limit'],
                        monthly_transaction_limit=row['monthly_transaction_limit'],
                        single_transaction_limit=row['single_transaction_limit'],
                        total_transactions=row['total_transactions'],
                        total_volume=row['total_volume'],
                        total_commission_earned=row['total_commission_earned'],
                        customer_count=row['customer_count'],
                        rating=row['rating'],
                        review_count=row['review_count'],
                        training_completed=row['training_completed'],
                        certification_level=row['certification_level'],
                        onboarding_completed=row['onboarding_completed'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to create agent profile: {e}")
            return None
    
    async def approve_agent(
        self,
        agent_id: str,
        approved_by: str,
        approval_notes: str = None
    ) -> bool:
        """الموافقة على الوكيل"""
        try:
            logger.info(f"✅ Approving agent: {agent_id}")
            
            async with self.db_connection.get_connection() as conn:
                # Update agent status
                query = """
                    UPDATE agent_profiles 
                    SET status = 'active',
                        approved_by = $2,
                        approved_at = CURRENT_TIMESTAMP,
                        notes = COALESCE(notes || E'\n', '') || $3,
                        updated_by = $2,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = $1 AND status = 'pending'
                    RETURNING agent_code
                """
                
                result = await conn.fetchval(query, agent_id, approved_by, approval_notes)
                
                if result:
                    # Update statistics
                    self.active_agents += 1
                    self.pending_agents -= 1
                    
                    logger.info(f"✅ Agent approved: {result}")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to approve agent: {e}")
            return False
    
    async def calculate_commission(
        self,
        agent_id: str,
        transaction_id: str,
        transaction_amount: Decimal,
        commission_type: CommissionType = CommissionType.DIRECT
    ) -> Optional[AgentCommission]:
        """حساب عمولة الوكيل"""
        try:
            logger.info(f"💰 Calculating commission for agent: {agent_id}")
            
            # Get agent profile
            agent = await self.get_agent_profile(agent_id)
            if not agent or agent.status != AgentStatus.ACTIVE:
                logger.error(f"❌ Agent not found or not active: {agent_id}")
                return None
            
            # Calculate commission rate based on type
            if commission_type == CommissionType.DIRECT:
                commission_rate = agent.base_commission_rate
            elif commission_type == CommissionType.TIER:
                commission_rate = agent.tier_commission_rate
            elif commission_type == CommissionType.VOLUME_BONUS:
                commission_rate = agent.volume_bonus_rate
            else:
                commission_rate = agent.base_commission_rate
            
            # Calculate commission amount
            commission_amount = transaction_amount * commission_rate
            
            # Get commission period (current month)
            commission_period = datetime.now().strftime('%Y-%m')
            due_date = (datetime.now().replace(day=1) + timedelta(days=32)).replace(day=1).date()
            
            async with self.db_connection.get_connection() as conn:
                # Insert commission record
                query = """
                    INSERT INTO agent_commissions (
                        agent_id, transaction_id, commission_type, commission_rate,
                        transaction_amount, commission_amount, commission_period, due_date
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING *
                """
                
                row = await conn.fetchrow(
                    query,
                    agent_id,
                    transaction_id,
                    commission_type.value,
                    commission_rate,
                    transaction_amount,
                    commission_amount,
                    commission_period,
                    due_date
                )
                
                if row:
                    # Update agent totals
                    await self._update_agent_totals(agent_id, transaction_amount, commission_amount)
                    
                    logger.info(f"✅ Commission calculated: {commission_amount}")
                    
                    return AgentCommission(
                        id=row['id'],
                        agent_id=row['agent_id'],
                        transaction_id=row['transaction_id'],
                        commission_type=CommissionType(row['commission_type']),
                        commission_rate=row['commission_rate'],
                        transaction_amount=row['transaction_amount'],
                        commission_amount=row['commission_amount'],
                        status=row['status'],
                        commission_period=row['commission_period'],
                        due_date=row['due_date'],
                        created_at=row['created_at']
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to calculate commission: {e}")
            return None
    
    async def get_agent_hierarchy(self, agent_id: str) -> List[AgentProfile]:
        """الحصول على الهيكل الهرمي للوكيل"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Get agent and all its children
                query = """
                    WITH RECURSIVE agent_tree AS (
                        -- Base case: the agent itself
                        SELECT * FROM agent_profiles WHERE id = $1 AND deleted_at IS NULL
                        
                        UNION ALL
                        
                        -- Recursive case: children of agents in the tree
                        SELECT ap.* 
                        FROM agent_profiles ap
                        INNER JOIN agent_tree at ON ap.parent_agent_id = at.id
                        WHERE ap.deleted_at IS NULL
                    )
                    SELECT * FROM agent_tree ORDER BY level, agent_code
                """
                
                rows = await conn.fetch(query, agent_id)
                
                hierarchy = []
                for row in rows:
                    hierarchy.append(AgentProfile(
                        id=row['id'],
                        user_id=row['user_id'],
                        agent_code=row['agent_code'],
                        agent_type=AgentType(row['agent_type']),
                        status=AgentStatus(row['status']),
                        parent_agent_id=row['parent_agent_id'],
                        level=row['level'],
                        hierarchy_path=row['hierarchy_path'],
                        business_name=row['business_name'],
                        region=row['region'],
                        city=row['city'],
                        base_commission_rate=row['base_commission_rate'],
                        tier_commission_rate=row['tier_commission_rate'],
                        volume_bonus_rate=row['volume_bonus_rate'],
                        daily_transaction_limit=row['daily_transaction_limit'],
                        monthly_transaction_limit=row['monthly_transaction_limit'],
                        single_transaction_limit=row['single_transaction_limit'],
                        total_transactions=row['total_transactions'],
                        total_volume=row['total_volume'],
                        total_commission_earned=row['total_commission_earned'],
                        customer_count=row['customer_count'],
                        rating=row['rating'],
                        review_count=row['review_count'],
                        training_completed=row['training_completed'],
                        certification_level=row['certification_level'],
                        onboarding_completed=row['onboarding_completed'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    ))
                
                logger.info(f"📊 Retrieved hierarchy for agent {agent_id}: {len(hierarchy)} agents")
                return hierarchy
                
        except Exception as e:
            logger.error(f"❌ Failed to get agent hierarchy: {e}")
            return []
    
    async def get_agent_performance(
        self,
        agent_id: str,
        period_type: str = 'monthly',
        start_date: date = None,
        end_date: date = None
    ) -> List[AgentPerformance]:
        """الحصول على أداء الوكيل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM agent_performance 
                    WHERE agent_id = $1 AND period_type = $2
                """
                params = [agent_id, period_type]
                
                if start_date:
                    query += " AND period_start >= $3"
                    params.append(start_date)
                
                if end_date:
                    query += f" AND period_end <= ${len(params) + 1}"
                    params.append(end_date)
                
                query += " ORDER BY period_start DESC"
                
                rows = await conn.fetch(query, *params)
                
                performance_list = []
                for row in rows:
                    performance_list.append(AgentPerformance(
                        id=row['id'],
                        agent_id=row['agent_id'],
                        period_type=row['period_type'],
                        period_start=row['period_start'],
                        period_end=row['period_end'],
                        transaction_count=row['transaction_count'],
                        transaction_volume=row['transaction_volume'],
                        average_transaction_amount=row['average_transaction_amount'],
                        total_commission=row['total_commission'],
                        direct_commission=row['direct_commission'],
                        tier_commission=row['tier_commission'],
                        bonus_commission=row['bonus_commission'],
                        new_customers=row['new_customers'],
                        active_customers=row['active_customers'],
                        customer_retention_rate=row['customer_retention_rate'],
                        success_rate=row['success_rate'],
                        average_processing_time=row['average_processing_time'],
                        customer_satisfaction=row['customer_satisfaction'],
                        complaint_count=row['complaint_count'],
                        regional_rank=row['regional_rank'],
                        national_rank=row['national_rank'],
                        volume_target=row['volume_target'],
                        volume_achievement_rate=row['volume_achievement_rate'],
                        transaction_target=row['transaction_target'],
                        transaction_achievement_rate=row['transaction_achievement_rate']
                    ))
                
                logger.info(f"📊 Retrieved performance for agent {agent_id}: {len(performance_list)} records")
                return performance_list
                
        except Exception as e:
            logger.error(f"❌ Failed to get agent performance: {e}")
            return []
    
    async def get_agent_profile(self, agent_id: str) -> Optional[AgentProfile]:
        """الحصول على ملف الوكيل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM agent_profiles 
                    WHERE id = $1 AND deleted_at IS NULL
                """
                
                row = await conn.fetchrow(query, agent_id)
                
                if row:
                    return AgentProfile(
                        id=row['id'],
                        user_id=row['user_id'],
                        agent_code=row['agent_code'],
                        agent_type=AgentType(row['agent_type']),
                        status=AgentStatus(row['status']),
                        parent_agent_id=row['parent_agent_id'],
                        level=row['level'],
                        hierarchy_path=row['hierarchy_path'],
                        business_name=row['business_name'],
                        region=row['region'],
                        city=row['city'],
                        base_commission_rate=row['base_commission_rate'],
                        tier_commission_rate=row['tier_commission_rate'],
                        volume_bonus_rate=row['volume_bonus_rate'],
                        daily_transaction_limit=row['daily_transaction_limit'],
                        monthly_transaction_limit=row['monthly_transaction_limit'],
                        single_transaction_limit=row['single_transaction_limit'],
                        total_transactions=row['total_transactions'],
                        total_volume=row['total_volume'],
                        total_commission_earned=row['total_commission_earned'],
                        customer_count=row['customer_count'],
                        rating=row['rating'],
                        review_count=row['review_count'],
                        training_completed=row['training_completed'],
                        certification_level=row['certification_level'],
                        onboarding_completed=row['onboarding_completed'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get agent profile: {e}")
            return None
    
    # Helper methods
    async def _validate_user_for_agent(self, user_id: str) -> bool:
        """التحقق من صحة المستخدم لإنشاء وكيل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT id FROM users 
                    WHERE id = $1 AND role IN ('agent', 'agent_manager') 
                    AND is_active = true AND deleted_at IS NULL
                """
                
                result = await conn.fetchval(query, user_id)
                return result is not None
                
        except Exception as e:
            logger.error(f"❌ Failed to validate user for agent: {e}")
            return False
    
    async def _validate_parent_agent(self, parent_agent_id: str) -> bool:
        """التحقق من صحة الوكيل الأب"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT level FROM agent_profiles 
                    WHERE id = $1 AND status = 'active' AND deleted_at IS NULL
                """
                
                level = await conn.fetchval(query, parent_agent_id)
                return level is not None and level < self.max_hierarchy_levels
                
        except Exception as e:
            logger.error(f"❌ Failed to validate parent agent: {e}")
            return False
    
    async def _create_initial_performance_record(self, agent_id: str):
        """إنشاء سجل أداء أولي للوكيل"""
        try:
            current_date = datetime.now().date()
            period_start = current_date.replace(day=1)
            
            # Calculate period end (last day of month)
            if current_date.month == 12:
                period_end = current_date.replace(year=current_date.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                period_end = current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)
            
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO agent_performance (
                        agent_id, period_type, period_start, period_end
                    ) VALUES ($1, 'monthly', $2, $3)
                    ON CONFLICT (agent_id, period_type, period_start) DO NOTHING
                """
                
                await conn.execute(query, agent_id, period_start, period_end)
                
        except Exception as e:
            logger.error(f"❌ Failed to create initial performance record: {e}")
    
    async def _update_agent_totals(self, agent_id: str, transaction_amount: Decimal, commission_amount: Decimal):
        """تحديث إجماليات الوكيل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE agent_profiles 
                    SET total_transactions = total_transactions + 1,
                        total_volume = total_volume + $2,
                        total_commission_earned = total_commission_earned + $3,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = $1
                """
                
                await conn.execute(query, agent_id, transaction_amount, commission_amount)
                
        except Exception as e:
            logger.error(f"❌ Failed to update agent totals: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الوكلاء"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Get basic counts
                counts_query = """
                    SELECT 
                        COUNT(*) as total_agents,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_agents,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_agents,
                        COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_agents,
                        SUM(total_volume) as total_volume,
                        SUM(total_commission_earned) as total_commissions
                    FROM agent_profiles 
                    WHERE deleted_at IS NULL
                """
                
                counts = await conn.fetchrow(counts_query)
                
                return {
                    "total_agents": counts['total_agents'] or 0,
                    "active_agents": counts['active_agents'] or 0,
                    "pending_agents": counts['pending_agents'] or 0,
                    "suspended_agents": counts['suspended_agents'] or 0,
                    "total_volume": float(counts['total_volume'] or 0),
                    "total_commissions": float(counts['total_commissions'] or 0),
                    "average_commission_rate": float(self.default_commission_rates['base'])
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get agent statistics: {e}")
            return {
                "total_agents": 0,
                "active_agents": 0,
                "pending_agents": 0,
                "suspended_agents": 0,
                "total_volume": 0,
                "total_commissions": 0,
                "average_commission_rate": 0
            }
