'use client';

import { motion } from 'framer-motion';
import { 
  BellIcon,
  CheckIcon,
  TrashIcon,
  EyeIcon,
  ArrowsRightLeftIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { useNotifications } from '@/contexts/NotificationContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Button from '@/components/ui/Button';
import { clsx } from 'clsx';

interface NotificationDropdownProps {
  onClose: () => void;
}

// أيقونات أنواع الإشعارات
const notificationIcons = {
  info: InformationCircleIcon,
  success: CheckIcon,
  warning: ExclamationTriangleIcon,
  error: ExclamationTriangleIcon,
  transfer: ArrowsRightLeftIcon,
  security: ShieldCheckIcon,
};

// ألوان أنواع الإشعارات
const notificationColors = {
  info: 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20',
  success: 'text-success-600 dark:text-success-400 bg-success-100 dark:bg-success-900/20',
  warning: 'text-warning-600 dark:text-warning-400 bg-warning-100 dark:bg-warning-900/20',
  error: 'text-error-600 dark:text-error-400 bg-error-100 dark:bg-error-900/20',
  transfer: 'text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/20',
  security: 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/20',
};

export default function NotificationDropdown({ onClose }: NotificationDropdownProps) {
  const { 
    state: notificationState, 
    markAsRead, 
    markAllAsRead, 
    deleteNotification,
    clearAllNotifications,
  } = useNotifications();
  const { formatDate, formatTime, isRTL } = useLanguage();

  // معالجة النقر على الإشعار
  const handleNotificationClick = async (notificationId: string, actionUrl?: string) => {
    await markAsRead(notificationId);
    
    if (actionUrl) {
      window.location.href = actionUrl;
    }
    
    onClose();
  };

  // معالجة تمييز الكل كمقروء
  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
  };

  // معالجة مسح جميع الإشعارات
  const handleClearAll = async () => {
    await clearAllNotifications();
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: -10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95, y: -10 }}
      className={clsx(
        'absolute top-full mt-2 w-96 max-w-sm bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50',
        isRTL ? 'left-0' : 'right-0'
      )}
      onClick={(e) => e.stopPropagation()}
    >
      {/* الرأس */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <BellIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              الإشعارات
            </h3>
            {notificationState.unreadCount > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/20 text-primary-800 dark:text-primary-200">
                {notificationState.unreadCount} جديد
              </span>
            )}
          </div>
          
          {notificationState.notifications.length > 0 && (
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              {notificationState.unreadCount > 0 && (
                <button
                  onClick={handleMarkAllAsRead}
                  className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium"
                >
                  تمييز الكل كمقروء
                </button>
              )}
              <button
                onClick={handleClearAll}
                className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              >
                مسح الكل
              </button>
            </div>
          )}
        </div>
      </div>

      {/* المحتوى */}
      <div className="max-h-96 overflow-y-auto">
        {notificationState.isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              جاري تحميل الإشعارات...
            </p>
          </div>
        ) : notificationState.notifications.length === 0 ? (
          <div className="p-8 text-center">
            <BellIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <p className="text-sm text-gray-500 dark:text-gray-400">
              لا توجد إشعارات جديدة
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {notificationState.notifications.map((notification, index) => {
              const IconComponent = notificationIcons[notification.type];
              const colorClasses = notificationColors[notification.type];
              
              return (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className={clsx(
                    'p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200 cursor-pointer',
                    !notification.isRead && 'bg-primary-50/50 dark:bg-primary-900/10'
                  )}
                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}
                >
                  <div className="flex items-start space-x-3 rtl:space-x-reverse">
                    {/* الأيقونة */}
                    <div className={clsx('p-2 rounded-full flex-shrink-0', colorClasses)}>
                      <IconComponent className="h-4 w-4" />
                    </div>

                    {/* المحتوى */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className={clsx(
                            'text-sm font-medium',
                            notification.isRead 
                              ? 'text-gray-700 dark:text-gray-300' 
                              : 'text-gray-900 dark:text-white'
                          )}>
                            {notification.title}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {notification.message}
                          </p>
                          
                          {/* وقت الإشعار */}
                          <div className="flex items-center space-x-2 rtl:space-x-reverse mt-2">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDate(notification.timestamp)} - {formatTime(notification.timestamp)}
                            </span>
                            {notification.isImportant && (
                              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-error-100 dark:bg-error-900/20 text-error-800 dark:text-error-200">
                                مهم
                              </span>
                            )}
                          </div>

                          {/* زر الإجراء */}
                          {notification.actionUrl && notification.actionText && (
                            <button className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium mt-2">
                              {notification.actionText}
                            </button>
                          )}
                        </div>

                        {/* أزرار الإجراءات */}
                        <div className="flex items-center space-x-1 rtl:space-x-reverse ml-2 rtl:ml-0 rtl:mr-2">
                          {!notification.isRead && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                markAsRead(notification.id);
                              }}
                              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                              title="تمييز كمقروء"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteNotification(notification.id);
                            }}
                            className="p-1 text-gray-400 hover:text-error-600 dark:hover:text-error-400 rounded"
                            title="حذف"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}
      </div>

      {/* التذييل */}
      {notificationState.notifications.length > 0 && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="ghost"
            size="sm"
            fullWidth
            onClick={() => {
              window.location.href = '/dashboard/notifications';
              onClose();
            }}
          >
            عرض جميع الإشعارات
          </Button>
        </div>
      )}
    </motion.div>
  );
}
