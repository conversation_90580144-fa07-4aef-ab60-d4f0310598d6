import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsPhoneNumber, IsString, Length } from 'class-validator';

export class ChangePhoneDto {
  @ApiProperty({
    description: 'رقم الهاتف الجديد',
    example: '+966501234567',
  })
  @IsPhoneNumber('SA', { message: 'رقم الهاتف غير صحيح' })
  @IsNotEmpty({ message: 'رقم الهاتف الجديد مطلوب' })
  newPhone: string;

  @ApiProperty({
    description: 'رمز التحقق المرسل للهاتف الحالي',
    example: '123456',
  })
  @IsString({ message: 'رمز التحقق يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رمز التحقق مطلوب' })
  @Length(6, 6, { message: 'رمز التحقق يجب أن يكون 6 أرقام' })
  currentPhoneOtp: string;
}
