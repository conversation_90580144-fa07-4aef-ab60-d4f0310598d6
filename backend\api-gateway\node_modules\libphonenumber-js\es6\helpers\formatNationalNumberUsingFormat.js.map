{"version": 3, "file": "formatNationalNumberUsingFormat.js", "names": ["applyInternationalSeparatorStyle", "FIRST_GROUP_PATTERN", "formatNationalNumberUsingFormat", "number", "format", "useInternationalFormat", "withNationalPrefix", "carrierCode", "metadata", "formattedNumber", "replace", "RegExp", "pattern", "internationalFormat", "nationalPrefixFormattingRule"], "sources": ["../../source/helpers/formatNationalNumberUsingFormat.js"], "sourcesContent": ["import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'\r\n\r\n// This was originally set to $1 but there are some countries for which the\r\n// first group is not used in the national pattern (e.g. Argentina) so the $1\r\n// group does not match correctly. Therefore, we use `\\d`, so that the first\r\n// group actually used in the pattern will be matched.\r\nexport const FIRST_GROUP_PATTERN = /(\\$\\d)/\r\n\r\nexport default function formatNationalNumberUsingFormat(\r\n\tnumber,\r\n\tformat,\r\n\t{\r\n\t\tuseInternationalFormat,\r\n\t\twithNationalPrefix,\r\n\t\tcarrierCode,\r\n\t\tmetadata\r\n\t}\r\n) {\r\n\tconst formattedNumber = number.replace(\r\n\t\tnew RegExp(format.pattern()),\r\n\t\tuseInternationalFormat\r\n\t\t\t? format.internationalFormat()\r\n\t\t\t: (\r\n\t\t\t\t// This library doesn't use `domestic_carrier_code_formatting_rule`,\r\n\t\t\t\t// because that one is only used when formatting phone numbers\r\n\t\t\t\t// for dialing from a mobile phone, and this is not a dialing library.\r\n\t\t\t\t// carrierCode && format.domesticCarrierCodeFormattingRule()\r\n\t\t\t\t// \t// First, replace the $CC in the formatting rule with the desired carrier code.\r\n\t\t\t\t// \t// Then, replace the $FG in the formatting rule with the first group\r\n\t\t\t\t// \t// and the carrier code combined in the appropriate way.\r\n\t\t\t\t// \t? format.format().replace(FIRST_GROUP_PATTERN, format.domesticCarrierCodeFormattingRule().replace('$CC', carrierCode))\r\n\t\t\t\t// \t: (\r\n\t\t\t\t// \t\twithNationalPrefix && format.nationalPrefixFormattingRule()\r\n\t\t\t\t// \t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\r\n\t\t\t\t// \t\t\t: format.format()\r\n\t\t\t\t// \t)\r\n\t\t\t\twithNationalPrefix && format.nationalPrefixFormattingRule()\r\n\t\t\t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\r\n\t\t\t\t\t: format.format()\r\n\t\t\t)\r\n\t)\r\n\tif (useInternationalFormat) {\r\n\t\treturn applyInternationalSeparatorStyle(formattedNumber)\r\n\t}\r\n\treturn formattedNumber\r\n}"], "mappings": "AAAA,OAAOA,gCAAP,MAA6C,uCAA7C,C,CAEA;AACA;AACA;AACA;;AACA,OAAO,IAAMC,mBAAmB,GAAG,QAA5B;AAEP,eAAe,SAASC,+BAAT,CACdC,MADc,EAEdC,MAFc,QASb;EAAA,IALAC,sBAKA,QALAA,sBAKA;EAAA,IAJAC,kBAIA,QAJAA,kBAIA;EAAA,IAHAC,WAGA,QAHAA,WAGA;EAAA,IAFAC,QAEA,QAFAA,QAEA;EACD,IAAMC,eAAe,GAAGN,MAAM,CAACO,OAAP,CACvB,IAAIC,MAAJ,CAAWP,MAAM,CAACQ,OAAP,EAAX,CADuB,EAEvBP,sBAAsB,GACnBD,MAAM,CAACS,mBAAP,EADmB,GAGpB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAP,kBAAkB,IAAIF,MAAM,CAACU,4BAAP,EAAtB,GACGV,MAAM,CAACA,MAAP,GAAgBM,OAAhB,CAAwBT,mBAAxB,EAA6CG,MAAM,CAACU,4BAAP,EAA7C,CADH,GAEGV,MAAM,CAACA,MAAP,EApBkB,CAAxB;;EAuBA,IAAIC,sBAAJ,EAA4B;IAC3B,OAAOL,gCAAgC,CAACS,eAAD,CAAvC;EACA;;EACD,OAAOA,eAAP;AACA"}