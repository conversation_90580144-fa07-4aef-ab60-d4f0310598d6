# إعدادات التطبيق
NODE_ENV=development
PORT=3000
APP_NAME=WS Transfir API Gateway
APP_VERSION=1.0.0

# قاعدة البيانات
POSTGRES_URL=postgresql://ws_user:ws_password@localhost:5432/ws_transfir
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=ws_user
POSTGRES_PASSWORD=ws_password
POSTGRES_DB=ws_transfir

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# MongoDB (للسجلات)
MONGODB_URL=**************************************************************

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# الأمان
BCRYPT_ROUNDS=12
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# خدمات خارجية
# SMS Service (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Email Service (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# AWS Services
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=ws-transfir-documents

# خدمات الدفع
# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# خدمات KYC
JUMIO_API_TOKEN=your_jumio_api_token
JUMIO_API_SECRET=your_jumio_api_secret

# إعدادات CORS
ALLOWED_ORIGINS=http://localhost:3100,http://localhost:3000

# إعدادات التشفير
ENCRYPTION_KEY=your-32-character-encryption-key
ENCRYPTION_IV=your-16-character-iv

# إعدادات الملفات
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf

# إعدادات التحويل
MIN_TRANSFER_AMOUNT=1
MAX_TRANSFER_AMOUNT=50000
TRANSFER_FEE_PERCENTAGE=2.5
CURRENCY_EXCHANGE_API_KEY=your_exchange_api_key

# إعدادات الإشعارات
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# إعدادات المراقبة
SENTRY_DSN=your_sentry_dsn
NEW_RELIC_LICENSE_KEY=your_newrelic_license_key

# إعدادات التطوير
LOG_LEVEL=debug
SWAGGER_ENABLED=true
DEBUG_MODE=true
