import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsEmail, IsString, Length } from 'class-validator';

export class ChangeEmailDto {
  @ApiProperty({
    description: 'البريد الإلكتروني الجديد',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'البريد الإلكتروني غير صحيح' })
  @IsNotEmpty({ message: 'البريد الإلكتروني الجديد مطلوب' })
  newEmail: string;

  @ApiProperty({
    description: 'رمز التحقق المرسل للبريد الحالي',
    example: '123456',
  })
  @IsString({ message: 'رمز التحقق يجب أن يكون نص' })
  @IsNotEmpty({ message: 'رمز التحقق مطلوب' })
  @Length(6, 6, { message: 'رمز التحقق يجب أن يكون 6 أرقام' })
  currentEmailOtp: string;
}
