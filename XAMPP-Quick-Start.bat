@echo off
chcp 65001 >nul
title WS Transfir - XAMPP Quick Start

color 0C
cls

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🔥 WS TRANSFIR - XAMPP QUICK START 🔥                  ██
echo ██                                                            ██
echo ██    تشغيل سريع لنظام WS Transfir على XAMPP                ██
echo ██    Quick Launch for WS Transfir on XAMPP                  ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 📅 التاريخ: %DATE%
echo ⏰ الوقت: %TIME%
echo 🔥 المنصة: XAMPP
echo 🌐 المنفذ: 8080
echo.

:: Quick system check
echo 🔍 فحص سريع للنظام...

:: Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت - يرجى تثبيته من nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js متاح

:: Check required files
if not exist "xampp-server.js" (
    echo ❌ ملف xampp-server.js غير موجود
    pause
    exit /b 1
)
echo ✅ ملفات النظام موجودة

:: Install dependencies if needed
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات...
    npm install express cors --silent
)
echo ✅ المكتبات جاهزة

echo.
echo 🚀 تشغيل النظام على XAMPP...
echo.

:: Kill existing processes
taskkill /F /IM node.exe >nul 2>&1

:: Start the server
start "WS Transfir XAMPP" cmd /k "title WS Transfir XAMPP Server && color 0C && echo 🔥 WS Transfir XAMPP Server && echo ========================== && echo 🌐 URL: http://localhost:8080 && echo 📊 Health: http://localhost:8080/api/health && echo 🔐 Login: <EMAIL> / admin123 && echo. && node xampp-server.js"

:: Wait and open browser
timeout /t 5 /nobreak >nul
start "" "http://localhost:8080"

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    ✅ WS TRANSFIR XAMPP SYSTEM IS NOW RUNNING!            ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 🌐 روابط الوصول:
echo ==================
echo 🎨 الواجهة الرئيسية:    http://localhost:8080
echo 📊 فحص الصحة:          http://localhost:8080/api/health
echo 🔐 تسجيل الدخول:       http://localhost:8080/api/auth/login
echo 💸 التحويلات:          http://localhost:8080/api/transfers
echo.

echo 🔐 بيانات الدخول:
echo ==================
echo 👨‍💼 مدير: <EMAIL> / admin123
echo 👤 مستخدم: <EMAIL> / password123
echo.

echo 💡 نصائح:
echo ==========
echo 🔹 النظام يعمل على المنفذ 8080
echo 🔹 متوافق مع XAMPP بالكامل
echo 🔹 لإيقاف النظام: أغلق نافذة الخادم
echo 🔹 للدعم: <EMAIL>
echo.

echo 🎉 النظام جاهز على XAMPP!
echo.

pause
