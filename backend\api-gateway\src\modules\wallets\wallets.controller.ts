import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';

import { WalletsService } from './wallets.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

// DTOs
import { TopUpWalletDto } from './dto/top-up-wallet.dto';
import { WithdrawFromWalletDto } from './dto/withdraw-from-wallet.dto';
import { TransferBetweenWalletsDto } from './dto/transfer-between-wallets.dto';
import { PayBillDto } from './dto/pay-bill.dto';
import { WalletTransactionQueryDto } from './dto/wallet-transaction-query.dto';

@ApiTags('wallets')
@Controller('wallets')
@UseGuards(ThrottlerGuard, JwtAuthGuard)
@ApiBearerAuth()
export class WalletsController {
  constructor(private readonly walletsService: WalletsService) {}

  @Get('balance')
  @ApiOperation({ summary: 'الحصول على رصيد المحفظة' })
  @ApiResponse({ status: 200, description: 'رصيد المحفظة' })
  async getBalance(@Request() req) {
    return this.walletsService.getBalance(req.user.id);
  }

  @Get('balance/:currency')
  @ApiOperation({ summary: 'الحصول على رصيد عملة معينة' })
  @ApiResponse({ status: 200, description: 'رصيد العملة' })
  async getCurrencyBalance(@Param('currency') currency: string, @Request() req) {
    return this.walletsService.getCurrencyBalance(req.user.id, currency);
  }

  @Post('top-up')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'شحن المحفظة' })
  @ApiResponse({ status: 201, description: 'تم شحن المحفظة بنجاح' })
  @ApiResponse({ status: 400, description: 'بيانات غير صحيحة' })
  async topUpWallet(@Body() topUpDto: TopUpWalletDto, @Request() req) {
    return this.walletsService.topUpWallet(req.user.id, topUpDto);
  }

  @Post('withdraw')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'سحب من المحفظة' })
  @ApiResponse({ status: 201, description: 'تم السحب بنجاح' })
  @ApiResponse({ status: 400, description: 'رصيد غير كافي' })
  async withdrawFromWallet(@Body() withdrawDto: WithdrawFromWalletDto, @Request() req) {
    return this.walletsService.withdrawFromWallet(req.user.id, withdrawDto);
  }

  @Post('transfer')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'تحويل بين المحافظ' })
  @ApiResponse({ status: 201, description: 'تم التحويل بنجاح' })
  @ApiResponse({ status: 400, description: 'بيانات غير صحيحة' })
  async transferBetweenWallets(@Body() transferDto: TransferBetweenWalletsDto, @Request() req) {
    return this.walletsService.transferBetweenWallets(req.user.id, transferDto);
  }

  @Post('pay-bill')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'دفع فاتورة من المحفظة' })
  @ApiResponse({ status: 201, description: 'تم دفع الفاتورة بنجاح' })
  @ApiResponse({ status: 400, description: 'رصيد غير كافي' })
  async payBill(@Body() payBillDto: PayBillDto, @Request() req) {
    return this.walletsService.payBill(req.user.id, payBillDto);
  }

  @Get('transactions')
  @ApiOperation({ summary: 'سجل معاملات المحفظة' })
  @ApiResponse({ status: 200, description: 'سجل المعاملات' })
  async getTransactions(@Query() queryDto: WalletTransactionQueryDto, @Request() req) {
    return this.walletsService.getTransactions(req.user.id, queryDto);
  }

  @Get('transactions/:transactionId')
  @ApiOperation({ summary: 'تفاصيل معاملة محددة' })
  @ApiResponse({ status: 200, description: 'تفاصيل المعاملة' })
  @ApiResponse({ status: 404, description: 'المعاملة غير موجودة' })
  async getTransactionDetails(@Param('transactionId') transactionId: string, @Request() req) {
    return this.walletsService.getTransactionDetails(req.user.id, transactionId);
  }

  @Get('statistics')
  @ApiOperation({ summary: 'إحصائيات المحفظة' })
  @ApiResponse({ status: 200, description: 'إحصائيات المحفظة' })
  async getWalletStatistics(@Request() req) {
    return this.walletsService.getWalletStatistics(req.user.id);
  }

  @Post('freeze')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'تجميد المحفظة' })
  @ApiResponse({ status: 200, description: 'تم تجميد المحفظة' })
  async freezeWallet(@Body('reason') reason: string, @Request() req) {
    return this.walletsService.freezeWallet(req.user.id, reason);
  }

  @Post('unfreeze')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'إلغاء تجميد المحفظة' })
  @ApiResponse({ status: 200, description: 'تم إلغاء تجميد المحفظة' })
  async unfreezeWallet(@Request() req) {
    return this.walletsService.unfreezeWallet(req.user.id);
  }

  @Get('exchange-rates')
  @ApiOperation({ summary: 'أسعار صرف العملات في المحفظة' })
  @ApiResponse({ status: 200, description: 'أسعار الصرف' })
  async getExchangeRates() {
    return this.walletsService.getExchangeRates();
  }

  @Post('convert')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'تحويل عملة داخل المحفظة' })
  @ApiResponse({ status: 201, description: 'تم تحويل العملة بنجاح' })
  async convertCurrency(@Body() convertDto: any, @Request() req) {
    return this.walletsService.convertCurrency(req.user.id, convertDto);
  }

  @Get('limits')
  @ApiOperation({ summary: 'حدود المحفظة' })
  @ApiResponse({ status: 200, description: 'حدود المحفظة' })
  async getWalletLimits(@Request() req) {
    return this.walletsService.getWalletLimits(req.user.id);
  }

  @Post('request-limit-increase')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'طلب زيادة حدود المحفظة' })
  @ApiResponse({ status: 201, description: 'تم تقديم الطلب' })
  async requestLimitIncrease(@Body() requestDto: any, @Request() req) {
    return this.walletsService.requestLimitIncrease(req.user.id, requestDto);
  }

  @Get('cards')
  @ApiOperation({ summary: 'البطاقات المربوطة بالمحفظة' })
  @ApiResponse({ status: 200, description: 'قائمة البطاقات' })
  async getLinkedCards(@Request() req) {
    return this.walletsService.getLinkedCards(req.user.id);
  }

  @Post('cards/link')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'ربط بطاقة جديدة' })
  @ApiResponse({ status: 201, description: 'تم ربط البطاقة بنجاح' })
  async linkCard(@Body() cardDto: any, @Request() req) {
    return this.walletsService.linkCard(req.user.id, cardDto);
  }

  @Delete('cards/:cardId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'إلغاء ربط بطاقة' })
  @ApiResponse({ status: 200, description: 'تم إلغاء ربط البطاقة' })
  async unlinkCard(@Param('cardId') cardId: string, @Request() req) {
    return this.walletsService.unlinkCard(req.user.id, cardId);
  }

  @Get('recurring-payments')
  @ApiOperation({ summary: 'المدفوعات المتكررة' })
  @ApiResponse({ status: 200, description: 'قائمة المدفوعات المتكررة' })
  async getRecurringPayments(@Request() req) {
    return this.walletsService.getRecurringPayments(req.user.id);
  }

  @Post('recurring-payments')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'إنشاء دفعة متكررة' })
  @ApiResponse({ status: 201, description: 'تم إنشاء الدفعة المتكررة' })
  async createRecurringPayment(@Body() recurringDto: any, @Request() req) {
    return this.walletsService.createRecurringPayment(req.user.id, recurringDto);
  }

  @Put('recurring-payments/:paymentId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'تحديث دفعة متكررة' })
  @ApiResponse({ status: 200, description: 'تم تحديث الدفعة المتكررة' })
  async updateRecurringPayment(
    @Param('paymentId') paymentId: string,
    @Body() updateDto: any,
    @Request() req,
  ) {
    return this.walletsService.updateRecurringPayment(req.user.id, paymentId, updateDto);
  }

  @Delete('recurring-payments/:paymentId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'إلغاء دفعة متكررة' })
  @ApiResponse({ status: 200, description: 'تم إلغاء الدفعة المتكررة' })
  async cancelRecurringPayment(@Param('paymentId') paymentId: string, @Request() req) {
    return this.walletsService.cancelRecurringPayment(req.user.id, paymentId);
  }
}
