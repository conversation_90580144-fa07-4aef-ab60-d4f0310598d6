/**
 * WS Transfir Online Frontend Server
 * خادم الواجهة الأمامية الأونلاين
 */

const express = require('express');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = 3100;

console.log('🎨 تشغيل خادم الواجهة الأمامية الأونلاين');
console.log('==========================================');

// Serve static files
app.use(express.static('.'));

// Main route - serve the online frontend
app.get('/', (req, res) => {
  try {
    const html = fs.readFileSync('online-frontend.html', 'utf8');
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.send(html);
  } catch (error) {
    res.status(500).send(`
      <h1>خطأ في تحميل الواجهة</h1>
      <p>Error: ${error.message}</p>
      <p><a href="/api">جرب الوصول المباشر للـ API</a></p>
    `);
  }
});

// Health check for frontend
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'WS Transfir Frontend',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Redirect API calls to backend
app.use('/api', (req, res) => {
  res.redirect(`http://localhost:3000${req.originalUrl}`);
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('🎨 ═══════════════════════════════════════════════════════════');
  console.log('🎨 WS TRANSFIR FRONTEND SERVER - ONLINE');
  console.log('🎨 ═══════════════════════════════════════════════════════════');
  console.log(`🌐 Frontend URL: http://localhost:${PORT}`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`🔗 API Redirect: http://localhost:${PORT}/api/*`);
  console.log('🎨 ═══════════════════════════════════════════════════════════');
  console.log('');
});

module.exports = app;
