import { Type } from '@nestjs/common';
import { ReferenceObject, ResponseObject, SchemaObject } from '../interfaces/open-api-spec.interface';
export interface ApiResponseMetadata extends Omit<ResponseObject, 'description'> {
    status?: number | 'default' | '1XX' | '2XX' | '3XX' | '4XX' | '5XX';
    type?: Type<unknown> | Function | [Function] | string;
    isArray?: boolean;
    description?: string;
    example?: any;
}
export interface ApiResponseSchemaHost extends Omit<ResponseObject, 'description'> {
    schema: SchemaObject & Partial<ReferenceObject>;
    status?: number | 'default' | '1XX' | '2XX' | '3XX' | '4XX' | '5XX';
    description?: string;
}
export type ApiResponseOptions = ApiResponseMetadata | ApiResponseSchemaHost;
export declare function ApiResponse(options: ApiResponseOptions, { overrideExisting }?: {
    overrideExisting: boolean;
}): MethodDecorator & ClassDecorator;
export declare const ApiContinueResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiSwitchingProtocolsResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiProcessingResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiEarlyhintsResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiOkResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiCreatedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiAcceptedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiNonAuthoritativeInformationResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiNoContentResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiResetContentResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiPartialContentResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiAmbiguousResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiMovedPermanentlyResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiFoundResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiSeeOtherResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiNotModifiedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiTemporaryRedirectResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiPermanentRedirectResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiBadRequestResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiUnauthorizedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiPaymentRequiredResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiForbiddenResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiNotFoundResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiMethodNotAllowedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiNotAcceptableResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiProxyAuthenticationRequiredResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiRequestTimeoutResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiConflictResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiGoneResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiLengthRequiredResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiPreconditionFailedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiPayloadTooLargeResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiUriTooLongResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiUnsupportedMediaTypeResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiRequestedRangeNotSatisfiableResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiExpectationFailedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiIAmATeapotResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiMisdirectedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiUnprocessableEntityResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiFailedDependencyResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiPreconditionRequiredResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiTooManyRequestsResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiInternalServerErrorResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiNotImplementedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiBadGatewayResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiServiceUnavailableResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiGatewayTimeoutResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator, ApiHttpVersionNotSupportedResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator;
export declare const ApiDefaultResponse: (options?: ApiResponseOptions) => MethodDecorator & ClassDecorator;
