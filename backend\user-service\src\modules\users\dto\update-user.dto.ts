import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsPhoneNumber,
  Min<PERSON>ength,
  MaxLength,
  Matches,
  IsOptional,
  IsDateString,
  IsIn,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { CreateUserDto } from './create-user.dto';
import { UserStatus, UserRole, KYCStatus } from '../entities/user.entity';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiProperty({
    description: 'الاسم الأول',
    example: 'أحمد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الاسم الأول يجب أن يكون نص' })
  @MinLength(2, { message: 'الاسم الأول يجب أن يكون على الأقل حرفين' })
  @MaxLength(50, { message: 'الاسم الأول يجب أن يكون أقل من 50 حرف' })
  firstName?: string;

  @ApiProperty({
    description: 'الاسم الأخير',
    example: 'محمد',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الاسم الأخير يجب أن يكون نص' })
  @MinLength(2, { message: 'الاسم الأخير يجب أن يكون على الأقل حرفين' })
  @MaxLength(50, { message: 'الاسم الأخير يجب أن يكون أقل من 50 حرف' })
  lastName?: string;

  @ApiProperty({
    description: 'رقم الهاتف',
    example: '+966501234567',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber('SA', { message: 'رقم الهاتف غير صالح' })
  phone?: string;

  @ApiProperty({
    description: 'رقم الهوية الوطنية',
    example: '1234567890',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'رقم الهوية يجب أن يكون نص' })
  @Matches(/^\d{10}$/, { message: 'رقم الهوية يجب أن يكون 10 أرقام' })
  nationalId?: string;

  @ApiProperty({
    description: 'تاريخ الميلاد',
    example: '1990-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'تاريخ الميلاد غير صالح' })
  dateOfBirth?: string;

  @ApiProperty({
    description: 'الجنس',
    example: 'male',
    enum: ['male', 'female'],
    required: false,
  })
  @IsOptional()
  @IsIn(['male', 'female'], { message: 'الجنس يجب أن يكون ذكر أو أنثى' })
  gender?: string;

  @ApiProperty({
    description: 'الجنسية',
    example: 'SA',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'الجنسية يجب أن تكون نص' })
  @MaxLength(2, { message: 'الجنسية يجب أن تكون رمز من حرفين' })
  nationality?: string;

  @ApiProperty({
    description: 'اللغة المفضلة',
    example: 'ar',
    enum: ['ar', 'en'],
    required: false,
  })
  @IsOptional()
  @IsIn(['ar', 'en'], { message: 'اللغة يجب أن تكون عربية أو إنجليزية' })
  preferredLanguage?: string;

  @ApiProperty({
    description: 'العملة المفضلة',
    example: 'SAR',
    enum: ['SAR', 'USD', 'EUR', 'GBP', 'AED'],
    required: false,
  })
  @IsOptional()
  @IsIn(['SAR', 'USD', 'EUR', 'GBP', 'AED'], {
    message: 'العملة غير مدعومة',
  })
  preferredCurrency?: string;

  @ApiProperty({
    description: 'تفعيل الإشعارات',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تفعيل الإشعارات يجب أن يكون قيمة منطقية' })
  notificationsEnabled?: boolean;

  @ApiProperty({
    description: 'حالة المستخدم',
    enum: UserStatus,
    example: UserStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserStatus, { message: 'حالة المستخدم غير صالحة' })
  status?: UserStatus;

  @ApiProperty({
    description: 'دور المستخدم',
    enum: UserRole,
    example: UserRole.USER,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserRole, { message: 'دور المستخدم غير صالح' })
  role?: UserRole;

  @ApiProperty({
    description: 'حالة التحقق من الهوية',
    enum: KYCStatus,
    example: KYCStatus.APPROVED,
    required: false,
  })
  @IsOptional()
  @IsEnum(KYCStatus, { message: 'حالة التحقق من الهوية غير صالحة' })
  kycStatus?: KYCStatus;

  @ApiProperty({
    description: 'تفعيل البريد الإلكتروني',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تفعيل البريد الإلكتروني يجب أن يكون قيمة منطقية' })
  emailVerified?: boolean;

  @ApiProperty({
    description: 'تفعيل رقم الهاتف',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'تفعيل رقم الهاتف يجب أن يكون قيمة منطقية' })
  phoneVerified?: boolean;
}
