{"version": 3, "file": "ParserContext.js", "sourceRoot": "", "sources": ["../../src/parser/ParserContext.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAEtC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD;;;GAGG;AACH;IAqCE,uBAAmB,aAAiC,EAAE,WAAsB;QA1B5E;;;WAGG;QACI,iBAAY,GAAc,SAAS,CAAC,KAAK,CAAC;QAEjD;;WAEG;QACI,UAAK,GAAgB,EAAE,CAAC;QAE/B;;WAEG;QACI,WAAM,GAAY,EAAE,CAAC;QAa1B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAExE,IAAI,CAAC,GAAG,GAAG,IAAI,gBAAgB,EAAE,CAAC;IACpC,CAAC;IACH,oBAAC;AAAD,CAAC,AA7CD,IA6CC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { TextRange } from './TextRange';\r\nimport type { Token } from './Token';\r\nimport { DocComment } from '../nodes';\r\nimport type { TSDocConfiguration } from '../configuration/TSDocConfiguration';\r\nimport { ParserMessageLog } from './ParserMessageLog';\r\n\r\n/**\r\n * An internal data structure that tracks all the state being built up by the various\r\n * parser stages.\r\n */\r\nexport class ParserContext {\r\n  /**\r\n   * The configuration that was provided for the TSDocParser.\r\n   */\r\n  public readonly configuration: TSDocConfiguration;\r\n\r\n  /**\r\n   * The `sourceRange` indicates the start and end of the original input that was parsed.\r\n   */\r\n  public readonly sourceRange: TextRange;\r\n\r\n  /**\r\n   * The text range starting from the opening `/**` and ending with\r\n   * the closing `*\\/` delimiter.\r\n   */\r\n  public commentRange: TextRange = TextRange.empty;\r\n\r\n  /**\r\n   * The text ranges corresponding to the lines of content inside the comment.\r\n   */\r\n  public lines: TextRange[] = [];\r\n\r\n  /**\r\n   * A complete list of all tokens that were extracted from the input lines.\r\n   */\r\n  public tokens: Token[] = [];\r\n\r\n  /**\r\n   * The parsed doc comment object.  This is the primary output of the parser.\r\n   */\r\n  public readonly docComment: DocComment;\r\n\r\n  /**\r\n   * A queryable log that reports warnings and error messages that occurred during parsing.\r\n   */\r\n  public readonly log: ParserMessageLog;\r\n\r\n  public constructor(configuration: TSDocConfiguration, sourceRange: TextRange) {\r\n    this.configuration = configuration;\r\n    this.sourceRange = sourceRange;\r\n\r\n    this.docComment = new DocComment({ configuration: this.configuration });\r\n\r\n    this.log = new ParserMessageLog();\r\n  }\r\n}\r\n"]}