import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ScheduleModule } from '@nestjs/schedule';

// Modules
import { KycModule } from './modules/kyc/kyc.module';
import { AmlModule } from './modules/aml/aml.module';
import { SanctionsModule } from './modules/sanctions/sanctions.module';
import { RiskAssessmentModule } from './modules/risk-assessment/risk-assessment.module';
import { ComplianceReportsModule } from './modules/compliance-reports/compliance-reports.module';
import { DocumentVerificationModule } from './modules/document-verification/document-verification.module';

// Entities
import { KycRecord } from './modules/kyc/entities/kyc-record.entity';
import { AmlCheck } from './modules/aml/entities/aml-check.entity';
import { SanctionsCheck } from './modules/sanctions/entities/sanctions-check.entity';
import { RiskAssessment } from './modules/risk-assessment/entities/risk-assessment.entity';
import { ComplianceReport } from './modules/compliance-reports/entities/compliance-report.entity';
import { DocumentVerification } from './modules/document-verification/entities/document-verification.entity';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('COMPLIANCE_DB_HOST', 'localhost'),
        port: configService.get('COMPLIANCE_DB_PORT', 5432),
        username: configService.get('COMPLIANCE_DB_USERNAME', 'postgres'),
        password: configService.get('COMPLIANCE_DB_PASSWORD', 'password'),
        database: configService.get('COMPLIANCE_DB_NAME', 'ws_compliance'),
        entities: [
          KycRecord,
          AmlCheck,
          SanctionsCheck,
          RiskAssessment,
          ComplianceReport,
          DocumentVerification,
        ],
        synchronize: configService.get('NODE_ENV') === 'development',
        logging: configService.get('NODE_ENV') === 'development',
        ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
      }),
      inject: [ConfigService],
    }),

    // HTTP Client for external compliance APIs
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: configService.get('HTTP_TIMEOUT', 60000), // Longer timeout for compliance checks
        maxRedirects: 5,
        retries: 3,
      }),
      inject: [ConfigService],
    }),

    // JWT Authentication
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),

    // Passport
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // Task Scheduling for compliance monitoring
    ScheduleModule.forRoot(),

    // Feature Modules
    KycModule,
    AmlModule,
    SanctionsModule,
    RiskAssessmentModule,
    ComplianceReportsModule,
    DocumentVerificationModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    console.log('🛡️  Compliance Service initialized');
    console.log(`📋 KYC enabled: ${this.configService.get('KYC_ENABLED', 'true')}`);
    console.log(`🔍 AML enabled: ${this.configService.get('AML_ENABLED', 'true')}`);
    console.log(`⚖️  Sanctions screening: ${this.configService.get('SANCTIONS_SCREENING', 'true')}`);
    console.log(`📄 Document verification: ${this.configService.get('DOCUMENT_VERIFICATION', 'true')}`);
    console.log(`🎯 Risk assessment: ${this.configService.get('RISK_ASSESSMENT', 'true')}`);
    console.log(`📊 Database: ${this.configService.get('COMPLIANCE_DB_NAME', 'ws_compliance')}`);
    console.log(`🏛️  SAMA compliance: ${this.configService.get('SAMA_COMPLIANCE', 'true')}`);
    console.log(`🌍 FATF compliance: ${this.configService.get('FATF_COMPLIANCE', 'true')}`);
  }
}
