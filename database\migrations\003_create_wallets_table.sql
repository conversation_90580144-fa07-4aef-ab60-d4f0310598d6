-- Migration: Create Wallets Table
-- Description: إنشاء جدول المحافظ الرقمية
-- Version: 003
-- Created: 2024-01-15

-- Create wallets table
CREATE TABLE IF NOT EXISTS wallets (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Wallet Basic Info
    wallet_number VARCHAR(20) UNIQUE NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'SAR',
    type VARCHAR(20) NOT NULL DEFAULT 'primary' CHECK (type IN ('primary', 'savings', 'business', 'escrow', 'commission')),
    
    -- Balance Information
    balance DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (balance >= 0),
    available_balance DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (available_balance >= 0),
    pending_balance DECIMAL(15,2) NOT NULL DEFAULT 0,
    reserved_balance DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (reserved_balance >= 0),
    
    -- Limits
    daily_limit DECIMAL(15,2) DEFAULT 50000,
    monthly_limit DECIMAL(15,2) DEFAULT 1000000,
    transaction_limit DECIMAL(15,2) DEFAULT 10000,
    
    -- Usage Tracking
    daily_used DECIMAL(15,2) NOT NULL DEFAULT 0,
    monthly_used DECIMAL(15,2) NOT NULL DEFAULT 0,
    last_reset_date DATE DEFAULT CURRENT_DATE,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'frozen', 'closed')),
    is_default BOOLEAN NOT NULL DEFAULT false,
    
    -- Security
    pin_hash VARCHAR(255),
    pin_attempts INTEGER NOT NULL DEFAULT 0,
    pin_locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Notifications
    low_balance_threshold DECIMAL(15,2) DEFAULT 100,
    notification_enabled BOOLEAN NOT NULL DEFAULT true,
    
    -- Metadata
    name VARCHAR(100),
    description TEXT,
    metadata JSONB DEFAULT '{}',
    tags TEXT[],
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    -- Soft Delete
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by VARCHAR(50),
    
    -- Foreign Key Constraints
    CONSTRAINT fk_wallets_user FOREIGN KEY (user_id) REFERENCES users(id),
    
    -- Check Constraints
    CONSTRAINT chk_balance_consistency CHECK (balance = available_balance + pending_balance + reserved_balance)
);

-- Create wallet transactions table for detailed tracking
CREATE TABLE IF NOT EXISTS wallet_transactions (
    id VARCHAR(50) PRIMARY KEY,
    
    -- Basic Info
    wallet_id VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(50),
    
    -- Transaction Details
    type VARCHAR(20) NOT NULL CHECK (type IN ('credit', 'debit', 'hold', 'release', 'fee')),
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    
    -- Description
    description TEXT,
    reference VARCHAR(100),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    
    -- Foreign Key Constraints
    CONSTRAINT fk_wallet_transactions_wallet FOREIGN KEY (wallet_id) REFERENCES wallets(id),
    CONSTRAINT fk_wallet_transactions_transaction FOREIGN KEY (transaction_id) REFERENCES transactions(id)
);

-- Create indexes for wallets
CREATE INDEX idx_wallets_user_id ON wallets(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallets_currency ON wallets(currency) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallets_type ON wallets(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallets_status ON wallets(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallets_balance ON wallets(balance) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallets_number ON wallets(wallet_number) WHERE deleted_at IS NULL;

-- Create composite indexes
CREATE INDEX idx_wallets_user_currency ON wallets(user_id, currency) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallets_user_type ON wallets(user_id, type) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallets_user_default ON wallets(user_id, is_default) WHERE deleted_at IS NULL;

-- Create indexes for wallet transactions
CREATE INDEX idx_wallet_transactions_wallet_id ON wallet_transactions(wallet_id);
CREATE INDEX idx_wallet_transactions_transaction_id ON wallet_transactions(transaction_id);
CREATE INDEX idx_wallet_transactions_type ON wallet_transactions(type);
CREATE INDEX idx_wallet_transactions_created_at ON wallet_transactions(created_at);

-- Create GIN indexes for JSONB fields
CREATE INDEX idx_wallets_metadata ON wallets USING GIN(metadata) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallets_tags ON wallets USING GIN(tags) WHERE deleted_at IS NULL;
CREATE INDEX idx_wallet_transactions_metadata ON wallet_transactions USING GIN(metadata);

-- Create function to generate wallet number
CREATE OR REPLACE FUNCTION generate_wallet_number()
RETURNS TRIGGER AS $$
DECLARE
    prefix VARCHAR(2);
    user_part VARCHAR(6);
    currency_part VARCHAR(3);
    sequence_part VARCHAR(6);
    new_number VARCHAR(20);
BEGIN
    -- Set prefix based on wallet type
    CASE NEW.type
        WHEN 'primary' THEN prefix := 'WP';
        WHEN 'savings' THEN prefix := 'WS';
        WHEN 'business' THEN prefix := 'WB';
        WHEN 'escrow' THEN prefix := 'WE';
        WHEN 'commission' THEN prefix := 'WC';
        ELSE prefix := 'WX';
    END CASE;
    
    -- Get user part (last 6 chars of user_id)
    user_part := RIGHT(NEW.user_id, 6);
    
    -- Get currency part
    currency_part := NEW.currency;
    
    -- Get sequence part
    SELECT LPAD(NEXTVAL('wallet_number_seq')::TEXT, 6, '0') INTO sequence_part;
    
    -- Combine parts
    new_number := prefix || user_part || currency_part || sequence_part;
    
    NEW.wallet_number := new_number;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for wallet numbers
CREATE SEQUENCE IF NOT EXISTS wallet_number_seq START 1;

-- Create trigger to auto-generate wallet number
CREATE TRIGGER generate_wallet_number_trigger
    BEFORE INSERT ON wallets
    FOR EACH ROW
    EXECUTE FUNCTION generate_wallet_number();

-- Create function to generate wallet ID
CREATE OR REPLACE FUNCTION generate_wallet_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'wallet_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to auto-generate wallet ID
CREATE TRIGGER generate_wallet_id_trigger
    BEFORE INSERT ON wallets
    FOR EACH ROW
    EXECUTE FUNCTION generate_wallet_id();

-- Create function to generate wallet transaction ID
CREATE OR REPLACE FUNCTION generate_wallet_transaction_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.id IS NULL THEN
        NEW.id = 'wtxn_' || encode(gen_random_bytes(12), 'base64');
        NEW.id = replace(NEW.id, '/', '_');
        NEW.id = replace(NEW.id, '+', '-');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to auto-generate wallet transaction ID
CREATE TRIGGER generate_wallet_transaction_id_trigger
    BEFORE INSERT ON wallet_transactions
    FOR EACH ROW
    EXECUTE FUNCTION generate_wallet_transaction_id();

-- Create trigger to update updated_at for wallets
CREATE TRIGGER update_wallets_updated_at
    BEFORE UPDATE ON wallets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to ensure only one default wallet per user per currency
CREATE OR REPLACE FUNCTION ensure_single_default_wallet()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_default = true THEN
        -- Remove default flag from other wallets of the same user and currency
        UPDATE wallets 
        SET is_default = false 
        WHERE user_id = NEW.user_id 
          AND currency = NEW.currency 
          AND id != NEW.id 
          AND deleted_at IS NULL;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to ensure single default wallet
CREATE TRIGGER ensure_single_default_wallet_trigger
    BEFORE INSERT OR UPDATE ON wallets
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_default_wallet();

-- Create function to reset daily/monthly limits
CREATE OR REPLACE FUNCTION reset_wallet_limits()
RETURNS void AS $$
BEGIN
    -- Reset daily limits
    UPDATE wallets 
    SET daily_used = 0 
    WHERE last_reset_date < CURRENT_DATE;
    
    -- Reset monthly limits (first day of month)
    UPDATE wallets 
    SET monthly_used = 0 
    WHERE EXTRACT(DAY FROM last_reset_date) = 1 
      AND EXTRACT(DAY FROM CURRENT_DATE) = 1 
      AND last_reset_date < CURRENT_DATE;
    
    -- Update reset date
    UPDATE wallets 
    SET last_reset_date = CURRENT_DATE 
    WHERE last_reset_date < CURRENT_DATE;
END;
$$ language 'plpgsql';

-- Add comments for documentation
COMMENT ON TABLE wallets IS 'جدول المحافظ الرقمية - يحتوي على أرصدة المستخدمين';
COMMENT ON COLUMN wallets.id IS 'معرف المحفظة الفريد';
COMMENT ON COLUMN wallets.wallet_number IS 'رقم المحفظة المرئي للمستخدم';
COMMENT ON COLUMN wallets.balance IS 'الرصيد الإجمالي';
COMMENT ON COLUMN wallets.available_balance IS 'الرصيد المتاح للاستخدام';
COMMENT ON COLUMN wallets.pending_balance IS 'الرصيد المعلق';
COMMENT ON COLUMN wallets.reserved_balance IS 'الرصيد المحجوز';

COMMENT ON TABLE wallet_transactions IS 'جدول معاملات المحفظة - تتبع تفصيلي لحركة الأموال';
COMMENT ON COLUMN wallet_transactions.type IS 'نوع المعاملة (إيداع، سحب، حجز، إطلاق)';
COMMENT ON COLUMN wallet_transactions.balance_before IS 'الرصيد قبل المعاملة';
COMMENT ON COLUMN wallet_transactions.balance_after IS 'الرصيد بعد المعاملة';

-- Create views for different wallet types
CREATE VIEW primary_wallets AS
SELECT * FROM wallets 
WHERE type = 'primary' AND deleted_at IS NULL;

CREATE VIEW active_wallets AS
SELECT * FROM wallets 
WHERE status = 'active' AND deleted_at IS NULL;

CREATE VIEW low_balance_wallets AS
SELECT * FROM wallets 
WHERE balance <= low_balance_threshold AND deleted_at IS NULL;

-- Create view for wallet summary
CREATE VIEW wallet_summary AS
SELECT 
    user_id,
    currency,
    COUNT(*) as wallet_count,
    SUM(balance) as total_balance,
    SUM(available_balance) as total_available,
    SUM(pending_balance) as total_pending,
    SUM(reserved_balance) as total_reserved
FROM wallets 
WHERE deleted_at IS NULL
GROUP BY user_id, currency;

-- Create view for wallet statistics
CREATE VIEW wallet_statistics AS
SELECT 
    currency,
    type,
    status,
    COUNT(*) as wallet_count,
    SUM(balance) as total_balance,
    AVG(balance) as average_balance,
    MIN(balance) as min_balance,
    MAX(balance) as max_balance,
    COUNT(CASE WHEN balance <= low_balance_threshold THEN 1 END) as low_balance_count
FROM wallets 
WHERE deleted_at IS NULL
GROUP BY currency, type, status;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON wallets TO ws_app_user;
GRANT SELECT, INSERT, UPDATE ON wallet_transactions TO ws_app_user;
GRANT SELECT ON primary_wallets TO ws_app_user;
GRANT SELECT ON active_wallets TO ws_app_user;
GRANT SELECT ON low_balance_wallets TO ws_app_user;
GRANT SELECT ON wallet_summary TO ws_app_user;
GRANT SELECT ON wallet_statistics TO ws_app_user;
GRANT USAGE ON SEQUENCE wallet_number_seq TO ws_app_user;
