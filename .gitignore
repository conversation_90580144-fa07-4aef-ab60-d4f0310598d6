# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage
.grunt

# Compiled binary addons
build/Release

# Users Environment Variables
.lock-wscript

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Database
*.sqlite
*.sqlite3
*.db

# Uploads
uploads/
temp/
tmp/

# Build outputs
build/
dist/
lib/

# Test outputs
test-results/
coverage/

# Docker
.dockerignore

# Kubernetes
*.yaml.bak
*.yml.bak

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Azure
.azure/

# Google Cloud
.gcloud/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp

# Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# WS Transfir specific
config/local.json
config/production.json
config/staging.json
secrets/
keys/
certificates/
backups/
exports/
reports/
analytics/
monitoring/

# Frontend specific
frontend/web-app/.next/
frontend/web-app/out/
frontend/web-app/build/
frontend/web-app/.env.local
frontend/web-app/.env.production.local

# Backend specific
backend/*/dist/
backend/*/build/
backend/*/logs/
backend/*/uploads/
backend/*/.env
backend/*/.env.local

# Database
database/data/
database/backups/
database/migrations/temp/

# Monitoring
monitoring/data/
monitoring/logs/

# Documentation
docs/build/
docs/dist/

# Cache
.cache/
*.cache

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local development
local/
dev/
development/

# Production
production/
prod/

# Staging
staging/
stage/

# Testing
test-data/
mock-data/
fixtures/

# Performance
benchmark/
profiling/

# Security
security/
audit/
vulnerability/

# Compliance
compliance/reports/
kyc/data/
aml/reports/
