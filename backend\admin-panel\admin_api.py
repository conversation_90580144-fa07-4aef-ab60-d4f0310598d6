"""
Admin Panel API
==============
واجهة برمجة التطبيقات للوحة الإدارة
"""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field, validator

from ..shared.auth.middleware import get_current_user, require_role, require_permission
from ..shared.auth.jwt_manager import TokenPayload, UserRole, Permission
from ..shared.database.connection import DatabaseConnection

from .admin_dashboard import AdminDashboard, DashboardPeriod
from .user_management import UserManagement
from .financial_reports import FinancialReports, ReportType, ReportFormat
from .system_settings import SystemSettings, SettingCategory

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/admin", tags=["Admin Panel"])


# Pydantic Models
class DashboardPeriodRequest(BaseModel):
    """طلب فترة لوحة التحكم"""
    period: str = Field(default="today", description="الفترة الزمنية")
    
    @validator('period')
    def validate_period(cls, v):
        valid_periods = [p.value for p in DashboardPeriod]
        if v not in valid_periods:
            raise ValueError(f'فترة غير صحيحة. الفترات المتاحة: {valid_periods}')
        return v


class UserStatusUpdateRequest(BaseModel):
    """طلب تحديث حالة المستخدم"""
    is_active: bool = Field(..., description="حالة النشاط")
    reason: Optional[str] = Field(None, description="سبب التغيير")


class KYCStatusUpdateRequest(BaseModel):
    """طلب تحديث حالة التحقق من الهوية"""
    kyc_status: str = Field(..., description="حالة التحقق من الهوية")
    kyc_level: int = Field(..., description="مستوى التحقق")
    notes: Optional[str] = Field(None, description="ملاحظات")
    
    @validator('kyc_status')
    def validate_kyc_status(cls, v):
        valid_statuses = ['pending', 'in_review', 'approved', 'rejected', 'expired']
        if v not in valid_statuses:
            raise ValueError('حالة KYC غير صحيحة')
        return v
    
    @validator('kyc_level')
    def validate_kyc_level(cls, v):
        if v not in [1, 2, 3]:
            raise ValueError('مستوى KYC يجب أن يكون 1، 2، أو 3')
        return v


class ReportGenerationRequest(BaseModel):
    """طلب إنشاء تقرير"""
    report_type: str = Field(..., description="نوع التقرير")
    start_date: date = Field(..., description="تاريخ البداية")
    end_date: date = Field(..., description="تاريخ النهاية")
    format: str = Field(default="json", description="تنسيق التقرير")
    filters: Optional[Dict[str, Any]] = Field(default={}, description="فلاتر إضافية")
    
    @validator('report_type')
    def validate_report_type(cls, v):
        valid_types = [t.value for t in ReportType]
        if v not in valid_types:
            raise ValueError(f'نوع تقرير غير صحيح. الأنواع المتاحة: {valid_types}')
        return v
    
    @validator('format')
    def validate_format(cls, v):
        valid_formats = [f.value for f in ReportFormat]
        if v not in valid_formats:
            raise ValueError(f'تنسيق غير صحيح. التنسيقات المتاحة: {valid_formats}')
        return v


class SettingUpdateRequest(BaseModel):
    """طلب تحديث إعداد"""
    key: str = Field(..., description="مفتاح الإعداد")
    value: Any = Field(..., description="قيمة الإعداد")
    description: Optional[str] = Field(None, description="وصف الإعداد")


class BulkSettingsUpdateRequest(BaseModel):
    """طلب تحديث متعدد للإعدادات"""
    settings: Dict[str, Any] = Field(..., description="الإعدادات للتحديث")


# Dependency injection
async def get_admin_dashboard() -> AdminDashboard:
    """الحصول على خدمة لوحة الإدارة"""
    db_connection = DatabaseConnection()
    return AdminDashboard(db_connection)


async def get_user_management() -> UserManagement:
    """الحصول على خدمة إدارة المستخدمين"""
    db_connection = DatabaseConnection()
    return UserManagement(db_connection)


async def get_financial_reports() -> FinancialReports:
    """الحصول على خدمة التقارير المالية"""
    db_connection = DatabaseConnection()
    return FinancialReports(db_connection)


async def get_system_settings() -> SystemSettings:
    """الحصول على خدمة إعدادات النظام"""
    db_connection = DatabaseConnection()
    return SystemSettings(db_connection)


# Dashboard Endpoints

@router.get("/dashboard/overview")
async def get_dashboard_overview(
    period: str = Query(default="today", description="الفترة الزمنية"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_ADMIN_DASHBOARD)),
    dashboard: AdminDashboard = Depends(get_admin_dashboard)
):
    """الحصول على نظرة عامة على لوحة التحكم"""
    try:
        period_enum = DashboardPeriod(period)
        overview = await dashboard.get_system_overview(period_enum)
        
        return {
            "success": True,
            "data": overview
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"فترة غير صحيحة: {str(e)}"
        )
    except Exception as e:
        logger.error(f"❌ Failed to get dashboard overview: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب نظرة عامة على لوحة التحكم"
        )


@router.get("/dashboard/real-time")
async def get_real_time_metrics(
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_ADMIN_DASHBOARD)),
    dashboard: AdminDashboard = Depends(get_admin_dashboard)
):
    """الحصول على المقاييس في الوقت الفعلي"""
    try:
        metrics = await dashboard.get_real_time_metrics()
        
        return {
            "success": True,
            "data": metrics
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get real-time metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب المقاييس في الوقت الفعلي"
        )


@router.get("/dashboard/financial")
async def get_financial_dashboard(
    period: str = Query(default="this_month", description="الفترة الزمنية"),
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_FINANCIAL_DATA)),
    dashboard: AdminDashboard = Depends(get_admin_dashboard)
):
    """الحصول على لوحة التحكم المالية"""
    try:
        period_enum = DashboardPeriod(period)
        financial_data = await dashboard.get_financial_dashboard(period_enum)
        
        return {
            "success": True,
            "data": financial_data
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get financial dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب لوحة التحكم المالية"
        )


# User Management Endpoints

@router.get("/users")
async def get_users_list(
    page: int = Query(default=1, ge=1, description="رقم الصفحة"),
    limit: int = Query(default=50, ge=1, le=100, description="عدد النتائج في الصفحة"),
    search: Optional[str] = Query(None, description="البحث في الاسم أو البريد الإلكتروني"),
    role: Optional[str] = Query(None, description="فلترة حسب الدور"),
    status: Optional[str] = Query(None, description="فلترة حسب الحالة"),
    kyc_status: Optional[str] = Query(None, description="فلترة حسب حالة KYC"),
    sort_by: str = Query(default="created_at", description="ترتيب حسب"),
    sort_order: str = Query(default="desc", description="اتجاه الترتيب"),
    current_user: TokenPayload = Depends(require_permission(Permission.MANAGE_USERS)),
    user_management: UserManagement = Depends(get_user_management)
):
    """الحصول على قائمة المستخدمين"""
    try:
        users_data = await user_management.get_users_list(
            page=page,
            limit=limit,
            search=search,
            role=role,
            status=status,
            kyc_status=kyc_status,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        return {
            "success": True,
            "data": users_data
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get users list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب قائمة المستخدمين"
        )


@router.get("/users/{user_id}")
async def get_user_details(
    user_id: str = Path(..., description="معرف المستخدم"),
    current_user: TokenPayload = Depends(require_permission(Permission.MANAGE_USERS)),
    user_management: UserManagement = Depends(get_user_management)
):
    """الحصول على تفاصيل المستخدم"""
    try:
        user_details = await user_management.get_user_details(user_id)
        
        if not user_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="المستخدم غير موجود"
            )
        
        return {
            "success": True,
            "data": user_details
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get user details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب تفاصيل المستخدم"
        )


@router.put("/users/{user_id}/status")
async def update_user_status(
    user_id: str = Path(..., description="معرف المستخدم"),
    status_data: UserStatusUpdateRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.MANAGE_USERS)),
    user_management: UserManagement = Depends(get_user_management)
):
    """تحديث حالة المستخدم"""
    try:
        success = await user_management.update_user_status(
            user_id=user_id,
            is_active=status_data.is_active,
            updated_by=current_user.user_id,
            reason=status_data.reason
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="فشل في تحديث حالة المستخدم"
            )
        
        return {
            "success": True,
            "message": "تم تحديث حالة المستخدم بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update user status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء تحديث حالة المستخدم"
        )


@router.put("/users/{user_id}/kyc")
async def update_user_kyc_status(
    user_id: str = Path(..., description="معرف المستخدم"),
    kyc_data: KYCStatusUpdateRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.MANAGE_KYC)),
    user_management: UserManagement = Depends(get_user_management)
):
    """تحديث حالة التحقق من الهوية"""
    try:
        success = await user_management.update_kyc_status(
            user_id=user_id,
            kyc_status=kyc_data.kyc_status,
            kyc_level=kyc_data.kyc_level,
            updated_by=current_user.user_id,
            notes=kyc_data.notes
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="فشل في تحديث حالة KYC"
            )
        
        return {
            "success": True,
            "message": "تم تحديث حالة KYC بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update KYC status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء تحديث حالة KYC"
        )


# Financial Reports Endpoints

@router.post("/reports/generate")
async def generate_report(
    report_request: ReportGenerationRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.GENERATE_REPORTS)),
    financial_reports: FinancialReports = Depends(get_financial_reports)
):
    """إنشاء تقرير مالي"""
    try:
        report_type = ReportType(report_request.report_type)
        
        if report_type == ReportType.REVENUE:
            report_data = await financial_reports.generate_revenue_report(
                start_date=report_request.start_date,
                end_date=report_request.end_date,
                include_projections=report_request.filters.get('include_projections', False)
            )
        elif report_type == ReportType.TRANSACTIONS:
            report_data = await financial_reports.generate_transaction_report(
                start_date=report_request.start_date,
                end_date=report_request.end_date,
                transaction_type=report_request.filters.get('transaction_type'),
                agent_id=report_request.filters.get('agent_id')
            )
        elif report_type == ReportType.COMMISSIONS:
            report_data = await financial_reports.generate_commission_report(
                start_date=report_request.start_date,
                end_date=report_request.end_date,
                agent_id=report_request.filters.get('agent_id'),
                commission_type=report_request.filters.get('commission_type')
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"نوع التقرير غير مدعوم: {report_type.value}"
            )
        
        # Export in requested format if not JSON
        if report_request.format != "json":
            report_format = ReportFormat(report_request.format)
            exported_data = await financial_reports.export_report(report_data, report_format)
            
            # Return as file download
            return StreamingResponse(
                io.BytesIO(exported_data),
                media_type="application/octet-stream",
                headers={
                    "Content-Disposition": f"attachment; filename=report_{report_type.value}_{report_request.start_date}_{report_request.end_date}.{report_request.format}"
                }
            )
        
        return {
            "success": True,
            "data": report_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to generate report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء إنشاء التقرير"
        )


# System Settings Endpoints

@router.get("/settings")
async def get_all_settings(
    category: Optional[str] = Query(None, description="فئة الإعدادات"),
    current_user: TokenPayload = Depends(require_permission(Permission.MANAGE_SETTINGS)),
    system_settings: SystemSettings = Depends(get_system_settings)
):
    """الحصول على جميع الإعدادات أو حسب الفئة"""
    try:
        if category:
            category_enum = SettingCategory(category)
            settings = await system_settings.get_settings_by_category(category_enum)
        else:
            # Get all settings (this would need to be implemented)
            settings = {}
            for cat in SettingCategory:
                cat_settings = await system_settings.get_settings_by_category(cat)
                settings.update(cat_settings)
        
        return {
            "success": True,
            "data": settings
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"فئة إعدادات غير صحيحة: {str(e)}"
        )
    except Exception as e:
        logger.error(f"❌ Failed to get settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب الإعدادات"
        )


@router.put("/settings")
async def update_setting(
    setting_data: SettingUpdateRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.MANAGE_SETTINGS)),
    system_settings: SystemSettings = Depends(get_system_settings)
):
    """تحديث إعداد واحد"""
    try:
        success = await system_settings.set_setting(
            key=setting_data.key,
            value=setting_data.value,
            updated_by=current_user.user_id,
            description=setting_data.description
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="فشل في تحديث الإعداد"
            )
        
        return {
            "success": True,
            "message": "تم تحديث الإعداد بنجاح"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update setting: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء تحديث الإعداد"
        )


@router.put("/settings/bulk")
async def bulk_update_settings(
    settings_data: BulkSettingsUpdateRequest = Body(...),
    current_user: TokenPayload = Depends(require_permission(Permission.MANAGE_SETTINGS)),
    system_settings: SystemSettings = Depends(get_system_settings)
):
    """تحديث متعدد للإعدادات"""
    try:
        results = await system_settings.bulk_update_settings(
            settings=settings_data.settings,
            updated_by=current_user.user_id
        )
        
        successful_updates = sum(1 for success in results.values() if success)
        total_updates = len(results)
        
        return {
            "success": True,
            "message": f"تم تحديث {successful_updates} من {total_updates} إعدادات",
            "data": {
                "results": results,
                "successful_updates": successful_updates,
                "total_updates": total_updates
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to bulk update settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء التحديث المتعدد للإعدادات"
        )


@router.get("/statistics")
async def get_admin_statistics(
    current_user: TokenPayload = Depends(require_permission(Permission.VIEW_ANALYTICS)),
    dashboard: AdminDashboard = Depends(get_admin_dashboard),
    user_management: UserManagement = Depends(get_user_management),
    system_settings: SystemSettings = Depends(get_system_settings)
):
    """الحصول على إحصائيات شاملة للإدارة"""
    try:
        # Get statistics from different services
        user_stats = await user_management.get_statistics()
        settings_stats = await system_settings.get_statistics()
        
        return {
            "success": True,
            "data": {
                "users": user_stats,
                "settings": settings_stats,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get admin statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="حدث خطأ أثناء جلب الإحصائيات"
        )


# Error handlers
@router.exception_handler(ValueError)
async def value_error_handler(request, exc):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "success": False,
            "error": "invalid_input",
            "message": str(exc)
        }
    )


@router.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"❌ Unhandled exception in admin API: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "error": "internal_server_error",
            "message": "حدث خطأ داخلي في الخادم"
        }
    )
