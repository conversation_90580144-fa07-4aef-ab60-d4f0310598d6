import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    // التحقق من وجود decorator @Public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    if (err || !user) {
      throw err || new UnauthorizedException('رمز الوصول غير صحيح أو منتهي الصلاحية');
    }

    // التحقق من حالة المستخدم
    if (!user.isActive) {
      throw new UnauthorizedException('الحساب معطل');
    }

    if (!user.isVerified) {
      throw new UnauthorizedException('يرجى التحقق من الحساب أولاً');
    }

    return user;
  }
}
