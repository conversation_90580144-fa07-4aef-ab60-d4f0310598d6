@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: WS Transfir Frontend Only Startup
:: تشغيل الواجهة الأمامية فقط لنظام WS Transfir

echo 🎨 تشغيل الواجهة الأمامية - WS Transfir
echo ========================================
echo.

:: Check if Node.js is installed
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo.
    echo 📥 خطوات التثبيت:
    echo    1. اذهب إلى https://nodejs.org
    echo    2. حمل الإصدار LTS (الموصى به)
    echo    3. قم بتثبيت Node.js
    echo    4. أعد تشغيل Command Prompt
    echo    5. شغل هذا الملف مرة أخرى
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js متاح - الإصدار: %NODE_VERSION%
echo.

:: Check if we're in the right directory
if not exist "frontend\web-app" (
    echo ❌ مجلد frontend/web-app غير موجود
    echo.
    echo 📁 تأكد من أنك في المجلد الصحيح للمشروع
    echo    المجلد الحالي: %CD%
    echo    المطلوب: مجلد يحتوي على frontend/web-app
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على مجلد الواجهة الأمامية
echo.

:: Navigate to frontend directory
cd frontend\web-app

:: Check if package.json exists
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود في frontend/web-app
    echo.
    echo 🔧 إنشاء package.json أساسي...
    (
        echo {
        echo   "name": "ws-transfir-web-app",
        echo   "version": "1.0.0",
        echo   "private": true,
        echo   "scripts": {
        echo     "dev": "next dev -p 3100",
        echo     "build": "next build",
        echo     "start": "next start -p 3100",
        echo     "lint": "next lint"
        echo   },
        echo   "dependencies": {
        echo     "next": "14.0.0",
        echo     "react": "18.2.0",
        echo     "react-dom": "18.2.0",
        echo     "@mui/material": "^5.14.0",
        echo     "@mui/icons-material": "^5.14.0",
        echo     "@emotion/react": "^11.11.0",
        echo     "@emotion/styled": "^11.11.0",
        echo     "formik": "^2.4.0",
        echo     "yup": "^1.3.0"
        echo   },
        echo   "devDependencies": {
        echo     "@types/node": "20.8.0",
        echo     "@types/react": "18.2.0",
        echo     "@types/react-dom": "18.2.0",
        echo     "typescript": "5.2.0",
        echo     "eslint": "8.51.0",
        echo     "eslint-config-next": "14.0.0"
        echo   }
        echo }
    ) > package.json
    echo ✅ تم إنشاء package.json
    echo.
)

:: Install dependencies
echo 📦 تثبيت dependencies...
echo    هذا قد يستغرق بضع دقائق في المرة الأولى...
echo.

npm install
if errorlevel 1 (
    echo ❌ فشل في تثبيت dependencies
    echo.
    echo 🔧 حلول مقترحة:
    echo    1. تأكد من اتصال الإنترنت
    echo    2. جرب: npm cache clean --force
    echo    3. جرب: npm install --legacy-peer-deps
    echo    4. احذف مجلد node_modules وأعد المحاولة
    echo.
    pause
    cd ..\..
    exit /b 1
)

echo ✅ تم تثبيت dependencies بنجاح
echo.

:: Create next.config.js if it doesn't exist
if not exist "next.config.js" (
    echo 🔧 إنشاء ملف إعداد Next.js...
    (
        echo /** @type {import('next'^).NextConfig} */
        echo const nextConfig = {
        echo   reactStrictMode: true,
        echo   swcMinify: true,
        echo   env: {
        echo     NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL ^|^| 'http://localhost:3000',
        echo     NEXT_PUBLIC_APP_NAME: 'WS Transfir',
        echo   },
        echo   async rewrites(^) {
        echo     return [
        echo       {
        echo         source: '/api/:path*',
        echo         destination: 'http://localhost:3000/api/:path*',
        echo       },
        echo     ];
        echo   },
        echo };
        echo.
        echo module.exports = nextConfig;
    ) > next.config.js
    echo ✅ تم إنشاء next.config.js
    echo.
)

:: Create .env.local if it doesn't exist
if not exist ".env.local" (
    echo ⚙️ إنشاء ملف البيئة المحلية...
    (
        echo # WS Transfir Frontend Environment
        echo NEXT_PUBLIC_API_URL=http://localhost:3000
        echo NEXT_PUBLIC_APP_NAME=WS Transfir
        echo NEXT_PUBLIC_APP_VERSION=1.0.0
        echo.
        echo # Development settings
        echo NODE_ENV=development
    ) > .env.local
    echo ✅ تم إنشاء .env.local
    echo.
)

:: Check if src directory exists, if not create basic structure
if not exist "src" (
    echo 📁 إنشاء هيكل المشروع الأساسي...
    mkdir src
    mkdir src\pages
    mkdir src\components
    mkdir src\styles
    
    :: Create basic index page
    (
        echo import React from 'react';
        echo import Head from 'next/head';
        echo.
        echo export default function Home(^) {
        echo   return (
        echo     ^<div^>
        echo       ^<Head^>
        echo         ^<title^>WS Transfir - نظام التحويلات المالية^</title^>
        echo         ^<meta name="description" content="نظام WS Transfir للتحويلات المالية" /^>
        echo       ^</Head^>
        echo.
        echo       ^<main style={{ padding: '2rem', textAlign: 'center' }}^>
        echo         ^<h1^>🚀 مرحباً بك في WS Transfir^</h1^>
        echo         ^<p^>نظام التحويلات المالية^</p^>
        echo         ^<div style={{ marginTop: '2rem' }}^>
        echo           ^<h2^>📱 الصفحات المتاحة:^</h2^>
        echo           ^<ul style={{ listStyle: 'none', padding: 0 }}^>
        echo             ^<li^>^<a href="/login"^>🔐 تسجيل الدخول^</a^>^</li^>
        echo             ^<li^>^<a href="/register"^>📝 إنشاء حساب^</a^>^</li^>
        echo             ^<li^>^<a href="/profile"^>👤 الملف الشخصي^</a^>^</li^>
        echo             ^<li^>^<a href="/transfers"^>💸 التحويلات^</a^>^</li^>
        echo           ^</ul^>
        echo         ^</div^>
        echo       ^</main^>
        echo     ^</div^>
        echo   ^);
        echo }
    ) > src\pages\index.tsx
    
    echo ✅ تم إنشاء هيكل المشروع الأساسي
    echo.
)

:: Start the development server
echo 🚀 بدء تشغيل خادم التطوير...
echo.
echo ✅ معلومات التشغيل:
echo    🌐 الرابط: http://localhost:3100
echo    📁 المجلد: %CD%
echo    🔧 الوضع: Development
echo.
echo 📝 ملاحظات:
echo    - سيتم فتح المتصفح تلقائياً
echo    - اضغط Ctrl+C لإيقاف الخادم
echo    - التغييرات ستظهر تلقائياً (Hot Reload)
echo.

:: Start the server and open browser
echo 🎬 بدء التشغيل...
timeout /t 2 /nobreak >nul

start "" "http://localhost:3100"

npm run dev

:: If we reach here, the server stopped
echo.
echo 🛑 تم إيقاف خادم التطوير
echo.

cd ..\..
pause
